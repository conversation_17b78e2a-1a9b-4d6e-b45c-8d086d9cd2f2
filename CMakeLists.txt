set(CMAKE_SYSTEM_NAME Generic)
set(CMAKE_SYSTEM_VERSION 1)
cmake_minimum_required(VERSION 3.28)

set(CMAKE_C_COMPILER gcc)
set(CMAKE_CXX_COMPILER g++)
set(CMAKE_ASM_COMPILER gcc)
set(CMAKE_TRY_COMPILE_TARGET_TYPE STATIC_LIBRARY)

# 添加预处理器定义
add_definitions(-DSDL_MAIN_HANDLED)

project(LVPageHub)

set(CMAKE_CXX_STANDARD 14)

include_directories(
        lvgl
        User/Inc
        ViewModel/Inc
        Pages/temp_page
)

file(GLOB_RECURSE SOURCES "lvgl/*.*" "User/*.*" "main.c" "ViewModel/*.*" "Pages/*.*")

# 手动添加主文件
set(MAIN_FILE main.c)

# 添加可执行文件
add_executable(${PROJECT_NAME} ${SOURCES})