#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif


#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_RENDER_LVGL_LOGO_L8
#define LV_ATTRIBUTE_IMG_RENDER_LVGL_LOGO_L8
#endif

static const
LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMG_RENDER_LVGL_LOGO_L8
uint8_t img_render_lvgl_logo_l8_map[] = {

    0x00,0x00,0x40,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x47,0x48,0x47,0x47,0x47,0x46,0x47,0x20,0x00,
    0x02,0x4b,0x2f,0x32,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x33,0x2c,0x2a,0x2e,0x3e,0x29,
    0x40,0x2f,0x35,0x36,0x35,0x36,0x36,0x37,0x36,0x35,0x36,0x36,0x35,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x35,0x31,0x5e,0x69,0x35,0x2a,0x45,
    0x47,0x32,0x36,0x35,0x36,0x36,0x35,0x36,0x35,0x36,0x36,0x35,0x36,0x35,0x36,0x35,0x37,0x36,0x36,0x36,0x36,0x35,0x37,0x2d,0x7a,0xfb,0xfd,0x9c,0x2c,0x38,
    0x47,0x33,0x35,0x36,0x35,0x36,0x36,0x36,0x36,0x35,0x37,0x36,0x36,0x36,0x36,0x36,0x36,0x35,0x36,0x35,0x36,0x36,0x37,0x29,0xb1,0xfd,0xfd,0xd8,0x2f,0x39,
    0x47,0x33,0x36,0x36,0x36,0x36,0x36,0x35,0x36,0x36,0x36,0x36,0x36,0x35,0x35,0x35,0x37,0x35,0x36,0x36,0x36,0x36,0x37,0x2f,0x60,0xe7,0xf1,0x7f,0x2d,0x39,
    0x47,0x33,0x36,0x36,0x36,0x36,0x36,0x35,0x36,0x36,0x35,0x36,0x36,0x36,0x35,0x35,0x36,0x36,0x36,0x37,0x36,0x36,0x36,0x36,0x30,0x43,0x49,0x2f,0x35,0x39,
    0x46,0x33,0x36,0x36,0x36,0x36,0x35,0x36,0x36,0x36,0x35,0x36,0x36,0x35,0x36,0x36,0x37,0x36,0x36,0x35,0x36,0x36,0x35,0x36,0x36,0x32,0x31,0x35,0x35,0x39,
    0x49,0x27,0x29,0x2a,0x29,0x2a,0x2a,0x29,0x29,0x2f,0x37,0x36,0x36,0x36,0x35,0x36,0x36,0x36,0x35,0x37,0x35,0x35,0x37,0x36,0x36,0x37,0x35,0x36,0x35,0x39,
    0x17,0x41,0x45,0x45,0x45,0x45,0x45,0x44,0x45,0x47,0x2e,0x36,0x35,0x37,0x36,0x36,0x36,0x35,0x36,0x36,0x36,0x36,0x35,0x36,0x36,0x36,0x36,0x36,0x35,0x39,
    0x05,0x35,0x3c,0x3a,0x3a,0x3a,0x3a,0x3d,0x33,0x07,0x49,0x2e,0x36,0x36,0x36,0x36,0x37,0x36,0x36,0x35,0x36,0x36,0x35,0x35,0x36,0x36,0x36,0x36,0x35,0x39,
    0x67,0x71,0x72,0x71,0x71,0x71,0x72,0x72,0x6c,0x48,0x32,0x2b,0x36,0x36,0x36,0x35,0x35,0x36,0x36,0x37,0x35,0x36,0x36,0x36,0x36,0x36,0x35,0x35,0x35,0x39,
    0x72,0x77,0x79,0x79,0x79,0x79,0x79,0x79,0x6f,0x5a,0x30,0x2b,0x35,0x37,0x36,0x36,0x36,0x36,0x36,0x35,0x36,0x36,0x35,0x36,0x35,0x36,0x36,0x36,0x35,0x39,
    0x72,0x77,0x79,0x79,0x78,0x79,0x79,0x79,0x70,0x59,0x31,0x2c,0x36,0x37,0x35,0x36,0x36,0x36,0x36,0x35,0x35,0x36,0x35,0x36,0x36,0x36,0x36,0x36,0x35,0x39,
    0x72,0x77,0x79,0x79,0x79,0x79,0x79,0x79,0x70,0x59,0x31,0x2c,0x35,0x36,0x36,0x36,0x37,0x35,0x36,0x36,0x36,0x36,0x35,0x35,0x36,0x36,0x36,0x36,0x35,0x39,
    0x72,0x77,0x79,0x79,0x79,0x79,0x79,0x79,0x70,0x59,0x31,0x2b,0x36,0x36,0x35,0x36,0x36,0x36,0x35,0x36,0x36,0x36,0x37,0x36,0x36,0x36,0x35,0x36,0x35,0x39,
    0x72,0x77,0x79,0x79,0x79,0x79,0x78,0x79,0x70,0x59,0x31,0x2c,0x36,0x36,0x36,0x35,0x35,0x36,0x35,0x37,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x35,0x39,
    0x71,0x77,0x79,0x79,0x78,0x79,0x79,0x79,0x70,0x59,0x32,0x2b,0x36,0x36,0x35,0x36,0x35,0x36,0x36,0x37,0x36,0x36,0x35,0x36,0x36,0x36,0x36,0x36,0x35,0x39,
    0x72,0x71,0x74,0x74,0x74,0x73,0x74,0x74,0x6b,0x5a,0x31,0x23,0x2f,0x2e,0x2f,0x2f,0x2f,0x2d,0x2e,0x32,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x36,0x35,0x39,
    0x41,0x70,0x72,0x72,0x72,0x72,0x72,0x72,0x6f,0x29,0x0e,0x49,0x46,0x47,0x48,0x48,0x48,0x48,0x48,0x3f,0x2e,0x36,0x35,0x35,0x36,0x36,0x36,0x36,0x35,0x39,
    0x00,0x07,0x11,0x10,0x10,0x10,0x10,0x11,0x07,0x00,0x00,0x01,0x13,0x11,0x11,0x11,0x11,0x11,0x0e,0x0b,0x4b,0x2e,0x36,0x36,0x36,0x36,0x36,0x36,0x35,0x39,
    0x51,0x9d,0xa4,0xa4,0xa4,0xa4,0xa4,0xa4,0x9b,0x2e,0x1a,0x62,0x65,0x65,0x65,0x65,0x65,0x65,0x60,0x52,0x15,0x36,0x34,0x36,0x36,0x36,0x35,0x36,0x35,0x39,
    0x91,0xaf,0xb1,0xb1,0xb1,0xb1,0xb1,0xb1,0xac,0x60,0x35,0x59,0x63,0x63,0x63,0x63,0x63,0x63,0x5d,0x65,0x0d,0x3d,0x32,0x36,0x35,0x36,0x36,0x36,0x35,0x39,
    0x8f,0xb1,0xb3,0xb3,0xb3,0xb3,0xb3,0xb3,0xae,0x5b,0x32,0x5e,0x65,0x65,0x65,0x65,0x65,0x65,0x60,0x65,0x11,0x3f,0x31,0x35,0x36,0x36,0x36,0x36,0x35,0x39,
    0x8d,0xb1,0xb3,0xb3,0xb3,0xb3,0xb3,0xb3,0xad,0x5c,0x32,0x5e,0x65,0x65,0x65,0x65,0x65,0x65,0x60,0x65,0x11,0x3f,0x32,0x36,0x35,0x36,0x36,0x36,0x35,0x39,
    0x8f,0xb1,0xb3,0xb3,0xb3,0xb3,0xb3,0xb3,0xae,0x5c,0x32,0x5e,0x65,0x65,0x65,0x65,0x65,0x65,0x60,0x65,0x11,0x3f,0x32,0x35,0x36,0x36,0x36,0x36,0x35,0x39,
    0x8f,0xb1,0xb3,0xb3,0xb3,0xb3,0xb3,0xb3,0xad,0x5b,0x32,0x5e,0x65,0x65,0x65,0x65,0x65,0x65,0x60,0x65,0x11,0x3f,0x32,0x35,0x36,0x36,0x36,0x36,0x35,0x39,
    0x6e,0xb0,0xb3,0xb3,0xb3,0xb3,0xb3,0xb3,0xae,0x5c,0x32,0x5e,0x65,0x65,0x65,0x65,0x65,0x65,0x5f,0x65,0x11,0x3f,0x32,0x36,0x36,0x36,0x36,0x36,0x33,0x3d,
    0x1b,0x9c,0xaf,0xb1,0xb1,0xb1,0xb1,0xb1,0xad,0x5e,0x35,0x5b,0x65,0x65,0x65,0x65,0x65,0x65,0x5e,0x65,0x11,0x3a,0x31,0x35,0x35,0x35,0x35,0x33,0x26,0x48,
    0x00,0x26,0x8a,0xa7,0xac,0xab,0xab,0xac,0xa3,0x35,0x1c,0x62,0x65,0x65,0x65,0x65,0x65,0x65,0x5e,0x58,0x03,0x49,0x33,0x39,0x39,0x3a,0x38,0x3d,0x49,0x02,

};

const lv_image_dsc_t img_render_lvgl_logo_l8 = {
  .header.magic = LV_IMAGE_HEADER_MAGIC,
  .header.cf = LV_COLOR_FORMAT_L8,
  .header.flags = 0,
  .header.w = 30,
  .header.h = 30,
  .header.stride = 30,
  .data_size = sizeof(img_render_lvgl_logo_l8_map),
  .data = img_render_lvgl_logo_l8_map,
};

