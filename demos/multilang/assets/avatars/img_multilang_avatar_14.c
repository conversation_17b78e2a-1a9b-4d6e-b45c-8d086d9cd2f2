#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_14
    #define LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_14
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_14 uint8_t
img_multilang_avatar_14_map[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb4, 0xc3, 0xc3, 0x11, 0x81, 0x8b, 0x94, 0x37, 0xb5, 0xb5, 0xb5, 0x5e, 0xf3, 0xe8, 0xe2, 0x86, 0xf8, 0xf1, 0xe9, 0xa8, 0xf4, 0xf2, 0xea, 0xbf, 0xf2, 0xf1, 0xef, 0xd2, 0xf0, 0xf0, 0xec, 0xe5, 0xf3, 0xf0, 0xea, 0xee, 0xfd, 0xfa, 0xf7, 0xf2, 0xfb, 0xfb, 0xfc, 0xff, 0xf6, 0xf3, 0xef, 0xff, 0xfc, 0xfa, 0xf8, 0xf2, 0xfc, 0xfd, 0xfd, 0xee, 0xfc, 0xfd, 0xfd, 0xe5, 0xfc, 0xfc, 0xfc, 0xd2, 0xfd, 0xfd, 0xfd, 0xbf, 0xfd, 0xfd, 0xfd, 0xa8, 0xfd, 0xfd, 0xfd, 0x86, 0xff, 0xfc, 0xfc, 0x5e, 0xff, 0xff, 0xff, 0x37, 0xff, 0xff, 0xff, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc1, 0xc1, 0xc1, 0x19, 0xff, 0xff, 0xff, 0x55, 0xff, 0xff, 0xff, 0x8f, 0xdf, 0xe3, 0xe3, 0xc1, 0x8f, 0x9c, 0xa3, 0xe9, 0x8d, 0x96, 0x9c, 0xff, 0xe0, 0xe0, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xf9, 0xf5, 0xf2, 0xff, 0xf7, 0xf1, 0xef, 0xff, 0xf5, 0xf0, 0xed, 0xff, 0xf4, 0xef, 0xec, 0xff, 0xf3, 0xef, 0xea, 0xff, 0xfa, 0xf8, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf6, 0xf1, 0xff, 0xf9, 0xf7, 0xf5, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfe, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfe, 0xfd, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xe9, 0x92, 0x96, 0x8e, 0xc1, 0x7e, 0x8c, 0x97, 0x8f, 0xff, 0xff, 0xff, 0x55, 0xff, 0xf4, 0xf4, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xfe, 0xfe, 0x07, 0xfb, 0xfb, 0xff, 0x46, 0xff, 0xff, 0xff, 0x8e, 0xf5, 0xf0, 0xea, 0xd7, 0xc1, 0xbd, 0xbc, 0xff, 0xde, 0xe3, 0xe6, 0xff, 0xb6, 0xba, 0xc0, 0xff, 0x8f, 0x95, 0x97, 0xff, 0xc1, 0xc2, 0xc1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xf9, 0xf4, 0xf4, 0xff, 0xf9, 0xf0, 0xef, 0xff, 0xf8, 0xf1, 0xee, 0xff, 0xf6, 0xf0, 0xed, 0xff, 0xf5, 0xef, 0xea, 0xff, 0xfa, 0xf8, 0xf4, 0xff, 0xfc, 0xff, 0xfe, 0xff, 0xf9, 0xfa, 0xf7, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfb, 0xfd, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xfc, 0xfb, 0xfd, 0xff, 0xfe, 0xfd, 0xfe, 0xff, 0xf9, 0xfd, 0xfe, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9d, 0xa2, 0x9b, 0xff, 0x6a, 0x76, 0x84, 0xff, 0xf9, 0xf5, 0xf4, 0xff, 0xfe, 0xf8, 0xef, 0xff, 0xf4, 0xf1, 0xee, 0xd7, 0xfb, 0xf7, 0xf4, 0x8e, 0xf7, 0xf4, 0xf0, 0x46, 0xfe, 0xfe, 0xfe, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x05, 0xfb, 0xfb, 0xf4, 0x48, 0xff, 0xfd, 0xf8, 0x9f, 0xfd, 0xfb, 0xfb, 0xef, 0xfa, 0xfd, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x81, 0x81, 0x8b, 0xff, 0x65, 0x72, 0x77, 0xff, 0xb7, 0xb5, 0xb7, 0xff, 0xf2, 0xf1, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfd, 0xff, 0xf9, 0xf2, 0xf1, 0xff, 0xf8, 0xf1, 0xed, 0xff, 0xf6, 0xf1, 0xee, 0xff, 0xf5, 0xf0, 0xee, 0xff, 0xf7, 0xf1, 0xeb, 0xff, 0xfe, 0xfa, 0xf4, 0xff, 0xfb, 0xfe, 0xfc, 0xff, 0xdc, 0xde, 0xdd, 0xff, 0xde, 0xd9, 0xd9, 0xff, 0xfa, 0xf3, 0xf2, 0xff, 0xed, 0xe7, 0xe4, 0xff, 0xf2, 0xee, 0xea, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfe, 0xfc, 0xfe, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfb, 0xfe, 0xff, 0xff, 0xfb, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xb3, 0xae, 0xa8, 0xff, 0x65, 0x70, 0x7b, 0xff, 0xed, 0xea, 0xe6, 0xff, 0xff, 0xfc, 0xf4, 0xff, 0xf3, 0xf2, 0xf0, 0xff, 0xf8, 0xf5, 0xf1, 0xff, 0xf8, 0xf5, 0xf1, 0xff, 0xf6, 0xf3, 0xee, 0xef, 0xf5, 0xf3, 0xf0, 0x9f, 0xf7, 0xf4, 0xf0, 0x48, 0xff, 0xff, 0xff, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf7, 0xf7, 0xf7, 0x23, 0xfb, 0xf7, 0xf3, 0x86, 0xfb, 0xf9, 0xf4, 0xe5, 0xfc, 0xf9, 0xf5, 0xff, 0xfe, 0xfc, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xfd, 0xfd, 0xff, 0x96, 0x9e, 0xa5, 0xff, 0x49, 0x4f, 0x5f, 0xff, 0xc9, 0xc7, 0xc6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xf9, 0xf7, 0xf6, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xfc, 0xf6, 0xf3, 0xff, 0xfd, 0xf7, 0xf3, 0xff, 0xff, 0xfc, 0xf4, 0xff, 0xf0, 0xec, 0xe3, 0xff, 0xe8, 0xe1, 0xdf, 0xff, 0xe8, 0xdb, 0xdb, 0xff, 0xef, 0xe5, 0xe5, 0xff, 0xff, 0xfb, 0xf9, 0xff, 0xf8, 0xf5, 0xf1, 0xff, 0xf7, 0xf2, 0xef, 0xff, 0xf5, 0xf4, 0xf0, 0xff, 0xfb, 0xfc, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xf7, 0xf6, 0xff, 0xfc, 0xf3, 0xf4, 0xff, 0xf7, 0xf2, 0xf1, 0xff, 0xff, 0xfd, 0xf9, 0xff, 0xca, 0xc6, 0xbf, 0xff, 0x64, 0x72, 0x7d, 0xff, 0xdd, 0xdd, 0xd7, 0xff, 0xfa, 0xf8, 0xee, 0xff, 0xe8, 0xe8, 0xe6, 0xff, 0xf4, 0xf1, 0xec, 0xff, 0xf7, 0xf4, 0xf0, 0xff, 0xf9, 0xf6, 0xf2, 0xff, 0xf9, 0xf7, 0xf2, 0xff, 0xf5, 0xf2, 0xee, 0xff, 0xf7, 0xf2, 0xf0, 0xe5, 0xf5, 0xf3, 0xef, 0x86, 0xff, 0xf0, 0xf0, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfb, 0xff, 0xfb, 0x41, 0xfd, 0xfd, 0xfd, 0xb4, 0xfd, 0xfb, 0xfb, 0xfe, 0xfb, 0xf9, 0xf7, 0xff, 0xfc, 0xfb, 0xf3, 0xff, 0xff, 0xfd, 0xf2, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xc5, 0xcd, 0xcf, 0xff, 0x9f, 0xa1, 0xa5, 0xff, 0xa7, 0xa6, 0xa5, 0xff, 0x9e, 0x9e, 0x9f, 0xff, 0xee, 0xef, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfc, 0xfe, 0xfd, 0xff, 0xfb, 0xfe, 0xfe, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xe8, 0xe7, 0xe2, 0xff, 0xda, 0xd6, 0xce, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xe5, 0xe3, 0xeb, 0xff, 0xd2, 0xd3, 0xd5, 0xff, 0xc2, 0xc4, 0xbf, 0xff, 0xda, 0xd9, 0xd8, 0xff, 0xf2, 0xed, 0xec, 0xff, 0xf2, 0xef, 0xec, 0xff, 0xf4, 0xf6, 0xf4, 0xff, 0xf7, 0xf9, 0xfb, 0xff, 0xfb, 0xf8, 0xfa, 0xff, 0xed, 0xeb, 0xeb, 0xff, 0xe0, 0xe1, 0xde, 0xff, 0xf6, 0xf7, 0xf5, 0xff, 0xf8, 0xf3, 0xef, 0xff, 0xf5, 0xed, 0xe8, 0xff, 0xf7, 0xef, 0xe9, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xdd, 0xdd, 0xd9, 0xff, 0x7f, 0x8b, 0x95, 0xff, 0xbc, 0xc4, 0xc4, 0xff, 0xea, 0xef, 0xe2, 0xff, 0xe8, 0xe7, 0xe1, 0xff, 0xf2, 0xed, 0xea, 0xff, 0xf1, 0xee, 0xea, 0xff, 0xf2, 0xef, 0xeb, 0xff, 0xf8, 0xf5, 0xf1, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xf8, 0xf5, 0xf0, 0xff, 0xf7, 0xf6, 0xf0, 0xff, 0xfc, 0xf5, 0xf1, 0xfe, 0xfd, 0xf3, 0xf2, 0xb4, 0xfb, 0xf3, 0xef, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfb, 0xfb, 0xfb, 0x4c, 0xfd, 0xfc, 0xfc, 0xcb, 0xfa, 0xfd, 0xfd, 0xff, 0xfb, 0xfd, 0xfd, 0xff, 0xff, 0xfc, 0xfc, 0xff, 0xff, 0xfc, 0xfa, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0xd4, 0xcf, 0xff, 0x95, 0x9c, 0x98, 0xff, 0x9a, 0xa1, 0x9f, 0xff, 0xe1, 0xe3, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xd2, 0xcc, 0xff, 0xd0, 0xd9, 0xd5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xee, 0xea, 0xe8, 0xff, 0xe0, 0xd9, 0xd4, 0xff, 0xde, 0xd8, 0xd3, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xb0, 0xae, 0xb5, 0xff, 0xa9, 0xa7, 0xb2, 0xff, 0xbd, 0xbb, 0xc7, 0xff, 0xc1, 0xc0, 0xcb, 0xff, 0xc7, 0xc7, 0xcc, 0xff, 0xcb, 0xca, 0xcd, 0xff, 0xdd, 0xdb, 0xde, 0xff, 0xec, 0xea, 0xea, 0xff, 0xe7, 0xe2, 0xe2, 0xff, 0xe6, 0xe4, 0xe5, 0xff, 0xce, 0xd1, 0xd3, 0xff, 0xcb, 0xcd, 0xce, 0xff, 0xf2, 0xf2, 0xf0, 0xff, 0xfa, 0xf5, 0xf4, 0xff, 0xf6, 0xeb, 0xe9, 0xff, 0xf0, 0xe9, 0xe9, 0xff, 0xe6, 0xdd, 0xdd, 0xff, 0x97, 0xa1, 0xb1, 0xff, 0x9e, 0xb0, 0xc0, 0xff, 0xdb, 0xe1, 0xdf, 0xff, 0xf6, 0xf3, 0xed, 0xff, 0xf6, 0xf3, 0xef, 0xff, 0xf3, 0xf1, 0xec, 0xff, 0xf3, 0xf0, 0xec, 0xff, 0xec, 0xe9, 0xe5, 0xff, 0xf3, 0xf0, 0xec, 0xff, 0xfa, 0xf6, 0xf5, 0xff, 0xfa, 0xf6, 0xf4, 0xff, 0xfc, 0xf5, 0xf3, 0xff, 0xfc, 0xf4, 0xf1, 0xff, 0xf9, 0xf3, 0xf0, 0xff, 0xf6, 0xf2, 0xed, 0xcb, 0xf5, 0xf1, 0xee, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfb, 0xfb, 0xfb, 0x46, 0xfc, 0xfc, 0xfc, 0xc9, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xf9, 0xfd, 0xfc, 0xff, 0xfa, 0xfc, 0xfb, 0xff, 0xff, 0xfc, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xee, 0xf0, 0xf0, 0xff, 0x93, 0x9e, 0xa3, 0xff, 0x72, 0x7c, 0x7e, 0xff, 0xbc, 0xba, 0xb8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5, 0xd9, 0xd2, 0xff, 0xc7, 0xce, 0xc8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xe0, 0xdd, 0xdd, 0xff, 0xe1, 0xd7, 0xd0, 0xff, 0xf0, 0xe7, 0xe3, 0xff, 0xd9, 0xd7, 0xd8, 0xff, 0xc9, 0xc8, 0xc8, 0xff, 0xca, 0xc1, 0xc1, 0xff, 0xe1, 0xda, 0xdb, 0xff, 0xd2, 0xd0, 0xd0, 0xff, 0xc6, 0xc4, 0xc3, 0xff, 0xd2, 0xce, 0xce, 0xff, 0xe1, 0xdc, 0xdd, 0xff, 0xe3, 0xdd, 0xdf, 0xff, 0xdc, 0xd7, 0xd8, 0xff, 0xd3, 0xd4, 0xd3, 0xff, 0xc7, 0xcc, 0xce, 0xff, 0xc0, 0xc5, 0xca, 0xff, 0xc9, 0xce, 0xd3, 0xff, 0xd5, 0xd5, 0xdc, 0xff, 0xe5, 0xe1, 0xe9, 0xff, 0xeb, 0xe4, 0xe8, 0xff, 0xe6, 0xe5, 0xe5, 0xff, 0xce, 0xd0, 0xd4, 0xff, 0x96, 0xa6, 0xbc, 0xff, 0x9d, 0xaa, 0xbd, 0xff, 0xe1, 0xdb, 0xdf, 0xff, 0xed, 0xe7, 0xe4, 0xff, 0xf3, 0xf1, 0xec, 0xff, 0xf8, 0xf4, 0xf0, 0xff, 0xf5, 0xf2, 0xee, 0xff, 0xf0, 0xed, 0xe8, 0xff, 0xf1, 0xed, 0xeb, 0xff, 0xfd, 0xf7, 0xfa, 0xff, 0xfd, 0xf8, 0xfa, 0xff, 0xf9, 0xf6, 0xf3, 0xff, 0xf8, 0xf5, 0xf1, 0xff, 0xf7, 0xf4, 0xf0, 0xff, 0xf7, 0xf3, 0xee, 0xff, 0xf6, 0xf2, 0xed, 0xff, 0xf7, 0xf3, 0xee, 0xc9, 0xfb, 0xf7, 0xf7, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x24, 0xfc, 0xfc, 0xfc, 0xb4, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfa, 0xfc, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xcb, 0xc7, 0xff, 0x77, 0x7a, 0x87, 0xff, 0x89, 0x8a, 0x91, 0xff, 0xdf, 0xde, 0xd9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd9, 0xda, 0xd8, 0xff, 0xc8, 0xca, 0xc7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xeb, 0xeb, 0xff, 0xd2, 0xcd, 0xcc, 0xff, 0xf6, 0xef, 0xe7, 0xff, 0xe2, 0xde, 0xd9, 0xff, 0xc8, 0xc8, 0xc9, 0xff, 0xd0, 0xce, 0xce, 0xff, 0xe0, 0xde, 0xdc, 0xff, 0xd7, 0xd4, 0xd4, 0xff, 0xd6, 0xd4, 0xd2, 0xff, 0xe7, 0xe8, 0xde, 0xff, 0xeb, 0xe8, 0xe2, 0xff, 0xde, 0xd8, 0xd8, 0xff, 0xd4, 0xcf, 0xd4, 0xff, 0xce, 0xcb, 0xd2, 0xff, 0xc5, 0xc4, 0xc9, 0xff, 0xc9, 0xc7, 0xce, 0xff, 0xc3, 0xc1, 0xc8, 0xff, 0xb6, 0xb9, 0xbf, 0xff, 0xb8, 0xb9, 0xbf, 0xff, 0xb4, 0xb3, 0xb9, 0xff, 0xca, 0xc9, 0xcc, 0xff, 0xd7, 0xd4, 0xd2, 0xff, 0xdb, 0xdb, 0xde, 0xff, 0x9c, 0xa6, 0xb3, 0xff, 0x91, 0x9f, 0xa8, 0xff, 0xee, 0xe7, 0xe4, 0xff, 0xf0, 0xea, 0xe7, 0xff, 0xf1, 0xed, 0xed, 0xff, 0xfb, 0xf8, 0xf7, 0xff, 0xf6, 0xf1, 0xf0, 0xff, 0xfa, 0xf5, 0xf4, 0xff, 0xff, 0xfc, 0xfc, 0xff, 0xff, 0xfd, 0xfd, 0xff, 0xfc, 0xfa, 0xfb, 0xff, 0xf9, 0xf6, 0xf3, 0xff, 0xf8, 0xf5, 0xf1, 0xff, 0xf6, 0xf3, 0xef, 0xff, 0xf8, 0xf6, 0xec, 0xff, 0xf8, 0xf6, 0xeb, 0xff, 0xf8, 0xf6, 0xed, 0xff, 0xfc, 0xf9, 0xf7, 0xff, 0xfd, 0xfd, 0xfd, 0xb5, 0xff, 0xff, 0xff, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe2, 0xe2, 0xe2, 0x09, 0xff, 0xff, 0xff, 0x87, 0xfd, 0xfd, 0xfd, 0xf9, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xfb, 0xfc, 0xfc, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xf8, 0xf8, 0xff, 0xa4, 0xae, 0xb0, 0xff, 0x7a, 0x82, 0x86, 0xff, 0xbd, 0xba, 0xb8, 0xff, 0xff, 0xfc, 0xf5, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6, 0xe7, 0xe5, 0xff, 0xc6, 0xc6, 0xc5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xf6, 0xf6, 0xf7, 0xff, 0xfa, 0xfd, 0xfd, 0xff, 0xdf, 0xe0, 0xde, 0xff, 0xdd, 0xd7, 0xd6, 0xff, 0xf4, 0xf2, 0xee, 0xff, 0xe2, 0xe0, 0xda, 0xff, 0xd6, 0xd2, 0xcb, 0xff, 0xe8, 0xe3, 0xdc, 0xff, 0xea, 0xe5, 0xdf, 0xff, 0xe7, 0xe3, 0xde, 0xff, 0xff, 0xfb, 0xf6, 0xff, 0xe8, 0xe4, 0xe0, 0xff, 0xdf, 0xd7, 0xd7, 0xff, 0xe5, 0xde, 0xdb, 0xff, 0xdb, 0xd3, 0xcf, 0xff, 0xe4, 0xd5, 0xd7, 0xff, 0xdf, 0xd0, 0xd2, 0xff, 0xd5, 0xc6, 0xc8, 0xff, 0xda, 0xcd, 0xce, 0xff, 0xbc, 0xb5, 0xb4, 0xff, 0xb8, 0xb1, 0xb2, 0xff, 0xcf, 0xc6, 0xc7, 0xff, 0xd6, 0xd1, 0xd3, 0xff, 0xe8, 0xe5, 0xe5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0xbc, 0xb3, 0xff, 0x7b, 0x90, 0x94, 0xff, 0xea, 0xeb, 0xe8, 0xff, 0xff, 0xfb, 0xf6, 0xff, 0xf4, 0xef, 0xef, 0xff, 0xf1, 0xed, 0xec, 0xff, 0xfa, 0xf9, 0xf8, 0xff, 0xfd, 0xfc, 0xfa, 0xff, 0xff, 0xfe, 0xfd, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xfc, 0xfa, 0xfa, 0xff, 0xf9, 0xf6, 0xf3, 0xff, 0xf9, 0xf5, 0xf1, 0xff, 0xf7, 0xf4, 0xf0, 0xff, 0xf8, 0xf4, 0xef, 0xff, 0xf8, 0xf4, 0xef, 0xff, 0xf8, 0xf4, 0xef, 0xff, 0xfb, 0xf8, 0xf5, 0xff, 0xfd, 0xfc, 0xfd, 0xff, 0xfb, 0xfb, 0xfb, 0xf9, 0xfb, 0xfb, 0xfb, 0x87, 0xff, 0xff, 0xff, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcf, 0xcf, 0xcf, 0x40, 0xd6, 0xd7, 0xd7, 0xda, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfb, 0xfb, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc9, 0xca, 0xcc, 0xff, 0x94, 0x9b, 0x9c, 0xff, 0xae, 0xac, 0xad, 0xff, 0xf2, 0xec, 0xea, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xfd, 0xf8, 0xf4, 0xff, 0xf8, 0xf4, 0xf2, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf0, 0xef, 0xff, 0xc3, 0xc6, 0xc2, 0xff, 0xf7, 0xfa, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xea, 0xe8, 0xe2, 0xff, 0xf2, 0xf0, 0xeb, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xd8, 0xd7, 0xd9, 0xff, 0xde, 0xd9, 0xd4, 0xff, 0xf5, 0xed, 0xe5, 0xff, 0xf5, 0xf2, 0xf1, 0xff, 0xe8, 0xe5, 0xe1, 0xff, 0xee, 0xeb, 0xe1, 0xff, 0xd8, 0xd2, 0xcb, 0xff, 0xd0, 0xc9, 0xcc, 0xff, 0xe8, 0xe2, 0xe3, 0xff, 0xde, 0xd9, 0xd5, 0xff, 0xe2, 0xdc, 0xd7, 0xff, 0xe7, 0xe3, 0xdd, 0xff, 0xe5, 0xe3, 0xde, 0xff, 0xe5, 0xda, 0xd4, 0xff, 0xdd, 0xce, 0xc9, 0xff, 0xd4, 0xca, 0xc6, 0xff, 0xcc, 0xc1, 0xc5, 0xff, 0xe2, 0xd8, 0xde, 0xff, 0xec, 0xe4, 0xe5, 0xff, 0xe2, 0xde, 0xdd, 0xff, 0xf4, 0xee, 0xeb, 0xff, 0xd3, 0xd0, 0xce, 0xff, 0x83, 0x96, 0xa6, 0xff, 0xd0, 0xd3, 0xde, 0xff, 0xf2, 0xea, 0xe8, 0xff, 0xf2, 0xed, 0xec, 0xff, 0xf3, 0xf0, 0xee, 0xff, 0xf7, 0xf8, 0xf7, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xf9, 0xf9, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfc, 0xfd, 0xff, 0xf7, 0xf4, 0xf1, 0xff, 0xf7, 0xf4, 0xef, 0xff, 0xf9, 0xf5, 0xf1, 0xff, 0xf8, 0xf3, 0xf0, 0xff, 0xf8, 0xf3, 0xf0, 0xff, 0xf8, 0xf3, 0xef, 0xff, 0xfa, 0xf7, 0xf4, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xda, 0xfb, 0xfb, 0xfb, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xfe, 0xfe, 0x07, 0xff, 0xff, 0xff, 0x8e, 0xd7, 0xd5, 0xd3, 0xfe, 0xd1, 0xd0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0xe8, 0xe8, 0xff, 0x9d, 0xa7, 0xa5, 0xff, 0x97, 0x9f, 0x9e, 0xff, 0xdf, 0xdf, 0xde, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfa, 0xf7, 0xf4, 0xff, 0xf8, 0xf4, 0xf3, 0xff, 0xfc, 0xfc, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf5, 0xf4, 0xff, 0xc6, 0xcb, 0xc7, 0xff, 0xf3, 0xf8, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xea, 0xe3, 0xe2, 0xff, 0xf0, 0xea, 0xe5, 0xff, 0xcb, 0xcd, 0xce, 0xff, 0x86, 0x8e, 0x99, 0xff, 0xbe, 0xc3, 0xce, 0xff, 0xd3, 0xd3, 0xda, 0xff, 0xdf, 0xda, 0xde, 0xff, 0xcb, 0xc5, 0xc5, 0xff, 0xbb, 0xbd, 0xc0, 0xff, 0xa9, 0xaf, 0xb1, 0xff, 0xaf, 0xb0, 0xaf, 0xff, 0xce, 0xc8, 0xc5, 0xff, 0xd3, 0xcc, 0xc3, 0xff, 0xde, 0xd6, 0xd1, 0xff, 0xed, 0xe2, 0xe2, 0xff, 0xe4, 0xde, 0xdb, 0xff, 0xe2, 0xdd, 0xdb, 0xff, 0xed, 0xe6, 0xe0, 0xff, 0xe6, 0xe5, 0xe3, 0xff, 0xc5, 0xc0, 0xc3, 0xff, 0xa5, 0xa9, 0xad, 0xff, 0xb2, 0xb7, 0xc1, 0xff, 0xdf, 0xd9, 0xd9, 0xff, 0xdd, 0xd7, 0xd5, 0xff, 0xd4, 0xcf, 0xcc, 0xff, 0xc1, 0xc1, 0xc6, 0xff, 0x90, 0xa2, 0xb7, 0xff, 0xbe, 0xc3, 0xd1, 0xff, 0xf4, 0xed, 0xec, 0xff, 0xfc, 0xfb, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xf8, 0xf7, 0xf7, 0xff, 0xf7, 0xf5, 0xf5, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xf7, 0xf4, 0xf1, 0xff, 0xf4, 0xf1, 0xed, 0xff, 0xf8, 0xf5, 0xf1, 0xff, 0xf8, 0xf3, 0xf0, 0xff, 0xf8, 0xf3, 0xf0, 0xff, 0xf8, 0xf3, 0xef, 0xff, 0xf9, 0xf7, 0xf3, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xfe, 0xfb, 0xfb, 0xfb, 0x8e, 0xfe, 0xfe, 0xfe, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xf8, 0xec, 0x29, 0xfe, 0xfe, 0xfe, 0xd1, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xe5, 0xe5, 0xff, 0xcf, 0xce, 0xce, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xfc, 0xfb, 0xff, 0xb4, 0xc1, 0xbf, 0xff, 0x91, 0x96, 0x96, 0xff, 0xbe, 0xbf, 0xbf, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xfa, 0xf8, 0xf4, 0xff, 0xfc, 0xf9, 0xf4, 0xff, 0xfc, 0xf8, 0xf5, 0xff, 0xfb, 0xf6, 0xf5, 0xff, 0xfb, 0xfb, 0xf9, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf3, 0xef, 0xff, 0xbd, 0xbf, 0xbf, 0xff, 0xc3, 0xc5, 0xca, 0xff, 0xd9, 0xd8, 0xdd, 0xff, 0xb4, 0xb6, 0xbe, 0xff, 0x99, 0xa0, 0xaf, 0xff, 0x67, 0x70, 0x83, 0xff, 0x5f, 0x67, 0x77, 0xff, 0x6c, 0x70, 0x7c, 0xff, 0x72, 0x79, 0x89, 0xff, 0x77, 0x7f, 0x8d, 0xff, 0x96, 0x99, 0x98, 0xff, 0x90, 0x90, 0x96, 0xff, 0x8b, 0x87, 0x8f, 0xff, 0xb0, 0xaa, 0xad, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xdc, 0xd4, 0xd4, 0xff, 0xe5, 0xdd, 0xda, 0xff, 0xf4, 0xf1, 0xea, 0xff, 0xf5, 0xeb, 0xe7, 0xff, 0xff, 0xf8, 0xf4, 0xff, 0xf8, 0xf9, 0xf6, 0xff, 0x9f, 0x9d, 0xa6, 0xff, 0xa8, 0x9d, 0x9f, 0xff, 0xaf, 0xab, 0xa4, 0xff, 0xa2, 0xa5, 0xa9, 0xff, 0xd3, 0xd0, 0xd3, 0xff, 0xf2, 0xec, 0xea, 0xff, 0xfa, 0xf5, 0xf1, 0xff, 0xde, 0xd9, 0xdc, 0xff, 0xa0, 0xab, 0xbd, 0xff, 0xd1, 0xd7, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xf9, 0xf9, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf8, 0xf8, 0xff, 0xf2, 0xf0, 0xf1, 0xff, 0xf4, 0xf2, 0xef, 0xff, 0xf7, 0xf4, 0xef, 0xff, 0xf6, 0xf3, 0xef, 0xff, 0xf7, 0xf2, 0xef, 0xff, 0xf8, 0xf3, 0xf0, 0xff, 0xf7, 0xf3, 0xef, 0xff, 0xf9, 0xf7, 0xf4, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xd1, 0xfe, 0xfe, 0xfe, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xba, 0xae, 0x56, 0xf4, 0xef, 0xe2, 0xf3, 0xfc, 0xfb, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6, 0xe8, 0xe5, 0xff, 0xd1, 0xd0, 0xcc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0xd9, 0xd7, 0xff, 0x98, 0xa1, 0x9f, 0xff, 0xa6, 0xaa, 0xa9, 0xff, 0xee, 0xeb, 0xeb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xfd, 0xfd, 0xfb, 0xff, 0xfa, 0xfd, 0xfd, 0xff, 0xfb, 0xfb, 0xfa, 0xff, 0xfb, 0xf8, 0xf5, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xf8, 0xf5, 0xf5, 0xff, 0xfc, 0xfb, 0xf9, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xfc, 0xfc, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xda, 0xdb, 0xdb, 0xff, 0x83, 0x89, 0x91, 0xff, 0x95, 0x9d, 0xaa, 0xff, 0x84, 0x8c, 0x99, 0xff, 0x53, 0x61, 0x70, 0xff, 0x3b, 0x44, 0x53, 0xff, 0x40, 0x44, 0x50, 0xff, 0x38, 0x3b, 0x46, 0xff, 0x2d, 0x2d, 0x34, 0xff, 0x33, 0x35, 0x3f, 0xff, 0x43, 0x4a, 0x5a, 0xff, 0x69, 0x72, 0x86, 0xff, 0xac, 0xb0, 0xbd, 0xff, 0xd4, 0xd3, 0xd4, 0xff, 0xad, 0xac, 0xad, 0xff, 0x80, 0x83, 0x90, 0xff, 0x84, 0x8b, 0x9d, 0xff, 0x9f, 0xa8, 0xb4, 0xff, 0xb6, 0xbc, 0xc4, 0xff, 0xe0, 0xe2, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9e, 0xa3, 0xad, 0xff, 0x81, 0x7d, 0x86, 0xff, 0xdb, 0xd0, 0xcc, 0xff, 0xc9, 0xbf, 0xb8, 0xff, 0xc5, 0xba, 0xbc, 0xff, 0xbe, 0xb7, 0xbb, 0xff, 0xe0, 0xde, 0xe0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf5, 0xff, 0xb4, 0xbb, 0xc4, 0xff, 0xb9, 0xc5, 0xca, 0xff, 0xff, 0xfc, 0xf9, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xf8, 0xfc, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfd, 0xfd, 0xfb, 0xff, 0xed, 0xe9, 0xe9, 0xff, 0xed, 0xea, 0xe7, 0xff, 0xf6, 0xf4, 0xef, 0xff, 0xf7, 0xf4, 0xf0, 0xff, 0xfb, 0xf3, 0xf0, 0xff, 0xfb, 0xf2, 0xef, 0xff, 0xfb, 0xf4, 0xef, 0xff, 0xfa, 0xf7, 0xf2, 0xff, 0xfc, 0xfb, 0xfa, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xf3, 0xfc, 0xfc, 0xfc, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 0x55, 0x03, 0x7b, 0x7b, 0x76, 0x8a, 0xbf, 0xbb, 0xaf, 0xfe, 0xf5, 0xee, 0xe2, 0xff, 0xfe, 0xfa, 0xfc, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xea, 0xef, 0xe8, 0xff, 0xbd, 0xc7, 0xc5, 0xff, 0xd5, 0xdf, 0xe3, 0xff, 0xb0, 0xb7, 0xb7, 0xff, 0x95, 0x9e, 0x9c, 0xff, 0xcd, 0xcd, 0xcd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfd, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xfc, 0xfc, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfa, 0xfb, 0xfc, 0xff, 0xfa, 0xf8, 0xf7, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xf7, 0xf6, 0xf5, 0xff, 0xfd, 0xfa, 0xfa, 0xff, 0xff, 0xfe, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x84, 0x85, 0x91, 0xff, 0x45, 0x46, 0x57, 0xff, 0x4a, 0x51, 0x5e, 0xff, 0x38, 0x45, 0x4c, 0xff, 0x3c, 0x3d, 0x47, 0xff, 0x41, 0x3c, 0x41, 0xff, 0x3c, 0x38, 0x38, 0xff, 0x48, 0x43, 0x45, 0xff, 0x53, 0x51, 0x55, 0xff, 0x4b, 0x4c, 0x53, 0xff, 0x4b, 0x4c, 0x57, 0xff, 0x4c, 0x4b, 0x59, 0xff, 0x71, 0x71, 0x7e, 0xff, 0x64, 0x6d, 0x76, 0xff, 0x57, 0x63, 0x6e, 0xff, 0x58, 0x5e, 0x71, 0xff, 0x48, 0x4d, 0x62, 0xff, 0x66, 0x6b, 0x81, 0xff, 0x7c, 0x83, 0x9b, 0xff, 0xbf, 0xcb, 0xde, 0xff, 0xa0, 0xab, 0xb8, 0xff, 0x70, 0x6a, 0x75, 0xff, 0xf5, 0xe5, 0xe1, 0xff, 0xe7, 0xe5, 0xdf, 0xff, 0xca, 0xca, 0xc5, 0xff, 0xf8, 0xee, 0xe9, 0xff, 0xdb, 0xcd, 0xcb, 0xff, 0xd2, 0xd0, 0xd4, 0xff, 0xea, 0xec, 0xec, 0xff, 0xff, 0xf8, 0xf5, 0xff, 0xbe, 0xc5, 0xd3, 0xff, 0xb2, 0xc5, 0xc9, 0xff, 0xec, 0xe4, 0xe1, 0xff, 0xf7, 0xf5, 0xf5, 0xff, 0xfb, 0xfe, 0xfc, 0xff, 0xfb, 0xfd, 0xfc, 0xff, 0xfc, 0xfc, 0xfd, 0xff, 0xfa, 0xfb, 0xfa, 0xff, 0xfa, 0xfa, 0xf7, 0xff, 0xff, 0xf9, 0xf9, 0xff, 0xf0, 0xec, 0xe9, 0xff, 0xed, 0xeb, 0xe7, 0xff, 0xf8, 0xf4, 0xf0, 0xff, 0xfe, 0xf4, 0xf1, 0xff, 0xfd, 0xf2, 0xf0, 0xff, 0xfd, 0xf4, 0xee, 0xff, 0xfa, 0xf6, 0xef, 0xff, 0xfd, 0xfa, 0xf9, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xfe, 0xfb, 0xfb, 0xfb, 0x8b, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x73, 0x73, 0x73, 0x0b, 0x76, 0x75, 0x76, 0xae, 0x7b, 0x7b, 0x77, 0xff, 0xc0, 0xbc, 0xaf, 0xff, 0xfa, 0xf4, 0xe8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x97, 0xa9, 0xae, 0xff, 0x71, 0x81, 0x8f, 0xff, 0xba, 0xb8, 0xba, 0xff, 0xf5, 0xec, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfd, 0xfc, 0xff, 0xff, 0xfd, 0xfb, 0xff, 0xfe, 0xfe, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xfb, 0xf9, 0xf7, 0xff, 0xfc, 0xf9, 0xf4, 0xff, 0xf9, 0xf7, 0xf6, 0xff, 0xfc, 0xf9, 0xf9, 0xff, 0xfd, 0xfd, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0xc2, 0xc4, 0xff, 0x3e, 0x3f, 0x4a, 0xff, 0x4a, 0x46, 0x4e, 0xff, 0x6a, 0x64, 0x66, 0xff, 0x81, 0x80, 0x7e, 0xff, 0x4e, 0x48, 0x47, 0xff, 0x55, 0x4e, 0x51, 0xff, 0x4d, 0x48, 0x4c, 0xff, 0x5c, 0x57, 0x5a, 0xff, 0x50, 0x4b, 0x4d, 0xff, 0x47, 0x45, 0x4a, 0xff, 0x38, 0x39, 0x3f, 0xff, 0x2f, 0x30, 0x35, 0xff, 0x2f, 0x2b, 0x37, 0xff, 0x2f, 0x31, 0x3e, 0xff, 0x42, 0x4a, 0x55, 0xff, 0x3d, 0x44, 0x4d, 0xff, 0x3f, 0x43, 0x48, 0xff, 0x45, 0x4c, 0x56, 0xff, 0x4f, 0x60, 0x73, 0xff, 0x58, 0x6a, 0x85, 0xff, 0x48, 0x50, 0x63, 0xff, 0xbf, 0xc1, 0xc1, 0xff, 0xf1, 0xeb, 0xec, 0xff, 0x96, 0x96, 0xa7, 0xff, 0xb2, 0xb7, 0xc7, 0xff, 0xe5, 0xe3, 0xe9, 0xff, 0xcf, 0xce, 0xd0, 0xff, 0xd8, 0xd9, 0xde, 0xff, 0xda, 0xdb, 0xd9, 0xff, 0xfc, 0xf6, 0xee, 0xff, 0xaf, 0xba, 0xca, 0xff, 0xa9, 0xb9, 0xcf, 0xff, 0xe3, 0xd6, 0xe1, 0xff, 0xea, 0xe3, 0xe3, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfb, 0xfb, 0xfa, 0xff, 0xf7, 0xf7, 0xf7, 0xff, 0xfa, 0xfa, 0xf9, 0xff, 0xfe, 0xfe, 0xfd, 0xff, 0xff, 0xfb, 0xfa, 0xff, 0xf5, 0xf1, 0xee, 0xff, 0xf0, 0xee, 0xea, 0xff, 0xf9, 0xf5, 0xf1, 0xff, 0xfd, 0xf4, 0xf1, 0xff, 0xfc, 0xf2, 0xf0, 0xff, 0xfb, 0xf3, 0xee, 0xff, 0xf8, 0xf4, 0xee, 0xff, 0xfb, 0xf9, 0xf7, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xaf, 0xff, 0xff, 0xff, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x7f, 0x7f, 0x16, 0x85, 0x82, 0x81, 0xc9, 0x80, 0x7e, 0x7d, 0xff, 0x7d, 0x7b, 0x7a, 0xff, 0xb4, 0xb1, 0xa7, 0xff, 0xf3, 0xee, 0xe3, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xa8, 0xb0, 0xb4, 0xff, 0xb5, 0xb8, 0xb9, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xfd, 0xfc, 0xff, 0xfe, 0xfd, 0xfc, 0xff, 0xfd, 0xfe, 0xfe, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfd, 0xfc, 0xff, 0xfe, 0xfa, 0xf5, 0xff, 0xfe, 0xf9, 0xf3, 0xff, 0xfb, 0xf9, 0xf5, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xff, 0xfc, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0x78, 0x78, 0x79, 0xff, 0x3a, 0x3d, 0x40, 0xff, 0x7d, 0x7c, 0x7f, 0xff, 0xd1, 0xc9, 0xcc, 0xff, 0x96, 0x8f, 0x93, 0xff, 0x5e, 0x5a, 0x5c, 0xff, 0x5b, 0x59, 0x5d, 0xff, 0x44, 0x44, 0x49, 0xff, 0x3d, 0x3c, 0x41, 0xff, 0x41, 0x3d, 0x42, 0xff, 0x3a, 0x35, 0x3a, 0xff, 0x34, 0x30, 0x35, 0xff, 0x3d, 0x3a, 0x3f, 0xff, 0x37, 0x33, 0x39, 0xff, 0x3e, 0x3b, 0x41, 0xff, 0x3a, 0x3a, 0x3e, 0xff, 0x2c, 0x2c, 0x2f, 0xff, 0x31, 0x31, 0x36, 0xff, 0x38, 0x3e, 0x44, 0xff, 0x42, 0x4a, 0x51, 0xff, 0x25, 0x26, 0x30, 0xff, 0x2f, 0x2f, 0x39, 0xff, 0x6c, 0x6e, 0x79, 0xff, 0x68, 0x67, 0x70, 0xff, 0x72, 0x68, 0x6e, 0xff, 0x89, 0x80, 0x89, 0xff, 0x7b, 0x7a, 0x87, 0xff, 0x6e, 0x74, 0x8a, 0xff, 0x95, 0x9f, 0xb2, 0xff, 0xb6, 0xb8, 0xc5, 0xff, 0xe5, 0xe3, 0xe6, 0xff, 0xc1, 0xc8, 0xd5, 0xff, 0x97, 0xa6, 0xc2, 0xff, 0xae, 0xb0, 0xc7, 0xff, 0xb2, 0xbb, 0xc6, 0xff, 0xec, 0xed, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfc, 0xf8, 0xff, 0xfc, 0xfc, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf8, 0xf9, 0xff, 0xf6, 0xf3, 0xf0, 0xff, 0xf1, 0xee, 0xe9, 0xff, 0xf2, 0xef, 0xeb, 0xff, 0xf8, 0xf3, 0xf0, 0xff, 0xf9, 0xf4, 0xf1, 0xff, 0xfb, 0xf6, 0xf2, 0xff, 0xf8, 0xf5, 0xf1, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfc, 0xfb, 0xfa, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xc9, 0xff, 0xff, 0xff, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x7f, 0x7f, 0x1a, 0x81, 0x80, 0x7f, 0xd6, 0x89, 0x88, 0x87, 0xff, 0x88, 0x86, 0x85, 0xff, 0x76, 0x74, 0x74, 0xff, 0xab, 0xa7, 0x9e, 0xff, 0xf5, 0xf1, 0xe5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0xcb, 0xcb, 0xff, 0xcb, 0xd4, 0xcd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xfe, 0xff, 0xfc, 0xfe, 0xfb, 0xff, 0xfb, 0xfc, 0xfb, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfd, 0xfb, 0xfb, 0xff, 0xfd, 0xf9, 0xf4, 0xff, 0xff, 0xfb, 0xf4, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0xe2, 0xe0, 0xff, 0x47, 0x47, 0x48, 0xff, 0x3d, 0x3b, 0x3e, 0xff, 0x9e, 0x9d, 0xa1, 0xff, 0xca, 0xca, 0xce, 0xff, 0x66, 0x66, 0x6b, 0xff, 0x46, 0x47, 0x4c, 0xff, 0x44, 0x44, 0x48, 0xff, 0x43, 0x42, 0x46, 0xff, 0x33, 0x32, 0x36, 0xff, 0x39, 0x33, 0x36, 0xff, 0x31, 0x2a, 0x2c, 0xff, 0x33, 0x2c, 0x2f, 0xff, 0x3e, 0x37, 0x39, 0xff, 0x32, 0x2b, 0x2d, 0xff, 0x25, 0x1e, 0x20, 0xff, 0x2d, 0x25, 0x27, 0xff, 0x2c, 0x24, 0x26, 0xff, 0x31, 0x28, 0x2d, 0xff, 0x31, 0x2b, 0x31, 0xff, 0x27, 0x23, 0x25, 0xff, 0x2d, 0x24, 0x24, 0xff, 0x38, 0x37, 0x3f, 0xff, 0x3d, 0x41, 0x54, 0xff, 0x74, 0x75, 0x87, 0xff, 0xbe, 0xb7, 0xbf, 0xff, 0xdc, 0xca, 0xc9, 0xff, 0xbc, 0xac, 0xa6, 0xff, 0x9b, 0x8f, 0x92, 0xff, 0x92, 0x8f, 0x96, 0xff, 0x86, 0x84, 0x94, 0xff, 0x94, 0x94, 0xa4, 0xff, 0xb0, 0xbb, 0xcc, 0xff, 0xa7, 0xbe, 0xd2, 0xff, 0xc7, 0xca, 0xd8, 0xff, 0xaa, 0xae, 0xba, 0xff, 0xb6, 0xbc, 0xc4, 0xff, 0xe8, 0xe4, 0xe8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfc, 0xfa, 0xff, 0xfd, 0xfd, 0xff, 0xff, 0xfc, 0xfd, 0xfe, 0xff, 0xfe, 0xfc, 0xf8, 0xff, 0xf8, 0xf4, 0xee, 0xff, 0xef, 0xed, 0xe8, 0xff, 0xf3, 0xef, 0xec, 0xff, 0xf4, 0xef, 0xec, 0xff, 0xf4, 0xf0, 0xec, 0xff, 0xf4, 0xf1, 0xed, 0xff, 0xfc, 0xf8, 0xf3, 0xff, 0xfd, 0xfc, 0xfb, 0xff, 0xfc, 0xfd, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xd6, 0xff, 0xff, 0xff, 0x1a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x87, 0x87, 0x87, 0x20, 0x85, 0x82, 0x81, 0xde, 0x82, 0x80, 0x7f, 0xff, 0x8b, 0x89, 0x88, 0xff, 0x89, 0x87, 0x86, 0xff, 0x75, 0x73, 0x73, 0xff, 0xa8, 0xa6, 0x9c, 0xff, 0xf6, 0xf3, 0xe6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcb, 0xd7, 0xd4, 0xff, 0xcb, 0xd4, 0xcd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xfe, 0xff, 0xff, 0xfb, 0xff, 0xfe, 0xff, 0xfc, 0xfe, 0xfd, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xfd, 0xfd, 0xfc, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xfe, 0xfa, 0xf6, 0xff, 0xff, 0xfc, 0xf6, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd6, 0xd5, 0xd1, 0xff, 0x41, 0x41, 0x41, 0xff, 0x44, 0x40, 0x45, 0xff, 0x6f, 0x6e, 0x75, 0xff, 0x7e, 0x82, 0x87, 0xff, 0x47, 0x49, 0x4b, 0xff, 0x37, 0x38, 0x3b, 0xff, 0x39, 0x39, 0x3c, 0xff, 0x3b, 0x3a, 0x3d, 0xff, 0x36, 0x35, 0x39, 0xff, 0x30, 0x29, 0x2b, 0xff, 0x30, 0x28, 0x28, 0xff, 0x35, 0x2e, 0x2e, 0xff, 0x32, 0x2a, 0x2a, 0xff, 0x23, 0x1b, 0x1b, 0xff, 0x25, 0x20, 0x20, 0xff, 0x2c, 0x28, 0x2a, 0xff, 0x2a, 0x25, 0x27, 0xff, 0x29, 0x22, 0x24, 0xff, 0x27, 0x23, 0x24, 0xff, 0x24, 0x1f, 0x1f, 0xff, 0x2b, 0x23, 0x26, 0xff, 0x44, 0x46, 0x54, 0xff, 0x4d, 0x56, 0x6c, 0xff, 0x57, 0x63, 0x7c, 0xff, 0x77, 0x87, 0xa7, 0xff, 0x79, 0x8a, 0xa4, 0xff, 0xaa, 0xb0, 0xbc, 0xff, 0xe5, 0xe2, 0xea, 0xff, 0xb7, 0xb9, 0xc5, 0xff, 0xac, 0xaa, 0xb4, 0xff, 0x9a, 0x96, 0x9f, 0xff, 0x93, 0x98, 0xa7, 0xff, 0x9f, 0xb6, 0xc6, 0xff, 0xe2, 0xe1, 0xe4, 0xff, 0xf0, 0xe1, 0xe1, 0xff, 0xcb, 0xc9, 0xcd, 0xff, 0xc1, 0xbf, 0xc4, 0xff, 0xe6, 0xe1, 0xe4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xf8, 0xf6, 0xff, 0xf9, 0xf4, 0xf2, 0xff, 0xfb, 0xf4, 0xef, 0xff, 0xf5, 0xef, 0xed, 0xff, 0xf2, 0xed, 0xec, 0xff, 0xf6, 0xf4, 0xef, 0xff, 0xfb, 0xf9, 0xf6, 0xff, 0xfb, 0xfa, 0xfb, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xde, 0xff, 0xff, 0xff, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x7f, 0x7f, 0x1c, 0x85, 0x87, 0x82, 0xdb, 0x89, 0x87, 0x87, 0xff, 0x84, 0x82, 0x81, 0xff, 0x87, 0x85, 0x84, 0xff, 0x87, 0x85, 0x84, 0xff, 0x7c, 0x7a, 0x7a, 0xff, 0xa1, 0xa0, 0x96, 0xff, 0xee, 0xee, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8, 0xdf, 0xde, 0xff, 0xda, 0xe0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfd, 0xfb, 0xff, 0xfc, 0xf9, 0xf8, 0xff, 0xfc, 0xf9, 0xf6, 0xff, 0xfe, 0xfa, 0xf6, 0xff, 0xfe, 0xfa, 0xf6, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf8, 0xf5, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xf2, 0xf4, 0xe9, 0xff, 0x5f, 0x5e, 0x5b, 0xff, 0x39, 0x37, 0x3c, 0xff, 0x4e, 0x4f, 0x58, 0xff, 0x46, 0x48, 0x50, 0xff, 0x41, 0x40, 0x41, 0xff, 0x42, 0x3c, 0x3e, 0xff, 0x38, 0x34, 0x36, 0xff, 0x2f, 0x2e, 0x30, 0xff, 0x3a, 0x39, 0x3a, 0xff, 0x2f, 0x2b, 0x2c, 0xff, 0x2b, 0x26, 0x27, 0xff, 0x2e, 0x2a, 0x2a, 0xff, 0x1a, 0x15, 0x14, 0xff, 0x20, 0x1b, 0x1a, 0xff, 0x2a, 0x25, 0x26, 0xff, 0x2d, 0x28, 0x2b, 0xff, 0x2b, 0x25, 0x28, 0xff, 0x26, 0x21, 0x22, 0xff, 0x2c, 0x28, 0x28, 0xff, 0x32, 0x2c, 0x2c, 0xff, 0x2c, 0x23, 0x25, 0xff, 0x34, 0x2f, 0x3a, 0xff, 0x44, 0x46, 0x52, 0xff, 0x47, 0x4c, 0x58, 0xff, 0x4e, 0x56, 0x66, 0xff, 0x43, 0x51, 0x62, 0xff, 0x54, 0x5f, 0x74, 0xff, 0x65, 0x6c, 0x86, 0xff, 0x71, 0x7b, 0x90, 0xff, 0x9e, 0xa2, 0xae, 0xff, 0xe3, 0xe1, 0xe4, 0xff, 0xcf, 0xc9, 0xd0, 0xff, 0x9d, 0xaa, 0xbe, 0xff, 0xc5, 0xce, 0xda, 0xff, 0xec, 0xdb, 0xdb, 0xff, 0xd8, 0xd0, 0xd4, 0xff, 0xcc, 0xd0, 0xd6, 0xff, 0xc0, 0xbe, 0xc6, 0xff, 0xe7, 0xe4, 0xe7, 0xff, 0xf6, 0xf4, 0xf1, 0xff, 0xf1, 0xf0, 0xee, 0xff, 0xdc, 0xd9, 0xd9, 0xff, 0xca, 0xc4, 0xc6, 0xff, 0xcc, 0xc8, 0xca, 0xff, 0xde, 0xde, 0xdf, 0xff, 0xfd, 0xfb, 0xf7, 0xff, 0xfe, 0xf9, 0xf8, 0xff, 0xf7, 0xf5, 0xf1, 0xff, 0xf4, 0xf6, 0xf2, 0xff, 0xfa, 0xfa, 0xfb, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xdc, 0xfe, 0xfe, 0xfe, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x91, 0x85, 0x79, 0x15, 0x89, 0x86, 0x7f, 0xd6, 0x87, 0x87, 0x82, 0xff, 0x8b, 0x89, 0x88, 0xff, 0x86, 0x84, 0x84, 0xff, 0x88, 0x86, 0x85, 0xff, 0x89, 0x87, 0x86, 0xff, 0x81, 0x7f, 0x7f, 0xff, 0x9f, 0x9d, 0x93, 0xff, 0xe4, 0xe5, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5, 0xda, 0xd6, 0xff, 0xcc, 0xd8, 0xd9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xf9, 0xff, 0xfe, 0xfb, 0xf9, 0xff, 0xfc, 0xf8, 0xf5, 0xff, 0xfc, 0xf7, 0xf0, 0xff, 0xfb, 0xf6, 0xee, 0xff, 0xfb, 0xf7, 0xf2, 0xff, 0xfd, 0xfa, 0xf7, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xf9, 0xf9, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x98, 0x89, 0xff, 0x36, 0x2e, 0x2e, 0xff, 0x48, 0x45, 0x4a, 0xff, 0x3c, 0x3c, 0x41, 0xff, 0x3a, 0x3a, 0x3c, 0xff, 0x45, 0x40, 0x43, 0xff, 0x34, 0x2e, 0x30, 0xff, 0x32, 0x2d, 0x2f, 0xff, 0x30, 0x2b, 0x2d, 0xff, 0x34, 0x2f, 0x30, 0xff, 0x2e, 0x29, 0x2a, 0xff, 0x1d, 0x18, 0x19, 0xff, 0x1e, 0x19, 0x18, 0xff, 0x25, 0x1f, 0x1e, 0xff, 0x26, 0x21, 0x1f, 0xff, 0x22, 0x1f, 0x1e, 0xff, 0x24, 0x20, 0x1f, 0xff, 0x28, 0x23, 0x24, 0xff, 0x25, 0x21, 0x23, 0xff, 0x2c, 0x27, 0x2a, 0xff, 0x2c, 0x25, 0x2a, 0xff, 0x30, 0x28, 0x2b, 0xff, 0x34, 0x2c, 0x30, 0xff, 0x31, 0x2c, 0x35, 0xff, 0x36, 0x34, 0x3f, 0xff, 0x39, 0x3b, 0x45, 0xff, 0x39, 0x3d, 0x48, 0xff, 0x40, 0x47, 0x55, 0xff, 0x4a, 0x53, 0x64, 0xff, 0x60, 0x67, 0x78, 0xff, 0x81, 0x8c, 0x99, 0xff, 0x89, 0x93, 0xa6, 0xff, 0x71, 0x81, 0xa3, 0xff, 0xad, 0xbf, 0xd3, 0xff, 0xea, 0xe3, 0xe5, 0xff, 0xd3, 0xcd, 0xcd, 0xff, 0xcc, 0xcc, 0xcd, 0xff, 0xc7, 0xc2, 0xc6, 0xff, 0xd2, 0xca, 0xce, 0xff, 0xdd, 0xd4, 0xd8, 0xff, 0xe0, 0xd8, 0xdd, 0xff, 0xda, 0xd7, 0xd5, 0xff, 0xc5, 0xc2, 0xbe, 0xff, 0xb6, 0xb2, 0xb0, 0xff, 0xaa, 0xa5, 0xa5, 0xff, 0xc1, 0xbf, 0xc4, 0xff, 0xfb, 0xf8, 0xfa, 0xff, 0xfd, 0xfc, 0xf1, 0xff, 0xf4, 0xf8, 0xef, 0xff, 0xfa, 0xfb, 0xfc, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xd7, 0xff, 0xff, 0xff, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4c, 0x4c, 0x4c, 0x0a, 0x84, 0x7f, 0x7a, 0xc8, 0x89, 0x87, 0x82, 0xff, 0x88, 0x87, 0x83, 0xff, 0x8d, 0x8b, 0x88, 0xff, 0x88, 0x87, 0x84, 0xff, 0x89, 0x88, 0x86, 0xff, 0x89, 0x87, 0x87, 0xff, 0x83, 0x81, 0x81, 0xff, 0x9b, 0x9c, 0x92, 0xff, 0xdc, 0xda, 0xcb, 0xff, 0xfc, 0xfa, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc6, 0xc7, 0xc2, 0xff, 0xaa, 0xb5, 0xbf, 0xff, 0xf8, 0xf5, 0xf4, 0xff, 0xfd, 0xf6, 0xed, 0xff, 0xf6, 0xf2, 0xed, 0xff, 0xf7, 0xf2, 0xed, 0xff, 0xf9, 0xf5, 0xef, 0xff, 0xf9, 0xf5, 0xef, 0xff, 0xfb, 0xf8, 0xf3, 0xff, 0xff, 0xfc, 0xf9, 0xff, 0xff, 0xfd, 0xf9, 0xff, 0xfd, 0xfb, 0xf7, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xfe, 0xfd, 0xf9, 0xff, 0xfb, 0xf9, 0xf7, 0xff, 0xf7, 0xf2, 0xee, 0xff, 0xfa, 0xf7, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xec, 0xe5, 0xff, 0x65, 0x5d, 0x5c, 0xff, 0x36, 0x32, 0x35, 0xff, 0x3f, 0x3e, 0x41, 0xff, 0x36, 0x36, 0x37, 0xff, 0x3d, 0x3c, 0x3d, 0xff, 0x34, 0x2f, 0x32, 0xff, 0x38, 0x31, 0x35, 0xff, 0x2c, 0x25, 0x2a, 0xff, 0x2a, 0x24, 0x24, 0xff, 0x32, 0x2c, 0x2b, 0xff, 0x25, 0x1e, 0x1e, 0xff, 0x2e, 0x28, 0x28, 0xff, 0x34, 0x2e, 0x2e, 0xff, 0x30, 0x2c, 0x2a, 0xff, 0x23, 0x22, 0x1f, 0xff, 0x1d, 0x1b, 0x18, 0xff, 0x22, 0x1b, 0x17, 0xff, 0x23, 0x19, 0x14, 0xff, 0x24, 0x1b, 0x17, 0xff, 0x26, 0x1c, 0x1c, 0xff, 0x26, 0x1e, 0x21, 0xff, 0x27, 0x21, 0x22, 0xff, 0x24, 0x1e, 0x1f, 0xff, 0x23, 0x1c, 0x21, 0xff, 0x36, 0x32, 0x39, 0xff, 0x41, 0x43, 0x4c, 0xff, 0x46, 0x4a, 0x56, 0xff, 0x49, 0x4a, 0x5b, 0xff, 0x42, 0x46, 0x59, 0xff, 0x3c, 0x44, 0x54, 0xff, 0x68, 0x6e, 0x7b, 0xff, 0x86, 0x90, 0xa9, 0xff, 0x87, 0x99, 0xb5, 0xff, 0xc7, 0xca, 0xda, 0xff, 0xde, 0xd6, 0xdf, 0xff, 0xe3, 0xdd, 0xdf, 0xff, 0xf4, 0xec, 0xe5, 0xff, 0xe1, 0xd7, 0xd3, 0xff, 0xde, 0xd6, 0xd7, 0xff, 0xd1, 0xc9, 0xc9, 0xff, 0xe0, 0xda, 0xd7, 0xff, 0xf0, 0xed, 0xe6, 0xff, 0xef, 0xea, 0xe0, 0xff, 0xf4, 0xe2, 0xdd, 0xff, 0xad, 0xa3, 0xaf, 0xff, 0xbd, 0xc1, 0xcd, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xf7, 0xf8, 0xeb, 0xff, 0xf8, 0xfa, 0xf8, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfc, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xc8, 0xff, 0xff, 0xff, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 0x55, 0x03, 0x4d, 0x53, 0x53, 0xb1, 0x78, 0x79, 0x76, 0xff, 0x83, 0x82, 0x7e, 0xff, 0x85, 0x84, 0x80, 0xff, 0x8b, 0x8a, 0x86, 0xff, 0x89, 0x88, 0x83, 0xff, 0x8a, 0x88, 0x86, 0xff, 0x8e, 0x8b, 0x8b, 0xff, 0x87, 0x86, 0x86, 0xff, 0x95, 0x99, 0x8e, 0xff, 0xcc, 0xc8, 0xb8, 0xff, 0xf2, 0xf1, 0xe6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xf9, 0xf3, 0xff, 0xc4, 0xc2, 0xb8, 0xff, 0x9a, 0x9f, 0xa6, 0xff, 0xe5, 0xe0, 0xde, 0xff, 0xf9, 0xf2, 0xeb, 0xff, 0xf5, 0xf1, 0xec, 0xff, 0xf9, 0xf5, 0xf0, 0xff, 0xf9, 0xf6, 0xf3, 0xff, 0xf9, 0xf5, 0xf3, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xfd, 0xfb, 0xf7, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xfd, 0xf8, 0xf4, 0xff, 0xfc, 0xf8, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf6, 0xf1, 0xff, 0xc8, 0xce, 0xd2, 0xff, 0xb0, 0xb8, 0xc2, 0xff, 0x66, 0x6b, 0x70, 0xff, 0x30, 0x33, 0x33, 0xff, 0x3e, 0x3f, 0x40, 0xff, 0x36, 0x35, 0x39, 0xff, 0x31, 0x30, 0x34, 0xff, 0x32, 0x31, 0x34, 0xff, 0x2e, 0x2d, 0x2e, 0xff, 0x37, 0x36, 0x36, 0xff, 0x3d, 0x39, 0x3c, 0xff, 0x3a, 0x36, 0x38, 0xff, 0x32, 0x2e, 0x32, 0xff, 0x2b, 0x29, 0x2f, 0xff, 0x2b, 0x29, 0x2f, 0xff, 0x2f, 0x2c, 0x2e, 0xff, 0x30, 0x2d, 0x2b, 0xff, 0x2e, 0x2c, 0x2a, 0xff, 0x30, 0x2b, 0x28, 0xff, 0x2a, 0x23, 0x22, 0xff, 0x26, 0x1f, 0x1c, 0xff, 0x24, 0x1d, 0x16, 0xff, 0x1c, 0x19, 0x15, 0xff, 0x23, 0x20, 0x1c, 0xff, 0x23, 0x1e, 0x1b, 0xff, 0x1e, 0x17, 0x18, 0xff, 0x26, 0x20, 0x25, 0xff, 0x32, 0x2e, 0x37, 0xff, 0x3b, 0x3a, 0x46, 0xff, 0x3c, 0x3c, 0x4a, 0xff, 0x3b, 0x40, 0x4d, 0xff, 0x3e, 0x44, 0x53, 0xff, 0x57, 0x5b, 0x68, 0xff, 0x6f, 0x7b, 0x90, 0xff, 0x7c, 0x90, 0xad, 0xff, 0x9a, 0xa5, 0xb9, 0xff, 0xa5, 0xab, 0xb8, 0xff, 0xb1, 0xbe, 0xd1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xfe, 0xfb, 0xf8, 0xff, 0xf6, 0xf0, 0xef, 0xff, 0xfa, 0xf8, 0xf5, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xef, 0xe5, 0xdf, 0xff, 0xa6, 0xa3, 0xa6, 0xff, 0xde, 0xdf, 0xe0, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xf9, 0xfa, 0xf4, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xfd, 0xfc, 0xff, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xb1, 0xff, 0xff, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3d, 0x44, 0x48, 0x8d, 0x4e, 0x51, 0x51, 0xff, 0x74, 0x73, 0x6f, 0xff, 0x80, 0x7f, 0x7a, 0xff, 0x83, 0x82, 0x7e, 0xff, 0x89, 0x88, 0x84, 0xff, 0x89, 0x89, 0x84, 0xff, 0x8c, 0x8a, 0x88, 0xff, 0x91, 0x8e, 0x8e, 0xff, 0x87, 0x86, 0x86, 0xff, 0x92, 0x95, 0x8a, 0xff, 0xc5, 0xc1, 0xb2, 0xff, 0xee, 0xec, 0xe2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xec, 0xe3, 0xff, 0xcd, 0xc7, 0xb6, 0xff, 0x9f, 0xa1, 0xa1, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xff, 0xfa, 0xf3, 0xff, 0xf7, 0xf4, 0xef, 0xff, 0xf8, 0xf4, 0xef, 0xff, 0xf7, 0xf4, 0xf1, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xff, 0xfd, 0xfb, 0xff, 0xff, 0xfe, 0xfc, 0xff, 0xff, 0xfd, 0xfb, 0xff, 0xff, 0xfe, 0xf8, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xfb, 0xfb, 0xfa, 0xff, 0xb8, 0xc2, 0xcb, 0xff, 0x63, 0x68, 0x77, 0xff, 0x47, 0x46, 0x54, 0xff, 0x52, 0x59, 0x64, 0xff, 0x42, 0x48, 0x52, 0xff, 0x34, 0x31, 0x31, 0xff, 0x41, 0x39, 0x36, 0xff, 0x3f, 0x3b, 0x42, 0xff, 0x38, 0x34, 0x3c, 0xff, 0x38, 0x36, 0x3c, 0xff, 0x38, 0x38, 0x3d, 0xff, 0x47, 0x47, 0x4b, 0xff, 0x4b, 0x4e, 0x55, 0xff, 0x41, 0x44, 0x4c, 0xff, 0x3e, 0x41, 0x49, 0xff, 0x37, 0x39, 0x43, 0xff, 0x2c, 0x2f, 0x39, 0xff, 0x29, 0x2d, 0x37, 0xff, 0x30, 0x34, 0x3f, 0xff, 0x39, 0x3d, 0x48, 0xff, 0x3b, 0x3d, 0x47, 0xff, 0x34, 0x37, 0x40, 0xff, 0x2f, 0x31, 0x38, 0xff, 0x31, 0x2c, 0x2c, 0xff, 0x21, 0x17, 0x13, 0xff, 0x22, 0x18, 0x10, 0xff, 0x25, 0x1e, 0x19, 0xff, 0x21, 0x18, 0x18, 0xff, 0x27, 0x1c, 0x1f, 0xff, 0x2c, 0x22, 0x27, 0xff, 0x37, 0x30, 0x3a, 0xff, 0x3c, 0x3b, 0x46, 0xff, 0x3c, 0x3e, 0x48, 0xff, 0x39, 0x3d, 0x47, 0xff, 0x45, 0x46, 0x50, 0xff, 0x51, 0x5a, 0x6d, 0xff, 0x69, 0x76, 0x8f, 0xff, 0x9b, 0xa2, 0xb0, 0xff, 0xbf, 0xc3, 0xc7, 0xff, 0x96, 0xa7, 0xb9, 0xff, 0xad, 0xb8, 0xcf, 0xff, 0xfc, 0xf8, 0xfa, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfb, 0xf8, 0xf8, 0xff, 0xea, 0xe9, 0xea, 0xff, 0xf6, 0xf5, 0xf3, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xfa, 0xf9, 0xf3, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xce, 0xc3, 0xc0, 0xff, 0xb1, 0xb2, 0xbc, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfd, 0xfc, 0xf5, 0xff, 0xfc, 0xfd, 0xfe, 0xff, 0xfd, 0xfc, 0xff, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x4b, 0x4e, 0x55, 0x3a, 0x43, 0x49, 0xfe, 0x4a, 0x4c, 0x4e, 0xff, 0x77, 0x74, 0x72, 0xff, 0x81, 0x82, 0x78, 0xff, 0x84, 0x84, 0x7f, 0xff, 0x89, 0x89, 0x86, 0xff, 0x88, 0x89, 0x84, 0xff, 0x8a, 0x8b, 0x88, 0xff, 0x90, 0x90, 0x8f, 0xff, 0x89, 0x8a, 0x8a, 0xff, 0x8c, 0x8c, 0x85, 0xff, 0xce, 0xcb, 0xbb, 0xff, 0xff, 0xfd, 0xfb, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xe1, 0xdd, 0xd0, 0xff, 0xd6, 0xcc, 0xbd, 0xff, 0xa0, 0xa7, 0xa4, 0xff, 0xd7, 0xd7, 0xd8, 0xff, 0xff, 0xf8, 0xf1, 0xff, 0xf7, 0xef, 0xe8, 0xff, 0xf5, 0xef, 0xe8, 0xff, 0xf5, 0xf2, 0xed, 0xff, 0xf6, 0xf4, 0xee, 0xff, 0xf7, 0xf4, 0xf0, 0xff, 0xf8, 0xf4, 0xf3, 0xff, 0xef, 0xed, 0xea, 0xff, 0xe6, 0xe3, 0xe3, 0xff, 0xe2, 0xde, 0xe3, 0xff, 0xbc, 0xc4, 0xd0, 0xff, 0x75, 0x80, 0x94, 0xff, 0x28, 0x2d, 0x3c, 0xff, 0x28, 0x29, 0x31, 0xff, 0x4d, 0x4d, 0x52, 0xff, 0x4d, 0x4e, 0x54, 0xff, 0x3a, 0x3d, 0x43, 0xff, 0x35, 0x34, 0x35, 0xff, 0x42, 0x3d, 0x3d, 0xff, 0x44, 0x42, 0x4b, 0xff, 0x4d, 0x4d, 0x58, 0xff, 0x51, 0x55, 0x63, 0xff, 0x58, 0x5e, 0x70, 0xff, 0x61, 0x68, 0x7c, 0xff, 0x5f, 0x66, 0x7a, 0xff, 0x58, 0x5e, 0x71, 0xff, 0x58, 0x5d, 0x71, 0xff, 0x5c, 0x60, 0x7b, 0xff, 0x56, 0x63, 0x7f, 0xff, 0x52, 0x63, 0x82, 0xff, 0x53, 0x62, 0x81, 0xff, 0x57, 0x68, 0x83, 0xff, 0x56, 0x5f, 0x7b, 0xff, 0x4e, 0x58, 0x72, 0xff, 0x44, 0x4d, 0x61, 0xff, 0x3e, 0x3a, 0x44, 0xff, 0x28, 0x1e, 0x24, 0xff, 0x1b, 0x10, 0x0c, 0xff, 0x1d, 0x14, 0x0d, 0xff, 0x22, 0x1a, 0x18, 0xff, 0x22, 0x17, 0x17, 0xff, 0x21, 0x18, 0x1a, 0xff, 0x27, 0x22, 0x27, 0xff, 0x3e, 0x3e, 0x46, 0xff, 0x3d, 0x41, 0x4c, 0xff, 0x2b, 0x2b, 0x33, 0xff, 0x35, 0x34, 0x3e, 0xff, 0x3a, 0x41, 0x53, 0xff, 0x57, 0x61, 0x74, 0xff, 0x87, 0x93, 0xa1, 0xff, 0xae, 0xb1, 0xbf, 0xff, 0xa7, 0xb0, 0xc2, 0xff, 0x9a, 0xa4, 0xb9, 0xff, 0xac, 0xb6, 0xc8, 0xff, 0xe4, 0xe9, 0xf3, 0xff, 0xf8, 0xf3, 0xf2, 0xff, 0xe8, 0xe7, 0xe4, 0xff, 0xff, 0xfe, 0xfb, 0xff, 0xfc, 0xfb, 0xf5, 0xff, 0xf7, 0xf4, 0xf2, 0xff, 0xfe, 0xfb, 0xf4, 0xff, 0xe9, 0xe5, 0xdb, 0xff, 0xae, 0xae, 0xb5, 0xff, 0xee, 0xed, 0xf0, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfd, 0xfc, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xfe, 0xfc, 0xfc, 0xfc, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x5d, 0x69, 0x29, 0x48, 0x4d, 0x52, 0xf3, 0x32, 0x3c, 0x46, 0xff, 0x44, 0x47, 0x4c, 0xff, 0x79, 0x77, 0x75, 0xff, 0x86, 0x87, 0x7d, 0xff, 0x89, 0x89, 0x83, 0xff, 0x8c, 0x8d, 0x8a, 0xff, 0x88, 0x89, 0x85, 0xff, 0x87, 0x88, 0x85, 0xff, 0x8f, 0x90, 0x8e, 0xff, 0x8f, 0x90, 0x90, 0xff, 0x8b, 0x8a, 0x84, 0xff, 0xd9, 0xd6, 0xc6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf0, 0xe9, 0xff, 0xd9, 0xd6, 0xc3, 0xff, 0xdf, 0xd9, 0xc8, 0xff, 0xa1, 0xa4, 0xa4, 0xff, 0xcc, 0xcb, 0xcd, 0xff, 0xfa, 0xf4, 0xec, 0xff, 0xf3, 0xef, 0xe7, 0xff, 0xf5, 0xf1, 0xea, 0xff, 0xf6, 0xf3, 0xed, 0xff, 0xf4, 0xf0, 0xed, 0xff, 0xee, 0xeb, 0xe3, 0xff, 0xe8, 0xe7, 0xda, 0xff, 0xeb, 0xea, 0xdd, 0xff, 0xfc, 0xf8, 0xeb, 0xff, 0xcf, 0xce, 0xcd, 0xff, 0x5b, 0x61, 0x6e, 0xff, 0x1c, 0x22, 0x31, 0xff, 0x2f, 0x2b, 0x2e, 0xff, 0x4c, 0x4b, 0x4e, 0xff, 0x3f, 0x46, 0x4c, 0xff, 0x31, 0x34, 0x37, 0xff, 0x31, 0x2f, 0x30, 0xff, 0x3a, 0x3f, 0x47, 0xff, 0x4c, 0x56, 0x68, 0xff, 0x5d, 0x67, 0x7e, 0xff, 0x69, 0x77, 0x91, 0xff, 0x71, 0x7e, 0x9d, 0xff, 0x76, 0x84, 0xa6, 0xff, 0x76, 0x88, 0xad, 0xff, 0x76, 0x85, 0xab, 0xff, 0x70, 0x7e, 0xa2, 0xff, 0x6f, 0x7f, 0xa3, 0xff, 0x71, 0x84, 0xad, 0xff, 0x73, 0x89, 0xb7, 0xff, 0x79, 0x8d, 0xbd, 0xff, 0x7b, 0x8e, 0xb8, 0xff, 0x7c, 0x92, 0xb7, 0xff, 0x7d, 0x8f, 0xb6, 0xff, 0x78, 0x8a, 0xaf, 0xff, 0x7f, 0x91, 0xb2, 0xff, 0x70, 0x7d, 0x97, 0xff, 0x4d, 0x57, 0x6a, 0xff, 0x2c, 0x2a, 0x32, 0xff, 0x1c, 0x11, 0x0e, 0xff, 0x1d, 0x12, 0x09, 0xff, 0x20, 0x16, 0x0f, 0xff, 0x24, 0x1e, 0x1c, 0xff, 0x1c, 0x18, 0x18, 0xff, 0x3b, 0x3a, 0x3e, 0xff, 0x47, 0x49, 0x52, 0xff, 0x2e, 0x2f, 0x38, 0xff, 0x33, 0x32, 0x3c, 0xff, 0x37, 0x37, 0x42, 0xff, 0x44, 0x45, 0x54, 0xff, 0x5d, 0x70, 0x82, 0xff, 0x67, 0x79, 0x93, 0xff, 0x85, 0x93, 0xac, 0xff, 0xb5, 0xc2, 0xd2, 0xff, 0xa8, 0xb3, 0xcb, 0xff, 0x9b, 0xab, 0xc1, 0xff, 0xca, 0xcb, 0xcf, 0xff, 0xf2, 0xee, 0xef, 0xff, 0xf5, 0xf4, 0xf3, 0xff, 0xf9, 0xf9, 0xf4, 0xff, 0xfa, 0xfa, 0xf5, 0xff, 0xfc, 0xf9, 0xef, 0xff, 0xff, 0xfb, 0xef, 0xff, 0xb8, 0xb7, 0xbf, 0xff, 0xdb, 0xda, 0xde, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xfe, 0xfc, 0xfb, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xf4, 0xff, 0xff, 0xff, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x6d, 0x91, 0x07, 0x4f, 0x5d, 0x6d, 0xd1, 0x45, 0x52, 0x5c, 0xff, 0x32, 0x3f, 0x4a, 0xff, 0x3d, 0x45, 0x4e, 0xff, 0x76, 0x75, 0x73, 0xff, 0x89, 0x89, 0x7d, 0xff, 0x85, 0x86, 0x80, 0xff, 0x86, 0x87, 0x84, 0xff, 0x87, 0x88, 0x84, 0xff, 0x8b, 0x8c, 0x89, 0xff, 0x91, 0x92, 0x91, 0xff, 0x8f, 0x91, 0x90, 0xff, 0x88, 0x88, 0x80, 0xff, 0xd9, 0xd6, 0xc9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0xe3, 0xd9, 0xff, 0xd3, 0xd1, 0xbb, 0xff, 0xe6, 0xe3, 0xd2, 0xff, 0xb1, 0xb1, 0xb0, 0xff, 0xc3, 0xc5, 0xc5, 0xff, 0xf5, 0xf6, 0xec, 0xff, 0xf7, 0xf7, 0xec, 0xff, 0xf8, 0xf6, 0xef, 0xff, 0xec, 0xe7, 0xe5, 0xff, 0xe8, 0xe4, 0xde, 0xff, 0xf2, 0xf0, 0xe9, 0xff, 0xf9, 0xf8, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xea, 0xe9, 0xe6, 0xff, 0x73, 0x7e, 0x82, 0xff, 0x2b, 0x2d, 0x33, 0xff, 0x2a, 0x24, 0x24, 0xff, 0x46, 0x45, 0x4b, 0xff, 0x3c, 0x3d, 0x42, 0xff, 0x26, 0x25, 0x25, 0xff, 0x29, 0x29, 0x2d, 0xff, 0x42, 0x3f, 0x49, 0xff, 0x5c, 0x62, 0x7b, 0xff, 0x69, 0x7c, 0xa1, 0xff, 0x75, 0x8a, 0xaf, 0xff, 0x79, 0x8f, 0xb6, 0xff, 0x78, 0x8e, 0xb5, 0xff, 0x79, 0x91, 0xb6, 0xff, 0x77, 0x91, 0xb9, 0xff, 0x78, 0x91, 0xba, 0xff, 0x76, 0x8f, 0xb7, 0xff, 0x76, 0x8f, 0xb7, 0xff, 0x77, 0x8f, 0xba, 0xff, 0x78, 0x8e, 0xbd, 0xff, 0x80, 0x91, 0xbf, 0xff, 0x84, 0x94, 0xbb, 0xff, 0x82, 0x94, 0xb9, 0xff, 0x85, 0x97, 0xbc, 0xff, 0x86, 0x98, 0xbb, 0xff, 0x89, 0x9d, 0xbf, 0xff, 0x89, 0xa1, 0xc5, 0xff, 0x7a, 0x8e, 0xb2, 0xff, 0x5c, 0x68, 0x8a, 0xff, 0x36, 0x3a, 0x4e, 0xff, 0x18, 0x1a, 0x13, 0xff, 0x19, 0x14, 0x07, 0xff, 0x1d, 0x1b, 0x14, 0xff, 0x21, 0x1d, 0x1d, 0xff, 0x3f, 0x39, 0x3c, 0xff, 0x36, 0x34, 0x37, 0xff, 0x37, 0x38, 0x40, 0xff, 0x3c, 0x3d, 0x48, 0xff, 0x3e, 0x3c, 0x42, 0xff, 0x3a, 0x3b, 0x49, 0xff, 0x34, 0x3c, 0x54, 0xff, 0x40, 0x4a, 0x63, 0xff, 0x5c, 0x69, 0x84, 0xff, 0x84, 0x95, 0xb0, 0xff, 0xb9, 0xc1, 0xd0, 0xff, 0xbd, 0xc6, 0xce, 0xff, 0xbb, 0xc0, 0xcb, 0xff, 0xf6, 0xeb, 0xee, 0xff, 0xe4, 0xdb, 0xd9, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xfe, 0xfb, 0xf9, 0xff, 0xfe, 0xf8, 0xf2, 0xff, 0xff, 0xfd, 0xf5, 0xff, 0xcb, 0xca, 0xce, 0xff, 0xdb, 0xd9, 0xdb, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xfd, 0xfb, 0xfa, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xd1, 0xfe, 0xfe, 0xfe, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x6f, 0x83, 0x8e, 0x57, 0x65, 0x71, 0xff, 0x4d, 0x5e, 0x65, 0xff, 0x3a, 0x48, 0x53, 0xff, 0x38, 0x42, 0x4e, 0xff, 0x6f, 0x70, 0x6e, 0xff, 0x89, 0x85, 0x78, 0xff, 0x82, 0x85, 0x7e, 0xff, 0x84, 0x85, 0x82, 0xff, 0x8a, 0x8a, 0x86, 0xff, 0x8e, 0x91, 0x8f, 0xff, 0x91, 0x95, 0x94, 0xff, 0x8e, 0x91, 0x90, 0xff, 0x85, 0x87, 0x7e, 0xff, 0xd0, 0xce, 0xc2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdd, 0xda, 0xce, 0xff, 0xd6, 0xd0, 0xbe, 0xff, 0xf7, 0xef, 0xe4, 0xff, 0xd7, 0xda, 0xd8, 0xff, 0xbf, 0xc7, 0xc4, 0xff, 0xf8, 0xf7, 0xee, 0xff, 0xf9, 0xf6, 0xec, 0xff, 0xed, 0xeb, 0xe4, 0xff, 0xee, 0xe9, 0xea, 0xff, 0xfb, 0xf8, 0xf2, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbc, 0xc8, 0xd0, 0xff, 0x57, 0x63, 0x6e, 0xff, 0x24, 0x26, 0x2c, 0xff, 0x17, 0x11, 0x10, 0xff, 0x3c, 0x3a, 0x40, 0xff, 0x48, 0x48, 0x50, 0xff, 0x28, 0x25, 0x26, 0xff, 0x2c, 0x27, 0x28, 0xff, 0x4b, 0x4a, 0x5b, 0xff, 0x66, 0x73, 0x93, 0xff, 0x77, 0x8b, 0xb0, 0xff, 0x79, 0x8f, 0xb8, 0xff, 0x7d, 0x90, 0xb7, 0xff, 0x7e, 0x91, 0xb6, 0xff, 0x7a, 0x8f, 0xb5, 0xff, 0x7b, 0x93, 0xb9, 0xff, 0x7c, 0x94, 0xb9, 0xff, 0x79, 0x90, 0xb9, 0xff, 0x79, 0x8f, 0xb9, 0xff, 0x77, 0x8d, 0xb8, 0xff, 0x76, 0x8d, 0xba, 0xff, 0x75, 0x8d, 0xbb, 0xff, 0x78, 0x8d, 0xb7, 0xff, 0x7a, 0x8d, 0xb3, 0xff, 0x78, 0x8c, 0xb4, 0xff, 0x7a, 0x90, 0xb5, 0xff, 0x7e, 0x94, 0xb9, 0xff, 0x82, 0x98, 0xbb, 0xff, 0x8a, 0x9e, 0xc0, 0xff, 0x89, 0x9c, 0xc4, 0xff, 0x7c, 0x91, 0xc0, 0xff, 0x61, 0x73, 0x9d, 0xff, 0x39, 0x47, 0x54, 0xff, 0x1e, 0x1e, 0x1c, 0xff, 0x18, 0x11, 0x0e, 0xff, 0x2e, 0x25, 0x29, 0xff, 0x36, 0x32, 0x35, 0xff, 0x1f, 0x1c, 0x1c, 0xff, 0x41, 0x3f, 0x45, 0xff, 0x3c, 0x3d, 0x4a, 0xff, 0x31, 0x31, 0x3b, 0xff, 0x34, 0x3e, 0x51, 0xff, 0x71, 0x76, 0x8f, 0xff, 0x78, 0x79, 0x86, 0xff, 0x69, 0x6a, 0x7d, 0xff, 0x70, 0x7a, 0x8f, 0xff, 0x75, 0x86, 0x98, 0xff, 0xa6, 0xb4, 0xd0, 0xff, 0xc2, 0xca, 0xdb, 0xff, 0xe3, 0xe4, 0xeb, 0xff, 0xf8, 0xf6, 0xf4, 0xff, 0xd8, 0xda, 0xda, 0xff, 0xe0, 0xe1, 0xe3, 0xff, 0xfc, 0xf6, 0xf1, 0xff, 0xf0, 0xea, 0xe5, 0xff, 0xc8, 0xc4, 0xc4, 0xff, 0xed, 0xe8, 0xe7, 0xff, 0xff, 0xfe, 0xfa, 0xff, 0xfd, 0xfb, 0xf9, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0x8f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x67, 0x77, 0x87, 0x40, 0x63, 0x73, 0x86, 0xfe, 0x60, 0x6f, 0x7a, 0xff, 0x61, 0x71, 0x79, 0xff, 0x4a, 0x58, 0x62, 0xff, 0x33, 0x3c, 0x47, 0xff, 0x65, 0x63, 0x65, 0xff, 0x85, 0x81, 0x7a, 0xff, 0x81, 0x83, 0x80, 0xff, 0x8a, 0x8c, 0x88, 0xff, 0x90, 0x91, 0x8c, 0xff, 0x8e, 0x91, 0x8e, 0xff, 0x91, 0x94, 0x92, 0xff, 0x90, 0x92, 0x92, 0xff, 0x89, 0x89, 0x86, 0xff, 0xbc, 0xbb, 0xad, 0xff, 0xed, 0xea, 0xde, 0xff, 0xd9, 0xd8, 0xc7, 0xff, 0xdd, 0xd6, 0xc7, 0xff, 0xff, 0xf4, 0xf0, 0xff, 0xec, 0xee, 0xf0, 0xff, 0xc4, 0xca, 0xcb, 0xff, 0xf4, 0xf1, 0xec, 0xff, 0xfa, 0xf6, 0xf1, 0xff, 0xfa, 0xfa, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xe8, 0xe9, 0xff, 0x9f, 0xa4, 0xad, 0xff, 0x4c, 0x5b, 0x70, 0xff, 0x35, 0x3c, 0x4f, 0xff, 0x3e, 0x3c, 0x3d, 0xff, 0x35, 0x2c, 0x25, 0xff, 0x40, 0x3e, 0x4a, 0xff, 0x37, 0x35, 0x3d, 0xff, 0x2b, 0x21, 0x2a, 0xff, 0x50, 0x55, 0x72, 0xff, 0x74, 0x85, 0xa8, 0xff, 0x7b, 0x90, 0xb4, 0xff, 0x7b, 0x91, 0xb6, 0xff, 0x7d, 0x91, 0xb6, 0xff, 0x7e, 0x91, 0xb6, 0xff, 0x7f, 0x91, 0xb6, 0xff, 0x7e, 0x93, 0xb8, 0xff, 0x7d, 0x94, 0xbb, 0xff, 0x7c, 0x93, 0xb9, 0xff, 0x79, 0x8f, 0xb8, 0xff, 0x77, 0x8d, 0xb7, 0xff, 0x76, 0x8c, 0xb7, 0xff, 0x75, 0x8c, 0xb9, 0xff, 0x72, 0x89, 0xb6, 0xff, 0x71, 0x88, 0xb4, 0xff, 0x71, 0x89, 0xb4, 0xff, 0x71, 0x89, 0xb4, 0xff, 0x73, 0x8b, 0xb5, 0xff, 0x77, 0x90, 0xb9, 0xff, 0x79, 0x91, 0xba, 0xff, 0x80, 0x95, 0xbc, 0xff, 0x8b, 0xa3, 0xc5, 0xff, 0x8c, 0xa5, 0xc5, 0xff, 0x81, 0x94, 0xbf, 0xff, 0x6c, 0x7d, 0xa8, 0xff, 0x3c, 0x43, 0x5c, 0xff, 0x22, 0x16, 0x1d, 0xff, 0x2c, 0x26, 0x28, 0xff, 0x24, 0x22, 0x24, 0xff, 0x17, 0x14, 0x15, 0xff, 0x39, 0x36, 0x3b, 0xff, 0x40, 0x3d, 0x47, 0xff, 0x2e, 0x29, 0x31, 0xff, 0x2c, 0x31, 0x3e, 0xff, 0x59, 0x69, 0x81, 0xff, 0x9a, 0x9b, 0xae, 0xff, 0x7c, 0x83, 0x94, 0xff, 0xa1, 0xad, 0xc0, 0xff, 0x71, 0x77, 0x92, 0xff, 0x52, 0x67, 0x91, 0xff, 0x84, 0x96, 0xb8, 0xff, 0x86, 0x9e, 0xba, 0xff, 0xaa, 0xba, 0xcb, 0xff, 0xb6, 0xc0, 0xd2, 0xff, 0xb5, 0xc2, 0xd2, 0xff, 0xe7, 0xe7, 0xe7, 0xff, 0xd2, 0xcd, 0xc8, 0xff, 0xcb, 0xc6, 0xc5, 0xff, 0xf0, 0xec, 0xec, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xfe, 0xfb, 0xfb, 0xfb, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0x8d, 0x8d, 0x09, 0x67, 0x76, 0x8a, 0xdb, 0x62, 0x71, 0x88, 0xff, 0x5f, 0x6d, 0x7c, 0xff, 0x64, 0x73, 0x7a, 0xff, 0x58, 0x66, 0x6e, 0xff, 0x2f, 0x3b, 0x44, 0xff, 0x53, 0x53, 0x57, 0xff, 0x87, 0x81, 0x7c, 0xff, 0x8c, 0x8b, 0x87, 0xff, 0x8f, 0x8f, 0x8b, 0xff, 0x91, 0x91, 0x8d, 0xff, 0x90, 0x91, 0x8e, 0xff, 0x90, 0x93, 0x91, 0xff, 0x92, 0x93, 0x92, 0xff, 0x89, 0x88, 0x8b, 0xff, 0xb3, 0xb3, 0xa9, 0xff, 0xe5, 0xe4, 0xd1, 0xff, 0xd8, 0xd5, 0xc4, 0xff, 0xde, 0xd8, 0xcc, 0xff, 0xff, 0xfe, 0xfc, 0xff, 0xf4, 0xf6, 0xf8, 0xff, 0xd2, 0xd7, 0xd7, 0xff, 0xf4, 0xf3, 0xeb, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfe, 0xff, 0xff, 0xb3, 0xb4, 0xbc, 0xff, 0x64, 0x65, 0x74, 0xff, 0x3d, 0x3f, 0x51, 0xff, 0x2c, 0x2e, 0x3e, 0xff, 0x33, 0x2f, 0x3a, 0xff, 0x49, 0x45, 0x48, 0xff, 0x37, 0x35, 0x35, 0xff, 0x2e, 0x2a, 0x33, 0xff, 0x2b, 0x2a, 0x35, 0xff, 0x56, 0x57, 0x69, 0xff, 0x76, 0x89, 0xae, 0xff, 0x7c, 0x95, 0xbc, 0xff, 0x7f, 0x92, 0xb8, 0xff, 0x7f, 0x90, 0xb6, 0xff, 0x80, 0x91, 0xb6, 0xff, 0x7e, 0x90, 0xb6, 0xff, 0x7e, 0x8f, 0xb5, 0xff, 0x7a, 0x8f, 0xb5, 0xff, 0x77, 0x8f, 0xb5, 0xff, 0x77, 0x8e, 0xb3, 0xff, 0x75, 0x8b, 0xb4, 0xff, 0x74, 0x8a, 0xb4, 0xff, 0x74, 0x8a, 0xb5, 0xff, 0x73, 0x8a, 0xb8, 0xff, 0x70, 0x87, 0xb5, 0xff, 0x70, 0x87, 0xb5, 0xff, 0x70, 0x88, 0xb5, 0xff, 0x6f, 0x86, 0xb4, 0xff, 0x70, 0x89, 0xb1, 0xff, 0x72, 0x8d, 0xb3, 0xff, 0x75, 0x8e, 0xb6, 0xff, 0x7a, 0x92, 0xbb, 0xff, 0x82, 0x9c, 0xc0, 0xff, 0x8e, 0xa8, 0xc4, 0xff, 0x93, 0xa8, 0xc8, 0xff, 0x82, 0x96, 0xc2, 0xff, 0x68, 0x77, 0x9f, 0xff, 0x34, 0x32, 0x44, 0xff, 0x21, 0x1f, 0x23, 0xff, 0x2c, 0x2a, 0x2b, 0xff, 0x23, 0x1f, 0x20, 0xff, 0x27, 0x26, 0x29, 0xff, 0x34, 0x35, 0x3a, 0xff, 0x2f, 0x2f, 0x31, 0xff, 0x2f, 0x2c, 0x37, 0xff, 0x31, 0x3a, 0x4f, 0xff, 0x6d, 0x77, 0x8f, 0xff, 0x79, 0x85, 0xa0, 0xff, 0xaa, 0xb6, 0xc8, 0xff, 0xd3, 0xd1, 0xd7, 0xff, 0x82, 0x89, 0x9f, 0xff, 0x84, 0x8c, 0xab, 0xff, 0x8a, 0x94, 0xab, 0xff, 0x8c, 0x98, 0xaf, 0xff, 0x83, 0x90, 0xae, 0xff, 0x7a, 0x8c, 0xaf, 0xff, 0xae, 0xbb, 0xcc, 0xff, 0xdd, 0xd8, 0xd8, 0xff, 0xeb, 0xe7, 0xe4, 0xff, 0xfb, 0xf7, 0xf3, 0xff, 0xf9, 0xf4, 0xef, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xff, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xdb, 0xff, 0xff, 0xff, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x76, 0x83, 0x97, 0x88, 0x6a, 0x78, 0x8b, 0xff, 0x5f, 0x6d, 0x86, 0xff, 0x5e, 0x6b, 0x7c, 0xff, 0x5c, 0x6a, 0x71, 0xff, 0x55, 0x62, 0x6c, 0xff, 0x2e, 0x3b, 0x46, 0xff, 0x3f, 0x40, 0x45, 0xff, 0x76, 0x72, 0x6a, 0xff, 0x8e, 0x8c, 0x85, 0xff, 0x8d, 0x8c, 0x89, 0xff, 0x8b, 0x89, 0x85, 0xff, 0x8f, 0x8f, 0x8c, 0xff, 0x91, 0x92, 0x91, 0xff, 0x8f, 0x90, 0x8e, 0xff, 0x85, 0x85, 0x86, 0xff, 0xb2, 0xb3, 0xac, 0xff, 0xe5, 0xe5, 0xd5, 0xff, 0xdb, 0xd9, 0xcd, 0xff, 0xe4, 0xe1, 0xd9, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xef, 0xeb, 0xec, 0xff, 0xce, 0xd2, 0xd7, 0xff, 0xe8, 0xee, 0xee, 0xff, 0xe0, 0xe0, 0xdc, 0xff, 0xb7, 0xb6, 0xbc, 0xff, 0x84, 0x86, 0x8e, 0xff, 0x38, 0x38, 0x45, 0xff, 0x1b, 0x19, 0x21, 0xff, 0x20, 0x1a, 0x1d, 0xff, 0x1e, 0x1b, 0x1e, 0xff, 0x2f, 0x2b, 0x2d, 0xff, 0x3a, 0x31, 0x33, 0xff, 0x20, 0x1e, 0x1c, 0xff, 0x25, 0x27, 0x28, 0xff, 0x46, 0x50, 0x5d, 0xff, 0x72, 0x87, 0xa6, 0xff, 0x72, 0x90, 0xbc, 0xff, 0x76, 0x90, 0xb8, 0xff, 0x80, 0x93, 0xb8, 0xff, 0x7e, 0x91, 0xb7, 0xff, 0x7a, 0x8e, 0xb4, 0xff, 0x78, 0x8c, 0xb3, 0xff, 0x77, 0x8b, 0xb1, 0xff, 0x74, 0x89, 0xaf, 0xff, 0x72, 0x8a, 0xb0, 0xff, 0x73, 0x8a, 0xb0, 0xff, 0x73, 0x89, 0xb3, 0xff, 0x73, 0x89, 0xb3, 0xff, 0x72, 0x89, 0xb3, 0xff, 0x70, 0x87, 0xb4, 0xff, 0x70, 0x87, 0xb4, 0xff, 0x6e, 0x85, 0xb2, 0xff, 0x6a, 0x81, 0xae, 0xff, 0x69, 0x80, 0xae, 0xff, 0x6c, 0x85, 0xad, 0xff, 0x70, 0x89, 0xaf, 0xff, 0x72, 0x8a, 0xb3, 0xff, 0x74, 0x8a, 0xba, 0xff, 0x7a, 0x93, 0xc0, 0xff, 0x89, 0x9e, 0xc7, 0xff, 0x97, 0xa9, 0xcc, 0xff, 0x90, 0xa7, 0xc4, 0xff, 0x85, 0x9a, 0xc6, 0xff, 0x60, 0x6b, 0x8c, 0xff, 0x24, 0x1f, 0x23, 0xff, 0x25, 0x21, 0x20, 0xff, 0x31, 0x2d, 0x2f, 0xff, 0x24, 0x22, 0x25, 0xff, 0x2a, 0x2c, 0x30, 0xff, 0x36, 0x37, 0x3a, 0xff, 0x38, 0x37, 0x3f, 0xff, 0x37, 0x36, 0x46, 0xff, 0x4a, 0x51, 0x66, 0xff, 0x6b, 0x79, 0x92, 0xff, 0x9b, 0xa8, 0xba, 0xff, 0xe8, 0xe5, 0xe5, 0xff, 0xc9, 0xc1, 0xc5, 0xff, 0xbb, 0xbb, 0xc7, 0xff, 0xda, 0xd2, 0xdc, 0xff, 0xa2, 0xac, 0xb5, 0xff, 0xc8, 0xc5, 0xd4, 0xff, 0x9f, 0xa3, 0xb7, 0xff, 0x6f, 0x86, 0x99, 0xff, 0xb5, 0xbd, 0xca, 0xff, 0xf0, 0xee, 0xed, 0xff, 0xff, 0xf9, 0xf1, 0xff, 0xff, 0xf9, 0xf3, 0xff, 0xfe, 0xfb, 0xf6, 0xff, 0xfd, 0xfc, 0xfb, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x8c, 0xa1, 0x26, 0x77, 0x84, 0x96, 0xfa, 0x6c, 0x7b, 0x8d, 0xff, 0x5e, 0x6c, 0x84, 0xff, 0x5d, 0x6b, 0x7a, 0xff, 0x59, 0x66, 0x71, 0xff, 0x47, 0x52, 0x61, 0xff, 0x2c, 0x37, 0x47, 0xff, 0x3e, 0x3e, 0x44, 0xff, 0x6e, 0x69, 0x61, 0xff, 0x82, 0x80, 0x7a, 0xff, 0x83, 0x82, 0x7e, 0xff, 0x87, 0x86, 0x82, 0xff, 0x90, 0x90, 0x8d, 0xff, 0x94, 0x95, 0x93, 0xff, 0x8a, 0x8b, 0x89, 0xff, 0x80, 0x81, 0x7c, 0xff, 0xb0, 0xaf, 0xa8, 0xff, 0xe0, 0xde, 0xd5, 0xff, 0xd7, 0xd8, 0xcf, 0xff, 0xec, 0xeb, 0xe7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xdf, 0xe1, 0xff, 0x91, 0x9e, 0xab, 0xff, 0xb5, 0xb9, 0xbe, 0xff, 0xcd, 0xbe, 0xb8, 0xff, 0x8d, 0x89, 0x85, 0xff, 0x47, 0x48, 0x4a, 0xff, 0x33, 0x31, 0x38, 0xff, 0x29, 0x25, 0x29, 0xff, 0x27, 0x23, 0x21, 0xff, 0x21, 0x1e, 0x1d, 0xff, 0x2a, 0x25, 0x20, 0xff, 0x27, 0x1a, 0x18, 0xff, 0x26, 0x1f, 0x21, 0xff, 0x3d, 0x43, 0x4e, 0xff, 0x61, 0x74, 0x93, 0xff, 0x74, 0x8c, 0xb7, 0xff, 0x78, 0x8f, 0xbc, 0xff, 0x82, 0x94, 0xb9, 0xff, 0x84, 0x96, 0xb9, 0xff, 0x80, 0x93, 0xb9, 0xff, 0x7b, 0x8f, 0xb5, 0xff, 0x75, 0x8d, 0xb3, 0xff, 0x71, 0x88, 0xae, 0xff, 0x70, 0x88, 0xad, 0xff, 0x72, 0x89, 0xaf, 0xff, 0x72, 0x89, 0xaf, 0xff, 0x72, 0x88, 0xb1, 0xff, 0x73, 0x88, 0xb3, 0xff, 0x71, 0x88, 0xb2, 0xff, 0x6e, 0x87, 0xaf, 0xff, 0x6d, 0x86, 0xad, 0xff, 0x6b, 0x83, 0xad, 0xff, 0x67, 0x7e, 0xac, 0xff, 0x66, 0x7e, 0xab, 0xff, 0x68, 0x80, 0xac, 0xff, 0x6c, 0x83, 0xb0, 0xff, 0x6f, 0x86, 0xb3, 0xff, 0x72, 0x8a, 0xb6, 0xff, 0x7c, 0x96, 0xbe, 0xff, 0x88, 0x9f, 0xc3, 0xff, 0x93, 0xa7, 0xc8, 0xff, 0x9b, 0xae, 0xcf, 0xff, 0x93, 0xa6, 0xcc, 0xff, 0x75, 0x8e, 0xb9, 0xff, 0x3e, 0x47, 0x56, 0xff, 0x1a, 0x15, 0x13, 0xff, 0x24, 0x21, 0x21, 0xff, 0x35, 0x31, 0x34, 0xff, 0x32, 0x2e, 0x33, 0xff, 0x27, 0x22, 0x27, 0xff, 0x33, 0x30, 0x32, 0xff, 0x34, 0x35, 0x3b, 0xff, 0x47, 0x4d, 0x5a, 0xff, 0x52, 0x63, 0x78, 0xff, 0x6c, 0x7f, 0xa0, 0xff, 0xad, 0xb5, 0xcc, 0xff, 0xc2, 0xc2, 0xc8, 0xff, 0x8d, 0x97, 0xab, 0xff, 0xcf, 0xd9, 0xe9, 0xff, 0xcd, 0xcc, 0xd7, 0xff, 0xac, 0xb5, 0xc6, 0xff, 0xd2, 0xdc, 0xe5, 0xff, 0x95, 0xa1, 0xaf, 0xff, 0x84, 0x8f, 0xa9, 0xff, 0xd1, 0xd5, 0xd9, 0xff, 0xf3, 0xea, 0xe0, 0xff, 0xff, 0xf6, 0xed, 0xff, 0xff, 0xfd, 0xf8, 0xff, 0xfe, 0xfd, 0xfc, 0xff, 0xfd, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xfa, 0xff, 0xff, 0xff, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7d, 0x8d, 0x9c, 0xb3, 0x74, 0x83, 0x93, 0xff, 0x6b, 0x7b, 0x8b, 0xff, 0x5f, 0x6e, 0x80, 0xff, 0x56, 0x66, 0x77, 0xff, 0x56, 0x64, 0x73, 0xff, 0x44, 0x50, 0x5e, 0xff, 0x2a, 0x38, 0x47, 0xff, 0x4b, 0x4f, 0x54, 0xff, 0x80, 0x7a, 0x74, 0xff, 0x86, 0x83, 0x7c, 0xff, 0x80, 0x81, 0x7a, 0xff, 0x8c, 0x8a, 0x88, 0xff, 0x91, 0x90, 0x8f, 0xff, 0x8b, 0x8c, 0x8a, 0xff, 0x81, 0x81, 0x7f, 0xff, 0x7a, 0x7d, 0x7c, 0xff, 0xa5, 0xaa, 0xa6, 0xff, 0xda, 0xd9, 0xd0, 0xff, 0xdb, 0xda, 0xcf, 0xff, 0xf4, 0xf1, 0xee, 0xff, 0xe3, 0xdf, 0xdf, 0xff, 0xc5, 0xc1, 0xc0, 0xff, 0xac, 0xad, 0xb6, 0xff, 0xa1, 0x9c, 0xa3, 0xff, 0x8c, 0x88, 0x88, 0xff, 0x4c, 0x55, 0x5a, 0xff, 0x42, 0x47, 0x4c, 0xff, 0x41, 0x41, 0x48, 0xff, 0x2a, 0x28, 0x2f, 0xff, 0x28, 0x27, 0x2a, 0xff, 0x2b, 0x27, 0x28, 0xff, 0x1f, 0x16, 0x13, 0xff, 0x22, 0x19, 0x18, 0xff, 0x3c, 0x3b, 0x48, 0xff, 0x57, 0x60, 0x7b, 0xff, 0x6d, 0x83, 0xab, 0xff, 0x7d, 0x90, 0xbd, 0xff, 0x85, 0x96, 0xbe, 0xff, 0x89, 0x9a, 0xbd, 0xff, 0x86, 0x98, 0xbc, 0xff, 0x7f, 0x92, 0xb7, 0xff, 0x77, 0x8b, 0xb1, 0xff, 0x71, 0x87, 0xb1, 0xff, 0x70, 0x87, 0xb1, 0xff, 0x72, 0x88, 0xb0, 0xff, 0x73, 0x8b, 0xb0, 0xff, 0x73, 0x8a, 0xb0, 0xff, 0x73, 0x89, 0xb3, 0xff, 0x72, 0x88, 0xb3, 0xff, 0x70, 0x87, 0xb1, 0xff, 0x6e, 0x86, 0xaf, 0xff, 0x6d, 0x85, 0xae, 0xff, 0x6a, 0x82, 0xac, 0xff, 0x65, 0x7d, 0xa8, 0xff, 0x66, 0x7e, 0xa9, 0xff, 0x69, 0x7f, 0xae, 0xff, 0x6b, 0x81, 0xb1, 0xff, 0x6d, 0x84, 0xb2, 0xff, 0x73, 0x8a, 0xb2, 0xff, 0x7e, 0x96, 0xba, 0xff, 0x87, 0x9f, 0xc0, 0xff, 0x8f, 0xa5, 0xc5, 0xff, 0x9b, 0xaa, 0xd0, 0xff, 0x99, 0xa8, 0xcd, 0xff, 0x85, 0x9d, 0xc2, 0xff, 0x5b, 0x70, 0x8c, 0xff, 0x27, 0x27, 0x2c, 0xff, 0x1b, 0x18, 0x15, 0xff, 0x28, 0x25, 0x26, 0xff, 0x31, 0x2b, 0x2e, 0xff, 0x27, 0x21, 0x23, 0xff, 0x28, 0x26, 0x28, 0xff, 0x24, 0x29, 0x2f, 0xff, 0x42, 0x4a, 0x56, 0xff, 0x44, 0x4d, 0x61, 0xff, 0x56, 0x65, 0x83, 0xff, 0x8c, 0x99, 0xb7, 0xff, 0xc4, 0xc5, 0xcd, 0xff, 0x9d, 0x9d, 0xb1, 0xff, 0x84, 0x92, 0xb1, 0xff, 0xae, 0xb2, 0xcd, 0xff, 0x75, 0x8d, 0xb2, 0xff, 0x91, 0xa8, 0xc3, 0xff, 0xab, 0xb6, 0xc9, 0xff, 0x7e, 0x89, 0xa7, 0xff, 0xa1, 0xac, 0xbd, 0xff, 0xeb, 0xed, 0xe7, 0xff, 0xf2, 0xed, 0xe5, 0xff, 0xfd, 0xfb, 0xf5, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8e, 0xa1, 0xb0, 0x44, 0x7f, 0x8f, 0x9f, 0xff, 0x72, 0x80, 0x90, 0xff, 0x69, 0x78, 0x88, 0xff, 0x5f, 0x70, 0x80, 0xff, 0x53, 0x64, 0x75, 0xff, 0x5a, 0x68, 0x77, 0xff, 0x50, 0x5d, 0x6b, 0xff, 0x2b, 0x39, 0x49, 0xff, 0x4d, 0x51, 0x57, 0xff, 0x8c, 0x84, 0x7f, 0xff, 0x89, 0x86, 0x7e, 0xff, 0x81, 0x83, 0x79, 0xff, 0x89, 0x87, 0x86, 0xff, 0x8b, 0x8b, 0x8b, 0xff, 0x81, 0x83, 0x80, 0xff, 0x81, 0x81, 0x7f, 0xff, 0x86, 0x8c, 0x8d, 0xff, 0x9f, 0xa9, 0xa9, 0xff, 0xca, 0xc9, 0xc6, 0xff, 0xe1, 0xdc, 0xd7, 0xff, 0xed, 0xec, 0xe5, 0xff, 0xe0, 0xd7, 0xd3, 0xff, 0xe6, 0xdc, 0xd9, 0xff, 0x94, 0x9e, 0xa7, 0xff, 0x59, 0x65, 0x6e, 0xff, 0x3b, 0x3e, 0x45, 0xff, 0x31, 0x37, 0x45, 0xff, 0x4d, 0x52, 0x5c, 0xff, 0x44, 0x44, 0x50, 0xff, 0x3a, 0x39, 0x40, 0xff, 0x25, 0x22, 0x23, 0xff, 0x24, 0x1e, 0x1f, 0xff, 0x1a, 0x10, 0x10, 0xff, 0x2d, 0x2a, 0x2d, 0xff, 0x43, 0x4d, 0x67, 0xff, 0x61, 0x74, 0x9a, 0xff, 0x72, 0x8c, 0xb4, 0xff, 0x81, 0x92, 0xbf, 0xff, 0x87, 0x99, 0xc2, 0xff, 0x87, 0x9b, 0xbf, 0xff, 0x84, 0x97, 0xbb, 0xff, 0x7d, 0x8f, 0xb3, 0xff, 0x75, 0x88, 0xae, 0xff, 0x70, 0x86, 0xb1, 0xff, 0x71, 0x86, 0xb2, 0xff, 0x73, 0x89, 0xb2, 0xff, 0x74, 0x8c, 0xb1, 0xff, 0x74, 0x8b, 0xb1, 0xff, 0x74, 0x8a, 0xb4, 0xff, 0x74, 0x89, 0xb4, 0xff, 0x72, 0x88, 0xb2, 0xff, 0x6e, 0x86, 0xb0, 0xff, 0x6d, 0x85, 0xaf, 0xff, 0x6d, 0x85, 0xaf, 0xff, 0x6a, 0x81, 0xac, 0xff, 0x6b, 0x82, 0xad, 0xff, 0x6e, 0x86, 0xb2, 0xff, 0x6e, 0x86, 0xb2, 0xff, 0x70, 0x87, 0xb3, 0xff, 0x77, 0x8c, 0xb7, 0xff, 0x80, 0x98, 0xbe, 0xff, 0x89, 0xa2, 0xc5, 0xff, 0x90, 0xa5, 0xc9, 0xff, 0x97, 0xa8, 0xcc, 0xff, 0x96, 0xaa, 0xcb, 0xff, 0x8e, 0xa2, 0xc3, 0xff, 0x77, 0x8b, 0xae, 0xff, 0x3b, 0x41, 0x54, 0xff, 0x12, 0x10, 0x0f, 0xff, 0x1d, 0x19, 0x19, 0xff, 0x2f, 0x29, 0x2b, 0xff, 0x1e, 0x18, 0x19, 0xff, 0x22, 0x1f, 0x22, 0xff, 0x36, 0x38, 0x40, 0xff, 0x37, 0x3d, 0x4a, 0xff, 0x41, 0x46, 0x56, 0xff, 0x52, 0x5a, 0x72, 0xff, 0x73, 0x83, 0x9e, 0xff, 0xa5, 0xb1, 0xbe, 0xff, 0xd1, 0xc6, 0xcb, 0xff, 0xb2, 0xb1, 0xbe, 0xff, 0xa7, 0xb4, 0xcb, 0xff, 0x97, 0xa6, 0xc0, 0xff, 0x99, 0x9d, 0xb7, 0xff, 0xba, 0xbe, 0xcb, 0xff, 0x9e, 0xa8, 0xbe, 0xff, 0x78, 0x8c, 0xac, 0xff, 0xaf, 0xbc, 0xd2, 0xff, 0xdb, 0xdd, 0xe2, 0xff, 0xf1, 0xf3, 0xef, 0xff, 0xff, 0xfd, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xfb, 0xfb, 0xfb, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x92, 0xa3, 0xb2, 0xc8, 0x83, 0x92, 0xa2, 0xff, 0x73, 0x82, 0x92, 0xff, 0x69, 0x79, 0x89, 0xff, 0x60, 0x6f, 0x81, 0xff, 0x55, 0x65, 0x77, 0xff, 0x56, 0x64, 0x74, 0xff, 0x51, 0x5d, 0x6b, 0xff, 0x2f, 0x3d, 0x4b, 0xff, 0x48, 0x4d, 0x51, 0xff, 0x86, 0x80, 0x79, 0xff, 0x87, 0x84, 0x7d, 0xff, 0x82, 0x82, 0x7b, 0xff, 0x83, 0x81, 0x7e, 0xff, 0x86, 0x86, 0x84, 0xff, 0x88, 0x89, 0x88, 0xff, 0x84, 0x85, 0x85, 0xff, 0x8a, 0x91, 0x97, 0xff, 0x9e, 0xaa, 0xac, 0xff, 0xbf, 0xc0, 0xbf, 0xff, 0xf1, 0xed, 0xea, 0xff, 0xe6, 0xe5, 0xdb, 0xff, 0xe2, 0xe0, 0xd4, 0xff, 0xb9, 0xb7, 0xb3, 0xff, 0x47, 0x4f, 0x5c, 0xff, 0x46, 0x4f, 0x5b, 0xff, 0x45, 0x49, 0x52, 0xff, 0x34, 0x3a, 0x48, 0xff, 0x32, 0x35, 0x40, 0xff, 0x2c, 0x2c, 0x37, 0xff, 0x2d, 0x2a, 0x33, 0xff, 0x27, 0x24, 0x28, 0xff, 0x12, 0x0e, 0x0c, 0xff, 0x1f, 0x17, 0x17, 0xff, 0x32, 0x32, 0x40, 0xff, 0x4d, 0x5e, 0x82, 0xff, 0x69, 0x83, 0xaf, 0xff, 0x72, 0x8c, 0xb8, 0xff, 0x82, 0x94, 0xc0, 0xff, 0x87, 0x99, 0xc0, 0xff, 0x85, 0x99, 0xbd, 0xff, 0x83, 0x97, 0xbc, 0xff, 0x7e, 0x92, 0xb6, 0xff, 0x78, 0x8c, 0xb1, 0xff, 0x75, 0x8c, 0xb6, 0xff, 0x76, 0x8c, 0xb7, 0xff, 0x76, 0x8d, 0xb4, 0xff, 0x76, 0x8d, 0xb1, 0xff, 0x76, 0x8d, 0xb2, 0xff, 0x75, 0x8b, 0xb4, 0xff, 0x74, 0x8a, 0xb4, 0xff, 0x72, 0x89, 0xb3, 0xff, 0x70, 0x88, 0xb2, 0xff, 0x6f, 0x87, 0xb1, 0xff, 0x70, 0x88, 0xb1, 0xff, 0x6e, 0x87, 0xaf, 0xff, 0x6e, 0x87, 0xaf, 0xff, 0x73, 0x8d, 0xb4, 0xff, 0x76, 0x91, 0xb7, 0xff, 0x78, 0x91, 0xb8, 0xff, 0x7d, 0x94, 0xbc, 0xff, 0x88, 0x9f, 0xc3, 0xff, 0x92, 0xa9, 0xcb, 0xff, 0x98, 0xad, 0xcf, 0xff, 0x9e, 0xaf, 0xd2, 0xff, 0x9c, 0xae, 0xcc, 0xff, 0x95, 0xa8, 0xc9, 0xff, 0x89, 0x9c, 0xc4, 0xff, 0x55, 0x5d, 0x7b, 0xff, 0x1d, 0x17, 0x1c, 0xff, 0x24, 0x1f, 0x1e, 0xff, 0x29, 0x23, 0x25, 0xff, 0x21, 0x1c, 0x1d, 0xff, 0x20, 0x1a, 0x1d, 0xff, 0x3e, 0x3b, 0x46, 0xff, 0x30, 0x30, 0x3f, 0xff, 0x36, 0x38, 0x47, 0xff, 0x4f, 0x59, 0x6b, 0xff, 0x5b, 0x69, 0x87, 0xff, 0x8f, 0xa2, 0xc0, 0xff, 0xbf, 0xcb, 0xd2, 0xff, 0xe2, 0xde, 0xd9, 0xff, 0xca, 0xc7, 0xd1, 0xff, 0xc9, 0xcf, 0xde, 0xff, 0xd4, 0xd7, 0xd4, 0xff, 0xb1, 0xb4, 0xbc, 0xff, 0x98, 0x99, 0xaf, 0xff, 0x73, 0x85, 0xa5, 0xff, 0x9f, 0xaf, 0xcb, 0xff, 0xcc, 0xce, 0xd6, 0xff, 0xdd, 0xe0, 0xdc, 0xff, 0xf6, 0xf3, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfe, 0xfe, 0xfc, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfb, 0xfc, 0xfe, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0xa5, 0xb6, 0x4d, 0x93, 0xa3, 0xb4, 0xff, 0x84, 0x93, 0xa3, 0xff, 0x75, 0x84, 0x93, 0xff, 0x6c, 0x7b, 0x8c, 0xff, 0x60, 0x70, 0x83, 0xff, 0x58, 0x67, 0x7b, 0xff, 0x51, 0x5e, 0x70, 0xff, 0x46, 0x53, 0x62, 0xff, 0x2f, 0x3f, 0x4a, 0xff, 0x42, 0x49, 0x4c, 0xff, 0x80, 0x7c, 0x75, 0xff, 0x8a, 0x87, 0x82, 0xff, 0x85, 0x85, 0x81, 0xff, 0x87, 0x86, 0x81, 0xff, 0x8f, 0x8d, 0x8c, 0xff, 0x8e, 0x8e, 0x90, 0xff, 0x82, 0x86, 0x8a, 0xff, 0x8a, 0x92, 0x9d, 0xff, 0x94, 0x9e, 0xa8, 0xff, 0xd0, 0xd4, 0xd1, 0xff, 0xdb, 0xd9, 0xd4, 0xff, 0xbf, 0xbb, 0xb2, 0xff, 0xe2, 0xde, 0xd4, 0xff, 0xa8, 0xa9, 0xa9, 0xff, 0x3c, 0x40, 0x4a, 0xff, 0x3e, 0x43, 0x4e, 0xff, 0x3a, 0x3c, 0x47, 0xff, 0x29, 0x29, 0x34, 0xff, 0x20, 0x22, 0x2c, 0xff, 0x1b, 0x1b, 0x1e, 0xff, 0x1e, 0x19, 0x22, 0xff, 0x24, 0x21, 0x2a, 0xff, 0x10, 0x11, 0x0d, 0xff, 0x17, 0x15, 0x17, 0xff, 0x38, 0x3d, 0x59, 0xff, 0x65, 0x78, 0xa0, 0xff, 0x72, 0x8a, 0xb5, 0xff, 0x79, 0x8f, 0xba, 0xff, 0x83, 0x97, 0xbf, 0xff, 0x89, 0x9c, 0xc1, 0xff, 0x87, 0x9a, 0xc0, 0xff, 0x84, 0x99, 0xbd, 0xff, 0x80, 0x96, 0xb9, 0xff, 0x7c, 0x92, 0xb7, 0xff, 0x7b, 0x91, 0xbb, 0xff, 0x7b, 0x91, 0xbc, 0xff, 0x7a, 0x91, 0xb8, 0xff, 0x79, 0x91, 0xb4, 0xff, 0x78, 0x90, 0xb4, 0xff, 0x76, 0x8c, 0xb5, 0xff, 0x74, 0x89, 0xb4, 0xff, 0x72, 0x89, 0xb3, 0xff, 0x72, 0x8a, 0xb4, 0xff, 0x74, 0x8b, 0xb6, 0xff, 0x70, 0x88, 0xb0, 0xff, 0x6e, 0x88, 0xad, 0xff, 0x72, 0x8c, 0xb2, 0xff, 0x77, 0x91, 0xb6, 0xff, 0x7c, 0x97, 0xbb, 0xff, 0x7f, 0x98, 0xbd, 0xff, 0x84, 0x99, 0xbe, 0xff, 0x8f, 0xa1, 0xc4, 0xff, 0x9d, 0xae, 0xcd, 0xff, 0xa3, 0xb3, 0xd2, 0xff, 0xa4, 0xb6, 0xd4, 0xff, 0xa2, 0xb2, 0xd1, 0xff, 0x9f, 0xae, 0xcc, 0xff, 0x93, 0xa5, 0xc7, 0xff, 0x70, 0x7a, 0x9f, 0xff, 0x2e, 0x29, 0x37, 0xff, 0x17, 0x12, 0x12, 0xff, 0x21, 0x1c, 0x1d, 0xff, 0x27, 0x22, 0x24, 0xff, 0x2b, 0x23, 0x28, 0xff, 0x39, 0x33, 0x41, 0xff, 0x3c, 0x3b, 0x4c, 0xff, 0x2e, 0x2f, 0x3c, 0xff, 0x4c, 0x57, 0x68, 0xff, 0x52, 0x65, 0x7d, 0xff, 0x74, 0x87, 0xa5, 0xff, 0x7c, 0x90, 0xb6, 0xff, 0xc1, 0xcc, 0xda, 0xff, 0xc3, 0xc7, 0xd0, 0xff, 0x9d, 0xaf, 0xca, 0xff, 0xf2, 0xf7, 0xf1, 0xff, 0xd8, 0xd7, 0xe0, 0xff, 0xaf, 0xab, 0xb5, 0xff, 0x68, 0x71, 0x9a, 0xff, 0xac, 0xbe, 0xd5, 0xff, 0xe5, 0xe7, 0xde, 0xff, 0xf6, 0xf2, 0xea, 0xff, 0xfc, 0xf9, 0xf3, 0xff, 0xfa, 0xfa, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0xec, 0xe7, 0xff, 0xc3, 0xc3, 0xc0, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x94, 0xa4, 0xb5, 0xcd, 0x92, 0xa2, 0xb3, 0xff, 0x85, 0x94, 0xa4, 0xff, 0x76, 0x85, 0x95, 0xff, 0x6d, 0x7c, 0x8e, 0xff, 0x62, 0x71, 0x85, 0xff, 0x5b, 0x6a, 0x7c, 0xff, 0x4f, 0x5d, 0x71, 0xff, 0x41, 0x52, 0x64, 0xff, 0x2e, 0x3f, 0x4f, 0xff, 0x3e, 0x43, 0x47, 0xff, 0x7a, 0x76, 0x6e, 0xff, 0x8c, 0x8a, 0x83, 0xff, 0x8d, 0x8d, 0x88, 0xff, 0x95, 0x93, 0x8f, 0xff, 0x93, 0x91, 0x8f, 0xff, 0x81, 0x81, 0x82, 0xff, 0x8a, 0x8d, 0x90, 0xff, 0x95, 0x9f, 0xad, 0xff, 0x91, 0x9f, 0xac, 0xff, 0xc3, 0xc9, 0xc8, 0xff, 0xbc, 0xba, 0xb4, 0xff, 0xdc, 0xd7, 0xcd, 0xff, 0xf7, 0xf1, 0xe8, 0xff, 0xa1, 0xa3, 0xa3, 0xff, 0x51, 0x56, 0x60, 0xff, 0x3c, 0x40, 0x48, 0xff, 0x23, 0x23, 0x2a, 0xff, 0x1b, 0x19, 0x21, 0xff, 0x1b, 0x1a, 0x1f, 0xff, 0x1f, 0x1a, 0x1b, 0xff, 0x1d, 0x1a, 0x1d, 0xff, 0x1e, 0x1f, 0x25, 0xff, 0x19, 0x17, 0x19, 0xff, 0x1c, 0x1e, 0x27, 0xff, 0x47, 0x51, 0x75, 0xff, 0x6c, 0x83, 0xb0, 0xff, 0x76, 0x8d, 0xb8, 0xff, 0x7e, 0x94, 0xbf, 0xff, 0x84, 0x99, 0xc1, 0xff, 0x8b, 0x9d, 0xc3, 0xff, 0x88, 0x9b, 0xc1, 0xff, 0x83, 0x99, 0xbd, 0xff, 0x80, 0x98, 0xba, 0xff, 0x7e, 0x94, 0xbb, 0xff, 0x7c, 0x93, 0xbd, 0xff, 0x7e, 0x95, 0xbf, 0xff, 0x7b, 0x92, 0xbc, 0xff, 0x77, 0x8e, 0xb6, 0xff, 0x75, 0x8b, 0xb4, 0xff, 0x75, 0x8a, 0xb4, 0xff, 0x74, 0x89, 0xb4, 0xff, 0x72, 0x88, 0xb2, 0xff, 0x70, 0x88, 0xb2, 0xff, 0x70, 0x89, 0xb3, 0xff, 0x6f, 0x86, 0xb1, 0xff, 0x6f, 0x86, 0xb1, 0xff, 0x73, 0x8b, 0xb6, 0xff, 0x76, 0x90, 0xb6, 0xff, 0x78, 0x94, 0xb8, 0xff, 0x81, 0x9a, 0xbf, 0xff, 0x8a, 0x9b, 0xc1, 0xff, 0x97, 0xa5, 0xc9, 0xff, 0xa8, 0xb4, 0xd2, 0xff, 0xae, 0xbb, 0xd4, 0xff, 0xaf, 0xbb, 0xd7, 0xff, 0xa9, 0xb9, 0xd3, 0xff, 0x9f, 0xb1, 0xca, 0xff, 0x96, 0xa9, 0xc3, 0xff, 0x84, 0x95, 0xbc, 0xff, 0x47, 0x49, 0x69, 0xff, 0x1b, 0x14, 0x16, 0xff, 0x24, 0x1e, 0x1e, 0xff, 0x29, 0x24, 0x24, 0xff, 0x25, 0x1d, 0x1f, 0xff, 0x30, 0x2d, 0x36, 0xff, 0x40, 0x44, 0x50, 0xff, 0x39, 0x3d, 0x46, 0xff, 0x40, 0x45, 0x55, 0xff, 0x51, 0x5a, 0x6d, 0xff, 0x4d, 0x5d, 0x74, 0xff, 0x4f, 0x64, 0x89, 0xff, 0x73, 0x84, 0xab, 0xff, 0x9a, 0xb0, 0xce, 0xff, 0x91, 0xab, 0xc0, 0xff, 0xc2, 0xc8, 0xd6, 0xff, 0xef, 0xed, 0xf4, 0xff, 0xdc, 0xdc, 0xda, 0xff, 0x7f, 0x83, 0xa2, 0xff, 0xaa, 0xb4, 0xcd, 0xff, 0xe5, 0xe7, 0xe4, 0xff, 0xf5, 0xee, 0xea, 0xff, 0xfc, 0xf9, 0xf7, 0xff, 0xfc, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xeb, 0xea, 0xe6, 0xff, 0xc1, 0xc4, 0xc4, 0xff, 0x95, 0x9b, 0x9d, 0xff, 0x6b, 0x75, 0x7c, 0xff, 0x55, 0x5d, 0x67, 0xff, 0x52, 0x54, 0x5f, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa3, 0xb7, 0xc7, 0x40, 0x94, 0xa3, 0xb2, 0xff, 0x91, 0xa0, 0xb2, 0xff, 0x85, 0x93, 0xa5, 0xff, 0x76, 0x86, 0x95, 0xff, 0x6d, 0x7c, 0x8b, 0xff, 0x63, 0x72, 0x84, 0xff, 0x58, 0x6a, 0x7c, 0xff, 0x4d, 0x5e, 0x73, 0xff, 0x44, 0x54, 0x68, 0xff, 0x32, 0x44, 0x55, 0xff, 0x38, 0x41, 0x47, 0xff, 0x77, 0x74, 0x6c, 0xff, 0x91, 0x8e, 0x88, 0xff, 0x91, 0x90, 0x8e, 0xff, 0x91, 0x93, 0x8f, 0xff, 0x8a, 0x8c, 0x8a, 0xff, 0x80, 0x82, 0x84, 0xff, 0x96, 0x9a, 0x9f, 0xff, 0x98, 0xa5, 0xb3, 0xff, 0x91, 0xa0, 0xae, 0xff, 0xac, 0xb0, 0xb1, 0xff, 0xd2, 0xce, 0xcc, 0xff, 0xf3, 0xf0, 0xed, 0xff, 0xd4, 0xd0, 0xc7, 0xff, 0xb9, 0xb9, 0xb4, 0xff, 0x65, 0x6a, 0x71, 0xff, 0x21, 0x24, 0x2d, 0xff, 0x1a, 0x19, 0x1f, 0xff, 0x1c, 0x1b, 0x1e, 0xff, 0x23, 0x22, 0x23, 0xff, 0x1e, 0x19, 0x16, 0xff, 0x1f, 0x1b, 0x1a, 0xff, 0x18, 0x15, 0x16, 0xff, 0x16, 0x13, 0x16, 0xff, 0x2a, 0x2e, 0x3f, 0xff, 0x50, 0x62, 0x85, 0xff, 0x6c, 0x84, 0xae, 0xff, 0x7c, 0x92, 0xbc, 0xff, 0x82, 0x99, 0xc4, 0xff, 0x86, 0x9a, 0xc2, 0xff, 0x8a, 0x9c, 0xc1, 0xff, 0x88, 0x9b, 0xc1, 0xff, 0x84, 0x97, 0xbd, 0xff, 0x82, 0x96, 0xba, 0xff, 0x80, 0x96, 0xb9, 0xff, 0x7e, 0x94, 0xbd, 0xff, 0x7d, 0x92, 0xbd, 0xff, 0x7d, 0x90, 0xbd, 0xff, 0x7a, 0x8d, 0xbb, 0xff, 0x72, 0x88, 0xb5, 0xff, 0x70, 0x8a, 0xb1, 0xff, 0x6e, 0x88, 0xae, 0xff, 0x6d, 0x85, 0xac, 0xff, 0x6d, 0x86, 0xad, 0xff, 0x6d, 0x87, 0xad, 0xff, 0x6c, 0x85, 0xaf, 0xff, 0x6d, 0x85, 0xb3, 0xff, 0x72, 0x88, 0xb5, 0xff, 0x73, 0x89, 0xb0, 0xff, 0x70, 0x89, 0xaf, 0xff, 0x75, 0x8e, 0xb7, 0xff, 0x7c, 0x8d, 0xb6, 0xff, 0x83, 0x91, 0xb8, 0xff, 0x8e, 0x9c, 0xbe, 0xff, 0x9b, 0xab, 0xc8, 0xff, 0xa9, 0xb9, 0xd5, 0xff, 0xb4, 0xc6, 0xe3, 0xff, 0xad, 0xbf, 0xde, 0xff, 0x99, 0xad, 0xca, 0xff, 0x82, 0x9c, 0xbf, 0xff, 0x62, 0x6e, 0x8f, 0xff, 0x2d, 0x27, 0x30, 0xff, 0x25, 0x20, 0x21, 0xff, 0x25, 0x23, 0x29, 0xff, 0x18, 0x17, 0x14, 0xff, 0x2b, 0x26, 0x29, 0xff, 0x2d, 0x2c, 0x37, 0xff, 0x36, 0x3a, 0x43, 0xff, 0x33, 0x35, 0x42, 0xff, 0x44, 0x47, 0x59, 0xff, 0x52, 0x59, 0x6d, 0xff, 0x47, 0x55, 0x68, 0xff, 0x56, 0x66, 0x87, 0xff, 0x87, 0x97, 0xbb, 0xff, 0xab, 0xbb, 0xd0, 0xff, 0xb0, 0xb6, 0xcd, 0xff, 0xb1, 0xba, 0xc6, 0xff, 0xbd, 0xc6, 0xd3, 0xff, 0x8a, 0x93, 0xa3, 0xff, 0xa7, 0xb5, 0xc8, 0xff, 0xde, 0xe1, 0xef, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xfd, 0xf8, 0xf5, 0xff, 0xff, 0xfe, 0xfc, 0xff, 0xfe, 0xff, 0xfe, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xfe, 0xfd, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfb, 0xfd, 0xfe, 0xff, 0xfb, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfc, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xf4, 0xec, 0xff, 0xc7, 0xcb, 0xca, 0xff, 0x91, 0x99, 0x9f, 0xff, 0x71, 0x76, 0x80, 0xff, 0x5b, 0x62, 0x6f, 0xff, 0x55, 0x5b, 0x6a, 0xff, 0x68, 0x67, 0x70, 0xff, 0x90, 0x8b, 0x8b, 0xff, 0xbc, 0xb4, 0xb0, 0xff, 0xe0, 0xd9, 0xd2, 0xff, 0xfb, 0xf7, 0xeb, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa6, 0xb9, 0xc6, 0xb3, 0x99, 0xa7, 0xb5, 0xff, 0x93, 0xa0, 0xb4, 0xff, 0x85, 0x93, 0xa6, 0xff, 0x74, 0x84, 0x92, 0xff, 0x67, 0x77, 0x83, 0xff, 0x5f, 0x6e, 0x7e, 0xff, 0x55, 0x68, 0x7b, 0xff, 0x4e, 0x5f, 0x74, 0xff, 0x4b, 0x5a, 0x6c, 0xff, 0x35, 0x45, 0x57, 0xff, 0x36, 0x40, 0x4a, 0xff, 0x7b, 0x78, 0x74, 0xff, 0x99, 0x94, 0x90, 0xff, 0x8c, 0x8a, 0x8a, 0xff, 0x88, 0x8b, 0x89, 0xff, 0x90, 0x96, 0x96, 0xff, 0x93, 0x99, 0x9c, 0xff, 0x9c, 0xa5, 0xab, 0xff, 0x8c, 0x9b, 0xa8, 0xff, 0x88, 0x95, 0xa3, 0xff, 0xb0, 0xb3, 0xb4, 0xff, 0xe9, 0xe7, 0xe3, 0xff, 0xe6, 0xe4, 0xe5, 0xff, 0xe0, 0xdd, 0xd6, 0xff, 0xc1, 0xc0, 0xb8, 0xff, 0x40, 0x44, 0x49, 0xff, 0x1e, 0x20, 0x2b, 0xff, 0x1d, 0x1d, 0x24, 0xff, 0x1d, 0x1d, 0x1f, 0xff, 0x21, 0x1b, 0x1f, 0xff, 0x24, 0x1a, 0x1b, 0xff, 0x1c, 0x15, 0x16, 0xff, 0x10, 0x0b, 0x08, 0xff, 0x17, 0x14, 0x17, 0xff, 0x34, 0x3a, 0x4d, 0xff, 0x54, 0x68, 0x88, 0xff, 0x71, 0x89, 0xae, 0xff, 0x7c, 0x92, 0xbd, 0xff, 0x81, 0x98, 0xc3, 0xff, 0x85, 0x99, 0xc0, 0xff, 0x88, 0x9b, 0xbe, 0xff, 0x87, 0x9b, 0xc0, 0xff, 0x88, 0x99, 0xbf, 0xff, 0x8a, 0x9a, 0xbd, 0xff, 0x8a, 0x9c, 0xbb, 0xff, 0x8b, 0x9d, 0xc0, 0xff, 0x86, 0x96, 0xbe, 0xff, 0x81, 0x8f, 0xba, 0xff, 0x78, 0x87, 0xb4, 0xff, 0x72, 0x87, 0xb1, 0xff, 0x6d, 0x87, 0xac, 0xff, 0x6b, 0x85, 0xa8, 0xff, 0x6a, 0x83, 0xa8, 0xff, 0x6b, 0x84, 0xab, 0xff, 0x6b, 0x85, 0xaa, 0xff, 0x6a, 0x85, 0xad, 0xff, 0x6a, 0x82, 0xb0, 0xff, 0x6e, 0x83, 0xb0, 0xff, 0x6c, 0x7e, 0xa7, 0xff, 0x63, 0x74, 0x9b, 0xff, 0x54, 0x65, 0x8c, 0xff, 0x50, 0x5f, 0x81, 0xff, 0x43, 0x4f, 0x6e, 0xff, 0x41, 0x4d, 0x6b, 0xff, 0x4d, 0x59, 0x76, 0xff, 0x5b, 0x6a, 0x82, 0xff, 0x73, 0x7f, 0x98, 0xff, 0x92, 0x9b, 0xb5, 0xff, 0xa9, 0xb5, 0xcf, 0xff, 0x9c, 0xb1, 0xd1, 0xff, 0x78, 0x88, 0xaf, 0xff, 0x3e, 0x41, 0x59, 0xff, 0x1c, 0x16, 0x17, 0xff, 0x2d, 0x2a, 0x30, 0xff, 0x20, 0x23, 0x24, 0xff, 0x19, 0x12, 0x12, 0xff, 0x28, 0x24, 0x2b, 0xff, 0x2b, 0x29, 0x32, 0xff, 0x2e, 0x2d, 0x38, 0xff, 0x34, 0x38, 0x45, 0xff, 0x40, 0x48, 0x59, 0xff, 0x43, 0x4c, 0x5f, 0xff, 0x3f, 0x4b, 0x62, 0xff, 0x6c, 0x75, 0x99, 0xff, 0x98, 0xa3, 0xc2, 0xff, 0xcd, 0xd4, 0xe0, 0xff, 0xc8, 0xca, 0xd5, 0xff, 0xa3, 0xac, 0xbc, 0xff, 0xa6, 0xb1, 0xc0, 0xff, 0xba, 0xba, 0xc3, 0xff, 0xb2, 0xb5, 0xc2, 0xff, 0xd8, 0xdd, 0xe8, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf2, 0xf3, 0xff, 0xc6, 0xcc, 0xcc, 0xff, 0x9f, 0xa7, 0xa7, 0xff, 0x7b, 0x7e, 0x82, 0xff, 0x61, 0x64, 0x71, 0xff, 0x58, 0x5d, 0x6e, 0xff, 0x68, 0x6c, 0x79, 0xff, 0x8f, 0x8f, 0x95, 0xff, 0xbe, 0xb7, 0xb3, 0xff, 0xe8, 0xdc, 0xd5, 0xff, 0xff, 0xf9, 0xf0, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfd, 0xfc, 0xb3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0xa5, 0xac, 0x22, 0xa8, 0xb7, 0xc2, 0xfe, 0x99, 0xa9, 0xb7, 0xff, 0x92, 0xa0, 0xb3, 0xff, 0x85, 0x93, 0xa6, 0xff, 0x77, 0x86, 0x95, 0xff, 0x6b, 0x7a, 0x87, 0xff, 0x60, 0x6f, 0x80, 0xff, 0x55, 0x68, 0x7b, 0xff, 0x4e, 0x5f, 0x74, 0xff, 0x4c, 0x5b, 0x6d, 0xff, 0x3a, 0x4c, 0x5d, 0xff, 0x36, 0x3e, 0x4b, 0xff, 0x7c, 0x78, 0x78, 0xff, 0x9a, 0x94, 0x93, 0xff, 0x89, 0x87, 0x86, 0xff, 0x8b, 0x8d, 0x8a, 0xff, 0x97, 0x9e, 0x9c, 0xff, 0x95, 0xa0, 0xa0, 0xff, 0x93, 0xa0, 0xa5, 0xff, 0x81, 0x8f, 0x9c, 0xff, 0x87, 0x93, 0xa1, 0xff, 0xb7, 0xbb, 0xba, 0xff, 0xfa, 0xfa, 0xf1, 0xff, 0xe9, 0xeb, 0xe2, 0xff, 0xd7, 0xd4, 0xcb, 0xff, 0x9f, 0x9c, 0x9c, 0xff, 0x1d, 0x20, 0x2b, 0xff, 0x1b, 0x1d, 0x29, 0xff, 0x1e, 0x1d, 0x23, 0xff, 0x26, 0x25, 0x2a, 0xff, 0x22, 0x22, 0x2a, 0xff, 0x24, 0x22, 0x26, 0xff, 0x17, 0x13, 0x15, 0xff, 0x11, 0x0c, 0x0a, 0xff, 0x21, 0x1e, 0x20, 0xff, 0x3c, 0x41, 0x53, 0xff, 0x54, 0x67, 0x88, 0xff, 0x72, 0x8a, 0xb1, 0xff, 0x7c, 0x92, 0xbd, 0xff, 0x80, 0x96, 0xc2, 0xff, 0x84, 0x99, 0xc1, 0xff, 0x89, 0x9d, 0xc3, 0xff, 0x84, 0x99, 0xc1, 0xff, 0x8c, 0x9a, 0xbf, 0xff, 0x88, 0x96, 0xb5, 0xff, 0x7f, 0x8e, 0xaa, 0xff, 0x78, 0x84, 0xa2, 0xff, 0x6e, 0x79, 0x9a, 0xff, 0x67, 0x73, 0x97, 0xff, 0x5f, 0x70, 0x94, 0xff, 0x60, 0x74, 0x98, 0xff, 0x62, 0x77, 0x9d, 0xff, 0x6c, 0x80, 0xa6, 0xff, 0x6d, 0x82, 0xa9, 0xff, 0x68, 0x7f, 0xac, 0xff, 0x68, 0x7f, 0xab, 0xff, 0x66, 0x7f, 0xaa, 0xff, 0x6a, 0x83, 0xb0, 0xff, 0x66, 0x7b, 0xa9, 0xff, 0x57, 0x69, 0x95, 0xff, 0x45, 0x51, 0x76, 0xff, 0x39, 0x40, 0x5c, 0xff, 0x37, 0x40, 0x51, 0xff, 0x38, 0x3d, 0x4b, 0xff, 0x3e, 0x40, 0x50, 0xff, 0x37, 0x39, 0x4a, 0xff, 0x35, 0x3a, 0x45, 0xff, 0x3b, 0x3c, 0x45, 0xff, 0x4e, 0x4d, 0x56, 0xff, 0x67, 0x68, 0x6f, 0xff, 0x8e, 0x9c, 0xaf, 0xff, 0x96, 0xac, 0xd5, 0xff, 0x54, 0x64, 0x87, 0xff, 0x24, 0x1c, 0x1a, 0xff, 0x2d, 0x25, 0x23, 0xff, 0x2e, 0x2f, 0x38, 0xff, 0x1f, 0x17, 0x1a, 0xff, 0x24, 0x22, 0x24, 0xff, 0x25, 0x21, 0x25, 0xff, 0x2a, 0x27, 0x2e, 0xff, 0x40, 0x41, 0x4c, 0xff, 0x46, 0x4c, 0x5a, 0xff, 0x46, 0x4d, 0x5c, 0xff, 0x42, 0x4a, 0x5e, 0xff, 0x4f, 0x5a, 0x7c, 0xff, 0x6d, 0x80, 0xa6, 0xff, 0xa5, 0xb0, 0xc9, 0xff, 0xff, 0xfa, 0xfa, 0xff, 0xe0, 0xd8, 0xde, 0xff, 0xa1, 0xa2, 0xb5, 0xff, 0xd2, 0xd3, 0xd5, 0xff, 0xd8, 0xd6, 0xd1, 0xff, 0xb0, 0xb0, 0xc1, 0xff, 0xc7, 0xd1, 0xdc, 0xff, 0xe9, 0xec, 0xf0, 0xff, 0xf9, 0xf9, 0xf4, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xee, 0xf2, 0xec, 0xff, 0xcd, 0xd6, 0xd5, 0xff, 0xa1, 0xaf, 0xb7, 0xff, 0x87, 0x90, 0x9d, 0xff, 0x75, 0x79, 0x81, 0xff, 0x68, 0x6c, 0x75, 0xff, 0x76, 0x78, 0x82, 0xff, 0x9b, 0x95, 0x98, 0xff, 0xbf, 0xba, 0xb8, 0xff, 0xe6, 0xdd, 0xd6, 0xff, 0xff, 0xfd, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfe, 0xf8, 0xff, 0xf9, 0xfd, 0xf6, 0xff, 0xf9, 0xfd, 0xf3, 0xff, 0xfd, 0xf9, 0xf1, 0xff, 0xff, 0xf3, 0xf2, 0xfe, 0xff, 0xf7, 0xf7, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8a, 0x98, 0xa7, 0x86, 0xa8, 0xb9, 0xc3, 0xff, 0x98, 0xa9, 0xb7, 0xff, 0x8f, 0x9f, 0xb0, 0xff, 0x86, 0x93, 0xa6, 0xff, 0x78, 0x87, 0x96, 0xff, 0x6e, 0x7d, 0x8a, 0xff, 0x65, 0x73, 0x83, 0xff, 0x59, 0x69, 0x7c, 0xff, 0x51, 0x60, 0x75, 0xff, 0x49, 0x57, 0x69, 0xff, 0x3f, 0x4f, 0x60, 0xff, 0x3d, 0x47, 0x56, 0xff, 0x72, 0x75, 0x77, 0xff, 0x91, 0x8e, 0x87, 0xff, 0x87, 0x85, 0x80, 0xff, 0x91, 0x92, 0x90, 0xff, 0x98, 0xa0, 0x9e, 0xff, 0x95, 0xa1, 0xa4, 0xff, 0x87, 0x92, 0x9e, 0xff, 0x79, 0x8a, 0x9a, 0xff, 0x81, 0x8f, 0x97, 0xff, 0xc1, 0xc2, 0xbe, 0xff, 0xfd, 0xfc, 0xf3, 0xff, 0xe3, 0xe4, 0xd8, 0xff, 0xc1, 0xc4, 0xbe, 0xff, 0x5b, 0x5f, 0x60, 0xff, 0x1b, 0x1b, 0x26, 0xff, 0x1c, 0x1a, 0x26, 0xff, 0x25, 0x23, 0x2e, 0xff, 0x35, 0x34, 0x3d, 0xff, 0x30, 0x32, 0x37, 0xff, 0x20, 0x21, 0x27, 0xff, 0x17, 0x15, 0x19, 0xff, 0x13, 0x0e, 0x09, 0xff, 0x2a, 0x27, 0x26, 0xff, 0x40, 0x48, 0x5e, 0xff, 0x59, 0x6c, 0x8f, 0xff, 0x79, 0x8b, 0xb2, 0xff, 0x84, 0x94, 0xbe, 0xff, 0x84, 0x96, 0xc2, 0xff, 0x87, 0x9d, 0xcc, 0xff, 0x82, 0x94, 0xb9, 0xff, 0x76, 0x82, 0x9b, 0xff, 0x6a, 0x6d, 0x82, 0xff, 0x58, 0x5a, 0x6d, 0xff, 0x4f, 0x52, 0x64, 0xff, 0x4c, 0x4d, 0x60, 0xff, 0x49, 0x49, 0x5d, 0xff, 0x4e, 0x4f, 0x62, 0xff, 0x54, 0x5a, 0x70, 0xff, 0x4b, 0x57, 0x75, 0xff, 0x55, 0x62, 0x7f, 0xff, 0x6a, 0x7b, 0x9e, 0xff, 0x69, 0x7f, 0xac, 0xff, 0x63, 0x7a, 0xa9, 0xff, 0x63, 0x7a, 0xa8, 0xff, 0x62, 0x79, 0xa7, 0xff, 0x6a, 0x81, 0xae, 0xff, 0x5f, 0x77, 0xa5, 0xff, 0x42, 0x57, 0x7d, 0xff, 0x35, 0x41, 0x5b, 0xff, 0x3d, 0x40, 0x50, 0xff, 0x43, 0x47, 0x57, 0xff, 0x4b, 0x4c, 0x60, 0xff, 0x4d, 0x51, 0x66, 0xff, 0x4c, 0x55, 0x6a, 0xff, 0x51, 0x5a, 0x6e, 0xff, 0x5b, 0x5f, 0x74, 0xff, 0x6b, 0x6f, 0x81, 0xff, 0x5e, 0x63, 0x72, 0xff, 0x53, 0x5c, 0x65, 0xff, 0x7e, 0x8c, 0xa1, 0xff, 0x79, 0x8e, 0xaf, 0xff, 0x34, 0x32, 0x3a, 0xff, 0x1f, 0x16, 0x17, 0xff, 0x20, 0x1d, 0x24, 0xff, 0x2a, 0x24, 0x27, 0xff, 0x24, 0x20, 0x23, 0xff, 0x29, 0x24, 0x29, 0xff, 0x27, 0x21, 0x29, 0xff, 0x2e, 0x2a, 0x35, 0xff, 0x37, 0x3a, 0x44, 0xff, 0x43, 0x47, 0x54, 0xff, 0x4b, 0x51, 0x68, 0xff, 0x54, 0x5e, 0x80, 0xff, 0x5a, 0x6c, 0x93, 0xff, 0x6b, 0x86, 0xac, 0xff, 0x99, 0xab, 0xc4, 0xff, 0xcb, 0xce, 0xda, 0xff, 0xcd, 0xd1, 0xd4, 0xff, 0xb6, 0xc0, 0xbf, 0xff, 0xf5, 0xf3, 0xf1, 0xff, 0xe8, 0xe1, 0xe7, 0xff, 0xab, 0xb2, 0xc4, 0xff, 0xbd, 0xc0, 0xd3, 0xff, 0xc3, 0xcc, 0xda, 0xff, 0xb5, 0xc5, 0xd0, 0xff, 0xb0, 0xc1, 0xcd, 0xff, 0x9f, 0xae, 0xb9, 0xff, 0x88, 0x93, 0xa2, 0xff, 0x81, 0x89, 0x92, 0xff, 0x83, 0x87, 0x8a, 0xff, 0xa1, 0x9e, 0xa3, 0xff, 0xc7, 0xc2, 0xbf, 0xff, 0xec, 0xe4, 0xde, 0xff, 0xff, 0xfe, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xfe, 0xfc, 0xf8, 0xff, 0xfa, 0xf9, 0xf7, 0xff, 0xfa, 0xf8, 0xf5, 0xff, 0xfd, 0xf7, 0xf4, 0xff, 0xfd, 0xf7, 0xf6, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xf9, 0xf8, 0xf3, 0xff, 0xfc, 0xf7, 0xf4, 0xff, 0xff, 0xf5, 0xf3, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x3f, 0x3f, 0x04, 0x86, 0x97, 0xa6, 0xe7, 0xa9, 0xb9, 0xc8, 0xff, 0x99, 0xaa, 0xbb, 0xff, 0x91, 0xa0, 0xb5, 0xff, 0x89, 0x96, 0xa9, 0xff, 0x78, 0x88, 0x96, 0xff, 0x6e, 0x7e, 0x8a, 0xff, 0x68, 0x74, 0x85, 0xff, 0x5c, 0x6b, 0x7e, 0xff, 0x54, 0x62, 0x77, 0xff, 0x4a, 0x58, 0x6a, 0xff, 0x37, 0x46, 0x57, 0xff, 0x3a, 0x45, 0x53, 0xff, 0x72, 0x78, 0x77, 0xff, 0x88, 0x87, 0x7f, 0xff, 0x89, 0x89, 0x87, 0xff, 0x97, 0x9a, 0x9b, 0xff, 0x92, 0x9b, 0xa0, 0xff, 0x8e, 0x9b, 0xa4, 0xff, 0x84, 0x92, 0x9f, 0xff, 0x80, 0x8e, 0x9c, 0xff, 0x79, 0x86, 0x93, 0xff, 0xc2, 0xc4, 0xc5, 0xff, 0xf7, 0xf7, 0xeb, 0xff, 0xfe, 0xfd, 0xf6, 0xff, 0x93, 0x95, 0x99, 0xff, 0x2d, 0x32, 0x38, 0xff, 0x2c, 0x2e, 0x37, 0xff, 0x18, 0x18, 0x20, 0xff, 0x20, 0x21, 0x29, 0xff, 0x1d, 0x1c, 0x22, 0xff, 0x22, 0x1e, 0x1f, 0xff, 0x1f, 0x1c, 0x1e, 0xff, 0x12, 0x0d, 0x0e, 0xff, 0x14, 0x0b, 0x09, 0xff, 0x30, 0x2f, 0x35, 0xff, 0x48, 0x50, 0x69, 0xff, 0x6a, 0x7d, 0xa0, 0xff, 0x85, 0x95, 0xb7, 0xff, 0x86, 0x99, 0xc3, 0xff, 0x8d, 0xa0, 0xca, 0xff, 0x77, 0x81, 0xa0, 0xff, 0x50, 0x54, 0x69, 0xff, 0x4a, 0x45, 0x47, 0xff, 0x38, 0x34, 0x37, 0xff, 0x3d, 0x38, 0x45, 0xff, 0x4b, 0x46, 0x56, 0xff, 0x49, 0x47, 0x54, 0xff, 0x45, 0x43, 0x4f, 0xff, 0x45, 0x43, 0x51, 0xff, 0x4e, 0x4d, 0x5f, 0xff, 0x4b, 0x4d, 0x60, 0xff, 0x47, 0x50, 0x68, 0xff, 0x58, 0x6b, 0x8e, 0xff, 0x66, 0x7e, 0xac, 0xff, 0x64, 0x7c, 0xaa, 0xff, 0x66, 0x7d, 0xaa, 0xff, 0x66, 0x7d, 0xaa, 0xff, 0x66, 0x7d, 0xaa, 0xff, 0x5b, 0x73, 0xa1, 0xff, 0x47, 0x5a, 0x80, 0xff, 0x48, 0x56, 0x71, 0xff, 0x50, 0x58, 0x71, 0xff, 0x58, 0x5f, 0x81, 0xff, 0x5c, 0x69, 0x8f, 0xff, 0x56, 0x69, 0x91, 0xff, 0x57, 0x6b, 0x94, 0xff, 0x63, 0x77, 0x9f, 0xff, 0x6a, 0x7c, 0xa9, 0xff, 0x74, 0x88, 0xb3, 0xff, 0x8a, 0xa0, 0xc6, 0xff, 0x8c, 0x95, 0xb1, 0xff, 0x6b, 0x6c, 0x7f, 0xff, 0x70, 0x7a, 0x97, 0xff, 0x42, 0x46, 0x59, 0xff, 0x1a, 0x15, 0x14, 0xff, 0x16, 0x0f, 0x0e, 0xff, 0x1d, 0x15, 0x15, 0xff, 0x20, 0x19, 0x1b, 0xff, 0x21, 0x1d, 0x22, 0xff, 0x31, 0x2a, 0x34, 0xff, 0x36, 0x32, 0x3d, 0xff, 0x33, 0x34, 0x3f, 0xff, 0x3b, 0x3d, 0x4b, 0xff, 0x48, 0x50, 0x67, 0xff, 0x57, 0x65, 0x82, 0xff, 0x5b, 0x6c, 0x8f, 0xff, 0x6b, 0x80, 0xa8, 0xff, 0x88, 0x9d, 0xbe, 0xff, 0x99, 0xaa, 0xbd, 0xff, 0xb0, 0xc0, 0xd0, 0xff, 0xcf, 0xd4, 0xdd, 0xff, 0xc0, 0xc1, 0xcf, 0xff, 0xb9, 0xc3, 0xd6, 0xff, 0xb1, 0xc1, 0xd7, 0xff, 0xa6, 0xb4, 0xcd, 0xff, 0x97, 0xa8, 0xc8, 0xff, 0x8a, 0x9e, 0xbf, 0xff, 0x9b, 0xa9, 0xbf, 0xff, 0x9f, 0xa5, 0xb4, 0xff, 0xcc, 0xc9, 0xcd, 0xff, 0xf5, 0xec, 0xe6, 0xff, 0xff, 0xfb, 0xed, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xfe, 0xfc, 0xff, 0xfc, 0xfc, 0xf9, 0xff, 0xfc, 0xfb, 0xf6, 0xff, 0xfd, 0xfb, 0xf4, 0xff, 0xfc, 0xf9, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfc, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xf9, 0xf7, 0xf3, 0xff, 0xf9, 0xf7, 0xf3, 0xff, 0xfb, 0xf7, 0xf2, 0xe7, 0xff, 0xff, 0xff, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2d, 0x3b, 0x45, 0x49, 0x87, 0x94, 0xa6, 0xff, 0xac, 0xba, 0xcd, 0xff, 0x9d, 0xac, 0xbf, 0xff, 0x92, 0xa0, 0xb7, 0xff, 0x86, 0x94, 0xa7, 0xff, 0x7d, 0x8c, 0x9b, 0xff, 0x75, 0x84, 0x91, 0xff, 0x68, 0x75, 0x86, 0xff, 0x5d, 0x6d, 0x80, 0xff, 0x53, 0x62, 0x77, 0xff, 0x4c, 0x5b, 0x6c, 0xff, 0x3c, 0x4c, 0x5c, 0xff, 0x36, 0x41, 0x4f, 0xff, 0x6e, 0x71, 0x72, 0xff, 0x8e, 0x90, 0x8a, 0xff, 0x8d, 0x92, 0x93, 0xff, 0x97, 0x9c, 0x9c, 0xff, 0x8a, 0x93, 0x9e, 0xff, 0x81, 0x91, 0xa3, 0xff, 0x8e, 0x9f, 0xa8, 0xff, 0x85, 0x8e, 0x9b, 0xff, 0x78, 0x86, 0x97, 0xff, 0xc0, 0xc6, 0xca, 0xff, 0xef, 0xed, 0xe3, 0xff, 0xd2, 0xce, 0xcf, 0xff, 0x6a, 0x67, 0x75, 0xff, 0x3a, 0x3b, 0x47, 0xff, 0x20, 0x21, 0x29, 0xff, 0x18, 0x18, 0x1b, 0xff, 0x1a, 0x1b, 0x1e, 0xff, 0x04, 0x04, 0x06, 0xff, 0x18, 0x12, 0x13, 0xff, 0x1f, 0x1b, 0x1c, 0xff, 0x0b, 0x03, 0x01, 0xff, 0x1b, 0x11, 0x10, 0xff, 0x30, 0x32, 0x3f, 0xff, 0x51, 0x5b, 0x77, 0xff, 0x76, 0x88, 0xad, 0xff, 0x8c, 0x9d, 0xc0, 0xff, 0x92, 0xa8, 0xcf, 0xff, 0x6e, 0x7d, 0x9c, 0xff, 0x3e, 0x3c, 0x45, 0xff, 0x3b, 0x38, 0x41, 0xff, 0x4b, 0x4c, 0x58, 0xff, 0x53, 0x58, 0x65, 0xff, 0x57, 0x59, 0x6f, 0xff, 0x5b, 0x5d, 0x7a, 0xff, 0x59, 0x61, 0x7c, 0xff, 0x52, 0x5a, 0x74, 0xff, 0x4b, 0x54, 0x6f, 0xff, 0x50, 0x59, 0x75, 0xff, 0x57, 0x5e, 0x77, 0xff, 0x51, 0x5b, 0x75, 0xff, 0x58, 0x6c, 0x8e, 0xff, 0x66, 0x7f, 0xab, 0xff, 0x66, 0x7d, 0xab, 0xff, 0x66, 0x7d, 0xaa, 0xff, 0x65, 0x7c, 0xa9, 0xff, 0x64, 0x7b, 0xa8, 0xff, 0x60, 0x77, 0xa5, 0xff, 0x5d, 0x73, 0x9e, 0xff, 0x65, 0x79, 0xa3, 0xff, 0x61, 0x75, 0x9f, 0xff, 0x56, 0x6d, 0x99, 0xff, 0x56, 0x67, 0x98, 0xff, 0x53, 0x64, 0x94, 0xff, 0x52, 0x65, 0x93, 0xff, 0x56, 0x69, 0x97, 0xff, 0x56, 0x6b, 0x9e, 0xff, 0x63, 0x7a, 0xad, 0xff, 0x83, 0x9c, 0xcc, 0xff, 0x9c, 0xb4, 0xd8, 0xff, 0xa4, 0xb0, 0xc6, 0xff, 0x72, 0x78, 0x92, 0xff, 0x40, 0x45, 0x5a, 0xff, 0x1c, 0x1b, 0x16, 0xff, 0x1e, 0x14, 0x0e, 0xff, 0x1c, 0x12, 0x11, 0xff, 0x1e, 0x15, 0x14, 0xff, 0x20, 0x1c, 0x21, 0xff, 0x2a, 0x25, 0x2e, 0xff, 0x36, 0x34, 0x3e, 0xff, 0x47, 0x4b, 0x55, 0xff, 0x46, 0x4a, 0x58, 0xff, 0x39, 0x41, 0x54, 0xff, 0x54, 0x60, 0x7a, 0xff, 0x66, 0x76, 0x96, 0xff, 0x64, 0x79, 0xa2, 0xff, 0x7d, 0x97, 0xb9, 0xff, 0xb1, 0xc5, 0xdb, 0xff, 0x98, 0xa8, 0xc7, 0xff, 0xa0, 0xb2, 0xd0, 0xff, 0xa4, 0xba, 0xd1, 0xff, 0x9e, 0xb5, 0xc7, 0xff, 0xb9, 0xc3, 0xd1, 0xff, 0xc5, 0xcc, 0xd5, 0xff, 0xcb, 0xd5, 0xdc, 0xff, 0xda, 0xdd, 0xde, 0xff, 0xf3, 0xef, 0xeb, 0xff, 0xe6, 0xe2, 0xe0, 0xff, 0xec, 0xe9, 0xe5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfc, 0xf7, 0xff, 0xfa, 0xfd, 0xf4, 0xff, 0xf9, 0xfc, 0xf8, 0xff, 0xfb, 0xf9, 0xf7, 0xff, 0xfe, 0xf8, 0xf5, 0xff, 0xfc, 0xfa, 0xf6, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xfb, 0xf8, 0xf4, 0x4a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x23, 0x33, 0x3e, 0x9f, 0x7e, 0x8e, 0xa3, 0xff, 0xad, 0xbd, 0xd1, 0xff, 0x9e, 0xae, 0xbe, 0xff, 0x92, 0xa2, 0xb3, 0xff, 0x85, 0x95, 0xa7, 0xff, 0x80, 0x8e, 0x9d, 0xff, 0x7b, 0x89, 0x97, 0xff, 0x6b, 0x7a, 0x8b, 0xff, 0x5b, 0x6f, 0x81, 0xff, 0x53, 0x64, 0x77, 0xff, 0x4a, 0x59, 0x69, 0xff, 0x44, 0x56, 0x68, 0xff, 0x38, 0x46, 0x53, 0xff, 0x63, 0x64, 0x68, 0xff, 0x95, 0x99, 0x97, 0xff, 0x8a, 0x90, 0x8f, 0xff, 0x91, 0x97, 0x97, 0xff, 0x88, 0x92, 0x9d, 0xff, 0x82, 0x92, 0xa5, 0xff, 0x92, 0x9c, 0xa1, 0xff, 0x8a, 0x92, 0x9e, 0xff, 0x77, 0x89, 0x99, 0xff, 0xa9, 0xb3, 0xb6, 0xff, 0xdc, 0xdb, 0xd8, 0xff, 0x89, 0x86, 0x8d, 0xff, 0x55, 0x50, 0x60, 0xff, 0x31, 0x30, 0x3b, 0xff, 0x16, 0x16, 0x1d, 0xff, 0x18, 0x17, 0x18, 0xff, 0x16, 0x15, 0x16, 0xff, 0x08, 0x07, 0x08, 0xff, 0x21, 0x1f, 0x1f, 0xff, 0x16, 0x15, 0x15, 0xff, 0x07, 0x03, 0x00, 0xff, 0x20, 0x1a, 0x17, 0xff, 0x33, 0x35, 0x42, 0xff, 0x51, 0x5e, 0x7d, 0xff, 0x7b, 0x8e, 0xb5, 0xff, 0x94, 0xa7, 0xcc, 0xff, 0x73, 0x7f, 0x98, 0xff, 0x40, 0x3d, 0x4a, 0xff, 0x56, 0x5b, 0x6e, 0xff, 0x70, 0x7c, 0x99, 0xff, 0x77, 0x83, 0xaa, 0xff, 0x75, 0x85, 0xad, 0xff, 0x6c, 0x7c, 0xa4, 0xff, 0x64, 0x74, 0xa0, 0xff, 0x5e, 0x73, 0xa0, 0xff, 0x5e, 0x72, 0xa0, 0xff, 0x5a, 0x6f, 0x9b, 0xff, 0x5e, 0x73, 0x9d, 0xff, 0x65, 0x7a, 0xa4, 0xff, 0x6c, 0x7b, 0x9f, 0xff, 0x6d, 0x80, 0xa3, 0xff, 0x67, 0x80, 0xa9, 0xff, 0x65, 0x7c, 0xab, 0xff, 0x66, 0x7d, 0xab, 0xff, 0x65, 0x7d, 0xaa, 0xff, 0x64, 0x7b, 0xa9, 0xff, 0x65, 0x7c, 0xaa, 0xff, 0x6d, 0x84, 0xb1, 0xff, 0x6c, 0x7f, 0xad, 0xff, 0x58, 0x6a, 0x99, 0xff, 0x4b, 0x5f, 0x8d, 0xff, 0x44, 0x53, 0x83, 0xff, 0x3d, 0x4b, 0x78, 0xff, 0x38, 0x4a, 0x73, 0xff, 0x3c, 0x4c, 0x77, 0xff, 0x3c, 0x4d, 0x7e, 0xff, 0x3f, 0x4f, 0x84, 0xff, 0x61, 0x6f, 0xa4, 0xff, 0x8c, 0x9d, 0xcc, 0xff, 0xa9, 0xbe, 0xde, 0xff, 0xa9, 0xb2, 0xcb, 0xff, 0x4b, 0x54, 0x64, 0xff, 0x16, 0x16, 0x15, 0xff, 0x19, 0x0d, 0x0b, 0xff, 0x1f, 0x14, 0x14, 0xff, 0x23, 0x1b, 0x19, 0xff, 0x1b, 0x18, 0x1b, 0xff, 0x1e, 0x18, 0x1f, 0xff, 0x24, 0x23, 0x2b, 0xff, 0x2a, 0x2f, 0x3a, 0xff, 0x3f, 0x46, 0x54, 0xff, 0x40, 0x45, 0x58, 0xff, 0x47, 0x4e, 0x67, 0xff, 0x5c, 0x6a, 0x88, 0xff, 0x5e, 0x73, 0x9a, 0xff, 0x77, 0x92, 0xb7, 0xff, 0x96, 0xa9, 0xc7, 0xff, 0xb1, 0xc0, 0xdb, 0xff, 0xa6, 0xb7, 0xce, 0xff, 0xb7, 0xc4, 0xd2, 0xff, 0xdd, 0xdf, 0xe1, 0xff, 0xf9, 0xf3, 0xec, 0xff, 0xff, 0xfc, 0xf5, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xfe, 0xf8, 0xff, 0xf1, 0xed, 0xe7, 0xff, 0xea, 0xe6, 0xe2, 0xff, 0xff, 0xfc, 0xf9, 0xff, 0xfe, 0xfa, 0xf7, 0xff, 0xfd, 0xf9, 0xf6, 0xff, 0xfd, 0xf8, 0xf5, 0xff, 0xfd, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xfa, 0xf6, 0xf3, 0x9f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x24, 0x24, 0x07, 0x36, 0x3e, 0x47, 0xef, 0x79, 0x8d, 0x9e, 0xff, 0xa8, 0xbb, 0xce, 0xff, 0x9f, 0xae, 0xbf, 0xff, 0x91, 0xa2, 0xb3, 0xff, 0x89, 0x9a, 0xac, 0xff, 0x7e, 0x8c, 0x9b, 0xff, 0x79, 0x86, 0x94, 0xff, 0x70, 0x7f, 0x90, 0xff, 0x60, 0x73, 0x86, 0xff, 0x53, 0x65, 0x77, 0xff, 0x4c, 0x5b, 0x6b, 0xff, 0x46, 0x58, 0x69, 0xff, 0x3b, 0x49, 0x55, 0xff, 0x59, 0x5b, 0x5f, 0xff, 0x8d, 0x8e, 0x89, 0xff, 0x92, 0x93, 0x93, 0xff, 0x86, 0x8f, 0x99, 0xff, 0x83, 0x8f, 0xa1, 0xff, 0x8b, 0x91, 0x9a, 0xff, 0x8e, 0x95, 0x96, 0xff, 0x91, 0x9e, 0xa5, 0xff, 0x79, 0x89, 0x99, 0xff, 0xa8, 0xb1, 0xba, 0xff, 0xcf, 0xd6, 0xd5, 0xff, 0x51, 0x56, 0x5f, 0xff, 0x20, 0x1e, 0x2f, 0xff, 0x1d, 0x1a, 0x24, 0xff, 0x11, 0x11, 0x18, 0xff, 0x11, 0x10, 0x11, 0xff, 0x0f, 0x0f, 0x11, 0xff, 0x11, 0x10, 0x12, 0xff, 0x1d, 0x1b, 0x1b, 0xff, 0x0d, 0x0c, 0x0c, 0xff, 0x0c, 0x07, 0x07, 0xff, 0x1e, 0x16, 0x18, 0xff, 0x2e, 0x2f, 0x3f, 0xff, 0x55, 0x60, 0x7f, 0xff, 0x8c, 0xa0, 0xc8, 0xff, 0x7d, 0x8d, 0xb0, 0xff, 0x46, 0x4a, 0x5e, 0xff, 0x66, 0x69, 0x83, 0xff, 0x85, 0x92, 0xbf, 0xff, 0x81, 0x94, 0xc5, 0xff, 0x78, 0x8f, 0xbe, 0xff, 0x71, 0x89, 0xb6, 0xff, 0x65, 0x7c, 0xab, 0xff, 0x5d, 0x71, 0xa4, 0xff, 0x58, 0x6b, 0x9c, 0xff, 0x54, 0x66, 0x98, 0xff, 0x56, 0x68, 0x9b, 0xff, 0x5a, 0x6c, 0xa0, 0xff, 0x65, 0x76, 0xa9, 0xff, 0x70, 0x7f, 0xaf, 0xff, 0x7c, 0x8c, 0xb4, 0xff, 0x78, 0x8c, 0xb2, 0xff, 0x68, 0x82, 0xac, 0xff, 0x69, 0x82, 0xad, 0xff, 0x6d, 0x86, 0xaf, 0xff, 0x70, 0x89, 0xb3, 0xff, 0x75, 0x8e, 0xb8, 0xff, 0x69, 0x7f, 0xac, 0xff, 0x53, 0x65, 0x95, 0xff, 0x43, 0x54, 0x83, 0xff, 0x37, 0x48, 0x73, 0xff, 0x2a, 0x35, 0x65, 0xff, 0x35, 0x40, 0x6c, 0xff, 0x43, 0x52, 0x78, 0xff, 0x5a, 0x68, 0x91, 0xff, 0x77, 0x84, 0xad, 0xff, 0x75, 0x81, 0xa8, 0xff, 0x57, 0x5e, 0x8a, 0xff, 0x5c, 0x64, 0x98, 0xff, 0x89, 0x9d, 0xc3, 0xff, 0xad, 0xbc, 0xe2, 0xff, 0x78, 0x85, 0xa0, 0xff, 0x1a, 0x17, 0x18, 0xff, 0x12, 0x06, 0x03, 0xff, 0x18, 0x0d, 0x0d, 0xff, 0x1f, 0x16, 0x16, 0xff, 0x18, 0x17, 0x15, 0xff, 0x19, 0x16, 0x17, 0xff, 0x30, 0x2d, 0x35, 0xff, 0x3d, 0x3d, 0x4a, 0xff, 0x34, 0x36, 0x44, 0xff, 0x44, 0x47, 0x5b, 0xff, 0x44, 0x4d, 0x66, 0xff, 0x4f, 0x5d, 0x7c, 0xff, 0x5c, 0x6d, 0x95, 0xff, 0x7a, 0x8f, 0xb3, 0xff, 0x9b, 0xab, 0xca, 0xff, 0xb0, 0xbc, 0xd3, 0xff, 0xd1, 0xd8, 0xe1, 0xff, 0xd8, 0xd9, 0xdb, 0xff, 0xe4, 0xe2, 0xe0, 0xff, 0xfa, 0xf9, 0xf4, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf6, 0xf1, 0xff, 0xfa, 0xf5, 0xf1, 0xff, 0xf1, 0xee, 0xeb, 0xff, 0xe8, 0xe5, 0xe1, 0xff, 0xf7, 0xf4, 0xf0, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xf9, 0xf7, 0xf3, 0xef, 0xfe, 0xfe, 0xfe, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3d, 0x41, 0x45, 0x46, 0x7a, 0x7d, 0x80, 0xff, 0x89, 0x98, 0xa4, 0xff, 0xa0, 0xb1, 0xc2, 0xff, 0x9f, 0xae, 0xbf, 0xff, 0x92, 0xa2, 0xb3, 0xff, 0x89, 0x9a, 0xab, 0xff, 0x80, 0x8e, 0x9e, 0xff, 0x7b, 0x88, 0x96, 0xff, 0x70, 0x7f, 0x8e, 0xff, 0x60, 0x72, 0x84, 0xff, 0x57, 0x67, 0x7c, 0xff, 0x51, 0x5f, 0x73, 0xff, 0x41, 0x52, 0x64, 0xff, 0x32, 0x41, 0x4f, 0xff, 0x54, 0x5a, 0x5e, 0xff, 0x87, 0x88, 0x83, 0xff, 0x95, 0x94, 0x91, 0xff, 0x85, 0x8d, 0x9e, 0xff, 0x81, 0x8e, 0xa0, 0xff, 0x91, 0x95, 0x96, 0xff, 0x92, 0x98, 0x9b, 0xff, 0x96, 0xa5, 0xac, 0xff, 0x76, 0x84, 0x96, 0xff, 0x88, 0x91, 0xa1, 0xff, 0x90, 0x99, 0x9f, 0xff, 0x2f, 0x39, 0x44, 0xff, 0x19, 0x1c, 0x29, 0xff, 0x12, 0x10, 0x19, 0xff, 0x0a, 0x09, 0x0f, 0xff, 0x11, 0x0e, 0x14, 0xff, 0x13, 0x10, 0x13, 0xff, 0x14, 0x11, 0x10, 0xff, 0x11, 0x0e, 0x0d, 0xff, 0x0d, 0x0a, 0x0a, 0xff, 0x0a, 0x06, 0x02, 0xff, 0x13, 0x0c, 0x0a, 0xff, 0x26, 0x27, 0x3b, 0xff, 0x5c, 0x6b, 0x8e, 0xff, 0x91, 0xa4, 0xc8, 0xff, 0x52, 0x60, 0x7f, 0xff, 0x5c, 0x65, 0x82, 0xff, 0x93, 0xa0, 0xca, 0xff, 0x86, 0x96, 0xc7, 0xff, 0x7e, 0x8c, 0xbf, 0xff, 0x72, 0x84, 0xb4, 0xff, 0x60, 0x73, 0x9d, 0xff, 0x4c, 0x60, 0x8e, 0xff, 0x40, 0x54, 0x84, 0xff, 0x3f, 0x4f, 0x7d, 0xff, 0x45, 0x54, 0x7f, 0xff, 0x50, 0x5b, 0x88, 0xff, 0x4e, 0x59, 0x89, 0xff, 0x50, 0x60, 0x8e, 0xff, 0x63, 0x73, 0xa2, 0xff, 0x78, 0x88, 0xb4, 0xff, 0x7f, 0x93, 0xba, 0xff, 0x75, 0x8d, 0xb3, 0xff, 0x71, 0x88, 0xae, 0xff, 0x74, 0x8a, 0xb3, 0xff, 0x83, 0x9b, 0xc1, 0xff, 0x85, 0x9e, 0xc2, 0xff, 0x5e, 0x74, 0x9e, 0xff, 0x3e, 0x50, 0x7a, 0xff, 0x32, 0x40, 0x68, 0xff, 0x2b, 0x39, 0x5b, 0xff, 0x39, 0x46, 0x63, 0xff, 0x45, 0x4e, 0x66, 0xff, 0x3a, 0x3e, 0x54, 0xff, 0x32, 0x36, 0x49, 0xff, 0x4b, 0x50, 0x5e, 0xff, 0x76, 0x7d, 0x8e, 0xff, 0xa8, 0xb0, 0xc7, 0xff, 0x69, 0x75, 0x99, 0xff, 0x56, 0x6a, 0x99, 0xff, 0x8d, 0xa1, 0xcf, 0xff, 0x89, 0x9a, 0xb8, 0xff, 0x2f, 0x2a, 0x31, 0xff, 0x10, 0x02, 0x00, 0xff, 0x18, 0x0d, 0x0d, 0xff, 0x1f, 0x16, 0x16, 0xff, 0x1c, 0x19, 0x18, 0xff, 0x21, 0x1d, 0x1c, 0xff, 0x27, 0x22, 0x26, 0xff, 0x42, 0x40, 0x4b, 0xff, 0x44, 0x45, 0x54, 0xff, 0x3b, 0x40, 0x51, 0xff, 0x4d, 0x54, 0x6d, 0xff, 0x50, 0x5b, 0x7a, 0xff, 0x62, 0x73, 0x99, 0xff, 0x5b, 0x6f, 0x95, 0xff, 0x78, 0x8c, 0xaf, 0xff, 0x7d, 0x95, 0xbb, 0xff, 0xad, 0xc1, 0xd0, 0xff, 0xec, 0xec, 0xea, 0xff, 0xe4, 0xde, 0xdf, 0xff, 0xea, 0xe6, 0xe5, 0xff, 0xf9, 0xf7, 0xf6, 0xff, 0xf5, 0xf4, 0xf3, 0xff, 0xef, 0xed, 0xea, 0xff, 0xea, 0xe7, 0xe3, 0xff, 0xf3, 0xf0, 0xec, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf7, 0xf4, 0xff, 0xfb, 0xf7, 0xf2, 0xff, 0xfc, 0xf9, 0xf4, 0xff, 0xfc, 0xf8, 0xf3, 0xff, 0xfc, 0xf8, 0xf3, 0xff, 0xfc, 0xf8, 0xf3, 0xff, 0xfa, 0xf8, 0xf3, 0xff, 0xfa, 0xf8, 0xf4, 0xff, 0xfb, 0xfb, 0xf4, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x67, 0x73, 0x7c, 0x8d, 0x8f, 0x9a, 0xa3, 0xff, 0x8d, 0x9c, 0xac, 0xff, 0x9c, 0xab, 0xbc, 0xff, 0x9f, 0xad, 0xbd, 0xff, 0x95, 0xa4, 0xb4, 0xff, 0x8e, 0x9f, 0xaf, 0xff, 0x84, 0x92, 0xa3, 0xff, 0x77, 0x83, 0x92, 0xff, 0x70, 0x7d, 0x8a, 0xff, 0x68, 0x77, 0x87, 0xff, 0x5c, 0x69, 0x81, 0xff, 0x52, 0x5e, 0x76, 0xff, 0x48, 0x58, 0x6a, 0xff, 0x34, 0x44, 0x54, 0xff, 0x4e, 0x58, 0x5b, 0xff, 0x88, 0x8b, 0x88, 0xff, 0x90, 0x8f, 0x8c, 0xff, 0x86, 0x8b, 0x99, 0xff, 0x7f, 0x8c, 0x9b, 0xff, 0x90, 0x96, 0x9b, 0xff, 0x92, 0x9c, 0xa4, 0xff, 0x93, 0xa3, 0xae, 0xff, 0x7c, 0x88, 0x9f, 0xff, 0x6a, 0x75, 0x8a, 0xff, 0x6d, 0x74, 0x83, 0xff, 0x2c, 0x34, 0x43, 0xff, 0x27, 0x2e, 0x3b, 0xff, 0x20, 0x23, 0x2d, 0xff, 0x16, 0x17, 0x1e, 0xff, 0x15, 0x13, 0x1d, 0xff, 0x11, 0x0d, 0x0f, 0xff, 0x10, 0x0b, 0x07, 0xff, 0x10, 0x0b, 0x0a, 0xff, 0x0d, 0x08, 0x08, 0xff, 0x0b, 0x06, 0x00, 0xff, 0x0d, 0x09, 0x05, 0xff, 0x25, 0x2c, 0x43, 0xff, 0x6b, 0x7e, 0xa6, 0xff, 0x7e, 0x8e, 0xae, 0xff, 0x5d, 0x6a, 0x88, 0xff, 0x87, 0x95, 0xbf, 0xff, 0x8b, 0x9a, 0xcb, 0xff, 0x7e, 0x8f, 0xbc, 0xff, 0x6b, 0x7a, 0xab, 0xff, 0x55, 0x65, 0x94, 0xff, 0x58, 0x68, 0x94, 0xff, 0x58, 0x66, 0x93, 0xff, 0x45, 0x54, 0x7d, 0xff, 0x35, 0x44, 0x68, 0xff, 0x2c, 0x3c, 0x61, 0xff, 0x34, 0x43, 0x69, 0xff, 0x3e, 0x4b, 0x74, 0xff, 0x41, 0x51, 0x78, 0xff, 0x55, 0x67, 0x90, 0xff, 0x68, 0x7f, 0xaa, 0xff, 0x78, 0x90, 0xba, 0xff, 0x81, 0x95, 0xba, 0xff, 0x79, 0x8f, 0xb3, 0xff, 0x80, 0x92, 0xbc, 0xff, 0x94, 0xa7, 0xca, 0xff, 0x89, 0xa0, 0xbe, 0xff, 0x5b, 0x6b, 0x95, 0xff, 0x41, 0x4a, 0x72, 0xff, 0x30, 0x33, 0x5b, 0xff, 0x34, 0x32, 0x57, 0xff, 0x36, 0x3b, 0x49, 0xff, 0x1e, 0x20, 0x1d, 0xff, 0x1a, 0x13, 0x0b, 0xff, 0x11, 0x08, 0x00, 0xff, 0x14, 0x0b, 0x07, 0xff, 0x31, 0x28, 0x2c, 0xff, 0x4e, 0x49, 0x4e, 0xff, 0x73, 0x76, 0x84, 0xff, 0x4c, 0x52, 0x79, 0xff, 0x69, 0x78, 0xa4, 0xff, 0x8c, 0xa2, 0xc6, 0xff, 0x41, 0x44, 0x52, 0xff, 0x0e, 0x01, 0x00, 0xff, 0x1b, 0x10, 0x0f, 0xff, 0x22, 0x18, 0x19, 0xff, 0x1c, 0x17, 0x18, 0xff, 0x24, 0x1e, 0x1e, 0xff, 0x25, 0x20, 0x22, 0xff, 0x28, 0x27, 0x30, 0xff, 0x4e, 0x53, 0x61, 0xff, 0x4b, 0x53, 0x65, 0xff, 0x44, 0x4a, 0x61, 0xff, 0x4f, 0x57, 0x73, 0xff, 0x60, 0x71, 0x94, 0xff, 0x67, 0x7a, 0xa0, 0xff, 0x71, 0x8b, 0xa8, 0xff, 0x62, 0x84, 0xab, 0xff, 0x75, 0x8f, 0xb3, 0xff, 0xc2, 0xce, 0xdb, 0xff, 0xe0, 0xdf, 0xe2, 0xff, 0xf1, 0xe4, 0xe2, 0xff, 0xf6, 0xf2, 0xed, 0xff, 0xf6, 0xf4, 0xef, 0xff, 0xf9, 0xf5, 0xf1, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xfe, 0xfb, 0xf7, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfc, 0xf8, 0xf4, 0xff, 0xfa, 0xf7, 0xf3, 0xff, 0xf8, 0xf6, 0xf4, 0xff, 0xfa, 0xf7, 0xf5, 0xff, 0xfb, 0xf9, 0xf6, 0xff, 0xfb, 0xf9, 0xf6, 0xff, 0xfb, 0xf9, 0xf6, 0xff, 0xfb, 0xfa, 0xf8, 0xff, 0xfb, 0xfa, 0xf9, 0xff, 0xfb, 0xfb, 0xf9, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0x75, 0x8b, 0xd7, 0x7c, 0x8b, 0x99, 0xff, 0x89, 0x98, 0xa6, 0xff, 0x9b, 0xaa, 0xba, 0xff, 0x9e, 0xad, 0xbd, 0xff, 0x95, 0xa5, 0xb5, 0xff, 0x91, 0xa2, 0xb2, 0xff, 0x88, 0x96, 0xa7, 0xff, 0x78, 0x84, 0x93, 0xff, 0x6f, 0x7d, 0x8b, 0xff, 0x68, 0x78, 0x89, 0xff, 0x5a, 0x69, 0x80, 0xff, 0x58, 0x66, 0x7c, 0xff, 0x57, 0x67, 0x79, 0xff, 0x36, 0x45, 0x54, 0xff, 0x3f, 0x48, 0x49, 0xff, 0x86, 0x87, 0x87, 0xff, 0x9a, 0x96, 0x9a, 0xff, 0x86, 0x8d, 0x95, 0xff, 0x7d, 0x8d, 0x9a, 0xff, 0x8a, 0x93, 0x9d, 0xff, 0x95, 0xa1, 0xab, 0xff, 0x86, 0x95, 0xa3, 0xff, 0x7f, 0x89, 0xa4, 0xff, 0x6e, 0x77, 0x8f, 0xff, 0x48, 0x4e, 0x5e, 0xff, 0x22, 0x28, 0x3a, 0xff, 0x27, 0x2f, 0x40, 0xff, 0x31, 0x37, 0x45, 0xff, 0x36, 0x39, 0x44, 0xff, 0x23, 0x25, 0x2d, 0xff, 0x0e, 0x11, 0x11, 0xff, 0x0b, 0x0b, 0x06, 0xff, 0x0f, 0x0a, 0x09, 0xff, 0x0d, 0x0a, 0x08, 0xff, 0x0f, 0x07, 0x04, 0xff, 0x0b, 0x06, 0x08, 0xff, 0x28, 0x37, 0x50, 0xff, 0x71, 0x86, 0xab, 0xff, 0x74, 0x82, 0xa3, 0xff, 0x7d, 0x8c, 0xab, 0xff, 0x8a, 0xa1, 0xca, 0xff, 0x77, 0x8f, 0xc1, 0xff, 0x5d, 0x71, 0xa5, 0xff, 0x5c, 0x6c, 0xa0, 0xff, 0x70, 0x85, 0xac, 0xff, 0x53, 0x69, 0x88, 0xff, 0x38, 0x46, 0x61, 0xff, 0x31, 0x38, 0x4b, 0xff, 0x32, 0x38, 0x4d, 0xff, 0x30, 0x3d, 0x5b, 0xff, 0x32, 0x44, 0x66, 0xff, 0x40, 0x4e, 0x6f, 0xff, 0x49, 0x51, 0x73, 0xff, 0x5b, 0x64, 0x8f, 0xff, 0x6d, 0x7b, 0xa8, 0xff, 0x78, 0x8a, 0xb4, 0xff, 0x82, 0x97, 0xbd, 0xff, 0x7f, 0x95, 0xbb, 0xff, 0x87, 0x99, 0xc0, 0xff, 0xa3, 0xb4, 0xd2, 0xff, 0x99, 0xad, 0xca, 0xff, 0x61, 0x75, 0x9b, 0xff, 0x4c, 0x5b, 0x80, 0xff, 0x31, 0x39, 0x5a, 0xff, 0x1a, 0x1a, 0x39, 0xff, 0x31, 0x30, 0x49, 0xff, 0x3c, 0x3d, 0x4d, 0xff, 0x3a, 0x3a, 0x45, 0xff, 0x33, 0x30, 0x3a, 0xff, 0x38, 0x37, 0x41, 0xff, 0x44, 0x42, 0x4b, 0xff, 0x2e, 0x26, 0x2e, 0xff, 0x37, 0x33, 0x36, 0xff, 0x4a, 0x4d, 0x59, 0xff, 0x57, 0x63, 0x87, 0xff, 0x7d, 0x93, 0xc8, 0xff, 0x51, 0x5d, 0x73, 0xff, 0x11, 0x09, 0x02, 0xff, 0x18, 0x0b, 0x0b, 0xff, 0x21, 0x18, 0x18, 0xff, 0x20, 0x1b, 0x1c, 0xff, 0x27, 0x22, 0x21, 0xff, 0x2b, 0x26, 0x29, 0xff, 0x28, 0x27, 0x30, 0xff, 0x35, 0x39, 0x47, 0xff, 0x5c, 0x60, 0x79, 0xff, 0x4d, 0x55, 0x6d, 0xff, 0x46, 0x52, 0x66, 0xff, 0x56, 0x63, 0x82, 0xff, 0x64, 0x74, 0x95, 0xff, 0x6e, 0x87, 0xa5, 0xff, 0x81, 0x96, 0xb7, 0xff, 0xa5, 0xb3, 0xd2, 0xff, 0x8f, 0x9f, 0xbb, 0xff, 0xb3, 0xc1, 0xd0, 0xff, 0xd9, 0xdd, 0xdd, 0xff, 0xf8, 0xf2, 0xec, 0xff, 0xfa, 0xf6, 0xf0, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xfa, 0xf6, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfd, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfb, 0xf8, 0xf3, 0xff, 0xfb, 0xf8, 0xf3, 0xff, 0xfb, 0xf8, 0xf3, 0xff, 0xfc, 0xf8, 0xf2, 0xff, 0xfa, 0xf8, 0xf5, 0xff, 0xf8, 0xf8, 0xf9, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xfa, 0xfa, 0xfb, 0xff, 0xfa, 0xfa, 0xfb, 0xff, 0xfa, 0xfa, 0xfb, 0xff, 0xfe, 0xfb, 0xfc, 0xff, 0xff, 0xfb, 0xfd, 0xff, 0xfb, 0xfc, 0xfc, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x75, 0x89, 0xa6, 0x1a, 0x5f, 0x72, 0x8c, 0xff, 0x65, 0x73, 0x87, 0xff, 0x7d, 0x8a, 0x9f, 0xff, 0x9c, 0xaa, 0xbb, 0xff, 0xa0, 0xaf, 0xbf, 0xff, 0x95, 0xa5, 0xb5, 0xff, 0x91, 0xa1, 0xb1, 0xff, 0x89, 0x95, 0xa5, 0xff, 0x7e, 0x89, 0x97, 0xff, 0x72, 0x80, 0x8d, 0xff, 0x65, 0x75, 0x86, 0xff, 0x5c, 0x6d, 0x80, 0xff, 0x5a, 0x6c, 0x7c, 0xff, 0x4f, 0x62, 0x75, 0xff, 0x3e, 0x4b, 0x5c, 0xff, 0x47, 0x4b, 0x51, 0xff, 0x7d, 0x82, 0x88, 0xff, 0x96, 0x93, 0x9b, 0xff, 0x82, 0x87, 0x91, 0xff, 0x7e, 0x8c, 0x95, 0xff, 0x8c, 0x98, 0x9f, 0xff, 0x97, 0xa4, 0xac, 0xff, 0x89, 0x97, 0xa7, 0xff, 0x76, 0x84, 0x98, 0xff, 0x4b, 0x56, 0x6d, 0xff, 0x2e, 0x35, 0x4b, 0xff, 0x26, 0x2c, 0x3f, 0xff, 0x2a, 0x2e, 0x3f, 0xff, 0x22, 0x25, 0x37, 0xff, 0x2c, 0x2e, 0x40, 0xff, 0x30, 0x34, 0x42, 0xff, 0x18, 0x1f, 0x22, 0xff, 0x0f, 0x0d, 0x0a, 0xff, 0x0e, 0x0a, 0x08, 0xff, 0x0e, 0x0b, 0x0a, 0xff, 0x0d, 0x06, 0x01, 0xff, 0x13, 0x0e, 0x10, 0xff, 0x35, 0x43, 0x60, 0xff, 0x67, 0x7d, 0xa2, 0xff, 0x7c, 0x8e, 0xb1, 0xff, 0x88, 0x99, 0xbf, 0xff, 0x7d, 0x90, 0xbc, 0xff, 0x61, 0x77, 0xa6, 0xff, 0x5e, 0x72, 0xa5, 0xff, 0x62, 0x70, 0x92, 0xff, 0x40, 0x44, 0x53, 0xff, 0x1e, 0x21, 0x2b, 0xff, 0x0e, 0x0e, 0x12, 0xff, 0x05, 0x02, 0x00, 0xff, 0x0f, 0x0e, 0x13, 0xff, 0x39, 0x38, 0x4b, 0xff, 0x32, 0x31, 0x4d, 0xff, 0x30, 0x36, 0x53, 0xff, 0x47, 0x57, 0x75, 0xff, 0x5d, 0x6c, 0x91, 0xff, 0x70, 0x7e, 0xa7, 0xff, 0x77, 0x88, 0xb2, 0xff, 0x7f, 0x92, 0xb9, 0xff, 0x85, 0x97, 0xbe, 0xff, 0x89, 0x9d, 0xc3, 0xff, 0xa7, 0xb5, 0xd7, 0xff, 0xab, 0xb9, 0xd7, 0xff, 0x71, 0x89, 0xad, 0xff, 0x53, 0x68, 0x8d, 0xff, 0x4d, 0x5d, 0x7f, 0xff, 0x42, 0x51, 0x74, 0xff, 0x37, 0x44, 0x6b, 0xff, 0x41, 0x4d, 0x76, 0xff, 0x4d, 0x58, 0x81, 0xff, 0x4b, 0x57, 0x7f, 0xff, 0x51, 0x5e, 0x7f, 0xff, 0x55, 0x62, 0x80, 0xff, 0x6a, 0x74, 0x93, 0xff, 0x7e, 0x89, 0xa6, 0xff, 0x81, 0x92, 0xad, 0xff, 0x65, 0x76, 0x9b, 0xff, 0x63, 0x7b, 0xb0, 0xff, 0x61, 0x6f, 0x90, 0xff, 0x1f, 0x17, 0x1b, 0xff, 0x19, 0x0e, 0x0a, 0xff, 0x21, 0x19, 0x16, 0xff, 0x21, 0x1b, 0x1c, 0xff, 0x28, 0x24, 0x2a, 0xff, 0x2c, 0x26, 0x28, 0xff, 0x22, 0x20, 0x25, 0xff, 0x2c, 0x2c, 0x38, 0xff, 0x4c, 0x4f, 0x64, 0xff, 0x54, 0x5c, 0x72, 0xff, 0x40, 0x4e, 0x63, 0xff, 0x4b, 0x59, 0x75, 0xff, 0x55, 0x65, 0x84, 0xff, 0x59, 0x6f, 0x93, 0xff, 0x5d, 0x71, 0x92, 0xff, 0x9c, 0xa7, 0xbe, 0xff, 0xa8, 0xb9, 0xd1, 0xff, 0x99, 0xac, 0xc5, 0xff, 0xa7, 0xb5, 0xca, 0xff, 0xd9, 0xd8, 0xde, 0xff, 0xf2, 0xf1, 0xec, 0xff, 0xf8, 0xf8, 0xf4, 0xff, 0xf6, 0xf3, 0xef, 0xff, 0xed, 0xea, 0xe6, 0xff, 0xf0, 0xed, 0xe9, 0xff, 0xf9, 0xf6, 0xf2, 0xff, 0xfb, 0xf8, 0xf4, 0xff, 0xfc, 0xf8, 0xf4, 0xff, 0xfd, 0xf9, 0xf4, 0xff, 0xfb, 0xf8, 0xf3, 0xff, 0xfc, 0xf8, 0xf4, 0xff, 0xfc, 0xf8, 0xf4, 0xff, 0xfc, 0xf9, 0xf5, 0xff, 0xfc, 0xf9, 0xf6, 0xff, 0xfc, 0xf9, 0xf6, 0xff, 0xfc, 0xf9, 0xf7, 0xff, 0xfc, 0xf8, 0xf6, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xfd, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfd, 0xfe, 0xff, 0xfb, 0xfe, 0xfb, 0xff, 0xff, 0xff, 0xf5, 0x1b, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x71, 0x8d, 0xaa, 0x58, 0x66, 0x7a, 0x94, 0xff, 0x53, 0x62, 0x7c, 0xff, 0x69, 0x79, 0x95, 0xff, 0x9b, 0xaa, 0xbd, 0xff, 0xa3, 0xb2, 0xc0, 0xff, 0x97, 0xa6, 0xb6, 0xff, 0x92, 0xa2, 0xb1, 0xff, 0x89, 0x94, 0xa6, 0xff, 0x80, 0x89, 0x98, 0xff, 0x74, 0x82, 0x8f, 0xff, 0x66, 0x76, 0x87, 0xff, 0x5f, 0x71, 0x83, 0xff, 0x61, 0x73, 0x85, 0xff, 0x4d, 0x5e, 0x71, 0xff, 0x40, 0x52, 0x63, 0xff, 0x4d, 0x5b, 0x67, 0xff, 0x77, 0x80, 0x86, 0xff, 0x85, 0x84, 0x8c, 0xff, 0x7f, 0x85, 0x8f, 0xff, 0x84, 0x95, 0x9e, 0xff, 0x8d, 0x9c, 0xa6, 0xff, 0x93, 0xa2, 0xac, 0xff, 0x74, 0x83, 0x92, 0xff, 0x4d, 0x5d, 0x6e, 0xff, 0x32, 0x3f, 0x54, 0xff, 0x2a, 0x33, 0x47, 0xff, 0x30, 0x37, 0x45, 0xff, 0x30, 0x32, 0x42, 0xff, 0x20, 0x21, 0x33, 0xff, 0x1e, 0x1f, 0x32, 0xff, 0x24, 0x27, 0x39, 0xff, 0x1c, 0x22, 0x2a, 0xff, 0x16, 0x11, 0x11, 0xff, 0x0d, 0x08, 0x06, 0xff, 0x07, 0x05, 0x04, 0xff, 0x06, 0x00, 0x00, 0xff, 0x1d, 0x17, 0x19, 0xff, 0x45, 0x50, 0x6e, 0xff, 0x66, 0x7d, 0xa5, 0xff, 0x7c, 0x92, 0xbb, 0xff, 0x82, 0x95, 0xc0, 0xff, 0x74, 0x81, 0xa9, 0xff, 0x4b, 0x57, 0x7c, 0xff, 0x3e, 0x43, 0x5f, 0xff, 0x1b, 0x1c, 0x21, 0xff, 0x0d, 0x0a, 0x09, 0xff, 0x31, 0x29, 0x31, 0xff, 0x38, 0x2f, 0x3a, 0xff, 0x39, 0x34, 0x3f, 0xff, 0x3d, 0x3e, 0x50, 0xff, 0x43, 0x43, 0x5d, 0xff, 0x36, 0x3a, 0x57, 0xff, 0x37, 0x44, 0x60, 0xff, 0x4a, 0x5f, 0x7e, 0xff, 0x62, 0x75, 0x9a, 0xff, 0x71, 0x85, 0xaf, 0xff, 0x6f, 0x86, 0xb2, 0xff, 0x76, 0x89, 0xb3, 0xff, 0x81, 0x93, 0xbe, 0xff, 0x89, 0x9f, 0xc6, 0xff, 0xa8, 0xb5, 0xd7, 0xff, 0xb3, 0xbf, 0xdf, 0xff, 0x91, 0xa6, 0xc6, 0xff, 0x6f, 0x82, 0xa9, 0xff, 0x5a, 0x6c, 0x95, 0xff, 0x56, 0x6b, 0x90, 0xff, 0x48, 0x5d, 0x80, 0xff, 0x35, 0x49, 0x73, 0xff, 0x37, 0x48, 0x79, 0xff, 0x3e, 0x50, 0x80, 0xff, 0x41, 0x54, 0x80, 0xff, 0x48, 0x5b, 0x86, 0xff, 0x4f, 0x63, 0x8e, 0xff, 0x65, 0x79, 0xa5, 0xff, 0x6d, 0x85, 0xb5, 0xff, 0x77, 0x8e, 0xbb, 0xff, 0x7f, 0x95, 0xc1, 0xff, 0x6d, 0x7e, 0xa7, 0xff, 0x26, 0x26, 0x38, 0xff, 0x17, 0x0e, 0x0c, 0xff, 0x1f, 0x15, 0x10, 0xff, 0x21, 0x19, 0x1b, 0xff, 0x2a, 0x27, 0x30, 0xff, 0x2a, 0x23, 0x24, 0xff, 0x1d, 0x17, 0x17, 0xff, 0x24, 0x1f, 0x28, 0xff, 0x35, 0x38, 0x46, 0xff, 0x54, 0x5d, 0x72, 0xff, 0x54, 0x61, 0x7d, 0xff, 0x46, 0x55, 0x73, 0xff, 0x4c, 0x5c, 0x7e, 0xff, 0x60, 0x77, 0x94, 0xff, 0x5f, 0x7a, 0x95, 0xff, 0x51, 0x62, 0x89, 0xff, 0x6a, 0x80, 0xa4, 0xff, 0x85, 0xa0, 0xc2, 0xff, 0x87, 0xa3, 0xc8, 0xff, 0xb4, 0xc4, 0xda, 0xff, 0xce, 0xd1, 0xd5, 0xff, 0xdd, 0xdd, 0xdb, 0xff, 0xf4, 0xf1, 0xeb, 0xff, 0xf1, 0xee, 0xeb, 0xff, 0xf5, 0xf2, 0xee, 0xff, 0xf9, 0xf6, 0xf1, 0xff, 0xf7, 0xf4, 0xf1, 0xff, 0xf7, 0xf5, 0xf4, 0xff, 0xf9, 0xf8, 0xf7, 0xff, 0xfa, 0xf8, 0xf7, 0xff, 0xfb, 0xf9, 0xf8, 0xff, 0xfb, 0xf9, 0xf8, 0xff, 0xfa, 0xfa, 0xfa, 0xff, 0xf9, 0xfa, 0xfb, 0xff, 0xf9, 0xfa, 0xfb, 0xff, 0xfb, 0xfb, 0xfc, 0xff, 0xfb, 0xfb, 0xfc, 0xff, 0xfb, 0xfb, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xfc, 0xff, 0xfc, 0x58, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x6d, 0x8a, 0xaa, 0x90, 0x6c, 0x86, 0x9f, 0xff, 0x4f, 0x63, 0x75, 0xff, 0x57, 0x6e, 0x82, 0xff, 0x96, 0xa9, 0xbc, 0xff, 0xa3, 0xb1, 0xc1, 0xff, 0x99, 0xa8, 0xb9, 0xff, 0x95, 0xa4, 0xb4, 0xff, 0x8b, 0x96, 0xa6, 0xff, 0x81, 0x8b, 0x99, 0xff, 0x76, 0x84, 0x93, 0xff, 0x67, 0x77, 0x89, 0xff, 0x63, 0x73, 0x8a, 0xff, 0x61, 0x71, 0x8a, 0xff, 0x4d, 0x5e, 0x75, 0xff, 0x43, 0x56, 0x6e, 0xff, 0x4b, 0x63, 0x79, 0xff, 0x76, 0x83, 0x89, 0xff, 0x80, 0x84, 0x88, 0xff, 0x83, 0x8e, 0x95, 0xff, 0x8d, 0x9f, 0xab, 0xff, 0x87, 0x97, 0xa7, 0xff, 0x67, 0x77, 0x85, 0xff, 0x51, 0x61, 0x71, 0xff, 0x45, 0x54, 0x66, 0xff, 0x36, 0x42, 0x56, 0xff, 0x2b, 0x34, 0x43, 0xff, 0x43, 0x4a, 0x55, 0xff, 0x31, 0x34, 0x43, 0xff, 0x20, 0x21, 0x35, 0xff, 0x1e, 0x20, 0x33, 0xff, 0x1e, 0x21, 0x36, 0xff, 0x22, 0x26, 0x32, 0xff, 0x1c, 0x18, 0x1b, 0xff, 0x10, 0x0a, 0x09, 0xff, 0x07, 0x02, 0x00, 0xff, 0x06, 0x00, 0x00, 0xff, 0x1f, 0x1a, 0x1c, 0xff, 0x46, 0x52, 0x70, 0xff, 0x6b, 0x84, 0xad, 0xff, 0x75, 0x8c, 0xb7, 0xff, 0x73, 0x89, 0xb7, 0xff, 0x61, 0x76, 0xa0, 0xff, 0x4f, 0x5d, 0x7e, 0xff, 0x3e, 0x43, 0x5e, 0xff, 0x3f, 0x47, 0x5a, 0xff, 0x4d, 0x58, 0x6f, 0xff, 0x53, 0x5a, 0x7a, 0xff, 0x53, 0x5d, 0x81, 0xff, 0x53, 0x5e, 0x88, 0xff, 0x47, 0x53, 0x7e, 0xff, 0x3b, 0x4a, 0x76, 0xff, 0x44, 0x55, 0x7e, 0xff, 0x52, 0x63, 0x8a, 0xff, 0x64, 0x74, 0xa0, 0xff, 0x72, 0x85, 0xb0, 0xff, 0x72, 0x88, 0xb5, 0xff, 0x6b, 0x82, 0xb0, 0xff, 0x71, 0x88, 0xb4, 0xff, 0x7a, 0x91, 0xc0, 0xff, 0x84, 0x9a, 0xc3, 0xff, 0xa4, 0xb2, 0xd5, 0xff, 0xb6, 0xc3, 0xe1, 0xff, 0xa4, 0xb5, 0xd3, 0xff, 0x8a, 0x9e, 0xc4, 0xff, 0x69, 0x7f, 0xab, 0xff, 0x5f, 0x74, 0x9c, 0xff, 0x64, 0x7c, 0xa0, 0xff, 0x5d, 0x71, 0x99, 0xff, 0x53, 0x62, 0x8e, 0xff, 0x4d, 0x5c, 0x88, 0xff, 0x4b, 0x5d, 0x87, 0xff, 0x4b, 0x5e, 0x88, 0xff, 0x57, 0x69, 0x93, 0xff, 0x72, 0x82, 0xad, 0xff, 0x7b, 0x8d, 0xbb, 0xff, 0x82, 0x97, 0xc6, 0xff, 0x93, 0xa6, 0xd0, 0xff, 0x82, 0x97, 0xc4, 0xff, 0x32, 0x3c, 0x56, 0xff, 0x12, 0x09, 0x08, 0xff, 0x1c, 0x12, 0x0a, 0xff, 0x1d, 0x16, 0x15, 0xff, 0x28, 0x25, 0x2d, 0xff, 0x2d, 0x28, 0x29, 0xff, 0x20, 0x1b, 0x18, 0xff, 0x1d, 0x15, 0x1a, 0xff, 0x2b, 0x2d, 0x38, 0xff, 0x45, 0x4d, 0x63, 0xff, 0x55, 0x60, 0x7e, 0xff, 0x55, 0x64, 0x80, 0xff, 0x48, 0x59, 0x78, 0xff, 0x58, 0x6c, 0x8e, 0xff, 0x6e, 0x85, 0xa8, 0xff, 0x75, 0x8e, 0xb2, 0xff, 0x69, 0x7c, 0xa0, 0xff, 0x6d, 0x83, 0xa6, 0xff, 0x78, 0x98, 0xbe, 0xff, 0x99, 0xb6, 0xd3, 0xff, 0xdf, 0xe3, 0xe7, 0xff, 0xf0, 0xe6, 0xe3, 0xff, 0xfc, 0xf5, 0xf1, 0xff, 0xff, 0xfc, 0xf6, 0xff, 0xfd, 0xfa, 0xf5, 0xff, 0xfb, 0xf7, 0xf3, 0xff, 0xf9, 0xf6, 0xf3, 0xff, 0xf7, 0xf7, 0xf8, 0xff, 0xfa, 0xfb, 0xfb, 0xff, 0xfb, 0xfb, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfa, 0xfb, 0xfc, 0xff, 0xf9, 0xfb, 0xfd, 0xff, 0xf9, 0xfb, 0xfd, 0xff, 0xfa, 0xfd, 0xfe, 0xff, 0xfa, 0xfd, 0xfe, 0xff, 0xfb, 0xfd, 0xfd, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xff, 0x90, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x6c, 0x89, 0xab, 0xc6, 0x72, 0x8e, 0xab, 0xff, 0x58, 0x69, 0x7a, 0xff, 0x4f, 0x62, 0x76, 0xff, 0x90, 0xa6, 0xbd, 0xff, 0xa4, 0xb5, 0xc6, 0xff, 0x9d, 0xac, 0xbc, 0xff, 0x97, 0xa6, 0xb6, 0xff, 0x8b, 0x98, 0xa4, 0xff, 0x84, 0x90, 0x9b, 0xff, 0x7b, 0x88, 0x99, 0xff, 0x6b, 0x7a, 0x8c, 0xff, 0x6a, 0x7a, 0x94, 0xff, 0x62, 0x76, 0x93, 0xff, 0x59, 0x70, 0x8b, 0xff, 0x54, 0x6c, 0x8c, 0xff, 0x52, 0x64, 0x81, 0xff, 0x70, 0x76, 0x83, 0xff, 0x86, 0x90, 0x91, 0xff, 0x8a, 0x97, 0x9c, 0xff, 0x7c, 0x88, 0x9a, 0xff, 0x5f, 0x6d, 0x80, 0xff, 0x53, 0x62, 0x74, 0xff, 0x4f, 0x5e, 0x72, 0xff, 0x4f, 0x5e, 0x6f, 0xff, 0x2f, 0x3a, 0x4b, 0xff, 0x35, 0x3e, 0x4b, 0xff, 0x4f, 0x54, 0x60, 0xff, 0x25, 0x28, 0x3c, 0xff, 0x20, 0x25, 0x39, 0xff, 0x1f, 0x24, 0x37, 0xff, 0x2b, 0x2f, 0x42, 0xff, 0x25, 0x26, 0x37, 0xff, 0x17, 0x17, 0x1f, 0xff, 0x13, 0x0d, 0x0e, 0xff, 0x0c, 0x03, 0x00, 0xff, 0x0b, 0x02, 0x00, 0xff, 0x19, 0x16, 0x1b, 0xff, 0x42, 0x50, 0x6f, 0xff, 0x6d, 0x89, 0xb1, 0xff, 0x71, 0x8b, 0xb4, 0xff, 0x67, 0x80, 0xad, 0xff, 0x5d, 0x76, 0xa3, 0xff, 0x6f, 0x80, 0xa8, 0xff, 0x77, 0x86, 0xae, 0xff, 0x6c, 0x7f, 0xa9, 0xff, 0x62, 0x73, 0x9e, 0xff, 0x5d, 0x6e, 0x99, 0xff, 0x53, 0x64, 0x8f, 0xff, 0x45, 0x57, 0x81, 0xff, 0x40, 0x54, 0x7c, 0xff, 0x49, 0x5d, 0x83, 0xff, 0x5d, 0x6f, 0x99, 0xff, 0x6c, 0x7c, 0xaa, 0xff, 0x76, 0x86, 0xb2, 0xff, 0x75, 0x8a, 0xb5, 0xff, 0x72, 0x87, 0xb5, 0xff, 0x6d, 0x81, 0xb2, 0xff, 0x72, 0x8a, 0xb7, 0xff, 0x7c, 0x94, 0xc3, 0xff, 0x86, 0x9d, 0xc7, 0xff, 0x9e, 0xaf, 0xd2, 0xff, 0xb2, 0xbe, 0xda, 0xff, 0xab, 0xb5, 0xd3, 0xff, 0x94, 0xa7, 0xc9, 0xff, 0x86, 0x9e, 0xbf, 0xff, 0x7a, 0x90, 0xb0, 0xff, 0x68, 0x81, 0xab, 0xff, 0x6a, 0x7f, 0xad, 0xff, 0x71, 0x83, 0xae, 0xff, 0x6d, 0x7f, 0xab, 0xff, 0x73, 0x84, 0xaf, 0xff, 0x7d, 0x8e, 0xb9, 0xff, 0x87, 0x98, 0xc2, 0xff, 0x95, 0xa8, 0xcf, 0xff, 0x9a, 0xac, 0xd3, 0xff, 0x9b, 0xad, 0xd4, 0xff, 0x97, 0xa8, 0xd1, 0xff, 0x91, 0xa4, 0xd4, 0xff, 0x47, 0x53, 0x6f, 0xff, 0x14, 0x0d, 0x07, 0xff, 0x1b, 0x13, 0x0a, 0xff, 0x1f, 0x1a, 0x15, 0xff, 0x25, 0x20, 0x24, 0xff, 0x25, 0x22, 0x28, 0xff, 0x2b, 0x28, 0x2a, 0xff, 0x1e, 0x17, 0x1d, 0xff, 0x44, 0x43, 0x52, 0xff, 0x42, 0x49, 0x5f, 0xff, 0x48, 0x53, 0x6e, 0xff, 0x53, 0x62, 0x7d, 0xff, 0x46, 0x5a, 0x75, 0xff, 0x51, 0x63, 0x87, 0xff, 0x5d, 0x6d, 0x95, 0xff, 0x7a, 0x8d, 0xae, 0xff, 0x9a, 0xaa, 0xc7, 0xff, 0x8f, 0x9e, 0xbc, 0xff, 0x82, 0x94, 0xb5, 0xff, 0x7e, 0x9d, 0xba, 0xff, 0x9b, 0xb4, 0xc8, 0xff, 0xca, 0xcb, 0xdd, 0xff, 0xf5, 0xeb, 0xf3, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xf8, 0xf7, 0xf5, 0xff, 0xfd, 0xfa, 0xf8, 0xff, 0xff, 0xfd, 0xfb, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfe, 0xff, 0xf9, 0xfc, 0xfd, 0xc4, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0xff, 0x01, 0x66, 0x83, 0xa3, 0xf2, 0x6f, 0x8d, 0xad, 0xff, 0x61, 0x75, 0x8c, 0xff, 0x4f, 0x5d, 0x6d, 0xff, 0x8c, 0x9e, 0xb3, 0xff, 0xab, 0xbc, 0xce, 0xff, 0x9f, 0xae, 0xbe, 0xff, 0x95, 0xa4, 0xb4, 0xff, 0x8c, 0x99, 0xa5, 0xff, 0x88, 0x93, 0x9f, 0xff, 0x7d, 0x8a, 0x9b, 0xff, 0x70, 0x7f, 0x91, 0xff, 0x6b, 0x7b, 0x94, 0xff, 0x6f, 0x82, 0x9e, 0xff, 0x64, 0x7c, 0x95, 0xff, 0x59, 0x74, 0x90, 0xff, 0x4f, 0x62, 0x79, 0xff, 0x6b, 0x6e, 0x78, 0xff, 0x88, 0x8e, 0x94, 0xff, 0x6d, 0x79, 0x81, 0xff, 0x5e, 0x69, 0x7f, 0xff, 0x6a, 0x76, 0x8f, 0xff, 0x59, 0x66, 0x7d, 0xff, 0x4c, 0x5c, 0x70, 0xff, 0x42, 0x50, 0x61, 0xff, 0x2a, 0x35, 0x47, 0xff, 0x4c, 0x54, 0x61, 0xff, 0x3d, 0x41, 0x4c, 0xff, 0x1f, 0x24, 0x37, 0xff, 0x1d, 0x27, 0x3a, 0xff, 0x1d, 0x26, 0x38, 0xff, 0x25, 0x2a, 0x3d, 0xff, 0x1f, 0x21, 0x31, 0xff, 0x16, 0x15, 0x1c, 0xff, 0x13, 0x0d, 0x0f, 0xff, 0x11, 0x08, 0x04, 0xff, 0x0b, 0x01, 0x00, 0xff, 0x10, 0x0e, 0x14, 0xff, 0x3d, 0x4b, 0x6b, 0xff, 0x70, 0x8b, 0xb2, 0xff, 0x71, 0x8b, 0xb4, 0xff, 0x71, 0x89, 0xb4, 0xff, 0x72, 0x8a, 0xb5, 0xff, 0x69, 0x80, 0xaf, 0xff, 0x5f, 0x71, 0xa2, 0xff, 0x5a, 0x6b, 0x9a, 0xff, 0x55, 0x65, 0x95, 0xff, 0x51, 0x63, 0x8f, 0xff, 0x51, 0x62, 0x8e, 0xff, 0x55, 0x68, 0x93, 0xff, 0x57, 0x6d, 0x95, 0xff, 0x64, 0x7a, 0xa2, 0xff, 0x74, 0x89, 0xb3, 0xff, 0x76, 0x8b, 0xb6, 0xff, 0x76, 0x8c, 0xb5, 0xff, 0x78, 0x8d, 0xb7, 0xff, 0x6e, 0x82, 0xb1, 0xff, 0x6f, 0x83, 0xb3, 0xff, 0x7b, 0x8e, 0xbb, 0xff, 0x81, 0x94, 0xc3, 0xff, 0x8a, 0x9f, 0xc9, 0xff, 0x98, 0xaa, 0xcc, 0xff, 0xb3, 0xbe, 0xd9, 0xff, 0xc9, 0xce, 0xe4, 0xff, 0xa9, 0xb5, 0xcf, 0xff, 0x9c, 0xb1, 0xcb, 0xff, 0x89, 0xa3, 0xbe, 0xff, 0x77, 0x90, 0xb7, 0xff, 0x71, 0x88, 0xb7, 0xff, 0x6f, 0x85, 0xb4, 0xff, 0x7a, 0x91, 0xc0, 0xff, 0x8c, 0x9e, 0xca, 0xff, 0x93, 0xa4, 0xcf, 0xff, 0x98, 0xa8, 0xd2, 0xff, 0xa6, 0xb6, 0xd9, 0xff, 0xae, 0xbc, 0xe1, 0xff, 0xa7, 0xba, 0xdb, 0xff, 0x99, 0xae, 0xd2, 0xff, 0x95, 0xa5, 0xdb, 0xff, 0x5b, 0x65, 0x88, 0xff, 0x15, 0x0e, 0x11, 0xff, 0x1f, 0x18, 0x18, 0xff, 0x2f, 0x28, 0x2a, 0xff, 0x28, 0x26, 0x25, 0xff, 0x24, 0x1f, 0x22, 0xff, 0x1f, 0x19, 0x21, 0xff, 0x29, 0x2b, 0x33, 0xff, 0x44, 0x4a, 0x57, 0xff, 0x3b, 0x46, 0x5a, 0xff, 0x4a, 0x59, 0x73, 0xff, 0x4c, 0x5b, 0x7a, 0xff, 0x4a, 0x5b, 0x7d, 0xff, 0x64, 0x74, 0x98, 0xff, 0x78, 0x84, 0xa7, 0xff, 0x6b, 0x7a, 0x99, 0xff, 0x77, 0x88, 0xa6, 0xff, 0x99, 0xa5, 0xbd, 0xff, 0xad, 0xb1, 0xc1, 0xff, 0xa7, 0xb4, 0xc3, 0xff, 0x96, 0xab, 0xc4, 0xff, 0x9e, 0xab, 0xc6, 0xff, 0xce, 0xd3, 0xdc, 0xff, 0xff, 0xfd, 0xfd, 0xff, 0xfd, 0xfe, 0xff, 0xff, 0xfa, 0xfd, 0xfe, 0xff, 0xfb, 0xfd, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfe, 0xfe, 0xff, 0xf9, 0xfb, 0xfc, 0xf2, 0xff, 0xff, 0xff, 0x01,
    0x5c, 0x7b, 0x9a, 0x21, 0x5c, 0x77, 0x9a, 0xff, 0x6f, 0x8b, 0xad, 0xff, 0x6f, 0x85, 0x9f, 0xff, 0x54, 0x66, 0x76, 0xff, 0x82, 0x96, 0xa8, 0xff, 0xa8, 0xba, 0xcc, 0xff, 0x9e, 0xad, 0xbd, 0xff, 0x96, 0xa4, 0xb6, 0xff, 0x8e, 0x9b, 0xa9, 0xff, 0x88, 0x93, 0xa0, 0xff, 0x7c, 0x89, 0x9a, 0xff, 0x72, 0x81, 0x93, 0xff, 0x6b, 0x7b, 0x94, 0xff, 0x6b, 0x7f, 0x9c, 0xff, 0x65, 0x7b, 0x99, 0xff, 0x55, 0x68, 0x83, 0xff, 0x4b, 0x5f, 0x72, 0xff, 0x66, 0x73, 0x81, 0xff, 0x73, 0x7c, 0x89, 0xff, 0x5c, 0x68, 0x76, 0xff, 0x67, 0x73, 0x88, 0xff, 0x6e, 0x7c, 0x92, 0xff, 0x51, 0x5f, 0x75, 0xff, 0x49, 0x59, 0x6d, 0xff, 0x36, 0x44, 0x55, 0xff, 0x30, 0x3b, 0x4b, 0xff, 0x52, 0x59, 0x66, 0xff, 0x2f, 0x34, 0x42, 0xff, 0x1c, 0x24, 0x39, 0xff, 0x17, 0x25, 0x37, 0xff, 0x16, 0x24, 0x33, 0xff, 0x1c, 0x22, 0x32, 0xff, 0x1f, 0x21, 0x2f, 0xff, 0x17, 0x16, 0x1f, 0xff, 0x0f, 0x09, 0x09, 0xff, 0x08, 0x01, 0x00, 0xff, 0x08, 0x01, 0x00, 0xff, 0x16, 0x12, 0x11, 0xff, 0x3d, 0x4a, 0x69, 0xff, 0x6d, 0x88, 0xb6, 0xff, 0x71, 0x8a, 0xb5, 0xff, 0x78, 0x8e, 0xbb, 0xff, 0x7a, 0x8f, 0xbd, 0xff, 0x72, 0x89, 0xba, 0xff, 0x6d, 0x83, 0xb3, 0xff, 0x66, 0x7a, 0xa9, 0xff, 0x65, 0x79, 0xa8, 0xff, 0x64, 0x78, 0xa6, 0xff, 0x68, 0x7c, 0xa9, 0xff, 0x6e, 0x82, 0xb1, 0xff, 0x6f, 0x84, 0xb3, 0xff, 0x6d, 0x81, 0xb2, 0xff, 0x6f, 0x84, 0xb0, 0xff, 0x78, 0x8d, 0xb6, 0xff, 0x7c, 0x91, 0xba, 0xff, 0x7a, 0x90, 0xbc, 0xff, 0x6f, 0x84, 0xb4, 0xff, 0x75, 0x88, 0xb8, 0xff, 0x81, 0x91, 0xbf, 0xff, 0x86, 0x96, 0xc4, 0xff, 0x86, 0x97, 0xc4, 0xff, 0x89, 0x9b, 0xc1, 0xff, 0x9a, 0xac, 0xcb, 0xff, 0xc7, 0xd1, 0xea, 0xff, 0xd6, 0xd9, 0xeb, 0xff, 0xb0, 0xbc, 0xd8, 0xff, 0x78, 0x8a, 0xb9, 0xff, 0x4f, 0x64, 0x98, 0xff, 0x68, 0x80, 0xb1, 0xff, 0x6d, 0x83, 0xb6, 0xff, 0x72, 0x88, 0xba, 0xff, 0x82, 0x94, 0xc1, 0xff, 0x8b, 0x9c, 0xc5, 0xff, 0x94, 0xa6, 0xcd, 0xff, 0xa0, 0xb0, 0xd5, 0xff, 0xae, 0xbc, 0xde, 0xff, 0xaf, 0xbe, 0xe0, 0xff, 0x9e, 0xaf, 0xd7, 0xff, 0x95, 0xa7, 0xdc, 0xff, 0x6b, 0x79, 0xa1, 0xff, 0x22, 0x20, 0x2a, 0xff, 0x1a, 0x13, 0x15, 0xff, 0x1c, 0x15, 0x15, 0xff, 0x17, 0x14, 0x12, 0xff, 0x1b, 0x17, 0x18, 0xff, 0x20, 0x1d, 0x26, 0xff, 0x3f, 0x45, 0x52, 0xff, 0x40, 0x49, 0x5a, 0xff, 0x3e, 0x4a, 0x5f, 0xff, 0x4e, 0x5b, 0x76, 0xff, 0x4f, 0x5f, 0x7d, 0xff, 0x50, 0x64, 0x89, 0xff, 0x74, 0x87, 0xab, 0xff, 0x9e, 0xac, 0xc8, 0xff, 0x99, 0xa8, 0xbe, 0xff, 0x80, 0x8b, 0xa3, 0xff, 0x8a, 0x90, 0xab, 0xff, 0xa3, 0xac, 0xbd, 0xff, 0xb0, 0xb8, 0xc3, 0xff, 0xbb, 0xc3, 0xd2, 0xff, 0xb8, 0xc2, 0xd6, 0xff, 0xd3, 0xd6, 0xdd, 0xff, 0xf9, 0xf5, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfc, 0xff, 0xff, 0xfa, 0xfc, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x21,
    0x5f, 0x7a, 0x9c, 0x4b, 0x54, 0x70, 0x91, 0xff, 0x64, 0x7e, 0x9e, 0xff, 0x72, 0x8b, 0xa6, 0xff, 0x5e, 0x78, 0x8c, 0xff, 0x80, 0x97, 0xaa, 0xff, 0xa6, 0xb9, 0xca, 0xff, 0xa1, 0xb1, 0xc1, 0xff, 0x9b, 0xaa, 0xbd, 0xff, 0x93, 0x9e, 0xae, 0xff, 0x89, 0x93, 0xa1, 0xff, 0x7d, 0x8a, 0x9b, 0xff, 0x73, 0x81, 0x94, 0xff, 0x6e, 0x7f, 0x96, 0xff, 0x66, 0x7b, 0x97, 0xff, 0x60, 0x76, 0x95, 0xff, 0x4d, 0x61, 0x7b, 0xff, 0x49, 0x59, 0x6e, 0xff, 0x6d, 0x7c, 0x8f, 0xff, 0x69, 0x77, 0x88, 0xff, 0x5f, 0x6d, 0x7d, 0xff, 0x67, 0x76, 0x87, 0xff, 0x55, 0x65, 0x77, 0xff, 0x4a, 0x5a, 0x6c, 0xff, 0x40, 0x4f, 0x63, 0xff, 0x2c, 0x39, 0x4b, 0xff, 0x36, 0x43, 0x51, 0xff, 0x36, 0x44, 0x52, 0xff, 0x19, 0x28, 0x3b, 0xff, 0x1e, 0x2a, 0x40, 0xff, 0x1c, 0x27, 0x3b, 0xff, 0x16, 0x23, 0x33, 0xff, 0x21, 0x27, 0x36, 0xff, 0x1e, 0x1f, 0x2d, 0xff, 0x16, 0x17, 0x1f, 0xff, 0x0d, 0x08, 0x07, 0xff, 0x0d, 0x06, 0x0d, 0xff, 0x0c, 0x06, 0x0e, 0xff, 0x22, 0x1b, 0x14, 0xff, 0x46, 0x52, 0x6e, 0xff, 0x6d, 0x85, 0xba, 0xff, 0x72, 0x89, 0xba, 0xff, 0x7a, 0x91, 0xc0, 0xff, 0x80, 0x93, 0xc3, 0xff, 0x7d, 0x91, 0xbf, 0xff, 0x7c, 0x8f, 0xbe, 0xff, 0x81, 0x94, 0xc4, 0xff, 0x7c, 0x90, 0xc0, 0xff, 0x7a, 0x8f, 0xbf, 0xff, 0x7c, 0x90, 0xbf, 0xff, 0x77, 0x8b, 0xba, 0xff, 0x6d, 0x81, 0xb0, 0xff, 0x6b, 0x7f, 0xaf, 0xff, 0x76, 0x88, 0xb7, 0xff, 0x70, 0x83, 0xaf, 0xff, 0x72, 0x85, 0xb2, 0xff, 0x72, 0x84, 0xb3, 0xff, 0x6e, 0x81, 0xb1, 0xff, 0x80, 0x92, 0xc1, 0xff, 0x89, 0x99, 0xc6, 0xff, 0x88, 0x96, 0xc4, 0xff, 0x7b, 0x8d, 0xba, 0xff, 0x74, 0x87, 0xb8, 0xff, 0x73, 0x87, 0xc1, 0xff, 0x96, 0xa2, 0xd0, 0xff, 0xd5, 0xd6, 0xeb, 0xff, 0xd3, 0xda, 0xeb, 0xff, 0xcb, 0xce, 0xec, 0xff, 0x64, 0x71, 0xa5, 0xff, 0x30, 0x46, 0x7d, 0xff, 0x5a, 0x70, 0xa3, 0xff, 0x68, 0x7d, 0xb1, 0xff, 0x70, 0x83, 0xb6, 0xff, 0x7a, 0x8d, 0xbb, 0xff, 0x83, 0x98, 0xc2, 0xff, 0x90, 0xa4, 0xcc, 0xff, 0xa1, 0xb2, 0xd7, 0xff, 0xad, 0xbc, 0xe0, 0xff, 0xa7, 0xb7, 0xe0, 0xff, 0x96, 0xab, 0xd8, 0xff, 0x76, 0x85, 0xb2, 0xff, 0x26, 0x28, 0x36, 0xff, 0x0d, 0x07, 0x06, 0xff, 0x19, 0x12, 0x10, 0xff, 0x21, 0x1c, 0x1e, 0xff, 0x19, 0x17, 0x1c, 0xff, 0x30, 0x34, 0x3c, 0xff, 0x47, 0x4e, 0x5e, 0xff, 0x39, 0x41, 0x56, 0xff, 0x49, 0x54, 0x6a, 0xff, 0x4d, 0x58, 0x72, 0xff, 0x49, 0x57, 0x73, 0xff, 0x4c, 0x5f, 0x84, 0xff, 0x75, 0x8a, 0xaf, 0xff, 0x9f, 0xb0, 0xcb, 0xff, 0xa0, 0xb0, 0xc3, 0xff, 0xc3, 0xc8, 0xd2, 0xff, 0xa5, 0xa7, 0xbf, 0xff, 0x7a, 0x93, 0xb4, 0xff, 0x9d, 0xaf, 0xc6, 0xff, 0xd0, 0xd4, 0xde, 0xff, 0xb5, 0xbd, 0xcc, 0xff, 0xd6, 0xd7, 0xdf, 0xff, 0xf9, 0xf6, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfd, 0xff, 0xfd, 0xfd, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x4b,
    0x5f, 0x7b, 0x9a, 0x6b, 0x53, 0x70, 0x8b, 0xff, 0x56, 0x72, 0x92, 0xff, 0x6a, 0x86, 0xa7, 0xff, 0x60, 0x7b, 0x94, 0xff, 0x80, 0x98, 0xac, 0xff, 0xa6, 0xb9, 0xca, 0xff, 0xa2, 0xb2, 0xc2, 0xff, 0x9a, 0xa8, 0xbb, 0xff, 0x94, 0x9f, 0xae, 0xff, 0x8b, 0x95, 0xa3, 0xff, 0x7e, 0x8b, 0x9c, 0xff, 0x79, 0x87, 0x9b, 0xff, 0x6e, 0x80, 0x95, 0xff, 0x5e, 0x74, 0x8a, 0xff, 0x57, 0x6f, 0x89, 0xff, 0x50, 0x64, 0x7e, 0xff, 0x4c, 0x5c, 0x71, 0xff, 0x60, 0x6f, 0x81, 0xff, 0x66, 0x77, 0x86, 0xff, 0x5e, 0x6f, 0x7e, 0xff, 0x5d, 0x6d, 0x7f, 0xff, 0x48, 0x57, 0x6b, 0xff, 0x4d, 0x5c, 0x6f, 0xff, 0x3b, 0x4b, 0x5e, 0xff, 0x2b, 0x39, 0x49, 0xff, 0x37, 0x44, 0x53, 0xff, 0x27, 0x31, 0x44, 0xff, 0x22, 0x2c, 0x44, 0xff, 0x27, 0x2e, 0x46, 0xff, 0x1e, 0x24, 0x37, 0xff, 0x18, 0x20, 0x31, 0xff, 0x1b, 0x20, 0x2f, 0xff, 0x18, 0x19, 0x26, 0xff, 0x13, 0x15, 0x1e, 0xff, 0x0b, 0x09, 0x0c, 0xff, 0x21, 0x19, 0x2d, 0xff, 0x1b, 0x12, 0x25, 0xff, 0x1f, 0x17, 0x12, 0xff, 0x47, 0x56, 0x6c, 0xff, 0x6b, 0x84, 0xb8, 0xff, 0x71, 0x86, 0xbc, 0xff, 0x7b, 0x8f, 0xc2, 0xff, 0x82, 0x96, 0xc4, 0xff, 0x85, 0x9a, 0xc9, 0xff, 0x8a, 0x9b, 0xc9, 0xff, 0x8d, 0x9d, 0xc8, 0xff, 0x8a, 0x9a, 0xc5, 0xff, 0x83, 0x96, 0xc4, 0xff, 0x7a, 0x8f, 0xbe, 0xff, 0x70, 0x84, 0xb3, 0xff, 0x67, 0x7a, 0xad, 0xff, 0x67, 0x7b, 0xae, 0xff, 0x66, 0x78, 0xaa, 0xff, 0x62, 0x73, 0xa4, 0xff, 0x6f, 0x81, 0xb2, 0xff, 0x77, 0x87, 0xb6, 0xff, 0x7e, 0x8d, 0xbc, 0xff, 0x8b, 0x9b, 0xca, 0xff, 0x95, 0xa5, 0xd3, 0xff, 0x87, 0x96, 0xc3, 0xff, 0x6d, 0x80, 0xb1, 0xff, 0x64, 0x7a, 0xb1, 0xff, 0x62, 0x76, 0xad, 0xff, 0x70, 0x82, 0xb6, 0xff, 0x95, 0xa8, 0xd7, 0xff, 0xaf, 0xbf, 0xe3, 0xff, 0xd1, 0xdd, 0xf5, 0xff, 0xbc, 0xc9, 0xe4, 0xff, 0x3f, 0x4e, 0x81, 0xff, 0x39, 0x4e, 0x89, 0xff, 0x58, 0x6b, 0xa3, 0xff, 0x62, 0x76, 0xaf, 0xff, 0x6d, 0x82, 0xb8, 0xff, 0x78, 0x8e, 0xbf, 0xff, 0x87, 0x9c, 0xc8, 0xff, 0x95, 0xa9, 0xd1, 0xff, 0xa3, 0xb4, 0xd8, 0xff, 0xa8, 0xba, 0xdb, 0xff, 0x9c, 0xb3, 0xdb, 0xff, 0x80, 0x8b, 0xbf, 0xff, 0x2b, 0x2a, 0x3d, 0xff, 0x0f, 0x0b, 0x07, 0xff, 0x1f, 0x16, 0x19, 0xff, 0x2b, 0x23, 0x2a, 0xff, 0x2f, 0x2d, 0x36, 0xff, 0x32, 0x38, 0x44, 0xff, 0x3a, 0x44, 0x57, 0xff, 0x39, 0x41, 0x55, 0xff, 0x4c, 0x56, 0x6c, 0xff, 0x41, 0x4c, 0x67, 0xff, 0x44, 0x4d, 0x6b, 0xff, 0x4b, 0x5a, 0x80, 0xff, 0x56, 0x68, 0x8f, 0xff, 0x84, 0x91, 0xb2, 0xff, 0xa2, 0xaf, 0xc9, 0xff, 0xbc, 0xc8, 0xd7, 0xff, 0xb5, 0xbe, 0xc9, 0xff, 0x86, 0x94, 0xae, 0xff, 0xac, 0xb9, 0xcd, 0xff, 0xc0, 0xc8, 0xd3, 0xff, 0xb4, 0xbe, 0xd0, 0xff, 0xdc, 0xde, 0xe5, 0xff, 0xfe, 0xfb, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0x6b,
    0x66, 0x80, 0x9f, 0x8b, 0x5a, 0x75, 0x8e, 0xff, 0x51, 0x6e, 0x87, 0xff, 0x60, 0x7b, 0x9d, 0xff, 0x59, 0x6e, 0x89, 0xff, 0x78, 0x8e, 0x9c, 0xff, 0xa4, 0xb7, 0xc3, 0xff, 0xa0, 0xac, 0xba, 0xff, 0x97, 0xa5, 0xb5, 0xff, 0x8f, 0x9e, 0xae, 0xff, 0x87, 0x95, 0xa3, 0xff, 0x80, 0x8d, 0x9b, 0xff, 0x7c, 0x8b, 0x9c, 0xff, 0x75, 0x88, 0x9a, 0xff, 0x65, 0x7d, 0x90, 0xff, 0x56, 0x6f, 0x86, 0xff, 0x5d, 0x6f, 0x85, 0xff, 0x48, 0x56, 0x68, 0xff, 0x4e, 0x5c, 0x6d, 0xff, 0x60, 0x6f, 0x80, 0xff, 0x55, 0x65, 0x76, 0xff, 0x46, 0x55, 0x67, 0xff, 0x56, 0x63, 0x78, 0xff, 0x4e, 0x5a, 0x73, 0xff, 0x31, 0x3d, 0x54, 0xff, 0x33, 0x41, 0x56, 0xff, 0x34, 0x40, 0x55, 0xff, 0x26, 0x2e, 0x42, 0xff, 0x28, 0x2d, 0x43, 0xff, 0x22, 0x27, 0x3c, 0xff, 0x1c, 0x22, 0x34, 0xff, 0x14, 0x18, 0x27, 0xff, 0x11, 0x16, 0x23, 0xff, 0x14, 0x17, 0x22, 0xff, 0x13, 0x15, 0x1d, 0xff, 0x0f, 0x0c, 0x10, 0xff, 0x1e, 0x1b, 0x2a, 0xff, 0x16, 0x15, 0x25, 0xff, 0x17, 0x10, 0x11, 0xff, 0x43, 0x4d, 0x64, 0xff, 0x6b, 0x83, 0xb8, 0xff, 0x72, 0x85, 0xba, 0xff, 0x7a, 0x8c, 0xc1, 0xff, 0x82, 0x96, 0xc4, 0xff, 0x86, 0x9b, 0xc9, 0xff, 0x89, 0x9b, 0xc8, 0xff, 0x88, 0x96, 0xc4, 0xff, 0x89, 0x98, 0xc4, 0xff, 0x8b, 0x9a, 0xc3, 0xff, 0x81, 0x91, 0xbe, 0xff, 0x71, 0x82, 0xb7, 0xff, 0x61, 0x73, 0xae, 0xff, 0x55, 0x69, 0x9f, 0xff, 0x4d, 0x63, 0x93, 0xff, 0x6c, 0x7b, 0xaa, 0xff, 0x83, 0x94, 0xc3, 0xff, 0x8b, 0x9f, 0xcc, 0xff, 0x96, 0xaa, 0xd6, 0xff, 0x9c, 0xa8, 0xd7, 0xff, 0x91, 0x9f, 0xd2, 0xff, 0x7c, 0x8d, 0xc2, 0xff, 0x63, 0x75, 0xaf, 0xff, 0x5b, 0x6f, 0xa9, 0xff, 0x65, 0x7a, 0xaf, 0xff, 0x67, 0x7d, 0xb1, 0xff, 0x3e, 0x53, 0x8a, 0xff, 0x3a, 0x4e, 0x88, 0xff, 0x67, 0x7a, 0xb7, 0xff, 0x92, 0x9f, 0xd6, 0xff, 0x58, 0x67, 0x9d, 0xff, 0x38, 0x4f, 0x83, 0xff, 0x47, 0x5c, 0x91, 0xff, 0x59, 0x6d, 0xa6, 0xff, 0x64, 0x78, 0xb2, 0xff, 0x6e, 0x83, 0xba, 0xff, 0x7c, 0x90, 0xbf, 0xff, 0x8b, 0x9b, 0xc8, 0xff, 0x9d, 0xae, 0xd4, 0xff, 0xa7, 0xbb, 0xdc, 0xff, 0xa4, 0xb9, 0xdf, 0xff, 0x82, 0x91, 0xc5, 0xff, 0x34, 0x31, 0x43, 0xff, 0x15, 0x0c, 0x0b, 0xff, 0x17, 0x10, 0x13, 0xff, 0x21, 0x1c, 0x22, 0xff, 0x34, 0x33, 0x3c, 0xff, 0x33, 0x36, 0x43, 0xff, 0x33, 0x3a, 0x4e, 0xff, 0x40, 0x4b, 0x60, 0xff, 0x47, 0x53, 0x66, 0xff, 0x39, 0x43, 0x58, 0xff, 0x3a, 0x42, 0x5d, 0xff, 0x4c, 0x59, 0x7a, 0xff, 0x44, 0x57, 0x7c, 0xff, 0x68, 0x7e, 0xa2, 0xff, 0xb3, 0xc2, 0xdd, 0xff, 0xc9, 0xce, 0xdd, 0xff, 0x9a, 0xa0, 0xb8, 0xff, 0x9b, 0xa6, 0xbf, 0xff, 0xb5, 0xc0, 0xd1, 0xff, 0xb8, 0xc1, 0xd0, 0xff, 0xb4, 0xbd, 0xcd, 0xff, 0xe5, 0xe8, 0xee, 0xff, 0xff, 0xfb, 0xf7, 0xff, 0xfd, 0xfb, 0xf9, 0xff, 0xfb, 0xfa, 0xf9, 0xff, 0xfa, 0xf9, 0xf8, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0x8b,
    0x64, 0x7f, 0x9a, 0xaa, 0x59, 0x74, 0x8f, 0xff, 0x45, 0x61, 0x7b, 0xff, 0x4b, 0x62, 0x7e, 0xff, 0x44, 0x55, 0x6b, 0xff, 0x72, 0x7f, 0x8b, 0xff, 0xae, 0xba, 0xc6, 0xff, 0xa2, 0xae, 0xbc, 0xff, 0x93, 0xa2, 0xb2, 0xff, 0x8d, 0x9c, 0xad, 0xff, 0x85, 0x94, 0xa2, 0xff, 0x82, 0x8f, 0x9c, 0xff, 0x7b, 0x8c, 0x9c, 0xff, 0x78, 0x8a, 0x9d, 0xff, 0x6c, 0x81, 0x98, 0xff, 0x59, 0x70, 0x8a, 0xff, 0x58, 0x6c, 0x84, 0xff, 0x47, 0x58, 0x6c, 0xff, 0x57, 0x65, 0x79, 0xff, 0x57, 0x64, 0x79, 0xff, 0x42, 0x4e, 0x64, 0xff, 0x51, 0x5f, 0x71, 0xff, 0x5d, 0x69, 0x7f, 0xff, 0x44, 0x4e, 0x6a, 0xff, 0x3d, 0x47, 0x63, 0xff, 0x36, 0x41, 0x5d, 0xff, 0x28, 0x34, 0x4d, 0xff, 0x20, 0x2b, 0x3e, 0xff, 0x1f, 0x26, 0x3a, 0xff, 0x19, 0x20, 0x34, 0xff, 0x10, 0x16, 0x28, 0xff, 0x12, 0x16, 0x25, 0xff, 0x11, 0x16, 0x23, 0xff, 0x10, 0x13, 0x1f, 0xff, 0x10, 0x14, 0x1a, 0xff, 0x0e, 0x0d, 0x0c, 0xff, 0x13, 0x13, 0x1a, 0xff, 0x14, 0x16, 0x26, 0xff, 0x11, 0x0e, 0x1c, 0xff, 0x35, 0x3a, 0x52, 0xff, 0x6a, 0x82, 0xb4, 0xff, 0x74, 0x87, 0xbc, 0xff, 0x7c, 0x8e, 0xc3, 0xff, 0x82, 0x96, 0xc6, 0xff, 0x86, 0x9a, 0xca, 0xff, 0x86, 0x98, 0xc9, 0xff, 0x82, 0x92, 0xc4, 0xff, 0x82, 0x94, 0xc5, 0xff, 0x8a, 0x99, 0xc3, 0xff, 0x7e, 0x8e, 0xbe, 0xff, 0x6b, 0x7d, 0xb7, 0xff, 0x58, 0x6a, 0xa7, 0xff, 0x49, 0x5e, 0x94, 0xff, 0x50, 0x66, 0x97, 0xff, 0x69, 0x77, 0xa8, 0xff, 0x85, 0x96, 0xc7, 0xff, 0x80, 0x98, 0xcb, 0xff, 0x51, 0x6a, 0x9c, 0xff, 0x35, 0x43, 0x76, 0xff, 0x47, 0x53, 0x87, 0xff, 0x65, 0x76, 0xad, 0xff, 0x62, 0x74, 0xae, 0xff, 0x59, 0x6d, 0xa7, 0xff, 0x67, 0x7a, 0xb4, 0xff, 0x48, 0x56, 0x8d, 0xff, 0x0b, 0x17, 0x4a, 0xff, 0x1e, 0x2d, 0x65, 0xff, 0x43, 0x56, 0x94, 0xff, 0x5a, 0x69, 0xa2, 0xff, 0x54, 0x66, 0x9a, 0xff, 0x47, 0x5e, 0x91, 0xff, 0x3e, 0x54, 0x87, 0xff, 0x4a, 0x5d, 0x96, 0xff, 0x5a, 0x6d, 0xa7, 0xff, 0x64, 0x77, 0xb1, 0xff, 0x6d, 0x82, 0xb8, 0xff, 0x7d, 0x8e, 0xc1, 0xff, 0x8e, 0xa0, 0xcd, 0xff, 0x9e, 0xb2, 0xdb, 0xff, 0x9e, 0xb4, 0xe0, 0xff, 0x7f, 0x91, 0xc3, 0xff, 0x31, 0x30, 0x3c, 0xff, 0x18, 0x0e, 0x0d, 0xff, 0x15, 0x10, 0x13, 0xff, 0x18, 0x14, 0x1b, 0xff, 0x2f, 0x2d, 0x36, 0xff, 0x33, 0x34, 0x40, 0xff, 0x34, 0x39, 0x4d, 0xff, 0x4b, 0x56, 0x6b, 0xff, 0x51, 0x5d, 0x71, 0xff, 0x44, 0x4f, 0x61, 0xff, 0x31, 0x3b, 0x50, 0xff, 0x48, 0x56, 0x73, 0xff, 0x50, 0x66, 0x86, 0xff, 0x51, 0x6f, 0x93, 0xff, 0x7f, 0x99, 0xba, 0xff, 0xaa, 0xb4, 0xca, 0xff, 0x85, 0x90, 0xb4, 0xff, 0x90, 0xa3, 0xc3, 0xff, 0x9e, 0xb3, 0xcf, 0xff, 0xc7, 0xd0, 0xdf, 0xff, 0xca, 0xd2, 0xdc, 0xff, 0xdf, 0xe7, 0xea, 0xff, 0xf9, 0xf7, 0xef, 0xff, 0xf5, 0xf3, 0xf1, 0xff, 0xf5, 0xf3, 0xf2, 0xff, 0xf6, 0xf4, 0xf3, 0xff, 0xfb, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xab,
    0x4c, 0x6a, 0x80, 0xbe, 0x47, 0x62, 0x80, 0xff, 0x3c, 0x54, 0x75, 0xff, 0x37, 0x4a, 0x6a, 0xff, 0x2b, 0x3a, 0x55, 0xff, 0x6a, 0x71, 0x81, 0xff, 0xb0, 0xb8, 0xc5, 0xff, 0xa3, 0xb3, 0xc0, 0xff, 0x93, 0xa3, 0xb3, 0xff, 0x8f, 0x9e, 0xaf, 0xff, 0x8a, 0x99, 0xa7, 0xff, 0x80, 0x8e, 0x9c, 0xff, 0x77, 0x86, 0x97, 0xff, 0x70, 0x82, 0x99, 0xff, 0x73, 0x87, 0xa3, 0xff, 0x60, 0x76, 0x93, 0xff, 0x54, 0x6a, 0x86, 0xff, 0x54, 0x69, 0x81, 0xff, 0x5e, 0x6d, 0x84, 0xff, 0x46, 0x52, 0x6a, 0xff, 0x49, 0x55, 0x6d, 0xff, 0x5b, 0x68, 0x7c, 0xff, 0x4d, 0x5b, 0x6e, 0xff, 0x3e, 0x4a, 0x62, 0xff, 0x3a, 0x47, 0x5d, 0xff, 0x33, 0x40, 0x55, 0xff, 0x22, 0x2e, 0x43, 0xff, 0x18, 0x22, 0x36, 0xff, 0x19, 0x21, 0x35, 0xff, 0x17, 0x1f, 0x33, 0xff, 0x16, 0x1c, 0x2e, 0xff, 0x16, 0x1a, 0x29, 0xff, 0x0d, 0x11, 0x1e, 0xff, 0x0f, 0x11, 0x1b, 0xff, 0x10, 0x11, 0x16, 0xff, 0x0f, 0x0f, 0x0d, 0xff, 0x0a, 0x0a, 0x0c, 0xff, 0x15, 0x15, 0x24, 0xff, 0x18, 0x19, 0x33, 0xff, 0x28, 0x2b, 0x42, 0xff, 0x69, 0x79, 0xa6, 0xff, 0x77, 0x8b, 0xc2, 0xff, 0x7e, 0x90, 0xc6, 0xff, 0x82, 0x95, 0xc8, 0xff, 0x86, 0x99, 0xcc, 0xff, 0x83, 0x95, 0xc9, 0xff, 0x7f, 0x92, 0xc6, 0xff, 0x84, 0x98, 0xcb, 0xff, 0x84, 0x97, 0xc6, 0xff, 0x73, 0x87, 0xbb, 0xff, 0x61, 0x75, 0xaf, 0xff, 0x4f, 0x61, 0x9b, 0xff, 0x47, 0x5c, 0x90, 0xff, 0x5b, 0x70, 0xa2, 0xff, 0x6f, 0x7c, 0xaf, 0xff, 0x71, 0x80, 0xb3, 0xff, 0x5a, 0x6e, 0xa6, 0xff, 0x31, 0x47, 0x81, 0xff, 0x1f, 0x2b, 0x65, 0xff, 0x34, 0x40, 0x75, 0xff, 0x59, 0x68, 0x9f, 0xff, 0x65, 0x77, 0xb1, 0xff, 0x5f, 0x73, 0xad, 0xff, 0x5e, 0x70, 0xaa, 0xff, 0x4b, 0x5f, 0x94, 0xff, 0x50, 0x65, 0x97, 0xff, 0x4e, 0x63, 0x97, 0xff, 0x51, 0x65, 0x9c, 0xff, 0x58, 0x6a, 0x9d, 0xff, 0x56, 0x69, 0x9b, 0xff, 0x4d, 0x63, 0x97, 0xff, 0x45, 0x5a, 0x8d, 0xff, 0x41, 0x55, 0x8c, 0xff, 0x50, 0x63, 0x9c, 0xff, 0x5d, 0x71, 0xab, 0xff, 0x64, 0x79, 0xb4, 0xff, 0x71, 0x84, 0xbe, 0xff, 0x80, 0x93, 0xc7, 0xff, 0x90, 0xa5, 0xd4, 0xff, 0x90, 0xa6, 0xd9, 0xff, 0x7a, 0x8e, 0xbe, 0xff, 0x24, 0x24, 0x29, 0xff, 0x14, 0x0a, 0x05, 0xff, 0x24, 0x1e, 0x21, 0xff, 0x15, 0x12, 0x17, 0xff, 0x2f, 0x2e, 0x36, 0xff, 0x3e, 0x41, 0x4d, 0xff, 0x37, 0x3e, 0x51, 0xff, 0x4b, 0x55, 0x6b, 0xff, 0x46, 0x51, 0x66, 0xff, 0x41, 0x4b, 0x5f, 0xff, 0x41, 0x4c, 0x60, 0xff, 0x45, 0x54, 0x6d, 0xff, 0x50, 0x61, 0x7f, 0xff, 0x4f, 0x65, 0x8a, 0xff, 0x6d, 0x84, 0xac, 0xff, 0x80, 0x97, 0xb8, 0xff, 0x6d, 0x86, 0xad, 0xff, 0x8d, 0xa4, 0xc4, 0xff, 0xb2, 0xc1, 0xd7, 0xff, 0xb1, 0xba, 0xcb, 0xff, 0xab, 0xb4, 0xc4, 0xff, 0xbf, 0xc5, 0xcf, 0xff, 0xe3, 0xe2, 0xe3, 0xff, 0xed, 0xeb, 0xe9, 0xff, 0xf2, 0xf1, 0xee, 0xff, 0xf6, 0xf6, 0xf5, 0xff, 0xf4, 0xf4, 0xf4, 0xff, 0xfa, 0xfa, 0xf9, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xbe,
    0x32, 0x50, 0x67, 0xd3, 0x35, 0x4c, 0x6b, 0xff, 0x2f, 0x45, 0x63, 0xff, 0x20, 0x36, 0x4a, 0xff, 0x17, 0x26, 0x39, 0xff, 0x63, 0x6c, 0x7c, 0xff, 0xac, 0xb9, 0xc6, 0xff, 0xa3, 0xb4, 0xc1, 0xff, 0x94, 0xa4, 0xb4, 0xff, 0x91, 0xa0, 0xb0, 0xff, 0x8d, 0x9c, 0xac, 0xff, 0x7d, 0x8c, 0x9c, 0xff, 0x7a, 0x86, 0x99, 0xff, 0x6f, 0x81, 0x98, 0xff, 0x75, 0x8b, 0xa7, 0xff, 0x62, 0x7a, 0x99, 0xff, 0x5b, 0x71, 0x8e, 0xff, 0x5d, 0x73, 0x8d, 0xff, 0x48, 0x59, 0x70, 0xff, 0x4a, 0x56, 0x6c, 0xff, 0x51, 0x5d, 0x73, 0xff, 0x4d, 0x5b, 0x70, 0xff, 0x4a, 0x5a, 0x6d, 0xff, 0x3e, 0x4e, 0x60, 0xff, 0x39, 0x47, 0x5c, 0xff, 0x2a, 0x34, 0x4b, 0xff, 0x1d, 0x23, 0x39, 0xff, 0x1b, 0x24, 0x38, 0xff, 0x1d, 0x26, 0x3a, 0xff, 0x18, 0x20, 0x34, 0xff, 0x1a, 0x20, 0x32, 0xff, 0x1a, 0x1e, 0x2d, 0xff, 0x10, 0x11, 0x1b, 0xff, 0x11, 0x11, 0x16, 0xff, 0x14, 0x12, 0x15, 0xff, 0x13, 0x12, 0x16, 0xff, 0x0a, 0x08, 0x0a, 0xff, 0x18, 0x17, 0x20, 0xff, 0x2e, 0x34, 0x4d, 0xff, 0x22, 0x26, 0x3e, 0xff, 0x5e, 0x66, 0x8b, 0xff, 0x7d, 0x8e, 0xc7, 0xff, 0x7e, 0x8f, 0xc7, 0xff, 0x81, 0x93, 0xc8, 0xff, 0x85, 0x97, 0xcc, 0xff, 0x81, 0x94, 0xc8, 0xff, 0x82, 0x95, 0xc8, 0xff, 0x84, 0x97, 0xca, 0xff, 0x7c, 0x8f, 0xc3, 0xff, 0x6c, 0x7f, 0xb7, 0xff, 0x59, 0x6e, 0xa8, 0xff, 0x48, 0x5c, 0x93, 0xff, 0x45, 0x5b, 0x8e, 0xff, 0x67, 0x7a, 0xab, 0xff, 0x81, 0x8f, 0xbd, 0xff, 0x82, 0x8c, 0xbb, 0xff, 0x72, 0x7f, 0xaf, 0xff, 0x6e, 0x7c, 0xb0, 0xff, 0x78, 0x84, 0xbc, 0xff, 0x72, 0x7e, 0xb7, 0xff, 0x65, 0x74, 0xac, 0xff, 0x5a, 0x6d, 0xa4, 0xff, 0x55, 0x6a, 0xa1, 0xff, 0x4f, 0x62, 0x9a, 0xff, 0x4c, 0x61, 0x95, 0xff, 0x58, 0x70, 0x9e, 0xff, 0x5c, 0x74, 0xa1, 0xff, 0x5e, 0x75, 0xa4, 0xff, 0x59, 0x71, 0xa0, 0xff, 0x55, 0x6b, 0x9c, 0xff, 0x50, 0x65, 0x98, 0xff, 0x4b, 0x60, 0x92, 0xff, 0x42, 0x57, 0x8b, 0xff, 0x44, 0x58, 0x90, 0xff, 0x53, 0x65, 0xa0, 0xff, 0x5c, 0x6e, 0xa8, 0xff, 0x67, 0x79, 0xb3, 0xff, 0x70, 0x84, 0xbd, 0xff, 0x7c, 0x91, 0xc5, 0xff, 0x7d, 0x95, 0xc9, 0xff, 0x6c, 0x84, 0xb4, 0xff, 0x21, 0x1f, 0x21, 0xff, 0x08, 0x00, 0x00, 0xff, 0x25, 0x20, 0x23, 0xff, 0x25, 0x23, 0x23, 0xff, 0x24, 0x23, 0x27, 0xff, 0x2f, 0x32, 0x3c, 0xff, 0x34, 0x3c, 0x4c, 0xff, 0x42, 0x4a, 0x60, 0xff, 0x39, 0x41, 0x56, 0xff, 0x33, 0x3c, 0x50, 0xff, 0x41, 0x4c, 0x62, 0xff, 0x49, 0x54, 0x6e, 0xff, 0x47, 0x54, 0x71, 0xff, 0x4f, 0x60, 0x80, 0xff, 0x5a, 0x6d, 0x91, 0xff, 0x5e, 0x75, 0x97, 0xff, 0x5c, 0x78, 0x9a, 0xff, 0x77, 0x91, 0xb6, 0xff, 0xa8, 0xb9, 0xd3, 0xff, 0xbd, 0xc7, 0xd8, 0xff, 0xb0, 0xbe, 0xd1, 0xff, 0xb8, 0xc9, 0xd8, 0xff, 0xf5, 0xf8, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfe, 0xfb, 0xff, 0xfa, 0xff, 0xfd, 0xff, 0xfa, 0xf9, 0xf8, 0xff, 0xf9, 0xf6, 0xf5, 0xff, 0xfd, 0xfb, 0xfb, 0xff, 0xfd, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0x29, 0x41, 0x59, 0xe7, 0x22, 0x36, 0x4b, 0xff, 0x19, 0x2b, 0x3f, 0xff, 0x0c, 0x1e, 0x2d, 0xff, 0x0d, 0x16, 0x21, 0xff, 0x60, 0x66, 0x73, 0xff, 0xac, 0xb9, 0xc7, 0xff, 0xa5, 0xb6, 0xc2, 0xff, 0x95, 0xa5, 0xb5, 0xff, 0x92, 0xa1, 0xb1, 0xff, 0x8d, 0x9c, 0xac, 0xff, 0x84, 0x92, 0xa3, 0xff, 0x7a, 0x85, 0x99, 0xff, 0x73, 0x84, 0x9d, 0xff, 0x6f, 0x86, 0xa3, 0xff, 0x68, 0x80, 0x9d, 0xff, 0x5b, 0x71, 0x8e, 0xff, 0x54, 0x69, 0x83, 0xff, 0x4b, 0x5c, 0x73, 0xff, 0x56, 0x61, 0x78, 0xff, 0x4a, 0x56, 0x6d, 0xff, 0x40, 0x4d, 0x64, 0xff, 0x4a, 0x58, 0x70, 0xff, 0x4e, 0x5c, 0x74, 0xff, 0x32, 0x3f, 0x55, 0xff, 0x1b, 0x24, 0x3c, 0xff, 0x1d, 0x23, 0x3a, 0xff, 0x24, 0x2c, 0x3f, 0xff, 0x23, 0x2c, 0x40, 0xff, 0x15, 0x1d, 0x31, 0xff, 0x17, 0x1e, 0x2f, 0xff, 0x1a, 0x1e, 0x2d, 0xff, 0x10, 0x14, 0x20, 0xff, 0x10, 0x12, 0x1c, 0xff, 0x14, 0x14, 0x1a, 0xff, 0x14, 0x12, 0x15, 0xff, 0x0b, 0x0a, 0x0d, 0xff, 0x12, 0x14, 0x18, 0xff, 0x2f, 0x36, 0x4a, 0xff, 0x23, 0x26, 0x44, 0xff, 0x48, 0x55, 0x72, 0xff, 0x7b, 0x90, 0xc3, 0xff, 0x81, 0x91, 0xcb, 0xff, 0x81, 0x93, 0xc8, 0xff, 0x86, 0x97, 0xcc, 0xff, 0x82, 0x94, 0xc9, 0xff, 0x84, 0x97, 0xc9, 0xff, 0x80, 0x93, 0xc4, 0xff, 0x75, 0x85, 0xbc, 0xff, 0x63, 0x75, 0xb2, 0xff, 0x4f, 0x63, 0xa1, 0xff, 0x40, 0x55, 0x8c, 0xff, 0x53, 0x69, 0x9e, 0xff, 0x6f, 0x82, 0xb0, 0xff, 0x7f, 0x8e, 0xb7, 0xff, 0x8a, 0x93, 0xc1, 0xff, 0x85, 0x95, 0xbe, 0xff, 0x81, 0x93, 0xbd, 0xff, 0x7a, 0x8a, 0xb9, 0xff, 0x69, 0x7b, 0xaf, 0xff, 0x57, 0x6f, 0xa1, 0xff, 0x50, 0x68, 0x9a, 0xff, 0x4b, 0x60, 0x93, 0xff, 0x45, 0x5c, 0x8f, 0xff, 0x4e, 0x62, 0x90, 0xff, 0x5c, 0x6f, 0x9b, 0xff, 0x6c, 0x81, 0xaa, 0xff, 0x6f, 0x85, 0xad, 0xff, 0x6a, 0x7f, 0xa5, 0xff, 0x61, 0x77, 0xa1, 0xff, 0x54, 0x6b, 0x9b, 0xff, 0x49, 0x5f, 0x8e, 0xff, 0x44, 0x5b, 0x8a, 0xff, 0x3b, 0x51, 0x84, 0xff, 0x3f, 0x53, 0x8b, 0xff, 0x51, 0x63, 0x9d, 0xff, 0x5c, 0x6f, 0xa8, 0xff, 0x65, 0x77, 0xb1, 0xff, 0x6f, 0x82, 0xbb, 0xff, 0x72, 0x89, 0xc2, 0xff, 0x60, 0x78, 0xa9, 0xff, 0x23, 0x21, 0x22, 0xff, 0x0e, 0x04, 0x00, 0xff, 0x20, 0x1b, 0x1f, 0xff, 0x22, 0x21, 0x20, 0xff, 0x19, 0x17, 0x1b, 0xff, 0x2f, 0x2d, 0x39, 0xff, 0x3f, 0x42, 0x53, 0xff, 0x3e, 0x44, 0x57, 0xff, 0x40, 0x46, 0x57, 0xff, 0x3c, 0x43, 0x55, 0xff, 0x39, 0x44, 0x5c, 0xff, 0x4b, 0x58, 0x72, 0xff, 0x4a, 0x57, 0x73, 0xff, 0x60, 0x6e, 0x8a, 0xff, 0x59, 0x69, 0x88, 0xff, 0x42, 0x52, 0x6f, 0xff, 0x49, 0x5a, 0x78, 0xff, 0x56, 0x6c, 0x90, 0xff, 0x5c, 0x76, 0x99, 0xff, 0x64, 0x7a, 0x9b, 0xff, 0x81, 0x99, 0xb9, 0xff, 0x91, 0xa7, 0xc1, 0xff, 0xbe, 0xcc, 0xd9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xfa, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xf9, 0xf7, 0xf6, 0xff, 0xfa, 0xf8, 0xf7, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xe6,
    0x14, 0x22, 0x34, 0xf0, 0x0b, 0x18, 0x26, 0xff, 0x08, 0x17, 0x21, 0xff, 0x03, 0x0c, 0x15, 0xff, 0x09, 0x0c, 0x10, 0xff, 0x51, 0x59, 0x63, 0xff, 0xa7, 0xb5, 0xc4, 0xff, 0xa8, 0xb8, 0xc7, 0xff, 0x96, 0xa6, 0xb6, 0xff, 0x91, 0xa0, 0xb0, 0xff, 0x8c, 0x9b, 0xab, 0xff, 0x88, 0x95, 0xa6, 0xff, 0x7a, 0x88, 0x99, 0xff, 0x74, 0x86, 0x9c, 0xff, 0x70, 0x87, 0xa2, 0xff, 0x6d, 0x85, 0xa5, 0xff, 0x5b, 0x73, 0x90, 0xff, 0x47, 0x5b, 0x75, 0xff, 0x4e, 0x5e, 0x73, 0xff, 0x51, 0x5f, 0x71, 0xff, 0x3f, 0x4e, 0x60, 0xff, 0x38, 0x44, 0x5e, 0xff, 0x60, 0x6a, 0x88, 0xff, 0x58, 0x65, 0x7f, 0xff, 0x2b, 0x39, 0x4d, 0xff, 0x1c, 0x27, 0x3b, 0xff, 0x26, 0x2f, 0x43, 0xff, 0x27, 0x31, 0x46, 0xff, 0x22, 0x2d, 0x42, 0xff, 0x18, 0x21, 0x35, 0xff, 0x1c, 0x20, 0x33, 0xff, 0x1b, 0x1e, 0x2f, 0xff, 0x13, 0x19, 0x27, 0xff, 0x13, 0x18, 0x25, 0xff, 0x14, 0x16, 0x1f, 0xff, 0x14, 0x13, 0x18, 0xff, 0x11, 0x0b, 0x0d, 0xff, 0x0e, 0x0d, 0x0f, 0xff, 0x22, 0x27, 0x41, 0xff, 0x2a, 0x2b, 0x4e, 0xff, 0x43, 0x4e, 0x68, 0xff, 0x75, 0x88, 0xb6, 0xff, 0x81, 0x94, 0xd0, 0xff, 0x81, 0x94, 0xc9, 0xff, 0x84, 0x98, 0xcc, 0xff, 0x80, 0x94, 0xc7, 0xff, 0x81, 0x94, 0xc7, 0xff, 0x7a, 0x8d, 0xc2, 0xff, 0x69, 0x7b, 0xb6, 0xff, 0x57, 0x68, 0xa9, 0xff, 0x43, 0x55, 0x95, 0xff, 0x43, 0x58, 0x8e, 0xff, 0x5d, 0x73, 0xa7, 0xff, 0x70, 0x84, 0xb3, 0xff, 0x81, 0x92, 0xba, 0xff, 0x87, 0x97, 0xbb, 0xff, 0x83, 0x95, 0xb7, 0xff, 0x7f, 0x90, 0xb6, 0xff, 0x72, 0x84, 0xb0, 0xff, 0x60, 0x74, 0xa8, 0xff, 0x56, 0x6c, 0xa0, 0xff, 0x55, 0x69, 0x9e, 0xff, 0x52, 0x64, 0x99, 0xff, 0x52, 0x64, 0x9a, 0xff, 0x57, 0x6a, 0x9f, 0xff, 0x6c, 0x7e, 0xad, 0xff, 0x87, 0x9a, 0xc1, 0xff, 0x8e, 0xa2, 0xc4, 0xff, 0x85, 0x98, 0xc0, 0xff, 0x71, 0x89, 0xb4, 0xff, 0x5c, 0x75, 0xa3, 0xff, 0x4f, 0x66, 0x93, 0xff, 0x46, 0x5d, 0x8a, 0xff, 0x3d, 0x52, 0x81, 0xff, 0x37, 0x4b, 0x7d, 0xff, 0x43, 0x57, 0x8f, 0xff, 0x53, 0x65, 0xa0, 0xff, 0x58, 0x6a, 0xa5, 0xff, 0x60, 0x73, 0xab, 0xff, 0x65, 0x7c, 0xb5, 0xff, 0x59, 0x71, 0x9f, 0xff, 0x27, 0x25, 0x28, 0xff, 0x13, 0x05, 0x02, 0xff, 0x19, 0x14, 0x17, 0xff, 0x1d, 0x1c, 0x1f, 0xff, 0x27, 0x23, 0x29, 0xff, 0x2e, 0x2a, 0x35, 0xff, 0x43, 0x47, 0x5a, 0xff, 0x41, 0x45, 0x58, 0xff, 0x45, 0x47, 0x59, 0xff, 0x43, 0x47, 0x59, 0xff, 0x32, 0x3c, 0x50, 0xff, 0x46, 0x51, 0x6d, 0xff, 0x4b, 0x56, 0x72, 0xff, 0x54, 0x62, 0x7d, 0xff, 0x6a, 0x7d, 0x9d, 0xff, 0x6f, 0x7c, 0x9b, 0xff, 0x69, 0x74, 0x96, 0xff, 0x84, 0x92, 0xae, 0xff, 0x7c, 0x8c, 0xa5, 0xff, 0x70, 0x8c, 0xae, 0xff, 0x9c, 0xae, 0xca, 0xff, 0xbe, 0xbf, 0xce, 0xff, 0x96, 0x9e, 0xb6, 0xff, 0xb6, 0xbb, 0xcf, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xfa, 0xf5, 0xef, 0xff, 0xfd, 0xfd, 0xfc, 0xff, 0xff, 0xfd, 0xfd, 0xff, 0xfe, 0xfc, 0xfc, 0xff, 0xfd, 0xfe, 0xfe, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x20, 0x29, 0x36, 0xf3, 0x26, 0x30, 0x3f, 0xff, 0x29, 0x32, 0x35, 0xff, 0x1d, 0x1c, 0x1e, 0xff, 0x18, 0x25, 0x36, 0xff, 0x4c, 0x60, 0x76, 0xff, 0x9f, 0xae, 0xbe, 0xff, 0xaa, 0xb8, 0xc9, 0xff, 0x97, 0xa6, 0xb6, 0xff, 0x92, 0xa2, 0xb2, 0xff, 0x90, 0x9e, 0xaf, 0xff, 0x88, 0x93, 0xa6, 0xff, 0x79, 0x88, 0x98, 0xff, 0x73, 0x88, 0x9a, 0xff, 0x70, 0x86, 0xa1, 0xff, 0x69, 0x80, 0xa2, 0xff, 0x58, 0x72, 0x8f, 0xff, 0x50, 0x63, 0x7d, 0xff, 0x4d, 0x5b, 0x71, 0xff, 0x4c, 0x5b, 0x6e, 0xff, 0x43, 0x53, 0x65, 0xff, 0x45, 0x51, 0x6c, 0xff, 0x6c, 0x75, 0x95, 0xff, 0x5a, 0x67, 0x7f, 0xff, 0x37, 0x47, 0x5a, 0xff, 0x24, 0x31, 0x45, 0xff, 0x2f, 0x3a, 0x50, 0xff, 0x2d, 0x38, 0x51, 0xff, 0x24, 0x2f, 0x48, 0xff, 0x1b, 0x23, 0x3b, 0xff, 0x1b, 0x21, 0x34, 0xff, 0x1d, 0x21, 0x33, 0xff, 0x18, 0x1d, 0x2e, 0xff, 0x16, 0x1b, 0x27, 0xff, 0x12, 0x15, 0x1e, 0xff, 0x11, 0x11, 0x19, 0xff, 0x16, 0x0c, 0x0e, 0xff, 0x10, 0x08, 0x09, 0xff, 0x1f, 0x21, 0x39, 0xff, 0x1e, 0x21, 0x3f, 0xff, 0x30, 0x35, 0x50, 0xff, 0x6d, 0x7c, 0xa8, 0xff, 0x81, 0x98, 0xd0, 0xff, 0x7f, 0x95, 0xc9, 0xff, 0x80, 0x96, 0xc9, 0xff, 0x7d, 0x94, 0xc7, 0xff, 0x7b, 0x90, 0xc7, 0xff, 0x70, 0x83, 0xc0, 0xff, 0x5e, 0x71, 0xaf, 0xff, 0x48, 0x5b, 0x9a, 0xff, 0x3a, 0x4d, 0x88, 0xff, 0x4d, 0x62, 0x95, 0xff, 0x62, 0x7a, 0xa9, 0xff, 0x73, 0x88, 0xb7, 0xff, 0x82, 0x94, 0xbe, 0xff, 0x88, 0x9a, 0xbc, 0xff, 0x85, 0x95, 0xbc, 0xff, 0x82, 0x91, 0xbd, 0xff, 0x79, 0x87, 0xb7, 0xff, 0x68, 0x76, 0xab, 0xff, 0x5f, 0x6c, 0xa4, 0xff, 0x60, 0x6a, 0xa3, 0xff, 0x61, 0x6c, 0xa1, 0xff, 0x5a, 0x65, 0x9b, 0xff, 0x56, 0x5f, 0x98, 0xff, 0x5e, 0x65, 0x98, 0xff, 0x6d, 0x73, 0x9e, 0xff, 0x72, 0x7f, 0xa7, 0xff, 0x7a, 0x89, 0xb7, 0xff, 0x7c, 0x90, 0xbf, 0xff, 0x6c, 0x83, 0xb2, 0xff, 0x57, 0x6e, 0x9c, 0xff, 0x47, 0x5b, 0x8a, 0xff, 0x40, 0x53, 0x81, 0xff, 0x37, 0x4c, 0x7a, 0xff, 0x3a, 0x4f, 0x82, 0xff, 0x4e, 0x62, 0x9a, 0xff, 0x52, 0x64, 0x9f, 0xff, 0x5a, 0x6e, 0xa4, 0xff, 0x61, 0x78, 0xae, 0xff, 0x55, 0x6a, 0x96, 0xff, 0x22, 0x20, 0x25, 0xff, 0x0d, 0x00, 0x00, 0xff, 0x18, 0x11, 0x14, 0xff, 0x2b, 0x27, 0x2f, 0xff, 0x2c, 0x28, 0x2f, 0xff, 0x1d, 0x1c, 0x24, 0xff, 0x45, 0x4d, 0x60, 0xff, 0x40, 0x47, 0x5b, 0xff, 0x3a, 0x3e, 0x52, 0xff, 0x39, 0x3f, 0x4f, 0xff, 0x2b, 0x33, 0x45, 0xff, 0x3a, 0x44, 0x61, 0xff, 0x52, 0x5b, 0x75, 0xff, 0x45, 0x57, 0x72, 0xff, 0x63, 0x79, 0x9d, 0xff, 0x86, 0x98, 0xba, 0xff, 0x83, 0x98, 0xb6, 0xff, 0xb4, 0xc5, 0xd9, 0xff, 0xbe, 0xc3, 0xd6, 0xff, 0x8c, 0xa1, 0xbd, 0xff, 0xb8, 0xce, 0xdb, 0xff, 0xe2, 0xe5, 0xe1, 0xff, 0xe8, 0xde, 0xdf, 0xff, 0xa4, 0xa7, 0xb5, 0xff, 0xc9, 0xcf, 0xd9, 0xff, 0xf5, 0xf5, 0xf4, 0xff, 0xf3, 0xf2, 0xf1, 0xff, 0xf7, 0xf6, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x45, 0x59, 0x73, 0xff, 0x64, 0x7a, 0x96, 0xff, 0x7a, 0x8a, 0x98, 0xff, 0x55, 0x59, 0x60, 0xff, 0x2c, 0x40, 0x5a, 0xff, 0x4d, 0x68, 0x88, 0xff, 0x8d, 0xa0, 0xb4, 0xff, 0xa7, 0xb5, 0xc2, 0xff, 0x94, 0xa2, 0xb1, 0xff, 0x92, 0xa2, 0xb2, 0xff, 0x94, 0xa1, 0xb2, 0xff, 0x8c, 0x97, 0xaa, 0xff, 0x82, 0x90, 0xa0, 0xff, 0x72, 0x86, 0x99, 0xff, 0x5e, 0x74, 0x90, 0xff, 0x54, 0x6c, 0x8c, 0xff, 0x5e, 0x77, 0x94, 0xff, 0x54, 0x68, 0x81, 0xff, 0x4b, 0x59, 0x70, 0xff, 0x4f, 0x5b, 0x75, 0xff, 0x4e, 0x59, 0x73, 0xff, 0x5a, 0x68, 0x85, 0xff, 0x64, 0x74, 0x92, 0xff, 0x4d, 0x5f, 0x77, 0xff, 0x45, 0x53, 0x6c, 0xff, 0x33, 0x3d, 0x57, 0xff, 0x2c, 0x34, 0x4f, 0xff, 0x2c, 0x34, 0x51, 0xff, 0x29, 0x31, 0x4f, 0xff, 0x1c, 0x27, 0x3f, 0xff, 0x11, 0x1c, 0x2e, 0xff, 0x18, 0x21, 0x31, 0xff, 0x1b, 0x22, 0x31, 0xff, 0x18, 0x1d, 0x29, 0xff, 0x13, 0x17, 0x20, 0xff, 0x13, 0x14, 0x1a, 0xff, 0x14, 0x0c, 0x0f, 0xff, 0x0e, 0x00, 0x02, 0xff, 0x17, 0x17, 0x1f, 0xff, 0x2c, 0x35, 0x4a, 0xff, 0x26, 0x2a, 0x44, 0xff, 0x5d, 0x68, 0x90, 0xff, 0x86, 0x9a, 0xcf, 0xff, 0x7e, 0x93, 0xc8, 0xff, 0x7e, 0x92, 0xc5, 0xff, 0x79, 0x90, 0xc6, 0xff, 0x74, 0x8d, 0xc7, 0xff, 0x65, 0x7b, 0xb9, 0xff, 0x51, 0x63, 0xa5, 0xff, 0x3b, 0x4f, 0x89, 0xff, 0x41, 0x58, 0x89, 0xff, 0x58, 0x6f, 0x9d, 0xff, 0x69, 0x82, 0xae, 0xff, 0x76, 0x8d, 0xb6, 0xff, 0x83, 0x94, 0xbf, 0xff, 0x8c, 0x99, 0xc6, 0xff, 0x81, 0x8e, 0xbe, 0xff, 0x6d, 0x7b, 0xb1, 0xff, 0x63, 0x6e, 0xa7, 0xff, 0x5e, 0x64, 0x99, 0xff, 0x53, 0x58, 0x95, 0xff, 0x50, 0x55, 0x8f, 0xff, 0x4f, 0x54, 0x85, 0xff, 0x40, 0x45, 0x79, 0xff, 0x41, 0x47, 0x76, 0xff, 0x4d, 0x4f, 0x7e, 0xff, 0x4a, 0x48, 0x7c, 0xff, 0x40, 0x41, 0x78, 0xff, 0x43, 0x4a, 0x79, 0xff, 0x4c, 0x56, 0x84, 0xff, 0x53, 0x63, 0x94, 0xff, 0x4f, 0x69, 0x97, 0xff, 0x45, 0x5b, 0x8a, 0xff, 0x40, 0x53, 0x83, 0xff, 0x36, 0x4b, 0x79, 0xff, 0x36, 0x4d, 0x7b, 0xff, 0x4d, 0x62, 0x94, 0xff, 0x52, 0x66, 0x9d, 0xff, 0x5b, 0x6f, 0xa6, 0xff, 0x5c, 0x73, 0xaa, 0xff, 0x53, 0x62, 0x91, 0xff, 0x1c, 0x19, 0x1f, 0xff, 0x0d, 0x05, 0x01, 0xff, 0x27, 0x21, 0x23, 0xff, 0x26, 0x23, 0x2a, 0xff, 0x22, 0x1e, 0x25, 0xff, 0x21, 0x1f, 0x27, 0xff, 0x41, 0x48, 0x5b, 0xff, 0x48, 0x52, 0x66, 0xff, 0x41, 0x4b, 0x5d, 0xff, 0x39, 0x42, 0x52, 0xff, 0x32, 0x3b, 0x4e, 0xff, 0x44, 0x4e, 0x6b, 0xff, 0x4a, 0x54, 0x6e, 0xff, 0x59, 0x6b, 0x86, 0xff, 0x62, 0x77, 0x9c, 0xff, 0x81, 0x91, 0xb2, 0xff, 0xa6, 0xbb, 0xcb, 0xff, 0x8c, 0xa0, 0xb7, 0xff, 0x9c, 0xac, 0xce, 0xff, 0x87, 0xa0, 0xc0, 0xff, 0xaf, 0xc1, 0xcb, 0xff, 0xf3, 0xee, 0xf0, 0xff, 0xff, 0xfd, 0xf5, 0xff, 0xd8, 0xd9, 0xd4, 0xff, 0xb2, 0xb0, 0xc0, 0xff, 0xee, 0xf2, 0xf2, 0xff, 0xf1, 0xf0, 0xee, 0xff, 0xf2, 0xf0, 0xf0, 0xff, 0xf8, 0xf7, 0xf7, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x4e, 0x68, 0x8b, 0xff, 0x6d, 0x88, 0xab, 0xff, 0x97, 0xad, 0xc1, 0xff, 0x6a, 0x75, 0x7f, 0xff, 0x2b, 0x3e, 0x56, 0xff, 0x46, 0x61, 0x83, 0xff, 0x87, 0x9c, 0xaf, 0xff, 0xa3, 0xb2, 0xc1, 0xff, 0x92, 0xa0, 0xb1, 0xff, 0x93, 0xa2, 0xb2, 0xff, 0x8e, 0x9f, 0xaf, 0xff, 0x82, 0x94, 0xa7, 0xff, 0x73, 0x84, 0x9a, 0xff, 0x5d, 0x71, 0x89, 0xff, 0x5d, 0x75, 0x8f, 0xff, 0x65, 0x7b, 0x9a, 0xff, 0x60, 0x77, 0x92, 0xff, 0x52, 0x66, 0x81, 0xff, 0x42, 0x50, 0x6a, 0xff, 0x47, 0x53, 0x6e, 0xff, 0x4f, 0x5c, 0x79, 0xff, 0x6f, 0x7f, 0x9c, 0xff, 0x6b, 0x7d, 0x97, 0xff, 0x51, 0x64, 0x7a, 0xff, 0x3c, 0x4a, 0x65, 0xff, 0x35, 0x41, 0x5d, 0xff, 0x2f, 0x3a, 0x56, 0xff, 0x2d, 0x37, 0x53, 0xff, 0x28, 0x32, 0x4f, 0xff, 0x1f, 0x2b, 0x42, 0xff, 0x12, 0x1e, 0x2f, 0xff, 0x1a, 0x22, 0x32, 0xff, 0x1c, 0x21, 0x34, 0xff, 0x18, 0x1b, 0x2c, 0xff, 0x13, 0x14, 0x21, 0xff, 0x14, 0x13, 0x1c, 0xff, 0x11, 0x0d, 0x13, 0xff, 0x0b, 0x02, 0x03, 0xff, 0x06, 0x00, 0x01, 0xff, 0x2d, 0x35, 0x49, 0xff, 0x37, 0x3e, 0x5a, 0xff, 0x56, 0x60, 0x82, 0xff, 0x86, 0x9a, 0xce, 0xff, 0x7e, 0x93, 0xca, 0xff, 0x7e, 0x93, 0xc7, 0xff, 0x79, 0x8d, 0xc4, 0xff, 0x72, 0x86, 0xc2, 0xff, 0x5b, 0x72, 0xb1, 0xff, 0x3e, 0x52, 0x91, 0xff, 0x38, 0x4c, 0x7f, 0xff, 0x50, 0x64, 0x94, 0xff, 0x63, 0x76, 0xa8, 0xff, 0x6f, 0x86, 0xb3, 0xff, 0x78, 0x92, 0xbd, 0xff, 0x7b, 0x8e, 0xbd, 0xff, 0x73, 0x7a, 0xaf, 0xff, 0x54, 0x5c, 0x97, 0xff, 0x49, 0x51, 0x8b, 0xff, 0x4d, 0x51, 0x8d, 0xff, 0x53, 0x55, 0x93, 0xff, 0x55, 0x53, 0x92, 0xff, 0x4c, 0x4d, 0x86, 0xff, 0x40, 0x42, 0x7d, 0xff, 0x47, 0x4a, 0x82, 0xff, 0x57, 0x61, 0x87, 0xff, 0x48, 0x4e, 0x82, 0xff, 0x58, 0x5b, 0x92, 0xff, 0x6f, 0x72, 0x9e, 0xff, 0x63, 0x6a, 0x8f, 0xff, 0x29, 0x26, 0x59, 0xff, 0x1c, 0x1d, 0x4a, 0xff, 0x2b, 0x3a, 0x63, 0xff, 0x35, 0x45, 0x73, 0xff, 0x3c, 0x4a, 0x79, 0xff, 0x37, 0x48, 0x74, 0xff, 0x3a, 0x4f, 0x7f, 0xff, 0x52, 0x65, 0x9e, 0xff, 0x52, 0x6b, 0xa0, 0xff, 0x55, 0x6e, 0xa2, 0xff, 0x57, 0x6c, 0xa8, 0xff, 0x4c, 0x5b, 0x87, 0xff, 0x1d, 0x1b, 0x1e, 0xff, 0x18, 0x12, 0x10, 0xff, 0x27, 0x25, 0x27, 0xff, 0x1f, 0x1e, 0x20, 0xff, 0x1d, 0x1c, 0x1f, 0xff, 0x1e, 0x1f, 0x27, 0xff, 0x3b, 0x41, 0x4f, 0xff, 0x4b, 0x56, 0x6f, 0xff, 0x45, 0x53, 0x6e, 0xff, 0x3d, 0x45, 0x56, 0xff, 0x43, 0x4c, 0x5d, 0xff, 0x4e, 0x5a, 0x70, 0xff, 0x44, 0x51, 0x6b, 0xff, 0x50, 0x60, 0x7f, 0xff, 0x6c, 0x7e, 0xa6, 0xff, 0x6b, 0x7e, 0xa5, 0xff, 0xc1, 0xce, 0xde, 0xff, 0xba, 0xc2, 0xd3, 0xff, 0x77, 0x90, 0xb2, 0xff, 0x7a, 0x9c, 0xbf, 0xff, 0xa7, 0xb5, 0xc7, 0xff, 0xcf, 0xd0, 0xd7, 0xff, 0xf2, 0xf7, 0xf8, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xe4, 0xdc, 0xda, 0xff, 0xef, 0xef, 0xea, 0xff, 0xe9, 0xe8, 0xe7, 0xff, 0xf7, 0xf5, 0xf5, 0xff, 0xfc, 0xfb, 0xfb, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x6b, 0x90, 0xf3, 0x5d, 0x7a, 0x9d, 0xff, 0x8b, 0xa1, 0xb5, 0xff, 0x6e, 0x79, 0x7f, 0xff, 0x2d, 0x3f, 0x58, 0xff, 0x3e, 0x5b, 0x81, 0xff, 0x83, 0x9a, 0xae, 0xff, 0xa3, 0xb4, 0xc7, 0xff, 0x91, 0xa2, 0xb7, 0xff, 0x8a, 0x9b, 0xaf, 0xff, 0x7a, 0x8e, 0xa1, 0xff, 0x71, 0x8b, 0x9c, 0xff, 0x6a, 0x82, 0x99, 0xff, 0x65, 0x7d, 0x96, 0xff, 0x6d, 0x84, 0x9f, 0xff, 0x67, 0x7c, 0x9a, 0xff, 0x58, 0x70, 0x8a, 0xff, 0x4b, 0x5f, 0x79, 0xff, 0x3e, 0x50, 0x6b, 0xff, 0x42, 0x59, 0x73, 0xff, 0x64, 0x78, 0x8e, 0xff, 0x76, 0x87, 0x99, 0xff, 0x65, 0x75, 0x88, 0xff, 0x55, 0x62, 0x7c, 0xff, 0x3a, 0x44, 0x61, 0xff, 0x36, 0x42, 0x5f, 0xff, 0x31, 0x3d, 0x59, 0xff, 0x36, 0x41, 0x5c, 0xff, 0x36, 0x42, 0x5e, 0xff, 0x1f, 0x2a, 0x44, 0xff, 0x17, 0x21, 0x35, 0xff, 0x1b, 0x22, 0x34, 0xff, 0x1a, 0x1f, 0x33, 0xff, 0x19, 0x1c, 0x2f, 0xff, 0x14, 0x14, 0x22, 0xff, 0x12, 0x10, 0x19, 0xff, 0x11, 0x0e, 0x15, 0xff, 0x0c, 0x06, 0x08, 0xff, 0x08, 0x00, 0x00, 0xff, 0x1c, 0x1e, 0x2d, 0xff, 0x39, 0x41, 0x5c, 0xff, 0x55, 0x61, 0x7f, 0xff, 0x81, 0x99, 0xc8, 0xff, 0x7f, 0x95, 0xc9, 0xff, 0x7f, 0x94, 0xc6, 0xff, 0x7b, 0x8e, 0xc4, 0xff, 0x71, 0x83, 0xc0, 0xff, 0x52, 0x69, 0xa9, 0xff, 0x33, 0x4a, 0x84, 0xff, 0x39, 0x4f, 0x7b, 0xff, 0x4a, 0x5f, 0x8e, 0xff, 0x5d, 0x72, 0xa6, 0xff, 0x6d, 0x7d, 0xae, 0xff, 0x63, 0x74, 0xa7, 0xff, 0x43, 0x52, 0x8c, 0xff, 0x3c, 0x43, 0x83, 0xff, 0x65, 0x69, 0xa2, 0xff, 0x72, 0x74, 0xa6, 0xff, 0x7c, 0x80, 0xa7, 0xff, 0x6c, 0x6f, 0xa0, 0xff, 0x8f, 0x95, 0xb1, 0xff, 0x94, 0x9e, 0xb4, 0xff, 0x65, 0x6b, 0x99, 0xff, 0x7b, 0x83, 0xa6, 0xff, 0x9d, 0xae, 0xbf, 0xff, 0x7a, 0x88, 0xaa, 0xff, 0x99, 0xa6, 0xc5, 0xff, 0xb3, 0xbd, 0xd1, 0xff, 0x9f, 0xa7, 0xc3, 0xff, 0x5c, 0x5d, 0x86, 0xff, 0x22, 0x21, 0x43, 0xff, 0x14, 0x15, 0x3c, 0xff, 0x22, 0x28, 0x55, 0xff, 0x2f, 0x3d, 0x68, 0xff, 0x36, 0x4b, 0x73, 0xff, 0x48, 0x5c, 0x8c, 0xff, 0x58, 0x6a, 0xa7, 0xff, 0x53, 0x6d, 0xa5, 0xff, 0x52, 0x6c, 0x9f, 0xff, 0x58, 0x70, 0xa5, 0xff, 0x41, 0x4f, 0x72, 0xff, 0x13, 0x0e, 0x0d, 0xff, 0x24, 0x1f, 0x20, 0xff, 0x23, 0x22, 0x28, 0xff, 0x18, 0x16, 0x19, 0xff, 0x1b, 0x19, 0x1d, 0xff, 0x22, 0x24, 0x2c, 0xff, 0x31, 0x38, 0x45, 0xff, 0x4a, 0x51, 0x65, 0xff, 0x45, 0x50, 0x6b, 0xff, 0x44, 0x4c, 0x60, 0xff, 0x46, 0x4e, 0x60, 0xff, 0x37, 0x43, 0x58, 0xff, 0x4c, 0x5a, 0x72, 0xff, 0x4d, 0x59, 0x77, 0xff, 0x6f, 0x7c, 0xa1, 0xff, 0x68, 0x7f, 0xaa, 0xff, 0x79, 0x8d, 0xab, 0xff, 0xbb, 0xbc, 0xca, 0xff, 0x93, 0x9f, 0xbe, 0xff, 0x70, 0x8a, 0xb5, 0xff, 0x96, 0xaa, 0xc6, 0xff, 0xaf, 0xc0, 0xcf, 0xff, 0xd0, 0xd6, 0xde, 0xff, 0xda, 0xde, 0xdd, 0xff, 0xcf, 0xcf, 0xce, 0xff, 0xe4, 0xe0, 0xe2, 0xff, 0xf8, 0xf6, 0xf5, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xfc, 0xfb, 0xfb, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x50, 0x6d, 0x92, 0xf0, 0x58, 0x77, 0x9e, 0xff, 0x84, 0x9b, 0xb1, 0xff, 0x73, 0x7e, 0x85, 0xff, 0x2f, 0x41, 0x59, 0xff, 0x38, 0x56, 0x7b, 0xff, 0x79, 0x93, 0xaa, 0xff, 0x9f, 0xb3, 0xc8, 0xff, 0x8c, 0xa0, 0xb6, 0xff, 0x82, 0x98, 0xae, 0xff, 0x7b, 0x8f, 0xa3, 0xff, 0x7a, 0x8c, 0xa0, 0xff, 0x79, 0x8a, 0xa2, 0xff, 0x6f, 0x84, 0x9d, 0xff, 0x66, 0x7e, 0x98, 0xff, 0x5b, 0x6f, 0x8e, 0xff, 0x52, 0x68, 0x84, 0xff, 0x5e, 0x71, 0x8c, 0xff, 0x5a, 0x6d, 0x89, 0xff, 0x4d, 0x62, 0x7c, 0xff, 0x71, 0x7d, 0x90, 0xff, 0x75, 0x82, 0x8e, 0xff, 0x67, 0x75, 0x85, 0xff, 0x42, 0x4c, 0x67, 0xff, 0x36, 0x42, 0x5f, 0xff, 0x3a, 0x47, 0x63, 0xff, 0x2f, 0x3b, 0x57, 0xff, 0x35, 0x3f, 0x5a, 0xff, 0x44, 0x4f, 0x6a, 0xff, 0x27, 0x31, 0x4c, 0xff, 0x1a, 0x22, 0x3b, 0xff, 0x1b, 0x20, 0x37, 0xff, 0x16, 0x1c, 0x30, 0xff, 0x1a, 0x1e, 0x2f, 0xff, 0x1a, 0x1c, 0x2a, 0xff, 0x14, 0x14, 0x1d, 0xff, 0x0e, 0x0b, 0x11, 0xff, 0x0a, 0x04, 0x07, 0xff, 0x0b, 0x04, 0x02, 0xff, 0x22, 0x1f, 0x24, 0xff, 0x2b, 0x2e, 0x41, 0xff, 0x44, 0x50, 0x6e, 0xff, 0x81, 0x98, 0xc6, 0xff, 0x82, 0x99, 0xc9, 0xff, 0x7d, 0x93, 0xc1, 0xff, 0x7d, 0x91, 0xc5, 0xff, 0x74, 0x87, 0xc3, 0xff, 0x58, 0x6d, 0xad, 0xff, 0x39, 0x50, 0x88, 0xff, 0x34, 0x4b, 0x75, 0xff, 0x39, 0x4e, 0x7c, 0xff, 0x3c, 0x4d, 0x81, 0xff, 0x41, 0x49, 0x78, 0xff, 0x2c, 0x2d, 0x5b, 0xff, 0x34, 0x32, 0x66, 0xff, 0x6e, 0x72, 0xa5, 0xff, 0xb1, 0xbc, 0xd2, 0xff, 0xbd, 0xc1, 0xd4, 0xff, 0xbe, 0xc6, 0xd2, 0xff, 0xa7, 0xb8, 0xc6, 0xff, 0xbe, 0xcd, 0xcc, 0xff, 0xc1, 0xd0, 0xcd, 0xff, 0xad, 0xba, 0xc8, 0xff, 0xa9, 0xb8, 0xbf, 0xff, 0xb9, 0xcb, 0xc8, 0xff, 0xb6, 0xc2, 0xcd, 0xff, 0xc4, 0xd3, 0xd8, 0xff, 0xc5, 0xda, 0xe0, 0xff, 0xb5, 0xc4, 0xdb, 0xff, 0xa4, 0xad, 0xc7, 0xff, 0x4e, 0x54, 0x6d, 0xff, 0x16, 0x13, 0x37, 0xff, 0x25, 0x25, 0x50, 0xff, 0x32, 0x44, 0x6a, 0xff, 0x36, 0x53, 0x77, 0xff, 0x50, 0x65, 0x96, 0xff, 0x5a, 0x6c, 0xa8, 0xff, 0x54, 0x6c, 0xa3, 0xff, 0x50, 0x6a, 0x9e, 0xff, 0x53, 0x6f, 0x9c, 0xff, 0x36, 0x3f, 0x58, 0xff, 0x0e, 0x06, 0x02, 0xff, 0x23, 0x1e, 0x22, 0xff, 0x2d, 0x2c, 0x37, 0xff, 0x25, 0x22, 0x2c, 0xff, 0x23, 0x20, 0x28, 0xff, 0x29, 0x2a, 0x32, 0xff, 0x2f, 0x35, 0x43, 0xff, 0x3a, 0x3e, 0x4b, 0xff, 0x48, 0x51, 0x64, 0xff, 0x4a, 0x53, 0x68, 0xff, 0x3c, 0x45, 0x57, 0xff, 0x30, 0x3c, 0x51, 0xff, 0x42, 0x4e, 0x64, 0xff, 0x40, 0x4a, 0x64, 0xff, 0x56, 0x62, 0x82, 0xff, 0x6d, 0x7b, 0x9f, 0xff, 0x65, 0x7d, 0xa5, 0xff, 0x92, 0x9d, 0xbc, 0xff, 0xb0, 0xba, 0xcf, 0xff, 0x75, 0x90, 0xb2, 0xff, 0x8a, 0xa2, 0xc0, 0xff, 0xbd, 0xcc, 0xe0, 0xff, 0xdd, 0xde, 0xe8, 0xff, 0xef, 0xea, 0xe6, 0xff, 0xca, 0xc5, 0xc3, 0xff, 0xcd, 0xcd, 0xca, 0xff, 0xf7, 0xf6, 0xf5, 0xff, 0xec, 0xe9, 0xe7, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x50, 0x6f, 0x90, 0xe7, 0x5a, 0x78, 0x9c, 0xff, 0x84, 0x99, 0xb1, 0xff, 0x77, 0x85, 0x8d, 0xff, 0x32, 0x45, 0x5a, 0xff, 0x3a, 0x52, 0x74, 0xff, 0x74, 0x8c, 0xa4, 0xff, 0x9b, 0xb2, 0xc3, 0xff, 0x8f, 0xa4, 0xb5, 0xff, 0x8f, 0xa3, 0xb4, 0xff, 0x88, 0x9b, 0xae, 0xff, 0x79, 0x88, 0x9f, 0xff, 0x79, 0x87, 0x9e, 0xff, 0x73, 0x87, 0x9e, 0xff, 0x5e, 0x74, 0x90, 0xff, 0x5f, 0x73, 0x92, 0xff, 0x70, 0x82, 0xa2, 0xff, 0x65, 0x77, 0x97, 0xff, 0x56, 0x6a, 0x88, 0xff, 0x56, 0x67, 0x81, 0xff, 0x62, 0x6f, 0x87, 0xff, 0x5a, 0x65, 0x77, 0xff, 0x50, 0x5d, 0x6f, 0xff, 0x2f, 0x3d, 0x54, 0xff, 0x27, 0x3a, 0x55, 0xff, 0x32, 0x41, 0x5d, 0xff, 0x2f, 0x3a, 0x57, 0xff, 0x2f, 0x3a, 0x56, 0xff, 0x40, 0x4d, 0x63, 0xff, 0x24, 0x30, 0x48, 0xff, 0x17, 0x1e, 0x3b, 0xff, 0x1d, 0x21, 0x3d, 0xff, 0x1a, 0x21, 0x35, 0xff, 0x18, 0x1e, 0x2e, 0xff, 0x18, 0x1c, 0x2a, 0xff, 0x14, 0x18, 0x22, 0xff, 0x0d, 0x0b, 0x0f, 0xff, 0x0d, 0x05, 0x05, 0xff, 0x03, 0x00, 0x00, 0xff, 0x37, 0x33, 0x33, 0xff, 0x53, 0x4f, 0x5d, 0xff, 0x36, 0x3e, 0x62, 0xff, 0x7c, 0x8f, 0xc2, 0xff, 0x86, 0x9b, 0xcb, 0xff, 0x7e, 0x91, 0xc1, 0xff, 0x7e, 0x93, 0xc5, 0xff, 0x78, 0x8e, 0xc5, 0xff, 0x6a, 0x7e, 0xb8, 0xff, 0x4e, 0x62, 0x9c, 0xff, 0x32, 0x46, 0x78, 0xff, 0x30, 0x40, 0x6d, 0xff, 0x26, 0x2f, 0x59, 0xff, 0x12, 0x18, 0x3d, 0xff, 0x24, 0x23, 0x3a, 0xff, 0x8c, 0x85, 0x9a, 0xff, 0xc4, 0xcd, 0xe6, 0xff, 0xca, 0xdf, 0xe9, 0xff, 0xcf, 0xde, 0xdf, 0xff, 0xc1, 0xce, 0xd2, 0xff, 0xa3, 0xb1, 0xbf, 0xff, 0xbf, 0xc6, 0xcb, 0xff, 0xc7, 0xcd, 0xd0, 0xff, 0x9a, 0xa5, 0xac, 0xff, 0x8b, 0x97, 0xa2, 0xff, 0x9e, 0xa9, 0xb7, 0xff, 0x80, 0x89, 0x99, 0xff, 0x73, 0x7a, 0x8f, 0xff, 0x73, 0x81, 0x9b, 0xff, 0x77, 0x84, 0x95, 0xff, 0x62, 0x61, 0x64, 0xff, 0x31, 0x30, 0x40, 0xff, 0x21, 0x24, 0x4a, 0xff, 0x38, 0x41, 0x6c, 0xff, 0x3d, 0x50, 0x76, 0xff, 0x3f, 0x59, 0x7f, 0xff, 0x54, 0x6a, 0xa0, 0xff, 0x58, 0x6c, 0xa5, 0xff, 0x54, 0x6a, 0x9b, 0xff, 0x4c, 0x66, 0x98, 0xff, 0x49, 0x60, 0x8a, 0xff, 0x25, 0x28, 0x39, 0xff, 0x1d, 0x17, 0x12, 0xff, 0x23, 0x1e, 0x24, 0xff, 0x27, 0x26, 0x32, 0xff, 0x34, 0x34, 0x3e, 0xff, 0x24, 0x23, 0x2d, 0xff, 0x36, 0x35, 0x41, 0xff, 0x39, 0x3a, 0x4a, 0xff, 0x2b, 0x2f, 0x3d, 0xff, 0x46, 0x4e, 0x5e, 0xff, 0x48, 0x53, 0x65, 0xff, 0x44, 0x4e, 0x61, 0xff, 0x36, 0x40, 0x51, 0xff, 0x37, 0x40, 0x53, 0xff, 0x3c, 0x47, 0x5f, 0xff, 0x48, 0x59, 0x77, 0xff, 0x61, 0x74, 0x96, 0xff, 0x73, 0x87, 0xac, 0xff, 0x74, 0x88, 0xac, 0xff, 0xa1, 0xb2, 0xca, 0xff, 0x8e, 0xa8, 0xbe, 0xff, 0x7e, 0x99, 0xbc, 0xff, 0x91, 0xa6, 0xcd, 0xff, 0xac, 0xbe, 0xd5, 0xff, 0xd9, 0xdf, 0xe6, 0xff, 0xbe, 0xc0, 0xc4, 0xff, 0xd9, 0xdd, 0xdc, 0xff, 0xf7, 0xf9, 0xf5, 0xff, 0xef, 0xec, 0xea, 0xff, 0xe6, 0xe3, 0xe2, 0xff, 0xfd, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xe6,
    0x4f, 0x6c, 0x8f, 0xd3, 0x58, 0x76, 0x9a, 0xff, 0x7f, 0x94, 0xaa, 0xff, 0x7d, 0x8c, 0x93, 0xff, 0x35, 0x48, 0x5e, 0xff, 0x36, 0x4e, 0x71, 0xff, 0x70, 0x89, 0xa1, 0xff, 0x9c, 0xb2, 0xc3, 0xff, 0x92, 0xa6, 0xb7, 0xff, 0x8e, 0xa2, 0xb4, 0xff, 0x88, 0x9c, 0xae, 0xff, 0x83, 0x95, 0xa7, 0xff, 0x7f, 0x90, 0xa0, 0xff, 0x71, 0x86, 0x9c, 0xff, 0x5e, 0x74, 0x91, 0xff, 0x74, 0x88, 0xa7, 0xff, 0x74, 0x8a, 0xa8, 0xff, 0x50, 0x67, 0x86, 0xff, 0x54, 0x6b, 0x88, 0xff, 0x54, 0x65, 0x80, 0xff, 0x50, 0x5d, 0x74, 0xff, 0x47, 0x54, 0x68, 0xff, 0x38, 0x47, 0x5e, 0xff, 0x2f, 0x40, 0x5b, 0xff, 0x30, 0x43, 0x5f, 0xff, 0x40, 0x4e, 0x6a, 0xff, 0x3d, 0x47, 0x64, 0xff, 0x31, 0x3c, 0x57, 0xff, 0x2d, 0x3a, 0x50, 0xff, 0x18, 0x24, 0x3c, 0xff, 0x19, 0x1f, 0x3d, 0xff, 0x21, 0x24, 0x42, 0xff, 0x1c, 0x24, 0x38, 0xff, 0x18, 0x1e, 0x2e, 0xff, 0x14, 0x19, 0x26, 0xff, 0x14, 0x17, 0x21, 0xff, 0x12, 0x10, 0x14, 0xff, 0x0f, 0x06, 0x06, 0xff, 0x00, 0x00, 0x00, 0xff, 0x24, 0x21, 0x24, 0xff, 0x70, 0x70, 0x7a, 0xff, 0x2b, 0x35, 0x40, 0xff, 0x51, 0x61, 0x8a, 0xff, 0x93, 0xa8, 0xdd, 0xff, 0x82, 0x95, 0xc4, 0xff, 0x7c, 0x93, 0xc2, 0xff, 0x7a, 0x92, 0xc2, 0xff, 0x76, 0x8b, 0xc1, 0xff, 0x62, 0x76, 0xb0, 0xff, 0x3e, 0x51, 0x84, 0xff, 0x2a, 0x3b, 0x66, 0xff, 0x30, 0x3f, 0x65, 0xff, 0x29, 0x30, 0x5a, 0xff, 0x1b, 0x1c, 0x3c, 0xff, 0x4d, 0x4d, 0x4f, 0xff, 0x80, 0x85, 0x87, 0xff, 0x87, 0x8e, 0xa3, 0xff, 0x78, 0x7f, 0x9c, 0xff, 0x50, 0x53, 0x79, 0xff, 0x34, 0x2e, 0x54, 0xff, 0x3c, 0x37, 0x5a, 0xff, 0x47, 0x42, 0x62, 0xff, 0x26, 0x21, 0x43, 0xff, 0x21, 0x20, 0x47, 0xff, 0x30, 0x30, 0x56, 0xff, 0x24, 0x22, 0x4d, 0xff, 0x20, 0x1d, 0x50, 0xff, 0x1a, 0x13, 0x37, 0xff, 0x06, 0x02, 0x04, 0xff, 0x00, 0x00, 0x00, 0xff, 0x20, 0x1b, 0x36, 0xff, 0x42, 0x50, 0x80, 0xff, 0x44, 0x58, 0x83, 0xff, 0x42, 0x51, 0x7b, 0xff, 0x4a, 0x5c, 0x87, 0xff, 0x55, 0x6b, 0xa2, 0xff, 0x52, 0x66, 0xa1, 0xff, 0x4d, 0x65, 0x95, 0xff, 0x4a, 0x61, 0x90, 0xff, 0x39, 0x46, 0x65, 0xff, 0x15, 0x12, 0x16, 0xff, 0x1f, 0x15, 0x1b, 0xff, 0x20, 0x20, 0x29, 0xff, 0x25, 0x27, 0x30, 0xff, 0x25, 0x25, 0x31, 0xff, 0x1f, 0x1e, 0x28, 0xff, 0x35, 0x33, 0x3f, 0xff, 0x38, 0x3a, 0x49, 0xff, 0x2d, 0x33, 0x41, 0xff, 0x42, 0x4b, 0x5b, 0xff, 0x51, 0x5c, 0x6e, 0xff, 0x3e, 0x47, 0x59, 0xff, 0x39, 0x43, 0x54, 0xff, 0x42, 0x4b, 0x5e, 0xff, 0x46, 0x51, 0x6a, 0xff, 0x4c, 0x5d, 0x7c, 0xff, 0x63, 0x76, 0x99, 0xff, 0x77, 0x8a, 0xad, 0xff, 0x73, 0x86, 0xab, 0xff, 0x81, 0x96, 0xb8, 0xff, 0x95, 0xa9, 0xc4, 0xff, 0x8b, 0xa3, 0xc2, 0xff, 0x91, 0xa2, 0xbd, 0xff, 0x8f, 0xa2, 0xc2, 0xff, 0x9a, 0xae, 0xca, 0xff, 0xb1, 0xba, 0xc6, 0xff, 0xdc, 0xe1, 0xe2, 0xff, 0xed, 0xed, 0xeb, 0xff, 0xec, 0xe7, 0xe8, 0xff, 0xe3, 0xdf, 0xe0, 0xff, 0xfd, 0xfe, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0x50, 0x6c, 0x8e, 0xbe, 0x57, 0x76, 0x98, 0xff, 0x76, 0x8b, 0xa6, 0xff, 0x7b, 0x89, 0x94, 0xff, 0x39, 0x4b, 0x61, 0xff, 0x35, 0x4c, 0x6f, 0xff, 0x6d, 0x84, 0x9b, 0xff, 0x9c, 0xb3, 0xc6, 0xff, 0x95, 0xaa, 0xbc, 0xff, 0x8f, 0xa4, 0xb6, 0xff, 0x8d, 0xa0, 0xb1, 0xff, 0x84, 0x96, 0xa5, 0xff, 0x7e, 0x8e, 0x9f, 0xff, 0x76, 0x89, 0xa0, 0xff, 0x69, 0x7f, 0x9b, 0xff, 0x74, 0x8a, 0xa8, 0xff, 0x73, 0x8c, 0xab, 0xff, 0x53, 0x6d, 0x8d, 0xff, 0x4c, 0x64, 0x81, 0xff, 0x4b, 0x5d, 0x76, 0xff, 0x52, 0x62, 0x7c, 0xff, 0x54, 0x61, 0x78, 0xff, 0x53, 0x62, 0x7a, 0xff, 0x46, 0x5a, 0x76, 0xff, 0x40, 0x54, 0x6f, 0xff, 0x47, 0x57, 0x73, 0xff, 0x39, 0x45, 0x61, 0xff, 0x26, 0x31, 0x4d, 0xff, 0x23, 0x2f, 0x49, 0xff, 0x1f, 0x2a, 0x44, 0xff, 0x1d, 0x25, 0x3f, 0xff, 0x20, 0x25, 0x3d, 0xff, 0x18, 0x1e, 0x33, 0xff, 0x19, 0x1d, 0x2f, 0xff, 0x1b, 0x1e, 0x2e, 0xff, 0x15, 0x19, 0x23, 0xff, 0x14, 0x14, 0x18, 0xff, 0x10, 0x0a, 0x0b, 0xff, 0x09, 0x04, 0x01, 0xff, 0x09, 0x04, 0x08, 0xff, 0x30, 0x2f, 0x31, 0xff, 0x13, 0x15, 0x0e, 0xff, 0x37, 0x3b, 0x50, 0xff, 0x95, 0xab, 0xde, 0xff, 0x80, 0x96, 0xca, 0xff, 0x7d, 0x91, 0xc0, 0xff, 0x7c, 0x93, 0xc5, 0xff, 0x7b, 0x91, 0xc5, 0xff, 0x70, 0x85, 0xbd, 0xff, 0x4e, 0x63, 0x98, 0xff, 0x29, 0x3e, 0x6b, 0xff, 0x3b, 0x4b, 0x76, 0xff, 0x56, 0x5e, 0x92, 0xff, 0x44, 0x47, 0x81, 0xff, 0x17, 0x15, 0x31, 0xff, 0x00, 0x00, 0x00, 0xff, 0x09, 0x05, 0x11, 0xff, 0x39, 0x33, 0x59, 0xff, 0x43, 0x3e, 0x6d, 0xff, 0x35, 0x30, 0x5c, 0xff, 0x1a, 0x14, 0x4c, 0xff, 0x13, 0x0a, 0x42, 0xff, 0x1d, 0x14, 0x45, 0xff, 0x22, 0x1c, 0x50, 0xff, 0x2c, 0x23, 0x55, 0xff, 0x4c, 0x44, 0x6f, 0xff, 0x48, 0x43, 0x69, 0xff, 0x30, 0x2a, 0x46, 0xff, 0x21, 0x16, 0x2c, 0xff, 0x3a, 0x39, 0x57, 0xff, 0x59, 0x61, 0x92, 0xff, 0x4f, 0x64, 0x93, 0xff, 0x47, 0x5c, 0x88, 0xff, 0x46, 0x54, 0x84, 0xff, 0x50, 0x60, 0x8e, 0xff, 0x50, 0x66, 0x98, 0xff, 0x49, 0x62, 0x96, 0xff, 0x46, 0x5c, 0x8d, 0xff, 0x3b, 0x4d, 0x78, 0xff, 0x21, 0x27, 0x3c, 0xff, 0x19, 0x13, 0x16, 0xff, 0x1e, 0x1a, 0x1f, 0xff, 0x16, 0x18, 0x22, 0xff, 0x30, 0x2f, 0x3a, 0xff, 0x20, 0x1c, 0x22, 0xff, 0x1a, 0x16, 0x1f, 0xff, 0x37, 0x36, 0x43, 0xff, 0x31, 0x33, 0x43, 0xff, 0x25, 0x29, 0x38, 0xff, 0x3a, 0x42, 0x52, 0xff, 0x53, 0x5d, 0x71, 0xff, 0x34, 0x3d, 0x51, 0xff, 0x2e, 0x36, 0x47, 0xff, 0x3f, 0x47, 0x59, 0xff, 0x42, 0x4d, 0x65, 0xff, 0x45, 0x55, 0x74, 0xff, 0x60, 0x72, 0x95, 0xff, 0x76, 0x8a, 0xad, 0xff, 0x85, 0x98, 0xbc, 0xff, 0x88, 0x9c, 0xc1, 0xff, 0x7b, 0x96, 0xb8, 0xff, 0x8f, 0xa6, 0xbf, 0xff, 0xb4, 0xb7, 0xbd, 0xff, 0x9e, 0xa3, 0xb1, 0xff, 0x94, 0xa6, 0xc2, 0xff, 0xb4, 0xc0, 0xcf, 0xff, 0xec, 0xee, 0xeb, 0xff, 0xd3, 0xd0, 0xd4, 0xff, 0xeb, 0xe8, 0xef, 0xff, 0xeb, 0xe9, 0xee, 0xff, 0xf8, 0xfb, 0xfb, 0xff, 0xfa, 0xfc, 0xfd, 0xff, 0xfb, 0xfb, 0xfc, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xbe,
    0x52, 0x6d, 0x8b, 0xaa, 0x56, 0x76, 0x96, 0xff, 0x6e, 0x84, 0xa3, 0xff, 0x78, 0x87, 0x94, 0xff, 0x3d, 0x4e, 0x66, 0xff, 0x33, 0x48, 0x69, 0xff, 0x6a, 0x7c, 0x8f, 0xff, 0x9c, 0xb1, 0xc6, 0xff, 0x99, 0xae, 0xc2, 0xff, 0x93, 0xa8, 0xb9, 0xff, 0x8e, 0xa1, 0xb2, 0xff, 0x87, 0x97, 0xa6, 0xff, 0x78, 0x86, 0x98, 0xff, 0x73, 0x84, 0x9c, 0xff, 0x72, 0x88, 0xa4, 0xff, 0x72, 0x8a, 0xa8, 0xff, 0x7a, 0x93, 0xb4, 0xff, 0x56, 0x70, 0x92, 0xff, 0x4c, 0x65, 0x84, 0xff, 0x56, 0x6a, 0x84, 0xff, 0x52, 0x63, 0x7f, 0xff, 0x69, 0x74, 0x8c, 0xff, 0x72, 0x81, 0x98, 0xff, 0x49, 0x5f, 0x79, 0xff, 0x38, 0x4d, 0x68, 0xff, 0x33, 0x45, 0x60, 0xff, 0x27, 0x35, 0x51, 0xff, 0x2b, 0x37, 0x53, 0xff, 0x25, 0x31, 0x4f, 0xff, 0x1f, 0x29, 0x46, 0xff, 0x22, 0x2b, 0x42, 0xff, 0x1f, 0x26, 0x3a, 0xff, 0x16, 0x1a, 0x30, 0xff, 0x18, 0x19, 0x2d, 0xff, 0x22, 0x23, 0x37, 0xff, 0x1c, 0x1f, 0x32, 0xff, 0x15, 0x13, 0x20, 0xff, 0x11, 0x0c, 0x10, 0xff, 0x0d, 0x08, 0x08, 0xff, 0x0a, 0x04, 0x05, 0xff, 0x00, 0x00, 0x00, 0xff, 0x07, 0x00, 0x00, 0xff, 0x17, 0x0f, 0x10, 0xff, 0x76, 0x84, 0xaf, 0xff, 0x87, 0x9f, 0xdf, 0xff, 0x7e, 0x92, 0xc6, 0xff, 0x7c, 0x91, 0xc6, 0xff, 0x7e, 0x93, 0xc7, 0xff, 0x78, 0x8c, 0xc4, 0xff, 0x5d, 0x74, 0xa9, 0xff, 0x32, 0x49, 0x79, 0xff, 0x3e, 0x50, 0x7b, 0xff, 0x68, 0x75, 0xa6, 0xff, 0x67, 0x72, 0xa6, 0xff, 0x53, 0x5a, 0x8c, 0xff, 0x38, 0x38, 0x60, 0xff, 0x2c, 0x28, 0x4b, 0xff, 0x42, 0x3d, 0x68, 0xff, 0x64, 0x64, 0x8a, 0xff, 0x6b, 0x6c, 0x8e, 0xff, 0x68, 0x67, 0x89, 0xff, 0x5c, 0x5e, 0x80, 0xff, 0x51, 0x56, 0x78, 0xff, 0x60, 0x64, 0x85, 0xff, 0x6b, 0x6b, 0x8c, 0xff, 0x70, 0x70, 0x8e, 0xff, 0x5f, 0x5e, 0x80, 0xff, 0x5f, 0x5e, 0x92, 0xff, 0x73, 0x74, 0xb0, 0xff, 0x6b, 0x71, 0xaa, 0xff, 0x5d, 0x6c, 0x9a, 0xff, 0x57, 0x6b, 0x97, 0xff, 0x4c, 0x5f, 0x8e, 0xff, 0x47, 0x58, 0x89, 0xff, 0x4c, 0x60, 0x8f, 0xff, 0x48, 0x5f, 0x8b, 0xff, 0x41, 0x59, 0x85, 0xff, 0x34, 0x4a, 0x78, 0xff, 0x20, 0x2a, 0x4b, 0xff, 0x14, 0x12, 0x1d, 0xff, 0x25, 0x23, 0x30, 0xff, 0x28, 0x2e, 0x38, 0xff, 0x28, 0x2c, 0x3d, 0xff, 0x2b, 0x2b, 0x34, 0xff, 0x0f, 0x0f, 0x07, 0xff, 0x1d, 0x19, 0x1c, 0xff, 0x37, 0x36, 0x44, 0xff, 0x32, 0x35, 0x43, 0xff, 0x20, 0x20, 0x30, 0xff, 0x30, 0x34, 0x45, 0xff, 0x4e, 0x58, 0x6c, 0xff, 0x4b, 0x54, 0x69, 0xff, 0x2f, 0x33, 0x45, 0xff, 0x37, 0x3b, 0x4b, 0xff, 0x38, 0x42, 0x56, 0xff, 0x45, 0x53, 0x6e, 0xff, 0x5b, 0x6b, 0x8e, 0xff, 0x5f, 0x74, 0x99, 0xff, 0x80, 0x94, 0xb5, 0xff, 0x8f, 0xa0, 0xbc, 0xff, 0x81, 0x9b, 0xba, 0xff, 0x92, 0xa4, 0xbf, 0xff, 0xb8, 0xc1, 0xcf, 0xff, 0xbd, 0xc5, 0xcf, 0xff, 0x96, 0xa2, 0xb5, 0xff, 0x9b, 0xaa, 0xc4, 0xff, 0xcd, 0xd2, 0xe6, 0xff, 0xd7, 0xd9, 0xe1, 0xff, 0xd7, 0xd7, 0xdd, 0xff, 0xe8, 0xe6, 0xe6, 0xff, 0xf6, 0xf5, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xab,
    0x50, 0x6a, 0x8b, 0x8b, 0x55, 0x75, 0x96, 0xff, 0x68, 0x7e, 0x9c, 0xff, 0x77, 0x86, 0x92, 0xff, 0x42, 0x53, 0x6c, 0xff, 0x30, 0x44, 0x63, 0xff, 0x66, 0x75, 0x84, 0xff, 0x9e, 0xb1, 0xc2, 0xff, 0x9c, 0xb2, 0xc4, 0xff, 0x92, 0xa7, 0xb9, 0xff, 0x8d, 0xa0, 0xb2, 0xff, 0x88, 0x98, 0xa8, 0xff, 0x7b, 0x8a, 0x9c, 0xff, 0x6f, 0x81, 0x98, 0xff, 0x6d, 0x84, 0x9f, 0xff, 0x78, 0x91, 0xae, 0xff, 0x5f, 0x79, 0x99, 0xff, 0x5b, 0x75, 0x95, 0xff, 0x55, 0x6f, 0x91, 0xff, 0x4a, 0x62, 0x81, 0xff, 0x5e, 0x6a, 0x81, 0xff, 0x77, 0x80, 0x94, 0xff, 0x5b, 0x6a, 0x82, 0xff, 0x3a, 0x4f, 0x6a, 0xff, 0x29, 0x3e, 0x59, 0xff, 0x2c, 0x3e, 0x59, 0xff, 0x2e, 0x3b, 0x57, 0xff, 0x31, 0x3c, 0x59, 0xff, 0x27, 0x34, 0x50, 0xff, 0x1c, 0x27, 0x43, 0xff, 0x1f, 0x27, 0x3f, 0xff, 0x1c, 0x22, 0x37, 0xff, 0x1a, 0x1e, 0x34, 0xff, 0x18, 0x19, 0x2d, 0xff, 0x19, 0x1b, 0x2d, 0xff, 0x1d, 0x21, 0x30, 0xff, 0x16, 0x15, 0x20, 0xff, 0x12, 0x0d, 0x10, 0xff, 0x0c, 0x06, 0x06, 0xff, 0x0c, 0x07, 0x07, 0xff, 0x0c, 0x03, 0x04, 0xff, 0x0a, 0x00, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x41, 0x46, 0x5c, 0xff, 0x8a, 0xa3, 0xdb, 0xff, 0x7a, 0x92, 0xc7, 0xff, 0x79, 0x8c, 0xc1, 0xff, 0x79, 0x8d, 0xc2, 0xff, 0x79, 0x8d, 0xc5, 0xff, 0x69, 0x80, 0xb4, 0xff, 0x3f, 0x56, 0x88, 0xff, 0x3d, 0x51, 0x82, 0xff, 0x6c, 0x7c, 0xa8, 0xff, 0x71, 0x84, 0xaf, 0xff, 0x5d, 0x6f, 0xa1, 0xff, 0x5c, 0x6a, 0x9f, 0xff, 0x60, 0x63, 0xa0, 0xff, 0x64, 0x5e, 0xa0, 0xff, 0x6e, 0x68, 0xa3, 0xff, 0x69, 0x64, 0x9d, 0xff, 0x71, 0x6f, 0xa0, 0xff, 0x78, 0x79, 0xa6, 0xff, 0x7a, 0x7c, 0xa8, 0xff, 0x71, 0x73, 0x9f, 0xff, 0x69, 0x6e, 0xa2, 0xff, 0x62, 0x65, 0xa3, 0xff, 0x65, 0x66, 0xa8, 0xff, 0x6c, 0x72, 0xac, 0xff, 0x6f, 0x7a, 0xaf, 0xff, 0x66, 0x78, 0xa8, 0xff, 0x62, 0x76, 0xa2, 0xff, 0x5e, 0x6f, 0x9c, 0xff, 0x4f, 0x65, 0x93, 0xff, 0x48, 0x5f, 0x8f, 0xff, 0x47, 0x5f, 0x8d, 0xff, 0x3f, 0x59, 0x82, 0xff, 0x40, 0x50, 0x7c, 0xff, 0x2d, 0x3f, 0x6d, 0xff, 0x12, 0x19, 0x2a, 0xff, 0x0d, 0x08, 0x07, 0xff, 0x18, 0x19, 0x1e, 0xff, 0x21, 0x24, 0x2b, 0xff, 0x3b, 0x3d, 0x54, 0xff, 0x3d, 0x40, 0x4f, 0xff, 0x1d, 0x20, 0x21, 0xff, 0x1c, 0x1a, 0x1f, 0xff, 0x26, 0x26, 0x2f, 0xff, 0x33, 0x37, 0x41, 0xff, 0x28, 0x29, 0x37, 0xff, 0x2b, 0x2f, 0x41, 0xff, 0x3c, 0x46, 0x5a, 0xff, 0x5c, 0x65, 0x7b, 0xff, 0x48, 0x4e, 0x60, 0xff, 0x32, 0x36, 0x45, 0xff, 0x49, 0x4f, 0x60, 0xff, 0x50, 0x5b, 0x73, 0xff, 0x53, 0x63, 0x85, 0xff, 0x5d, 0x72, 0x97, 0xff, 0x62, 0x78, 0x99, 0xff, 0x68, 0x80, 0xa2, 0xff, 0x70, 0x85, 0xad, 0xff, 0xb1, 0xbb, 0xd9, 0xff, 0xa5, 0xb7, 0xcb, 0xff, 0xb5, 0xc3, 0xd6, 0xff, 0xb0, 0xb3, 0xc6, 0xff, 0x99, 0xa8, 0xc2, 0xff, 0x96, 0xac, 0xc4, 0xff, 0xa6, 0xb9, 0xce, 0xff, 0xba, 0xc4, 0xd0, 0xff, 0xc6, 0xc8, 0xcc, 0xff, 0xc6, 0xc8, 0xcb, 0xff, 0xe3, 0xe6, 0xeb, 0xff, 0xf6, 0xf7, 0xf9, 0xff, 0xfe, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0x8b,
    0x4e, 0x6b, 0x87, 0x6b, 0x53, 0x74, 0x95, 0xff, 0x64, 0x76, 0x95, 0xff, 0x76, 0x84, 0x90, 0xff, 0x45, 0x56, 0x68, 0xff, 0x2a, 0x3c, 0x5b, 0xff, 0x5e, 0x6c, 0x7e, 0xff, 0x9b, 0xb1, 0xc0, 0xff, 0xa0, 0xb6, 0xc9, 0xff, 0x94, 0xa9, 0xbc, 0xff, 0x8b, 0x9f, 0xb0, 0xff, 0x8a, 0x97, 0xa8, 0xff, 0x85, 0x95, 0xac, 0xff, 0x79, 0x8b, 0xa5, 0xff, 0x69, 0x7a, 0x95, 0xff, 0x69, 0x7e, 0x9c, 0xff, 0x5d, 0x74, 0x92, 0xff, 0x5f, 0x75, 0x91, 0xff, 0x55, 0x6e, 0x8d, 0xff, 0x49, 0x62, 0x81, 0xff, 0x67, 0x78, 0x8b, 0xff, 0x66, 0x71, 0x85, 0xff, 0x40, 0x4e, 0x68, 0xff, 0x33, 0x44, 0x61, 0xff, 0x3f, 0x54, 0x70, 0xff, 0x4c, 0x5e, 0x78, 0xff, 0x37, 0x45, 0x5e, 0xff, 0x2c, 0x36, 0x52, 0xff, 0x23, 0x30, 0x48, 0xff, 0x1f, 0x2b, 0x43, 0xff, 0x16, 0x1e, 0x37, 0xff, 0x17, 0x1b, 0x35, 0xff, 0x20, 0x26, 0x3c, 0xff, 0x16, 0x1a, 0x2d, 0xff, 0x18, 0x1b, 0x2d, 0xff, 0x19, 0x1f, 0x2a, 0xff, 0x14, 0x15, 0x1a, 0xff, 0x14, 0x0f, 0x10, 0xff, 0x0c, 0x06, 0x07, 0xff, 0x0c, 0x07, 0x08, 0xff, 0x0a, 0x04, 0x07, 0xff, 0x07, 0x03, 0x07, 0xff, 0x04, 0x00, 0x00, 0xff, 0x0c, 0x07, 0x0c, 0xff, 0x69, 0x7b, 0xa1, 0xff, 0x80, 0x9b, 0xcd, 0xff, 0x77, 0x89, 0xbc, 0xff, 0x73, 0x89, 0xbf, 0xff, 0x75, 0x8a, 0xc1, 0xff, 0x70, 0x86, 0xbb, 0xff, 0x58, 0x6e, 0xa2, 0xff, 0x46, 0x59, 0x8a, 0xff, 0x6a, 0x7a, 0xa6, 0xff, 0x7b, 0x8d, 0xb8, 0xff, 0x78, 0x89, 0xb8, 0xff, 0x6f, 0x7e, 0xac, 0xff, 0x6b, 0x73, 0xa1, 0xff, 0x73, 0x74, 0xa6, 0xff, 0x78, 0x73, 0xaf, 0xff, 0x69, 0x65, 0xac, 0xff, 0x60, 0x5a, 0xa6, 0xff, 0x5f, 0x5b, 0xa4, 0xff, 0x5d, 0x5b, 0xa0, 0xff, 0x5a, 0x56, 0x9d, 0xff, 0x58, 0x5a, 0x9d, 0xff, 0x59, 0x5e, 0xa0, 0xff, 0x5a, 0x5f, 0x9d, 0xff, 0x5b, 0x67, 0x9a, 0xff, 0x6a, 0x79, 0xa5, 0xff, 0x73, 0x84, 0xb1, 0xff, 0x67, 0x79, 0xa9, 0xff, 0x5d, 0x6d, 0x9c, 0xff, 0x52, 0x66, 0x98, 0xff, 0x4b, 0x62, 0x93, 0xff, 0x45, 0x5f, 0x8a, 0xff, 0x3a, 0x53, 0x7a, 0xff, 0x39, 0x48, 0x74, 0xff, 0x22, 0x2c, 0x4e, 0xff, 0x0b, 0x0d, 0x10, 0xff, 0x18, 0x12, 0x0f, 0xff, 0x17, 0x10, 0x12, 0xff, 0x17, 0x15, 0x1d, 0xff, 0x26, 0x25, 0x32, 0xff, 0x2e, 0x2f, 0x39, 0xff, 0x42, 0x48, 0x58, 0xff, 0x24, 0x26, 0x34, 0xff, 0x1d, 0x18, 0x1a, 0xff, 0x28, 0x2c, 0x33, 0xff, 0x31, 0x35, 0x44, 0xff, 0x31, 0x36, 0x47, 0xff, 0x35, 0x3d, 0x51, 0xff, 0x41, 0x4a, 0x5e, 0xff, 0x4c, 0x54, 0x68, 0xff, 0x3a, 0x42, 0x54, 0xff, 0x42, 0x4c, 0x5d, 0xff, 0x45, 0x51, 0x67, 0xff, 0x4b, 0x5a, 0x78, 0xff, 0x58, 0x6d, 0x8e, 0xff, 0x51, 0x66, 0x89, 0xff, 0x52, 0x69, 0x8c, 0xff, 0x64, 0x79, 0xa1, 0xff, 0x82, 0x93, 0xb4, 0xff, 0x9a, 0xaa, 0xc0, 0xff, 0xbc, 0xc8, 0xda, 0xff, 0xab, 0xb2, 0xc2, 0xff, 0xbc, 0xc2, 0xc9, 0xff, 0xbc, 0xc5, 0xcf, 0xff, 0x96, 0xa7, 0xc5, 0xff, 0xa5, 0xb4, 0xc7, 0xff, 0xb1, 0xbc, 0xca, 0xff, 0xc5, 0xca, 0xd6, 0xff, 0xe8, 0xe9, 0xea, 0xff, 0xfb, 0xfa, 0xf9, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0x6b,
    0x51, 0x69, 0x7d, 0x4b, 0x54, 0x73, 0x97, 0xff, 0x55, 0x6a, 0x84, 0xff, 0x6b, 0x7b, 0x83, 0xff, 0x43, 0x56, 0x63, 0xff, 0x1e, 0x32, 0x4f, 0xff, 0x54, 0x62, 0x73, 0xff, 0x9e, 0xaf, 0xbc, 0xff, 0xa6, 0xb7, 0xc5, 0xff, 0x97, 0xa6, 0xb3, 0xff, 0x8a, 0x9b, 0xa6, 0xff, 0x84, 0x8f, 0x9e, 0xff, 0x88, 0x95, 0xad, 0xff, 0x87, 0x98, 0xb3, 0xff, 0x63, 0x72, 0x90, 0xff, 0x4f, 0x61, 0x82, 0xff, 0x5b, 0x71, 0x8e, 0xff, 0x55, 0x6c, 0x87, 0xff, 0x4e, 0x64, 0x80, 0xff, 0x56, 0x6b, 0x8a, 0xff, 0x64, 0x77, 0x93, 0xff, 0x4a, 0x5a, 0x72, 0xff, 0x3c, 0x4c, 0x66, 0xff, 0x45, 0x59, 0x77, 0xff, 0x5a, 0x70, 0x8c, 0xff, 0x4b, 0x5d, 0x77, 0xff, 0x34, 0x43, 0x5b, 0xff, 0x27, 0x31, 0x4d, 0xff, 0x26, 0x32, 0x4a, 0xff, 0x20, 0x2d, 0x41, 0xff, 0x19, 0x22, 0x37, 0xff, 0x1b, 0x21, 0x38, 0xff, 0x20, 0x26, 0x3c, 0xff, 0x13, 0x18, 0x2b, 0xff, 0x1c, 0x1f, 0x31, 0xff, 0x19, 0x1e, 0x2a, 0xff, 0x10, 0x10, 0x16, 0xff, 0x14, 0x10, 0x10, 0xff, 0x0f, 0x09, 0x09, 0xff, 0x0f, 0x0a, 0x0a, 0xff, 0x0d, 0x08, 0x08, 0xff, 0x09, 0x04, 0x05, 0xff, 0x0d, 0x09, 0x08, 0xff, 0x00, 0x00, 0x00, 0xff, 0x28, 0x2a, 0x3e, 0xff, 0x7b, 0x90, 0xbd, 0xff, 0x76, 0x8d, 0xc1, 0xff, 0x6f, 0x88, 0xbb, 0xff, 0x6e, 0x87, 0xb7, 0xff, 0x71, 0x87, 0xba, 0xff, 0x6c, 0x82, 0xb5, 0xff, 0x58, 0x6e, 0x9e, 0xff, 0x62, 0x75, 0xa1, 0xff, 0x7b, 0x8e, 0xb9, 0xff, 0x79, 0x8b, 0xb8, 0xff, 0x82, 0x90, 0xbe, 0xff, 0x85, 0x91, 0xbf, 0xff, 0x82, 0x89, 0xb9, 0xff, 0x7a, 0x7e, 0xb3, 0xff, 0x67, 0x70, 0xa9, 0xff, 0x5f, 0x67, 0xa4, 0xff, 0x59, 0x5f, 0x9d, 0xff, 0x56, 0x5c, 0x98, 0xff, 0x59, 0x60, 0x9b, 0xff, 0x52, 0x5d, 0x96, 0xff, 0x52, 0x5c, 0x95, 0xff, 0x57, 0x63, 0x9b, 0xff, 0x5e, 0x70, 0xa4, 0xff, 0x6f, 0x81, 0xb1, 0xff, 0x6d, 0x80, 0xaf, 0xff, 0x60, 0x74, 0xa3, 0xff, 0x59, 0x6d, 0x9c, 0xff, 0x54, 0x69, 0x9a, 0xff, 0x4c, 0x63, 0x93, 0xff, 0x3f, 0x58, 0x83, 0xff, 0x36, 0x4a, 0x77, 0xff, 0x29, 0x35, 0x59, 0xff, 0x11, 0x12, 0x20, 0xff, 0x13, 0x06, 0x07, 0xff, 0x21, 0x16, 0x17, 0xff, 0x15, 0x10, 0x16, 0xff, 0x22, 0x24, 0x30, 0xff, 0x1b, 0x18, 0x1c, 0xff, 0x16, 0x13, 0x14, 0xff, 0x2c, 0x31, 0x3e, 0xff, 0x33, 0x3a, 0x46, 0xff, 0x1d, 0x1b, 0x1a, 0xff, 0x23, 0x20, 0x27, 0xff, 0x2d, 0x30, 0x40, 0xff, 0x32, 0x38, 0x49, 0xff, 0x3b, 0x42, 0x55, 0xff, 0x2c, 0x35, 0x48, 0xff, 0x37, 0x41, 0x56, 0xff, 0x5b, 0x63, 0x77, 0xff, 0x4d, 0x54, 0x68, 0xff, 0x3e, 0x49, 0x5f, 0xff, 0x50, 0x5d, 0x7a, 0xff, 0x51, 0x61, 0x85, 0xff, 0x5d, 0x6e, 0x94, 0xff, 0x62, 0x70, 0x94, 0xff, 0x83, 0x93, 0xad, 0xff, 0x8e, 0xa1, 0xba, 0xff, 0x93, 0xa3, 0xbb, 0xff, 0xbf, 0xc9, 0xda, 0xff, 0xc9, 0xd1, 0xd9, 0xff, 0xdc, 0xdb, 0xd9, 0xff, 0xcb, 0xc8, 0xcd, 0xff, 0x9f, 0xa5, 0xb3, 0xff, 0xcd, 0xd2, 0xdb, 0xff, 0xd6, 0xdb, 0xe3, 0xff, 0xbb, 0xc2, 0xc7, 0xff, 0xe7, 0xe9, 0xe8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfb, 0xfb, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0x4b,
    0x55, 0x64, 0x7b, 0x21, 0x4e, 0x6c, 0x92, 0xff, 0x49, 0x61, 0x72, 0xff, 0x66, 0x72, 0x75, 0xff, 0x45, 0x57, 0x60, 0xff, 0x12, 0x2b, 0x44, 0xff, 0x4c, 0x5a, 0x69, 0xff, 0xa2, 0xaa, 0xb3, 0xff, 0xa9, 0xb2, 0xba, 0xff, 0x96, 0xa0, 0xa8, 0xff, 0x88, 0x94, 0x9c, 0xff, 0x7d, 0x88, 0x92, 0xff, 0x84, 0x8f, 0xa1, 0xff, 0x7d, 0x8f, 0xa2, 0xff, 0x68, 0x7a, 0x8f, 0xff, 0x5e, 0x74, 0x8c, 0xff, 0x54, 0x6a, 0x85, 0xff, 0x58, 0x6e, 0x89, 0xff, 0x4f, 0x63, 0x7f, 0xff, 0x55, 0x67, 0x86, 0xff, 0x4e, 0x63, 0x85, 0xff, 0x4d, 0x60, 0x7b, 0xff, 0x58, 0x6c, 0x84, 0xff, 0x50, 0x66, 0x82, 0xff, 0x46, 0x5a, 0x77, 0xff, 0x42, 0x54, 0x6e, 0xff, 0x2b, 0x39, 0x52, 0xff, 0x2b, 0x36, 0x51, 0xff, 0x28, 0x35, 0x4b, 0xff, 0x13, 0x21, 0x33, 0xff, 0x1f, 0x2a, 0x3a, 0xff, 0x2f, 0x37, 0x49, 0xff, 0x20, 0x27, 0x3c, 0xff, 0x15, 0x1a, 0x2d, 0xff, 0x19, 0x1b, 0x2d, 0xff, 0x13, 0x17, 0x23, 0xff, 0x12, 0x12, 0x19, 0xff, 0x16, 0x12, 0x13, 0xff, 0x10, 0x0c, 0x0c, 0xff, 0x10, 0x0c, 0x0d, 0xff, 0x0f, 0x0a, 0x0b, 0xff, 0x0b, 0x05, 0x06, 0xff, 0x0b, 0x05, 0x08, 0xff, 0x0f, 0x09, 0x05, 0xff, 0x01, 0x00, 0x00, 0xff, 0x44, 0x4a, 0x64, 0xff, 0x7b, 0x8a, 0xc7, 0xff, 0x71, 0x84, 0xb8, 0xff, 0x6e, 0x86, 0xb2, 0xff, 0x70, 0x87, 0xb8, 0xff, 0x74, 0x8c, 0xbb, 0xff, 0x6c, 0x86, 0xb5, 0xff, 0x61, 0x79, 0xa5, 0xff, 0x71, 0x86, 0xb2, 0xff, 0x7a, 0x8b, 0xb9, 0xff, 0x7f, 0x8c, 0xbb, 0xff, 0x83, 0x96, 0xbf, 0xff, 0x8a, 0x9b, 0xc4, 0xff, 0x7f, 0x8d, 0xbb, 0xff, 0x6e, 0x7e, 0xb0, 0xff, 0x67, 0x76, 0xab, 0xff, 0x63, 0x71, 0xa9, 0xff, 0x62, 0x70, 0xa7, 0xff, 0x60, 0x6e, 0xa5, 0xff, 0x5b, 0x6a, 0x9e, 0xff, 0x59, 0x67, 0x9b, 0xff, 0x5f, 0x6e, 0xa3, 0xff, 0x62, 0x76, 0xa8, 0xff, 0x66, 0x7a, 0xa9, 0xff, 0x63, 0x79, 0xa7, 0xff, 0x59, 0x72, 0x9f, 0xff, 0x56, 0x6e, 0x9c, 0xff, 0x52, 0x6a, 0x98, 0xff, 0x4b, 0x62, 0x8c, 0xff, 0x36, 0x50, 0x78, 0xff, 0x2b, 0x3d, 0x6a, 0xff, 0x1d, 0x20, 0x39, 0xff, 0x0e, 0x0b, 0x09, 0xff, 0x19, 0x10, 0x11, 0xff, 0x17, 0x12, 0x16, 0xff, 0x1e, 0x21, 0x29, 0xff, 0x24, 0x23, 0x2e, 0xff, 0x1c, 0x14, 0x15, 0xff, 0x1c, 0x16, 0x17, 0xff, 0x1a, 0x1a, 0x23, 0xff, 0x3b, 0x41, 0x4c, 0xff, 0x28, 0x2a, 0x2d, 0xff, 0x1b, 0x16, 0x1f, 0xff, 0x29, 0x2a, 0x39, 0xff, 0x30, 0x35, 0x45, 0xff, 0x35, 0x3d, 0x51, 0xff, 0x38, 0x40, 0x56, 0xff, 0x44, 0x4e, 0x61, 0xff, 0x64, 0x6c, 0x7d, 0xff, 0x3e, 0x42, 0x56, 0xff, 0x3c, 0x41, 0x5b, 0xff, 0x57, 0x62, 0x7b, 0xff, 0x4c, 0x5a, 0x74, 0xff, 0x4d, 0x5e, 0x7b, 0xff, 0x4c, 0x60, 0x80, 0xff, 0x63, 0x74, 0x98, 0xff, 0x77, 0x8c, 0xb3, 0xff, 0x7c, 0x95, 0xb8, 0xff, 0x89, 0x9f, 0xbe, 0xff, 0xc3, 0xd3, 0xe4, 0xff, 0xee, 0xed, 0xf1, 0xff, 0xd1, 0xce, 0xd5, 0xff, 0xd2, 0xd7, 0xdd, 0xff, 0xbc, 0xbc, 0xc5, 0xff, 0xc0, 0xc0, 0xcd, 0xff, 0xb3, 0xbb, 0xc6, 0xff, 0xb3, 0xbd, 0xc8, 0xff, 0xf7, 0xfb, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0x21,
    0x00, 0x00, 0x00, 0x01, 0x4a, 0x69, 0x8c, 0xf2, 0x44, 0x56, 0x67, 0xff, 0x58, 0x60, 0x63, 0xff, 0x3e, 0x4b, 0x56, 0xff, 0x0d, 0x24, 0x39, 0xff, 0x42, 0x50, 0x5e, 0xff, 0x9e, 0xa3, 0xa6, 0xff, 0x9f, 0xa6, 0xab, 0xff, 0x8e, 0x99, 0xa5, 0xff, 0x8c, 0x98, 0xa2, 0xff, 0x85, 0x92, 0x9a, 0xff, 0x86, 0x96, 0xa1, 0xff, 0x71, 0x81, 0x8f, 0xff, 0x76, 0x86, 0x9a, 0xff, 0x73, 0x86, 0xa0, 0xff, 0x59, 0x6f, 0x86, 0xff, 0x52, 0x66, 0x7f, 0xff, 0x4e, 0x60, 0x80, 0xff, 0x4b, 0x5d, 0x81, 0xff, 0x46, 0x5a, 0x7a, 0xff, 0x5b, 0x6d, 0x89, 0xff, 0x5b, 0x6b, 0x85, 0xff, 0x45, 0x56, 0x71, 0xff, 0x44, 0x57, 0x72, 0xff, 0x3b, 0x49, 0x65, 0xff, 0x26, 0x30, 0x4d, 0xff, 0x29, 0x34, 0x4e, 0xff, 0x21, 0x2f, 0x43, 0xff, 0x10, 0x1f, 0x2f, 0xff, 0x22, 0x2e, 0x3e, 0xff, 0x38, 0x40, 0x51, 0xff, 0x20, 0x27, 0x39, 0xff, 0x18, 0x1d, 0x31, 0xff, 0x1a, 0x1c, 0x2f, 0xff, 0x10, 0x12, 0x1f, 0xff, 0x10, 0x0f, 0x1a, 0xff, 0x12, 0x11, 0x17, 0xff, 0x10, 0x0f, 0x12, 0xff, 0x0e, 0x0d, 0x11, 0xff, 0x13, 0x0e, 0x0f, 0xff, 0x10, 0x0a, 0x0a, 0xff, 0x0b, 0x06, 0x07, 0xff, 0x10, 0x0b, 0x0c, 0xff, 0x01, 0x00, 0x00, 0xff, 0x07, 0x03, 0x05, 0xff, 0x4e, 0x59, 0x7e, 0xff, 0x73, 0x85, 0xbd, 0xff, 0x72, 0x83, 0xb5, 0xff, 0x6f, 0x86, 0xb7, 0xff, 0x71, 0x8a, 0xb9, 0xff, 0x77, 0x8e, 0xbb, 0xff, 0x70, 0x87, 0xb6, 0xff, 0x6e, 0x85, 0xb3, 0xff, 0x76, 0x8a, 0xb7, 0xff, 0x7c, 0x8c, 0xba, 0xff, 0x80, 0x90, 0xb9, 0xff, 0x83, 0x94, 0xba, 0xff, 0x7a, 0x8b, 0xb4, 0xff, 0x6f, 0x7f, 0xb0, 0xff, 0x67, 0x76, 0xab, 0xff, 0x67, 0x75, 0xab, 0xff, 0x67, 0x76, 0xab, 0xff, 0x62, 0x70, 0xa5, 0xff, 0x5f, 0x6d, 0xa3, 0xff, 0x5d, 0x6c, 0xa2, 0xff, 0x5a, 0x6b, 0x9f, 0xff, 0x5a, 0x6f, 0x9d, 0xff, 0x5f, 0x73, 0xa2, 0xff, 0x60, 0x75, 0xa4, 0xff, 0x5a, 0x72, 0xa0, 0xff, 0x59, 0x71, 0x9f, 0xff, 0x52, 0x69, 0x98, 0xff, 0x43, 0x5a, 0x83, 0xff, 0x31, 0x48, 0x6e, 0xff, 0x24, 0x34, 0x59, 0xff, 0x0e, 0x09, 0x15, 0xff, 0x0f, 0x0b, 0x03, 0xff, 0x1a, 0x19, 0x21, 0xff, 0x21, 0x1e, 0x29, 0xff, 0x24, 0x28, 0x31, 0xff, 0x19, 0x19, 0x1a, 0xff, 0x24, 0x1b, 0x1d, 0xff, 0x1a, 0x17, 0x1b, 0xff, 0x1b, 0x1b, 0x25, 0xff, 0x37, 0x36, 0x48, 0xff, 0x2e, 0x2e, 0x3a, 0xff, 0x16, 0x15, 0x22, 0xff, 0x21, 0x23, 0x33, 0xff, 0x2b, 0x31, 0x40, 0xff, 0x35, 0x3c, 0x51, 0xff, 0x3f, 0x46, 0x60, 0xff, 0x48, 0x53, 0x63, 0xff, 0x45, 0x51, 0x5a, 0xff, 0x27, 0x2f, 0x3d, 0xff, 0x32, 0x38, 0x4b, 0xff, 0x44, 0x4d, 0x62, 0xff, 0x49, 0x56, 0x6e, 0xff, 0x57, 0x65, 0x80, 0xff, 0x53, 0x65, 0x83, 0xff, 0x5b, 0x6c, 0x94, 0xff, 0x6d, 0x81, 0xad, 0xff, 0x7a, 0x92, 0xba, 0xff, 0x6c, 0x89, 0xb0, 0xff, 0x8b, 0xa6, 0xc0, 0xff, 0xe7, 0xe0, 0xe4, 0xff, 0xff, 0xf7, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xf3, 0xed, 0xff, 0xc8, 0xc6, 0xcd, 0xff, 0xca, 0xca, 0xd7, 0xff, 0xc6, 0xc7, 0xd3, 0xff, 0xd8, 0xda, 0xde, 0xff, 0xfd, 0xfa, 0xf6, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xfa, 0xfb, 0xfc, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xf2, 0xff, 0xff, 0xff, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x3c, 0x56, 0x72, 0xc6, 0x31, 0x48, 0x60, 0xff, 0x2a, 0x40, 0x55, 0xff, 0x26, 0x3c, 0x50, 0xff, 0x22, 0x2f, 0x3b, 0xff, 0x43, 0x4c, 0x55, 0xff, 0x98, 0x9f, 0xa4, 0xff, 0xa4, 0xab, 0xb2, 0xff, 0x91, 0x9c, 0xa7, 0xff, 0x95, 0xa0, 0xad, 0xff, 0x95, 0xa0, 0xac, 0xff, 0x89, 0x96, 0xa6, 0xff, 0x77, 0x87, 0x97, 0xff, 0x71, 0x82, 0x96, 0xff, 0x65, 0x77, 0x93, 0xff, 0x55, 0x6d, 0x87, 0xff, 0x53, 0x6b, 0x87, 0xff, 0x56, 0x6a, 0x8c, 0xff, 0x55, 0x66, 0x8b, 0xff, 0x55, 0x6a, 0x89, 0xff, 0x5a, 0x6d, 0x88, 0xff, 0x51, 0x61, 0x7b, 0xff, 0x3f, 0x50, 0x6c, 0xff, 0x36, 0x4a, 0x64, 0xff, 0x31, 0x3f, 0x5b, 0xff, 0x2a, 0x34, 0x51, 0xff, 0x24, 0x2e, 0x4b, 0xff, 0x23, 0x2f, 0x4a, 0xff, 0x11, 0x1f, 0x33, 0xff, 0x1f, 0x28, 0x3b, 0xff, 0x35, 0x3b, 0x50, 0xff, 0x1a, 0x21, 0x34, 0xff, 0x13, 0x18, 0x2c, 0xff, 0x1e, 0x20, 0x33, 0xff, 0x13, 0x14, 0x21, 0xff, 0x0c, 0x0b, 0x15, 0xff, 0x10, 0x0f, 0x18, 0xff, 0x14, 0x15, 0x1e, 0xff, 0x0d, 0x0e, 0x16, 0xff, 0x11, 0x11, 0x13, 0xff, 0x10, 0x0e, 0x10, 0xff, 0x0a, 0x07, 0x09, 0xff, 0x13, 0x0e, 0x0f, 0xff, 0x0c, 0x06, 0x07, 0xff, 0x00, 0x00, 0x00, 0xff, 0x16, 0x1a, 0x22, 0xff, 0x51, 0x5b, 0x85, 0xff, 0x74, 0x85, 0xb1, 0xff, 0x75, 0x89, 0xb7, 0xff, 0x6f, 0x84, 0xb7, 0xff, 0x76, 0x8d, 0xbb, 0xff, 0x78, 0x90, 0xbe, 0xff, 0x75, 0x8b, 0xb9, 0xff, 0x76, 0x8a, 0xb7, 0xff, 0x79, 0x88, 0xb6, 0xff, 0x7e, 0x8d, 0xb9, 0xff, 0x81, 0x91, 0xbb, 0xff, 0x78, 0x88, 0xb3, 0xff, 0x6f, 0x7e, 0xaf, 0xff, 0x64, 0x72, 0xa8, 0xff, 0x62, 0x6f, 0xa6, 0xff, 0x61, 0x6e, 0xa3, 0xff, 0x60, 0x6d, 0xa2, 0xff, 0x58, 0x6b, 0xa0, 0xff, 0x54, 0x69, 0x9e, 0xff, 0x55, 0x69, 0x9c, 0xff, 0x58, 0x6c, 0x9a, 0xff, 0x5e, 0x71, 0xa0, 0xff, 0x5e, 0x74, 0xa2, 0xff, 0x5c, 0x74, 0xa1, 0xff, 0x5b, 0x72, 0xa0, 0xff, 0x51, 0x68, 0x98, 0xff, 0x39, 0x50, 0x7c, 0xff, 0x23, 0x3d, 0x66, 0xff, 0x19, 0x1d, 0x37, 0xff, 0x0c, 0x00, 0x00, 0xff, 0x0f, 0x13, 0x0e, 0xff, 0x1c, 0x1c, 0x2a, 0xff, 0x20, 0x1e, 0x2e, 0xff, 0x1c, 0x1c, 0x20, 0xff, 0x11, 0x10, 0x0e, 0xff, 0x1c, 0x18, 0x21, 0xff, 0x1d, 0x1e, 0x27, 0xff, 0x1f, 0x22, 0x31, 0xff, 0x30, 0x30, 0x45, 0xff, 0x2b, 0x2a, 0x35, 0xff, 0x22, 0x21, 0x2d, 0xff, 0x28, 0x2c, 0x3d, 0xff, 0x31, 0x3a, 0x4e, 0xff, 0x30, 0x3b, 0x51, 0xff, 0x44, 0x4e, 0x62, 0xff, 0x47, 0x52, 0x65, 0xff, 0x38, 0x41, 0x52, 0xff, 0x2d, 0x36, 0x43, 0xff, 0x39, 0x43, 0x4e, 0xff, 0x3b, 0x42, 0x52, 0xff, 0x40, 0x47, 0x5d, 0xff, 0x3e, 0x4b, 0x63, 0xff, 0x50, 0x62, 0x7c, 0xff, 0x53, 0x66, 0x88, 0xff, 0x5b, 0x71, 0x97, 0xff, 0x70, 0x8a, 0xac, 0xff, 0x76, 0x92, 0xb2, 0xff, 0x99, 0xae, 0xc8, 0xff, 0xcf, 0xd2, 0xd6, 0xff, 0xdf, 0xdd, 0xe0, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xe2, 0xe1, 0xdd, 0xff, 0xd2, 0xcf, 0xd1, 0xff, 0xe3, 0xe1, 0xe5, 0xff, 0xdb, 0xde, 0xdf, 0xff, 0xf2, 0xf0, 0xeb, 0xff, 0xf4, 0xf5, 0xf6, 0xff, 0xfa, 0xfb, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xc4, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x23, 0x3a, 0x51, 0x90, 0x1b, 0x2c, 0x42, 0xff, 0x19, 0x30, 0x46, 0xff, 0x52, 0x63, 0x75, 0xff, 0x55, 0x5c, 0x62, 0xff, 0x4e, 0x55, 0x5c, 0xff, 0x98, 0xa0, 0xa7, 0xff, 0xb0, 0xba, 0xc1, 0xff, 0x9d, 0xa9, 0xaf, 0xff, 0x9d, 0xa9, 0xb3, 0xff, 0x97, 0xa2, 0xb3, 0xff, 0x85, 0x93, 0xa6, 0xff, 0x7b, 0x8a, 0x9c, 0xff, 0x6d, 0x7d, 0x91, 0xff, 0x61, 0x74, 0x8f, 0xff, 0x57, 0x6e, 0x8b, 0xff, 0x64, 0x7c, 0x9c, 0xff, 0x63, 0x7a, 0x9e, 0xff, 0x54, 0x67, 0x8a, 0xff, 0x56, 0x6a, 0x88, 0xff, 0x56, 0x68, 0x85, 0xff, 0x41, 0x52, 0x6d, 0xff, 0x34, 0x45, 0x5e, 0xff, 0x38, 0x49, 0x64, 0xff, 0x32, 0x41, 0x5c, 0xff, 0x24, 0x30, 0x4e, 0xff, 0x23, 0x2e, 0x4e, 0xff, 0x25, 0x31, 0x4a, 0xff, 0x18, 0x25, 0x3b, 0xff, 0x1d, 0x29, 0x3e, 0xff, 0x24, 0x2c, 0x40, 0xff, 0x15, 0x1d, 0x2f, 0xff, 0x15, 0x19, 0x2d, 0xff, 0x1a, 0x1c, 0x2e, 0xff, 0x15, 0x18, 0x25, 0xff, 0x11, 0x12, 0x1b, 0xff, 0x0c, 0x0c, 0x15, 0xff, 0x18, 0x18, 0x23, 0xff, 0x15, 0x14, 0x21, 0xff, 0x11, 0x0e, 0x17, 0xff, 0x11, 0x0f, 0x18, 0xff, 0x0c, 0x09, 0x10, 0xff, 0x0f, 0x0c, 0x0b, 0xff, 0x0c, 0x09, 0x09, 0xff, 0x09, 0x02, 0x04, 0xff, 0x17, 0x10, 0x1f, 0xff, 0x21, 0x29, 0x47, 0xff, 0x42, 0x54, 0x77, 0xff, 0x6b, 0x7d, 0xa8, 0xff, 0x76, 0x8c, 0xbd, 0xff, 0x71, 0x89, 0xb8, 0xff, 0x77, 0x8f, 0xbe, 0xff, 0x79, 0x90, 0xbd, 0xff, 0x78, 0x8d, 0xb9, 0xff, 0x76, 0x89, 0xb6, 0xff, 0x7a, 0x89, 0xb9, 0xff, 0x7f, 0x8c, 0xbc, 0xff, 0x76, 0x85, 0xb4, 0xff, 0x6c, 0x7a, 0xad, 0xff, 0x62, 0x70, 0xa5, 0xff, 0x60, 0x6e, 0xa4, 0xff, 0x60, 0x6e, 0xa3, 0xff, 0x61, 0x6f, 0xa4, 0xff, 0x59, 0x6e, 0xa2, 0xff, 0x53, 0x6c, 0xa0, 0xff, 0x56, 0x6d, 0xa0, 0xff, 0x5d, 0x72, 0xa0, 0xff, 0x63, 0x78, 0xa7, 0xff, 0x64, 0x7a, 0xa9, 0xff, 0x60, 0x77, 0xa6, 0xff, 0x5a, 0x72, 0x9f, 0xff, 0x46, 0x5f, 0x8e, 0xff, 0x34, 0x4a, 0x74, 0xff, 0x1b, 0x2f, 0x4e, 0xff, 0x05, 0x09, 0x0e, 0xff, 0x10, 0x06, 0x00, 0xff, 0x0d, 0x0d, 0x11, 0xff, 0x28, 0x2c, 0x3a, 0xff, 0x22, 0x22, 0x31, 0xff, 0x1a, 0x18, 0x1c, 0xff, 0x1c, 0x1b, 0x1d, 0xff, 0x14, 0x14, 0x1d, 0xff, 0x1e, 0x20, 0x2b, 0xff, 0x26, 0x2b, 0x3b, 0xff, 0x2f, 0x32, 0x43, 0xff, 0x28, 0x28, 0x33, 0xff, 0x1b, 0x1b, 0x29, 0xff, 0x35, 0x3a, 0x50, 0xff, 0x3c, 0x46, 0x5f, 0xff, 0x36, 0x41, 0x58, 0xff, 0x32, 0x3a, 0x4a, 0xff, 0x2c, 0x34, 0x45, 0xff, 0x3a, 0x42, 0x5c, 0xff, 0x42, 0x48, 0x61, 0xff, 0x42, 0x49, 0x59, 0xff, 0x41, 0x49, 0x59, 0xff, 0x3f, 0x46, 0x5a, 0xff, 0x34, 0x3d, 0x54, 0xff, 0x4b, 0x5b, 0x76, 0xff, 0x55, 0x67, 0x86, 0xff, 0x64, 0x79, 0x9e, 0xff, 0x74, 0x8b, 0xaf, 0xff, 0x85, 0xa1, 0xbe, 0xff, 0x8d, 0xa6, 0xc3, 0xff, 0xb5, 0xc5, 0xcd, 0xff, 0xce, 0xcd, 0xd0, 0xff, 0xe8, 0xe3, 0xe8, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xdd, 0xd8, 0xdd, 0xff, 0xda, 0xdc, 0xdb, 0xff, 0xd9, 0xd8, 0xd3, 0xff, 0xdf, 0xdc, 0xdf, 0xff, 0xf9, 0xf8, 0xf7, 0xff, 0xf4, 0xf3, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0x90, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x0b, 0x1a, 0x22, 0x58, 0x01, 0x02, 0x04, 0xff, 0x27, 0x2d, 0x2f, 0xff, 0x86, 0x8e, 0x9c, 0xff, 0x73, 0x7d, 0x87, 0xff, 0x51, 0x5f, 0x66, 0xff, 0x8f, 0x9c, 0xa4, 0xff, 0xb2, 0xbe, 0xc5, 0xff, 0xa2, 0xad, 0xb2, 0xff, 0x99, 0xa5, 0xae, 0xff, 0x8f, 0x9d, 0xaf, 0xff, 0x80, 0x8f, 0xa3, 0xff, 0x7b, 0x8a, 0x9e, 0xff, 0x6b, 0x7a, 0x8f, 0xff, 0x69, 0x7d, 0x96, 0xff, 0x64, 0x79, 0x97, 0xff, 0x6c, 0x83, 0xa5, 0xff, 0x65, 0x7e, 0xa1, 0xff, 0x48, 0x5f, 0x7f, 0xff, 0x4d, 0x61, 0x7d, 0xff, 0x48, 0x5d, 0x79, 0xff, 0x36, 0x4a, 0x65, 0xff, 0x50, 0x5f, 0x77, 0xff, 0x47, 0x54, 0x6f, 0xff, 0x27, 0x35, 0x51, 0xff, 0x26, 0x35, 0x52, 0xff, 0x33, 0x3f, 0x5c, 0xff, 0x20, 0x2b, 0x3f, 0xff, 0x16, 0x1d, 0x2e, 0xff, 0x17, 0x1d, 0x30, 0xff, 0x19, 0x21, 0x34, 0xff, 0x13, 0x1b, 0x2e, 0xff, 0x1a, 0x1e, 0x32, 0xff, 0x1b, 0x1e, 0x30, 0xff, 0x14, 0x1a, 0x26, 0xff, 0x13, 0x16, 0x1f, 0xff, 0x0f, 0x10, 0x17, 0xff, 0x15, 0x14, 0x1e, 0xff, 0x19, 0x16, 0x26, 0xff, 0x12, 0x0d, 0x1a, 0xff, 0x11, 0x0c, 0x19, 0xff, 0x11, 0x0d, 0x17, 0xff, 0x12, 0x0f, 0x12, 0xff, 0x0e, 0x0b, 0x0e, 0xff, 0x07, 0x01, 0x02, 0xff, 0x17, 0x13, 0x27, 0xff, 0x23, 0x2e, 0x4c, 0xff, 0x22, 0x30, 0x51, 0xff, 0x39, 0x4b, 0x72, 0xff, 0x61, 0x78, 0xa4, 0xff, 0x72, 0x89, 0xb7, 0xff, 0x72, 0x89, 0xb6, 0xff, 0x79, 0x8f, 0xbc, 0xff, 0x7e, 0x93, 0xc0, 0xff, 0x7c, 0x93, 0xbf, 0xff, 0x7d, 0x8c, 0xbc, 0xff, 0x7e, 0x8b, 0xbb, 0xff, 0x78, 0x86, 0xb8, 0xff, 0x6f, 0x7c, 0xb1, 0xff, 0x63, 0x71, 0xa5, 0xff, 0x61, 0x6f, 0xa3, 0xff, 0x63, 0x71, 0xa6, 0xff, 0x63, 0x71, 0xa6, 0xff, 0x5d, 0x70, 0xa5, 0xff, 0x5a, 0x6f, 0xa4, 0xff, 0x5c, 0x71, 0xa4, 0xff, 0x63, 0x79, 0xaa, 0xff, 0x68, 0x7e, 0xaf, 0xff, 0x68, 0x7e, 0xaf, 0xff, 0x60, 0x77, 0xa6, 0xff, 0x53, 0x6c, 0x97, 0xff, 0x3f, 0x56, 0x84, 0xff, 0x2a, 0x37, 0x58, 0xff, 0x09, 0x0e, 0x1d, 0xff, 0x04, 0x05, 0x08, 0xff, 0x14, 0x0f, 0x0b, 0xff, 0x0f, 0x08, 0x0c, 0xff, 0x28, 0x2b, 0x36, 0xff, 0x36, 0x3a, 0x48, 0xff, 0x14, 0x13, 0x1e, 0xff, 0x1f, 0x1d, 0x26, 0xff, 0x20, 0x20, 0x27, 0xff, 0x23, 0x26, 0x30, 0xff, 0x23, 0x2a, 0x38, 0xff, 0x23, 0x2b, 0x38, 0xff, 0x24, 0x2a, 0x37, 0xff, 0x2a, 0x2e, 0x41, 0xff, 0x35, 0x3b, 0x56, 0xff, 0x40, 0x48, 0x62, 0xff, 0x30, 0x39, 0x4d, 0xff, 0x15, 0x17, 0x24, 0xff, 0x1d, 0x21, 0x2d, 0xff, 0x39, 0x41, 0x59, 0xff, 0x4c, 0x51, 0x69, 0xff, 0x51, 0x58, 0x6c, 0xff, 0x40, 0x4b, 0x5c, 0xff, 0x3a, 0x42, 0x56, 0xff, 0x3c, 0x43, 0x5c, 0xff, 0x40, 0x4b, 0x66, 0xff, 0x4f, 0x5c, 0x79, 0xff, 0x68, 0x79, 0x97, 0xff, 0x72, 0x86, 0xa6, 0xff, 0x7d, 0x93, 0xba, 0xff, 0x74, 0x92, 0xba, 0xff, 0x88, 0xa3, 0xbf, 0xff, 0xcc, 0xd1, 0xde, 0xff, 0xf5, 0xf1, 0xed, 0xff, 0xf5, 0xf0, 0xe9, 0xff, 0xc3, 0xc1, 0xd2, 0xff, 0xe0, 0xde, 0xe3, 0xff, 0xe5, 0xdc, 0xd8, 0xff, 0xe6, 0xde, 0xe4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfb, 0xf9, 0xff, 0xfe, 0xfe, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0x58, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x12, 0x09, 0x09, 0x1b, 0x0e, 0x18, 0x17, 0xff, 0x43, 0x51, 0x5d, 0xff, 0x84, 0x93, 0xa4, 0xff, 0x73, 0x7a, 0x84, 0xff, 0x52, 0x58, 0x60, 0xff, 0x8a, 0x90, 0x9a, 0xff, 0xaf, 0xb8, 0xc4, 0xff, 0xa0, 0xa8, 0xb5, 0xff, 0x99, 0xa3, 0xb0, 0xff, 0x93, 0xa0, 0xb0, 0xff, 0x84, 0x93, 0xa5, 0xff, 0x73, 0x83, 0x95, 0xff, 0x6c, 0x7c, 0x90, 0xff, 0x6d, 0x81, 0x99, 0xff, 0x69, 0x7e, 0x9c, 0xff, 0x6f, 0x87, 0xa9, 0xff, 0x62, 0x7b, 0x9d, 0xff, 0x3e, 0x55, 0x75, 0xff, 0x50, 0x63, 0x80, 0xff, 0x4e, 0x62, 0x7e, 0xff, 0x46, 0x5a, 0x76, 0xff, 0x47, 0x57, 0x6f, 0xff, 0x26, 0x35, 0x50, 0xff, 0x24, 0x33, 0x50, 0xff, 0x40, 0x4d, 0x69, 0xff, 0x40, 0x48, 0x62, 0xff, 0x1c, 0x24, 0x36, 0xff, 0x04, 0x09, 0x12, 0xff, 0x15, 0x1d, 0x27, 0xff, 0x1e, 0x2d, 0x40, 0xff, 0x18, 0x21, 0x35, 0xff, 0x1e, 0x21, 0x35, 0xff, 0x1c, 0x1f, 0x32, 0xff, 0x0d, 0x12, 0x1e, 0xff, 0x12, 0x13, 0x1c, 0xff, 0x19, 0x1c, 0x22, 0xff, 0x12, 0x16, 0x20, 0xff, 0x12, 0x14, 0x22, 0xff, 0x0f, 0x0c, 0x18, 0xff, 0x14, 0x0f, 0x1a, 0xff, 0x14, 0x10, 0x1a, 0xff, 0x15, 0x10, 0x18, 0xff, 0x16, 0x12, 0x19, 0xff, 0x0c, 0x04, 0x07, 0xff, 0x09, 0x05, 0x18, 0xff, 0x20, 0x2b, 0x4b, 0xff, 0x31, 0x3d, 0x5b, 0xff, 0x2a, 0x39, 0x5d, 0xff, 0x34, 0x48, 0x71, 0xff, 0x5c, 0x70, 0x9a, 0xff, 0x70, 0x82, 0xac, 0xff, 0x74, 0x89, 0xb4, 0xff, 0x7c, 0x91, 0xbf, 0xff, 0x81, 0x97, 0xc4, 0xff, 0x83, 0x92, 0xc2, 0xff, 0x81, 0x8e, 0xbe, 0xff, 0x7c, 0x8a, 0xbb, 0xff, 0x6f, 0x7d, 0xb1, 0xff, 0x60, 0x6e, 0xa1, 0xff, 0x60, 0x6e, 0xa2, 0xff, 0x64, 0x72, 0xa7, 0xff, 0x63, 0x71, 0xa6, 0xff, 0x62, 0x70, 0xa6, 0xff, 0x60, 0x6f, 0xa5, 0xff, 0x61, 0x71, 0xa6, 0xff, 0x63, 0x78, 0xad, 0xff, 0x67, 0x7b, 0xb1, 0xff, 0x67, 0x7c, 0xaf, 0xff, 0x5d, 0x74, 0xa3, 0xff, 0x48, 0x62, 0x90, 0xff, 0x2f, 0x44, 0x70, 0xff, 0x0e, 0x14, 0x2a, 0xff, 0x0a, 0x04, 0x06, 0xff, 0x14, 0x0c, 0x0d, 0xff, 0x14, 0x0d, 0x0f, 0xff, 0x15, 0x0e, 0x13, 0xff, 0x18, 0x17, 0x1e, 0xff, 0x31, 0x3a, 0x4d, 0xff, 0x1f, 0x1e, 0x2c, 0xff, 0x1f, 0x1c, 0x26, 0xff, 0x26, 0x25, 0x30, 0xff, 0x1b, 0x1e, 0x28, 0xff, 0x24, 0x2a, 0x37, 0xff, 0x25, 0x2d, 0x3d, 0xff, 0x29, 0x31, 0x43, 0xff, 0x32, 0x39, 0x4f, 0xff, 0x28, 0x31, 0x47, 0xff, 0x36, 0x41, 0x54, 0xff, 0x26, 0x30, 0x40, 0xff, 0x1c, 0x1c, 0x26, 0xff, 0x1c, 0x1e, 0x28, 0xff, 0x28, 0x2c, 0x3f, 0xff, 0x3d, 0x3f, 0x55, 0xff, 0x48, 0x53, 0x6c, 0xff, 0x4e, 0x5a, 0x6c, 0xff, 0x3b, 0x43, 0x57, 0xff, 0x45, 0x4b, 0x65, 0xff, 0x46, 0x50, 0x66, 0xff, 0x4e, 0x5a, 0x73, 0xff, 0x58, 0x65, 0x84, 0xff, 0x6e, 0x7e, 0xa2, 0xff, 0x60, 0x73, 0x97, 0xff, 0x62, 0x78, 0x9e, 0xff, 0x72, 0x8a, 0xb9, 0xff, 0xab, 0xbf, 0xd4, 0xff, 0xfe, 0xf9, 0xee, 0xff, 0xe6, 0xdc, 0xdc, 0xff, 0x8f, 0x9f, 0xb5, 0xff, 0xcf, 0xd3, 0xda, 0xff, 0xe1, 0xdf, 0xe0, 0xff, 0xf5, 0xf4, 0xfb, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xfd, 0xfc, 0xfa, 0xff, 0xfd, 0xfd, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xff, 0xff, 0xff, 0x1b, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x42, 0x4d, 0xd7, 0x5f, 0x79, 0x95, 0xff, 0x84, 0x96, 0xab, 0xff, 0x6e, 0x72, 0x7c, 0xff, 0x49, 0x45, 0x48, 0xff, 0x7f, 0x85, 0x8b, 0xff, 0xad, 0xbc, 0xc8, 0xff, 0x9f, 0xab, 0xb9, 0xff, 0x9a, 0xa7, 0xb8, 0xff, 0x93, 0xa2, 0xb4, 0xff, 0x8c, 0x9b, 0xad, 0xff, 0x7b, 0x8b, 0x9d, 0xff, 0x74, 0x85, 0x98, 0xff, 0x73, 0x84, 0x9c, 0xff, 0x62, 0x78, 0x94, 0xff, 0x5d, 0x76, 0x96, 0xff, 0x4b, 0x65, 0x86, 0xff, 0x3c, 0x52, 0x70, 0xff, 0x56, 0x69, 0x84, 0xff, 0x55, 0x64, 0x80, 0xff, 0x3f, 0x4c, 0x69, 0xff, 0x2f, 0x3c, 0x57, 0xff, 0x2e, 0x3a, 0x56, 0xff, 0x42, 0x4e, 0x6b, 0xff, 0x42, 0x4d, 0x68, 0xff, 0x24, 0x2d, 0x42, 0xff, 0x09, 0x10, 0x22, 0xff, 0x0b, 0x11, 0x1d, 0xff, 0x19, 0x25, 0x31, 0xff, 0x19, 0x29, 0x3e, 0xff, 0x19, 0x21, 0x37, 0xff, 0x15, 0x18, 0x2b, 0xff, 0x16, 0x19, 0x2a, 0xff, 0x10, 0x17, 0x25, 0xff, 0x0e, 0x15, 0x1f, 0xff, 0x10, 0x17, 0x1e, 0xff, 0x1b, 0x21, 0x29, 0xff, 0x1a, 0x1d, 0x2a, 0xff, 0x0d, 0x0c, 0x1a, 0xff, 0x0f, 0x0e, 0x18, 0xff, 0x12, 0x12, 0x1a, 0xff, 0x13, 0x10, 0x19, 0xff, 0x15, 0x13, 0x1d, 0xff, 0x15, 0x11, 0x15, 0xff, 0x0e, 0x0b, 0x15, 0xff, 0x14, 0x1e, 0x3e, 0xff, 0x2f, 0x3c, 0x5e, 0xff, 0x38, 0x43, 0x69, 0xff, 0x2f, 0x40, 0x6a, 0xff, 0x39, 0x4d, 0x73, 0xff, 0x5a, 0x6e, 0x93, 0xff, 0x6c, 0x80, 0xaa, 0xff, 0x74, 0x89, 0xb8, 0xff, 0x7e, 0x93, 0xc0, 0xff, 0x82, 0x94, 0xc3, 0xff, 0x81, 0x8f, 0xbf, 0xff, 0x7e, 0x8a, 0xbc, 0xff, 0x6f, 0x7d, 0xb1, 0xff, 0x62, 0x70, 0xa6, 0xff, 0x5f, 0x6c, 0xa4, 0xff, 0x60, 0x6e, 0xa5, 0xff, 0x61, 0x6e, 0xa5, 0xff, 0x61, 0x6d, 0xa2, 0xff, 0x60, 0x6c, 0xa0, 0xff, 0x5f, 0x6d, 0xa3, 0xff, 0x5c, 0x71, 0xa9, 0xff, 0x60, 0x74, 0xad, 0xff, 0x60, 0x74, 0xaa, 0xff, 0x59, 0x6d, 0x9c, 0xff, 0x3c, 0x50, 0x79, 0xff, 0x09, 0x17, 0x38, 0xff, 0x07, 0x06, 0x17, 0xff, 0x1b, 0x10, 0x13, 0xff, 0x11, 0x0a, 0x0a, 0xff, 0x12, 0x0d, 0x0f, 0xff, 0x15, 0x0f, 0x10, 0xff, 0x0b, 0x0a, 0x0a, 0xff, 0x2a, 0x2f, 0x45, 0xff, 0x2a, 0x2c, 0x3f, 0xff, 0x1a, 0x1b, 0x22, 0xff, 0x1f, 0x1c, 0x28, 0xff, 0x21, 0x1f, 0x2a, 0xff, 0x21, 0x26, 0x32, 0xff, 0x2c, 0x34, 0x46, 0xff, 0x30, 0x37, 0x4b, 0xff, 0x27, 0x2a, 0x3e, 0xff, 0x2b, 0x31, 0x42, 0xff, 0x33, 0x3c, 0x4b, 0xff, 0x29, 0x31, 0x40, 0xff, 0x28, 0x27, 0x36, 0xff, 0x1d, 0x1b, 0x2b, 0xff, 0x21, 0x21, 0x30, 0xff, 0x24, 0x27, 0x37, 0xff, 0x38, 0x42, 0x57, 0xff, 0x53, 0x60, 0x74, 0xff, 0x46, 0x51, 0x67, 0xff, 0x49, 0x53, 0x69, 0xff, 0x4e, 0x5b, 0x6d, 0xff, 0x49, 0x55, 0x69, 0xff, 0x54, 0x62, 0x82, 0xff, 0x71, 0x82, 0xab, 0xff, 0x60, 0x6c, 0x8f, 0xff, 0x69, 0x79, 0x9b, 0xff, 0x79, 0x8d, 0xb4, 0xff, 0x7f, 0x95, 0xb5, 0xff, 0xb7, 0xc5, 0xd5, 0xff, 0xb0, 0xb7, 0xc8, 0xff, 0xb0, 0xbe, 0xd3, 0xff, 0xe3, 0xe3, 0xe6, 0xff, 0xf0, 0xee, 0xe9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfb, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x47, 0x52, 0x8e, 0x6c, 0x85, 0x9f, 0xff, 0x87, 0x98, 0xae, 0xff, 0x6d, 0x74, 0x7d, 0xff, 0x45, 0x41, 0x42, 0xff, 0x75, 0x7f, 0x83, 0xff, 0xb3, 0xc5, 0xcf, 0xff, 0xa9, 0xb6, 0xc4, 0xff, 0x91, 0x9f, 0xb2, 0xff, 0x83, 0x91, 0xaa, 0xff, 0x87, 0x94, 0xac, 0xff, 0x80, 0x90, 0xa6, 0xff, 0x71, 0x80, 0x99, 0xff, 0x6f, 0x7b, 0x98, 0xff, 0x58, 0x6a, 0x87, 0xff, 0x45, 0x5b, 0x7a, 0xff, 0x51, 0x68, 0x88, 0xff, 0x47, 0x5b, 0x79, 0xff, 0x52, 0x65, 0x7f, 0xff, 0x67, 0x74, 0x8d, 0xff, 0x53, 0x5e, 0x77, 0xff, 0x3f, 0x4b, 0x64, 0xff, 0x46, 0x52, 0x6e, 0xff, 0x3a, 0x46, 0x62, 0xff, 0x20, 0x2d, 0x48, 0xff, 0x15, 0x21, 0x3a, 0xff, 0x0d, 0x15, 0x2a, 0xff, 0x0e, 0x15, 0x2b, 0xff, 0x10, 0x18, 0x2f, 0xff, 0x1a, 0x22, 0x37, 0xff, 0x19, 0x21, 0x36, 0xff, 0x0b, 0x12, 0x25, 0xff, 0x13, 0x18, 0x29, 0xff, 0x0e, 0x15, 0x23, 0xff, 0x10, 0x1b, 0x24, 0xff, 0x0c, 0x14, 0x1c, 0xff, 0x13, 0x17, 0x23, 0xff, 0x1c, 0x1f, 0x2f, 0xff, 0x13, 0x14, 0x25, 0xff, 0x0d, 0x0f, 0x1c, 0xff, 0x12, 0x15, 0x1e, 0xff, 0x11, 0x12, 0x1c, 0xff, 0x0d, 0x0e, 0x19, 0xff, 0x14, 0x15, 0x1d, 0xff, 0x17, 0x15, 0x1d, 0xff, 0x14, 0x1c, 0x36, 0xff, 0x26, 0x37, 0x5a, 0xff, 0x37, 0x47, 0x6e, 0xff, 0x36, 0x48, 0x73, 0xff, 0x2f, 0x44, 0x69, 0xff, 0x3c, 0x51, 0x75, 0xff, 0x58, 0x6e, 0x97, 0xff, 0x66, 0x7c, 0xab, 0xff, 0x72, 0x88, 0xb5, 0xff, 0x7e, 0x95, 0xc3, 0xff, 0x85, 0x96, 0xc5, 0xff, 0x81, 0x8e, 0xbe, 0xff, 0x72, 0x81, 0xb4, 0xff, 0x66, 0x73, 0xaa, 0xff, 0x60, 0x6d, 0xa6, 0xff, 0x5f, 0x6c, 0xa4, 0xff, 0x5f, 0x6c, 0xa3, 0xff, 0x5c, 0x6a, 0xa2, 0xff, 0x57, 0x65, 0x9d, 0xff, 0x55, 0x64, 0x9b, 0xff, 0x56, 0x6a, 0xa1, 0xff, 0x59, 0x6e, 0xa7, 0xff, 0x5c, 0x71, 0xa9, 0xff, 0x45, 0x57, 0x82, 0xff, 0x12, 0x1d, 0x3a, 0xff, 0x01, 0x0b, 0x1b, 0xff, 0x0d, 0x11, 0x19, 0xff, 0x0f, 0x0b, 0x0d, 0xff, 0x14, 0x0f, 0x10, 0xff, 0x15, 0x11, 0x12, 0xff, 0x15, 0x0d, 0x0e, 0xff, 0x0c, 0x06, 0x05, 0xff, 0x1e, 0x20, 0x2e, 0xff, 0x29, 0x2f, 0x40, 0xff, 0x1c, 0x1f, 0x25, 0xff, 0x20, 0x1c, 0x29, 0xff, 0x1c, 0x1b, 0x2a, 0xff, 0x14, 0x1a, 0x29, 0xff, 0x2a, 0x32, 0x46, 0xff, 0x39, 0x3f, 0x54, 0xff, 0x2a, 0x2c, 0x3e, 0xff, 0x24, 0x26, 0x35, 0xff, 0x1d, 0x21, 0x2f, 0xff, 0x22, 0x25, 0x34, 0xff, 0x2a, 0x29, 0x3a, 0xff, 0x24, 0x24, 0x35, 0xff, 0x27, 0x26, 0x34, 0xff, 0x27, 0x2a, 0x38, 0xff, 0x3a, 0x43, 0x54, 0xff, 0x4d, 0x59, 0x6e, 0xff, 0x52, 0x5d, 0x74, 0xff, 0x4b, 0x57, 0x6c, 0xff, 0x49, 0x57, 0x6c, 0xff, 0x45, 0x4f, 0x69, 0xff, 0x52, 0x5f, 0x7d, 0xff, 0x69, 0x7b, 0x9d, 0xff, 0x6b, 0x79, 0x9b, 0xff, 0x5d, 0x6f, 0x92, 0xff, 0x80, 0x95, 0xb7, 0xff, 0x7a, 0x8f, 0xb7, 0xff, 0x81, 0x96, 0xba, 0xff, 0xc2, 0xcb, 0xd4, 0xff, 0xfd, 0xfc, 0xfd, 0xff, 0xf1, 0xed, 0xeb, 0xff, 0xf3, 0xf2, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0x8e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2b, 0x3a, 0x48, 0x46, 0x6f, 0x87, 0xa2, 0xff, 0x89, 0x9b, 0xaf, 0xff, 0x6e, 0x74, 0x7d, 0xff, 0x46, 0x43, 0x45, 0xff, 0x6f, 0x78, 0x7d, 0xff, 0xb1, 0xc2, 0xcd, 0xff, 0xab, 0xb7, 0xc6, 0xff, 0x91, 0x9e, 0xaf, 0xff, 0x84, 0x93, 0xa6, 0xff, 0x7f, 0x8e, 0x9f, 0xff, 0x72, 0x83, 0x98, 0xff, 0x6a, 0x7b, 0x95, 0xff, 0x63, 0x72, 0x8e, 0xff, 0x5e, 0x6f, 0x8b, 0xff, 0x58, 0x6b, 0x8b, 0xff, 0x61, 0x76, 0x96, 0xff, 0x4f, 0x65, 0x83, 0xff, 0x4e, 0x61, 0x7c, 0xff, 0x6a, 0x79, 0x90, 0xff, 0x62, 0x6f, 0x84, 0xff, 0x42, 0x50, 0x66, 0xff, 0x34, 0x40, 0x5b, 0xff, 0x27, 0x32, 0x4f, 0xff, 0x19, 0x25, 0x42, 0xff, 0x19, 0x27, 0x43, 0xff, 0x1a, 0x25, 0x3d, 0xff, 0x10, 0x1b, 0x35, 0xff, 0x09, 0x12, 0x2b, 0xff, 0x23, 0x29, 0x3d, 0xff, 0x1a, 0x26, 0x3b, 0xff, 0x18, 0x22, 0x35, 0xff, 0x19, 0x20, 0x31, 0xff, 0x16, 0x1d, 0x2c, 0xff, 0x11, 0x1b, 0x25, 0xff, 0x13, 0x19, 0x24, 0xff, 0x14, 0x15, 0x25, 0xff, 0x19, 0x1a, 0x2d, 0xff, 0x1e, 0x22, 0x36, 0xff, 0x15, 0x1c, 0x2b, 0xff, 0x0f, 0x15, 0x22, 0xff, 0x0f, 0x11, 0x21, 0xff, 0x0f, 0x11, 0x21, 0xff, 0x12, 0x15, 0x23, 0xff, 0x12, 0x12, 0x1e, 0xff, 0x14, 0x17, 0x2d, 0xff, 0x21, 0x33, 0x53, 0xff, 0x34, 0x49, 0x70, 0xff, 0x37, 0x4c, 0x76, 0xff, 0x36, 0x4b, 0x71, 0xff, 0x34, 0x49, 0x6e, 0xff, 0x3f, 0x54, 0x7d, 0xff, 0x53, 0x67, 0x94, 0xff, 0x60, 0x74, 0xa1, 0xff, 0x6c, 0x84, 0xb1, 0xff, 0x80, 0x97, 0xc4, 0xff, 0x82, 0x93, 0xc3, 0xff, 0x73, 0x80, 0xb5, 0xff, 0x64, 0x70, 0xa9, 0xff, 0x5f, 0x6b, 0xa5, 0xff, 0x5f, 0x6c, 0xa4, 0xff, 0x5d, 0x6a, 0xa2, 0xff, 0x58, 0x68, 0xa3, 0xff, 0x53, 0x63, 0xa0, 0xff, 0x51, 0x62, 0x9d, 0xff, 0x53, 0x68, 0x9f, 0xff, 0x59, 0x6c, 0xa7, 0xff, 0x43, 0x56, 0x8a, 0xff, 0x0e, 0x1b, 0x3f, 0xff, 0x08, 0x09, 0x1f, 0xff, 0x17, 0x1a, 0x29, 0xff, 0x16, 0x1a, 0x26, 0xff, 0x0f, 0x0f, 0x15, 0xff, 0x13, 0x0e, 0x0f, 0xff, 0x10, 0x0c, 0x0e, 0xff, 0x16, 0x0c, 0x0f, 0xff, 0x16, 0x0c, 0x0d, 0xff, 0x18, 0x19, 0x1f, 0xff, 0x22, 0x24, 0x32, 0xff, 0x20, 0x22, 0x29, 0xff, 0x23, 0x21, 0x2d, 0xff, 0x20, 0x21, 0x30, 0xff, 0x19, 0x22, 0x35, 0xff, 0x27, 0x32, 0x47, 0xff, 0x35, 0x3c, 0x4e, 0xff, 0x1f, 0x23, 0x34, 0xff, 0x1d, 0x1e, 0x2d, 0xff, 0x24, 0x25, 0x34, 0xff, 0x20, 0x21, 0x30, 0xff, 0x25, 0x25, 0x35, 0xff, 0x25, 0x24, 0x35, 0xff, 0x20, 0x20, 0x30, 0xff, 0x2f, 0x32, 0x42, 0xff, 0x3c, 0x44, 0x57, 0xff, 0x38, 0x43, 0x59, 0xff, 0x4a, 0x55, 0x6b, 0xff, 0x45, 0x51, 0x67, 0xff, 0x49, 0x54, 0x70, 0xff, 0x50, 0x5b, 0x7a, 0xff, 0x62, 0x6c, 0x8b, 0xff, 0x58, 0x63, 0x81, 0xff, 0x60, 0x77, 0x93, 0xff, 0x4e, 0x64, 0x85, 0xff, 0x6d, 0x7f, 0xa3, 0xff, 0x70, 0x89, 0xb0, 0xff, 0x92, 0xa8, 0xc9, 0xff, 0xe8, 0xe4, 0xe6, 0xff, 0xee, 0xe4, 0xdd, 0xff, 0xfc, 0xf5, 0xee, 0xff, 0xf4, 0xf4, 0xf0, 0xff, 0xfc, 0xfd, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfd, 0xfd, 0xfd, 0xff, 0xfb, 0xfb, 0xfb, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x24, 0x48, 0x07, 0x68, 0x7d, 0x9a, 0xef, 0x84, 0x99, 0xaa, 0xff, 0x68, 0x75, 0x7b, 0xff, 0x40, 0x41, 0x42, 0xff, 0x66, 0x6e, 0x74, 0xff, 0xaa, 0xb9, 0xc7, 0xff, 0xa2, 0xae, 0xbc, 0xff, 0x9a, 0xa8, 0xb7, 0xff, 0x95, 0xa6, 0xb7, 0xff, 0x84, 0x94, 0xa4, 0xff, 0x74, 0x84, 0x95, 0xff, 0x71, 0x81, 0x92, 0xff, 0x6b, 0x7b, 0x8c, 0xff, 0x74, 0x86, 0x9f, 0xff, 0x64, 0x7a, 0x97, 0xff, 0x4f, 0x69, 0x88, 0xff, 0x50, 0x6a, 0x88, 0xff, 0x4c, 0x5f, 0x7b, 0xff, 0x70, 0x7e, 0x96, 0xff, 0x63, 0x71, 0x85, 0xff, 0x35, 0x43, 0x59, 0xff, 0x2c, 0x38, 0x53, 0xff, 0x26, 0x32, 0x4e, 0xff, 0x1c, 0x28, 0x44, 0xff, 0x21, 0x2b, 0x46, 0xff, 0x19, 0x25, 0x41, 0xff, 0x1f, 0x2b, 0x42, 0xff, 0x11, 0x1c, 0x2f, 0xff, 0x15, 0x20, 0x34, 0xff, 0x24, 0x2f, 0x47, 0xff, 0x1f, 0x28, 0x3d, 0xff, 0x12, 0x18, 0x2a, 0xff, 0x20, 0x24, 0x3b, 0xff, 0x18, 0x1f, 0x32, 0xff, 0x17, 0x1a, 0x28, 0xff, 0x18, 0x16, 0x27, 0xff, 0x13, 0x12, 0x22, 0xff, 0x16, 0x1c, 0x2e, 0xff, 0x1c, 0x24, 0x36, 0xff, 0x11, 0x17, 0x28, 0xff, 0x16, 0x18, 0x2b, 0xff, 0x17, 0x1a, 0x2d, 0xff, 0x15, 0x18, 0x26, 0xff, 0x13, 0x16, 0x22, 0xff, 0x14, 0x14, 0x30, 0xff, 0x24, 0x2f, 0x50, 0xff, 0x31, 0x48, 0x71, 0xff, 0x38, 0x4f, 0x78, 0xff, 0x3b, 0x50, 0x7a, 0xff, 0x38, 0x4f, 0x79, 0xff, 0x36, 0x49, 0x74, 0xff, 0x45, 0x54, 0x7f, 0xff, 0x55, 0x65, 0x90, 0xff, 0x51, 0x67, 0x95, 0xff, 0x5f, 0x78, 0xa5, 0xff, 0x76, 0x8b, 0xba, 0xff, 0x6f, 0x7e, 0xb4, 0xff, 0x61, 0x6d, 0xa9, 0xff, 0x5b, 0x6a, 0xa4, 0xff, 0x58, 0x6a, 0xa1, 0xff, 0x56, 0x68, 0xa0, 0xff, 0x56, 0x68, 0xa3, 0xff, 0x53, 0x65, 0xa0, 0xff, 0x4d, 0x5f, 0x9d, 0xff, 0x55, 0x69, 0xa9, 0xff, 0x41, 0x55, 0x84, 0xff, 0x0c, 0x1b, 0x3a, 0xff, 0x05, 0x0b, 0x25, 0xff, 0x1a, 0x1b, 0x37, 0xff, 0x19, 0x19, 0x2d, 0xff, 0x10, 0x12, 0x1d, 0xff, 0x0f, 0x0f, 0x14, 0xff, 0x0f, 0x0c, 0x0d, 0xff, 0x15, 0x11, 0x12, 0xff, 0x17, 0x16, 0x18, 0xff, 0x17, 0x1a, 0x1f, 0xff, 0x1b, 0x21, 0x28, 0xff, 0x1f, 0x1c, 0x27, 0xff, 0x1c, 0x19, 0x24, 0xff, 0x26, 0x27, 0x30, 0xff, 0x20, 0x21, 0x2d, 0xff, 0x1c, 0x23, 0x37, 0xff, 0x29, 0x34, 0x4c, 0xff, 0x34, 0x3d, 0x52, 0xff, 0x26, 0x2d, 0x3e, 0xff, 0x32, 0x35, 0x44, 0xff, 0x2b, 0x2e, 0x3d, 0xff, 0x23, 0x26, 0x35, 0xff, 0x28, 0x28, 0x38, 0xff, 0x2e, 0x2e, 0x3d, 0xff, 0x21, 0x22, 0x32, 0xff, 0x27, 0x2b, 0x3d, 0xff, 0x38, 0x3f, 0x53, 0xff, 0x38, 0x42, 0x58, 0xff, 0x3f, 0x4b, 0x60, 0xff, 0x42, 0x4e, 0x65, 0xff, 0x4f, 0x59, 0x75, 0xff, 0x5a, 0x66, 0x83, 0xff, 0x66, 0x78, 0x97, 0xff, 0x4b, 0x56, 0x70, 0xff, 0x5c, 0x6b, 0x89, 0xff, 0x64, 0x76, 0x96, 0xff, 0x73, 0x87, 0xa8, 0xff, 0x64, 0x85, 0xb0, 0xff, 0x8c, 0xa2, 0xcf, 0xff, 0xe2, 0xdd, 0xe5, 0xff, 0xe3, 0xdf, 0xdc, 0xff, 0xf6, 0xf3, 0xed, 0xff, 0xf4, 0xf5, 0xf3, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfd, 0xef, 0xfe, 0xfe, 0xfe, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 0x7a, 0x92, 0xa0, 0x81, 0x94, 0xab, 0xff, 0x67, 0x72, 0x7b, 0xff, 0x3e, 0x3f, 0x3f, 0xff, 0x5d, 0x65, 0x6a, 0xff, 0xa5, 0xb4, 0xc2, 0xff, 0xaa, 0xb6, 0xc4, 0xff, 0x9d, 0xab, 0xba, 0xff, 0x9c, 0xad, 0xbf, 0xff, 0x90, 0xa1, 0xb0, 0xff, 0x7d, 0x89, 0x9c, 0xff, 0x7a, 0x84, 0x99, 0xff, 0x81, 0x8c, 0xa0, 0xff, 0x6c, 0x7f, 0x98, 0xff, 0x4c, 0x63, 0x7f, 0xff, 0x4c, 0x67, 0x85, 0xff, 0x52, 0x6d, 0x8a, 0xff, 0x4d, 0x61, 0x7c, 0xff, 0x67, 0x75, 0x8c, 0xff, 0x40, 0x4d, 0x61, 0xff, 0x21, 0x2f, 0x44, 0xff, 0x27, 0x33, 0x4e, 0xff, 0x1f, 0x2b, 0x47, 0xff, 0x23, 0x2f, 0x4b, 0xff, 0x1f, 0x2a, 0x45, 0xff, 0x18, 0x23, 0x3f, 0xff, 0x1f, 0x2a, 0x40, 0xff, 0x22, 0x2d, 0x3f, 0xff, 0x19, 0x24, 0x37, 0xff, 0x29, 0x33, 0x4b, 0xff, 0x1d, 0x24, 0x3a, 0xff, 0x19, 0x1e, 0x32, 0xff, 0x22, 0x24, 0x40, 0xff, 0x1d, 0x22, 0x3b, 0xff, 0x15, 0x17, 0x28, 0xff, 0x13, 0x10, 0x21, 0xff, 0x0f, 0x0f, 0x1e, 0xff, 0x0e, 0x11, 0x1c, 0xff, 0x19, 0x19, 0x2c, 0xff, 0x1a, 0x1b, 0x32, 0xff, 0x1d, 0x25, 0x37, 0xff, 0x20, 0x2a, 0x3b, 0xff, 0x18, 0x1b, 0x2a, 0xff, 0x14, 0x14, 0x21, 0xff, 0x14, 0x15, 0x2f, 0xff, 0x22, 0x2d, 0x4e, 0xff, 0x31, 0x48, 0x72, 0xff, 0x39, 0x4f, 0x79, 0xff, 0x3c, 0x52, 0x7c, 0xff, 0x3d, 0x53, 0x7e, 0xff, 0x3a, 0x50, 0x7a, 0xff, 0x3d, 0x51, 0x7c, 0xff, 0x49, 0x5d, 0x88, 0xff, 0x4d, 0x63, 0x91, 0xff, 0x49, 0x61, 0x8f, 0xff, 0x4e, 0x65, 0x93, 0xff, 0x5b, 0x71, 0xa1, 0xff, 0x5f, 0x71, 0xa7, 0xff, 0x5a, 0x6c, 0xa7, 0xff, 0x53, 0x65, 0xa3, 0xff, 0x4e, 0x61, 0x9e, 0xff, 0x53, 0x67, 0x9f, 0xff, 0x53, 0x67, 0x9d, 0xff, 0x47, 0x5d, 0x95, 0xff, 0x2f, 0x43, 0x7a, 0xff, 0x0e, 0x1f, 0x46, 0xff, 0x09, 0x16, 0x37, 0xff, 0x1a, 0x26, 0x46, 0xff, 0x13, 0x1b, 0x32, 0xff, 0x0e, 0x0f, 0x1c, 0xff, 0x11, 0x0e, 0x15, 0xff, 0x0b, 0x07, 0x09, 0xff, 0x0b, 0x09, 0x09, 0xff, 0x17, 0x13, 0x12, 0xff, 0x1a, 0x19, 0x1c, 0xff, 0x13, 0x17, 0x20, 0xff, 0x19, 0x20, 0x2c, 0xff, 0x1b, 0x1c, 0x2b, 0xff, 0x11, 0x0f, 0x1e, 0xff, 0x13, 0x17, 0x25, 0xff, 0x19, 0x1f, 0x2f, 0xff, 0x1c, 0x23, 0x33, 0xff, 0x1e, 0x24, 0x38, 0xff, 0x2d, 0x33, 0x49, 0xff, 0x2d, 0x2f, 0x40, 0xff, 0x29, 0x2b, 0x3a, 0xff, 0x26, 0x2a, 0x39, 0xff, 0x20, 0x23, 0x34, 0xff, 0x2a, 0x2a, 0x3b, 0xff, 0x30, 0x2f, 0x3e, 0xff, 0x2c, 0x2c, 0x3c, 0xff, 0x2c, 0x2f, 0x41, 0xff, 0x3a, 0x3f, 0x54, 0xff, 0x3f, 0x48, 0x5f, 0xff, 0x41, 0x4d, 0x62, 0xff, 0x3a, 0x46, 0x5c, 0xff, 0x48, 0x4f, 0x67, 0xff, 0x5d, 0x64, 0x7d, 0xff, 0x54, 0x67, 0x86, 0xff, 0x55, 0x61, 0x81, 0xff, 0x74, 0x7d, 0xa1, 0xff, 0x73, 0x83, 0xa5, 0xff, 0x71, 0x85, 0xa5, 0xff, 0x67, 0x88, 0xb0, 0xff, 0x8b, 0xa2, 0xc4, 0xff, 0xd2, 0xcc, 0xcf, 0xff, 0xee, 0xe8, 0xe5, 0xff, 0xf1, 0xef, 0xe9, 0xff, 0xfc, 0xfb, 0xfb, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xfe, 0xfe, 0xfe, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfe, 0xfe, 0xff, 0xff, 0xfd, 0xfd, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5d, 0x78, 0x89, 0x4a, 0x7d, 0x92, 0xa8, 0xff, 0x65, 0x6f, 0x7b, 0xff, 0x3c, 0x3d, 0x3f, 0xff, 0x5a, 0x61, 0x68, 0xff, 0xa5, 0xb3, 0xc1, 0xff, 0xb3, 0xbf, 0xce, 0xff, 0x9e, 0xac, 0xb9, 0xff, 0x9c, 0xad, 0xbb, 0xff, 0x91, 0xa2, 0xb3, 0xff, 0x7a, 0x8c, 0xa4, 0xff, 0x67, 0x7a, 0x95, 0xff, 0x6a, 0x7e, 0x97, 0xff, 0x5d, 0x72, 0x8b, 0xff, 0x58, 0x6c, 0x89, 0xff, 0x60, 0x78, 0x97, 0xff, 0x54, 0x6e, 0x8c, 0xff, 0x3a, 0x4f, 0x69, 0xff, 0x30, 0x40, 0x58, 0xff, 0x2c, 0x3a, 0x51, 0xff, 0x28, 0x36, 0x4f, 0xff, 0x2a, 0x36, 0x51, 0xff, 0x19, 0x25, 0x42, 0xff, 0x18, 0x24, 0x40, 0xff, 0x1b, 0x26, 0x40, 0xff, 0x1e, 0x29, 0x45, 0xff, 0x13, 0x1d, 0x35, 0xff, 0x1e, 0x27, 0x3d, 0xff, 0x32, 0x3d, 0x55, 0xff, 0x22, 0x2e, 0x44, 0xff, 0x12, 0x1d, 0x2f, 0xff, 0x22, 0x29, 0x3a, 0xff, 0x24, 0x28, 0x44, 0xff, 0x1b, 0x22, 0x3b, 0xff, 0x15, 0x1a, 0x28, 0xff, 0x15, 0x16, 0x21, 0xff, 0x13, 0x14, 0x23, 0xff, 0x16, 0x16, 0x25, 0xff, 0x1a, 0x18, 0x2b, 0xff, 0x12, 0x12, 0x28, 0xff, 0x0c, 0x14, 0x26, 0xff, 0x1b, 0x23, 0x34, 0xff, 0x1f, 0x23, 0x39, 0xff, 0x11, 0x14, 0x26, 0xff, 0x0b, 0x12, 0x21, 0xff, 0x1f, 0x2c, 0x4a, 0xff, 0x32, 0x48, 0x73, 0xff, 0x3d, 0x53, 0x7c, 0xff, 0x3e, 0x55, 0x7c, 0xff, 0x41, 0x58, 0x7f, 0xff, 0x40, 0x57, 0x7f, 0xff, 0x3e, 0x55, 0x7f, 0xff, 0x47, 0x5d, 0x87, 0xff, 0x4e, 0x65, 0x92, 0xff, 0x4b, 0x62, 0x90, 0xff, 0x42, 0x59, 0x87, 0xff, 0x40, 0x58, 0x86, 0xff, 0x43, 0x5b, 0x8a, 0xff, 0x47, 0x5d, 0x92, 0xff, 0x44, 0x59, 0x95, 0xff, 0x42, 0x57, 0x92, 0xff, 0x3f, 0x57, 0x88, 0xff, 0x35, 0x4c, 0x7c, 0xff, 0x1f, 0x35, 0x63, 0xff, 0x09, 0x1f, 0x44, 0xff, 0x15, 0x25, 0x4c, 0xff, 0x26, 0x33, 0x59, 0xff, 0x13, 0x20, 0x3e, 0xff, 0x05, 0x0e, 0x21, 0xff, 0x14, 0x13, 0x23, 0xff, 0x11, 0x0e, 0x15, 0xff, 0x0f, 0x0c, 0x0a, 0xff, 0x10, 0x0b, 0x0f, 0xff, 0x15, 0x12, 0x17, 0xff, 0x18, 0x14, 0x1d, 0xff, 0x21, 0x1d, 0x2b, 0xff, 0x20, 0x1d, 0x2b, 0xff, 0x18, 0x18, 0x27, 0xff, 0x1b, 0x20, 0x2f, 0xff, 0x30, 0x36, 0x47, 0xff, 0x33, 0x3b, 0x4e, 0xff, 0x21, 0x25, 0x38, 0xff, 0x17, 0x1c, 0x29, 0xff, 0x17, 0x1b, 0x24, 0xff, 0x1c, 0x1e, 0x27, 0xff, 0x1c, 0x1f, 0x2c, 0xff, 0x26, 0x29, 0x37, 0xff, 0x1e, 0x20, 0x2d, 0xff, 0x26, 0x26, 0x34, 0xff, 0x2f, 0x30, 0x40, 0xff, 0x23, 0x26, 0x38, 0xff, 0x30, 0x36, 0x48, 0xff, 0x3f, 0x49, 0x5c, 0xff, 0x3e, 0x4a, 0x60, 0xff, 0x3c, 0x48, 0x61, 0xff, 0x3a, 0x46, 0x60, 0xff, 0x49, 0x51, 0x66, 0xff, 0x5c, 0x63, 0x78, 0xff, 0x58, 0x5f, 0x7e, 0xff, 0x55, 0x5e, 0x7f, 0xff, 0x5b, 0x6b, 0x8f, 0xff, 0x60, 0x74, 0x97, 0xff, 0x62, 0x76, 0x9a, 0xff, 0x61, 0x84, 0xad, 0xff, 0x97, 0xb1, 0xcd, 0xff, 0xdb, 0xd4, 0xda, 0xff, 0xf7, 0xf6, 0xf1, 0xff, 0xfc, 0xfb, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0x4a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x7f, 0x7f, 0x04, 0x7c, 0x90, 0xa7, 0xe7, 0x62, 0x6c, 0x7c, 0xff, 0x3d, 0x42, 0x49, 0xff, 0x59, 0x61, 0x6d, 0xff, 0xa1, 0xaf, 0xbe, 0xff, 0xae, 0xbb, 0xc9, 0xff, 0x9e, 0xac, 0xba, 0xff, 0x87, 0x96, 0xa8, 0xff, 0x72, 0x81, 0x9d, 0xff, 0x6c, 0x7d, 0x99, 0xff, 0x5e, 0x72, 0x8c, 0xff, 0x59, 0x72, 0x8b, 0xff, 0x66, 0x7d, 0x96, 0xff, 0x62, 0x77, 0x94, 0xff, 0x5c, 0x6e, 0x8f, 0xff, 0x43, 0x52, 0x71, 0xff, 0x2a, 0x36, 0x51, 0xff, 0x4c, 0x59, 0x74, 0xff, 0x50, 0x60, 0x7b, 0xff, 0x2c, 0x3b, 0x56, 0xff, 0x29, 0x35, 0x51, 0xff, 0x1e, 0x2a, 0x46, 0xff, 0x1a, 0x25, 0x41, 0xff, 0x24, 0x2e, 0x49, 0xff, 0x1c, 0x28, 0x43, 0xff, 0x0e, 0x15, 0x2e, 0xff, 0x1d, 0x24, 0x3d, 0xff, 0x39, 0x45, 0x63, 0xff, 0x28, 0x37, 0x4d, 0xff, 0x0d, 0x1a, 0x2a, 0xff, 0x18, 0x22, 0x32, 0xff, 0x26, 0x2d, 0x47, 0xff, 0x1e, 0x28, 0x3e, 0xff, 0x10, 0x19, 0x25, 0xff, 0x0e, 0x15, 0x1a, 0xff, 0x1b, 0x20, 0x30, 0xff, 0x1c, 0x1e, 0x36, 0xff, 0x11, 0x16, 0x28, 0xff, 0x0c, 0x11, 0x1d, 0xff, 0x12, 0x11, 0x1a, 0xff, 0x11, 0x0f, 0x17, 0xff, 0x15, 0x19, 0x28, 0xff, 0x0f, 0x17, 0x2f, 0xff, 0x0f, 0x12, 0x2b, 0xff, 0x1a, 0x26, 0x45, 0xff, 0x28, 0x3e, 0x69, 0xff, 0x3b, 0x51, 0x7a, 0xff, 0x3f, 0x55, 0x7c, 0xff, 0x46, 0x5c, 0x83, 0xff, 0x45, 0x5b, 0x84, 0xff, 0x40, 0x56, 0x80, 0xff, 0x48, 0x5e, 0x88, 0xff, 0x4e, 0x65, 0x92, 0xff, 0x4e, 0x65, 0x93, 0xff, 0x49, 0x60, 0x8f, 0xff, 0x41, 0x58, 0x89, 0xff, 0x37, 0x4e, 0x7e, 0xff, 0x34, 0x4c, 0x79, 0xff, 0x32, 0x4b, 0x74, 0xff, 0x2f, 0x48, 0x72, 0xff, 0x22, 0x3c, 0x67, 0xff, 0x11, 0x28, 0x54, 0xff, 0x0c, 0x1f, 0x4b, 0xff, 0x1c, 0x30, 0x57, 0xff, 0x1b, 0x2f, 0x53, 0xff, 0x14, 0x23, 0x44, 0xff, 0x0e, 0x19, 0x34, 0xff, 0x11, 0x17, 0x2e, 0xff, 0x12, 0x11, 0x25, 0xff, 0x0b, 0x0a, 0x13, 0xff, 0x12, 0x11, 0x11, 0xff, 0x13, 0x0e, 0x16, 0xff, 0x13, 0x12, 0x1f, 0xff, 0x1e, 0x1d, 0x2b, 0xff, 0x26, 0x23, 0x2f, 0xff, 0x18, 0x15, 0x20, 0xff, 0x16, 0x15, 0x21, 0xff, 0x24, 0x2c, 0x3b, 0xff, 0x35, 0x3b, 0x4d, 0xff, 0x31, 0x36, 0x49, 0xff, 0x36, 0x3b, 0x4f, 0xff, 0x24, 0x2c, 0x3d, 0xff, 0x1c, 0x1d, 0x29, 0xff, 0x10, 0x10, 0x18, 0xff, 0x18, 0x1b, 0x27, 0xff, 0x28, 0x2a, 0x35, 0xff, 0x22, 0x25, 0x2c, 0xff, 0x1d, 0x1d, 0x2a, 0xff, 0x2c, 0x2e, 0x40, 0xff, 0x28, 0x2d, 0x40, 0xff, 0x2b, 0x33, 0x46, 0xff, 0x39, 0x45, 0x56, 0xff, 0x31, 0x3c, 0x50, 0xff, 0x2e, 0x39, 0x53, 0xff, 0x43, 0x51, 0x6f, 0xff, 0x54, 0x67, 0x7d, 0xff, 0x4f, 0x5f, 0x70, 0xff, 0x48, 0x51, 0x6b, 0xff, 0x48, 0x54, 0x73, 0xff, 0x4d, 0x5d, 0x7d, 0xff, 0x58, 0x69, 0x8c, 0xff, 0x68, 0x7d, 0xa6, 0xff, 0x63, 0x88, 0xb3, 0xff, 0x9c, 0xb9, 0xd0, 0xff, 0xf6, 0xe9, 0xec, 0xff, 0xfe, 0xfb, 0xf5, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xf2, 0xf2, 0xf2, 0xff, 0xf0, 0xf1, 0xf1, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xfa, 0xf9, 0xf6, 0xff, 0xfe, 0xfc, 0xf9, 0xff, 0xfb, 0xfa, 0xf8, 0xff, 0xfb, 0xfb, 0xf9, 0xff, 0xfc, 0xfc, 0xf9, 0xff, 0xfa, 0xfc, 0xf9, 0xff, 0xfc, 0xfd, 0xfc, 0xff, 0xfb, 0xfc, 0xfb, 0xff, 0xfa, 0xfb, 0xfb, 0xe7, 0xff, 0xff, 0xff, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7a, 0x8b, 0xa0, 0x87, 0x5f, 0x6c, 0x80, 0xff, 0x3c, 0x44, 0x4f, 0xff, 0x52, 0x5e, 0x6c, 0xff, 0x9a, 0xa8, 0xb7, 0xff, 0xab, 0xb7, 0xc5, 0xff, 0x9d, 0xab, 0xb8, 0xff, 0x85, 0x94, 0xa5, 0xff, 0x78, 0x89, 0xa3, 0xff, 0x80, 0x8f, 0xa8, 0xff, 0x7a, 0x89, 0x9e, 0xff, 0x6f, 0x84, 0x98, 0xff, 0x71, 0x87, 0xa0, 0xff, 0x5b, 0x70, 0x8e, 0xff, 0x4c, 0x5f, 0x80, 0xff, 0x2d, 0x3f, 0x5e, 0xff, 0x35, 0x43, 0x5e, 0xff, 0x58, 0x65, 0x7f, 0xff, 0x46, 0x55, 0x6f, 0xff, 0x31, 0x40, 0x5a, 0xff, 0x25, 0x32, 0x4d, 0xff, 0x23, 0x2f, 0x4b, 0xff, 0x2a, 0x36, 0x52, 0xff, 0x27, 0x32, 0x4c, 0xff, 0x17, 0x22, 0x3e, 0xff, 0x16, 0x1e, 0x36, 0xff, 0x25, 0x2d, 0x45, 0xff, 0x30, 0x3b, 0x58, 0xff, 0x32, 0x3f, 0x5a, 0xff, 0x18, 0x23, 0x39, 0xff, 0x12, 0x1b, 0x2d, 0xff, 0x20, 0x27, 0x41, 0xff, 0x20, 0x29, 0x3f, 0xff, 0x19, 0x23, 0x31, 0xff, 0x14, 0x1e, 0x2b, 0xff, 0x16, 0x1d, 0x32, 0xff, 0x16, 0x1d, 0x33, 0xff, 0x0e, 0x17, 0x28, 0xff, 0x0e, 0x16, 0x22, 0xff, 0x15, 0x18, 0x1e, 0xff, 0x0c, 0x0e, 0x13, 0xff, 0x0e, 0x11, 0x1c, 0xff, 0x14, 0x17, 0x2a, 0xff, 0x10, 0x10, 0x24, 0xff, 0x16, 0x21, 0x40, 0xff, 0x27, 0x3e, 0x69, 0xff, 0x3b, 0x51, 0x7b, 0xff, 0x42, 0x58, 0x83, 0xff, 0x47, 0x5c, 0x88, 0xff, 0x46, 0x5c, 0x86, 0xff, 0x41, 0x57, 0x81, 0xff, 0x47, 0x5d, 0x87, 0xff, 0x4c, 0x63, 0x90, 0xff, 0x4e, 0x65, 0x93, 0xff, 0x4b, 0x62, 0x91, 0xff, 0x43, 0x5a, 0x8b, 0xff, 0x3a, 0x51, 0x83, 0xff, 0x37, 0x4f, 0x7c, 0xff, 0x33, 0x4d, 0x75, 0xff, 0x2e, 0x48, 0x71, 0xff, 0x27, 0x3e, 0x6a, 0xff, 0x1c, 0x2d, 0x5d, 0xff, 0x21, 0x30, 0x5f, 0xff, 0x20, 0x35, 0x5c, 0xff, 0x17, 0x2a, 0x4e, 0xff, 0x10, 0x1e, 0x42, 0xff, 0x13, 0x1c, 0x3d, 0xff, 0x1d, 0x21, 0x3c, 0xff, 0x15, 0x17, 0x23, 0xff, 0x12, 0x11, 0x18, 0xff, 0x11, 0x0d, 0x13, 0xff, 0x13, 0x12, 0x18, 0xff, 0x1b, 0x1f, 0x29, 0xff, 0x1f, 0x21, 0x2d, 0xff, 0x18, 0x15, 0x21, 0xff, 0x13, 0x10, 0x1c, 0xff, 0x22, 0x21, 0x2d, 0xff, 0x35, 0x3c, 0x4b, 0xff, 0x29, 0x2c, 0x3e, 0xff, 0x17, 0x14, 0x29, 0xff, 0x20, 0x28, 0x3a, 0xff, 0x31, 0x40, 0x56, 0xff, 0x22, 0x24, 0x3a, 0xff, 0x17, 0x11, 0x19, 0xff, 0x1d, 0x1e, 0x29, 0xff, 0x21, 0x23, 0x30, 0xff, 0x21, 0x23, 0x2b, 0xff, 0x1d, 0x1c, 0x2a, 0xff, 0x19, 0x1c, 0x2d, 0xff, 0x25, 0x28, 0x3c, 0xff, 0x39, 0x3b, 0x4f, 0xff, 0x2d, 0x34, 0x47, 0xff, 0x24, 0x2b, 0x3d, 0xff, 0x26, 0x2e, 0x41, 0xff, 0x46, 0x51, 0x6c, 0xff, 0x57, 0x65, 0x83, 0xff, 0x52, 0x60, 0x6e, 0xff, 0x47, 0x52, 0x66, 0xff, 0x36, 0x41, 0x62, 0xff, 0x4f, 0x5f, 0x7f, 0xff, 0x5b, 0x6c, 0x90, 0xff, 0x5d, 0x72, 0x9a, 0xff, 0x6a, 0x8a, 0xb6, 0xff, 0xa3, 0xbc, 0xd9, 0xff, 0xd3, 0xd4, 0xd9, 0xff, 0xdd, 0xdf, 0xdf, 0xff, 0xda, 0xdd, 0xdf, 0xff, 0xd8, 0xd2, 0xd1, 0xff, 0xd3, 0xd0, 0xd0, 0xff, 0xd3, 0xd4, 0xd5, 0xff, 0xd8, 0xd8, 0xd7, 0xff, 0xe3, 0xdd, 0xd6, 0xff, 0xe4, 0xde, 0xd6, 0xff, 0xe3, 0xdd, 0xd5, 0xff, 0xe8, 0xe0, 0xd9, 0xff, 0xe8, 0xe0, 0xd9, 0xff, 0xe8, 0xdf, 0xd8, 0xff, 0xeb, 0xe1, 0xda, 0xff, 0xf4, 0xeb, 0xe2, 0xff, 0xec, 0xe4, 0xdb, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0x7f, 0x78, 0x22, 0x5c, 0x6b, 0x72, 0xfe, 0x3e, 0x47, 0x54, 0xff, 0x55, 0x60, 0x6e, 0xff, 0x95, 0xa3, 0xb1, 0xff, 0xac, 0xb8, 0xc7, 0xff, 0xa2, 0xaf, 0xbc, 0xff, 0x98, 0xa7, 0xb3, 0xff, 0x8c, 0x9d, 0xad, 0xff, 0x7c, 0x8b, 0xa0, 0xff, 0x6a, 0x78, 0x91, 0xff, 0x65, 0x78, 0x92, 0xff, 0x66, 0x7a, 0x93, 0xff, 0x57, 0x6b, 0x84, 0xff, 0x44, 0x59, 0x74, 0xff, 0x36, 0x4e, 0x6a, 0xff, 0x46, 0x5d, 0x79, 0xff, 0x3f, 0x54, 0x6d, 0xff, 0x38, 0x48, 0x61, 0xff, 0x32, 0x3f, 0x59, 0xff, 0x2d, 0x39, 0x55, 0xff, 0x31, 0x3c, 0x5a, 0xff, 0x29, 0x34, 0x52, 0xff, 0x22, 0x2d, 0x49, 0xff, 0x1f, 0x2a, 0x46, 0xff, 0x28, 0x32, 0x4d, 0xff, 0x2c, 0x37, 0x51, 0xff, 0x1b, 0x27, 0x41, 0xff, 0x2d, 0x38, 0x51, 0xff, 0x2e, 0x37, 0x53, 0xff, 0x1d, 0x25, 0x40, 0xff, 0x16, 0x20, 0x36, 0xff, 0x18, 0x22, 0x38, 0xff, 0x1b, 0x26, 0x3b, 0xff, 0x15, 0x1a, 0x29, 0xff, 0x15, 0x1a, 0x29, 0xff, 0x14, 0x1c, 0x2f, 0xff, 0x13, 0x19, 0x29, 0xff, 0x13, 0x18, 0x24, 0xff, 0x10, 0x17, 0x21, 0xff, 0x0c, 0x13, 0x1f, 0xff, 0x0b, 0x11, 0x1b, 0xff, 0x08, 0x0d, 0x13, 0xff, 0x10, 0x13, 0x1d, 0xff, 0x1c, 0x23, 0x40, 0xff, 0x2e, 0x43, 0x69, 0xff, 0x3d, 0x54, 0x7c, 0xff, 0x44, 0x58, 0x88, 0xff, 0x46, 0x5a, 0x8b, 0xff, 0x47, 0x5b, 0x8a, 0xff, 0x45, 0x5b, 0x86, 0xff, 0x4a, 0x5f, 0x8b, 0xff, 0x4a, 0x61, 0x8f, 0xff, 0x4c, 0x63, 0x90, 0xff, 0x4b, 0x62, 0x8f, 0xff, 0x42, 0x5a, 0x88, 0xff, 0x3a, 0x53, 0x80, 0xff, 0x39, 0x50, 0x7f, 0xff, 0x37, 0x4f, 0x7e, 0xff, 0x32, 0x49, 0x78, 0xff, 0x2e, 0x45, 0x6e, 0xff, 0x2c, 0x3f, 0x67, 0xff, 0x2f, 0x40, 0x68, 0xff, 0x23, 0x37, 0x5d, 0xff, 0x23, 0x36, 0x5c, 0xff, 0x1a, 0x2b, 0x51, 0xff, 0x15, 0x20, 0x43, 0xff, 0x1e, 0x24, 0x41, 0xff, 0x19, 0x1b, 0x2c, 0xff, 0x15, 0x14, 0x1c, 0xff, 0x13, 0x15, 0x1e, 0xff, 0x15, 0x13, 0x1e, 0xff, 0x17, 0x17, 0x20, 0xff, 0x1c, 0x1b, 0x24, 0xff, 0x17, 0x12, 0x1e, 0xff, 0x18, 0x13, 0x1e, 0xff, 0x22, 0x22, 0x33, 0xff, 0x31, 0x3b, 0x4e, 0xff, 0x1a, 0x1f, 0x2b, 0xff, 0x20, 0x1e, 0x2a, 0xff, 0x0a, 0x11, 0x20, 0xff, 0x1f, 0x2c, 0x43, 0xff, 0x2c, 0x33, 0x4a, 0xff, 0x28, 0x27, 0x31, 0xff, 0x25, 0x27, 0x30, 0xff, 0x14, 0x16, 0x1f, 0xff, 0x18, 0x19, 0x1f, 0xff, 0x1f, 0x20, 0x27, 0xff, 0x14, 0x17, 0x20, 0xff, 0x1a, 0x1e, 0x2f, 0xff, 0x35, 0x3b, 0x4d, 0xff, 0x23, 0x29, 0x3a, 0xff, 0x26, 0x2b, 0x3c, 0xff, 0x26, 0x2d, 0x3f, 0xff, 0x2a, 0x32, 0x48, 0xff, 0x51, 0x58, 0x6f, 0xff, 0x57, 0x5f, 0x74, 0xff, 0x3a, 0x44, 0x5e, 0xff, 0x3a, 0x49, 0x68, 0xff, 0x4b, 0x5c, 0x7d, 0xff, 0x54, 0x67, 0x8c, 0xff, 0x68, 0x7f, 0xa6, 0xff, 0x79, 0x93, 0xbb, 0xff, 0x99, 0xad, 0xd2, 0xff, 0xb7, 0xc9, 0xdb, 0xff, 0xb6, 0xc3, 0xcc, 0xff, 0xc3, 0xc7, 0xce, 0xff, 0xd7, 0xd4, 0xd5, 0xff, 0xdd, 0xdc, 0xdb, 0xff, 0xd6, 0xd8, 0xd8, 0xff, 0xd0, 0xd1, 0xd1, 0xff, 0xd5, 0xd3, 0xd4, 0xff, 0xd1, 0xd1, 0xd2, 0xff, 0xd2, 0xcf, 0xd0, 0xff, 0xe2, 0xd9, 0xd6, 0xff, 0xe3, 0xd6, 0xcf, 0xff, 0xdd, 0xd2, 0xc9, 0xff, 0xdb, 0xd2, 0xcb, 0xff, 0xdf, 0xd5, 0xcd, 0xfe, 0xf0, 0xe9, 0xda, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x67, 0x5e, 0xb4, 0x47, 0x4f, 0x5a, 0xff, 0x5a, 0x65, 0x75, 0xff, 0x90, 0x9e, 0xab, 0xff, 0xb4, 0xc0, 0xd0, 0xff, 0xaa, 0xb6, 0xc4, 0xff, 0x94, 0xa1, 0xb0, 0xff, 0x92, 0xa2, 0xb3, 0xff, 0x7f, 0x8e, 0xa1, 0xff, 0x66, 0x77, 0x8c, 0xff, 0x68, 0x7c, 0x96, 0xff, 0x5a, 0x70, 0x8c, 0xff, 0x54, 0x6a, 0x86, 0xff, 0x51, 0x67, 0x82, 0xff, 0x42, 0x57, 0x73, 0xff, 0x4e, 0x63, 0x80, 0xff, 0x49, 0x5e, 0x7a, 0xff, 0x34, 0x43, 0x60, 0xff, 0x27, 0x32, 0x4f, 0xff, 0x38, 0x43, 0x61, 0xff, 0x3e, 0x48, 0x67, 0xff, 0x1c, 0x27, 0x44, 0xff, 0x17, 0x22, 0x3e, 0xff, 0x17, 0x22, 0x3e, 0xff, 0x1e, 0x29, 0x45, 0xff, 0x25, 0x30, 0x4c, 0xff, 0x12, 0x1d, 0x38, 0xff, 0x10, 0x19, 0x2d, 0xff, 0x23, 0x2b, 0x43, 0xff, 0x27, 0x2f, 0x4b, 0xff, 0x22, 0x2f, 0x47, 0xff, 0x1a, 0x27, 0x42, 0xff, 0x15, 0x1d, 0x33, 0xff, 0x14, 0x11, 0x19, 0xff, 0x15, 0x13, 0x1b, 0xff, 0x19, 0x21, 0x32, 0xff, 0x17, 0x1e, 0x2e, 0xff, 0x10, 0x13, 0x1f, 0xff, 0x11, 0x13, 0x1f, 0xff, 0x15, 0x18, 0x25, 0xff, 0x0d, 0x0f, 0x1a, 0xff, 0x07, 0x0a, 0x0d, 0xff, 0x0d, 0x0d, 0x16, 0xff, 0x1c, 0x20, 0x35, 0xff, 0x32, 0x43, 0x66, 0xff, 0x40, 0x53, 0x7d, 0xff, 0x44, 0x59, 0x87, 0xff, 0x48, 0x5d, 0x89, 0xff, 0x4b, 0x60, 0x8d, 0xff, 0x4d, 0x61, 0x8f, 0xff, 0x4d, 0x62, 0x8f, 0xff, 0x4d, 0x64, 0x90, 0xff, 0x4e, 0x65, 0x92, 0xff, 0x4a, 0x61, 0x8e, 0xff, 0x40, 0x59, 0x87, 0xff, 0x3d, 0x55, 0x83, 0xff, 0x3a, 0x51, 0x7f, 0xff, 0x3a, 0x50, 0x7f, 0xff, 0x39, 0x50, 0x7f, 0xff, 0x3a, 0x51, 0x7a, 0xff, 0x34, 0x47, 0x70, 0xff, 0x2a, 0x3c, 0x63, 0xff, 0x20, 0x33, 0x58, 0xff, 0x24, 0x37, 0x5e, 0xff, 0x1a, 0x2e, 0x55, 0xff, 0x14, 0x24, 0x46, 0xff, 0x1a, 0x27, 0x43, 0xff, 0x16, 0x1c, 0x30, 0xff, 0x16, 0x17, 0x20, 0xff, 0x12, 0x16, 0x26, 0xff, 0x0b, 0x0c, 0x1f, 0xff, 0x17, 0x19, 0x26, 0xff, 0x1e, 0x1d, 0x29, 0xff, 0x0f, 0x0b, 0x17, 0xff, 0x1d, 0x19, 0x24, 0xff, 0x1c, 0x1f, 0x2f, 0xff, 0x26, 0x2f, 0x45, 0xff, 0x19, 0x1e, 0x2c, 0xff, 0x12, 0x16, 0x22, 0xff, 0x25, 0x28, 0x35, 0xff, 0x31, 0x37, 0x48, 0xff, 0x31, 0x39, 0x4c, 0xff, 0x2d, 0x32, 0x42, 0xff, 0x2f, 0x32, 0x3e, 0xff, 0x11, 0x11, 0x1a, 0xff, 0x10, 0x0c, 0x15, 0xff, 0x16, 0x13, 0x17, 0xff, 0x16, 0x15, 0x19, 0xff, 0x25, 0x28, 0x39, 0xff, 0x21, 0x2b, 0x3c, 0xff, 0x1e, 0x26, 0x36, 0xff, 0x2c, 0x30, 0x42, 0xff, 0x2e, 0x33, 0x46, 0xff, 0x1a, 0x22, 0x36, 0xff, 0x27, 0x30, 0x46, 0xff, 0x49, 0x55, 0x71, 0xff, 0x32, 0x40, 0x5d, 0xff, 0x4a, 0x59, 0x76, 0xff, 0x50, 0x61, 0x81, 0xff, 0x4e, 0x62, 0x87, 0xff, 0x6d, 0x85, 0xac, 0xff, 0x72, 0x8c, 0xb1, 0xff, 0x84, 0x99, 0xbb, 0xff, 0xad, 0xc7, 0xdd, 0xff, 0xa1, 0xbb, 0xcb, 0xff, 0xc3, 0xd1, 0xe2, 0xff, 0xda, 0xd6, 0xe3, 0xff, 0xcd, 0xca, 0xd1, 0xff, 0xc5, 0xc8, 0xcd, 0xff, 0xca, 0xcb, 0xd2, 0xff, 0xca, 0xcc, 0xd2, 0xff, 0xce, 0xd0, 0xd4, 0xff, 0xd8, 0xd9, 0xdd, 0xff, 0xd6, 0xd6, 0xd9, 0xff, 0xcf, 0xce, 0xd0, 0xff, 0xcf, 0xc8, 0xc2, 0xff, 0xcc, 0xc5, 0xbf, 0xff, 0xc8, 0xc5, 0xc3, 0xb5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x7f, 0x6f, 0x40, 0x58, 0x5b, 0x61, 0xff, 0x5c, 0x68, 0x78, 0xff, 0x8f, 0x9d, 0xaa, 0xff, 0xbb, 0xc8, 0xd5, 0xff, 0xaf, 0xbc, 0xc8, 0xff, 0x92, 0xa0, 0xad, 0xff, 0x8d, 0x9f, 0xae, 0xff, 0x85, 0x96, 0xa6, 0xff, 0x78, 0x88, 0x99, 0xff, 0x75, 0x8a, 0x9f, 0xff, 0x6d, 0x84, 0xa1, 0xff, 0x64, 0x7d, 0x9c, 0xff, 0x59, 0x70, 0x8e, 0xff, 0x3a, 0x4d, 0x69, 0xff, 0x3e, 0x53, 0x6d, 0xff, 0x3e, 0x51, 0x6e, 0xff, 0x2d, 0x3b, 0x5c, 0xff, 0x2d, 0x37, 0x57, 0xff, 0x30, 0x3a, 0x59, 0xff, 0x29, 0x34, 0x52, 0xff, 0x0c, 0x18, 0x35, 0xff, 0x11, 0x1b, 0x38, 0xff, 0x17, 0x21, 0x3e, 0xff, 0x13, 0x1e, 0x39, 0xff, 0x13, 0x1f, 0x39, 0xff, 0x14, 0x20, 0x3a, 0xff, 0x10, 0x17, 0x2b, 0xff, 0x0b, 0x10, 0x23, 0xff, 0x1b, 0x22, 0x3a, 0xff, 0x29, 0x35, 0x53, 0xff, 0x26, 0x34, 0x52, 0xff, 0x15, 0x21, 0x39, 0xff, 0x0e, 0x11, 0x1e, 0xff, 0x09, 0x0d, 0x19, 0xff, 0x0f, 0x18, 0x2a, 0xff, 0x18, 0x1f, 0x2e, 0xff, 0x0e, 0x10, 0x1b, 0xff, 0x0e, 0x0d, 0x19, 0xff, 0x12, 0x12, 0x1e, 0xff, 0x12, 0x12, 0x19, 0xff, 0x0e, 0x0f, 0x10, 0xff, 0x0c, 0x0a, 0x10, 0xff, 0x0e, 0x15, 0x20, 0xff, 0x2b, 0x3a, 0x5b, 0xff, 0x43, 0x54, 0x80, 0xff, 0x47, 0x5e, 0x87, 0xff, 0x4b, 0x62, 0x8a, 0xff, 0x4a, 0x60, 0x8b, 0xff, 0x4c, 0x61, 0x8f, 0xff, 0x4e, 0x64, 0x91, 0xff, 0x4e, 0x65, 0x92, 0xff, 0x4e, 0x65, 0x92, 0xff, 0x4a, 0x61, 0x8e, 0xff, 0x43, 0x5b, 0x8a, 0xff, 0x40, 0x59, 0x87, 0xff, 0x41, 0x58, 0x86, 0xff, 0x42, 0x59, 0x86, 0xff, 0x40, 0x58, 0x86, 0xff, 0x41, 0x57, 0x84, 0xff, 0x30, 0x42, 0x70, 0xff, 0x26, 0x36, 0x63, 0xff, 0x23, 0x35, 0x5b, 0xff, 0x28, 0x3a, 0x60, 0xff, 0x27, 0x3b, 0x61, 0xff, 0x17, 0x2c, 0x4d, 0xff, 0x14, 0x23, 0x3e, 0xff, 0x15, 0x1e, 0x2d, 0xff, 0x12, 0x13, 0x21, 0xff, 0x0c, 0x12, 0x2c, 0xff, 0x10, 0x16, 0x2f, 0xff, 0x1a, 0x21, 0x36, 0xff, 0x2d, 0x31, 0x40, 0xff, 0x12, 0x12, 0x1e, 0xff, 0x12, 0x13, 0x1d, 0xff, 0x1e, 0x25, 0x2e, 0xff, 0x1c, 0x26, 0x38, 0xff, 0x34, 0x39, 0x4e, 0xff, 0x2e, 0x33, 0x46, 0xff, 0x31, 0x2f, 0x3e, 0xff, 0x42, 0x42, 0x4f, 0xff, 0x28, 0x30, 0x41, 0xff, 0x20, 0x28, 0x3c, 0xff, 0x3c, 0x40, 0x54, 0xff, 0x28, 0x29, 0x39, 0xff, 0x18, 0x17, 0x25, 0xff, 0x15, 0x11, 0x19, 0xff, 0x13, 0x11, 0x19, 0xff, 0x21, 0x24, 0x35, 0xff, 0x23, 0x2c, 0x3e, 0xff, 0x2c, 0x33, 0x45, 0xff, 0x2e, 0x34, 0x45, 0xff, 0x34, 0x3b, 0x4d, 0xff, 0x2f, 0x3a, 0x4f, 0xff, 0x20, 0x2c, 0x47, 0xff, 0x3a, 0x48, 0x66, 0xff, 0x35, 0x44, 0x61, 0xff, 0x39, 0x47, 0x65, 0xff, 0x4b, 0x5d, 0x7d, 0xff, 0x46, 0x59, 0x7d, 0xff, 0x63, 0x79, 0xa0, 0xff, 0x74, 0x8f, 0xb4, 0xff, 0x70, 0x8e, 0xab, 0xff, 0xa7, 0xbc, 0xd7, 0xff, 0xbc, 0xcc, 0xe4, 0xff, 0xbe, 0xcb, 0xde, 0xff, 0xc2, 0xc8, 0xd5, 0xff, 0xc6, 0xc7, 0xd2, 0xff, 0xc0, 0xc5, 0xd2, 0xff, 0xb5, 0xbb, 0xc4, 0xff, 0xcc, 0xcd, 0xce, 0xff, 0xde, 0xde, 0xdf, 0xff, 0xc3, 0xc5, 0xcb, 0xff, 0xa2, 0xab, 0xb6, 0xff, 0x9b, 0xa9, 0xba, 0xff, 0x96, 0xa4, 0xba, 0xff, 0x99, 0xa0, 0xad, 0xff, 0xb3, 0xaf, 0xaf, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x58, 0x61, 0x69, 0xcd, 0x66, 0x74, 0x87, 0xff, 0x90, 0x9e, 0xab, 0xff, 0xc7, 0xd9, 0xe2, 0xff, 0xb2, 0xc3, 0xcf, 0xff, 0x90, 0x9b, 0xad, 0xff, 0x87, 0x96, 0xa5, 0xff, 0x79, 0x88, 0x9b, 0xff, 0x71, 0x81, 0x96, 0xff, 0x6b, 0x7f, 0x93, 0xff, 0x78, 0x8e, 0xa8, 0xff, 0x76, 0x8e, 0xad, 0xff, 0x5d, 0x74, 0x94, 0xff, 0x37, 0x4a, 0x68, 0xff, 0x3a, 0x54, 0x6f, 0xff, 0x3a, 0x51, 0x6c, 0xff, 0x31, 0x40, 0x5d, 0xff, 0x24, 0x2f, 0x4d, 0xff, 0x20, 0x2b, 0x49, 0xff, 0x29, 0x34, 0x52, 0xff, 0x1b, 0x26, 0x44, 0xff, 0x15, 0x20, 0x3d, 0xff, 0x1b, 0x26, 0x45, 0xff, 0x18, 0x24, 0x3f, 0xff, 0x0f, 0x1b, 0x33, 0xff, 0x1c, 0x28, 0x40, 0xff, 0x1a, 0x24, 0x3e, 0xff, 0x12, 0x18, 0x2e, 0xff, 0x12, 0x17, 0x2d, 0xff, 0x20, 0x2a, 0x44, 0xff, 0x2a, 0x39, 0x52, 0xff, 0x1e, 0x2b, 0x45, 0xff, 0x0c, 0x15, 0x2d, 0xff, 0x0a, 0x12, 0x26, 0xff, 0x0e, 0x17, 0x2c, 0xff, 0x11, 0x14, 0x23, 0xff, 0x11, 0x11, 0x1a, 0xff, 0x0a, 0x0e, 0x18, 0xff, 0x11, 0x11, 0x19, 0xff, 0x13, 0x13, 0x17, 0xff, 0x0a, 0x0b, 0x0e, 0xff, 0x0e, 0x0f, 0x0f, 0xff, 0x0f, 0x18, 0x1f, 0xff, 0x23, 0x31, 0x50, 0xff, 0x41, 0x54, 0x7c, 0xff, 0x4a, 0x5f, 0x86, 0xff, 0x4a, 0x61, 0x8c, 0xff, 0x49, 0x61, 0x8e, 0xff, 0x4b, 0x61, 0x8f, 0xff, 0x4d, 0x64, 0x91, 0xff, 0x4f, 0x66, 0x93, 0xff, 0x4e, 0x65, 0x92, 0xff, 0x4b, 0x62, 0x8f, 0xff, 0x4a, 0x61, 0x8e, 0xff, 0x48, 0x5f, 0x8c, 0xff, 0x47, 0x5e, 0x8b, 0xff, 0x46, 0x5d, 0x8a, 0xff, 0x47, 0x5e, 0x8b, 0xff, 0x38, 0x4d, 0x7a, 0xff, 0x35, 0x4b, 0x78, 0xff, 0x33, 0x47, 0x74, 0xff, 0x32, 0x43, 0x6e, 0xff, 0x3f, 0x51, 0x77, 0xff, 0x2d, 0x42, 0x66, 0xff, 0x1b, 0x31, 0x57, 0xff, 0x0e, 0x1c, 0x35, 0xff, 0x17, 0x1b, 0x29, 0xff, 0x19, 0x16, 0x2f, 0xff, 0x23, 0x37, 0x54, 0xff, 0x1e, 0x2c, 0x49, 0xff, 0x1c, 0x1e, 0x35, 0xff, 0x30, 0x37, 0x49, 0xff, 0x20, 0x26, 0x35, 0xff, 0x11, 0x13, 0x1e, 0xff, 0x15, 0x1a, 0x26, 0xff, 0x22, 0x29, 0x3f, 0xff, 0x2b, 0x31, 0x4e, 0xff, 0x3d, 0x45, 0x5a, 0xff, 0x38, 0x3e, 0x54, 0xff, 0x36, 0x3c, 0x4c, 0xff, 0x24, 0x29, 0x35, 0xff, 0x1b, 0x1b, 0x2b, 0xff, 0x2e, 0x33, 0x49, 0xff, 0x2e, 0x36, 0x4e, 0xff, 0x23, 0x2a, 0x40, 0xff, 0x21, 0x27, 0x39, 0xff, 0x1f, 0x25, 0x38, 0xff, 0x15, 0x1a, 0x27, 0xff, 0x1c, 0x1e, 0x28, 0xff, 0x30, 0x30, 0x3f, 0xff, 0x25, 0x2b, 0x3b, 0xff, 0x24, 0x2e, 0x40, 0xff, 0x1c, 0x28, 0x3e, 0xff, 0x2c, 0x3a, 0x55, 0xff, 0x35, 0x45, 0x63, 0xff, 0x37, 0x48, 0x69, 0xff, 0x2e, 0x43, 0x66, 0xff, 0x42, 0x59, 0x7f, 0xff, 0x48, 0x5e, 0x85, 0xff, 0x5b, 0x6f, 0x9a, 0xff, 0x84, 0x9b, 0xc3, 0xff, 0x5c, 0x79, 0x9a, 0xff, 0x80, 0x97, 0xbe, 0xff, 0xb6, 0xc5, 0xe3, 0xff, 0xa8, 0xb9, 0xd1, 0xff, 0xa7, 0xbd, 0xd1, 0xff, 0xa9, 0xba, 0xcc, 0xff, 0xa0, 0xaf, 0xbf, 0xff, 0xbd, 0xc3, 0xca, 0xff, 0xd2, 0xd7, 0xdc, 0xff, 0xa5, 0xab, 0xbc, 0xff, 0x73, 0x7c, 0x97, 0xff, 0x5d, 0x6f, 0x90, 0xff, 0x5c, 0x75, 0x9e, 0xff, 0x5b, 0x78, 0xa7, 0xff, 0x6d, 0x82, 0xa6, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x51, 0x5e, 0x6e, 0x51, 0x6b, 0x79, 0x8b, 0xff, 0x85, 0x97, 0xa9, 0xff, 0xc3, 0xda, 0xea, 0xff, 0xaa, 0xc2, 0xd2, 0xff, 0x88, 0x96, 0xa9, 0xff, 0x88, 0x8d, 0xa2, 0xff, 0x81, 0x87, 0x98, 0xff, 0x71, 0x7d, 0x8b, 0xff, 0x6b, 0x7a, 0x8a, 0xff, 0x73, 0x85, 0x9a, 0xff, 0x65, 0x7a, 0x90, 0xff, 0x40, 0x57, 0x70, 0xff, 0x2b, 0x42, 0x5d, 0xff, 0x39, 0x53, 0x73, 0xff, 0x2c, 0x42, 0x62, 0xff, 0x2a, 0x39, 0x55, 0xff, 0x2c, 0x37, 0x56, 0xff, 0x25, 0x36, 0x55, 0xff, 0x21, 0x34, 0x50, 0xff, 0x17, 0x27, 0x45, 0xff, 0x1a, 0x2a, 0x48, 0xff, 0x21, 0x31, 0x4e, 0xff, 0x1e, 0x2a, 0x46, 0xff, 0x15, 0x1d, 0x3a, 0xff, 0x1d, 0x27, 0x44, 0xff, 0x24, 0x31, 0x4e, 0xff, 0x12, 0x1d, 0x37, 0xff, 0x10, 0x16, 0x2d, 0xff, 0x1e, 0x22, 0x38, 0xff, 0x2a, 0x34, 0x4b, 0xff, 0x14, 0x21, 0x37, 0xff, 0x0c, 0x17, 0x30, 0xff, 0x15, 0x19, 0x36, 0xff, 0x14, 0x17, 0x29, 0xff, 0x0e, 0x11, 0x1e, 0xff, 0x13, 0x15, 0x24, 0xff, 0x0d, 0x0f, 0x1d, 0xff, 0x14, 0x13, 0x20, 0xff, 0x0b, 0x0b, 0x12, 0xff, 0x0b, 0x0d, 0x0f, 0xff, 0x0a, 0x0b, 0x0c, 0xff, 0x14, 0x18, 0x27, 0xff, 0x24, 0x2c, 0x4d, 0xff, 0x3c, 0x4c, 0x70, 0xff, 0x49, 0x5e, 0x86, 0xff, 0x4b, 0x63, 0x8e, 0xff, 0x4a, 0x62, 0x90, 0xff, 0x4b, 0x62, 0x8f, 0xff, 0x4e, 0x65, 0x92, 0xff, 0x50, 0x67, 0x94, 0xff, 0x50, 0x67, 0x93, 0xff, 0x4b, 0x62, 0x8f, 0xff, 0x4a, 0x61, 0x8e, 0xff, 0x49, 0x60, 0x8d, 0xff, 0x49, 0x60, 0x8d, 0xff, 0x49, 0x60, 0x8d, 0xff, 0x4a, 0x61, 0x8e, 0xff, 0x3a, 0x4f, 0x7c, 0xff, 0x46, 0x5a, 0x88, 0xff, 0x42, 0x57, 0x83, 0xff, 0x31, 0x45, 0x6f, 0xff, 0x42, 0x59, 0x7d, 0xff, 0x2e, 0x44, 0x69, 0xff, 0x21, 0x35, 0x5f, 0xff, 0x14, 0x1f, 0x3b, 0xff, 0x1a, 0x18, 0x2f, 0xff, 0x1b, 0x21, 0x3c, 0xff, 0x23, 0x39, 0x53, 0xff, 0x1c, 0x28, 0x42, 0xff, 0x22, 0x24, 0x3a, 0xff, 0x1d, 0x26, 0x39, 0xff, 0x1c, 0x26, 0x38, 0xff, 0x1a, 0x1f, 0x2e, 0xff, 0x1b, 0x19, 0x2d, 0xff, 0x29, 0x2a, 0x40, 0xff, 0x2e, 0x32, 0x4a, 0xff, 0x22, 0x2a, 0x44, 0xff, 0x34, 0x45, 0x5e, 0xff, 0x33, 0x41, 0x58, 0xff, 0x38, 0x3b, 0x4d, 0xff, 0x23, 0x25, 0x2f, 0xff, 0x18, 0x19, 0x26, 0xff, 0x1f, 0x20, 0x30, 0xff, 0x24, 0x27, 0x37, 0xff, 0x1f, 0x23, 0x31, 0xff, 0x23, 0x26, 0x36, 0xff, 0x1a, 0x1c, 0x27, 0xff, 0x0a, 0x0a, 0x11, 0xff, 0x1c, 0x1a, 0x26, 0xff, 0x21, 0x22, 0x32, 0xff, 0x2c, 0x33, 0x44, 0xff, 0x25, 0x2d, 0x43, 0xff, 0x29, 0x31, 0x4f, 0xff, 0x1e, 0x30, 0x4d, 0xff, 0x25, 0x3e, 0x61, 0xff, 0x45, 0x5e, 0x8b, 0xff, 0x42, 0x5d, 0x8a, 0xff, 0x46, 0x63, 0x93, 0xff, 0x51, 0x6a, 0x98, 0xff, 0x65, 0x79, 0xa0, 0xff, 0x64, 0x7a, 0xa5, 0xff, 0x6a, 0x87, 0xb7, 0xff, 0x83, 0x9e, 0xc5, 0xff, 0x96, 0xb3, 0xd4, 0xff, 0x86, 0xa6, 0xc6, 0xff, 0x91, 0xab, 0xc9, 0xff, 0xb5, 0xc0, 0xce, 0xff, 0xcb, 0xcd, 0xdd, 0xff, 0x92, 0x9f, 0xbb, 0xff, 0x64, 0x74, 0x95, 0xff, 0x58, 0x6d, 0x91, 0xff, 0x46, 0x63, 0x87, 0xff, 0x3c, 0x59, 0x82, 0xff, 0x42, 0x61, 0x8e, 0xff, 0x67, 0x84, 0xad, 0x51, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x3b, 0x41, 0x47, 0xce, 0x86, 0x91, 0x9e, 0xff, 0xca, 0xdb, 0xeb, 0xff, 0xb8, 0xca, 0xd5, 0xff, 0xa4, 0xb0, 0xb2, 0xff, 0xb1, 0xb4, 0xb8, 0xff, 0xc7, 0xc3, 0xc6, 0xff, 0xc0, 0xbf, 0xbc, 0xff, 0xc6, 0xc9, 0xbf, 0xff, 0xca, 0xcb, 0xc7, 0xff, 0x9a, 0x95, 0xa7, 0xff, 0x5e, 0x63, 0x71, 0xff, 0x7a, 0x89, 0x91, 0xff, 0x68, 0x71, 0x85, 0xff, 0x3b, 0x49, 0x64, 0xff, 0x2d, 0x3b, 0x5c, 0xff, 0x4b, 0x57, 0x74, 0xff, 0x44, 0x51, 0x6b, 0xff, 0x22, 0x2f, 0x4e, 0xff, 0x1f, 0x30, 0x4c, 0xff, 0x24, 0x38, 0x53, 0xff, 0x2c, 0x43, 0x5e, 0xff, 0x26, 0x36, 0x53, 0xff, 0x1e, 0x27, 0x45, 0xff, 0x17, 0x20, 0x3e, 0xff, 0x28, 0x33, 0x50, 0xff, 0x22, 0x2d, 0x49, 0xff, 0x10, 0x17, 0x34, 0xff, 0x14, 0x19, 0x37, 0xff, 0x22, 0x2c, 0x49, 0xff, 0x0f, 0x1a, 0x2d, 0xff, 0x0c, 0x13, 0x1f, 0xff, 0x16, 0x1d, 0x32, 0xff, 0x0c, 0x10, 0x21, 0xff, 0x0e, 0x14, 0x22, 0xff, 0x19, 0x20, 0x32, 0xff, 0x10, 0x14, 0x25, 0xff, 0x15, 0x14, 0x20, 0xff, 0x0d, 0x0b, 0x10, 0xff, 0x0b, 0x0a, 0x0c, 0xff, 0x08, 0x08, 0x0c, 0xff, 0x17, 0x18, 0x2a, 0xff, 0x21, 0x28, 0x49, 0xff, 0x37, 0x47, 0x69, 0xff, 0x4b, 0x60, 0x86, 0xff, 0x4d, 0x63, 0x8d, 0xff, 0x4d, 0x64, 0x91, 0xff, 0x4f, 0x66, 0x93, 0xff, 0x4f, 0x66, 0x94, 0xff, 0x4e, 0x65, 0x92, 0xff, 0x4c, 0x63, 0x91, 0xff, 0x4a, 0x61, 0x8f, 0xff, 0x4a, 0x61, 0x8e, 0xff, 0x4a, 0x61, 0x8e, 0xff, 0x4a, 0x61, 0x8e, 0xff, 0x4b, 0x62, 0x8f, 0xff, 0x4a, 0x61, 0x8e, 0xff, 0x42, 0x58, 0x86, 0xff, 0x4c, 0x61, 0x90, 0xff, 0x4a, 0x60, 0x8e, 0xff, 0x3b, 0x51, 0x7b, 0xff, 0x35, 0x4d, 0x72, 0xff, 0x2e, 0x45, 0x6c, 0xff, 0x1b, 0x2d, 0x58, 0xff, 0x16, 0x1d, 0x3f, 0xff, 0x26, 0x29, 0x47, 0xff, 0x20, 0x31, 0x4c, 0xff, 0x17, 0x2d, 0x48, 0xff, 0x1c, 0x27, 0x47, 0xff, 0x19, 0x1a, 0x30, 0xff, 0x27, 0x2b, 0x3f, 0xff, 0x22, 0x27, 0x40, 0xff, 0x18, 0x1f, 0x32, 0xff, 0x21, 0x24, 0x35, 0xff, 0x12, 0x17, 0x21, 0xff, 0x27, 0x2e, 0x39, 0xff, 0x32, 0x34, 0x4f, 0xff, 0x28, 0x32, 0x4d, 0xff, 0x2e, 0x3b, 0x57, 0xff, 0x29, 0x32, 0x4e, 0xff, 0x34, 0x3f, 0x51, 0xff, 0x24, 0x28, 0x36, 0xff, 0x15, 0x12, 0x1c, 0xff, 0x21, 0x1d, 0x26, 0xff, 0x13, 0x13, 0x22, 0xff, 0x1d, 0x1d, 0x2c, 0xff, 0x21, 0x22, 0x2f, 0xff, 0x11, 0x16, 0x24, 0xff, 0x1e, 0x23, 0x34, 0xff, 0x21, 0x24, 0x38, 0xff, 0x24, 0x27, 0x3c, 0xff, 0x33, 0x37, 0x4d, 0xff, 0x2e, 0x34, 0x4c, 0xff, 0x22, 0x30, 0x4a, 0xff, 0x25, 0x3c, 0x60, 0xff, 0x42, 0x5f, 0x8d, 0xff, 0x54, 0x74, 0xa5, 0xff, 0x42, 0x63, 0x9c, 0xff, 0x43, 0x61, 0x8e, 0xff, 0x44, 0x56, 0x7b, 0xff, 0x5b, 0x74, 0xa1, 0xff, 0x74, 0x94, 0xc2, 0xff, 0x6e, 0x8b, 0xb7, 0xff, 0x93, 0xad, 0xd8, 0xff, 0x83, 0xa1, 0xc6, 0xff, 0x98, 0xac, 0xca, 0xff, 0xbd, 0xc5, 0xd5, 0xff, 0x9e, 0xaf, 0xc5, 0xff, 0x6d, 0x84, 0xa8, 0xff, 0x56, 0x71, 0x94, 0xff, 0x4f, 0x69, 0x8e, 0xff, 0x6c, 0x7f, 0xa2, 0xff, 0x90, 0x9f, 0xb5, 0xff, 0x9e, 0xa9, 0xba, 0xce, 0xff, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8e, 0x8a, 0x83, 0x46, 0xcb, 0xc3, 0xbd, 0xff, 0xe3, 0xdc, 0xdb, 0xff, 0xdc, 0xd7, 0xd6, 0xff, 0xdf, 0xda, 0xcf, 0xff, 0xe9, 0xe3, 0xd3, 0xff, 0xf5, 0xe9, 0xdf, 0xff, 0xf4, 0xe8, 0xde, 0xff, 0xf0, 0xeb, 0xda, 0xff, 0xae, 0xae, 0xa8, 0xff, 0x85, 0x81, 0x89, 0xff, 0xc5, 0xc3, 0xb7, 0xff, 0xec, 0xe5, 0xda, 0xff, 0x88, 0x89, 0x98, 0xff, 0x3d, 0x4b, 0x65, 0xff, 0x3c, 0x46, 0x64, 0xff, 0x6f, 0x79, 0x8b, 0xff, 0x7a, 0x7f, 0x88, 0xff, 0x44, 0x49, 0x5e, 0xff, 0x35, 0x42, 0x58, 0xff, 0x32, 0x40, 0x5e, 0xff, 0x2f, 0x45, 0x6b, 0xff, 0x2a, 0x40, 0x64, 0xff, 0x28, 0x38, 0x57, 0xff, 0x1c, 0x29, 0x49, 0xff, 0x24, 0x2e, 0x4d, 0xff, 0x2d, 0x37, 0x56, 0xff, 0x18, 0x24, 0x44, 0xff, 0x0d, 0x1e, 0x3e, 0xff, 0x14, 0x28, 0x46, 0xff, 0x12, 0x1d, 0x32, 0xff, 0x0b, 0x08, 0x10, 0xff, 0x0e, 0x10, 0x1c, 0xff, 0x0b, 0x10, 0x1e, 0xff, 0x13, 0x15, 0x22, 0xff, 0x16, 0x1a, 0x29, 0xff, 0x09, 0x0d, 0x1c, 0xff, 0x14, 0x13, 0x1a, 0xff, 0x15, 0x0f, 0x10, 0xff, 0x0b, 0x07, 0x0a, 0xff, 0x0a, 0x09, 0x10, 0xff, 0x16, 0x18, 0x29, 0xff, 0x1e, 0x26, 0x47, 0xff, 0x2b, 0x3c, 0x5f, 0xff, 0x46, 0x5a, 0x7f, 0xff, 0x4e, 0x63, 0x8b, 0xff, 0x4f, 0x65, 0x91, 0xff, 0x50, 0x67, 0x94, 0xff, 0x4d, 0x64, 0x90, 0xff, 0x4b, 0x62, 0x90, 0xff, 0x4a, 0x62, 0x90, 0xff, 0x49, 0x60, 0x8e, 0xff, 0x48, 0x5f, 0x8c, 0xff, 0x49, 0x60, 0x8d, 0xff, 0x48, 0x5f, 0x8c, 0xff, 0x4a, 0x61, 0x8d, 0xff, 0x49, 0x60, 0x8d, 0xff, 0x44, 0x5b, 0x89, 0xff, 0x4d, 0x65, 0x92, 0xff, 0x4a, 0x60, 0x8e, 0xff, 0x46, 0x5b, 0x88, 0xff, 0x36, 0x4d, 0x75, 0xff, 0x25, 0x3c, 0x65, 0xff, 0x12, 0x24, 0x4d, 0xff, 0x0e, 0x1b, 0x3c, 0xff, 0x1e, 0x31, 0x54, 0xff, 0x25, 0x3f, 0x63, 0xff, 0x14, 0x2d, 0x53, 0xff, 0x15, 0x23, 0x47, 0xff, 0x10, 0x12, 0x25, 0xff, 0x1a, 0x19, 0x29, 0xff, 0x2b, 0x32, 0x49, 0xff, 0x1f, 0x2a, 0x3f, 0xff, 0x1f, 0x27, 0x3d, 0xff, 0x1b, 0x21, 0x34, 0xff, 0x13, 0x1a, 0x28, 0xff, 0x2e, 0x35, 0x49, 0xff, 0x37, 0x3f, 0x5c, 0xff, 0x24, 0x2c, 0x44, 0xff, 0x18, 0x1f, 0x35, 0xff, 0x1c, 0x26, 0x39, 0xff, 0x38, 0x40, 0x54, 0xff, 0x22, 0x21, 0x30, 0xff, 0x12, 0x0e, 0x16, 0xff, 0x19, 0x19, 0x28, 0xff, 0x22, 0x23, 0x33, 0xff, 0x1f, 0x1f, 0x2d, 0xff, 0x1e, 0x20, 0x2e, 0xff, 0x1c, 0x22, 0x33, 0xff, 0x26, 0x2c, 0x41, 0xff, 0x22, 0x26, 0x3c, 0xff, 0x27, 0x2c, 0x40, 0xff, 0x2a, 0x35, 0x4c, 0xff, 0x2d, 0x3c, 0x5a, 0xff, 0x3d, 0x4e, 0x72, 0xff, 0x39, 0x55, 0x7f, 0xff, 0x41, 0x62, 0x92, 0xff, 0x45, 0x5e, 0x8f, 0xff, 0x31, 0x4e, 0x7c, 0xff, 0x48, 0x5f, 0x8a, 0xff, 0x4b, 0x63, 0x85, 0xff, 0x4d, 0x62, 0x87, 0xff, 0x60, 0x74, 0xa1, 0xff, 0x67, 0x7e, 0xaa, 0xff, 0x67, 0x81, 0xa4, 0xff, 0x91, 0xaa, 0xc6, 0xff, 0xa2, 0xb8, 0xd4, 0xff, 0x80, 0x9c, 0xc0, 0xff, 0x86, 0xa1, 0xc4, 0xff, 0x7d, 0x9a, 0xbe, 0xff, 0x59, 0x74, 0x9f, 0xff, 0x7d, 0x8f, 0xb6, 0xff, 0xad, 0xb8, 0xcf, 0xff, 0xb7, 0xba, 0xc9, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe1, 0xd4, 0xc6, 0xb4, 0xd6, 0xcc, 0xc1, 0xff, 0xd6, 0xca, 0xc3, 0xff, 0xde, 0xce, 0xc7, 0xff, 0xe3, 0xdc, 0xcd, 0xff, 0xec, 0xe2, 0xd5, 0xff, 0xdb, 0xd2, 0xcf, 0xff, 0x91, 0x90, 0x9b, 0xff, 0x71, 0x73, 0x80, 0xff, 0xc0, 0xbd, 0xb8, 0xff, 0xf6, 0xed, 0xdf, 0xff, 0xd1, 0xc5, 0xc6, 0xff, 0x61, 0x67, 0x7c, 0xff, 0x27, 0x38, 0x58, 0xff, 0x61, 0x6a, 0x7a, 0xff, 0x9f, 0x9e, 0xa5, 0xff, 0x7f, 0x7e, 0x8a, 0xff, 0x48, 0x4f, 0x62, 0xff, 0x4c, 0x53, 0x65, 0xff, 0x4c, 0x52, 0x6b, 0xff, 0x35, 0x48, 0x73, 0xff, 0x2d, 0x47, 0x71, 0xff, 0x2a, 0x43, 0x64, 0xff, 0x25, 0x38, 0x5b, 0xff, 0x14, 0x25, 0x46, 0xff, 0x16, 0x28, 0x48, 0xff, 0x22, 0x34, 0x55, 0xff, 0x1b, 0x31, 0x53, 0xff, 0x16, 0x2e, 0x4c, 0xff, 0x1a, 0x2e, 0x4b, 0xff, 0x0a, 0x11, 0x25, 0xff, 0x0c, 0x10, 0x1f, 0xff, 0x12, 0x19, 0x2a, 0xff, 0x13, 0x19, 0x2b, 0xff, 0x0f, 0x13, 0x23, 0xff, 0x0a, 0x09, 0x13, 0xff, 0x0e, 0x08, 0x0d, 0xff, 0x0e, 0x08, 0x09, 0xff, 0x06, 0x04, 0x07, 0xff, 0x05, 0x04, 0x0a, 0xff, 0x15, 0x17, 0x28, 0xff, 0x21, 0x28, 0x4a, 0xff, 0x20, 0x31, 0x53, 0xff, 0x38, 0x4c, 0x72, 0xff, 0x4f, 0x64, 0x8d, 0xff, 0x4e, 0x65, 0x91, 0xff, 0x4a, 0x61, 0x8f, 0xff, 0x46, 0x5d, 0x8a, 0xff, 0x46, 0x5e, 0x8c, 0xff, 0x45, 0x5c, 0x8b, 0xff, 0x41, 0x58, 0x87, 0xff, 0x40, 0x58, 0x85, 0xff, 0x43, 0x5a, 0x88, 0xff, 0x45, 0x5c, 0x89, 0xff, 0x48, 0x5f, 0x8c, 0xff, 0x46, 0x5d, 0x8b, 0xff, 0x45, 0x5e, 0x87, 0xff, 0x4a, 0x64, 0x8b, 0xff, 0x46, 0x5e, 0x88, 0xff, 0x3f, 0x54, 0x80, 0xff, 0x38, 0x4f, 0x76, 0xff, 0x2a, 0x3e, 0x67, 0xff, 0x18, 0x2a, 0x52, 0xff, 0x0c, 0x23, 0x41, 0xff, 0x14, 0x26, 0x46, 0xff, 0x1e, 0x34, 0x56, 0xff, 0x13, 0x2e, 0x50, 0xff, 0x11, 0x20, 0x3e, 0xff, 0x15, 0x1a, 0x2a, 0xff, 0x05, 0x05, 0x0c, 0xff, 0x12, 0x16, 0x23, 0xff, 0x23, 0x27, 0x3e, 0xff, 0x22, 0x27, 0x3e, 0xff, 0x27, 0x2e, 0x3e, 0xff, 0x20, 0x27, 0x34, 0xff, 0x23, 0x2a, 0x3f, 0xff, 0x26, 0x2f, 0x4d, 0xff, 0x1b, 0x23, 0x3a, 0xff, 0x33, 0x37, 0x49, 0xff, 0x1a, 0x20, 0x2e, 0xff, 0x1a, 0x1e, 0x2e, 0xff, 0x2a, 0x2c, 0x40, 0xff, 0x1d, 0x21, 0x32, 0xff, 0x15, 0x17, 0x22, 0xff, 0x1e, 0x20, 0x29, 0xff, 0x20, 0x22, 0x2c, 0xff, 0x1c, 0x1d, 0x2c, 0xff, 0x1e, 0x24, 0x34, 0xff, 0x20, 0x28, 0x39, 0xff, 0x2a, 0x31, 0x42, 0xff, 0x32, 0x38, 0x49, 0xff, 0x2b, 0x32, 0x45, 0xff, 0x2e, 0x39, 0x53, 0xff, 0x3c, 0x4e, 0x6a, 0xff, 0x48, 0x61, 0x90, 0xff, 0x35, 0x53, 0x83, 0xff, 0x3e, 0x5b, 0x85, 0xff, 0x40, 0x5d, 0x97, 0xff, 0x3b, 0x51, 0x77, 0xff, 0x22, 0x2f, 0x43, 0xff, 0x20, 0x34, 0x55, 0xff, 0x55, 0x67, 0x91, 0xff, 0x66, 0x75, 0x9d, 0xff, 0x54, 0x6d, 0x95, 0xff, 0x56, 0x70, 0x95, 0xff, 0x72, 0x87, 0xa8, 0xff, 0x7f, 0x9a, 0xc2, 0xff, 0x79, 0x95, 0xbc, 0xff, 0x77, 0x95, 0xbe, 0xff, 0x64, 0x85, 0xae, 0xff, 0x74, 0x94, 0xba, 0xff, 0x72, 0x8f, 0xbb, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd1, 0xca, 0xc4, 0x27, 0xd0, 0xc6, 0xbf, 0xfa, 0xd4, 0xca, 0xc1, 0xff, 0xd8, 0xcd, 0xc2, 0xff, 0xdb, 0xd6, 0xcc, 0xff, 0xd2, 0xc9, 0xc2, 0xff, 0xaf, 0xac, 0xae, 0xff, 0x5f, 0x69, 0x7a, 0xff, 0x82, 0x89, 0x9a, 0xff, 0xaf, 0xa9, 0xb7, 0xff, 0x96, 0x93, 0x9d, 0xff, 0x68, 0x70, 0x82, 0xff, 0x39, 0x46, 0x61, 0xff, 0x3a, 0x4c, 0x67, 0xff, 0x65, 0x68, 0x73, 0xff, 0x9e, 0x99, 0x9f, 0xff, 0x81, 0x84, 0x92, 0xff, 0x38, 0x3f, 0x57, 0xff, 0x3c, 0x46, 0x5c, 0xff, 0x55, 0x5a, 0x68, 0xff, 0x5c, 0x68, 0x83, 0xff, 0x34, 0x53, 0x76, 0xff, 0x1e, 0x39, 0x5c, 0xff, 0x26, 0x3b, 0x5e, 0xff, 0x0e, 0x25, 0x45, 0xff, 0x19, 0x2e, 0x50, 0xff, 0x2f, 0x49, 0x6f, 0xff, 0x22, 0x3a, 0x63, 0xff, 0x18, 0x31, 0x54, 0xff, 0x16, 0x2c, 0x53, 0xff, 0x10, 0x23, 0x44, 0xff, 0x11, 0x1e, 0x38, 0xff, 0x14, 0x1a, 0x37, 0xff, 0x0a, 0x11, 0x2e, 0xff, 0x0c, 0x15, 0x2e, 0xff, 0x0c, 0x11, 0x1e, 0xff, 0x08, 0x05, 0x04, 0xff, 0x0e, 0x09, 0x05, 0xff, 0x02, 0x00, 0x03, 0xff, 0x02, 0x04, 0x0c, 0xff, 0x12, 0x17, 0x2b, 0xff, 0x1d, 0x26, 0x45, 0xff, 0x1a, 0x2b, 0x49, 0xff, 0x2b, 0x3f, 0x63, 0xff, 0x49, 0x5f, 0x86, 0xff, 0x4f, 0x65, 0x8f, 0xff, 0x48, 0x5e, 0x8b, 0xff, 0x43, 0x59, 0x84, 0xff, 0x41, 0x57, 0x84, 0xff, 0x3d, 0x54, 0x81, 0xff, 0x38, 0x4e, 0x7b, 0xff, 0x39, 0x4d, 0x79, 0xff, 0x3d, 0x52, 0x7d, 0xff, 0x42, 0x56, 0x82, 0xff, 0x47, 0x5b, 0x87, 0xff, 0x44, 0x59, 0x85, 0xff, 0x44, 0x5b, 0x85, 0xff, 0x48, 0x5f, 0x88, 0xff, 0x41, 0x59, 0x82, 0xff, 0x37, 0x4f, 0x79, 0xff, 0x34, 0x4b, 0x70, 0xff, 0x24, 0x35, 0x58, 0xff, 0x1b, 0x33, 0x5f, 0xff, 0x28, 0x51, 0x7c, 0xff, 0x09, 0x1c, 0x3c, 0xff, 0x11, 0x1a, 0x38, 0xff, 0x1a, 0x29, 0x49, 0xff, 0x15, 0x25, 0x43, 0xff, 0x19, 0x1d, 0x31, 0xff, 0x10, 0x0f, 0x1a, 0xff, 0x19, 0x19, 0x2c, 0xff, 0x21, 0x22, 0x3b, 0xff, 0x19, 0x1d, 0x2f, 0xff, 0x15, 0x19, 0x27, 0xff, 0x14, 0x19, 0x27, 0xff, 0x15, 0x1d, 0x30, 0xff, 0x1c, 0x23, 0x3b, 0xff, 0x1e, 0x28, 0x3b, 0xff, 0x2a, 0x30, 0x44, 0xff, 0x21, 0x22, 0x32, 0xff, 0x12, 0x15, 0x23, 0xff, 0x17, 0x1d, 0x30, 0xff, 0x1e, 0x23, 0x35, 0xff, 0x14, 0x16, 0x1e, 0xff, 0x1f, 0x1f, 0x28, 0xff, 0x2b, 0x2d, 0x3b, 0xff, 0x1f, 0x27, 0x33, 0xff, 0x1b, 0x22, 0x32, 0xff, 0x20, 0x24, 0x37, 0xff, 0x29, 0x2c, 0x3e, 0xff, 0x43, 0x45, 0x54, 0xff, 0x2c, 0x2d, 0x3a, 0xff, 0x21, 0x27, 0x3d, 0xff, 0x21, 0x2f, 0x48, 0xff, 0x3a, 0x49, 0x69, 0xff, 0x4a, 0x54, 0x79, 0xff, 0x4e, 0x60, 0x8f, 0xff, 0x57, 0x71, 0xa2, 0xff, 0x17, 0x29, 0x41, 0xff, 0x1e, 0x2c, 0x46, 0xff, 0x72, 0x85, 0xb2, 0xff, 0x74, 0x89, 0xb6, 0xff, 0x78, 0x89, 0xb0, 0xff, 0x9e, 0xb9, 0xdc, 0xff, 0x8e, 0xa8, 0xca, 0xff, 0x4a, 0x61, 0x84, 0xff, 0x48, 0x61, 0x86, 0xff, 0x60, 0x7a, 0xa2, 0xff, 0x5a, 0x78, 0xa4, 0xff, 0x64, 0x85, 0xb2, 0xff, 0x72, 0x90, 0xbb, 0xfa, 0x72, 0x8c, 0xb8, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd3, 0xc8, 0xc4, 0x88, 0xd7, 0xcd, 0xc4, 0xff, 0xdb, 0xd1, 0xc6, 0xff, 0xd3, 0xca, 0xc4, 0xff, 0xc9, 0xc4, 0xbe, 0xff, 0xa7, 0xa9, 0xab, 0xff, 0x79, 0x81, 0x8f, 0xff, 0x91, 0x97, 0xaa, 0xff, 0x63, 0x6d, 0x8b, 0xff, 0x49, 0x5c, 0x75, 0xff, 0x4d, 0x5f, 0x74, 0xff, 0x5b, 0x69, 0x85, 0xff, 0x5b, 0x6a, 0x7f, 0xff, 0x60, 0x69, 0x7d, 0xff, 0x78, 0x7a, 0x8a, 0xff, 0x87, 0x87, 0x8f, 0xff, 0x37, 0x3d, 0x4f, 0xff, 0x2f, 0x3b, 0x50, 0xff, 0x77, 0x72, 0x7a, 0xff, 0xa1, 0x9a, 0x9e, 0xff, 0x5b, 0x66, 0x78, 0xff, 0x20, 0x32, 0x50, 0xff, 0x1e, 0x37, 0x5c, 0xff, 0x24, 0x3a, 0x60, 0xff, 0x24, 0x36, 0x5e, 0xff, 0x2a, 0x45, 0x72, 0xff, 0x28, 0x44, 0x74, 0xff, 0x23, 0x41, 0x6c, 0xff, 0x1a, 0x38, 0x64, 0xff, 0x1b, 0x33, 0x57, 0xff, 0x14, 0x27, 0x47, 0xff, 0x14, 0x27, 0x4c, 0xff, 0x09, 0x1e, 0x40, 0xff, 0x02, 0x18, 0x39, 0xff, 0x05, 0x0f, 0x2a, 0xff, 0x04, 0x02, 0x05, 0xff, 0x0d, 0x0a, 0x07, 0xff, 0x07, 0x06, 0x0e, 0xff, 0x03, 0x09, 0x13, 0xff, 0x0f, 0x14, 0x2d, 0xff, 0x16, 0x20, 0x42, 0xff, 0x16, 0x26, 0x44, 0xff, 0x1f, 0x2e, 0x52, 0xff, 0x3c, 0x4e, 0x75, 0xff, 0x4c, 0x5f, 0x8c, 0xff, 0x46, 0x5b, 0x8a, 0xff, 0x3f, 0x54, 0x81, 0xff, 0x3c, 0x50, 0x7a, 0xff, 0x37, 0x4b, 0x75, 0xff, 0x33, 0x47, 0x70, 0xff, 0x37, 0x49, 0x70, 0xff, 0x3e, 0x4f, 0x77, 0xff, 0x41, 0x53, 0x7a, 0xff, 0x44, 0x55, 0x7d, 0xff, 0x47, 0x58, 0x7f, 0xff, 0x41, 0x53, 0x7e, 0xff, 0x3f, 0x53, 0x7f, 0xff, 0x3b, 0x50, 0x79, 0xff, 0x31, 0x46, 0x6d, 0xff, 0x26, 0x3b, 0x65, 0xff, 0x1d, 0x35, 0x63, 0xff, 0x2e, 0x55, 0x87, 0xff, 0x32, 0x5e, 0x91, 0xff, 0x1b, 0x39, 0x63, 0xff, 0x0b, 0x16, 0x38, 0xff, 0x1e, 0x20, 0x40, 0xff, 0x1f, 0x2a, 0x46, 0xff, 0x1b, 0x22, 0x32, 0xff, 0x13, 0x17, 0x24, 0xff, 0x1d, 0x21, 0x3e, 0xff, 0x1b, 0x22, 0x40, 0xff, 0x18, 0x1d, 0x33, 0xff, 0x1d, 0x1f, 0x36, 0xff, 0x1d, 0x20, 0x38, 0xff, 0x1d, 0x23, 0x36, 0xff, 0x15, 0x1b, 0x2b, 0xff, 0x15, 0x1c, 0x24, 0xff, 0x1e, 0x24, 0x34, 0xff, 0x1e, 0x24, 0x3c, 0xff, 0x17, 0x1b, 0x28, 0xff, 0x18, 0x1b, 0x27, 0xff, 0x16, 0x18, 0x26, 0xff, 0x19, 0x1c, 0x23, 0xff, 0x21, 0x21, 0x2a, 0xff, 0x1f, 0x21, 0x33, 0xff, 0x22, 0x2d, 0x3b, 0xff, 0x1f, 0x26, 0x39, 0xff, 0x26, 0x26, 0x3c, 0xff, 0x2a, 0x2a, 0x3c, 0xff, 0x33, 0x35, 0x43, 0xff, 0x26, 0x2b, 0x3d, 0xff, 0x1e, 0x25, 0x3d, 0xff, 0x22, 0x2b, 0x41, 0xff, 0x2b, 0x37, 0x50, 0xff, 0x54, 0x67, 0x95, 0xff, 0x66, 0x85, 0xb4, 0xff, 0x29, 0x47, 0x61, 0xff, 0x25, 0x35, 0x50, 0xff, 0x5a, 0x6e, 0x98, 0xff, 0x66, 0x77, 0xa7, 0xff, 0x77, 0x85, 0xac, 0xff, 0x75, 0x8d, 0xb1, 0xff, 0x7a, 0x95, 0xb7, 0xff, 0x75, 0x90, 0xb7, 0xff, 0x63, 0x7e, 0xa8, 0xff, 0x2a, 0x45, 0x6c, 0xff, 0x40, 0x59, 0x81, 0xff, 0x53, 0x70, 0x9e, 0xff, 0x58, 0x76, 0xa4, 0xff, 0x6f, 0x89, 0xb2, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc6, 0xc6, 0xc6, 0x09, 0xd5, 0xca, 0xc2, 0xdb, 0xd0, 0xc5, 0xbe, 0xff, 0xc2, 0xb9, 0xb6, 0xff, 0xc2, 0xc1, 0xbd, 0xff, 0x91, 0x98, 0x9e, 0xff, 0x72, 0x77, 0x89, 0xff, 0x87, 0x89, 0xa1, 0xff, 0x6d, 0x79, 0x92, 0xff, 0x83, 0x92, 0xa4, 0xff, 0x8f, 0x99, 0xab, 0xff, 0x93, 0x96, 0xa6, 0xff, 0x85, 0x8d, 0x9c, 0xff, 0x5a, 0x6c, 0x8a, 0xff, 0x44, 0x4f, 0x66, 0xff, 0x59, 0x5c, 0x69, 0xff, 0x76, 0x77, 0x85, 0xff, 0x7f, 0x84, 0x93, 0xff, 0x89, 0x8d, 0x9f, 0xff, 0x7b, 0x84, 0x9d, 0xff, 0x45, 0x59, 0x7c, 0xff, 0x26, 0x3b, 0x5f, 0xff, 0x1b, 0x35, 0x5a, 0xff, 0x32, 0x50, 0x7c, 0xff, 0x39, 0x56, 0x86, 0xff, 0x21, 0x44, 0x77, 0xff, 0x28, 0x4c, 0x7f, 0xff, 0x34, 0x58, 0x87, 0xff, 0x2e, 0x57, 0x86, 0xff, 0x23, 0x3c, 0x63, 0xff, 0x11, 0x2a, 0x4d, 0xff, 0x18, 0x3e, 0x69, 0xff, 0x17, 0x3e, 0x66, 0xff, 0x07, 0x2d, 0x57, 0xff, 0x0a, 0x1c, 0x44, 0xff, 0x08, 0x06, 0x0e, 0xff, 0x05, 0x00, 0x00, 0xff, 0x0b, 0x0c, 0x12, 0xff, 0x0b, 0x12, 0x24, 0xff, 0x13, 0x19, 0x36, 0xff, 0x17, 0x20, 0x45, 0xff, 0x19, 0x27, 0x49, 0xff, 0x16, 0x22, 0x46, 0xff, 0x2a, 0x37, 0x5f, 0xff, 0x47, 0x5a, 0x81, 0xff, 0x43, 0x5c, 0x80, 0xff, 0x3b, 0x52, 0x77, 0xff, 0x36, 0x49, 0x6f, 0xff, 0x2f, 0x40, 0x68, 0xff, 0x2f, 0x41, 0x67, 0xff, 0x36, 0x46, 0x6a, 0xff, 0x3d, 0x4d, 0x72, 0xff, 0x42, 0x52, 0x77, 0xff, 0x44, 0x53, 0x79, 0xff, 0x43, 0x55, 0x79, 0xff, 0x41, 0x53, 0x7d, 0xff, 0x3f, 0x4d, 0x79, 0xff, 0x3a, 0x45, 0x6b, 0xff, 0x27, 0x33, 0x58, 0xff, 0x13, 0x28, 0x55, 0xff, 0x31, 0x53, 0x8d, 0xff, 0x36, 0x66, 0x9a, 0xff, 0x1e, 0x42, 0x73, 0xff, 0x27, 0x49, 0x7b, 0xff, 0x23, 0x45, 0x68, 0xff, 0x19, 0x2a, 0x45, 0xff, 0x19, 0x1d, 0x38, 0xff, 0x12, 0x1d, 0x2b, 0xff, 0x11, 0x1d, 0x26, 0xff, 0x1b, 0x23, 0x37, 0xff, 0x17, 0x23, 0x38, 0xff, 0x12, 0x1c, 0x2e, 0xff, 0x1b, 0x23, 0x35, 0xff, 0x22, 0x29, 0x3c, 0xff, 0x1d, 0x1d, 0x32, 0xff, 0x1a, 0x1c, 0x27, 0xff, 0x24, 0x2c, 0x36, 0xff, 0x1c, 0x23, 0x3a, 0xff, 0x18, 0x1a, 0x2c, 0xff, 0x17, 0x17, 0x21, 0xff, 0x1a, 0x1a, 0x23, 0xff, 0x11, 0x10, 0x1b, 0xff, 0x11, 0x13, 0x1b, 0xff, 0x13, 0x13, 0x1c, 0xff, 0x1b, 0x1c, 0x2e, 0xff, 0x18, 0x1e, 0x31, 0xff, 0x1f, 0x23, 0x3a, 0xff, 0x24, 0x26, 0x3c, 0xff, 0x24, 0x25, 0x36, 0xff, 0x24, 0x28, 0x38, 0xff, 0x2a, 0x33, 0x49, 0xff, 0x21, 0x29, 0x3c, 0xff, 0x1a, 0x20, 0x33, 0xff, 0x16, 0x20, 0x3a, 0xff, 0x36, 0x4a, 0x6c, 0xff, 0x41, 0x58, 0x77, 0xff, 0x11, 0x1e, 0x2c, 0xff, 0x29, 0x34, 0x4e, 0xff, 0x54, 0x6f, 0x98, 0xff, 0x55, 0x7b, 0xac, 0xff, 0x6c, 0x84, 0xb3, 0xff, 0x69, 0x82, 0xae, 0xff, 0x6b, 0x86, 0xb9, 0xff, 0x66, 0x84, 0xb2, 0xff, 0x68, 0x85, 0xb0, 0xff, 0x2a, 0x45, 0x72, 0xff, 0x35, 0x50, 0x78, 0xff, 0x65, 0x83, 0xac, 0xff, 0x7b, 0x96, 0xc1, 0xdb, 0x8d, 0xaa, 0xc6, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd3, 0xc8, 0xc0, 0x41, 0xc9, 0xc0, 0xb8, 0xfe, 0xc4, 0xbe, 0xba, 0xff, 0xcc, 0xc5, 0xc0, 0xff, 0x9d, 0xa1, 0xab, 0xff, 0x72, 0x7a, 0x8c, 0xff, 0x99, 0x97, 0xa6, 0xff, 0x92, 0x9c, 0xa8, 0xff, 0xa8, 0xab, 0xb5, 0xff, 0x98, 0x9d, 0xa9, 0xff, 0x85, 0x8e, 0x9d, 0xff, 0x74, 0x81, 0x9b, 0xff, 0x4b, 0x60, 0x81, 0xff, 0x60, 0x70, 0x82, 0xff, 0x7a, 0x88, 0x99, 0xff, 0x91, 0xa5, 0xc0, 0xff, 0x6a, 0x86, 0xa9, 0xff, 0x3f, 0x60, 0x8d, 0xff, 0x25, 0x4d, 0x81, 0xff, 0x24, 0x45, 0x74, 0xff, 0x29, 0x44, 0x6f, 0xff, 0x24, 0x41, 0x74, 0xff, 0x21, 0x48, 0x82, 0xff, 0x40, 0x6d, 0xa7, 0xff, 0x45, 0x71, 0xaa, 0xff, 0x2e, 0x55, 0x8e, 0xff, 0x30, 0x5a, 0x8f, 0xff, 0x24, 0x4c, 0x7e, 0xff, 0x16, 0x32, 0x5a, 0xff, 0x22, 0x42, 0x6d, 0xff, 0x1e, 0x49, 0x7c, 0xff, 0x16, 0x3f, 0x6e, 0xff, 0x19, 0x3f, 0x6e, 0xff, 0x17, 0x3d, 0x67, 0xff, 0x0d, 0x25, 0x3c, 0xff, 0x00, 0x02, 0x08, 0xff, 0x05, 0x02, 0x04, 0xff, 0x0b, 0x10, 0x27, 0xff, 0x11, 0x1a, 0x3a, 0xff, 0x17, 0x1f, 0x40, 0xff, 0x1d, 0x28, 0x4b, 0xff, 0x16, 0x22, 0x46, 0xff, 0x1d, 0x27, 0x4c, 0xff, 0x3c, 0x4b, 0x70, 0xff, 0x44, 0x57, 0x7b, 0xff, 0x3b, 0x4d, 0x71, 0xff, 0x2d, 0x3f, 0x63, 0xff, 0x23, 0x35, 0x59, 0xff, 0x26, 0x38, 0x5d, 0xff, 0x2f, 0x41, 0x66, 0xff, 0x37, 0x48, 0x6d, 0xff, 0x3c, 0x4f, 0x73, 0xff, 0x3f, 0x53, 0x75, 0xff, 0x40, 0x52, 0x74, 0xff, 0x3e, 0x4f, 0x6d, 0xff, 0x30, 0x3d, 0x61, 0xff, 0x2a, 0x32, 0x5c, 0xff, 0x25, 0x38, 0x5e, 0xff, 0x23, 0x48, 0x75, 0xff, 0x33, 0x61, 0x93, 0xff, 0x2b, 0x54, 0x86, 0xff, 0x2b, 0x4d, 0x80, 0xff, 0x23, 0x4d, 0x80, 0xff, 0x1b, 0x49, 0x73, 0xff, 0x1c, 0x38, 0x59, 0xff, 0x12, 0x1f, 0x3b, 0xff, 0x19, 0x1f, 0x36, 0xff, 0x1d, 0x25, 0x38, 0xff, 0x1c, 0x23, 0x37, 0xff, 0x1b, 0x27, 0x3a, 0xff, 0x1a, 0x24, 0x39, 0xff, 0x19, 0x21, 0x37, 0xff, 0x1a, 0x21, 0x38, 0xff, 0x22, 0x28, 0x3e, 0xff, 0x24, 0x27, 0x37, 0xff, 0x20, 0x25, 0x3a, 0xff, 0x1d, 0x23, 0x39, 0xff, 0x17, 0x17, 0x24, 0xff, 0x0f, 0x0d, 0x16, 0xff, 0x18, 0x18, 0x21, 0xff, 0x17, 0x16, 0x20, 0xff, 0x0c, 0x0a, 0x16, 0xff, 0x11, 0x0f, 0x1a, 0xff, 0x20, 0x1f, 0x2b, 0xff, 0x1d, 0x1d, 0x2c, 0xff, 0x1e, 0x21, 0x34, 0xff, 0x1b, 0x22, 0x34, 0xff, 0x13, 0x1b, 0x2c, 0xff, 0x1f, 0x26, 0x37, 0xff, 0x28, 0x2d, 0x40, 0xff, 0x13, 0x16, 0x22, 0xff, 0x18, 0x1b, 0x26, 0xff, 0x14, 0x17, 0x26, 0xff, 0x1e, 0x23, 0x32, 0xff, 0x1f, 0x21, 0x38, 0xff, 0x14, 0x14, 0x2e, 0xff, 0x17, 0x1f, 0x3b, 0xff, 0x46, 0x56, 0x77, 0xff, 0x74, 0xa9, 0xce, 0xff, 0x70, 0xa3, 0xd0, 0xff, 0x69, 0x8c, 0xbc, 0xff, 0x7b, 0xa2, 0xd2, 0xff, 0x87, 0xae, 0xd5, 0xff, 0x5c, 0x7e, 0xa3, 0xff, 0x4a, 0x68, 0x93, 0xff, 0x94, 0xb7, 0xdd, 0xff, 0xb0, 0xd1, 0xec, 0xfe, 0xa4, 0xc0, 0xe3, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcb, 0xc2, 0xb9, 0x8f, 0xc6, 0xc1, 0xbe, 0xff, 0xc6, 0xc1, 0xbd, 0xff, 0xb8, 0xb5, 0xb8, 0xff, 0x78, 0x7f, 0x95, 0xff, 0xa5, 0xa0, 0xac, 0xff, 0xbd, 0xba, 0xb6, 0xff, 0xab, 0xa8, 0xae, 0xff, 0x96, 0x99, 0xa6, 0xff, 0x83, 0x8f, 0xa9, 0xff, 0x7b, 0x96, 0xb9, 0xff, 0x72, 0x96, 0xbf, 0xff, 0x5d, 0x79, 0x9c, 0xff, 0x4d, 0x6b, 0x8b, 0xff, 0x39, 0x5f, 0x86, 0xff, 0x2c, 0x52, 0x80, 0xff, 0x2d, 0x4d, 0x83, 0xff, 0x27, 0x47, 0x78, 0xff, 0x22, 0x3e, 0x6f, 0xff, 0x28, 0x49, 0x83, 0xff, 0x23, 0x4b, 0x8b, 0xff, 0x0e, 0x37, 0x77, 0xff, 0x09, 0x34, 0x71, 0xff, 0x2a, 0x57, 0x96, 0xff, 0x47, 0x72, 0xb1, 0xff, 0x31, 0x5f, 0x98, 0xff, 0x1e, 0x48, 0x7d, 0xff, 0x30, 0x5d, 0x9a, 0xff, 0x39, 0x69, 0xaa, 0xff, 0x29, 0x58, 0x92, 0xff, 0x1d, 0x4c, 0x82, 0xff, 0x1d, 0x46, 0x7a, 0xff, 0x19, 0x44, 0x74, 0xff, 0x12, 0x42, 0x75, 0xff, 0x10, 0x2d, 0x5c, 0xff, 0x07, 0x0f, 0x2a, 0xff, 0x06, 0x05, 0x1a, 0xff, 0x12, 0x17, 0x33, 0xff, 0x15, 0x1f, 0x40, 0xff, 0x18, 0x25, 0x47, 0xff, 0x1c, 0x29, 0x4c, 0xff, 0x15, 0x21, 0x45, 0xff, 0x29, 0x35, 0x5a, 0xff, 0x3e, 0x4a, 0x71, 0xff, 0x39, 0x45, 0x6b, 0xff, 0x26, 0x38, 0x5d, 0xff, 0x1c, 0x30, 0x54, 0xff, 0x1f, 0x31, 0x55, 0xff, 0x28, 0x37, 0x5a, 0xff, 0x31, 0x3e, 0x62, 0xff, 0x34, 0x46, 0x65, 0xff, 0x34, 0x48, 0x6a, 0xff, 0x37, 0x49, 0x71, 0xff, 0x33, 0x4a, 0x70, 0xff, 0x2b, 0x4a, 0x7a, 0xff, 0x28, 0x51, 0x8a, 0xff, 0x31, 0x5f, 0x96, 0xff, 0x37, 0x6a, 0x9f, 0xff, 0x31, 0x62, 0x97, 0xff, 0x31, 0x5b, 0x92, 0xff, 0x30, 0x5a, 0x95, 0xff, 0x24, 0x54, 0x8a, 0xff, 0x1b, 0x3e, 0x6e, 0xff, 0x13, 0x2f, 0x53, 0xff, 0x0f, 0x21, 0x3b, 0xff, 0x18, 0x1b, 0x31, 0xff, 0x1f, 0x23, 0x37, 0xff, 0x21, 0x26, 0x3f, 0xff, 0x21, 0x2c, 0x43, 0xff, 0x17, 0x20, 0x38, 0xff, 0x16, 0x1d, 0x35, 0xff, 0x1e, 0x26, 0x3f, 0xff, 0x21, 0x2a, 0x3f, 0xff, 0x20, 0x27, 0x38, 0xff, 0x0e, 0x14, 0x27, 0xff, 0x12, 0x19, 0x2b, 0xff, 0x16, 0x18, 0x21, 0xff, 0x17, 0x16, 0x1e, 0xff, 0x15, 0x14, 0x1e, 0xff, 0x14, 0x13, 0x1e, 0xff, 0x17, 0x14, 0x21, 0xff, 0x14, 0x11, 0x1e, 0xff, 0x17, 0x16, 0x1f, 0xff, 0x15, 0x16, 0x1e, 0xff, 0x14, 0x17, 0x24, 0xff, 0x14, 0x1c, 0x2c, 0xff, 0x11, 0x1a, 0x2c, 0xff, 0x27, 0x2f, 0x40, 0xff, 0x22, 0x26, 0x33, 0xff, 0x12, 0x0e, 0x1c, 0xff, 0x1e, 0x17, 0x2a, 0xff, 0x0c, 0x1a, 0x30, 0xff, 0x19, 0x27, 0x3d, 0xff, 0x3b, 0x4c, 0x72, 0xff, 0x43, 0x63, 0x95, 0xff, 0x32, 0x48, 0x66, 0xff, 0x58, 0x51, 0x68, 0xff, 0x7a, 0x90, 0xb6, 0xff, 0x87, 0xb7, 0xe3, 0xff, 0x8c, 0xb4, 0xe0, 0xff, 0x80, 0xa9, 0xd5, 0xff, 0x5a, 0x82, 0xb9, 0xff, 0x36, 0x5b, 0x92, 0xff, 0x7c, 0x9f, 0xca, 0xff, 0x97, 0xbb, 0xdf, 0xff, 0x8a, 0xaf, 0xd6, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb6, 0xb6, 0xb6, 0x07, 0xc8, 0xc1, 0xbd, 0xd1, 0xc1, 0xbb, 0xb5, 0xff, 0xcd, 0xc3, 0xbb, 0xff, 0x93, 0x95, 0x9e, 0xff, 0x91, 0x8e, 0x99, 0xff, 0xad, 0xad, 0xb1, 0xff, 0x94, 0xa4, 0xb7, 0xff, 0x81, 0x9e, 0xc0, 0xff, 0x73, 0x9a, 0xc4, 0xff, 0x6c, 0x97, 0xc5, 0xff, 0x67, 0x90, 0xc1, 0xff, 0x42, 0x65, 0x96, 0xff, 0x44, 0x65, 0x96, 0xff, 0x44, 0x64, 0x92, 0xff, 0x2a, 0x51, 0x87, 0xff, 0x2e, 0x55, 0x8f, 0xff, 0x23, 0x40, 0x6f, 0xff, 0x1d, 0x47, 0x79, 0xff, 0x1d, 0x4a, 0x86, 0xff, 0x13, 0x39, 0x7a, 0xff, 0x17, 0x3b, 0x7b, 0xff, 0x13, 0x37, 0x77, 0xff, 0x0d, 0x35, 0x73, 0xff, 0x1a, 0x44, 0x7e, 0xff, 0x24, 0x4e, 0x86, 0xff, 0x43, 0x6d, 0xa7, 0xff, 0x4a, 0x81, 0xca, 0xff, 0x39, 0x75, 0xc4, 0xff, 0x35, 0x6d, 0xb3, 0xff, 0x36, 0x6b, 0xab, 0xff, 0x30, 0x5f, 0x9c, 0xff, 0x29, 0x55, 0x8f, 0xff, 0x1d, 0x49, 0x7c, 0xff, 0x16, 0x45, 0x76, 0xff, 0x17, 0x3e, 0x68, 0xff, 0x0b, 0x1e, 0x40, 0xff, 0x06, 0x0f, 0x2c, 0xff, 0x14, 0x17, 0x35, 0xff, 0x17, 0x1f, 0x40, 0xff, 0x18, 0x23, 0x49, 0xff, 0x13, 0x1f, 0x43, 0xff, 0x17, 0x24, 0x48, 0xff, 0x30, 0x3b, 0x5f, 0xff, 0x2f, 0x38, 0x5a, 0xff, 0x1f, 0x2b, 0x4c, 0xff, 0x12, 0x1f, 0x41, 0xff, 0x16, 0x25, 0x49, 0xff, 0x24, 0x38, 0x5f, 0xff, 0x31, 0x49, 0x6f, 0xff, 0x39, 0x53, 0x7c, 0xff, 0x3a, 0x58, 0x8a, 0xff, 0x36, 0x5f, 0x98, 0xff, 0x32, 0x63, 0xa2, 0xff, 0x36, 0x69, 0xa9, 0xff, 0x37, 0x6c, 0xad, 0xff, 0x35, 0x6a, 0xac, 0xff, 0x33, 0x66, 0xac, 0xff, 0x31, 0x68, 0xae, 0xff, 0x2d, 0x64, 0xa3, 0xff, 0x23, 0x53, 0x8e, 0xff, 0x1d, 0x42, 0x6e, 0xff, 0x18, 0x28, 0x4b, 0xff, 0x10, 0x1e, 0x42, 0xff, 0x0a, 0x16, 0x38, 0xff, 0x0b, 0x15, 0x2e, 0xff, 0x0d, 0x17, 0x2b, 0xff, 0x15, 0x1e, 0x31, 0xff, 0x16, 0x23, 0x34, 0xff, 0x12, 0x1c, 0x30, 0xff, 0x16, 0x1f, 0x34, 0xff, 0x19, 0x21, 0x36, 0xff, 0x13, 0x17, 0x2c, 0xff, 0x1b, 0x1c, 0x30, 0xff, 0x18, 0x1e, 0x30, 0xff, 0x0a, 0x13, 0x20, 0xff, 0x11, 0x10, 0x19, 0xff, 0x1b, 0x19, 0x22, 0xff, 0x18, 0x17, 0x20, 0xff, 0x0e, 0x0d, 0x16, 0xff, 0x16, 0x15, 0x20, 0xff, 0x16, 0x14, 0x21, 0xff, 0x13, 0x13, 0x1f, 0xff, 0x19, 0x1c, 0x27, 0xff, 0x1a, 0x20, 0x2c, 0xff, 0x13, 0x1b, 0x2c, 0xff, 0x1d, 0x22, 0x33, 0xff, 0x25, 0x2b, 0x3a, 0xff, 0x1c, 0x23, 0x38, 0xff, 0x14, 0x17, 0x2f, 0xff, 0x1b, 0x1f, 0x3b, 0xff, 0x11, 0x2a, 0x4a, 0xff, 0x1b, 0x2d, 0x51, 0xff, 0x2e, 0x3e, 0x67, 0xff, 0x41, 0x5c, 0x88, 0xff, 0x2c, 0x3e, 0x57, 0xff, 0x3f, 0x4d, 0x68, 0xff, 0x5f, 0x7d, 0xaf, 0xff, 0x60, 0x8c, 0xc6, 0xff, 0x7e, 0xaf, 0xde, 0xff, 0x93, 0xc6, 0xf0, 0xff, 0x98, 0xc3, 0xe9, 0xff, 0xa4, 0xcf, 0xf0, 0xff, 0xa4, 0xd1, 0xec, 0xff, 0x8e, 0xb9, 0xd9, 0xd1, 0x7f, 0x9f, 0xdf, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc2, 0xbc, 0xb6, 0x2a, 0xc5, 0xbd, 0xb5, 0xf3, 0xcb, 0xbe, 0xb1, 0xff, 0xb5, 0xb3, 0xa8, 0xff, 0x98, 0xa8, 0xb4, 0xff, 0x89, 0xaf, 0xd8, 0xff, 0x69, 0xa4, 0xd4, 0xff, 0x5c, 0x96, 0xcc, 0xff, 0x59, 0x96, 0xcc, 0xff, 0x55, 0x89, 0xc1, 0xff, 0x48, 0x6f, 0xa5, 0xff, 0x47, 0x6b, 0xa0, 0xff, 0x46, 0x68, 0x9e, 0xff, 0x31, 0x51, 0x82, 0xff, 0x2a, 0x50, 0x85, 0xff, 0x25, 0x41, 0x70, 0xff, 0x28, 0x48, 0x7f, 0xff, 0x22, 0x51, 0x90, 0xff, 0x12, 0x3d, 0x7c, 0xff, 0x0a, 0x34, 0x76, 0xff, 0x07, 0x31, 0x71, 0xff, 0x11, 0x3b, 0x7a, 0xff, 0x15, 0x41, 0x7e, 0xff, 0x1a, 0x48, 0x84, 0xff, 0x28, 0x53, 0x95, 0xff, 0x30, 0x5d, 0xa2, 0xff, 0x3a, 0x6e, 0xb3, 0xff, 0x43, 0x7d, 0xc5, 0xff, 0x3b, 0x79, 0xc7, 0xff, 0x3a, 0x75, 0xc1, 0xff, 0x3d, 0x75, 0xbe, 0xff, 0x38, 0x6c, 0xb3, 0xff, 0x2a, 0x5f, 0x9f, 0xff, 0x22, 0x4e, 0x88, 0xff, 0x1f, 0x47, 0x7c, 0xff, 0x12, 0x45, 0x73, 0xff, 0x0b, 0x31, 0x55, 0xff, 0x0f, 0x17, 0x3b, 0xff, 0x12, 0x13, 0x34, 0xff, 0x10, 0x1b, 0x37, 0xff, 0x0e, 0x12, 0x2f, 0xff, 0x0c, 0x12, 0x2f, 0xff, 0x1e, 0x2c, 0x4e, 0xff, 0x28, 0x3a, 0x62, 0xff, 0x27, 0x3e, 0x6b, 0xff, 0x2c, 0x45, 0x75, 0xff, 0x36, 0x54, 0x88, 0xff, 0x3a, 0x62, 0x9a, 0xff, 0x3e, 0x6f, 0xaa, 0xff, 0x3f, 0x71, 0xae, 0xff, 0x40, 0x6f, 0xae, 0xff, 0x3c, 0x6e, 0xaf, 0xff, 0x38, 0x6d, 0xac, 0xff, 0x38, 0x6c, 0xac, 0xff, 0x33, 0x67, 0xac, 0xff, 0x33, 0x6b, 0xb1, 0xff, 0x34, 0x67, 0xa6, 0xff, 0x26, 0x56, 0x8c, 0xff, 0x28, 0x51, 0x7e, 0xff, 0x25, 0x3d, 0x6b, 0xff, 0x1e, 0x31, 0x59, 0xff, 0x18, 0x29, 0x4c, 0xff, 0x1b, 0x32, 0x5e, 0xff, 0x0f, 0x27, 0x53, 0xff, 0x0b, 0x1a, 0x3a, 0xff, 0x10, 0x19, 0x34, 0xff, 0x18, 0x21, 0x3b, 0xff, 0x1c, 0x28, 0x3c, 0xff, 0x18, 0x21, 0x35, 0xff, 0x21, 0x29, 0x3e, 0xff, 0x12, 0x1a, 0x2e, 0xff, 0x0d, 0x12, 0x25, 0xff, 0x13, 0x19, 0x2d, 0xff, 0x17, 0x1f, 0x31, 0xff, 0x0f, 0x14, 0x22, 0xff, 0x0e, 0x0b, 0x18, 0xff, 0x0f, 0x0c, 0x16, 0xff, 0x17, 0x16, 0x1f, 0xff, 0x14, 0x14, 0x1d, 0xff, 0x0a, 0x0c, 0x14, 0xff, 0x16, 0x16, 0x22, 0xff, 0x1d, 0x1c, 0x2d, 0xff, 0x17, 0x19, 0x2a, 0xff, 0x19, 0x1f, 0x2e, 0xff, 0x19, 0x21, 0x31, 0xff, 0x0b, 0x11, 0x22, 0xff, 0x15, 0x19, 0x2b, 0xff, 0x1d, 0x28, 0x45, 0xff, 0x17, 0x31, 0x57, 0xff, 0x21, 0x47, 0x74, 0xff, 0x24, 0x42, 0x6b, 0xff, 0x22, 0x2f, 0x50, 0xff, 0x27, 0x30, 0x50, 0xff, 0x08, 0x19, 0x3c, 0xff, 0x14, 0x2f, 0x5d, 0xff, 0x3d, 0x69, 0xa0, 0xff, 0x60, 0x9e, 0xd4, 0xff, 0x67, 0xa3, 0xdb, 0xff, 0x61, 0x9e, 0xd0, 0xff, 0x90, 0xc3, 0xe9, 0xff, 0xb9, 0xe2, 0xff, 0xff, 0xac, 0xda, 0xf8, 0xff, 0x93, 0xc4, 0xe9, 0xf4, 0x97, 0xc8, 0xf2, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc6, 0xba, 0xae, 0x55, 0xc2, 0xb6, 0xae, 0xfe, 0xb1, 0xcb, 0xd5, 0xff, 0x7c, 0xb4, 0xdc, 0xff, 0x53, 0x8e, 0xcb, 0xff, 0x4f, 0x93, 0xcc, 0xff, 0x57, 0x9c, 0xcf, 0xff, 0x58, 0x95, 0xcf, 0xff, 0x46, 0x7b, 0xb9, 0xff, 0x3d, 0x6b, 0xa7, 0xff, 0x42, 0x69, 0xa5, 0xff, 0x34, 0x5a, 0x92, 0xff, 0x2a, 0x4f, 0x83, 0xff, 0x2f, 0x51, 0x86, 0xff, 0x20, 0x3f, 0x68, 0xff, 0x26, 0x51, 0x87, 0xff, 0x1a, 0x4b, 0x8d, 0xff, 0x03, 0x36, 0x76, 0xff, 0x04, 0x34, 0x79, 0xff, 0x06, 0x38, 0x7a, 0xff, 0x0e, 0x3f, 0x81, 0xff, 0x1a, 0x4c, 0x8d, 0xff, 0x26, 0x5a, 0x9d, 0xff, 0x2c, 0x5e, 0xa6, 0xff, 0x25, 0x58, 0xa2, 0xff, 0x21, 0x55, 0x9c, 0xff, 0x31, 0x67, 0xa8, 0xff, 0x43, 0x7a, 0xbe, 0xff, 0x44, 0x7e, 0xc8, 0xff, 0x3b, 0x79, 0xc5, 0xff, 0x38, 0x77, 0xc1, 0xff, 0x38, 0x74, 0xc1, 0xff, 0x39, 0x6a, 0xb5, 0xff, 0x2d, 0x5a, 0x9b, 0xff, 0x1c, 0x4c, 0x86, 0xff, 0x1b, 0x48, 0x80, 0xff, 0x10, 0x37, 0x66, 0xff, 0x03, 0x14, 0x38, 0xff, 0x0a, 0x17, 0x38, 0xff, 0x23, 0x33, 0x5d, 0xff, 0x28, 0x47, 0x77, 0xff, 0x2c, 0x5a, 0x91, 0xff, 0x3a, 0x68, 0xa4, 0xff, 0x3e, 0x6e, 0xae, 0xff, 0x41, 0x73, 0xb8, 0xff, 0x3e, 0x72, 0xb7, 0xff, 0x40, 0x76, 0xb7, 0xff, 0x42, 0x74, 0xbb, 0xff, 0x38, 0x6f, 0xb8, 0xff, 0x35, 0x70, 0xb5, 0xff, 0x39, 0x6f, 0xae, 0xff, 0x38, 0x6e, 0xad, 0xff, 0x36, 0x6b, 0xaf, 0xff, 0x36, 0x6b, 0xb4, 0xff, 0x31, 0x64, 0xaa, 0xff, 0x23, 0x4a, 0x7f, 0xff, 0x20, 0x45, 0x79, 0xff, 0x25, 0x4d, 0x84, 0xff, 0x1f, 0x42, 0x74, 0xff, 0x16, 0x3b, 0x6e, 0xff, 0x1f, 0x3f, 0x70, 0xff, 0x24, 0x4b, 0x81, 0xff, 0x17, 0x40, 0x70, 0xff, 0x14, 0x2b, 0x51, 0xff, 0x0f, 0x17, 0x37, 0xff, 0x20, 0x26, 0x42, 0xff, 0x1f, 0x29, 0x41, 0xff, 0x19, 0x22, 0x3b, 0xff, 0x21, 0x26, 0x42, 0xff, 0x1e, 0x24, 0x3d, 0xff, 0x13, 0x19, 0x2b, 0xff, 0x12, 0x1a, 0x2b, 0xff, 0x16, 0x18, 0x2d, 0xff, 0x10, 0x0e, 0x21, 0xff, 0x0d, 0x0f, 0x1d, 0xff, 0x11, 0x0e, 0x1b, 0xff, 0x15, 0x10, 0x1f, 0xff, 0x1b, 0x18, 0x25, 0xff, 0x0b, 0x0b, 0x12, 0xff, 0x16, 0x15, 0x21, 0xff, 0x1f, 0x1d, 0x2d, 0xff, 0x16, 0x18, 0x27, 0xff, 0x16, 0x1b, 0x29, 0xff, 0x1e, 0x22, 0x2f, 0xff, 0x0f, 0x14, 0x26, 0xff, 0x11, 0x1b, 0x36, 0xff, 0x21, 0x39, 0x5f, 0xff, 0x22, 0x4b, 0x7c, 0xff, 0x1e, 0x48, 0x7d, 0xff, 0x14, 0x26, 0x42, 0xff, 0x14, 0x23, 0x38, 0xff, 0x13, 0x28, 0x4f, 0xff, 0x23, 0x3e, 0x73, 0xff, 0x40, 0x6a, 0xa9, 0xff, 0x51, 0x8e, 0xc8, 0xff, 0x5f, 0x9e, 0xd3, 0xff, 0x6f, 0xab, 0xe0, 0xff, 0x71, 0xa7, 0xd5, 0xff, 0x8f, 0xbd, 0xde, 0xff, 0x88, 0xc0, 0xe1, 0xff, 0x77, 0xb4, 0xdd, 0xff, 0x7c, 0xb7, 0xe4, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb8, 0xc8, 0xd5, 0x8d, 0x71, 0xb1, 0xe4, 0xff, 0x55, 0x97, 0xd5, 0xff, 0x48, 0x7f, 0xbd, 0xff, 0x45, 0x7d, 0xb7, 0xff, 0x5c, 0x94, 0xcd, 0xff, 0x52, 0x84, 0xbf, 0xff, 0x39, 0x6a, 0xa9, 0xff, 0x30, 0x61, 0x9f, 0xff, 0x3d, 0x6a, 0xa5, 0xff, 0x2f, 0x57, 0x8e, 0xff, 0x26, 0x4c, 0x80, 0xff, 0x2a, 0x4e, 0x82, 0xff, 0x1e, 0x45, 0x78, 0xff, 0x1e, 0x4b, 0x83, 0xff, 0x10, 0x3e, 0x7b, 0xff, 0x01, 0x36, 0x7c, 0xff, 0x00, 0x34, 0x7b, 0xff, 0x06, 0x39, 0x7e, 0xff, 0x10, 0x42, 0x86, 0xff, 0x1d, 0x50, 0x95, 0xff, 0x24, 0x59, 0xa0, 0xff, 0x2b, 0x60, 0xa7, 0xff, 0x33, 0x66, 0xb1, 0xff, 0x2f, 0x61, 0xae, 0xff, 0x26, 0x58, 0xa1, 0xff, 0x2b, 0x5e, 0xa1, 0xff, 0x3a, 0x70, 0xb5, 0xff, 0x46, 0x81, 0xc7, 0xff, 0x44, 0x7d, 0xc6, 0xff, 0x3d, 0x74, 0xbe, 0xff, 0x3a, 0x71, 0xbd, 0xff, 0x37, 0x6c, 0xb7, 0xff, 0x33, 0x60, 0xa8, 0xff, 0x21, 0x4c, 0x89, 0xff, 0x1c, 0x4a, 0x7e, 0xff, 0x2a, 0x53, 0x82, 0xff, 0x36, 0x60, 0x96, 0xff, 0x42, 0x76, 0xb5, 0xff, 0x42, 0x79, 0xbf, 0xff, 0x41, 0x75, 0xbb, 0xff, 0x3d, 0x6f, 0xb2, 0xff, 0x38, 0x6c, 0xaf, 0xff, 0x39, 0x6f, 0xb3, 0xff, 0x3a, 0x73, 0xb7, 0xff, 0x3d, 0x77, 0xbb, 0xff, 0x3d, 0x73, 0xb8, 0xff, 0x38, 0x70, 0xb7, 0xff, 0x38, 0x72, 0xb8, 0xff, 0x3a, 0x72, 0xb6, 0xff, 0x38, 0x6f, 0xb2, 0xff, 0x32, 0x6a, 0xaf, 0xff, 0x33, 0x69, 0xb0, 0xff, 0x2d, 0x5e, 0xa6, 0xff, 0x23, 0x50, 0x93, 0xff, 0x2a, 0x59, 0xa1, 0xff, 0x25, 0x58, 0xa3, 0xff, 0x19, 0x44, 0x7f, 0xff, 0x15, 0x3f, 0x74, 0xff, 0x28, 0x4c, 0x7c, 0xff, 0x25, 0x41, 0x6d, 0xff, 0x14, 0x32, 0x5f, 0xff, 0x09, 0x1f, 0x47, 0xff, 0x06, 0x12, 0x31, 0xff, 0x10, 0x1a, 0x37, 0xff, 0x26, 0x2b, 0x47, 0xff, 0x25, 0x2a, 0x44, 0xff, 0x13, 0x1c, 0x36, 0xff, 0x10, 0x1d, 0x35, 0xff, 0x21, 0x2e, 0x42, 0xff, 0x16, 0x1c, 0x2d, 0xff, 0x10, 0x0f, 0x23, 0xff, 0x11, 0x0e, 0x28, 0xff, 0x0e, 0x11, 0x2b, 0xff, 0x11, 0x14, 0x2c, 0xff, 0x0e, 0x11, 0x1b, 0xff, 0x19, 0x1a, 0x25, 0xff, 0x14, 0x16, 0x27, 0xff, 0x0e, 0x11, 0x21, 0xff, 0x12, 0x16, 0x25, 0xff, 0x1b, 0x1d, 0x2d, 0xff, 0x15, 0x1b, 0x2c, 0xff, 0x13, 0x1c, 0x32, 0xff, 0x18, 0x26, 0x44, 0xff, 0x16, 0x31, 0x55, 0xff, 0x1e, 0x3f, 0x66, 0xff, 0x27, 0x3d, 0x64, 0xff, 0x0c, 0x1b, 0x3b, 0xff, 0x05, 0x16, 0x30, 0xff, 0x18, 0x36, 0x60, 0xff, 0x28, 0x52, 0x8a, 0xff, 0x41, 0x73, 0xaf, 0xff, 0x53, 0x89, 0xc8, 0xff, 0x64, 0xa2, 0xd8, 0xff, 0x6f, 0xaa, 0xdc, 0xff, 0x77, 0xae, 0xd8, 0xff, 0x80, 0xb6, 0xda, 0xff, 0x81, 0xbb, 0xe5, 0xff, 0x74, 0xb5, 0xe1, 0xff, 0x73, 0xb4, 0xe3, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0xaa, 0xff, 0x03, 0x3f, 0x88, 0xd2, 0xb1, 0x42, 0x7f, 0xc2, 0xff, 0x44, 0x88, 0xc3, 0xff, 0x48, 0x88, 0xc5, 0xff, 0x4f, 0x86, 0xc6, 0xff, 0x38, 0x6d, 0xac, 0xff, 0x35, 0x66, 0xa5, 0xff, 0x32, 0x62, 0x9e, 0xff, 0x2b, 0x59, 0x91, 0xff, 0x1a, 0x40, 0x71, 0xff, 0x1e, 0x47, 0x7d, 0xff, 0x1a, 0x48, 0x85, 0xff, 0x18, 0x41, 0x79, 0xff, 0x19, 0x47, 0x87, 0xff, 0x03, 0x35, 0x75, 0xff, 0x03, 0x36, 0x7a, 0xff, 0x00, 0x34, 0x7b, 0xff, 0x06, 0x37, 0x7c, 0xff, 0x13, 0x42, 0x86, 0xff, 0x1f, 0x50, 0x95, 0xff, 0x28, 0x5c, 0xa2, 0xff, 0x2f, 0x64, 0xa7, 0xff, 0x34, 0x67, 0xb0, 0xff, 0x33, 0x63, 0xb1, 0xff, 0x31, 0x62, 0xaf, 0xff, 0x2e, 0x60, 0xac, 0xff, 0x29, 0x5f, 0xa9, 0xff, 0x33, 0x6c, 0xb5, 0xff, 0x47, 0x7d, 0xc6, 0xff, 0x47, 0x7d, 0xc6, 0xff, 0x3a, 0x72, 0xbc, 0xff, 0x2c, 0x66, 0xae, 0xff, 0x28, 0x60, 0xa4, 0xff, 0x2e, 0x64, 0xa6, 0xff, 0x3c, 0x6d, 0xae, 0xff, 0x42, 0x77, 0xb5, 0xff, 0x3a, 0x78, 0xb6, 0xff, 0x37, 0x72, 0xb6, 0xff, 0x36, 0x6c, 0xb1, 0xff, 0x37, 0x6a, 0xac, 0xff, 0x34, 0x67, 0xab, 0xff, 0x36, 0x6a, 0xb0, 0xff, 0x3d, 0x75, 0xb9, 0xff, 0x3e, 0x79, 0xbe, 0xff, 0x3e, 0x77, 0xbf, 0xff, 0x3a, 0x74, 0xbb, 0xff, 0x37, 0x6f, 0xb5, 0xff, 0x37, 0x6b, 0xb2, 0xff, 0x32, 0x67, 0xb0, 0xff, 0x36, 0x6a, 0xb2, 0xff, 0x38, 0x6a, 0xb2, 0xff, 0x33, 0x67, 0xb0, 0xff, 0x32, 0x67, 0xb2, 0xff, 0x32, 0x66, 0xae, 0xff, 0x2b, 0x62, 0xa6, 0xff, 0x25, 0x5e, 0xa1, 0xff, 0x1e, 0x47, 0x80, 0xff, 0x16, 0x30, 0x5e, 0xff, 0x19, 0x31, 0x58, 0xff, 0x0d, 0x22, 0x45, 0xff, 0x04, 0x19, 0x49, 0xff, 0x0b, 0x20, 0x4b, 0xff, 0x10, 0x20, 0x41, 0xff, 0x10, 0x1f, 0x3f, 0xff, 0x23, 0x29, 0x46, 0xff, 0x27, 0x2a, 0x46, 0xff, 0x1b, 0x24, 0x3f, 0xff, 0x0e, 0x1b, 0x36, 0xff, 0x1b, 0x22, 0x3e, 0xff, 0x1a, 0x1b, 0x33, 0xff, 0x0b, 0x0b, 0x22, 0xff, 0x0d, 0x10, 0x2c, 0xff, 0x0a, 0x11, 0x32, 0xff, 0x09, 0x14, 0x31, 0xff, 0x06, 0x0f, 0x23, 0xff, 0x0b, 0x11, 0x23, 0xff, 0x18, 0x1f, 0x32, 0xff, 0x0d, 0x14, 0x27, 0xff, 0x09, 0x13, 0x25, 0xff, 0x21, 0x28, 0x3b, 0xff, 0x22, 0x2c, 0x42, 0xff, 0x16, 0x27, 0x47, 0xff, 0x13, 0x2a, 0x54, 0xff, 0x0c, 0x2e, 0x5a, 0xff, 0x0c, 0x26, 0x45, 0xff, 0x19, 0x1e, 0x33, 0xff, 0x1c, 0x24, 0x3e, 0xff, 0x1b, 0x41, 0x70, 0xff, 0x26, 0x5f, 0x96, 0xff, 0x39, 0x6e, 0xaa, 0xff, 0x4c, 0x84, 0xc1, 0xff, 0x5a, 0x9b, 0xd5, 0xff, 0x6c, 0xae, 0xde, 0xff, 0x79, 0xb9, 0xe2, 0xff, 0x7e, 0xbb, 0xe1, 0xff, 0x77, 0xb5, 0xdf, 0xff, 0x70, 0xb0, 0xe2, 0xff, 0x71, 0xb5, 0xe6, 0xb1, 0x55, 0xaa, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x7f, 0xcc, 0x0a, 0x38, 0x72, 0xbb, 0xc8, 0x3f, 0x80, 0xbe, 0xff, 0x52, 0x95, 0xcf, 0xff, 0x4d, 0x8b, 0xcc, 0xff, 0x3a, 0x72, 0xb5, 0xff, 0x39, 0x6b, 0xab, 0xff, 0x2f, 0x5e, 0x9a, 0xff, 0x23, 0x50, 0x8a, 0xff, 0x1b, 0x46, 0x7f, 0xff, 0x22, 0x4c, 0x85, 0xff, 0x1a, 0x47, 0x84, 0xff, 0x13, 0x36, 0x67, 0xff, 0x13, 0x3a, 0x6f, 0xff, 0x02, 0x33, 0x77, 0xff, 0x05, 0x38, 0x7c, 0xff, 0x02, 0x36, 0x7d, 0xff, 0x06, 0x39, 0x7d, 0xff, 0x13, 0x44, 0x87, 0xff, 0x1f, 0x51, 0x95, 0xff, 0x27, 0x5b, 0xa1, 0xff, 0x2e, 0x62, 0xa7, 0xff, 0x32, 0x65, 0xae, 0xff, 0x33, 0x64, 0xb1, 0xff, 0x34, 0x66, 0xb2, 0xff, 0x36, 0x68, 0xb5, 0xff, 0x32, 0x67, 0xb5, 0xff, 0x2a, 0x62, 0xaf, 0xff, 0x3b, 0x72, 0xbb, 0xff, 0x44, 0x7c, 0xc4, 0xff, 0x3a, 0x72, 0xb9, 0xff, 0x2f, 0x66, 0xa9, 0xff, 0x2c, 0x60, 0xa1, 0xff, 0x2d, 0x63, 0xa6, 0xff, 0x2d, 0x62, 0xa4, 0xff, 0x28, 0x5c, 0x9d, 0xff, 0x26, 0x5b, 0xa3, 0xff, 0x2c, 0x5b, 0xa3, 0xff, 0x2c, 0x5e, 0xa5, 0xff, 0x30, 0x65, 0xad, 0xff, 0x34, 0x68, 0xb0, 0xff, 0x38, 0x6d, 0xb7, 0xff, 0x3b, 0x75, 0xbd, 0xff, 0x35, 0x70, 0xb8, 0xff, 0x34, 0x69, 0xb5, 0xff, 0x30, 0x65, 0xb1, 0xff, 0x2c, 0x62, 0xad, 0xff, 0x34, 0x69, 0xb4, 0xff, 0x3b, 0x70, 0xbb, 0xff, 0x38, 0x70, 0xbc, 0xff, 0x35, 0x71, 0xbc, 0xff, 0x34, 0x70, 0xba, 0xff, 0x34, 0x69, 0xb0, 0xff, 0x2a, 0x59, 0x9f, 0xff, 0x1d, 0x4a, 0x89, 0xff, 0x0d, 0x38, 0x72, 0xff, 0x0b, 0x29, 0x59, 0xff, 0x09, 0x1d, 0x46, 0xff, 0x09, 0x1f, 0x4d, 0xff, 0x13, 0x2f, 0x60, 0xff, 0x10, 0x39, 0x6c, 0xff, 0x1c, 0x40, 0x71, 0xff, 0x1e, 0x35, 0x5c, 0xff, 0x1a, 0x29, 0x4b, 0xff, 0x1c, 0x23, 0x44, 0xff, 0x1d, 0x21, 0x41, 0xff, 0x19, 0x20, 0x3e, 0xff, 0x25, 0x2e, 0x4d, 0xff, 0x1c, 0x22, 0x45, 0xff, 0x0c, 0x17, 0x36, 0xff, 0x08, 0x14, 0x2f, 0xff, 0x08, 0x14, 0x2e, 0xff, 0x09, 0x15, 0x32, 0xff, 0x0a, 0x18, 0x34, 0xff, 0x0b, 0x14, 0x39, 0xff, 0x10, 0x19, 0x37, 0xff, 0x16, 0x1f, 0x33, 0xff, 0x10, 0x1a, 0x2f, 0xff, 0x1b, 0x23, 0x38, 0xff, 0x34, 0x33, 0x4a, 0xff, 0x28, 0x2b, 0x45, 0xff, 0x1b, 0x25, 0x41, 0xff, 0x11, 0x21, 0x43, 0xff, 0x02, 0x1a, 0x40, 0xff, 0x09, 0x1d, 0x3f, 0xff, 0x1b, 0x32, 0x58, 0xff, 0x27, 0x4d, 0x7d, 0xff, 0x26, 0x59, 0x95, 0xff, 0x30, 0x66, 0xa5, 0xff, 0x3b, 0x70, 0xb1, 0xff, 0x45, 0x84, 0xbe, 0xff, 0x57, 0x9e, 0xd0, 0xff, 0x68, 0xad, 0xdb, 0xff, 0x6c, 0xab, 0xdc, 0xff, 0x69, 0xa6, 0xdb, 0xff, 0x69, 0xaa, 0xde, 0xff, 0x66, 0xac, 0xdc, 0xc8, 0x66, 0xb2, 0xe5, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0x6d, 0xb6, 0x15, 0x3d, 0x77, 0xba, 0xd7, 0x48, 0x84, 0xc0, 0xff, 0x43, 0x7d, 0xbc, 0xff, 0x35, 0x6e, 0xb2, 0xff, 0x33, 0x68, 0xaa, 0xff, 0x2f, 0x5f, 0x9c, 0xff, 0x2c, 0x5a, 0x96, 0xff, 0x26, 0x55, 0x92, 0xff, 0x1e, 0x4d, 0x89, 0xff, 0x1a, 0x48, 0x85, 0xff, 0x14, 0x40, 0x72, 0xff, 0x0c, 0x38, 0x6f, 0xff, 0x04, 0x35, 0x7f, 0xff, 0x06, 0x3a, 0x80, 0xff, 0x04, 0x39, 0x7e, 0xff, 0x03, 0x3c, 0x7f, 0xff, 0x10, 0x46, 0x89, 0xff, 0x1f, 0x52, 0x96, 0xff, 0x28, 0x5c, 0xa3, 0xff, 0x33, 0x67, 0xac, 0xff, 0x32, 0x66, 0xaf, 0xff, 0x31, 0x65, 0xb2, 0xff, 0x34, 0x68, 0xb4, 0xff, 0x36, 0x6c, 0xb8, 0xff, 0x35, 0x6a, 0xb7, 0xff, 0x32, 0x69, 0xb7, 0xff, 0x34, 0x6f, 0xba, 0xff, 0x3a, 0x75, 0xbd, 0xff, 0x3d, 0x78, 0xbd, 0xff, 0x3c, 0x73, 0xb8, 0xff, 0x34, 0x67, 0xad, 0xff, 0x28, 0x5c, 0x9f, 0xff, 0x22, 0x56, 0x98, 0xff, 0x20, 0x55, 0x98, 0xff, 0x1a, 0x4f, 0x96, 0xff, 0x14, 0x49, 0x8f, 0xff, 0x13, 0x47, 0x8f, 0xff, 0x14, 0x47, 0x91, 0xff, 0x1c, 0x4f, 0x98, 0xff, 0x2d, 0x62, 0xad, 0xff, 0x38, 0x6c, 0xb8, 0xff, 0x33, 0x68, 0xb4, 0xff, 0x2d, 0x64, 0xb2, 0xff, 0x33, 0x6a, 0xb8, 0xff, 0x3c, 0x73, 0xc0, 0xff, 0x3d, 0x75, 0xc0, 0xff, 0x3e, 0x77, 0xc2, 0xff, 0x36, 0x71, 0xbd, 0xff, 0x37, 0x6f, 0xb7, 0xff, 0x2a, 0x5d, 0x9e, 0xff, 0x11, 0x3c, 0x79, 0xff, 0x0b, 0x2d, 0x69, 0xff, 0x07, 0x27, 0x60, 0xff, 0x00, 0x1d, 0x51, 0xff, 0x03, 0x1d, 0x4c, 0xff, 0x12, 0x2d, 0x5a, 0xff, 0x1e, 0x3e, 0x73, 0xff, 0x26, 0x4d, 0x89, 0xff, 0x29, 0x5a, 0x95, 0xff, 0x2a, 0x53, 0x8a, 0xff, 0x16, 0x34, 0x5d, 0xff, 0x17, 0x2e, 0x53, 0xff, 0x1d, 0x29, 0x50, 0xff, 0x18, 0x22, 0x45, 0xff, 0x0c, 0x15, 0x35, 0xff, 0x18, 0x1f, 0x3d, 0xff, 0x2a, 0x36, 0x55, 0xff, 0x13, 0x22, 0x46, 0xff, 0x03, 0x17, 0x3b, 0xff, 0x05, 0x19, 0x3a, 0xff, 0x09, 0x17, 0x3a, 0xff, 0x14, 0x1e, 0x43, 0xff, 0x15, 0x21, 0x44, 0xff, 0x0f, 0x1f, 0x3f, 0xff, 0x15, 0x1e, 0x3c, 0xff, 0x19, 0x23, 0x3b, 0xff, 0x17, 0x22, 0x3d, 0xff, 0x1d, 0x1d, 0x3a, 0xff, 0x1a, 0x21, 0x3c, 0xff, 0x0b, 0x1a, 0x37, 0xff, 0x0e, 0x22, 0x42, 0xff, 0x17, 0x2e, 0x51, 0xff, 0x22, 0x38, 0x62, 0xff, 0x23, 0x4a, 0x7b, 0xff, 0x22, 0x55, 0x8b, 0xff, 0x2d, 0x5e, 0x9a, 0xff, 0x37, 0x66, 0xa9, 0xff, 0x3d, 0x73, 0xb4, 0xff, 0x42, 0x82, 0xbc, 0xff, 0x52, 0x93, 0xca, 0xff, 0x60, 0x9d, 0xd3, 0xff, 0x63, 0xa1, 0xd9, 0xff, 0x64, 0xa7, 0xdd, 0xff, 0x61, 0xa8, 0xdb, 0xd8, 0x61, 0xaa, 0xda, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x76, 0xbf, 0x1c, 0x3d, 0x76, 0xb2, 0xdc, 0x4b, 0x7f, 0xbe, 0xff, 0x42, 0x7e, 0xc0, 0xff, 0x35, 0x6e, 0xb3, 0xff, 0x30, 0x63, 0xa5, 0xff, 0x27, 0x58, 0x97, 0xff, 0x1e, 0x4f, 0x8e, 0xff, 0x1a, 0x49, 0x8a, 0xff, 0x12, 0x48, 0x86, 0xff, 0x0d, 0x48, 0x83, 0xff, 0x06, 0x41, 0x84, 0xff, 0x01, 0x3d, 0x84, 0xff, 0x02, 0x3a, 0x7f, 0xff, 0x01, 0x3a, 0x7f, 0xff, 0x06, 0x41, 0x83, 0xff, 0x13, 0x49, 0x8b, 0xff, 0x1f, 0x52, 0x96, 0xff, 0x28, 0x5c, 0xa1, 0xff, 0x32, 0x66, 0xab, 0xff, 0x33, 0x67, 0xb0, 0xff, 0x33, 0x66, 0xb4, 0xff, 0x35, 0x69, 0xb5, 0xff, 0x36, 0x6c, 0xb9, 0xff, 0x37, 0x6d, 0xb9, 0xff, 0x36, 0x6c, 0xba, 0xff, 0x32, 0x6b, 0xbb, 0xff, 0x3c, 0x76, 0xc2, 0xff, 0x46, 0x82, 0xca, 0xff, 0x3f, 0x7b, 0xc3, 0xff, 0x36, 0x6c, 0xb5, 0xff, 0x2b, 0x5e, 0xa4, 0xff, 0x23, 0x56, 0x9d, 0xff, 0x23, 0x57, 0x9e, 0xff, 0x22, 0x55, 0x9b, 0xff, 0x20, 0x53, 0x99, 0xff, 0x1b, 0x4f, 0x97, 0xff, 0x18, 0x4a, 0x94, 0xff, 0x1b, 0x4e, 0x97, 0xff, 0x20, 0x53, 0x9f, 0xff, 0x2d, 0x60, 0xae, 0xff, 0x39, 0x6e, 0xba, 0xff, 0x40, 0x79, 0xc2, 0xff, 0x3e, 0x76, 0xc1, 0xff, 0x3c, 0x73, 0xbc, 0xff, 0x3b, 0x71, 0xb9, 0xff, 0x36, 0x6d, 0xb6, 0xff, 0x35, 0x6c, 0xb3, 0xff, 0x26, 0x53, 0x94, 0xff, 0x04, 0x25, 0x61, 0xff, 0x00, 0x20, 0x59, 0xff, 0x01, 0x29, 0x5c, 0xff, 0x00, 0x25, 0x58, 0xff, 0x09, 0x2d, 0x64, 0xff, 0x1d, 0x45, 0x7e, 0xff, 0x20, 0x4c, 0x85, 0xff, 0x23, 0x51, 0x89, 0xff, 0x2a, 0x57, 0x91, 0xff, 0x30, 0x5f, 0xa0, 0xff, 0x33, 0x60, 0x9d, 0xff, 0x18, 0x3f, 0x71, 0xff, 0x11, 0x36, 0x64, 0xff, 0x19, 0x34, 0x5c, 0xff, 0x13, 0x23, 0x44, 0xff, 0x15, 0x20, 0x3f, 0xff, 0x10, 0x18, 0x35, 0xff, 0x1e, 0x27, 0x46, 0xff, 0x1f, 0x2d, 0x4f, 0xff, 0x08, 0x1c, 0x46, 0xff, 0x05, 0x1b, 0x4a, 0xff, 0x06, 0x1c, 0x48, 0xff, 0x0a, 0x21, 0x47, 0xff, 0x14, 0x28, 0x4a, 0xff, 0x19, 0x28, 0x4c, 0xff, 0x1d, 0x28, 0x4a, 0xff, 0x15, 0x1f, 0x39, 0xff, 0x11, 0x21, 0x3f, 0xff, 0x18, 0x25, 0x47, 0xff, 0x1c, 0x2c, 0x4b, 0xff, 0x11, 0x2f, 0x54, 0xff, 0x10, 0x35, 0x60, 0xff, 0x19, 0x40, 0x6d, 0xff, 0x20, 0x45, 0x75, 0xff, 0x22, 0x4e, 0x81, 0xff, 0x26, 0x58, 0x90, 0xff, 0x2c, 0x5f, 0x9d, 0xff, 0x36, 0x6a, 0xb0, 0xff, 0x3f, 0x75, 0xbb, 0xff, 0x46, 0x82, 0xc1, 0xff, 0x4c, 0x8b, 0xc6, 0xff, 0x53, 0x91, 0xcb, 0xff, 0x59, 0x99, 0xd3, 0xff, 0x5b, 0xa1, 0xda, 0xdd, 0x5b, 0xa3, 0xda, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x9a, 0xd0, 0x21, 0x4b, 0x94, 0xd1, 0xde, 0x42, 0x82, 0xc5, 0xff, 0x39, 0x70, 0xb6, 0xff, 0x2d, 0x61, 0xa5, 0xff, 0x22, 0x56, 0x96, 0xff, 0x1c, 0x50, 0x8d, 0xff, 0x15, 0x49, 0x87, 0xff, 0x0c, 0x44, 0x81, 0xff, 0x08, 0x41, 0x83, 0xff, 0x04, 0x3c, 0x85, 0xff, 0x01, 0x3d, 0x83, 0xff, 0x00, 0x3a, 0x7f, 0xff, 0x01, 0x3b, 0x80, 0xff, 0x0a, 0x42, 0x85, 0xff, 0x15, 0x4a, 0x8c, 0xff, 0x1f, 0x53, 0x97, 0xff, 0x29, 0x5c, 0xa4, 0xff, 0x32, 0x65, 0xab, 0xff, 0x32, 0x66, 0xb0, 0xff, 0x32, 0x66, 0xb3, 0xff, 0x36, 0x6a, 0xb6, 0xff, 0x35, 0x6a, 0xb7, 0xff, 0x36, 0x6c, 0xb9, 0xff, 0x36, 0x6c, 0xbb, 0xff, 0x35, 0x6e, 0xbf, 0xff, 0x3c, 0x75, 0xc4, 0xff, 0x42, 0x7e, 0xc9, 0xff, 0x44, 0x80, 0xca, 0xff, 0x3c, 0x75, 0xc1, 0xff, 0x34, 0x69, 0xb3, 0xff, 0x30, 0x62, 0xac, 0xff, 0x28, 0x5b, 0xa3, 0xff, 0x1f, 0x53, 0x99, 0xff, 0x1d, 0x51, 0x97, 0xff, 0x1d, 0x51, 0x98, 0xff, 0x1c, 0x4f, 0x97, 0xff, 0x21, 0x55, 0x9c, 0xff, 0x25, 0x59, 0xa4, 0xff, 0x24, 0x57, 0xa3, 0xff, 0x23, 0x56, 0xa0, 0xff, 0x2c, 0x5c, 0xa3, 0xff, 0x2a, 0x5b, 0xa0, 0xff, 0x25, 0x56, 0x99, 0xff, 0x27, 0x59, 0x9b, 0xff, 0x26, 0x58, 0x9b, 0xff, 0x1a, 0x4e, 0x8e, 0xff, 0x04, 0x34, 0x6f, 0xff, 0x00, 0x2c, 0x63, 0xff, 0x03, 0x2d, 0x66, 0xff, 0x07, 0x2c, 0x60, 0xff, 0x15, 0x3e, 0x75, 0xff, 0x29, 0x59, 0x9a, 0xff, 0x30, 0x62, 0xa5, 0xff, 0x2b, 0x5a, 0x9b, 0xff, 0x27, 0x53, 0x92, 0xff, 0x27, 0x54, 0x90, 0xff, 0x29, 0x5d, 0x9b, 0xff, 0x3a, 0x6e, 0xb5, 0xff, 0x31, 0x5e, 0xa0, 0xff, 0x0f, 0x39, 0x6d, 0xff, 0x0d, 0x35, 0x5c, 0xff, 0x17, 0x2d, 0x4e, 0xff, 0x17, 0x23, 0x43, 0xff, 0x10, 0x20, 0x3c, 0xff, 0x0f, 0x1d, 0x3b, 0xff, 0x19, 0x24, 0x44, 0xff, 0x13, 0x1f, 0x45, 0xff, 0x0c, 0x21, 0x4d, 0xff, 0x07, 0x28, 0x56, 0xff, 0x07, 0x29, 0x57, 0xff, 0x0d, 0x28, 0x50, 0xff, 0x1a, 0x2e, 0x53, 0xff, 0x1c, 0x28, 0x4a, 0xff, 0x13, 0x20, 0x39, 0xff, 0x16, 0x2f, 0x51, 0xff, 0x16, 0x28, 0x4f, 0xff, 0x11, 0x29, 0x4d, 0xff, 0x1a, 0x3b, 0x67, 0xff, 0x14, 0x39, 0x68, 0xff, 0x15, 0x41, 0x70, 0xff, 0x1c, 0x4b, 0x7d, 0xff, 0x22, 0x51, 0x8a, 0xff, 0x2a, 0x5c, 0x9a, 0xff, 0x31, 0x65, 0xa6, 0xff, 0x36, 0x6b, 0xb1, 0xff, 0x3b, 0x72, 0xb8, 0xff, 0x42, 0x7d, 0xbf, 0xff, 0x44, 0x81, 0xc0, 0xff, 0x46, 0x86, 0xc3, 0xff, 0x50, 0x93, 0xcf, 0xde, 0x55, 0x9a, 0xd8, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4e, 0x93, 0xd7, 0x1a, 0x3e, 0x82, 0xc7, 0xd7, 0x3b, 0x70, 0xb6, 0xff, 0x2f, 0x63, 0xa4, 0xff, 0x23, 0x56, 0x95, 0xff, 0x1b, 0x4f, 0x8c, 0xff, 0x13, 0x4a, 0x86, 0xff, 0x0b, 0x44, 0x81, 0xff, 0x09, 0x41, 0x83, 0xff, 0x08, 0x3e, 0x84, 0xff, 0x04, 0x3b, 0x82, 0xff, 0x02, 0x3b, 0x80, 0xff, 0x04, 0x3c, 0x81, 0xff, 0x0c, 0x41, 0x84, 0xff, 0x14, 0x49, 0x8b, 0xff, 0x1f, 0x53, 0x98, 0xff, 0x2b, 0x5d, 0xa7, 0xff, 0x32, 0x64, 0xad, 0xff, 0x34, 0x67, 0xb2, 0xff, 0x33, 0x67, 0xb3, 0xff, 0x36, 0x6a, 0xb6, 0xff, 0x35, 0x6b, 0xb8, 0xff, 0x36, 0x6c, 0xb9, 0xff, 0x35, 0x6c, 0xb9, 0xff, 0x35, 0x6e, 0xbd, 0xff, 0x3a, 0x73, 0xc3, 0xff, 0x40, 0x79, 0xc7, 0xff, 0x46, 0x7f, 0xca, 0xff, 0x41, 0x7b, 0xc7, 0xff, 0x38, 0x72, 0xbd, 0xff, 0x34, 0x6b, 0xb3, 0xff, 0x2e, 0x62, 0xa8, 0xff, 0x26, 0x5a, 0xa0, 0xff, 0x22, 0x56, 0x9c, 0xff, 0x1f, 0x53, 0x99, 0xff, 0x19, 0x4d, 0x92, 0xff, 0x15, 0x49, 0x8e, 0xff, 0x1a, 0x4e, 0x95, 0xff, 0x20, 0x53, 0x9b, 0xff, 0x1f, 0x52, 0x99, 0xff, 0x1d, 0x4e, 0x91, 0xff, 0x17, 0x4a, 0x89, 0xff, 0x10, 0x41, 0x7d, 0xff, 0x0c, 0x38, 0x75, 0xff, 0x0c, 0x3a, 0x77, 0xff, 0x01, 0x33, 0x70, 0xff, 0x01, 0x2d, 0x6a, 0xff, 0x09, 0x34, 0x6d, 0xff, 0x0f, 0x3e, 0x75, 0xff, 0x21, 0x55, 0x95, 0xff, 0x2d, 0x67, 0xa9, 0xff, 0x31, 0x6a, 0xa9, 0xff, 0x2a, 0x5d, 0x9d, 0xff, 0x27, 0x58, 0x99, 0xff, 0x2d, 0x5d, 0x9e, 0xff, 0x32, 0x63, 0xa5, 0xff, 0x38, 0x6e, 0xb2, 0xff, 0x3e, 0x75, 0xba, 0xff, 0x34, 0x6b, 0xad, 0xff, 0x1f, 0x4d, 0x83, 0xff, 0x10, 0x31, 0x5b, 0xff, 0x1a, 0x2c, 0x53, 0xff, 0x1c, 0x23, 0x49, 0xff, 0x13, 0x22, 0x44, 0xff, 0x12, 0x26, 0x45, 0xff, 0x15, 0x21, 0x40, 0xff, 0x0e, 0x1a, 0x37, 0xff, 0x0e, 0x1d, 0x3f, 0xff, 0x12, 0x23, 0x4d, 0xff, 0x16, 0x29, 0x5a, 0xff, 0x14, 0x28, 0x53, 0xff, 0x12, 0x27, 0x49, 0xff, 0x16, 0x22, 0x3e, 0xff, 0x1a, 0x27, 0x49, 0xff, 0x16, 0x32, 0x5f, 0xff, 0x13, 0x2a, 0x4f, 0xff, 0x11, 0x2d, 0x51, 0xff, 0x1a, 0x38, 0x68, 0xff, 0x1d, 0x3f, 0x6e, 0xff, 0x1e, 0x49, 0x75, 0xff, 0x1f, 0x4d, 0x7f, 0xff, 0x24, 0x53, 0x8c, 0xff, 0x2c, 0x5c, 0x9b, 0xff, 0x30, 0x64, 0xa5, 0xff, 0x34, 0x69, 0xac, 0xff, 0x38, 0x72, 0xb4, 0xff, 0x3f, 0x78, 0xbb, 0xff, 0x44, 0x7d, 0xbf, 0xff, 0x42, 0x82, 0xc0, 0xd7, 0x4b, 0x8d, 0xcf, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x8b, 0xd0, 0x16, 0x3b, 0x71, 0xb7, 0xca, 0x2c, 0x5f, 0xa1, 0xff, 0x23, 0x56, 0x94, 0xff, 0x1f, 0x51, 0x92, 0xff, 0x17, 0x4b, 0x8c, 0xff, 0x0e, 0x45, 0x85, 0xff, 0x0a, 0x42, 0x83, 0xff, 0x09, 0x3f, 0x85, 0xff, 0x06, 0x3d, 0x84, 0xff, 0x03, 0x3d, 0x81, 0xff, 0x05, 0x3e, 0x83, 0xff, 0x0b, 0x41, 0x84, 0xff, 0x14, 0x49, 0x8b, 0xff, 0x1e, 0x53, 0x97, 0xff, 0x2a, 0x5d, 0xa6, 0xff, 0x31, 0x64, 0xad, 0xff, 0x34, 0x68, 0xb3, 0xff, 0x33, 0x67, 0xb4, 0xff, 0x32, 0x66, 0xb2, 0xff, 0x32, 0x67, 0xb4, 0xff, 0x32, 0x68, 0xb5, 0xff, 0x32, 0x69, 0xb6, 0xff, 0x33, 0x6c, 0xbb, 0xff, 0x39, 0x72, 0xc2, 0xff, 0x40, 0x79, 0xc6, 0xff, 0x47, 0x81, 0xcb, 0xff, 0x46, 0x80, 0xcc, 0xff, 0x3a, 0x76, 0xc0, 0xff, 0x32, 0x6e, 0xb5, 0xff, 0x2f, 0x67, 0xac, 0xff, 0x2c, 0x60, 0xa5, 0xff, 0x24, 0x58, 0x9e, 0xff, 0x23, 0x57, 0x9c, 0xff, 0x22, 0x56, 0x9c, 0xff, 0x1d, 0x51, 0x97, 0xff, 0x18, 0x4c, 0x91, 0xff, 0x14, 0x49, 0x8c, 0xff, 0x13, 0x47, 0x8b, 0xff, 0x14, 0x47, 0x86, 0xff, 0x0e, 0x43, 0x7d, 0xff, 0x0d, 0x3d, 0x7b, 0xff, 0x0f, 0x3a, 0x7f, 0xff, 0x0b, 0x38, 0x7a, 0xff, 0x0e, 0x38, 0x7c, 0xff, 0x16, 0x42, 0x82, 0xff, 0x24, 0x54, 0x90, 0xff, 0x2e, 0x62, 0xa1, 0xff, 0x31, 0x6b, 0xb1, 0xff, 0x2f, 0x68, 0xae, 0xff, 0x2b, 0x5f, 0xa1, 0xff, 0x26, 0x57, 0x9b, 0xff, 0x20, 0x53, 0x97, 0xff, 0x28, 0x5d, 0xa0, 0xff, 0x34, 0x68, 0xac, 0xff, 0x3e, 0x72, 0xb5, 0xff, 0x3b, 0x70, 0xb4, 0xff, 0x2e, 0x65, 0xa8, 0xff, 0x25, 0x55, 0x8f, 0xff, 0x1f, 0x42, 0x70, 0xff, 0x10, 0x2e, 0x59, 0xff, 0x19, 0x27, 0x54, 0xff, 0x14, 0x1d, 0x46, 0xff, 0x13, 0x21, 0x43, 0xff, 0x0e, 0x23, 0x45, 0xff, 0x12, 0x25, 0x48, 0xff, 0x13, 0x21, 0x44, 0xff, 0x14, 0x1e, 0x40, 0xff, 0x13, 0x1b, 0x3c, 0xff, 0x13, 0x1d, 0x3d, 0xff, 0x09, 0x19, 0x3b, 0xff, 0x10, 0x27, 0x4a, 0xff, 0x1d, 0x3a, 0x61, 0xff, 0x11, 0x32, 0x62, 0xff, 0x0f, 0x2b, 0x52, 0xff, 0x14, 0x2b, 0x53, 0xff, 0x1b, 0x37, 0x6a, 0xff, 0x1c, 0x3c, 0x71, 0xff, 0x1c, 0x46, 0x76, 0xff, 0x1f, 0x4d, 0x7e, 0xff, 0x27, 0x55, 0x8e, 0xff, 0x2e, 0x5f, 0x9d, 0xff, 0x2f, 0x63, 0xa3, 0xff, 0x34, 0x68, 0xac, 0xff, 0x38, 0x72, 0xb4, 0xff, 0x3d, 0x77, 0xb9, 0xff, 0x42, 0x7c, 0xbe, 0xca, 0x45, 0x7f, 0xc5, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x73, 0xb9, 0x0b, 0x2b, 0x61, 0xa4, 0xaf, 0x23, 0x56, 0x97, 0xff, 0x1e, 0x51, 0x95, 0xff, 0x17, 0x4d, 0x90, 0xff, 0x0e, 0x47, 0x8a, 0xff, 0x0c, 0x44, 0x88, 0xff, 0x09, 0x41, 0x86, 0xff, 0x05, 0x3e, 0x83, 0xff, 0x03, 0x3c, 0x81, 0xff, 0x05, 0x3e, 0x83, 0xff, 0x0b, 0x43, 0x85, 0xff, 0x15, 0x4a, 0x8c, 0xff, 0x1f, 0x54, 0x98, 0xff, 0x29, 0x5d, 0xa4, 0xff, 0x33, 0x66, 0xac, 0xff, 0x33, 0x67, 0xb0, 0xff, 0x30, 0x64, 0xb0, 0xff, 0x31, 0x65, 0xb1, 0xff, 0x31, 0x66, 0xb3, 0xff, 0x33, 0x69, 0xb6, 0xff, 0x36, 0x6c, 0xba, 0xff, 0x35, 0x6e, 0xbd, 0xff, 0x36, 0x6f, 0xbe, 0xff, 0x3d, 0x77, 0xc4, 0xff, 0x46, 0x80, 0xca, 0xff, 0x46, 0x81, 0xca, 0xff, 0x3f, 0x7c, 0xc4, 0xff, 0x37, 0x71, 0xb9, 0xff, 0x33, 0x69, 0xb1, 0xff, 0x31, 0x64, 0xac, 0xff, 0x29, 0x5c, 0xa4, 0xff, 0x27, 0x5b, 0xa2, 0xff, 0x26, 0x5a, 0xa0, 0xff, 0x22, 0x56, 0x9c, 0xff, 0x21, 0x57, 0x9b, 0xff, 0x1b, 0x52, 0x97, 0xff, 0x16, 0x4c, 0x90, 0xff, 0x16, 0x49, 0x89, 0xff, 0x11, 0x46, 0x84, 0xff, 0x11, 0x43, 0x83, 0xff, 0x17, 0x46, 0x89, 0xff, 0x1c, 0x4c, 0x8e, 0xff, 0x26, 0x56, 0x9c, 0xff, 0x31, 0x63, 0xa8, 0xff, 0x33, 0x68, 0xac, 0xff, 0x30, 0x65, 0xad, 0xff, 0x33, 0x66, 0xac, 0xff, 0x30, 0x63, 0xa8, 0xff, 0x23, 0x59, 0x9c, 0xff, 0x21, 0x54, 0x98, 0xff, 0x21, 0x56, 0x9a, 0xff, 0x29, 0x5e, 0xa2, 0xff, 0x33, 0x69, 0xad, 0xff, 0x3b, 0x72, 0xb8, 0xff, 0x39, 0x6f, 0xb3, 0xff, 0x2d, 0x5e, 0xa0, 0xff, 0x29, 0x55, 0x90, 0xff, 0x1a, 0x47, 0x75, 0xff, 0x0f, 0x37, 0x62, 0xff, 0x12, 0x31, 0x60, 0xff, 0x10, 0x2c, 0x59, 0xff, 0x10, 0x21, 0x49, 0xff, 0x0c, 0x1c, 0x3e, 0xff, 0x17, 0x2d, 0x51, 0xff, 0x1f, 0x2f, 0x59, 0xff, 0x10, 0x2a, 0x4e, 0xff, 0x05, 0x22, 0x42, 0xff, 0x06, 0x20, 0x40, 0xff, 0x11, 0x2d, 0x52, 0xff, 0x12, 0x33, 0x5f, 0xff, 0x13, 0x3c, 0x65, 0xff, 0x16, 0x3f, 0x67, 0xff, 0x0a, 0x2b, 0x55, 0xff, 0x11, 0x2a, 0x57, 0xff, 0x18, 0x36, 0x69, 0xff, 0x18, 0x3e, 0x70, 0xff, 0x26, 0x4f, 0x80, 0xff, 0x28, 0x56, 0x8b, 0xff, 0x29, 0x59, 0x93, 0xff, 0x2a, 0x5c, 0x9a, 0xff, 0x31, 0x66, 0xa6, 0xff, 0x34, 0x6d, 0xad, 0xff, 0x36, 0x6f, 0xb1, 0xff, 0x3c, 0x76, 0xb9, 0xb0, 0x45, 0x73, 0xb9, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 0xaa, 0x03, 0x24, 0x59, 0x9b, 0x8b, 0x1e, 0x52, 0x95, 0xff, 0x17, 0x4e, 0x90, 0xff, 0x0e, 0x48, 0x8b, 0xff, 0x0c, 0x45, 0x8b, 0xff, 0x09, 0x43, 0x87, 0xff, 0x05, 0x3f, 0x83, 0xff, 0x04, 0x3d, 0x82, 0xff, 0x06, 0x3f, 0x84, 0xff, 0x09, 0x43, 0x86, 0xff, 0x14, 0x49, 0x8c, 0xff, 0x1f, 0x53, 0x96, 0xff, 0x29, 0x5d, 0xa3, 0xff, 0x32, 0x66, 0xab, 0xff, 0x34, 0x67, 0xb1, 0xff, 0x32, 0x65, 0xb3, 0xff, 0x34, 0x68, 0xb4, 0xff, 0x30, 0x65, 0xb2, 0xff, 0x30, 0x66, 0xb3, 0xff, 0x33, 0x69, 0xb7, 0xff, 0x32, 0x6c, 0xbb, 0xff, 0x36, 0x6f, 0xbe, 0xff, 0x3c, 0x75, 0xc3, 0xff, 0x40, 0x7b, 0xc5, 0xff, 0x43, 0x80, 0xc5, 0xff, 0x43, 0x7f, 0xc5, 0xff, 0x3f, 0x76, 0xc0, 0xff, 0x3b, 0x6e, 0xb8, 0xff, 0x34, 0x69, 0xb2, 0xff, 0x2c, 0x61, 0xab, 0xff, 0x29, 0x5e, 0xa5, 0xff, 0x2b, 0x5f, 0xa4, 0xff, 0x29, 0x5d, 0xa3, 0xff, 0x28, 0x5f, 0xa5, 0xff, 0x28, 0x60, 0xa5, 0xff, 0x25, 0x5c, 0xa1, 0xff, 0x24, 0x5a, 0x9e, 0xff, 0x23, 0x59, 0x9d, 0xff, 0x26, 0x5a, 0x9e, 0xff, 0x2a, 0x5d, 0xa1, 0xff, 0x2e, 0x62, 0xa5, 0xff, 0x30, 0x66, 0xab, 0xff, 0x31, 0x67, 0xae, 0xff, 0x32, 0x66, 0xad, 0xff, 0x2f, 0x62, 0xa8, 0xff, 0x2c, 0x60, 0xa7, 0xff, 0x29, 0x5e, 0xa2, 0xff, 0x22, 0x57, 0x99, 0xff, 0x1f, 0x55, 0x97, 0xff, 0x25, 0x5a, 0x9c, 0xff, 0x30, 0x64, 0xa6, 0xff, 0x36, 0x6c, 0xb0, 0xff, 0x38, 0x71, 0xb8, 0xff, 0x33, 0x69, 0xad, 0xff, 0x2b, 0x5c, 0x9d, 0xff, 0x29, 0x57, 0x91, 0xff, 0x21, 0x4c, 0x7d, 0xff, 0x16, 0x3d, 0x6b, 0xff, 0x0d, 0x32, 0x62, 0xff, 0x11, 0x32, 0x62, 0xff, 0x11, 0x2a, 0x57, 0xff, 0x0a, 0x26, 0x52, 0xff, 0x0e, 0x2b, 0x52, 0xff, 0x19, 0x2d, 0x52, 0xff, 0x0c, 0x2e, 0x55, 0xff, 0x08, 0x30, 0x5c, 0xff, 0x0f, 0x31, 0x5f, 0xff, 0x13, 0x35, 0x63, 0xff, 0x17, 0x3d, 0x6d, 0xff, 0x0f, 0x39, 0x6b, 0xff, 0x0f, 0x37, 0x63, 0xff, 0x12, 0x34, 0x5e, 0xff, 0x13, 0x32, 0x62, 0xff, 0x15, 0x39, 0x6c, 0xff, 0x16, 0x40, 0x72, 0xff, 0x22, 0x4e, 0x80, 0xff, 0x29, 0x57, 0x8e, 0xff, 0x28, 0x58, 0x93, 0xff, 0x25, 0x59, 0x97, 0xff, 0x31, 0x68, 0xa7, 0xff, 0x39, 0x75, 0xb1, 0xff, 0x37, 0x6f, 0xb0, 0x8b, 0x55, 0x55, 0xaa, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x52, 0x95, 0x57, 0x18, 0x4d, 0x90, 0xf3, 0x0f, 0x49, 0x8c, 0xff, 0x0e, 0x47, 0x8c, 0xff, 0x0b, 0x44, 0x89, 0xff, 0x07, 0x40, 0x85, 0xff, 0x05, 0x3e, 0x83, 0xff, 0x07, 0x40, 0x85, 0xff, 0x0c, 0x46, 0x88, 0xff, 0x16, 0x4c, 0x8e, 0xff, 0x20, 0x54, 0x98, 0xff, 0x29, 0x5d, 0xa4, 0xff, 0x33, 0x67, 0xac, 0xff, 0x35, 0x69, 0xb2, 0xff, 0x35, 0x69, 0xb6, 0xff, 0x36, 0x6a, 0xb6, 0xff, 0x31, 0x67, 0xb4, 0xff, 0x30, 0x66, 0xb3, 0xff, 0x31, 0x68, 0xb5, 0xff, 0x32, 0x6b, 0xba, 0xff, 0x35, 0x6e, 0xbd, 0xff, 0x39, 0x72, 0xc0, 0xff, 0x3d, 0x77, 0xc2, 0xff, 0x41, 0x7d, 0xc3, 0xff, 0x41, 0x7d, 0xc4, 0xff, 0x41, 0x79, 0xc2, 0xff, 0x3d, 0x73, 0xbc, 0xff, 0x34, 0x6e, 0xb6, 0xff, 0x2c, 0x67, 0xb0, 0xff, 0x2c, 0x63, 0xaa, 0xff, 0x31, 0x64, 0xa9, 0xff, 0x30, 0x64, 0xaa, 0xff, 0x31, 0x68, 0xad, 0xff, 0x30, 0x67, 0xac, 0xff, 0x2c, 0x63, 0xa9, 0xff, 0x2b, 0x63, 0xaa, 0xff, 0x2b, 0x64, 0xaa, 0xff, 0x2e, 0x64, 0xab, 0xff, 0x30, 0x63, 0xab, 0xff, 0x30, 0x64, 0xac, 0xff, 0x34, 0x68, 0xaf, 0xff, 0x33, 0x67, 0xad, 0xff, 0x2f, 0x63, 0xa9, 0xff, 0x2c, 0x60, 0xa6, 0xff, 0x27, 0x5b, 0xa2, 0xff, 0x23, 0x57, 0x9c, 0xff, 0x20, 0x55, 0x98, 0xff, 0x20, 0x55, 0x98, 0xff, 0x28, 0x5d, 0xa0, 0xff, 0x37, 0x6c, 0xae, 0xff, 0x39, 0x6f, 0xb3, 0xff, 0x36, 0x6e, 0xb5, 0xff, 0x2f, 0x65, 0xa8, 0xff, 0x25, 0x57, 0x9b, 0xff, 0x24, 0x50, 0x90, 0xff, 0x21, 0x49, 0x7d, 0xff, 0x14, 0x3c, 0x6d, 0xff, 0x0e, 0x34, 0x68, 0xff, 0x0f, 0x2d, 0x61, 0xff, 0x08, 0x2a, 0x5b, 0xff, 0x09, 0x38, 0x6d, 0xff, 0x15, 0x3d, 0x74, 0xff, 0x17, 0x37, 0x66, 0xff, 0x0e, 0x31, 0x56, 0xff, 0x0a, 0x2a, 0x51, 0xff, 0x12, 0x2c, 0x58, 0xff, 0x0c, 0x28, 0x53, 0xff, 0x11, 0x31, 0x5b, 0xff, 0x14, 0x39, 0x60, 0xff, 0x10, 0x32, 0x5c, 0xff, 0x0f, 0x30, 0x60, 0xff, 0x13, 0x3a, 0x6d, 0xff, 0x1d, 0x46, 0x7c, 0xff, 0x1e, 0x49, 0x80, 0xff, 0x22, 0x4f, 0x85, 0xff, 0x23, 0x51, 0x87, 0xff, 0x28, 0x58, 0x92, 0xff, 0x31, 0x64, 0xa2, 0xff, 0x30, 0x66, 0xa4, 0xf3, 0x31, 0x6c, 0xa7, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0x4d, 0x8e, 0x2b, 0x13, 0x4a, 0x8c, 0xd2, 0x0e, 0x48, 0x8d, 0xff, 0x0b, 0x44, 0x89, 0xff, 0x09, 0x42, 0x87, 0xff, 0x07, 0x40, 0x85, 0xff, 0x06, 0x40, 0x85, 0xff, 0x0c, 0x46, 0x88, 0xff, 0x16, 0x4b, 0x8e, 0xff, 0x21, 0x54, 0x98, 0xff, 0x2b, 0x5f, 0xa5, 0xff, 0x35, 0x68, 0xae, 0xff, 0x35, 0x6b, 0xb3, 0xff, 0x34, 0x6c, 0xb8, 0xff, 0x35, 0x6b, 0xb6, 0xff, 0x33, 0x69, 0xb4, 0xff, 0x32, 0x69, 0xb4, 0xff, 0x34, 0x6c, 0xb7, 0xff, 0x34, 0x6d, 0xba, 0xff, 0x34, 0x6e, 0xba, 0xff, 0x35, 0x6f, 0xbc, 0xff, 0x3a, 0x74, 0xbf, 0xff, 0x3f, 0x7a, 0xc1, 0xff, 0x41, 0x7d, 0xc4, 0xff, 0x3d, 0x78, 0xc0, 0xff, 0x3a, 0x74, 0xbc, 0xff, 0x39, 0x70, 0xb9, 0xff, 0x37, 0x6e, 0xb7, 0xff, 0x32, 0x68, 0xb0, 0xff, 0x32, 0x66, 0xaf, 0xff, 0x32, 0x67, 0xb0, 0xff, 0x33, 0x6a, 0xb0, 0xff, 0x31, 0x69, 0xad, 0xff, 0x30, 0x67, 0xac, 0xff, 0x31, 0x68, 0xae, 0xff, 0x32, 0x69, 0xaf, 0xff, 0x32, 0x68, 0xad, 0xff, 0x33, 0x67, 0xad, 0xff, 0x33, 0x67, 0xae, 0xff, 0x34, 0x68, 0xae, 0xff, 0x2e, 0x62, 0xa8, 0xff, 0x2b, 0x5f, 0xa5, 0xff, 0x29, 0x5d, 0xa3, 0xff, 0x26, 0x5a, 0xa0, 0xff, 0x22, 0x56, 0x9c, 0xff, 0x21, 0x54, 0x9b, 0xff, 0x23, 0x57, 0x9d, 0xff, 0x2c, 0x63, 0xa8, 0xff, 0x37, 0x6f, 0xb4, 0xff, 0x35, 0x6c, 0xb2, 0xff, 0x34, 0x6a, 0xb1, 0xff, 0x2f, 0x63, 0xa5, 0xff, 0x25, 0x55, 0x9a, 0xff, 0x27, 0x53, 0x96, 0xff, 0x1f, 0x46, 0x7c, 0xff, 0x0e, 0x35, 0x68, 0xff, 0x09, 0x32, 0x65, 0xff, 0x06, 0x2f, 0x63, 0xff, 0x09, 0x36, 0x6b, 0xff, 0x0f, 0x3d, 0x73, 0xff, 0x1c, 0x47, 0x7d, 0xff, 0x24, 0x52, 0x8e, 0xff, 0x1d, 0x4d, 0x88, 0xff, 0x1e, 0x42, 0x72, 0xff, 0x1a, 0x3a, 0x6b, 0xff, 0x13, 0x35, 0x65, 0xff, 0x0e, 0x2e, 0x59, 0xff, 0x12, 0x30, 0x5a, 0xff, 0x17, 0x37, 0x65, 0xff, 0x16, 0x3d, 0x72, 0xff, 0x1f, 0x4e, 0x84, 0xff, 0x20, 0x51, 0x89, 0xff, 0x26, 0x56, 0x8e, 0xff, 0x30, 0x60, 0x99, 0xff, 0x2c, 0x5d, 0x98, 0xff, 0x30, 0x60, 0x9c, 0xff, 0x30, 0x63, 0xa0, 0xd2, 0x28, 0x62, 0x9c, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1f, 0x3f, 0x7f, 0x08, 0x0e, 0x49, 0x8e, 0x8f, 0x0d, 0x45, 0x89, 0xfe, 0x0b, 0x44, 0x89, 0xff, 0x09, 0x42, 0x87, 0xff, 0x07, 0x41, 0x86, 0xff, 0x0c, 0x45, 0x88, 0xff, 0x15, 0x4b, 0x8d, 0xff, 0x22, 0x55, 0x99, 0xff, 0x2a, 0x5e, 0xa4, 0xff, 0x33, 0x66, 0xab, 0xff, 0x36, 0x6c, 0xb5, 0xff, 0x36, 0x6e, 0xbb, 0xff, 0x35, 0x6d, 0xb8, 0xff, 0x33, 0x6b, 0xb5, 0xff, 0x34, 0x6a, 0xb5, 0xff, 0x34, 0x6c, 0xb8, 0xff, 0x32, 0x6d, 0xb9, 0xff, 0x33, 0x6e, 0xba, 0xff, 0x35, 0x6f, 0xbc, 0xff, 0x39, 0x73, 0xbe, 0xff, 0x3d, 0x79, 0xbe, 0xff, 0x40, 0x7b, 0xc2, 0xff, 0x3d, 0x78, 0xc0, 0xff, 0x3c, 0x76, 0xbf, 0xff, 0x3b, 0x72, 0xbb, 0xff, 0x39, 0x70, 0xba, 0xff, 0x36, 0x6c, 0xb5, 0xff, 0x34, 0x69, 0xb2, 0xff, 0x34, 0x69, 0xb2, 0xff, 0x35, 0x6c, 0xb1, 0xff, 0x35, 0x6d, 0xb0, 0xff, 0x34, 0x6b, 0xb0, 0xff, 0x33, 0x6a, 0xaf, 0xff, 0x33, 0x6b, 0xaf, 0xff, 0x34, 0x69, 0xae, 0xff, 0x34, 0x67, 0xae, 0xff, 0x33, 0x67, 0xad, 0xff, 0x32, 0x66, 0xac, 0xff, 0x2a, 0x5e, 0xa4, 0xff, 0x27, 0x5b, 0xa1, 0xff, 0x27, 0x5b, 0xa1, 0xff, 0x25, 0x58, 0x9e, 0xff, 0x22, 0x56, 0x9c, 0xff, 0x24, 0x59, 0x9d, 0xff, 0x2b, 0x5e, 0xa4, 0xff, 0x35, 0x6c, 0xb0, 0xff, 0x36, 0x6e, 0xb2, 0xff, 0x32, 0x69, 0xad, 0xff, 0x2d, 0x60, 0xa3, 0xff, 0x25, 0x54, 0x94, 0xff, 0x2e, 0x5a, 0x97, 0xff, 0x2c, 0x56, 0x90, 0xff, 0x12, 0x3c, 0x71, 0xff, 0x03, 0x31, 0x63, 0xff, 0x07, 0x37, 0x68, 0xff, 0x0f, 0x40, 0x73, 0xff, 0x0c, 0x3c, 0x75, 0xff, 0x0e, 0x3e, 0x77, 0xff, 0x21, 0x4d, 0x84, 0xff, 0x24, 0x55, 0x92, 0xff, 0x25, 0x59, 0x9a, 0xff, 0x26, 0x57, 0x94, 0xff, 0x1f, 0x52, 0x8f, 0xff, 0x1e, 0x50, 0x8d, 0xff, 0x1f, 0x4c, 0x89, 0xff, 0x1c, 0x47, 0x85, 0xff, 0x23, 0x50, 0x8a, 0xff, 0x25, 0x54, 0x8d, 0xff, 0x21, 0x4f, 0x8d, 0xff, 0x23, 0x55, 0x92, 0xff, 0x2a, 0x5d, 0x99, 0xff, 0x2e, 0x60, 0x9d, 0xff, 0x2d, 0x61, 0x9f, 0xfe, 0x31, 0x65, 0xa4, 0x8f, 0x3f, 0x5f, 0x9f, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0b, 0x45, 0x8b, 0x42, 0x0a, 0x44, 0x89, 0xdb, 0x09, 0x42, 0x87, 0xff, 0x08, 0x42, 0x87, 0xff, 0x0c, 0x46, 0x88, 0xff, 0x16, 0x4b, 0x8d, 0xff, 0x21, 0x55, 0x99, 0xff, 0x28, 0x5c, 0xa2, 0xff, 0x30, 0x64, 0xa9, 0xff, 0x37, 0x6c, 0xb5, 0xff, 0x37, 0x6e, 0xb9, 0xff, 0x37, 0x6d, 0xb7, 0xff, 0x35, 0x6c, 0xb6, 0xff, 0x35, 0x6c, 0xb7, 0xff, 0x37, 0x6f, 0xba, 0xff, 0x36, 0x6f, 0xbb, 0xff, 0x34, 0x6d, 0xb9, 0xff, 0x36, 0x6f, 0xbb, 0xff, 0x37, 0x72, 0xbc, 0xff, 0x38, 0x73, 0xbb, 0xff, 0x3d, 0x78, 0xc0, 0xff, 0x3c, 0x78, 0xc0, 0xff, 0x3e, 0x78, 0xc0, 0xff, 0x3d, 0x74, 0xbd, 0xff, 0x37, 0x6e, 0xb7, 0xff, 0x36, 0x6c, 0xb5, 0xff, 0x36, 0x6b, 0xb4, 0xff, 0x37, 0x6d, 0xb6, 0xff, 0x38, 0x6e, 0xb5, 0xff, 0x35, 0x6b, 0xb1, 0xff, 0x33, 0x69, 0xaf, 0xff, 0x33, 0x6a, 0xaf, 0xff, 0x34, 0x6b, 0xb0, 0xff, 0x33, 0x68, 0xae, 0xff, 0x32, 0x65, 0xab, 0xff, 0x2f, 0x63, 0xa9, 0xff, 0x2d, 0x61, 0xa7, 0xff, 0x28, 0x5c, 0xa2, 0xff, 0x26, 0x5a, 0xa0, 0xff, 0x24, 0x5a, 0x9f, 0xff, 0x1e, 0x54, 0x99, 0xff, 0x20, 0x53, 0x9a, 0xff, 0x2a, 0x5c, 0xa4, 0xff, 0x31, 0x64, 0xac, 0xff, 0x39, 0x70, 0xb6, 0xff, 0x39, 0x70, 0xb8, 0xff, 0x37, 0x6e, 0xb4, 0xff, 0x31, 0x63, 0xa5, 0xff, 0x24, 0x53, 0x93, 0xff, 0x27, 0x53, 0x8f, 0xff, 0x1e, 0x4c, 0x84, 0xff, 0x17, 0x47, 0x7e, 0xff, 0x13, 0x44, 0x7e, 0xff, 0x13, 0x44, 0x81, 0xff, 0x10, 0x40, 0x7c, 0xff, 0x0a, 0x3c, 0x73, 0xff, 0x0e, 0x40, 0x77, 0xff, 0x1f, 0x4e, 0x87, 0xff, 0x26, 0x57, 0x97, 0xff, 0x26, 0x5a, 0x9c, 0xff, 0x27, 0x5a, 0x99, 0xff, 0x24, 0x57, 0x96, 0xff, 0x22, 0x55, 0x93, 0xff, 0x27, 0x57, 0x96, 0xff, 0x26, 0x55, 0x94, 0xff, 0x21, 0x54, 0x92, 0xff, 0x21, 0x56, 0x94, 0xff, 0x28, 0x58, 0x99, 0xff, 0x28, 0x5a, 0x9a, 0xff, 0x2a, 0x5e, 0x9d, 0xff, 0x2b, 0x5f, 0x9f, 0xdb, 0x2a, 0x60, 0x9e, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x8d, 0x09, 0x09, 0x42, 0x86, 0x87, 0x09, 0x41, 0x87, 0xf9, 0x0c, 0x46, 0x88, 0xff, 0x17, 0x4c, 0x8e, 0xff, 0x20, 0x54, 0x98, 0xff, 0x27, 0x5b, 0xa1, 0xff, 0x30, 0x64, 0xa9, 0xff, 0x34, 0x68, 0xb0, 0xff, 0x36, 0x6b, 0xb4, 0xff, 0x39, 0x6e, 0xb7, 0xff, 0x36, 0x6c, 0xb7, 0xff, 0x37, 0x6e, 0xb9, 0xff, 0x3a, 0x71, 0xbc, 0xff, 0x39, 0x70, 0xbb, 0xff, 0x37, 0x6d, 0xb8, 0xff, 0x37, 0x6f, 0xba, 0xff, 0x36, 0x70, 0xbc, 0xff, 0x36, 0x70, 0xbc, 0xff, 0x3a, 0x75, 0xbd, 0xff, 0x3e, 0x79, 0xc1, 0xff, 0x3c, 0x76, 0xbe, 0xff, 0x3f, 0x75, 0xbe, 0xff, 0x37, 0x6e, 0xb7, 0xff, 0x34, 0x6b, 0xb4, 0xff, 0x33, 0x6b, 0xb4, 0xff, 0x36, 0x6d, 0xb6, 0xff, 0x37, 0x6c, 0xb5, 0xff, 0x34, 0x68, 0xb2, 0xff, 0x33, 0x68, 0xb1, 0xff, 0x33, 0x6a, 0xaf, 0xff, 0x30, 0x68, 0xac, 0xff, 0x2e, 0x63, 0xa9, 0xff, 0x2e, 0x62, 0xa8, 0xff, 0x2c, 0x60, 0xa6, 0xff, 0x2a, 0x5e, 0xa4, 0xff, 0x27, 0x5a, 0xa1, 0xff, 0x24, 0x59, 0x9e, 0xff, 0x21, 0x59, 0x9e, 0xff, 0x1f, 0x57, 0x9b, 0xff, 0x25, 0x58, 0xa0, 0xff, 0x2f, 0x5f, 0xaa, 0xff, 0x32, 0x69, 0xb2, 0xff, 0x3a, 0x71, 0xbb, 0xff, 0x36, 0x6d, 0xb7, 0xff, 0x37, 0x6e, 0xb8, 0xff, 0x34, 0x69, 0xb0, 0xff, 0x33, 0x66, 0xaa, 0xff, 0x2f, 0x62, 0xa4, 0xff, 0x27, 0x5c, 0x9b, 0xff, 0x20, 0x56, 0x92, 0xff, 0x16, 0x49, 0x86, 0xff, 0x14, 0x46, 0x84, 0xff, 0x10, 0x42, 0x7e, 0xff, 0x0d, 0x3f, 0x75, 0xff, 0x14, 0x46, 0x7b, 0xff, 0x21, 0x52, 0x8c, 0xff, 0x27, 0x58, 0x97, 0xff, 0x27, 0x5c, 0x9e, 0xff, 0x2c, 0x5d, 0x9f, 0xff, 0x2c, 0x5b, 0x9c, 0xff, 0x29, 0x59, 0x99, 0xff, 0x25, 0x56, 0x94, 0xff, 0x27, 0x59, 0x96, 0xff, 0x28, 0x5a, 0x99, 0xff, 0x26, 0x59, 0x9a, 0xff, 0x2b, 0x5f, 0x9e, 0xff, 0x28, 0x5f, 0x9e, 0xf9, 0x2b, 0x63, 0xa1, 0x88, 0x38, 0x55, 0xaa, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x44, 0x89, 0x25, 0x0c, 0x45, 0x88, 0xb7, 0x17, 0x4c, 0x8f, 0xff, 0x21, 0x54, 0x97, 0xff, 0x25, 0x59, 0xa0, 0xff, 0x2f, 0x63, 0xa8, 0xff, 0x34, 0x68, 0xb0, 0xff, 0x35, 0x6a, 0xb4, 0xff, 0x37, 0x6c, 0xb5, 0xff, 0x35, 0x6c, 0xb7, 0xff, 0x37, 0x6e, 0xba, 0xff, 0x39, 0x70, 0xbb, 0xff, 0x37, 0x6e, 0xb9, 0xff, 0x36, 0x6c, 0xb7, 0xff, 0x34, 0x6d, 0xb8, 0xff, 0x33, 0x6e, 0xb9, 0xff, 0x34, 0x6e, 0xb9, 0xff, 0x37, 0x72, 0xba, 0xff, 0x3f, 0x7b, 0xc2, 0xff, 0x3a, 0x74, 0xbd, 0xff, 0x3d, 0x74, 0xbd, 0xff, 0x39, 0x70, 0xb9, 0xff, 0x35, 0x6d, 0xb6, 0xff, 0x35, 0x6c, 0xb5, 0xff, 0x34, 0x6c, 0xb5, 0xff, 0x34, 0x69, 0xb2, 0xff, 0x31, 0x66, 0xb0, 0xff, 0x30, 0x65, 0xae, 0xff, 0x2f, 0x66, 0xab, 0xff, 0x2c, 0x64, 0xa8, 0xff, 0x2b, 0x60, 0xa6, 0xff, 0x2b, 0x5e, 0xa5, 0xff, 0x29, 0x5d, 0xa3, 0xff, 0x28, 0x5c, 0xa2, 0xff, 0x24, 0x58, 0x9e, 0xff, 0x20, 0x55, 0x9b, 0xff, 0x1f, 0x57, 0x9c, 0xff, 0x22, 0x59, 0x9d, 0xff, 0x27, 0x5c, 0xa3, 0xff, 0x30, 0x67, 0xb1, 0xff, 0x35, 0x71, 0xb9, 0xff, 0x3b, 0x73, 0xbc, 0xff, 0x37, 0x6c, 0xb6, 0xff, 0x33, 0x6a, 0xb5, 0xff, 0x30, 0x67, 0xb2, 0xff, 0x34, 0x69, 0xb0, 0xff, 0x32, 0x66, 0xab, 0xff, 0x27, 0x5d, 0xa0, 0xff, 0x1d, 0x54, 0x92, 0xff, 0x14, 0x4a, 0x86, 0xff, 0x0f, 0x46, 0x81, 0xff, 0x0e, 0x43, 0x7e, 0xff, 0x12, 0x41, 0x7d, 0xff, 0x1a, 0x49, 0x85, 0xff, 0x23, 0x52, 0x8e, 0xff, 0x27, 0x59, 0x97, 0xff, 0x28, 0x5e, 0x9f, 0xff, 0x28, 0x5c, 0xa0, 0xff, 0x25, 0x58, 0x9c, 0xff, 0x27, 0x5b, 0x9e, 0xff, 0x26, 0x5a, 0x9b, 0xff, 0x27, 0x5b, 0x9b, 0xff, 0x29, 0x5e, 0xa0, 0xff, 0x2a, 0x5f, 0xa3, 0xff, 0x25, 0x5a, 0x9e, 0xb7, 0x28, 0x5d, 0xa1, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0x4c, 0x8e, 0x46, 0x1e, 0x52, 0x97, 0xca, 0x24, 0x58, 0x9e, 0xff, 0x2c, 0x60, 0xa5, 0xff, 0x32, 0x66, 0xae, 0xff, 0x36, 0x6b, 0xb4, 0xff, 0x36, 0x6b, 0xb3, 0xff, 0x37, 0x6d, 0xb7, 0xff, 0x3a, 0x71, 0xbb, 0xff, 0x3a, 0x71, 0xba, 0xff, 0x35, 0x6c, 0xb7, 0xff, 0x34, 0x6b, 0xb6, 0xff, 0x34, 0x6b, 0xb6, 0xff, 0x33, 0x6b, 0xb6, 0xff, 0x33, 0x6b, 0xb7, 0xff, 0x37, 0x6f, 0xb9, 0xff, 0x3e, 0x77, 0xbf, 0xff, 0x3b, 0x75, 0xbb, 0xff, 0x3c, 0x74, 0xbb, 0xff, 0x3b, 0x72, 0xbc, 0xff, 0x36, 0x6d, 0xb6, 0xff, 0x36, 0x6c, 0xb4, 0xff, 0x35, 0x6a, 0xb3, 0xff, 0x33, 0x69, 0xb0, 0xff, 0x30, 0x67, 0xac, 0xff, 0x2d, 0x63, 0xa9, 0xff, 0x2c, 0x63, 0xa8, 0xff, 0x2b, 0x63, 0xa8, 0xff, 0x2c, 0x61, 0xa7, 0xff, 0x2b, 0x5e, 0xa5, 0xff, 0x28, 0x5c, 0xa2, 0xff, 0x25, 0x59, 0x9f, 0xff, 0x21, 0x55, 0x9b, 0xff, 0x21, 0x55, 0x9b, 0xff, 0x23, 0x58, 0x9d, 0xff, 0x24, 0x5d, 0xa1, 0xff, 0x29, 0x63, 0xa8, 0xff, 0x37, 0x70, 0xb7, 0xff, 0x3b, 0x75, 0xbb, 0xff, 0x39, 0x72, 0xb8, 0xff, 0x38, 0x70, 0xb6, 0xff, 0x33, 0x6c, 0xb3, 0xff, 0x30, 0x6b, 0xb2, 0xff, 0x35, 0x6b, 0xb2, 0xff, 0x30, 0x64, 0xaa, 0xff, 0x27, 0x5d, 0xa0, 0xff, 0x21, 0x57, 0x99, 0xff, 0x19, 0x51, 0x8e, 0xff, 0x11, 0x4a, 0x84, 0xff, 0x0c, 0x43, 0x7f, 0xff, 0x10, 0x41, 0x7f, 0xff, 0x18, 0x48, 0x86, 0xff, 0x20, 0x51, 0x8f, 0xff, 0x27, 0x58, 0x98, 0xff, 0x2b, 0x5c, 0xa0, 0xff, 0x28, 0x5c, 0xa1, 0xff, 0x22, 0x58, 0x9c, 0xff, 0x28, 0x5d, 0xa1, 0xff, 0x2b, 0x60, 0xa4, 0xff, 0x2b, 0x60, 0xa3, 0xff, 0x2c, 0x62, 0xa5, 0xca, 0x2b, 0x62, 0xa3, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x56, 0x9b, 0x4d, 0x29, 0x5c, 0xa2, 0xcc, 0x2e, 0x62, 0xaa, 0xff, 0x35, 0x69, 0xb3, 0xff, 0x37, 0x6c, 0xb6, 0xff, 0x36, 0x6d, 0xb5, 0xff, 0x38, 0x6f, 0xb7, 0xff, 0x37, 0x6e, 0xb8, 0xff, 0x34, 0x6c, 0xb7, 0xff, 0x33, 0x6a, 0xb5, 0xff, 0x35, 0x6b, 0xb7, 0xff, 0x35, 0x6b, 0xb7, 0xff, 0x34, 0x6a, 0xb6, 0xff, 0x37, 0x6d, 0xb9, 0xff, 0x3e, 0x74, 0xbc, 0xff, 0x3e, 0x77, 0xbb, 0xff, 0x3a, 0x74, 0xb8, 0xff, 0x39, 0x71, 0xba, 0xff, 0x36, 0x6b, 0xb6, 0xff, 0x36, 0x6a, 0xb3, 0xff, 0x35, 0x6a, 0xb3, 0xff, 0x32, 0x69, 0xaf, 0xff, 0x2f, 0x66, 0xaa, 0xff, 0x2c, 0x63, 0xa7, 0xff, 0x2c, 0x63, 0xa8, 0xff, 0x2b, 0x63, 0xa7, 0xff, 0x2b, 0x61, 0xa6, 0xff, 0x29, 0x5c, 0xa2, 0xff, 0x25, 0x59, 0x9f, 0xff, 0x22, 0x56, 0x9c, 0xff, 0x1f, 0x53, 0x99, 0xff, 0x22, 0x56, 0x9c, 0xff, 0x26, 0x5a, 0x9f, 0xff, 0x29, 0x62, 0xa7, 0xff, 0x31, 0x6c, 0xb1, 0xff, 0x3b, 0x74, 0xb8, 0xff, 0x3c, 0x75, 0xb9, 0xff, 0x3a, 0x72, 0xb7, 0xff, 0x38, 0x71, 0xb6, 0xff, 0x35, 0x6f, 0xb4, 0xff, 0x34, 0x70, 0xb3, 0xff, 0x37, 0x6d, 0xb2, 0xff, 0x31, 0x65, 0xa9, 0xff, 0x29, 0x5e, 0xa0, 0xff, 0x22, 0x57, 0x9b, 0xff, 0x1d, 0x53, 0x93, 0xff, 0x17, 0x4e, 0x8c, 0xff, 0x11, 0x48, 0x86, 0xff, 0x10, 0x46, 0x83, 0xff, 0x15, 0x4b, 0x87, 0xff, 0x1d, 0x52, 0x90, 0xff, 0x24, 0x58, 0x98, 0xff, 0x29, 0x5b, 0xa0, 0xff, 0x2b, 0x5f, 0xa3, 0xff, 0x29, 0x5e, 0xa1, 0xff, 0x2b, 0x60, 0xa2, 0xff, 0x2a, 0x60, 0xa2, 0xcc, 0x2e, 0x63, 0xa5, 0x4d, 0x00, 0x00, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x27, 0x5e, 0xa4, 0x41, 0x2e, 0x64, 0xac, 0xb4, 0x34, 0x69, 0xb1, 0xfe, 0x33, 0x6a, 0xb2, 0xff, 0x34, 0x6b, 0xb4, 0xff, 0x35, 0x6c, 0xb5, 0xff, 0x33, 0x6a, 0xb5, 0xff, 0x32, 0x69, 0xb3, 0xff, 0x33, 0x6a, 0xb5, 0xff, 0x33, 0x6a, 0xb5, 0xff, 0x33, 0x6a, 0xb5, 0xff, 0x33, 0x69, 0xb4, 0xff, 0x38, 0x6f, 0xb7, 0xff, 0x3e, 0x77, 0xbc, 0xff, 0x3a, 0x72, 0xb8, 0xff, 0x37, 0x6e, 0xb8, 0xff, 0x36, 0x6c, 0xb6, 0xff, 0x34, 0x69, 0xb1, 0xff, 0x33, 0x69, 0xb1, 0xff, 0x32, 0x68, 0xae, 0xff, 0x2e, 0x65, 0xaa, 0xff, 0x2a, 0x61, 0xa6, 0xff, 0x2a, 0x61, 0xa6, 0xff, 0x2a, 0x61, 0xa6, 0xff, 0x29, 0x5e, 0xa4, 0xff, 0x27, 0x5a, 0xa0, 0xff, 0x23, 0x57, 0x9d, 0xff, 0x20, 0x54, 0x9a, 0xff, 0x20, 0x54, 0x9a, 0xff, 0x24, 0x57, 0x9d, 0xff, 0x27, 0x5c, 0xa2, 0xff, 0x2e, 0x67, 0xac, 0xff, 0x3a, 0x74, 0xb9, 0xff, 0x3d, 0x76, 0xbb, 0xff, 0x3a, 0x73, 0xb8, 0xff, 0x3b, 0x74, 0xb9, 0xff, 0x39, 0x72, 0xb7, 0xff, 0x36, 0x70, 0xb5, 0xff, 0x36, 0x71, 0xb5, 0xff, 0x37, 0x6c, 0xb2, 0xff, 0x33, 0x66, 0xab, 0xff, 0x2c, 0x61, 0xa3, 0xff, 0x24, 0x59, 0x9c, 0xff, 0x1f, 0x55, 0x97, 0xff, 0x19, 0x4f, 0x91, 0xff, 0x14, 0x49, 0x8a, 0xff, 0x11, 0x49, 0x85, 0xff, 0x14, 0x4c, 0x88, 0xff, 0x1c, 0x53, 0x91, 0xff, 0x23, 0x5a, 0x9a, 0xff, 0x28, 0x5e, 0xa1, 0xff, 0x29, 0x5e, 0xa1, 0xfe, 0x2a, 0x5e, 0xa1, 0xb4, 0x2b, 0x5e, 0xa0, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x66, 0xa7, 0x23, 0x32, 0x67, 0xb0, 0x88, 0x36, 0x6b, 0xb3, 0xe6, 0x36, 0x6b, 0xb4, 0xff, 0x33, 0x68, 0xb1, 0xff, 0x32, 0x67, 0xb0, 0xff, 0x33, 0x68, 0xb1, 0xff, 0x34, 0x68, 0xb1, 0xff, 0x35, 0x6a, 0xb3, 0xff, 0x33, 0x6a, 0xb3, 0xff, 0x36, 0x6d, 0xb6, 0xff, 0x3c, 0x73, 0xbb, 0xff, 0x3a, 0x71, 0xba, 0xff, 0x37, 0x6e, 0xb8, 0xff, 0x37, 0x6e, 0xb5, 0xff, 0x33, 0x6a, 0xae, 0xff, 0x33, 0x6a, 0xae, 0xff, 0x34, 0x6b, 0xb0, 0xff, 0x2c, 0x63, 0xa8, 0xff, 0x2b, 0x62, 0xa7, 0xff, 0x2d, 0x61, 0xa7, 0xff, 0x29, 0x5d, 0xa3, 0xff, 0x28, 0x5c, 0xa2, 0xff, 0x25, 0x59, 0x9f, 0xff, 0x23, 0x57, 0x9d, 0xff, 0x21, 0x55, 0x9b, 0xff, 0x21, 0x55, 0x9b, 0xff, 0x27, 0x5b, 0xa1, 0xff, 0x2b, 0x62, 0xa7, 0xff, 0x31, 0x6b, 0xb4, 0xff, 0x3b, 0x75, 0xbd, 0xff, 0x3d, 0x75, 0xb9, 0xff, 0x3a, 0x73, 0xb8, 0xff, 0x38, 0x75, 0xb9, 0xff, 0x36, 0x73, 0xb7, 0xff, 0x34, 0x71, 0xb5, 0xff, 0x36, 0x71, 0xb6, 0xff, 0x37, 0x6d, 0xb3, 0xff, 0x32, 0x66, 0xab, 0xff, 0x2d, 0x62, 0xa5, 0xff, 0x28, 0x5d, 0xa0, 0xff, 0x23, 0x58, 0x9b, 0xff, 0x1d, 0x52, 0x96, 0xff, 0x17, 0x4c, 0x8f, 0xff, 0x12, 0x4a, 0x85, 0xff, 0x16, 0x4f, 0x89, 0xff, 0x1f, 0x56, 0x95, 0xff, 0x25, 0x5a, 0x9d, 0xe7, 0x29, 0x5d, 0xa1, 0x88, 0x2b, 0x5e, 0xa0, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x66, 0x99, 0x05, 0x33, 0x67, 0xb3, 0x4a, 0x34, 0x67, 0xb0, 0xa0, 0x32, 0x66, 0xae, 0xef, 0x32, 0x67, 0xb0, 0xff, 0x33, 0x67, 0xb0, 0xff, 0x34, 0x69, 0xb1, 0xff, 0x32, 0x69, 0xb2, 0xff, 0x35, 0x6c, 0xb5, 0xff, 0x3b, 0x72, 0xbb, 0xff, 0x3a, 0x71, 0xba, 0xff, 0x37, 0x6e, 0xb8, 0xff, 0x36, 0x6d, 0xb4, 0xff, 0x35, 0x6c, 0xb0, 0xff, 0x33, 0x6a, 0xaf, 0xff, 0x30, 0x68, 0xad, 0xff, 0x2a, 0x62, 0xa6, 0xff, 0x2c, 0x63, 0xa8, 0xff, 0x2f, 0x62, 0xa9, 0xff, 0x29, 0x5c, 0xa2, 0xff, 0x26, 0x5a, 0xa0, 0xff, 0x24, 0x58, 0x9e, 0xff, 0x22, 0x56, 0x9c, 0xff, 0x21, 0x54, 0x9a, 0xff, 0x24, 0x57, 0x9d, 0xff, 0x2b, 0x5f, 0xa4, 0xff, 0x2e, 0x65, 0xab, 0xff, 0x34, 0x6e, 0xb9, 0xff, 0x3c, 0x76, 0xbf, 0xff, 0x3e, 0x77, 0xbb, 0xff, 0x3c, 0x75, 0xba, 0xff, 0x38, 0x75, 0xb9, 0xff, 0x34, 0x73, 0xb7, 0xff, 0x33, 0x71, 0xb5, 0xff, 0x36, 0x70, 0xb5, 0xff, 0x38, 0x6d, 0xb3, 0xff, 0x32, 0x65, 0xa9, 0xff, 0x2e, 0x63, 0xa5, 0xff, 0x2a, 0x5f, 0xa2, 0xff, 0x26, 0x5b, 0x9e, 0xff, 0x22, 0x56, 0x9a, 0xff, 0x1c, 0x50, 0x93, 0xff, 0x17, 0x4d, 0x8c, 0xef, 0x1b, 0x51, 0x8f, 0xa0, 0x22, 0x56, 0x97, 0x4a, 0x33, 0x66, 0x99, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x6d, 0xb6, 0x07, 0x2f, 0x66, 0xae, 0x46, 0x32, 0x66, 0xaf, 0x8e, 0x33, 0x68, 0xb0, 0xd7, 0x33, 0x68, 0xb1, 0xff, 0x35, 0x6c, 0xb5, 0xff, 0x39, 0x70, 0xb9, 0xff, 0x39, 0x70, 0xb9, 0xff, 0x36, 0x6d, 0xb7, 0xff, 0x34, 0x6c, 0xb3, 0xff, 0x34, 0x6d, 0xb1, 0xff, 0x33, 0x69, 0xae, 0xff, 0x2f, 0x65, 0xaa, 0xff, 0x2b, 0x62, 0xa7, 0xff, 0x2c, 0x61, 0xa6, 0xff, 0x2a, 0x5d, 0xa4, 0xff, 0x28, 0x5c, 0xa2, 0xff, 0x25, 0x59, 0x9f, 0xff, 0x22, 0x56, 0x9c, 0xff, 0x22, 0x56, 0x9c, 0xff, 0x21, 0x56, 0x9b, 0xff, 0x25, 0x5a, 0x9f, 0xff, 0x2e, 0x63, 0xa9, 0xff, 0x36, 0x6f, 0xb4, 0xff, 0x38, 0x74, 0xbc, 0xff, 0x3b, 0x76, 0xbd, 0xff, 0x3f, 0x77, 0xbb, 0xff, 0x3c, 0x75, 0xba, 0xff, 0x3a, 0x74, 0xb9, 0xff, 0x37, 0x72, 0xb7, 0xff, 0x34, 0x6f, 0xb3, 0xff, 0x34, 0x6e, 0xb2, 0xff, 0x37, 0x6e, 0xb4, 0xff, 0x35, 0x6a, 0xaf, 0xff, 0x2f, 0x64, 0xa8, 0xff, 0x28, 0x5d, 0xa1, 0xff, 0x27, 0x5c, 0xa1, 0xd7, 0x22, 0x57, 0x9c, 0x8e, 0x1d, 0x53, 0x95, 0x46, 0x24, 0x48, 0x91, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2f, 0x67, 0xb3, 0x1b, 0x36, 0x6c, 0xb4, 0x55, 0x37, 0x6c, 0xb5, 0x8f, 0x38, 0x71, 0xba, 0xc2, 0x38, 0x70, 0xb9, 0xea, 0x35, 0x6e, 0xb5, 0xff, 0x32, 0x6c, 0xb0, 0xff, 0x32, 0x66, 0xac, 0xff, 0x30, 0x63, 0xa9, 0xff, 0x2d, 0x61, 0xa7, 0xff, 0x2c, 0x60, 0xa6, 0xff, 0x2a, 0x5d, 0xa3, 0xff, 0x28, 0x5c, 0xa2, 0xff, 0x24, 0x58, 0x9e, 0xff, 0x21, 0x55, 0x9b, 0xff, 0x24, 0x57, 0x9e, 0xff, 0x22, 0x59, 0x9e, 0xff, 0x27, 0x5e, 0xa2, 0xff, 0x30, 0x67, 0xac, 0xff, 0x38, 0x72, 0xb7, 0xff, 0x38, 0x78, 0xbb, 0xff, 0x39, 0x76, 0xba, 0xff, 0x3c, 0x74, 0xb9, 0xff, 0x3a, 0x73, 0xb8, 0xff, 0x3a, 0x73, 0xb8, 0xff, 0x38, 0x71, 0xb6, 0xff, 0x36, 0x6f, 0xb4, 0xff, 0x37, 0x70, 0xb4, 0xea, 0x34, 0x6e, 0xb2, 0xc2, 0x33, 0x6c, 0xb0, 0x8f, 0x33, 0x69, 0xae, 0x55, 0x2f, 0x5e, 0xaa, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3c, 0x69, 0xb4, 0x11, 0x33, 0x6a, 0xb0, 0x37, 0x33, 0x66, 0xab, 0x5f, 0x2f, 0x62, 0xa9, 0x86, 0x2a, 0x5f, 0xa5, 0xa8, 0x2a, 0x5f, 0xa4, 0xc0, 0x2a, 0x5e, 0xa4, 0xd3, 0x26, 0x5a, 0xa0, 0xe6, 0x25, 0x58, 0x9e, 0xee, 0x22, 0x56, 0x9c, 0xf5, 0x22, 0x55, 0x9c, 0xff, 0x23, 0x59, 0x9e, 0xff, 0x2a, 0x61, 0xa6, 0xf5, 0x34, 0x6c, 0xb0, 0xee, 0x39, 0x74, 0xb9, 0xe6, 0x37, 0x76, 0xbb, 0xd3, 0x39, 0x74, 0xb9, 0xc0, 0x3c, 0x73, 0xb9, 0xa8, 0x39, 0x72, 0xb8, 0x86, 0x38, 0x70, 0xb6, 0x5f, 0x37, 0x6f, 0xb4, 0x37, 0x3c, 0x78, 0xb4, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

const lv_image_dsc_t img_multilang_avatar_14 = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 128,
    .header.h = 128,
    .header.stride = 512,
    .data = img_multilang_avatar_14_map,
    .data_size = sizeof(img_multilang_avatar_14_map),
};

#endif
