#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_4
    #define LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_4
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_4 uint8_t
img_multilang_avatar_4_map[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2d, 0x3c, 0x4b, 0x11, 0x29, 0x37, 0x45, 0x37, 0x25, 0x30, 0x41, 0x5e, 0x1e, 0x2b, 0x39, 0x86, 0x1f, 0x2d, 0x3b, 0xa8, 0x21, 0x30, 0x3e, 0xbf, 0x1b, 0x2b, 0x3c, 0xd2, 0x1e, 0x2d, 0x3e, 0xe5, 0x1b, 0x2b, 0x3c, 0xee, 0x26, 0x35, 0x45, 0xf2, 0x32, 0x42, 0x52, 0xff, 0x40, 0x50, 0x60, 0xff, 0x4a, 0x5b, 0x6b, 0xf2, 0x4c, 0x5c, 0x6d, 0xee, 0x41, 0x51, 0x61, 0xe5, 0x42, 0x51, 0x63, 0xd2, 0x35, 0x44, 0x55, 0xbf, 0x24, 0x35, 0x45, 0xa8, 0x2d, 0x40, 0x4f, 0x86, 0x49, 0x5c, 0x6c, 0x5e, 0x5c, 0x73, 0x81, 0x37, 0x5a, 0x69, 0x78, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x1e, 0x33, 0x19, 0x12, 0x21, 0x2d, 0x55, 0x1c, 0x27, 0x35, 0x8f, 0x22, 0x2b, 0x38, 0xc1, 0x27, 0x33, 0x40, 0xe9, 0x26, 0x33, 0x41, 0xff, 0x2a, 0x37, 0x44, 0xff, 0x2c, 0x3a, 0x47, 0xff, 0x2f, 0x3c, 0x49, 0xff, 0x2f, 0x3d, 0x49, 0xff, 0x30, 0x3c, 0x4c, 0xff, 0x35, 0x42, 0x53, 0xff, 0x39, 0x47, 0x57, 0xff, 0x32, 0x41, 0x51, 0xff, 0x30, 0x42, 0x51, 0xff, 0x2a, 0x3f, 0x4e, 0xff, 0x29, 0x3d, 0x4c, 0xff, 0x2d, 0x3e, 0x4d, 0xff, 0x2d, 0x3e, 0x4d, 0xff, 0x3e, 0x4f, 0x5f, 0xff, 0x47, 0x58, 0x69, 0xff, 0x49, 0x5a, 0x6b, 0xff, 0x54, 0x66, 0x77, 0xff, 0x39, 0x4b, 0x5c, 0xff, 0x17, 0x2b, 0x3c, 0xff, 0x1f, 0x33, 0x44, 0xff, 0x46, 0x5d, 0x6d, 0xff, 0x55, 0x6d, 0x7b, 0xe9, 0x54, 0x6b, 0x7a, 0xc1, 0x4c, 0x63, 0x72, 0x8f, 0x45, 0x5d, 0x6c, 0x55, 0x33, 0x47, 0x5b, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x48, 0x6d, 0x07, 0x3d, 0x5b, 0x69, 0x46, 0x3e, 0x54, 0x62, 0x8e, 0x3c, 0x4d, 0x5a, 0xd7, 0x30, 0x43, 0x50, 0xff, 0x22, 0x34, 0x41, 0xff, 0x12, 0x23, 0x30, 0xff, 0x03, 0x14, 0x21, 0xff, 0x07, 0x16, 0x22, 0xff, 0x13, 0x21, 0x2c, 0xff, 0x1b, 0x29, 0x34, 0xff, 0x1d, 0x2b, 0x36, 0xff, 0x22, 0x30, 0x3c, 0xff, 0x24, 0x32, 0x3d, 0xff, 0x23, 0x2d, 0x3b, 0xff, 0x29, 0x31, 0x41, 0xff, 0x2e, 0x3a, 0x49, 0xff, 0x37, 0x48, 0x55, 0xff, 0x49, 0x5d, 0x6b, 0xff, 0x52, 0x69, 0x75, 0xff, 0x52, 0x66, 0x74, 0xff, 0x3b, 0x4f, 0x5d, 0xff, 0x24, 0x38, 0x46, 0xff, 0x28, 0x3d, 0x4c, 0xff, 0x3d, 0x52, 0x63, 0xff, 0x40, 0x56, 0x67, 0xff, 0x3f, 0x53, 0x63, 0xff, 0x53, 0x64, 0x75, 0xff, 0x56, 0x6a, 0x7b, 0xff, 0x36, 0x4a, 0x5b, 0xff, 0x18, 0x2e, 0x3e, 0xff, 0x1e, 0x35, 0x45, 0xff, 0x33, 0x4a, 0x5a, 0xff, 0x43, 0x5a, 0x6a, 0xff, 0x4d, 0x64, 0x74, 0xff, 0x4b, 0x63, 0x73, 0xff, 0x43, 0x5a, 0x69, 0xd7, 0x1a, 0x30, 0x40, 0x8e, 0x12, 0x20, 0x33, 0x46, 0x24, 0x48, 0x48, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x66, 0x66, 0x05, 0x43, 0x58, 0x6a, 0x48, 0x3b, 0x4e, 0x60, 0x9f, 0x33, 0x47, 0x58, 0xef, 0x28, 0x3d, 0x52, 0xff, 0x2b, 0x43, 0x53, 0xff, 0x3b, 0x52, 0x61, 0xff, 0x46, 0x5c, 0x6b, 0xff, 0x4a, 0x60, 0x6f, 0xff, 0x43, 0x58, 0x67, 0xff, 0x33, 0x47, 0x57, 0xff, 0x22, 0x33, 0x40, 0xff, 0x15, 0x23, 0x2e, 0xff, 0x08, 0x17, 0x23, 0xff, 0x02, 0x10, 0x1c, 0xff, 0x08, 0x16, 0x22, 0xff, 0x12, 0x20, 0x2c, 0xff, 0x1d, 0x28, 0x35, 0xff, 0x1e, 0x27, 0x36, 0xff, 0x1f, 0x2a, 0x38, 0xff, 0x1c, 0x2b, 0x38, 0xff, 0x14, 0x26, 0x33, 0xff, 0x1a, 0x2e, 0x3a, 0xff, 0x2d, 0x40, 0x4e, 0xff, 0x48, 0x5b, 0x6a, 0xff, 0x56, 0x6a, 0x78, 0xff, 0x4d, 0x62, 0x72, 0xff, 0x44, 0x5a, 0x6a, 0xff, 0x40, 0x5a, 0x6a, 0xff, 0x37, 0x4b, 0x5c, 0xff, 0x2d, 0x3e, 0x4f, 0xff, 0x3c, 0x50, 0x61, 0xff, 0x5e, 0x72, 0x83, 0xff, 0x5c, 0x72, 0x82, 0xff, 0x2d, 0x45, 0x54, 0xff, 0x1d, 0x34, 0x46, 0xff, 0x29, 0x41, 0x54, 0xff, 0x33, 0x4b, 0x5c, 0xff, 0x3c, 0x54, 0x66, 0xff, 0x48, 0x60, 0x72, 0xff, 0x4e, 0x66, 0x78, 0xff, 0x38, 0x4a, 0x5c, 0xff, 0x22, 0x37, 0x48, 0xef, 0x29, 0x4b, 0x5b, 0x9f, 0x35, 0x5c, 0x6a, 0x48, 0x66, 0x66, 0x66, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x57, 0x66, 0x23, 0x4a, 0x59, 0x6a, 0x86, 0x46, 0x55, 0x66, 0xe5, 0x41, 0x51, 0x63, 0xff, 0x45, 0x55, 0x68, 0xff, 0x4a, 0x5c, 0x6e, 0xff, 0x41, 0x54, 0x69, 0xff, 0x37, 0x4e, 0x60, 0xff, 0x2c, 0x44, 0x54, 0xff, 0x26, 0x3d, 0x4d, 0xff, 0x25, 0x3c, 0x4c, 0xff, 0x34, 0x48, 0x59, 0xff, 0x45, 0x58, 0x6a, 0xff, 0x4b, 0x5e, 0x6d, 0xff, 0x46, 0x58, 0x65, 0xff, 0x39, 0x4b, 0x59, 0xff, 0x24, 0x36, 0x44, 0xff, 0x15, 0x27, 0x34, 0xff, 0x0c, 0x1f, 0x2d, 0xff, 0x08, 0x18, 0x25, 0xff, 0x0c, 0x1a, 0x27, 0xff, 0x21, 0x2e, 0x3b, 0xff, 0x25, 0x33, 0x3f, 0xff, 0x22, 0x30, 0x3c, 0xff, 0x21, 0x2f, 0x3a, 0xff, 0x1a, 0x28, 0x36, 0xff, 0x17, 0x26, 0x37, 0xff, 0x25, 0x35, 0x45, 0xff, 0x39, 0x4c, 0x5c, 0xff, 0x40, 0x54, 0x65, 0xff, 0x3e, 0x54, 0x65, 0xff, 0x4f, 0x62, 0x73, 0xff, 0x4f, 0x61, 0x72, 0xff, 0x38, 0x4c, 0x5d, 0xff, 0x30, 0x44, 0x55, 0xff, 0x43, 0x59, 0x6a, 0xff, 0x69, 0x80, 0x90, 0xff, 0x51, 0x68, 0x7a, 0xff, 0x24, 0x3d, 0x50, 0xff, 0x2a, 0x42, 0x55, 0xff, 0x2b, 0x43, 0x55, 0xff, 0x3a, 0x52, 0x64, 0xff, 0x46, 0x5e, 0x70, 0xff, 0x52, 0x6a, 0x7b, 0xff, 0x45, 0x5d, 0x6f, 0xff, 0x3e, 0x59, 0x6d, 0xff, 0x35, 0x54, 0x67, 0xff, 0x36, 0x54, 0x66, 0xe5, 0x48, 0x68, 0x77, 0x86, 0x7b, 0x91, 0xa0, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x56, 0x6d, 0x81, 0x41, 0x27, 0x38, 0x4c, 0xb4, 0x2c, 0x3e, 0x54, 0xfe, 0x39, 0x4c, 0x5e, 0xff, 0x40, 0x55, 0x63, 0xff, 0x3e, 0x53, 0x64, 0xff, 0x37, 0x4f, 0x60, 0xff, 0x37, 0x50, 0x62, 0xff, 0x3d, 0x56, 0x69, 0xff, 0x48, 0x61, 0x72, 0xff, 0x49, 0x60, 0x6f, 0xff, 0x40, 0x58, 0x68, 0xff, 0x31, 0x46, 0x56, 0xff, 0x21, 0x35, 0x46, 0xff, 0x24, 0x38, 0x49, 0xff, 0x2c, 0x40, 0x50, 0xff, 0x33, 0x47, 0x57, 0xff, 0x48, 0x5c, 0x6d, 0xff, 0x53, 0x67, 0x78, 0xff, 0x50, 0x64, 0x75, 0xff, 0x36, 0x4b, 0x5d, 0xff, 0x20, 0x34, 0x41, 0xff, 0x10, 0x23, 0x2d, 0xff, 0x18, 0x28, 0x34, 0xff, 0x18, 0x26, 0x32, 0xff, 0x1c, 0x28, 0x34, 0xff, 0x2e, 0x37, 0x42, 0xff, 0x32, 0x3a, 0x4a, 0xff, 0x34, 0x40, 0x50, 0xff, 0x31, 0x3e, 0x4d, 0xff, 0x2d, 0x3d, 0x4d, 0xff, 0x3a, 0x4d, 0x5e, 0xff, 0x3d, 0x52, 0x62, 0xff, 0x2d, 0x40, 0x50, 0xff, 0x34, 0x46, 0x57, 0xff, 0x42, 0x56, 0x68, 0xff, 0x45, 0x59, 0x6a, 0xff, 0x3b, 0x51, 0x61, 0xff, 0x38, 0x4e, 0x5e, 0xff, 0x58, 0x70, 0x81, 0xff, 0x52, 0x6a, 0x7d, 0xff, 0x24, 0x3b, 0x4d, 0xff, 0x2e, 0x46, 0x58, 0xff, 0x2a, 0x41, 0x53, 0xff, 0x2f, 0x47, 0x59, 0xff, 0x4a, 0x65, 0x76, 0xff, 0x48, 0x65, 0x77, 0xff, 0x3f, 0x57, 0x6b, 0xff, 0x4b, 0x61, 0x78, 0xff, 0x3d, 0x58, 0x6c, 0xff, 0x2e, 0x58, 0x68, 0xff, 0x45, 0x6a, 0x76, 0xfe, 0x80, 0x96, 0x9b, 0xb4, 0xac, 0xb8, 0xb8, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x50, 0x68, 0x7f, 0x4c, 0x5a, 0x73, 0x87, 0xcb, 0x2a, 0x43, 0x58, 0xff, 0x44, 0x5c, 0x6f, 0xff, 0x4d, 0x62, 0x75, 0xff, 0x4d, 0x64, 0x77, 0xff, 0x5c, 0x74, 0x86, 0xff, 0x6b, 0x85, 0x98, 0xff, 0x6b, 0x86, 0x9a, 0xff, 0x59, 0x76, 0x8b, 0xff, 0x4b, 0x68, 0x7d, 0xff, 0x3d, 0x57, 0x6c, 0xff, 0x3b, 0x52, 0x67, 0xff, 0x46, 0x5d, 0x72, 0xff, 0x46, 0x5e, 0x70, 0xff, 0x3b, 0x53, 0x64, 0xff, 0x30, 0x49, 0x59, 0xff, 0x30, 0x44, 0x51, 0xff, 0x28, 0x39, 0x45, 0xff, 0x21, 0x35, 0x43, 0xff, 0x27, 0x3c, 0x4d, 0xff, 0x40, 0x56, 0x67, 0xff, 0x51, 0x69, 0x7b, 0xff, 0x51, 0x6b, 0x7b, 0xff, 0x3e, 0x58, 0x68, 0xff, 0x1f, 0x37, 0x47, 0xff, 0x15, 0x29, 0x3a, 0xff, 0x1f, 0x2f, 0x40, 0xff, 0x22, 0x30, 0x42, 0xff, 0x15, 0x25, 0x34, 0xff, 0x1f, 0x2e, 0x3c, 0xff, 0x2e, 0x3a, 0x49, 0xff, 0x2e, 0x3d, 0x4b, 0xff, 0x3f, 0x50, 0x5e, 0xff, 0x49, 0x61, 0x6d, 0xff, 0x43, 0x5d, 0x6c, 0xff, 0x3e, 0x55, 0x66, 0xff, 0x2b, 0x40, 0x51, 0xff, 0x19, 0x2d, 0x3e, 0xff, 0x2e, 0x40, 0x51, 0xff, 0x41, 0x4e, 0x60, 0xff, 0x34, 0x4a, 0x5b, 0xff, 0x49, 0x64, 0x75, 0xff, 0x50, 0x66, 0x77, 0xff, 0x22, 0x34, 0x45, 0xff, 0x30, 0x45, 0x56, 0xff, 0x2f, 0x47, 0x57, 0xff, 0x20, 0x39, 0x4a, 0xff, 0x51, 0x6b, 0x7d, 0xff, 0x52, 0x6c, 0x7d, 0xff, 0x30, 0x4b, 0x5d, 0xff, 0x42, 0x5e, 0x70, 0xff, 0x3b, 0x5a, 0x6b, 0xff, 0x30, 0x53, 0x62, 0xff, 0x5a, 0x7b, 0x87, 0xff, 0x80, 0x96, 0xa0, 0xff, 0xa9, 0xb6, 0xb9, 0xcb, 0xc3, 0xc6, 0xc6, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1d, 0x2f, 0x41, 0x46, 0x3f, 0x56, 0x6b, 0xc9, 0x70, 0x89, 0x9f, 0xff, 0x2d, 0x47, 0x5b, 0xff, 0x3c, 0x56, 0x6a, 0xff, 0x59, 0x72, 0x84, 0xff, 0x2b, 0x41, 0x53, 0xff, 0x32, 0x45, 0x56, 0xff, 0x27, 0x39, 0x4a, 0xff, 0x27, 0x3c, 0x4e, 0xff, 0x47, 0x5d, 0x6e, 0xff, 0x63, 0x7b, 0x8e, 0xff, 0x6f, 0x88, 0x9b, 0xff, 0x64, 0x7d, 0x93, 0xff, 0x51, 0x6a, 0x82, 0xff, 0x43, 0x5d, 0x72, 0xff, 0x3c, 0x57, 0x6b, 0xff, 0x3c, 0x57, 0x6a, 0xff, 0x3f, 0x5a, 0x6c, 0xff, 0x3e, 0x55, 0x66, 0xff, 0x37, 0x4d, 0x5c, 0xff, 0x35, 0x4a, 0x5b, 0xff, 0x25, 0x3b, 0x4b, 0xff, 0x1c, 0x34, 0x43, 0xff, 0x20, 0x36, 0x46, 0xff, 0x3c, 0x54, 0x64, 0xff, 0x57, 0x70, 0x80, 0xff, 0x56, 0x6e, 0x7e, 0xff, 0x45, 0x5c, 0x6c, 0xff, 0x2e, 0x43, 0x55, 0xff, 0x21, 0x35, 0x46, 0xff, 0x2b, 0x40, 0x50, 0xff, 0x2e, 0x42, 0x50, 0xff, 0x25, 0x35, 0x45, 0xff, 0x20, 0x2e, 0x3f, 0xff, 0x27, 0x36, 0x46, 0xff, 0x31, 0x47, 0x55, 0xff, 0x41, 0x58, 0x68, 0xff, 0x4e, 0x63, 0x74, 0xff, 0x51, 0x65, 0x76, 0xff, 0x47, 0x5b, 0x6c, 0xff, 0x19, 0x2c, 0x3d, 0xff, 0x0e, 0x21, 0x31, 0xff, 0x33, 0x4a, 0x58, 0xff, 0x2c, 0x43, 0x51, 0xff, 0x50, 0x64, 0x74, 0xff, 0x65, 0x78, 0x89, 0xff, 0x26, 0x37, 0x47, 0xff, 0x2a, 0x41, 0x52, 0xff, 0x37, 0x51, 0x62, 0xff, 0x1c, 0x36, 0x47, 0xff, 0x49, 0x64, 0x75, 0xff, 0x50, 0x6c, 0x7d, 0xff, 0x2f, 0x4b, 0x5c, 0xff, 0x4b, 0x67, 0x79, 0xff, 0x2e, 0x4f, 0x5f, 0xff, 0x36, 0x59, 0x68, 0xff, 0x6c, 0x88, 0x93, 0xff, 0x86, 0x97, 0x9e, 0xff, 0xaf, 0xb8, 0xb8, 0xff, 0xc5, 0xc8, 0xc5, 0xc9, 0xc4, 0xc1, 0xbd, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x23, 0x3f, 0x4d, 0x24, 0x38, 0x4c, 0x5d, 0xb4, 0x20, 0x33, 0x46, 0xff, 0x6a, 0x81, 0x97, 0xff, 0x59, 0x71, 0x87, 0xff, 0x1d, 0x37, 0x4b, 0xff, 0x4e, 0x68, 0x7c, 0xff, 0x33, 0x4b, 0x5d, 0xff, 0x20, 0x34, 0x47, 0xff, 0x13, 0x20, 0x2f, 0xff, 0x00, 0x0c, 0x1b, 0xff, 0x0c, 0x1b, 0x2b, 0xff, 0x22, 0x32, 0x42, 0xff, 0x3c, 0x4e, 0x5f, 0xff, 0x4e, 0x60, 0x70, 0xff, 0x59, 0x71, 0x88, 0xff, 0x63, 0x7f, 0x98, 0xff, 0x5e, 0x79, 0x90, 0xff, 0x53, 0x6e, 0x86, 0xff, 0x4c, 0x68, 0x7e, 0xff, 0x46, 0x64, 0x78, 0xff, 0x42, 0x5e, 0x73, 0xff, 0x35, 0x50, 0x64, 0xff, 0x3e, 0x56, 0x6a, 0xff, 0x45, 0x5d, 0x6f, 0xff, 0x47, 0x5e, 0x6d, 0xff, 0x36, 0x4b, 0x5b, 0xff, 0x25, 0x39, 0x4a, 0xff, 0x24, 0x3b, 0x4b, 0xff, 0x41, 0x57, 0x67, 0xff, 0x5b, 0x73, 0x83, 0xff, 0x5d, 0x76, 0x86, 0xff, 0x50, 0x6a, 0x7a, 0xff, 0x37, 0x50, 0x60, 0xff, 0x28, 0x40, 0x4f, 0xff, 0x34, 0x49, 0x59, 0xff, 0x43, 0x55, 0x66, 0xff, 0x38, 0x49, 0x5a, 0xff, 0x29, 0x3a, 0x4b, 0xff, 0x2a, 0x3c, 0x4d, 0xff, 0x39, 0x4b, 0x5c, 0xff, 0x42, 0x55, 0x66, 0xff, 0x55, 0x6a, 0x7b, 0xff, 0x5e, 0x77, 0x87, 0xff, 0x33, 0x4e, 0x5f, 0xff, 0x0b, 0x21, 0x2e, 0xff, 0x20, 0x31, 0x3d, 0xff, 0x2a, 0x3d, 0x4c, 0xff, 0x4a, 0x5e, 0x6f, 0xff, 0x6e, 0x82, 0x94, 0xff, 0x2e, 0x45, 0x5a, 0xff, 0x2b, 0x47, 0x5a, 0xff, 0x3e, 0x57, 0x67, 0xff, 0x18, 0x32, 0x43, 0xff, 0x44, 0x60, 0x71, 0xff, 0x4c, 0x68, 0x79, 0xff, 0x45, 0x62, 0x73, 0xff, 0x5d, 0x7e, 0x8f, 0xff, 0x29, 0x4d, 0x5c, 0xff, 0x3e, 0x5e, 0x6a, 0xff, 0x71, 0x8a, 0x91, 0xff, 0x84, 0x94, 0x96, 0xff, 0xb0, 0xbc, 0xbb, 0xff, 0xc4, 0xc9, 0xc7, 0xff, 0xc6, 0xc3, 0xbf, 0xb5, 0xc6, 0xbf, 0xbf, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x71, 0x8d, 0x09, 0x07, 0x22, 0x34, 0x87, 0x30, 0x48, 0x5a, 0xf9, 0x2e, 0x44, 0x56, 0xff, 0x2b, 0x42, 0x56, 0xff, 0x6a, 0x82, 0x97, 0xff, 0x2d, 0x46, 0x5c, 0xff, 0x28, 0x43, 0x57, 0xff, 0x51, 0x6c, 0x80, 0xff, 0x2f, 0x45, 0x58, 0xff, 0x21, 0x32, 0x45, 0xff, 0x03, 0x0f, 0x1d, 0xff, 0x0c, 0x16, 0x23, 0xff, 0x30, 0x3d, 0x4c, 0xff, 0x2c, 0x3a, 0x4a, 0xff, 0x1d, 0x2d, 0x3d, 0xff, 0x35, 0x44, 0x54, 0xff, 0x3b, 0x50, 0x65, 0xff, 0x47, 0x61, 0x78, 0xff, 0x57, 0x71, 0x89, 0xff, 0x52, 0x6d, 0x85, 0xff, 0x47, 0x64, 0x7b, 0xff, 0x50, 0x6e, 0x86, 0xff, 0x4f, 0x6c, 0x85, 0xff, 0x42, 0x5e, 0x77, 0xff, 0x37, 0x52, 0x68, 0xff, 0x37, 0x51, 0x65, 0xff, 0x3f, 0x58, 0x6a, 0xff, 0x50, 0x66, 0x78, 0xff, 0x50, 0x65, 0x77, 0xff, 0x32, 0x48, 0x5a, 0xff, 0x22, 0x3a, 0x4c, 0xff, 0x27, 0x40, 0x51, 0xff, 0x3a, 0x54, 0x65, 0xff, 0x58, 0x72, 0x83, 0xff, 0x61, 0x7c, 0x8c, 0xff, 0x4a, 0x64, 0x75, 0xff, 0x38, 0x52, 0x63, 0xff, 0x38, 0x50, 0x61, 0xff, 0x38, 0x4d, 0x5e, 0xff, 0x49, 0x5b, 0x6d, 0xff, 0x48, 0x58, 0x6a, 0xff, 0x34, 0x45, 0x58, 0xff, 0x3d, 0x52, 0x64, 0xff, 0x47, 0x5d, 0x6f, 0xff, 0x49, 0x62, 0x73, 0xff, 0x56, 0x6f, 0x82, 0xff, 0x4a, 0x62, 0x70, 0xff, 0x1a, 0x2a, 0x37, 0xff, 0x0d, 0x1b, 0x2b, 0xff, 0x16, 0x27, 0x38, 0xff, 0x3a, 0x4f, 0x61, 0xff, 0x74, 0x8d, 0xa1, 0xff, 0x41, 0x5b, 0x6d, 0xff, 0x29, 0x43, 0x54, 0xff, 0x3d, 0x57, 0x68, 0xff, 0x1b, 0x37, 0x48, 0xff, 0x3c, 0x58, 0x69, 0xff, 0x4b, 0x68, 0x79, 0xff, 0x55, 0x74, 0x87, 0xff, 0x5a, 0x7d, 0x8e, 0xff, 0x25, 0x47, 0x55, 0xff, 0x5a, 0x7a, 0x83, 0xff, 0x7f, 0x97, 0x9c, 0xff, 0x86, 0x99, 0x9c, 0xff, 0xb3, 0xbe, 0xbd, 0xff, 0xc9, 0xcb, 0xc8, 0xff, 0xc2, 0xc0, 0xbf, 0xf9, 0xc6, 0xc0, 0xbe, 0x87, 0xc6, 0xc6, 0xc6, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x7b, 0x93, 0x40, 0x56, 0x73, 0x8c, 0xda, 0x11, 0x2a, 0x3d, 0xff, 0x35, 0x4e, 0x5f, 0xff, 0x2f, 0x48, 0x5a, 0xff, 0x3c, 0x56, 0x6b, 0xff, 0x55, 0x6e, 0x84, 0xff, 0x26, 0x41, 0x56, 0xff, 0x3b, 0x57, 0x6b, 0xff, 0x41, 0x5c, 0x70, 0xff, 0x31, 0x46, 0x58, 0xff, 0x1d, 0x2c, 0x40, 0xff, 0x05, 0x12, 0x22, 0xff, 0x22, 0x30, 0x3e, 0xff, 0x3a, 0x49, 0x5a, 0xff, 0x18, 0x29, 0x39, 0xff, 0x33, 0x46, 0x57, 0xff, 0x4f, 0x63, 0x73, 0xff, 0x4d, 0x64, 0x76, 0xff, 0x3f, 0x57, 0x6b, 0xff, 0x45, 0x5d, 0x72, 0xff, 0x53, 0x6c, 0x81, 0xff, 0x49, 0x66, 0x7b, 0xff, 0x3f, 0x5c, 0x73, 0xff, 0x42, 0x5e, 0x74, 0xff, 0x45, 0x60, 0x77, 0xff, 0x48, 0x63, 0x7b, 0xff, 0x3f, 0x5b, 0x72, 0xff, 0x3b, 0x55, 0x6e, 0xff, 0x37, 0x51, 0x6a, 0xff, 0x3c, 0x58, 0x6b, 0xff, 0x4d, 0x69, 0x79, 0xff, 0x53, 0x6e, 0x7f, 0xff, 0x44, 0x5e, 0x6f, 0xff, 0x2d, 0x46, 0x57, 0xff, 0x29, 0x42, 0x53, 0xff, 0x39, 0x50, 0x62, 0xff, 0x55, 0x6d, 0x7f, 0xff, 0x61, 0x7c, 0x8d, 0xff, 0x4d, 0x6a, 0x7b, 0xff, 0x35, 0x50, 0x61, 0xff, 0x26, 0x3e, 0x4f, 0xff, 0x42, 0x57, 0x69, 0xff, 0x54, 0x6b, 0x7d, 0xff, 0x3e, 0x55, 0x67, 0xff, 0x3f, 0x55, 0x67, 0xff, 0x52, 0x69, 0x7b, 0xff, 0x4d, 0x65, 0x76, 0xff, 0x4f, 0x68, 0x7a, 0xff, 0x51, 0x68, 0x7b, 0xff, 0x27, 0x35, 0x47, 0xff, 0x0d, 0x1b, 0x2c, 0xff, 0x0a, 0x19, 0x2a, 0xff, 0x30, 0x42, 0x51, 0xff, 0x6e, 0x87, 0x97, 0xff, 0x49, 0x65, 0x76, 0xff, 0x21, 0x3c, 0x4d, 0xff, 0x3e, 0x5a, 0x6b, 0xff, 0x22, 0x3e, 0x4f, 0xff, 0x35, 0x53, 0x64, 0xff, 0x4e, 0x6b, 0x7e, 0xff, 0x65, 0x83, 0x96, 0xff, 0x4c, 0x6f, 0x7e, 0xff, 0x36, 0x5a, 0x65, 0xff, 0x81, 0x9f, 0xa5, 0xff, 0x88, 0x9d, 0xa2, 0xff, 0x91, 0x9f, 0xa1, 0xff, 0xb2, 0xbb, 0xb9, 0xff, 0xc5, 0xc8, 0xc8, 0xff, 0xc3, 0xbf, 0xbc, 0xff, 0xc2, 0xc1, 0xbc, 0xda, 0xb8, 0xbc, 0xb8, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x24, 0x48, 0x6d, 0x07, 0x40, 0x5d, 0x76, 0x8e, 0x5c, 0x7b, 0x93, 0xfe, 0x55, 0x73, 0x89, 0xff, 0x1c, 0x35, 0x4a, 0xff, 0x30, 0x49, 0x5b, 0xff, 0x33, 0x4d, 0x60, 0xff, 0x46, 0x61, 0x75, 0xff, 0x43, 0x5e, 0x74, 0xff, 0x31, 0x4d, 0x61, 0xff, 0x42, 0x5e, 0x71, 0xff, 0x3b, 0x55, 0x69, 0xff, 0x26, 0x3c, 0x4d, 0xff, 0x1f, 0x2e, 0x41, 0xff, 0x12, 0x22, 0x33, 0xff, 0x3c, 0x4e, 0x5e, 0xff, 0x2a, 0x3d, 0x4e, 0xff, 0x31, 0x45, 0x56, 0xff, 0x43, 0x58, 0x6a, 0xff, 0x2c, 0x43, 0x55, 0xff, 0x2c, 0x41, 0x52, 0xff, 0x42, 0x56, 0x66, 0xff, 0x4e, 0x65, 0x77, 0xff, 0x54, 0x6c, 0x7f, 0xff, 0x54, 0x6e, 0x83, 0xff, 0x52, 0x6f, 0x83, 0xff, 0x47, 0x61, 0x76, 0xff, 0x3c, 0x56, 0x6b, 0xff, 0x31, 0x4c, 0x63, 0xff, 0x33, 0x50, 0x69, 0xff, 0x44, 0x60, 0x7b, 0xff, 0x49, 0x66, 0x83, 0xff, 0x3c, 0x5c, 0x71, 0xff, 0x3e, 0x5f, 0x6e, 0xff, 0x54, 0x72, 0x83, 0xff, 0x62, 0x7d, 0x8e, 0xff, 0x56, 0x6e, 0x7f, 0xff, 0x4e, 0x65, 0x77, 0xff, 0x39, 0x4d, 0x60, 0xff, 0x31, 0x45, 0x59, 0xff, 0x3d, 0x58, 0x6b, 0xff, 0x50, 0x70, 0x82, 0xff, 0x6b, 0x89, 0x9c, 0xff, 0x58, 0x73, 0x86, 0xff, 0x25, 0x3f, 0x52, 0xff, 0x2c, 0x46, 0x57, 0xff, 0x49, 0x62, 0x74, 0xff, 0x4e, 0x65, 0x77, 0xff, 0x46, 0x5b, 0x6d, 0xff, 0x50, 0x63, 0x74, 0xff, 0x54, 0x6d, 0x81, 0xff, 0x50, 0x6a, 0x81, 0xff, 0x53, 0x68, 0x7b, 0xff, 0x3b, 0x4b, 0x5c, 0xff, 0x0e, 0x1b, 0x29, 0xff, 0x02, 0x0f, 0x19, 0xff, 0x22, 0x36, 0x45, 0xff, 0x69, 0x84, 0x96, 0xff, 0x4d, 0x68, 0x79, 0xff, 0x1e, 0x3a, 0x4b, 0xff, 0x3d, 0x59, 0x6a, 0xff, 0x26, 0x46, 0x56, 0xff, 0x38, 0x52, 0x66, 0xff, 0x5c, 0x76, 0x8c, 0xff, 0x6c, 0x8d, 0x9e, 0xff, 0x3f, 0x63, 0x6f, 0xff, 0x5d, 0x7b, 0x84, 0xff, 0x96, 0xad, 0xb6, 0xff, 0x95, 0xa7, 0xa9, 0xff, 0x99, 0xa6, 0xa5, 0xff, 0xb7, 0xbc, 0xbb, 0xff, 0xc8, 0xc6, 0xc5, 0xff, 0xc0, 0xc0, 0xbc, 0xff, 0xb4, 0xbc, 0xb5, 0xfe, 0xb3, 0xba, 0xb3, 0x8e, 0xb6, 0xb6, 0xb6, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x63, 0x7c, 0x29, 0x36, 0x52, 0x6b, 0xd1, 0x47, 0x64, 0x7c, 0xff, 0x59, 0x75, 0x8c, 0xff, 0x56, 0x72, 0x86, 0xff, 0x25, 0x40, 0x54, 0xff, 0x32, 0x4c, 0x5f, 0xff, 0x3e, 0x58, 0x6a, 0xff, 0x4f, 0x68, 0x7a, 0xff, 0x37, 0x51, 0x65, 0xff, 0x3a, 0x56, 0x69, 0xff, 0x3e, 0x58, 0x69, 0xff, 0x31, 0x49, 0x5b, 0xff, 0x28, 0x3f, 0x4f, 0xff, 0x25, 0x3a, 0x4b, 0xff, 0x24, 0x39, 0x4b, 0xff, 0x3c, 0x51, 0x63, 0xff, 0x3c, 0x4f, 0x62, 0xff, 0x47, 0x57, 0x69, 0xff, 0x30, 0x3f, 0x52, 0xff, 0x26, 0x33, 0x47, 0xff, 0x27, 0x36, 0x47, 0xff, 0x35, 0x47, 0x56, 0xff, 0x38, 0x4d, 0x5e, 0xff, 0x46, 0x5f, 0x72, 0xff, 0x4d, 0x68, 0x7e, 0xff, 0x4f, 0x6b, 0x82, 0xff, 0x56, 0x72, 0x8a, 0xff, 0x4b, 0x68, 0x80, 0xff, 0x40, 0x5b, 0x72, 0xff, 0x2e, 0x49, 0x5e, 0xff, 0x20, 0x3a, 0x50, 0xff, 0x30, 0x48, 0x5f, 0xff, 0x45, 0x62, 0x77, 0xff, 0x4c, 0x6c, 0x81, 0xff, 0x4d, 0x6d, 0x81, 0xff, 0x52, 0x73, 0x86, 0xff, 0x59, 0x7b, 0x8e, 0xff, 0x5b, 0x7f, 0x93, 0xff, 0x58, 0x74, 0x86, 0xff, 0x39, 0x50, 0x60, 0xff, 0x29, 0x41, 0x53, 0xff, 0x33, 0x4c, 0x60, 0xff, 0x42, 0x5c, 0x71, 0xff, 0x62, 0x79, 0x90, 0xff, 0x6d, 0x87, 0x9c, 0xff, 0x44, 0x60, 0x73, 0xff, 0x2d, 0x4b, 0x60, 0xff, 0x3d, 0x5b, 0x6f, 0xff, 0x4d, 0x66, 0x7b, 0xff, 0x4c, 0x5c, 0x74, 0xff, 0x4c, 0x61, 0x74, 0xff, 0x57, 0x71, 0x82, 0xff, 0x52, 0x6a, 0x7f, 0xff, 0x47, 0x5e, 0x71, 0xff, 0x3c, 0x50, 0x5f, 0xff, 0x18, 0x28, 0x34, 0xff, 0x00, 0x0a, 0x19, 0xff, 0x1a, 0x26, 0x38, 0xff, 0x65, 0x7b, 0x8d, 0xff, 0x5d, 0x7c, 0x8d, 0xff, 0x23, 0x3c, 0x4e, 0xff, 0x41, 0x59, 0x68, 0xff, 0x35, 0x51, 0x64, 0xff, 0x33, 0x4f, 0x66, 0xff, 0x66, 0x84, 0x99, 0xff, 0x61, 0x7f, 0x91, 0xff, 0x49, 0x64, 0x72, 0xff, 0x8c, 0xa8, 0xb2, 0xff, 0x9f, 0xb7, 0xb6, 0xff, 0xa6, 0xb1, 0xac, 0xff, 0xac, 0xb1, 0xb0, 0xff, 0xb6, 0xbd, 0xbc, 0xff, 0xbb, 0xc3, 0xc1, 0xff, 0xbf, 0xbd, 0xb8, 0xff, 0xbb, 0xb9, 0xb3, 0xff, 0xb3, 0xb5, 0xb2, 0xd1, 0xb4, 0xb4, 0xae, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x64, 0x79, 0x56, 0x42, 0x60, 0x77, 0xf3, 0x3a, 0x56, 0x6e, 0xff, 0x4e, 0x69, 0x81, 0xff, 0x55, 0x72, 0x89, 0xff, 0x4e, 0x6c, 0x82, 0xff, 0x32, 0x4d, 0x64, 0xff, 0x2c, 0x48, 0x5d, 0xff, 0x45, 0x5f, 0x73, 0xff, 0x4e, 0x67, 0x7b, 0xff, 0x38, 0x53, 0x68, 0xff, 0x3c, 0x58, 0x6c, 0xff, 0x3e, 0x57, 0x6b, 0xff, 0x35, 0x4d, 0x61, 0xff, 0x33, 0x4a, 0x5c, 0xff, 0x2e, 0x45, 0x57, 0xff, 0x2d, 0x44, 0x56, 0xff, 0x40, 0x57, 0x68, 0xff, 0x53, 0x66, 0x78, 0xff, 0x4e, 0x5e, 0x71, 0xff, 0x41, 0x4f, 0x63, 0xff, 0x4a, 0x57, 0x6b, 0xff, 0x4d, 0x5c, 0x6f, 0xff, 0x51, 0x63, 0x75, 0xff, 0x53, 0x68, 0x79, 0xff, 0x5a, 0x71, 0x83, 0xff, 0x5c, 0x74, 0x88, 0xff, 0x5b, 0x73, 0x87, 0xff, 0x57, 0x72, 0x88, 0xff, 0x4b, 0x68, 0x7f, 0xff, 0x52, 0x6f, 0x84, 0xff, 0x58, 0x73, 0x88, 0xff, 0x3d, 0x59, 0x6d, 0xff, 0x20, 0x3c, 0x51, 0xff, 0x16, 0x32, 0x47, 0xff, 0x26, 0x42, 0x58, 0xff, 0x41, 0x5f, 0x74, 0xff, 0x55, 0x74, 0x89, 0xff, 0x59, 0x7b, 0x8f, 0xff, 0x55, 0x78, 0x8d, 0xff, 0x5d, 0x7d, 0x90, 0xff, 0x58, 0x76, 0x87, 0xff, 0x3a, 0x57, 0x68, 0xff, 0x32, 0x4b, 0x60, 0xff, 0x30, 0x49, 0x5d, 0xff, 0x2d, 0x46, 0x58, 0xff, 0x43, 0x5b, 0x70, 0xff, 0x67, 0x80, 0x96, 0xff, 0x64, 0x81, 0x97, 0xff, 0x3c, 0x5b, 0x70, 0xff, 0x3d, 0x59, 0x6f, 0xff, 0x5c, 0x73, 0x8a, 0xff, 0x4a, 0x62, 0x74, 0xff, 0x41, 0x5a, 0x6b, 0xff, 0x4c, 0x65, 0x79, 0xff, 0x51, 0x6b, 0x7e, 0xff, 0x4a, 0x5f, 0x71, 0xff, 0x33, 0x47, 0x56, 0xff, 0x1c, 0x27, 0x35, 0xff, 0x02, 0x09, 0x18, 0xff, 0x1b, 0x2e, 0x3d, 0xff, 0x6a, 0x85, 0x95, 0xff, 0x5f, 0x79, 0x8a, 0xff, 0x1b, 0x31, 0x42, 0xff, 0x43, 0x5e, 0x72, 0xff, 0x39, 0x58, 0x6d, 0xff, 0x3c, 0x5c, 0x71, 0xff, 0x70, 0x90, 0xa2, 0xff, 0x54, 0x72, 0x81, 0xff, 0x61, 0x82, 0x8c, 0xff, 0x9d, 0xb6, 0xb8, 0xff, 0xb2, 0xba, 0xb7, 0xff, 0xb3, 0xb7, 0xb7, 0xff, 0xa8, 0xb0, 0xae, 0xff, 0xb7, 0xc1, 0xbe, 0xff, 0xc1, 0xc0, 0xbc, 0xff, 0xbb, 0xb8, 0xb4, 0xff, 0xb6, 0xb8, 0xb4, 0xff, 0xb3, 0xb4, 0xb0, 0xf3, 0xb1, 0xb1, 0xae, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 0xaa, 0x03, 0x40, 0x60, 0x78, 0x8a, 0x3f, 0x5e, 0x75, 0xfe, 0x44, 0x63, 0x7a, 0xff, 0x43, 0x5f, 0x77, 0xff, 0x51, 0x6d, 0x85, 0xff, 0x4c, 0x6b, 0x83, 0xff, 0x45, 0x63, 0x7e, 0xff, 0x41, 0x5e, 0x77, 0xff, 0x2c, 0x49, 0x60, 0xff, 0x42, 0x5d, 0x75, 0xff, 0x53, 0x70, 0x86, 0xff, 0x42, 0x5e, 0x73, 0xff, 0x42, 0x5e, 0x73, 0xff, 0x49, 0x62, 0x78, 0xff, 0x43, 0x5c, 0x71, 0xff, 0x45, 0x5b, 0x70, 0xff, 0x3c, 0x52, 0x66, 0xff, 0x40, 0x56, 0x69, 0xff, 0x54, 0x68, 0x7a, 0xff, 0x63, 0x74, 0x88, 0xff, 0x5c, 0x6c, 0x80, 0xff, 0x5e, 0x6d, 0x80, 0xff, 0x61, 0x6f, 0x82, 0xff, 0x60, 0x71, 0x85, 0xff, 0x5f, 0x71, 0x86, 0xff, 0x55, 0x67, 0x7a, 0xff, 0x49, 0x5b, 0x6d, 0xff, 0x42, 0x54, 0x65, 0xff, 0x4d, 0x5e, 0x6f, 0xff, 0x5b, 0x72, 0x85, 0xff, 0x5c, 0x78, 0x8b, 0xff, 0x55, 0x70, 0x85, 0xff, 0x49, 0x66, 0x7b, 0xff, 0x48, 0x66, 0x7b, 0xff, 0x4f, 0x6d, 0x81, 0xff, 0x40, 0x5b, 0x70, 0xff, 0x27, 0x40, 0x56, 0xff, 0x18, 0x33, 0x48, 0xff, 0x1b, 0x37, 0x4c, 0xff, 0x40, 0x5c, 0x71, 0xff, 0x5e, 0x79, 0x8e, 0xff, 0x50, 0x70, 0x86, 0xff, 0x50, 0x73, 0x8a, 0xff, 0x64, 0x85, 0x99, 0xff, 0x55, 0x73, 0x87, 0xff, 0x3b, 0x57, 0x69, 0xff, 0x3b, 0x56, 0x65, 0xff, 0x31, 0x48, 0x5b, 0xff, 0x2e, 0x43, 0x5a, 0xff, 0x55, 0x6e, 0x84, 0xff, 0x73, 0x90, 0xa5, 0xff, 0x52, 0x71, 0x86, 0xff, 0x40, 0x60, 0x76, 0xff, 0x56, 0x72, 0x85, 0xff, 0x54, 0x6d, 0x7e, 0xff, 0x44, 0x5f, 0x73, 0xff, 0x45, 0x5f, 0x75, 0xff, 0x5a, 0x74, 0x87, 0xff, 0x44, 0x5a, 0x6c, 0xff, 0x2e, 0x3c, 0x49, 0xff, 0x0f, 0x17, 0x22, 0xff, 0x06, 0x14, 0x20, 0xff, 0x1e, 0x31, 0x3e, 0xff, 0x75, 0x8c, 0x9f, 0xff, 0x5d, 0x75, 0x8c, 0xff, 0x25, 0x3e, 0x52, 0xff, 0x4c, 0x6d, 0x7f, 0xff, 0x3e, 0x60, 0x74, 0xff, 0x4d, 0x72, 0x84, 0xff, 0x69, 0x8c, 0x9b, 0xff, 0x4f, 0x74, 0x81, 0xff, 0x85, 0xa2, 0xa7, 0xff, 0xb9, 0xc2, 0xc3, 0xff, 0xb5, 0xb8, 0xb9, 0xff, 0xaf, 0xb7, 0xb5, 0xff, 0xac, 0xb8, 0xb5, 0xff, 0xb8, 0xbe, 0xbb, 0xff, 0xbd, 0xbd, 0xb9, 0xff, 0xb7, 0xb9, 0xb4, 0xff, 0xb5, 0xb6, 0xb2, 0xff, 0xb3, 0xb4, 0xb0, 0xfe, 0xb5, 0xb5, 0xb1, 0x8b, 0xaa, 0xaa, 0xaa, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x5c, 0x73, 0x0b, 0x55, 0x75, 0x8b, 0xae, 0x3f, 0x5f, 0x76, 0xff, 0x35, 0x54, 0x6b, 0xff, 0x3f, 0x5e, 0x75, 0xff, 0x50, 0x6c, 0x84, 0xff, 0x52, 0x6e, 0x86, 0xff, 0x4e, 0x6d, 0x86, 0xff, 0x44, 0x63, 0x7d, 0xff, 0x54, 0x73, 0x8b, 0xff, 0x45, 0x62, 0x7a, 0xff, 0x41, 0x5c, 0x74, 0xff, 0x55, 0x73, 0x89, 0xff, 0x5b, 0x75, 0x8a, 0xff, 0x56, 0x6d, 0x83, 0xff, 0x5d, 0x75, 0x8a, 0xff, 0x58, 0x6e, 0x83, 0xff, 0x5a, 0x6c, 0x81, 0xff, 0x5d, 0x71, 0x83, 0xff, 0x5f, 0x70, 0x82, 0xff, 0x6c, 0x7b, 0x8e, 0xff, 0x7a, 0x89, 0x9c, 0xff, 0x75, 0x84, 0x97, 0xff, 0x77, 0x85, 0x98, 0xff, 0x74, 0x82, 0x95, 0xff, 0x70, 0x7e, 0x92, 0xff, 0x5e, 0x6d, 0x80, 0xff, 0x52, 0x60, 0x74, 0xff, 0x4f, 0x5e, 0x70, 0xff, 0x54, 0x63, 0x74, 0xff, 0x5e, 0x6b, 0x7c, 0xff, 0x58, 0x6a, 0x7c, 0xff, 0x5d, 0x74, 0x86, 0xff, 0x69, 0x81, 0x95, 0xff, 0x5c, 0x77, 0x8c, 0xff, 0x46, 0x63, 0x78, 0xff, 0x47, 0x64, 0x79, 0xff, 0x4a, 0x66, 0x7c, 0xff, 0x49, 0x63, 0x78, 0xff, 0x3e, 0x59, 0x6e, 0xff, 0x22, 0x3d, 0x52, 0xff, 0x0d, 0x27, 0x3c, 0xff, 0x25, 0x3d, 0x52, 0xff, 0x48, 0x65, 0x7c, 0xff, 0x4d, 0x6e, 0x87, 0xff, 0x47, 0x67, 0x7d, 0xff, 0x4f, 0x6f, 0x83, 0xff, 0x63, 0x82, 0x94, 0xff, 0x50, 0x6e, 0x7e, 0xff, 0x46, 0x5f, 0x73, 0xff, 0x38, 0x4e, 0x65, 0xff, 0x21, 0x39, 0x4f, 0xff, 0x46, 0x62, 0x77, 0xff, 0x68, 0x87, 0x9d, 0xff, 0x57, 0x79, 0x8e, 0xff, 0x4a, 0x67, 0x79, 0xff, 0x5b, 0x74, 0x87, 0xff, 0x51, 0x6e, 0x83, 0xff, 0x46, 0x63, 0x79, 0xff, 0x5b, 0x76, 0x8c, 0xff, 0x60, 0x7a, 0x8e, 0xff, 0x3c, 0x51, 0x5f, 0xff, 0x25, 0x33, 0x3e, 0xff, 0x03, 0x0b, 0x17, 0xff, 0x06, 0x13, 0x1f, 0xff, 0x23, 0x34, 0x45, 0xff, 0x82, 0x98, 0xae, 0xff, 0x64, 0x7e, 0x92, 0xff, 0x36, 0x54, 0x65, 0xff, 0x50, 0x72, 0x85, 0xff, 0x41, 0x65, 0x79, 0xff, 0x47, 0x6b, 0x7e, 0xff, 0x57, 0x7e, 0x8f, 0xff, 0x72, 0x92, 0x9c, 0xff, 0xab, 0xbb, 0xc0, 0xff, 0xbb, 0xc1, 0xc4, 0xff, 0xb5, 0xbd, 0xba, 0xff, 0xa7, 0xb4, 0xb1, 0xff, 0xb1, 0xb9, 0xb7, 0xff, 0xbb, 0xbc, 0xba, 0xff, 0xb9, 0xb9, 0xb5, 0xff, 0xb7, 0xb8, 0xb4, 0xff, 0xb4, 0xb5, 0xb1, 0xff, 0xb2, 0xb3, 0xaf, 0xff, 0xb1, 0xb3, 0xae, 0xaf, 0xb9, 0xb9, 0xa2, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x68, 0x7f, 0x16, 0x2c, 0x4a, 0x60, 0xc9, 0x4a, 0x6a, 0x80, 0xff, 0x44, 0x63, 0x7b, 0xff, 0x30, 0x4f, 0x67, 0xff, 0x3a, 0x59, 0x70, 0xff, 0x58, 0x74, 0x8d, 0xff, 0x61, 0x7d, 0x96, 0xff, 0x5c, 0x7a, 0x92, 0xff, 0x4a, 0x69, 0x7f, 0xff, 0x5e, 0x7a, 0x92, 0xff, 0x6b, 0x88, 0x9d, 0xff, 0x54, 0x70, 0x84, 0xff, 0x5e, 0x79, 0x8d, 0xff, 0x72, 0x88, 0x9c, 0xff, 0x6c, 0x80, 0x95, 0xff, 0x73, 0x86, 0x9a, 0xff, 0x7e, 0x8f, 0xa1, 0xff, 0x78, 0x88, 0x9a, 0xff, 0x7d, 0x8d, 0x9d, 0xff, 0x81, 0x8c, 0x9c, 0xff, 0x84, 0x90, 0x9e, 0xff, 0x8f, 0x9a, 0xa9, 0xff, 0x8f, 0x9b, 0xaa, 0xff, 0x8e, 0x9a, 0xa9, 0xff, 0x8e, 0x9b, 0xa9, 0xff, 0x86, 0x8f, 0x9f, 0xff, 0x7e, 0x86, 0x98, 0xff, 0x75, 0x80, 0x92, 0xff, 0x73, 0x7e, 0x91, 0xff, 0x6e, 0x7c, 0x8e, 0xff, 0x60, 0x6e, 0x82, 0xff, 0x59, 0x67, 0x7a, 0xff, 0x59, 0x68, 0x7a, 0xff, 0x5b, 0x6d, 0x81, 0xff, 0x69, 0x7f, 0x94, 0xff, 0x68, 0x82, 0x97, 0xff, 0x58, 0x73, 0x8b, 0xff, 0x49, 0x67, 0x7d, 0xff, 0x41, 0x60, 0x74, 0xff, 0x40, 0x5d, 0x72, 0xff, 0x40, 0x5b, 0x70, 0xff, 0x39, 0x54, 0x69, 0xff, 0x25, 0x3e, 0x53, 0xff, 0x21, 0x3b, 0x52, 0xff, 0x34, 0x4e, 0x66, 0xff, 0x49, 0x65, 0x7a, 0xff, 0x44, 0x61, 0x76, 0xff, 0x3f, 0x5e, 0x74, 0xff, 0x5f, 0x7f, 0x94, 0xff, 0x61, 0x82, 0x96, 0xff, 0x44, 0x63, 0x78, 0xff, 0x3f, 0x5a, 0x6f, 0xff, 0x20, 0x3a, 0x4f, 0xff, 0x2b, 0x47, 0x5c, 0xff, 0x5d, 0x7d, 0x92, 0xff, 0x5f, 0x7e, 0x90, 0xff, 0x4b, 0x66, 0x79, 0xff, 0x53, 0x71, 0x87, 0xff, 0x49, 0x69, 0x82, 0xff, 0x4c, 0x69, 0x82, 0xff, 0x5a, 0x76, 0x8d, 0xff, 0x58, 0x76, 0x89, 0xff, 0x37, 0x51, 0x61, 0xff, 0x1c, 0x28, 0x34, 0xff, 0x05, 0x0c, 0x15, 0xff, 0x06, 0x12, 0x1d, 0xff, 0x32, 0x48, 0x5a, 0xff, 0x7e, 0x99, 0xa9, 0xff, 0x5b, 0x77, 0x86, 0xff, 0x3b, 0x5a, 0x6e, 0xff, 0x46, 0x68, 0x7d, 0xff, 0x45, 0x69, 0x7e, 0xff, 0x45, 0x6a, 0x7e, 0xff, 0x61, 0x85, 0x93, 0xff, 0x8c, 0xa6, 0xae, 0xff, 0xb4, 0xbf, 0xc1, 0xff, 0xb6, 0xbd, 0xbb, 0xff, 0xad, 0xb8, 0xb4, 0xff, 0xb6, 0xba, 0xba, 0xff, 0xb8, 0xb6, 0xb5, 0xff, 0xb8, 0xb9, 0xb4, 0xff, 0xb8, 0xb9, 0xb5, 0xff, 0xb4, 0xb5, 0xb1, 0xff, 0xb2, 0xb3, 0xaf, 0xff, 0xb2, 0xb3, 0xaf, 0xff, 0xb0, 0xb1, 0xac, 0xc9, 0xad, 0xad, 0xad, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4e, 0x75, 0x89, 0x1a, 0x5f, 0x7e, 0x92, 0xd6, 0x47, 0x66, 0x7b, 0xff, 0x42, 0x63, 0x79, 0xff, 0x50, 0x6f, 0x86, 0xff, 0x43, 0x62, 0x78, 0xff, 0x48, 0x67, 0x7e, 0xff, 0x5a, 0x77, 0x8f, 0xff, 0x6f, 0x8a, 0xa3, 0xff, 0x73, 0x8e, 0xa5, 0xff, 0x6d, 0x88, 0x9d, 0xff, 0x63, 0x7f, 0x92, 0xff, 0x76, 0x90, 0xa4, 0xff, 0x75, 0x8f, 0xa1, 0xff, 0x75, 0x8f, 0xa1, 0xff, 0x89, 0x9d, 0xae, 0xff, 0x92, 0xa2, 0xb3, 0xff, 0x89, 0x98, 0xa9, 0xff, 0x8f, 0x9e, 0xae, 0xff, 0x9c, 0xa9, 0xba, 0xff, 0x98, 0xa5, 0xb4, 0xff, 0x9d, 0xa4, 0xb2, 0xff, 0xa4, 0xab, 0xb7, 0xff, 0xa5, 0xad, 0xb9, 0xff, 0xa3, 0xab, 0xb7, 0xff, 0x9a, 0xa3, 0xb0, 0xff, 0x95, 0x9f, 0xac, 0xff, 0x9a, 0x9f, 0xac, 0xff, 0x9e, 0xa3, 0xb1, 0xff, 0x8f, 0x96, 0xa7, 0xff, 0x80, 0x8a, 0x9c, 0xff, 0x73, 0x80, 0x94, 0xff, 0x65, 0x74, 0x8b, 0xff, 0x62, 0x6b, 0x7f, 0xff, 0x60, 0x69, 0x7a, 0xff, 0x55, 0x62, 0x75, 0xff, 0x57, 0x69, 0x7d, 0xff, 0x64, 0x7a, 0x91, 0xff, 0x68, 0x7f, 0x98, 0xff, 0x5f, 0x7e, 0x94, 0xff, 0x53, 0x75, 0x88, 0xff, 0x4b, 0x6b, 0x80, 0xff, 0x3e, 0x5c, 0x71, 0xff, 0x34, 0x50, 0x64, 0xff, 0x36, 0x52, 0x67, 0xff, 0x3b, 0x52, 0x66, 0xff, 0x3a, 0x4f, 0x62, 0xff, 0x35, 0x4e, 0x62, 0xff, 0x41, 0x5c, 0x72, 0xff, 0x48, 0x67, 0x7f, 0xff, 0x3f, 0x5e, 0x78, 0xff, 0x54, 0x79, 0x8e, 0xff, 0x67, 0x8c, 0x9f, 0xff, 0x55, 0x72, 0x87, 0xff, 0x44, 0x5c, 0x72, 0xff, 0x19, 0x33, 0x48, 0xff, 0x1e, 0x3b, 0x50, 0xff, 0x59, 0x75, 0x89, 0xff, 0x58, 0x76, 0x87, 0xff, 0x47, 0x67, 0x7c, 0xff, 0x52, 0x72, 0x8a, 0xff, 0x4b, 0x6b, 0x84, 0xff, 0x4e, 0x6b, 0x83, 0xff, 0x5a, 0x7d, 0x93, 0xff, 0x62, 0x82, 0x96, 0xff, 0x3b, 0x4e, 0x5a, 0xff, 0x0e, 0x17, 0x1e, 0xff, 0x10, 0x1a, 0x23, 0xff, 0x00, 0x0b, 0x1a, 0xff, 0x42, 0x58, 0x67, 0xff, 0x71, 0x8b, 0x99, 0xff, 0x41, 0x5e, 0x71, 0xff, 0x46, 0x65, 0x7c, 0xff, 0x58, 0x7a, 0x91, 0xff, 0x41, 0x62, 0x78, 0xff, 0x43, 0x6c, 0x7c, 0xff, 0x76, 0x9a, 0xa3, 0xff, 0xad, 0xbc, 0xbe, 0xff, 0xba, 0xc2, 0xbf, 0xff, 0xad, 0xb5, 0xb2, 0xff, 0xb8, 0xb7, 0xb9, 0xff, 0xb8, 0xb5, 0xb5, 0xff, 0xb7, 0xb8, 0xb3, 0xff, 0xb4, 0xb5, 0xb1, 0xff, 0xb4, 0xb5, 0xb1, 0xff, 0xb3, 0xb3, 0xaf, 0xff, 0xb2, 0xb3, 0xaf, 0xff, 0xb1, 0xb2, 0xae, 0xff, 0xaf, 0xb0, 0xab, 0xd6, 0xb0, 0xb0, 0xa6, 0x1a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4f, 0x67, 0x7f, 0x20, 0x58, 0x77, 0x89, 0xde, 0x55, 0x74, 0x87, 0xff, 0x5d, 0x7c, 0x8f, 0xff, 0x4f, 0x6a, 0x7e, 0xff, 0x56, 0x71, 0x85, 0xff, 0x57, 0x77, 0x8a, 0xff, 0x58, 0x79, 0x8c, 0xff, 0x66, 0x82, 0x98, 0xff, 0x76, 0x8e, 0xa4, 0xff, 0x78, 0x91, 0xa6, 0xff, 0x89, 0xa3, 0xb5, 0xff, 0x8b, 0xa3, 0xb6, 0xff, 0x88, 0x9b, 0xad, 0xff, 0x93, 0xa3, 0xb4, 0xff, 0x92, 0xa2, 0xb4, 0xff, 0x98, 0xa6, 0xb4, 0xff, 0xa2, 0xad, 0xb9, 0xff, 0xa1, 0xab, 0xb7, 0xff, 0xa1, 0xaa, 0xb4, 0xff, 0xb2, 0xb8, 0xc2, 0xff, 0xb4, 0xb8, 0xc1, 0xff, 0xb9, 0xbc, 0xc5, 0xff, 0xb9, 0xbb, 0xc5, 0xff, 0xb5, 0xb8, 0xc2, 0xff, 0xb5, 0xbb, 0xc4, 0xff, 0xb0, 0xb5, 0xbe, 0xff, 0xa9, 0xaf, 0xb9, 0xff, 0xaa, 0xb0, 0xbc, 0xff, 0xa7, 0xac, 0xb9, 0xff, 0x9d, 0xa4, 0xb1, 0xff, 0x93, 0x9a, 0xab, 0xff, 0x8a, 0x93, 0xa5, 0xff, 0x81, 0x8c, 0x9f, 0xff, 0x73, 0x7c, 0x8d, 0xff, 0x6e, 0x78, 0x89, 0xff, 0x62, 0x6e, 0x80, 0xff, 0x55, 0x61, 0x74, 0xff, 0x52, 0x5f, 0x73, 0xff, 0x61, 0x6f, 0x82, 0xff, 0x64, 0x7a, 0x91, 0xff, 0x63, 0x7f, 0x9a, 0xff, 0x59, 0x77, 0x90, 0xff, 0x53, 0x72, 0x8a, 0xff, 0x47, 0x62, 0x79, 0xff, 0x2b, 0x41, 0x5a, 0xff, 0x21, 0x37, 0x4a, 0xff, 0x30, 0x46, 0x54, 0xff, 0x3b, 0x51, 0x61, 0xff, 0x35, 0x4b, 0x60, 0xff, 0x43, 0x60, 0x75, 0xff, 0x48, 0x6d, 0x81, 0xff, 0x3e, 0x60, 0x77, 0xff, 0x50, 0x71, 0x8a, 0xff, 0x63, 0x8a, 0xa1, 0xff, 0x55, 0x7b, 0x90, 0xff, 0x42, 0x63, 0x77, 0xff, 0x19, 0x31, 0x47, 0xff, 0x16, 0x2d, 0x42, 0xff, 0x53, 0x6e, 0x83, 0xff, 0x5c, 0x7a, 0x8e, 0xff, 0x44, 0x64, 0x79, 0xff, 0x58, 0x78, 0x8b, 0xff, 0x5e, 0x7d, 0x91, 0xff, 0x48, 0x67, 0x7c, 0xff, 0x65, 0x83, 0x99, 0xff, 0x6c, 0x86, 0x9a, 0xff, 0x27, 0x3a, 0x4d, 0xff, 0x0d, 0x19, 0x29, 0xff, 0x19, 0x1c, 0x29, 0xff, 0x05, 0x0e, 0x1a, 0xff, 0x50, 0x66, 0x74, 0xff, 0x5d, 0x76, 0x85, 0xff, 0x45, 0x62, 0x73, 0xff, 0x54, 0x75, 0x8a, 0xff, 0x5b, 0x7b, 0x93, 0xff, 0x3b, 0x61, 0x77, 0xff, 0x5e, 0x83, 0x92, 0xff, 0xa3, 0xb8, 0xbe, 0xff, 0xba, 0xc4, 0xc2, 0xff, 0xab, 0xb1, 0xae, 0xff, 0xb2, 0xb8, 0xb8, 0xff, 0xb2, 0xb7, 0xb5, 0xff, 0xb7, 0xb8, 0xb3, 0xff, 0xb3, 0xb3, 0xaf, 0xff, 0xb4, 0xb5, 0xb1, 0xff, 0xb4, 0xb3, 0xaf, 0xff, 0xb3, 0xb1, 0xad, 0xff, 0xb2, 0xb2, 0xae, 0xff, 0xaf, 0xb0, 0xac, 0xff, 0xaa, 0xab, 0xa6, 0xde, 0xa7, 0xa7, 0xa7, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x51, 0x6d, 0x7f, 0x1c, 0x2e, 0x4a, 0x5f, 0xdb, 0x44, 0x60, 0x74, 0xff, 0x44, 0x61, 0x74, 0xff, 0x4b, 0x67, 0x7b, 0xff, 0x61, 0x7a, 0x8e, 0xff, 0x61, 0x7c, 0x90, 0xff, 0x61, 0x81, 0x93, 0xff, 0x6b, 0x8a, 0x9e, 0xff, 0x7d, 0x97, 0xab, 0xff, 0x84, 0x9c, 0xb2, 0xff, 0x89, 0xa0, 0xb2, 0xff, 0x96, 0xab, 0xba, 0xff, 0xa3, 0xb4, 0xc4, 0xff, 0xa3, 0xb2, 0xc1, 0xff, 0xa3, 0xaf, 0xbe, 0xff, 0xab, 0xb5, 0xc5, 0xff, 0xac, 0xb4, 0xc2, 0xff, 0xa9, 0xb0, 0xbb, 0xff, 0xae, 0xb4, 0xbf, 0xff, 0xb2, 0xb6, 0xbf, 0xff, 0xba, 0xbd, 0xc5, 0xff, 0xbf, 0xc0, 0xc9, 0xff, 0xbf, 0xc0, 0xca, 0xff, 0xba, 0xbb, 0xc5, 0xff, 0xba, 0xbc, 0xc6, 0xff, 0xba, 0xc0, 0xc8, 0xff, 0xba, 0xbe, 0xc7, 0xff, 0xb5, 0xbb, 0xc3, 0xff, 0xad, 0xb3, 0xbf, 0xff, 0xab, 0xb1, 0xbe, 0xff, 0xa3, 0xa9, 0xb7, 0xff, 0x9d, 0xa4, 0xb4, 0xff, 0x95, 0x9d, 0xae, 0xff, 0x89, 0x93, 0xa5, 0xff, 0x81, 0x8b, 0x9c, 0xff, 0x7e, 0x88, 0x99, 0xff, 0x79, 0x83, 0x95, 0xff, 0x6a, 0x76, 0x88, 0xff, 0x62, 0x6d, 0x7f, 0xff, 0x5a, 0x65, 0x78, 0xff, 0x56, 0x64, 0x7b, 0xff, 0x61, 0x77, 0x90, 0xff, 0x6a, 0x85, 0x9b, 0xff, 0x62, 0x80, 0x98, 0xff, 0x5c, 0x79, 0x92, 0xff, 0x53, 0x6f, 0x88, 0xff, 0x3d, 0x5a, 0x6e, 0xff, 0x21, 0x3e, 0x4f, 0xff, 0x1b, 0x33, 0x46, 0xff, 0x2d, 0x44, 0x58, 0xff, 0x32, 0x4b, 0x5f, 0xff, 0x40, 0x5f, 0x71, 0xff, 0x59, 0x75, 0x8d, 0xff, 0x4c, 0x68, 0x83, 0xff, 0x4c, 0x72, 0x88, 0xff, 0x5e, 0x87, 0x9d, 0xff, 0x69, 0x8b, 0xa0, 0xff, 0x5c, 0x78, 0x8c, 0xff, 0x22, 0x3a, 0x4e, 0xff, 0x0e, 0x27, 0x3a, 0xff, 0x50, 0x6c, 0x80, 0xff, 0x5a, 0x79, 0x8c, 0xff, 0x3e, 0x5c, 0x72, 0xff, 0x59, 0x79, 0x8e, 0xff, 0x62, 0x82, 0x97, 0xff, 0x47, 0x67, 0x7b, 0xff, 0x68, 0x86, 0x9c, 0xff, 0x6e, 0x88, 0x9e, 0xff, 0x19, 0x2c, 0x3f, 0xff, 0x1e, 0x2a, 0x37, 0xff, 0x1b, 0x21, 0x2d, 0xff, 0x12, 0x22, 0x2e, 0xff, 0x60, 0x79, 0x88, 0xff, 0x48, 0x64, 0x74, 0xff, 0x50, 0x70, 0x83, 0xff, 0x73, 0x95, 0xab, 0xff, 0x4b, 0x72, 0x89, 0xff, 0x3f, 0x67, 0x78, 0xff, 0x81, 0x9c, 0xa3, 0xff, 0xb7, 0xc4, 0xc4, 0xff, 0xaf, 0xb5, 0xb2, 0xff, 0xb1, 0xb6, 0xb7, 0xff, 0xb2, 0xb6, 0xb4, 0xff, 0xb3, 0xb7, 0xb1, 0xff, 0xb2, 0xb4, 0xb0, 0xff, 0xb1, 0xb1, 0xad, 0xff, 0xb1, 0xb1, 0xad, 0xff, 0xb2, 0xb0, 0xac, 0xff, 0xb0, 0xb0, 0xac, 0xff, 0xad, 0xae, 0xaa, 0xff, 0xaa, 0xab, 0xa7, 0xff, 0xa8, 0xa9, 0xa4, 0xdc, 0xa3, 0xad, 0xa3, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x6d, 0x79, 0x15, 0x60, 0x7b, 0x8d, 0xd6, 0x5e, 0x79, 0x8b, 0xff, 0x43, 0x5e, 0x70, 0xff, 0x4a, 0x66, 0x78, 0xff, 0x4e, 0x6a, 0x7c, 0xff, 0x5d, 0x77, 0x8a, 0xff, 0x6b, 0x84, 0x99, 0xff, 0x73, 0x8d, 0xa1, 0xff, 0x84, 0x9e, 0xb3, 0xff, 0x90, 0xa8, 0xbc, 0xff, 0x96, 0xad, 0xc0, 0xff, 0xa6, 0xb6, 0xc4, 0xff, 0xae, 0xbc, 0xc7, 0xff, 0xaf, 0xbd, 0xc7, 0xff, 0xb4, 0xc0, 0xcb, 0xff, 0xb6, 0xbf, 0xc9, 0xff, 0xb8, 0xc0, 0xca, 0xff, 0xbc, 0xc1, 0xcb, 0xff, 0xbf, 0xc3, 0xcc, 0xff, 0xbe, 0xc1, 0xcb, 0xff, 0xbf, 0xc0, 0xca, 0xff, 0xbd, 0xbd, 0xc8, 0xff, 0xbc, 0xbc, 0xc6, 0xff, 0xb8, 0xb9, 0xc3, 0xff, 0xba, 0xbc, 0xc6, 0xff, 0xbe, 0xc1, 0xcb, 0xff, 0xbd, 0xc3, 0xcc, 0xff, 0xbc, 0xc1, 0xca, 0xff, 0xb6, 0xbc, 0xc4, 0xff, 0xae, 0xb3, 0xbe, 0xff, 0xac, 0xb0, 0xbd, 0xff, 0xa8, 0xaf, 0xbc, 0xff, 0xa1, 0xa8, 0xb6, 0xff, 0x9e, 0xa6, 0xb7, 0xff, 0x92, 0x9b, 0xad, 0xff, 0x8d, 0x96, 0xa7, 0xff, 0x87, 0x91, 0xa2, 0xff, 0x7b, 0x84, 0x96, 0xff, 0x78, 0x82, 0x94, 0xff, 0x72, 0x7e, 0x90, 0xff, 0x65, 0x70, 0x83, 0xff, 0x64, 0x6e, 0x81, 0xff, 0x5b, 0x6a, 0x7c, 0xff, 0x5f, 0x74, 0x87, 0xff, 0x6b, 0x85, 0x9b, 0xff, 0x67, 0x88, 0xa0, 0xff, 0x5e, 0x80, 0x9a, 0xff, 0x54, 0x78, 0x90, 0xff, 0x4d, 0x71, 0x88, 0xff, 0x3d, 0x5c, 0x71, 0xff, 0x29, 0x43, 0x57, 0xff, 0x25, 0x3d, 0x51, 0xff, 0x24, 0x3b, 0x4c, 0xff, 0x2d, 0x44, 0x5a, 0xff, 0x4f, 0x68, 0x81, 0xff, 0x59, 0x79, 0x90, 0xff, 0x5b, 0x80, 0x96, 0xff, 0x5f, 0x84, 0x9a, 0xff, 0x63, 0x88, 0x9d, 0xff, 0x62, 0x80, 0x91, 0xff, 0x27, 0x3f, 0x4e, 0xff, 0x02, 0x1c, 0x2e, 0xff, 0x4b, 0x68, 0x7d, 0xff, 0x62, 0x82, 0x98, 0xff, 0x42, 0x64, 0x7d, 0xff, 0x53, 0x76, 0x8c, 0xff, 0x64, 0x87, 0x9a, 0xff, 0x4c, 0x6e, 0x85, 0xff, 0x68, 0x87, 0x9d, 0xff, 0x6c, 0x86, 0x98, 0xff, 0x1a, 0x2f, 0x3e, 0xff, 0x23, 0x35, 0x42, 0xff, 0x08, 0x11, 0x1d, 0xff, 0x23, 0x32, 0x42, 0xff, 0x52, 0x6b, 0x7c, 0xff, 0x46, 0x65, 0x77, 0xff, 0x64, 0x89, 0x9b, 0xff, 0x60, 0x8a, 0x9e, 0xff, 0x44, 0x6f, 0x81, 0xff, 0x5c, 0x7d, 0x86, 0xff, 0xa4, 0xb9, 0xbb, 0xff, 0xb2, 0xbc, 0xbc, 0xff, 0xad, 0xb0, 0xb2, 0xff, 0xaf, 0xb2, 0xb0, 0xff, 0xb1, 0xb5, 0xaf, 0xff, 0xaf, 0xb3, 0xae, 0xff, 0xaf, 0xb0, 0xac, 0xff, 0xae, 0xaf, 0xab, 0xff, 0xad, 0xad, 0xaa, 0xff, 0xad, 0xae, 0xaa, 0xff, 0xab, 0xac, 0xa8, 0xff, 0xa9, 0xaa, 0xa6, 0xff, 0xa7, 0xa8, 0xa4, 0xff, 0xa7, 0xa8, 0xa3, 0xd7, 0xaa, 0xaa, 0x9d, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x7f, 0x99, 0x0a, 0x4f, 0x69, 0x7a, 0xc8, 0x49, 0x62, 0x73, 0xff, 0x4c, 0x66, 0x77, 0xff, 0x4a, 0x65, 0x75, 0xff, 0x4b, 0x65, 0x76, 0xff, 0x5a, 0x74, 0x85, 0xff, 0x6c, 0x85, 0x98, 0xff, 0x7c, 0x94, 0xa6, 0xff, 0x8c, 0xa1, 0xb3, 0xff, 0x99, 0xab, 0xbc, 0xff, 0x9d, 0xb0, 0xc0, 0xff, 0xa4, 0xb7, 0xc7, 0xff, 0xae, 0xba, 0xc4, 0xff, 0xbc, 0xc4, 0xcb, 0xff, 0xc2, 0xca, 0xd3, 0xff, 0xc7, 0xd0, 0xd6, 0xff, 0xc9, 0xd0, 0xd6, 0xff, 0xc7, 0xce, 0xd4, 0xff, 0xc6, 0xc9, 0xd1, 0xff, 0xc6, 0xc7, 0xd2, 0xff, 0xc5, 0xc6, 0xd0, 0xff, 0xc2, 0xc3, 0xcd, 0xff, 0xc0, 0xc1, 0xcb, 0xff, 0xbd, 0xbd, 0xc7, 0xff, 0xb8, 0xb9, 0xc3, 0xff, 0xba, 0xbc, 0xc6, 0xff, 0xbd, 0xc1, 0xca, 0xff, 0xbd, 0xc2, 0xcb, 0xff, 0xbd, 0xc3, 0xcc, 0xff, 0xb9, 0xbf, 0xc8, 0xff, 0xb5, 0xbb, 0xc5, 0xff, 0xb2, 0xb5, 0xc0, 0xff, 0xa9, 0xae, 0xba, 0xff, 0xa3, 0xaa, 0xb8, 0xff, 0xa5, 0xad, 0xbc, 0xff, 0x9d, 0xa5, 0xb7, 0xff, 0x93, 0x9b, 0xab, 0xff, 0x8d, 0x96, 0xa4, 0xff, 0x83, 0x8c, 0x9d, 0xff, 0x7c, 0x85, 0x97, 0xff, 0x77, 0x81, 0x93, 0xff, 0x6c, 0x79, 0x8b, 0xff, 0x6f, 0x78, 0x88, 0xff, 0x6b, 0x73, 0x84, 0xff, 0x58, 0x66, 0x78, 0xff, 0x56, 0x6a, 0x7d, 0xff, 0x5a, 0x76, 0x8c, 0xff, 0x67, 0x88, 0x9e, 0xff, 0x65, 0x89, 0xa1, 0xff, 0x5c, 0x7f, 0x98, 0xff, 0x55, 0x76, 0x8e, 0xff, 0x4e, 0x6d, 0x82, 0xff, 0x48, 0x63, 0x77, 0xff, 0x39, 0x4e, 0x61, 0xff, 0x25, 0x3a, 0x50, 0xff, 0x19, 0x33, 0x4b, 0xff, 0x39, 0x57, 0x6f, 0xff, 0x65, 0x87, 0x9e, 0xff, 0x60, 0x85, 0x9b, 0xff, 0x4f, 0x76, 0x8d, 0xff, 0x56, 0x78, 0x8b, 0xff, 0x5e, 0x7b, 0x8b, 0xff, 0x26, 0x3f, 0x52, 0xff, 0x00, 0x10, 0x25, 0xff, 0x39, 0x55, 0x6a, 0xff, 0x63, 0x85, 0x9c, 0xff, 0x54, 0x77, 0x8c, 0xff, 0x5e, 0x81, 0x96, 0xff, 0x6a, 0x8e, 0xa5, 0xff, 0x4e, 0x70, 0x88, 0xff, 0x6c, 0x8b, 0x9e, 0xff, 0x52, 0x6e, 0x7f, 0xff, 0x36, 0x4c, 0x5a, 0xff, 0x20, 0x2d, 0x3b, 0xff, 0x00, 0x01, 0x11, 0xff, 0x45, 0x58, 0x69, 0xff, 0x42, 0x5e, 0x6f, 0xff, 0x47, 0x6a, 0x78, 0xff, 0x65, 0x8e, 0x9f, 0xff, 0x4d, 0x79, 0x8a, 0xff, 0x55, 0x7a, 0x85, 0xff, 0x88, 0xa4, 0xa9, 0xff, 0x9e, 0xaf, 0xb0, 0xff, 0xb0, 0xb5, 0xb7, 0xff, 0xae, 0xb2, 0xb1, 0xff, 0xac, 0xb2, 0xac, 0xff, 0xad, 0xb2, 0xad, 0xff, 0xad, 0xb0, 0xab, 0xff, 0xad, 0xaf, 0xab, 0xff, 0xad, 0xad, 0xa9, 0xff, 0xac, 0xad, 0xa9, 0xff, 0xab, 0xac, 0xa8, 0xff, 0xa9, 0xaa, 0xa6, 0xff, 0xa7, 0xa8, 0xa4, 0xff, 0xa6, 0xa7, 0xa3, 0xff, 0xa4, 0xa5, 0xa1, 0xc8, 0xb2, 0xb2, 0x99, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0xaa, 0xaa, 0x03, 0x56, 0x70, 0x84, 0xb1, 0x4d, 0x66, 0x78, 0xff, 0x4e, 0x66, 0x78, 0xff, 0x41, 0x58, 0x6b, 0xff, 0x5b, 0x73, 0x85, 0xff, 0x66, 0x7e, 0x91, 0xff, 0x66, 0x7f, 0x91, 0xff, 0x7d, 0x94, 0xa5, 0xff, 0x96, 0xa9, 0xb8, 0xff, 0xa8, 0xb5, 0xc4, 0xff, 0xb4, 0xbe, 0xca, 0xff, 0xb9, 0xc4, 0xcf, 0xff, 0xbe, 0xcb, 0xd5, 0xff, 0xc5, 0xce, 0xd7, 0xff, 0xc8, 0xcf, 0xd8, 0xff, 0xc9, 0xd0, 0xd7, 0xff, 0xc9, 0xcf, 0xd6, 0xff, 0xcb, 0xd1, 0xd5, 0xff, 0xc8, 0xce, 0xd2, 0xff, 0xc3, 0xc7, 0xcd, 0xff, 0xc6, 0xc8, 0xcf, 0xff, 0xc5, 0xc7, 0xcf, 0xff, 0xc1, 0xc4, 0xcd, 0xff, 0xc0, 0xc3, 0xcd, 0xff, 0xc2, 0xc5, 0xcf, 0xff, 0xc2, 0xc4, 0xce, 0xff, 0xc1, 0xc2, 0xcc, 0xff, 0xbc, 0xc0, 0xca, 0xff, 0xbb, 0xc0, 0xc9, 0xff, 0xba, 0xbe, 0xc7, 0xff, 0xb8, 0xbe, 0xc7, 0xff, 0xbb, 0xbf, 0xc8, 0xff, 0xba, 0xbc, 0xc7, 0xff, 0xae, 0xb3, 0xbf, 0xff, 0xa8, 0xae, 0xbb, 0xff, 0xa6, 0xad, 0xbb, 0xff, 0xa2, 0xab, 0xba, 0xff, 0x99, 0xa2, 0xb0, 0xff, 0x8f, 0x99, 0xa6, 0xff, 0x8d, 0x96, 0xa4, 0xff, 0x86, 0x8f, 0xa0, 0xff, 0x7d, 0x87, 0x97, 0xff, 0x7b, 0x86, 0x98, 0xff, 0x75, 0x81, 0x93, 0xff, 0x72, 0x7b, 0x8d, 0xff, 0x6f, 0x77, 0x89, 0xff, 0x61, 0x6b, 0x7d, 0xff, 0x55, 0x64, 0x77, 0xff, 0x5b, 0x70, 0x82, 0xff, 0x65, 0x80, 0x96, 0xff, 0x6b, 0x8a, 0xa5, 0xff, 0x62, 0x83, 0x9a, 0xff, 0x53, 0x76, 0x8d, 0xff, 0x52, 0x72, 0x89, 0xff, 0x57, 0x71, 0x89, 0xff, 0x51, 0x6d, 0x83, 0xff, 0x38, 0x56, 0x6a, 0xff, 0x1e, 0x3c, 0x52, 0xff, 0x2c, 0x4d, 0x64, 0xff, 0x5c, 0x7e, 0x94, 0xff, 0x6f, 0x93, 0xab, 0xff, 0x5b, 0x82, 0x98, 0xff, 0x55, 0x79, 0x8e, 0xff, 0x66, 0x82, 0x98, 0xff, 0x2e, 0x41, 0x56, 0xff, 0x00, 0x0b, 0x1e, 0xff, 0x27, 0x44, 0x55, 0xff, 0x5f, 0x81, 0x95, 0xff, 0x62, 0x84, 0x9c, 0xff, 0x67, 0x89, 0xa1, 0xff, 0x6b, 0x8e, 0xa6, 0xff, 0x62, 0x82, 0x98, 0xff, 0x65, 0x84, 0x98, 0xff, 0x59, 0x75, 0x85, 0xff, 0x39, 0x50, 0x5d, 0xff, 0x06, 0x16, 0x25, 0xff, 0x17, 0x25, 0x35, 0xff, 0x4e, 0x63, 0x74, 0xff, 0x2d, 0x48, 0x57, 0xff, 0x55, 0x78, 0x89, 0xff, 0x5e, 0x86, 0x9a, 0xff, 0x55, 0x7c, 0x8b, 0xff, 0x6d, 0x90, 0x98, 0xff, 0x88, 0xa1, 0xa3, 0xff, 0xa5, 0xb0, 0xb3, 0xff, 0xaf, 0xb5, 0xb2, 0xff, 0xab, 0xb1, 0xab, 0xff, 0xaa, 0xb0, 0xab, 0xff, 0xab, 0xb0, 0xaa, 0xff, 0xaa, 0xaf, 0xa9, 0xff, 0xab, 0xad, 0xa8, 0xff, 0xab, 0xab, 0xa8, 0xff, 0xaa, 0xab, 0xa7, 0xff, 0xa9, 0xaa, 0xa6, 0xff, 0xa7, 0xa8, 0xa4, 0xff, 0xa4, 0xa5, 0xa1, 0xff, 0xa4, 0xa5, 0xa1, 0xff, 0xa5, 0xa5, 0xa1, 0xb1, 0xaa, 0xaa, 0xaa, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x51, 0x6c, 0x80, 0x8d, 0x53, 0x6b, 0x7f, 0xff, 0x61, 0x7a, 0x8b, 0xff, 0x63, 0x7c, 0x8c, 0xff, 0x67, 0x7f, 0x90, 0xff, 0x60, 0x78, 0x89, 0xff, 0x66, 0x7e, 0x90, 0xff, 0x77, 0x91, 0xa2, 0xff, 0x98, 0xae, 0xbc, 0xff, 0xb5, 0xc3, 0xcf, 0xff, 0xc1, 0xc9, 0xd5, 0xff, 0xce, 0xd2, 0xdc, 0xff, 0xd2, 0xd8, 0xde, 0xff, 0xd4, 0xdd, 0xe1, 0xff, 0xd7, 0xde, 0xe6, 0xff, 0xd4, 0xda, 0xe4, 0xff, 0xd2, 0xd8, 0xdf, 0xff, 0xcd, 0xd3, 0xda, 0xff, 0xcb, 0xd1, 0xd6, 0xff, 0xc7, 0xcd, 0xd2, 0xff, 0xc5, 0xca, 0xce, 0xff, 0xc5, 0xca, 0xce, 0xff, 0xc2, 0xc7, 0xce, 0xff, 0xc1, 0xc7, 0xcf, 0xff, 0xbd, 0xc3, 0xcc, 0xff, 0xbd, 0xc4, 0xce, 0xff, 0xbf, 0xc2, 0xcc, 0xff, 0xc0, 0xc1, 0xcb, 0xff, 0xbf, 0xc3, 0xcc, 0xff, 0xbf, 0xc4, 0xcd, 0xff, 0xbc, 0xc1, 0xca, 0xff, 0xb9, 0xc0, 0xc9, 0xff, 0xb8, 0xbc, 0xc6, 0xff, 0xb8, 0xb8, 0xc3, 0xff, 0xb0, 0xb4, 0xbf, 0xff, 0xac, 0xb2, 0xbe, 0xff, 0xa5, 0xac, 0xbc, 0xff, 0xa3, 0xaa, 0xb8, 0xff, 0xa0, 0xa7, 0xb6, 0xff, 0x95, 0x9e, 0xac, 0xff, 0x92, 0x9a, 0xa8, 0xff, 0x91, 0x9a, 0xaa, 0xff, 0x8a, 0x94, 0xa5, 0xff, 0x84, 0x8e, 0x9f, 0xff, 0x7e, 0x8c, 0x9e, 0xff, 0x7a, 0x85, 0x98, 0xff, 0x7b, 0x80, 0x93, 0xff, 0x78, 0x7a, 0x8d, 0xff, 0x76, 0x7b, 0x8c, 0xff, 0x66, 0x6e, 0x7b, 0xff, 0x51, 0x62, 0x76, 0xff, 0x5b, 0x76, 0x8e, 0xff, 0x6a, 0x8b, 0xa2, 0xff, 0x5f, 0x84, 0x9c, 0xff, 0x4c, 0x71, 0x8a, 0xff, 0x4b, 0x6d, 0x8a, 0xff, 0x5b, 0x7c, 0x94, 0xff, 0x67, 0x88, 0x9a, 0xff, 0x55, 0x74, 0x89, 0xff, 0x38, 0x56, 0x6c, 0xff, 0x35, 0x54, 0x6d, 0xff, 0x4e, 0x6e, 0x87, 0xff, 0x65, 0x8e, 0xa6, 0xff, 0x65, 0x8f, 0xa7, 0xff, 0x64, 0x81, 0x98, 0xff, 0x6e, 0x84, 0x99, 0xff, 0x2f, 0x43, 0x56, 0xff, 0x00, 0x14, 0x21, 0xff, 0x23, 0x42, 0x53, 0xff, 0x51, 0x71, 0x89, 0xff, 0x6a, 0x8c, 0xa4, 0xff, 0x65, 0x88, 0xa1, 0xff, 0x70, 0x91, 0xaa, 0xff, 0x73, 0x92, 0xaa, 0xff, 0x56, 0x73, 0x85, 0xff, 0x58, 0x74, 0x80, 0xff, 0x43, 0x56, 0x66, 0xff, 0x04, 0x11, 0x22, 0xff, 0x27, 0x37, 0x47, 0xff, 0x2d, 0x42, 0x50, 0xff, 0x3b, 0x58, 0x6a, 0xff, 0x75, 0x98, 0xac, 0xff, 0x52, 0x7a, 0x8a, 0xff, 0x5f, 0x86, 0x90, 0xff, 0x7c, 0x9a, 0x9e, 0xff, 0x97, 0xa8, 0xaa, 0xff, 0xaa, 0xb2, 0xaf, 0xff, 0xaf, 0xb4, 0xae, 0xff, 0xa9, 0xaf, 0xaa, 0xff, 0xa9, 0xae, 0xa9, 0xff, 0xaa, 0xae, 0xa9, 0xff, 0xa9, 0xac, 0xa7, 0xff, 0xa8, 0xaa, 0xa5, 0xff, 0xa8, 0xa8, 0xa4, 0xff, 0xa7, 0xa8, 0xa4, 0xff, 0xa7, 0xa8, 0xa4, 0xff, 0xa5, 0xa6, 0xa2, 0xff, 0xa5, 0xa6, 0xa2, 0xff, 0xa5, 0xa6, 0xa3, 0xff, 0xa6, 0xa8, 0xa4, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x7b, 0x8a, 0x55, 0x54, 0x6b, 0x7d, 0xfe, 0x5c, 0x73, 0x85, 0xff, 0x5b, 0x72, 0x81, 0xff, 0x5e, 0x75, 0x85, 0xff, 0x48, 0x5f, 0x6f, 0xff, 0x5a, 0x70, 0x80, 0xff, 0x77, 0x8b, 0x98, 0xff, 0x98, 0xa8, 0xb3, 0xff, 0xb5, 0xc3, 0xcb, 0xff, 0xc3, 0xd0, 0xd8, 0xff, 0xc8, 0xd2, 0xdb, 0xff, 0xd2, 0xd8, 0xe0, 0xff, 0xdb, 0xde, 0xe5, 0xff, 0xd8, 0xdb, 0xe3, 0xff, 0xd6, 0xdb, 0xdf, 0xff, 0xd8, 0xde, 0xe1, 0xff, 0xd5, 0xd9, 0xde, 0xff, 0xd3, 0xd8, 0xdd, 0xff, 0xcf, 0xd2, 0xda, 0xff, 0xca, 0xcd, 0xd5, 0xff, 0xc7, 0xca, 0xd2, 0xff, 0xc3, 0xc7, 0xce, 0xff, 0xc5, 0xc9, 0xd0, 0xff, 0xc5, 0xc9, 0xd0, 0xff, 0xc0, 0xc4, 0xcc, 0xff, 0xc1, 0xc5, 0xcd, 0xff, 0xbd, 0xc0, 0xc9, 0xff, 0xba, 0xbc, 0xc7, 0xff, 0xc0, 0xc2, 0xcc, 0xff, 0xc1, 0xc4, 0xce, 0xff, 0xc2, 0xc5, 0xcf, 0xff, 0xc2, 0xc5, 0xce, 0xff, 0xbc, 0xc0, 0xc7, 0xff, 0xb6, 0xbc, 0xc2, 0xff, 0xb2, 0xb7, 0xbf, 0xff, 0xb0, 0xb4, 0xbf, 0xff, 0xa7, 0xaa, 0xb8, 0xff, 0xa6, 0xa9, 0xb8, 0xff, 0xa5, 0xab, 0xba, 0xff, 0x9d, 0xa4, 0xb4, 0xff, 0x99, 0xa0, 0xaf, 0xff, 0x97, 0x9e, 0xae, 0xff, 0x94, 0x9c, 0xad, 0xff, 0x8e, 0x96, 0xa7, 0xff, 0x88, 0x91, 0xa0, 0xff, 0x84, 0x8c, 0x9a, 0xff, 0x80, 0x87, 0x97, 0xff, 0x7e, 0x85, 0x97, 0xff, 0x78, 0x7f, 0x90, 0xff, 0x72, 0x7b, 0x8b, 0xff, 0x63, 0x6d, 0x7f, 0xff, 0x52, 0x60, 0x76, 0xff, 0x59, 0x6c, 0x81, 0xff, 0x65, 0x7f, 0x97, 0xff, 0x62, 0x82, 0x9c, 0xff, 0x4c, 0x71, 0x8b, 0xff, 0x46, 0x69, 0x82, 0xff, 0x57, 0x79, 0x90, 0xff, 0x6c, 0x8c, 0xa4, 0xff, 0x6e, 0x8d, 0xa5, 0xff, 0x50, 0x6f, 0x86, 0xff, 0x34, 0x52, 0x67, 0xff, 0x39, 0x5c, 0x73, 0xff, 0x5c, 0x84, 0x9a, 0xff, 0x76, 0x9b, 0xb1, 0xff, 0x67, 0x89, 0x9e, 0xff, 0x66, 0x84, 0x98, 0xff, 0x29, 0x46, 0x5b, 0xff, 0x04, 0x15, 0x28, 0xff, 0x33, 0x44, 0x57, 0xff, 0x42, 0x63, 0x7a, 0xff, 0x5d, 0x88, 0xa0, 0xff, 0x7c, 0xa3, 0xbb, 0xff, 0x7c, 0x9b, 0xb2, 0xff, 0x6f, 0x8b, 0x9f, 0xff, 0x55, 0x70, 0x83, 0xff, 0x78, 0x8e, 0x9f, 0xff, 0x2f, 0x40, 0x51, 0xff, 0x00, 0x0e, 0x1d, 0xff, 0x2c, 0x42, 0x51, 0xff, 0x1e, 0x3a, 0x48, 0xff, 0x61, 0x85, 0x93, 0xff, 0x60, 0x88, 0x97, 0xff, 0x67, 0x90, 0x9e, 0xff, 0x70, 0x8e, 0x97, 0xff, 0x97, 0xa8, 0xa9, 0xff, 0xa8, 0xb3, 0xb0, 0xff, 0xa5, 0xad, 0xaa, 0xff, 0xab, 0xad, 0xab, 0xff, 0xa5, 0xab, 0xa7, 0xff, 0xa4, 0xab, 0xa5, 0xff, 0xac, 0xa9, 0xa5, 0xff, 0xaa, 0xa8, 0xa4, 0xff, 0xa4, 0xa8, 0xa4, 0xff, 0xa6, 0xa9, 0xa4, 0xff, 0xa7, 0xaa, 0xa5, 0xff, 0xa6, 0xa9, 0xa4, 0xff, 0xa5, 0xa8, 0xa3, 0xff, 0xa4, 0xa8, 0xa3, 0xff, 0xa5, 0xa9, 0xa4, 0xfe, 0xab, 0xab, 0xa8, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x76, 0x8f, 0x9b, 0x29, 0x64, 0x7c, 0x8d, 0xf3, 0x66, 0x7d, 0x8f, 0xff, 0x5d, 0x72, 0x84, 0xff, 0x4a, 0x60, 0x70, 0xff, 0x41, 0x58, 0x68, 0xff, 0x54, 0x6b, 0x7b, 0xff, 0x5f, 0x74, 0x85, 0xff, 0x78, 0x8a, 0x96, 0xff, 0xa9, 0xb7, 0xc1, 0xff, 0xba, 0xc6, 0xcd, 0xff, 0xc6, 0xd2, 0xd9, 0xff, 0xca, 0xd3, 0xdc, 0xff, 0xd5, 0xdc, 0xe3, 0xff, 0xdc, 0xdf, 0xe8, 0xff, 0xdb, 0xdc, 0xe4, 0xff, 0xda, 0xde, 0xe1, 0xff, 0xdb, 0xe0, 0xe2, 0xff, 0xd6, 0xda, 0xdf, 0xff, 0xd5, 0xd9, 0xdd, 0xff, 0xd0, 0xd3, 0xdb, 0xff, 0xce, 0xd0, 0xd9, 0xff, 0xc8, 0xcb, 0xd3, 0xff, 0xc2, 0xc5, 0xcd, 0xff, 0xc3, 0xc6, 0xce, 0xff, 0xc6, 0xc9, 0xd1, 0xff, 0xc3, 0xc6, 0xce, 0xff, 0xc2, 0xc5, 0xcc, 0xff, 0xc3, 0xc5, 0xce, 0xff, 0xbd, 0xc0, 0xca, 0xff, 0xbf, 0xc1, 0xcb, 0xff, 0xc2, 0xc4, 0xce, 0xff, 0xc3, 0xc4, 0xce, 0xff, 0xc4, 0xc5, 0xcf, 0xff, 0xbf, 0xc3, 0xca, 0xff, 0xba, 0xc1, 0xc7, 0xff, 0xb4, 0xb9, 0xc1, 0xff, 0xaf, 0xb3, 0xbe, 0xff, 0xab, 0xad, 0xbb, 0xff, 0xa8, 0xaa, 0xb8, 0xff, 0xa7, 0xad, 0xbb, 0xff, 0xa3, 0xab, 0xb9, 0xff, 0xa0, 0xa7, 0xb6, 0xff, 0x9b, 0xa2, 0xb1, 0xff, 0x95, 0x9c, 0xac, 0xff, 0x93, 0x99, 0xab, 0xff, 0x8d, 0x93, 0xa3, 0xff, 0x89, 0x8f, 0x9d, 0xff, 0x86, 0x8d, 0x9d, 0xff, 0x82, 0x8b, 0x9c, 0xff, 0x77, 0x7f, 0x90, 0xff, 0x6d, 0x77, 0x89, 0xff, 0x6b, 0x74, 0x86, 0xff, 0x62, 0x6b, 0x7d, 0xff, 0x56, 0x63, 0x76, 0xff, 0x54, 0x67, 0x7c, 0xff, 0x5f, 0x7a, 0x90, 0xff, 0x66, 0x84, 0x9b, 0xff, 0x54, 0x75, 0x8d, 0xff, 0x44, 0x67, 0x80, 0xff, 0x4d, 0x70, 0x88, 0xff, 0x6a, 0x8c, 0xa5, 0xff, 0x73, 0x96, 0xad, 0xff, 0x56, 0x7a, 0x91, 0xff, 0x3b, 0x5d, 0x75, 0xff, 0x32, 0x52, 0x6a, 0xff, 0x54, 0x79, 0x90, 0xff, 0x73, 0x99, 0xae, 0xff, 0x6c, 0x8f, 0xa3, 0xff, 0x63, 0x84, 0x9a, 0xff, 0x37, 0x4d, 0x5f, 0xff, 0x0b, 0x1b, 0x2c, 0xff, 0x23, 0x3b, 0x53, 0xff, 0x37, 0x58, 0x71, 0xff, 0x6e, 0x8f, 0xa7, 0xff, 0x7b, 0x9a, 0xb0, 0xff, 0x7f, 0xa1, 0xb4, 0xff, 0x68, 0x88, 0x9b, 0xff, 0x55, 0x6e, 0x82, 0xff, 0x69, 0x7f, 0x90, 0xff, 0x1c, 0x32, 0x42, 0xff, 0x1c, 0x30, 0x3f, 0xff, 0x13, 0x2b, 0x39, 0xff, 0x33, 0x53, 0x61, 0xff, 0x7b, 0xa1, 0xb1, 0xff, 0x6b, 0x94, 0xa2, 0xff, 0x6f, 0x8f, 0x9a, 0xff, 0x89, 0xa0, 0xa1, 0xff, 0xaa, 0xb8, 0xb6, 0xff, 0xa0, 0xa6, 0xa4, 0xff, 0xa7, 0xa8, 0xa7, 0xff, 0xa5, 0xaa, 0xa8, 0xff, 0x9f, 0xa9, 0xa3, 0xff, 0xa7, 0xa7, 0xa3, 0xff, 0xa9, 0xa7, 0xa3, 0xff, 0xa3, 0xa8, 0xa3, 0xff, 0xa6, 0xaa, 0xa5, 0xff, 0xa8, 0xac, 0xa7, 0xff, 0xa7, 0xab, 0xa6, 0xff, 0xa5, 0xa9, 0xa4, 0xff, 0xa5, 0xa9, 0xa4, 0xff, 0xa7, 0xab, 0xa6, 0xff, 0xab, 0xac, 0xa8, 0xf4, 0xaa, 0xaa, 0xaa, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x91, 0xb6, 0x07, 0x45, 0x5f, 0x70, 0xd1, 0x43, 0x5b, 0x6d, 0xff, 0x51, 0x68, 0x7a, 0xff, 0x41, 0x58, 0x69, 0xff, 0x44, 0x5a, 0x6a, 0xff, 0x53, 0x6a, 0x7a, 0xff, 0x40, 0x57, 0x67, 0xff, 0x46, 0x5b, 0x6c, 0xff, 0x83, 0x96, 0xa3, 0xff, 0xa5, 0xb5, 0xbf, 0xff, 0xbb, 0xc5, 0xce, 0xff, 0xc8, 0xd2, 0xda, 0xff, 0xd4, 0xdd, 0xe5, 0xff, 0xdb, 0xe1, 0xe8, 0xff, 0xde, 0xe2, 0xe8, 0xff, 0xe1, 0xe5, 0xea, 0xff, 0xde, 0xe3, 0xe6, 0xff, 0xd9, 0xde, 0xe1, 0xff, 0xd6, 0xda, 0xdf, 0xff, 0xd2, 0xd6, 0xdb, 0xff, 0xcf, 0xd2, 0xda, 0xff, 0xcc, 0xcf, 0xd7, 0xff, 0xc5, 0xc8, 0xd0, 0xff, 0xc2, 0xc5, 0xcd, 0xff, 0xc1, 0xc4, 0xcc, 0xff, 0xc4, 0xc7, 0xcf, 0xff, 0xc6, 0xc9, 0xd1, 0xff, 0xc1, 0xc5, 0xcc, 0xff, 0xc1, 0xc3, 0xcd, 0xff, 0xc1, 0xc3, 0xcd, 0xff, 0xbe, 0xc0, 0xca, 0xff, 0xc0, 0xc2, 0xcc, 0xff, 0xbf, 0xc1, 0xcb, 0xff, 0xbd, 0xbe, 0xc9, 0xff, 0xbd, 0xc2, 0xca, 0xff, 0xb7, 0xbd, 0xc5, 0xff, 0xb7, 0xbc, 0xc5, 0xff, 0xb4, 0xb9, 0xc3, 0xff, 0xb1, 0xb5, 0xc1, 0xff, 0xad, 0xae, 0xbd, 0xff, 0xa8, 0xae, 0xbb, 0xff, 0xa6, 0xaf, 0xbb, 0xff, 0xa5, 0xad, 0xba, 0xff, 0x9f, 0xa6, 0xb5, 0xff, 0x99, 0xa0, 0xaf, 0xff, 0x96, 0x9d, 0xae, 0xff, 0x94, 0x9b, 0xab, 0xff, 0x92, 0x99, 0xa7, 0xff, 0x8c, 0x93, 0xa3, 0xff, 0x84, 0x8c, 0x9d, 0xff, 0x78, 0x80, 0x91, 0xff, 0x70, 0x7a, 0x8c, 0xff, 0x72, 0x7a, 0x8b, 0xff, 0x6a, 0x72, 0x83, 0xff, 0x62, 0x6d, 0x7e, 0xff, 0x5c, 0x6b, 0x7c, 0xff, 0x4f, 0x63, 0x74, 0xff, 0x5c, 0x71, 0x83, 0xff, 0x6e, 0x8b, 0xa1, 0xff, 0x61, 0x84, 0x9d, 0xff, 0x4b, 0x6c, 0x85, 0xff, 0x3f, 0x63, 0x7b, 0xff, 0x58, 0x7e, 0x97, 0xff, 0x6b, 0x95, 0xae, 0xff, 0x73, 0x94, 0xae, 0xff, 0x57, 0x73, 0x8c, 0xff, 0x33, 0x56, 0x6e, 0xff, 0x4b, 0x71, 0x87, 0xff, 0x79, 0x9d, 0xb2, 0xff, 0x6b, 0x8e, 0xa2, 0xff, 0x64, 0x85, 0x98, 0xff, 0x34, 0x4e, 0x61, 0xff, 0x14, 0x26, 0x3d, 0xff, 0x2c, 0x3d, 0x55, 0xff, 0x37, 0x4d, 0x65, 0xff, 0x5c, 0x7b, 0x8f, 0xff, 0x76, 0x9a, 0xad, 0xff, 0x90, 0xb4, 0xc8, 0xff, 0x69, 0x89, 0x9c, 0xff, 0x55, 0x70, 0x81, 0xff, 0x68, 0x82, 0x92, 0xff, 0x1f, 0x33, 0x43, 0xff, 0x0c, 0x1c, 0x2c, 0xff, 0x1b, 0x31, 0x40, 0xff, 0x73, 0x95, 0xa6, 0xff, 0x6c, 0x93, 0xa3, 0xff, 0x72, 0x96, 0xa0, 0xff, 0x73, 0x93, 0x96, 0xff, 0x95, 0xa7, 0xa7, 0xff, 0xac, 0xb0, 0xae, 0xff, 0xa6, 0xa5, 0xa5, 0xff, 0xa2, 0xa7, 0xa4, 0xff, 0x9d, 0xa9, 0xa5, 0xff, 0xa0, 0xa8, 0xa3, 0xff, 0xa5, 0xa8, 0xa3, 0xff, 0xa5, 0xa9, 0xa4, 0xff, 0xa6, 0xaa, 0xa5, 0xff, 0xa7, 0xab, 0xa6, 0xff, 0xa6, 0xaa, 0xa5, 0xff, 0xa4, 0xa8, 0xa3, 0xff, 0xa5, 0xa9, 0xa4, 0xff, 0xa6, 0xaa, 0xa5, 0xff, 0xa8, 0xaa, 0xa6, 0xff, 0xac, 0xad, 0xa9, 0xd1, 0xb6, 0xb6, 0xb6, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4b, 0x68, 0x7a, 0x8e, 0x23, 0x3e, 0x52, 0xff, 0x3b, 0x55, 0x66, 0xff, 0x2c, 0x45, 0x56, 0xff, 0x3b, 0x54, 0x64, 0xff, 0x5d, 0x74, 0x84, 0xff, 0x38, 0x4f, 0x5f, 0xff, 0x3e, 0x55, 0x65, 0xff, 0x69, 0x7f, 0x8f, 0xff, 0x7e, 0x91, 0x9e, 0xff, 0x9e, 0xad, 0xb8, 0xff, 0xbd, 0xc8, 0xcf, 0xff, 0xd5, 0xdd, 0xe4, 0xff, 0xe0, 0xe7, 0xed, 0xff, 0xdf, 0xe5, 0xea, 0xff, 0xdf, 0xe4, 0xe8, 0xff, 0xdc, 0xe0, 0xe5, 0xff, 0xd7, 0xdc, 0xe0, 0xff, 0xd4, 0xd9, 0xdd, 0xff, 0xd3, 0xd7, 0xdc, 0xff, 0xd0, 0xd4, 0xd9, 0xff, 0xcf, 0xd2, 0xda, 0xff, 0xcb, 0xce, 0xd6, 0xff, 0xc7, 0xca, 0xd2, 0xff, 0xc4, 0xc7, 0xcf, 0xff, 0xc3, 0xc6, 0xce, 0xff, 0xc3, 0xc6, 0xce, 0xff, 0xc9, 0xcc, 0xd4, 0xff, 0xc4, 0xc7, 0xcf, 0xff, 0xc6, 0xc8, 0xd1, 0xff, 0xc5, 0xc7, 0xd1, 0xff, 0xbc, 0xbe, 0xc8, 0xff, 0xbf, 0xc1, 0xcb, 0xff, 0xc3, 0xc5, 0xcf, 0xff, 0xbc, 0xbe, 0xc9, 0xff, 0xb8, 0xbc, 0xc6, 0xff, 0xb8, 0xbd, 0xc7, 0xff, 0xb9, 0xbe, 0xc8, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xb3, 0xb6, 0xc2, 0xff, 0xaa, 0xb2, 0xbc, 0xff, 0xa7, 0xb1, 0xbd, 0xff, 0xa7, 0xaf, 0xbc, 0xff, 0xa6, 0xad, 0xbb, 0xff, 0xa1, 0xa8, 0xb7, 0xff, 0xa0, 0xa7, 0xb5, 0xff, 0x9e, 0xa5, 0xb5, 0xff, 0x9a, 0xa1, 0xaf, 0xff, 0x95, 0x9b, 0xac, 0xff, 0x8c, 0x94, 0xa6, 0xff, 0x85, 0x8d, 0x9d, 0xff, 0x7b, 0x86, 0x97, 0xff, 0x7d, 0x85, 0x96, 0xff, 0x7d, 0x83, 0x94, 0xff, 0x6d, 0x76, 0x86, 0xff, 0x61, 0x6b, 0x7b, 0xff, 0x59, 0x64, 0x75, 0xff, 0x56, 0x61, 0x71, 0xff, 0x5c, 0x71, 0x84, 0xff, 0x6a, 0x86, 0x9c, 0xff, 0x6e, 0x8c, 0xa2, 0xff, 0x56, 0x78, 0x90, 0xff, 0x42, 0x67, 0x81, 0xff, 0x4f, 0x79, 0x96, 0xff, 0x6c, 0x8f, 0xaa, 0xff, 0x85, 0xa2, 0xbb, 0xff, 0x6d, 0x8f, 0xa7, 0xff, 0x3c, 0x62, 0x79, 0xff, 0x48, 0x6e, 0x84, 0xff, 0x6f, 0x93, 0xa9, 0xff, 0x68, 0x8c, 0xa1, 0xff, 0x72, 0x93, 0xa7, 0xff, 0x41, 0x56, 0x6a, 0xff, 0x22, 0x30, 0x45, 0xff, 0x37, 0x49, 0x5e, 0xff, 0x33, 0x50, 0x64, 0xff, 0x4d, 0x6f, 0x83, 0xff, 0x6e, 0x90, 0xa5, 0xff, 0x8f, 0xb2, 0xc5, 0xff, 0x78, 0x96, 0xa8, 0xff, 0x4f, 0x6a, 0x7a, 0xff, 0x52, 0x6c, 0x7c, 0xff, 0x1d, 0x29, 0x38, 0xff, 0x07, 0x15, 0x23, 0xff, 0x4f, 0x6d, 0x7e, 0xff, 0x79, 0xa0, 0xb0, 0xff, 0x6f, 0x96, 0xa0, 0xff, 0x71, 0x94, 0x99, 0xff, 0x7c, 0x94, 0x95, 0xff, 0xac, 0xb5, 0xb2, 0xff, 0xab, 0xab, 0xab, 0xff, 0xa1, 0xa4, 0xa3, 0xff, 0x99, 0xa5, 0xa1, 0xff, 0x9e, 0xa8, 0xa5, 0xff, 0xa5, 0xa9, 0xa5, 0xff, 0xa6, 0xaa, 0xa5, 0xff, 0xa5, 0xa9, 0xa4, 0xff, 0xa4, 0xa8, 0xa3, 0xff, 0xa4, 0xa8, 0xa3, 0xff, 0xa2, 0xa6, 0xa1, 0xff, 0xa3, 0xa7, 0xa2, 0xff, 0xa4, 0xa8, 0xa3, 0xff, 0xa9, 0xaa, 0xa6, 0xff, 0xad, 0xae, 0xaa, 0xff, 0xb0, 0xb0, 0xac, 0x8f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x73, 0x8f, 0xa3, 0x40, 0x2e, 0x4b, 0x61, 0xfe, 0x2e, 0x4a, 0x5f, 0xff, 0x39, 0x54, 0x66, 0xff, 0x3b, 0x55, 0x65, 0xff, 0x59, 0x72, 0x82, 0xff, 0x35, 0x4d, 0x5c, 0xff, 0x3e, 0x55, 0x65, 0xff, 0x53, 0x6a, 0x7a, 0xff, 0x60, 0x76, 0x86, 0xff, 0x7c, 0x8f, 0x9b, 0xff, 0xaa, 0xb9, 0xc3, 0xff, 0xcd, 0xd6, 0xdc, 0xff, 0xde, 0xe4, 0xeb, 0xff, 0xe3, 0xe9, 0xee, 0xff, 0xde, 0xe4, 0xe8, 0xff, 0xdd, 0xe2, 0xe7, 0xff, 0xdc, 0xe0, 0xe4, 0xff, 0xd7, 0xdc, 0xde, 0xff, 0xd8, 0xdd, 0xe0, 0xff, 0xd3, 0xd7, 0xdc, 0xff, 0xd4, 0xd8, 0xdd, 0xff, 0xd2, 0xd5, 0xdd, 0xff, 0xcd, 0xd0, 0xd8, 0xff, 0xcd, 0xd0, 0xd8, 0xff, 0xc8, 0xcb, 0xd3, 0xff, 0xc5, 0xc8, 0xd0, 0xff, 0xc7, 0xca, 0xd2, 0xff, 0xc9, 0xcc, 0xd4, 0xff, 0xc4, 0xc7, 0xcf, 0xff, 0xc4, 0xc6, 0xcf, 0xff, 0xc3, 0xc5, 0xcf, 0xff, 0xbf, 0xc1, 0xcb, 0xff, 0xc1, 0xc3, 0xcd, 0xff, 0xc3, 0xc5, 0xcf, 0xff, 0xc2, 0xc3, 0xcd, 0xff, 0xbd, 0xc0, 0xcb, 0xff, 0xb8, 0xbc, 0xc8, 0xff, 0xb9, 0xbd, 0xc8, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xb9, 0xbd, 0xc7, 0xff, 0xb7, 0xbb, 0xc4, 0xff, 0xae, 0xb5, 0xbf, 0xff, 0xa8, 0xb2, 0xbc, 0xff, 0xa8, 0xb1, 0xbd, 0xff, 0xa9, 0xb1, 0xbe, 0xff, 0xa6, 0xae, 0xbc, 0xff, 0xa1, 0xa8, 0xb8, 0xff, 0x9f, 0xa6, 0xb5, 0xff, 0x9c, 0xa3, 0xb2, 0xff, 0x98, 0x9f, 0xaf, 0xff, 0x94, 0x9c, 0xad, 0xff, 0x8d, 0x96, 0xa7, 0xff, 0x83, 0x8d, 0x9f, 0xff, 0x7d, 0x86, 0x98, 0xff, 0x79, 0x82, 0x92, 0xff, 0x77, 0x7e, 0x90, 0xff, 0x68, 0x6f, 0x81, 0xff, 0x59, 0x60, 0x71, 0xff, 0x51, 0x57, 0x67, 0xff, 0x48, 0x53, 0x64, 0xff, 0x53, 0x66, 0x74, 0xff, 0x6a, 0x7e, 0x90, 0xff, 0x6f, 0x8a, 0xa1, 0xff, 0x65, 0x86, 0x9f, 0xff, 0x58, 0x7d, 0x99, 0xff, 0x4f, 0x72, 0x8e, 0xff, 0x62, 0x84, 0x9e, 0xff, 0x7d, 0xa1, 0xba, 0xff, 0x72, 0x98, 0xb1, 0xff, 0x43, 0x68, 0x7f, 0xff, 0x4a, 0x69, 0x80, 0xff, 0x73, 0x95, 0xad, 0xff, 0x79, 0x9e, 0xb3, 0xff, 0x72, 0x8f, 0xa1, 0xff, 0x40, 0x58, 0x68, 0xff, 0x27, 0x3e, 0x4f, 0xff, 0x1e, 0x37, 0x49, 0xff, 0x2c, 0x45, 0x5b, 0xff, 0x4e, 0x69, 0x80, 0xff, 0x57, 0x76, 0x8a, 0xff, 0x8e, 0xae, 0xc0, 0xff, 0x65, 0x81, 0x91, 0xff, 0x4d, 0x69, 0x79, 0xff, 0x45, 0x53, 0x60, 0xff, 0x04, 0x0e, 0x1c, 0xff, 0x29, 0x48, 0x59, 0xff, 0x80, 0xa8, 0xb8, 0xff, 0x6b, 0x93, 0xa0, 0xff, 0x79, 0x9a, 0xa1, 0xff, 0x7c, 0x99, 0x99, 0xff, 0x9e, 0xb0, 0xad, 0xff, 0xac, 0xae, 0xaf, 0xff, 0xa5, 0xa6, 0xa7, 0xff, 0x9e, 0xa6, 0xa4, 0xff, 0xa0, 0xa6, 0xa5, 0xff, 0xa4, 0xa8, 0xa5, 0xff, 0xa6, 0xaa, 0xa5, 0xff, 0xa6, 0xaa, 0xa5, 0xff, 0xa4, 0xa8, 0xa3, 0xff, 0xa2, 0xa6, 0xa1, 0xff, 0xa1, 0xa6, 0xa1, 0xff, 0xa2, 0xa7, 0xa2, 0xff, 0xa5, 0xa9, 0xa4, 0xff, 0xaa, 0xac, 0xa8, 0xff, 0xad, 0xae, 0xaa, 0xff, 0xb1, 0xb1, 0xae, 0xfe, 0xb8, 0xb8, 0xb0, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x8d, 0x8d, 0x09, 0x42, 0x60, 0x77, 0xdb, 0x22, 0x3f, 0x57, 0xff, 0x36, 0x52, 0x67, 0xff, 0x3a, 0x54, 0x68, 0xff, 0x3f, 0x59, 0x69, 0xff, 0x25, 0x3e, 0x4d, 0xff, 0x25, 0x3c, 0x4b, 0xff, 0x31, 0x49, 0x58, 0xff, 0x3e, 0x55, 0x65, 0xff, 0x64, 0x79, 0x8a, 0xff, 0x8d, 0xa0, 0xad, 0xff, 0xb6, 0xc5, 0xd0, 0xff, 0xd0, 0xd7, 0xde, 0xff, 0xe1, 0xe6, 0xec, 0xff, 0xe2, 0xe9, 0xee, 0xff, 0xe0, 0xe4, 0xe9, 0xff, 0xde, 0xe3, 0xe6, 0xff, 0xdb, 0xe0, 0xe2, 0xff, 0xda, 0xdf, 0xe2, 0xff, 0xd8, 0xdc, 0xe0, 0xff, 0xd5, 0xd9, 0xde, 0xff, 0xd7, 0xdb, 0xe0, 0xff, 0xd6, 0xd9, 0xe1, 0xff, 0xd2, 0xd5, 0xde, 0xff, 0xcf, 0xd2, 0xda, 0xff, 0xca, 0xcd, 0xd5, 0xff, 0xc6, 0xc9, 0xd1, 0xff, 0xc6, 0xc9, 0xd1, 0xff, 0xc6, 0xc9, 0xd1, 0xff, 0xc5, 0xc8, 0xcf, 0xff, 0xc2, 0xc5, 0xce, 0xff, 0xc3, 0xc5, 0xcf, 0xff, 0xc2, 0xc4, 0xce, 0xff, 0xc1, 0xc3, 0xcd, 0xff, 0xbf, 0xc1, 0xcb, 0xff, 0xbe, 0xbf, 0xc9, 0xff, 0xbb, 0xbe, 0xc9, 0xff, 0xb6, 0xba, 0xc5, 0xff, 0xb3, 0xb7, 0xc2, 0xff, 0xb4, 0xb8, 0xc3, 0xff, 0xb1, 0xb5, 0xbf, 0xff, 0xb1, 0xb6, 0xbe, 0xff, 0xae, 0xb6, 0xc0, 0xff, 0xa8, 0xb2, 0xbc, 0xff, 0xa9, 0xb2, 0xbc, 0xff, 0xa7, 0xb0, 0xbc, 0xff, 0xa5, 0xae, 0xbb, 0xff, 0xa1, 0xa9, 0xb7, 0xff, 0x9e, 0xa6, 0xb5, 0xff, 0x9c, 0xa3, 0xb2, 0xff, 0x95, 0x9c, 0xac, 0xff, 0x93, 0x9b, 0xad, 0xff, 0x8e, 0x96, 0xa7, 0xff, 0x85, 0x8f, 0xa0, 0xff, 0x80, 0x8b, 0x9e, 0xff, 0x76, 0x80, 0x94, 0xff, 0x74, 0x7c, 0x8e, 0xff, 0x6e, 0x74, 0x88, 0xff, 0x60, 0x65, 0x78, 0xff, 0x55, 0x59, 0x6c, 0xff, 0x47, 0x4c, 0x5a, 0xff, 0x41, 0x4a, 0x54, 0xff, 0x49, 0x56, 0x64, 0xff, 0x5f, 0x74, 0x87, 0xff, 0x68, 0x84, 0x9c, 0xff, 0x6f, 0x90, 0xa8, 0xff, 0x67, 0x8c, 0xa5, 0xff, 0x50, 0x79, 0x92, 0xff, 0x54, 0x7c, 0x95, 0xff, 0x75, 0x9d, 0xb4, 0xff, 0x83, 0xa6, 0xbe, 0xff, 0x4b, 0x66, 0x7e, 0xff, 0x4b, 0x6a, 0x83, 0xff, 0x7a, 0x9f, 0xb6, 0xff, 0x7e, 0xa5, 0xb5, 0xff, 0x69, 0x8e, 0x9b, 0xff, 0x41, 0x5d, 0x6c, 0xff, 0x33, 0x47, 0x58, 0xff, 0x11, 0x21, 0x39, 0xff, 0x31, 0x44, 0x5e, 0xff, 0x50, 0x69, 0x7e, 0xff, 0x55, 0x73, 0x86, 0xff, 0x81, 0x9f, 0xb1, 0xff, 0x5d, 0x7b, 0x8c, 0xff, 0x53, 0x66, 0x74, 0xff, 0x36, 0x47, 0x54, 0xff, 0x0e, 0x2d, 0x3f, 0xff, 0x69, 0x90, 0xa1, 0xff, 0x77, 0x9d, 0xaa, 0xff, 0x80, 0x9c, 0xa7, 0xff, 0x7a, 0x99, 0x9a, 0xff, 0x8a, 0xa5, 0xa0, 0xff, 0xaa, 0xb1, 0xb1, 0xff, 0xa5, 0xa7, 0xa7, 0xff, 0xa2, 0xa8, 0xa7, 0xff, 0xa2, 0xa5, 0xa4, 0xff, 0xa5, 0xa7, 0xa3, 0xff, 0xa3, 0xa7, 0xa2, 0xff, 0xa3, 0xa7, 0xa2, 0xff, 0xa2, 0xa6, 0xa1, 0xff, 0x9f, 0xa3, 0x9e, 0xff, 0xa1, 0xa5, 0xa0, 0xff, 0xa2, 0xa6, 0xa1, 0xff, 0xa6, 0xaa, 0xa5, 0xff, 0xac, 0xad, 0xa9, 0xff, 0xae, 0xaf, 0xab, 0xff, 0xb3, 0xb4, 0xb0, 0xff, 0xb9, 0xb7, 0xb4, 0xdb, 0xc6, 0xc6, 0xc6, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x88, 0x9d, 0x88, 0x2b, 0x4c, 0x63, 0xff, 0x1d, 0x3c, 0x54, 0xff, 0x33, 0x51, 0x67, 0xff, 0x32, 0x4c, 0x60, 0xff, 0x31, 0x4b, 0x5d, 0xff, 0x34, 0x4d, 0x5e, 0xff, 0x37, 0x4c, 0x5c, 0xff, 0x27, 0x3d, 0x4d, 0xff, 0x4c, 0x64, 0x75, 0xff, 0x75, 0x8a, 0x9b, 0xff, 0x92, 0xa2, 0xaf, 0xff, 0xb2, 0xc0, 0xca, 0xff, 0xd4, 0xdc, 0xe4, 0xff, 0xe3, 0xe8, 0xef, 0xff, 0xe2, 0xe8, 0xed, 0xff, 0xe1, 0xe6, 0xeb, 0xff, 0xde, 0xe3, 0xe6, 0xff, 0xda, 0xdf, 0xe2, 0xff, 0xd5, 0xda, 0xdd, 0xff, 0xd4, 0xd8, 0xdc, 0xff, 0xd2, 0xd6, 0xdb, 0xff, 0xd3, 0xd7, 0xdc, 0xff, 0xd0, 0xd3, 0xdb, 0xff, 0xcf, 0xd2, 0xda, 0xff, 0xce, 0xd1, 0xd9, 0xff, 0xcc, 0xcf, 0xd7, 0xff, 0xca, 0xcd, 0xd5, 0xff, 0xc9, 0xcc, 0xd4, 0xff, 0xc8, 0xcb, 0xd3, 0xff, 0xc7, 0xca, 0xd2, 0xff, 0xc6, 0xc8, 0xd2, 0xff, 0xc5, 0xc6, 0xd1, 0xff, 0xc2, 0xc5, 0xcf, 0xff, 0xbf, 0xc4, 0xcd, 0xff, 0xbf, 0xc2, 0xcb, 0xff, 0xbc, 0xc2, 0xcb, 0xff, 0xbc, 0xc1, 0xcc, 0xff, 0xbd, 0xc0, 0xcc, 0xff, 0xb6, 0xba, 0xc5, 0xff, 0xb5, 0xb9, 0xc4, 0xff, 0xb4, 0xb8, 0xc3, 0xff, 0xb2, 0xb5, 0xc0, 0xff, 0xb0, 0xb5, 0xbf, 0xff, 0xaf, 0xb4, 0xbe, 0xff, 0xae, 0xb3, 0xbd, 0xff, 0xae, 0xb3, 0xbe, 0xff, 0xa9, 0xad, 0xbb, 0xff, 0xa9, 0xad, 0xbb, 0xff, 0xa8, 0xad, 0xbb, 0xff, 0xa1, 0xa8, 0xb5, 0xff, 0x9f, 0xa5, 0xb3, 0xff, 0x9c, 0xa3, 0xb1, 0xff, 0x93, 0x9a, 0xa9, 0xff, 0x8b, 0x93, 0xa2, 0xff, 0x86, 0x8e, 0x9e, 0xff, 0x7d, 0x85, 0x98, 0xff, 0x78, 0x7f, 0x92, 0xff, 0x72, 0x79, 0x8c, 0xff, 0x67, 0x6f, 0x82, 0xff, 0x57, 0x60, 0x75, 0xff, 0x4f, 0x54, 0x65, 0xff, 0x43, 0x48, 0x55, 0xff, 0x3a, 0x43, 0x50, 0xff, 0x3f, 0x4e, 0x5b, 0xff, 0x4b, 0x64, 0x73, 0xff, 0x5f, 0x7d, 0x8d, 0xff, 0x6e, 0x90, 0xa5, 0xff, 0x74, 0x9a, 0xb1, 0xff, 0x67, 0x8d, 0xa3, 0xff, 0x56, 0x7d, 0x94, 0xff, 0x73, 0x98, 0xb1, 0xff, 0x84, 0xac, 0xc3, 0xff, 0x4c, 0x70, 0x88, 0xff, 0x4c, 0x6d, 0x85, 0xff, 0x78, 0x9b, 0xb1, 0xff, 0x80, 0xa1, 0xb7, 0xff, 0x65, 0x84, 0x99, 0xff, 0x54, 0x70, 0x85, 0xff, 0x42, 0x57, 0x6a, 0xff, 0x0c, 0x1c, 0x2c, 0xff, 0x3a, 0x4e, 0x5f, 0xff, 0x50, 0x67, 0x7a, 0xff, 0x56, 0x71, 0x85, 0xff, 0x70, 0x8c, 0xa3, 0xff, 0x50, 0x68, 0x7c, 0xff, 0x4a, 0x5e, 0x6e, 0xff, 0x16, 0x30, 0x40, 0xff, 0x4b, 0x69, 0x78, 0xff, 0x82, 0xa3, 0xaf, 0xff, 0x77, 0x9a, 0xa4, 0xff, 0x74, 0x95, 0x9c, 0xff, 0x76, 0x8f, 0x92, 0xff, 0xa7, 0xb2, 0xb3, 0xff, 0xa6, 0xa9, 0xa8, 0xff, 0xa3, 0xa6, 0xa4, 0xff, 0x9e, 0xa5, 0xa3, 0xff, 0x9e, 0xa6, 0xa2, 0xff, 0xa1, 0xa6, 0xa1, 0xff, 0xa1, 0xa5, 0xa0, 0xff, 0xa0, 0xa4, 0x9f, 0xff, 0xa0, 0xa4, 0x9f, 0xff, 0xa0, 0xa1, 0x9d, 0xff, 0xa3, 0xa5, 0xa1, 0xff, 0xa7, 0xab, 0xa6, 0xff, 0xaa, 0xad, 0xa8, 0xff, 0xad, 0xb0, 0xab, 0xff, 0xb1, 0xb5, 0xb0, 0xff, 0xb6, 0xba, 0xb5, 0xff, 0xb9, 0xbb, 0xb9, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5d, 0x86, 0x9a, 0x26, 0x53, 0x7a, 0x8f, 0xfa, 0x1e, 0x40, 0x57, 0xff, 0x2b, 0x4b, 0x62, 0xff, 0x39, 0x57, 0x6d, 0xff, 0x34, 0x4f, 0x63, 0xff, 0x41, 0x5a, 0x6d, 0xff, 0x44, 0x5c, 0x6e, 0xff, 0x34, 0x49, 0x59, 0xff, 0x47, 0x5d, 0x6d, 0xff, 0x66, 0x7e, 0x8e, 0xff, 0x65, 0x7b, 0x8b, 0xff, 0x91, 0xa1, 0xae, 0xff, 0xba, 0xc7, 0xd2, 0xff, 0xd5, 0xde, 0xe5, 0xff, 0xe5, 0xea, 0xf1, 0xff, 0xe2, 0xe8, 0xee, 0xff, 0xe2, 0xe8, 0xed, 0xff, 0xde, 0xe2, 0xe6, 0xff, 0xdb, 0xe0, 0xe2, 0xff, 0xd6, 0xdb, 0xde, 0xff, 0xd5, 0xda, 0xdd, 0xff, 0xd0, 0xd4, 0xd9, 0xff, 0xcd, 0xd1, 0xd6, 0xff, 0xcd, 0xd0, 0xd8, 0xff, 0xcb, 0xce, 0xd6, 0xff, 0xc9, 0xcc, 0xd4, 0xff, 0xc6, 0xc9, 0xd1, 0xff, 0xc5, 0xc8, 0xd0, 0xff, 0xc3, 0xc6, 0xce, 0xff, 0xc5, 0xc8, 0xd0, 0xff, 0xc4, 0xc8, 0xcf, 0xff, 0xc1, 0xc4, 0xcd, 0xff, 0xc2, 0xc4, 0xce, 0xff, 0xc1, 0xc5, 0xce, 0xff, 0xbf, 0xc5, 0xce, 0xff, 0xc1, 0xc6, 0xcf, 0xff, 0xbf, 0xc6, 0xcf, 0xff, 0xbe, 0xc3, 0xce, 0xff, 0xbe, 0xc2, 0xcd, 0xff, 0xba, 0xbe, 0xc9, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xba, 0xbe, 0xc9, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xb8, 0xbc, 0xc5, 0xff, 0xb7, 0xbb, 0xc6, 0xff, 0xb1, 0xb5, 0xc0, 0xff, 0xaf, 0xb3, 0xbe, 0xff, 0xae, 0xb1, 0xbe, 0xff, 0xad, 0xaf, 0xbd, 0xff, 0xaa, 0xaf, 0xbc, 0xff, 0xa8, 0xaf, 0xbb, 0xff, 0xa8, 0xae, 0xba, 0xff, 0xa1, 0xa8, 0xb6, 0xff, 0x98, 0xa0, 0xaf, 0xff, 0x93, 0x9a, 0xa8, 0xff, 0x8a, 0x92, 0xa1, 0xff, 0x82, 0x89, 0x9b, 0xff, 0x7d, 0x83, 0x97, 0xff, 0x73, 0x7b, 0x8e, 0xff, 0x6c, 0x77, 0x89, 0xff, 0x5b, 0x65, 0x7a, 0xff, 0x4f, 0x57, 0x6a, 0xff, 0x4b, 0x51, 0x61, 0xff, 0x42, 0x4b, 0x57, 0xff, 0x3c, 0x47, 0x53, 0xff, 0x31, 0x43, 0x52, 0xff, 0x45, 0x5a, 0x6a, 0xff, 0x61, 0x7f, 0x94, 0xff, 0x6d, 0x90, 0xa7, 0xff, 0x81, 0xa3, 0xba, 0xff, 0x7c, 0xa1, 0xb7, 0xff, 0x60, 0x86, 0x9e, 0xff, 0x72, 0x9b, 0xb3, 0xff, 0x87, 0xae, 0xc6, 0xff, 0x43, 0x65, 0x7c, 0xff, 0x4a, 0x6b, 0x82, 0xff, 0x83, 0xa1, 0xba, 0xff, 0x8b, 0xaa, 0xc0, 0xff, 0x58, 0x78, 0x8e, 0xff, 0x5a, 0x73, 0x86, 0xff, 0x48, 0x5d, 0x6d, 0xff, 0x14, 0x2a, 0x3b, 0xff, 0x26, 0x3c, 0x4d, 0xff, 0x45, 0x5d, 0x70, 0xff, 0x57, 0x70, 0x85, 0xff, 0x54, 0x6c, 0x7e, 0xff, 0x44, 0x5c, 0x6c, 0xff, 0x28, 0x41, 0x51, 0xff, 0x2f, 0x4d, 0x5a, 0xff, 0x73, 0x95, 0xa1, 0xff, 0x6d, 0x94, 0x9f, 0xff, 0x7d, 0xa1, 0xa9, 0xff, 0x75, 0x8f, 0x93, 0xff, 0x91, 0xa0, 0xa2, 0xff, 0xa7, 0xab, 0xaa, 0xff, 0xa2, 0xa6, 0xa3, 0xff, 0x9b, 0xa5, 0xa2, 0xff, 0x9d, 0xa6, 0xa2, 0xff, 0xa0, 0xa5, 0xa0, 0xff, 0x9c, 0xa2, 0x9d, 0xff, 0x9c, 0xa1, 0x9c, 0xff, 0x9e, 0xa1, 0x9c, 0xff, 0x9e, 0xa0, 0x9b, 0xff, 0xa3, 0xa5, 0xa1, 0xff, 0xa5, 0xaa, 0xa4, 0xff, 0xa7, 0xab, 0xa6, 0xff, 0xae, 0xb2, 0xad, 0xff, 0xb2, 0xb6, 0xb1, 0xff, 0xb5, 0xba, 0xb5, 0xff, 0xb8, 0xbb, 0xb7, 0xfa, 0xbb, 0xbb, 0xbb, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x81, 0x9b, 0xb3, 0x42, 0x65, 0x7c, 0xff, 0x24, 0x45, 0x5c, 0xff, 0x35, 0x54, 0x6a, 0xff, 0x37, 0x55, 0x69, 0xff, 0x47, 0x62, 0x76, 0xff, 0x4a, 0x63, 0x75, 0xff, 0x52, 0x6a, 0x7b, 0xff, 0x40, 0x55, 0x65, 0xff, 0x4f, 0x66, 0x76, 0xff, 0x4d, 0x65, 0x75, 0xff, 0x6f, 0x84, 0x95, 0xff, 0x96, 0xa6, 0xb4, 0xff, 0xb3, 0xbf, 0xca, 0xff, 0xda, 0xe2, 0xe9, 0xff, 0xe6, 0xeb, 0xf2, 0xff, 0xe4, 0xea, 0xf0, 0xff, 0xdf, 0xe5, 0xea, 0xff, 0xe1, 0xe5, 0xe9, 0xff, 0xdd, 0xe2, 0xe5, 0xff, 0xd7, 0xdc, 0xdf, 0xff, 0xd7, 0xdb, 0xdf, 0xff, 0xd3, 0xd6, 0xdb, 0xff, 0xd0, 0xd5, 0xd9, 0xff, 0xd0, 0xd3, 0xdc, 0xff, 0xce, 0xd1, 0xd9, 0xff, 0xce, 0xd1, 0xd9, 0xff, 0xca, 0xcd, 0xd5, 0xff, 0xc7, 0xca, 0xd2, 0xff, 0xc5, 0xc8, 0xd0, 0xff, 0xc4, 0xc7, 0xcf, 0xff, 0xc3, 0xc7, 0xce, 0xff, 0xbf, 0xc2, 0xcb, 0xff, 0xbf, 0xc1, 0xcb, 0xff, 0xbf, 0xc2, 0xcc, 0xff, 0xbe, 0xc4, 0xcd, 0xff, 0xc1, 0xc6, 0xcf, 0xff, 0xbc, 0xc2, 0xcb, 0xff, 0xbe, 0xc4, 0xce, 0xff, 0xbf, 0xc3, 0xce, 0xff, 0xbc, 0xc0, 0xcb, 0xff, 0xba, 0xbe, 0xc9, 0xff, 0xb9, 0xbd, 0xc8, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xba, 0xbe, 0xc8, 0xff, 0xba, 0xbe, 0xc9, 0xff, 0xb4, 0xb8, 0xc3, 0xff, 0xb3, 0xb7, 0xc2, 0xff, 0xb1, 0xb4, 0xc2, 0xff, 0xae, 0xb1, 0xbf, 0xff, 0xaf, 0xb4, 0xc1, 0xff, 0xad, 0xb3, 0xc0, 0xff, 0xa8, 0xae, 0xbb, 0xff, 0xa0, 0xa7, 0xb5, 0xff, 0x9c, 0xa3, 0xb2, 0xff, 0x99, 0x9f, 0xae, 0xff, 0x92, 0x9a, 0xaa, 0xff, 0x89, 0x91, 0xa3, 0xff, 0x7f, 0x87, 0x99, 0xff, 0x74, 0x7e, 0x90, 0xff, 0x73, 0x7c, 0x8f, 0xff, 0x68, 0x73, 0x88, 0xff, 0x5b, 0x65, 0x77, 0xff, 0x53, 0x5c, 0x6c, 0xff, 0x48, 0x51, 0x5e, 0xff, 0x46, 0x4e, 0x5c, 0xff, 0x35, 0x41, 0x4f, 0xff, 0x2d, 0x37, 0x48, 0xff, 0x44, 0x5c, 0x70, 0xff, 0x5c, 0x7e, 0x94, 0xff, 0x70, 0x8f, 0xa6, 0xff, 0x7f, 0xa1, 0xb8, 0xff, 0x87, 0xab, 0xc2, 0xff, 0x73, 0x98, 0xb1, 0xff, 0x73, 0x98, 0xb1, 0xff, 0x7f, 0xa3, 0xba, 0xff, 0x48, 0x6a, 0x81, 0xff, 0x59, 0x7a, 0x91, 0xff, 0x96, 0xb5, 0xca, 0xff, 0x85, 0xa4, 0xb9, 0xff, 0x57, 0x74, 0x8a, 0xff, 0x64, 0x81, 0x97, 0xff, 0x4a, 0x66, 0x7a, 0xff, 0x0d, 0x26, 0x38, 0xff, 0x28, 0x3e, 0x4f, 0xff, 0x45, 0x5a, 0x69, 0xff, 0x50, 0x66, 0x75, 0xff, 0x4b, 0x64, 0x74, 0xff, 0x28, 0x43, 0x52, 0xff, 0x2e, 0x4d, 0x5c, 0xff, 0x5a, 0x7f, 0x8c, 0xff, 0x66, 0x8e, 0x9c, 0xff, 0x74, 0x9a, 0xa5, 0xff, 0x81, 0x9f, 0xa5, 0xff, 0x87, 0x9a, 0x9e, 0xff, 0xa1, 0xa9, 0xa9, 0xff, 0x9e, 0xa5, 0xa2, 0xff, 0x9a, 0xa3, 0xa0, 0xff, 0x9b, 0xa4, 0xa0, 0xff, 0x9c, 0xa2, 0x9d, 0xff, 0x9a, 0xa0, 0x9b, 0xff, 0x9a, 0xa0, 0x9b, 0xff, 0x9b, 0x9f, 0x9a, 0xff, 0x9f, 0xa3, 0x9e, 0xff, 0xa2, 0xa6, 0xa1, 0xff, 0xa4, 0xa8, 0xa3, 0xff, 0xa8, 0xac, 0xa7, 0xff, 0xae, 0xb2, 0xad, 0xff, 0xb1, 0xb5, 0xb0, 0xff, 0xb3, 0xb8, 0xb2, 0xff, 0xb7, 0xba, 0xb6, 0xff, 0xbb, 0xbb, 0xb9, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4b, 0x70, 0x8a, 0x44, 0x50, 0x75, 0x8f, 0xff, 0x38, 0x5b, 0x73, 0xff, 0x26, 0x46, 0x5c, 0xff, 0x33, 0x52, 0x67, 0xff, 0x43, 0x60, 0x75, 0xff, 0x59, 0x74, 0x86, 0xff, 0x5e, 0x76, 0x87, 0xff, 0x2b, 0x42, 0x52, 0xff, 0x2b, 0x41, 0x50, 0xff, 0x3f, 0x56, 0x66, 0xff, 0x62, 0x7a, 0x8a, 0xff, 0x7e, 0x94, 0xa4, 0xff, 0x90, 0xa0, 0xad, 0xff, 0xbb, 0xc9, 0xd3, 0xff, 0xdb, 0xe3, 0xea, 0xff, 0xe5, 0xea, 0xf1, 0xff, 0xe3, 0xea, 0xf0, 0xff, 0xde, 0xe3, 0xe8, 0xff, 0xdf, 0xe3, 0xe8, 0xff, 0xdc, 0xe2, 0xe4, 0xff, 0xd7, 0xdc, 0xdf, 0xff, 0xd5, 0xda, 0xdd, 0xff, 0xd2, 0xd6, 0xdb, 0xff, 0xd2, 0xd6, 0xdb, 0xff, 0xd1, 0xd4, 0xdc, 0xff, 0xcc, 0xcf, 0xd7, 0xff, 0xca, 0xcd, 0xd5, 0xff, 0xc9, 0xcc, 0xd4, 0xff, 0xc7, 0xca, 0xd2, 0xff, 0xc8, 0xcb, 0xd3, 0xff, 0xc6, 0xc9, 0xd1, 0xff, 0xc5, 0xc9, 0xd0, 0xff, 0xc2, 0xc5, 0xce, 0xff, 0xc1, 0xc3, 0xcd, 0xff, 0xbf, 0xc2, 0xcc, 0xff, 0xbc, 0xc2, 0xcb, 0xff, 0xbe, 0xc3, 0xcc, 0xff, 0xb8, 0xbe, 0xc7, 0xff, 0xb9, 0xbf, 0xc9, 0xff, 0xbb, 0xbf, 0xca, 0xff, 0xba, 0xbe, 0xc9, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xb6, 0xba, 0xc5, 0xff, 0xb7, 0xbb, 0xc6, 0xff, 0xb8, 0xbd, 0xc7, 0xff, 0xb9, 0xbe, 0xc8, 0xff, 0xb6, 0xba, 0xc5, 0xff, 0xb0, 0xb4, 0xbf, 0xff, 0xad, 0xb0, 0xbe, 0xff, 0xb0, 0xb3, 0xc1, 0xff, 0xaf, 0xb4, 0xc2, 0xff, 0xae, 0xb4, 0xc1, 0xff, 0xaa, 0xb0, 0xbd, 0xff, 0xa0, 0xa7, 0xb5, 0xff, 0x9b, 0xa2, 0xb1, 0xff, 0x97, 0x9e, 0xad, 0xff, 0x94, 0x9c, 0xac, 0xff, 0x8a, 0x94, 0xa5, 0xff, 0x80, 0x89, 0x9b, 0xff, 0x75, 0x7f, 0x91, 0xff, 0x6b, 0x77, 0x89, 0xff, 0x68, 0x73, 0x87, 0xff, 0x61, 0x6d, 0x81, 0xff, 0x54, 0x60, 0x70, 0xff, 0x4f, 0x57, 0x66, 0xff, 0x40, 0x47, 0x55, 0xff, 0x3a, 0x42, 0x50, 0xff, 0x35, 0x3a, 0x4a, 0xff, 0x35, 0x47, 0x5c, 0xff, 0x42, 0x61, 0x76, 0xff, 0x53, 0x71, 0x86, 0xff, 0x6c, 0x8b, 0xa2, 0xff, 0x7b, 0x9d, 0xb4, 0xff, 0x92, 0xb7, 0xcc, 0xff, 0x7d, 0xa3, 0xba, 0xff, 0x86, 0xab, 0xc2, 0xff, 0x80, 0xa4, 0xbb, 0xff, 0x3b, 0x5c, 0x73, 0xff, 0x64, 0x84, 0x99, 0xff, 0xaa, 0xc9, 0xdd, 0xff, 0x70, 0x91, 0xa8, 0xff, 0x4d, 0x70, 0x87, 0xff, 0x68, 0x87, 0x9c, 0xff, 0x43, 0x5e, 0x71, 0xff, 0x1c, 0x32, 0x42, 0xff, 0x26, 0x38, 0x46, 0xff, 0x36, 0x4a, 0x59, 0xff, 0x50, 0x67, 0x78, 0xff, 0x35, 0x4f, 0x5f, 0xff, 0x27, 0x45, 0x55, 0xff, 0x4b, 0x6e, 0x7d, 0xff, 0x60, 0x88, 0x97, 0xff, 0x64, 0x8b, 0x99, 0xff, 0x7c, 0x9f, 0xa8, 0xff, 0x86, 0x9f, 0xa4, 0xff, 0x9a, 0xa8, 0xa9, 0xff, 0x9a, 0xa4, 0xa2, 0xff, 0x98, 0xa1, 0x9e, 0xff, 0x99, 0xa1, 0x9d, 0xff, 0x96, 0x9d, 0x98, 0xff, 0x98, 0x9e, 0x99, 0xff, 0x99, 0xa0, 0x9b, 0xff, 0x98, 0x9e, 0x99, 0xff, 0xa0, 0xa4, 0x9f, 0xff, 0xa2, 0xa6, 0xa1, 0xff, 0xa4, 0xa8, 0xa3, 0xff, 0xa8, 0xac, 0xa7, 0xff, 0xac, 0xb0, 0xab, 0xff, 0xb0, 0xb4, 0xaf, 0xff, 0xb3, 0xb8, 0xb2, 0xff, 0xb6, 0xb9, 0xb5, 0xff, 0xba, 0xbb, 0xb9, 0xff, 0xbb, 0xbb, 0xbb, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4f, 0x75, 0x8e, 0xc8, 0x4a, 0x6f, 0x8a, 0xff, 0x2d, 0x4f, 0x69, 0xff, 0x32, 0x51, 0x67, 0xff, 0x44, 0x63, 0x77, 0xff, 0x57, 0x74, 0x86, 0xff, 0x55, 0x6f, 0x80, 0xff, 0x35, 0x4d, 0x5d, 0xff, 0x2f, 0x45, 0x55, 0xff, 0x43, 0x59, 0x67, 0xff, 0x64, 0x7b, 0x8b, 0xff, 0x73, 0x8b, 0x9b, 0xff, 0x73, 0x89, 0x99, 0xff, 0x9e, 0xae, 0xbb, 0xff, 0xbc, 0xca, 0xd4, 0xff, 0xd8, 0xe0, 0xe8, 0xff, 0xe4, 0xea, 0xf1, 0xff, 0xe2, 0xe8, 0xee, 0xff, 0xde, 0xe3, 0xe8, 0xff, 0xd9, 0xdd, 0xe3, 0xff, 0xd9, 0xde, 0xe1, 0xff, 0xd9, 0xde, 0xe0, 0xff, 0xd5, 0xda, 0xdd, 0xff, 0xd0, 0xd4, 0xd9, 0xff, 0xcd, 0xd1, 0xd6, 0xff, 0xce, 0xd1, 0xd9, 0xff, 0xd0, 0xd2, 0xdb, 0xff, 0xce, 0xd1, 0xd9, 0xff, 0xc7, 0xca, 0xd2, 0xff, 0xc2, 0xc4, 0xcc, 0xff, 0xc5, 0xc8, 0xd0, 0xff, 0xc6, 0xc9, 0xd1, 0xff, 0xc4, 0xc7, 0xcf, 0xff, 0xc4, 0xc6, 0xcf, 0xff, 0xc4, 0xc5, 0xd0, 0xff, 0xc1, 0xc5, 0xcf, 0xff, 0xbf, 0xc5, 0xce, 0xff, 0xc1, 0xc5, 0xce, 0xff, 0xbd, 0xc4, 0xcc, 0xff, 0xb8, 0xbe, 0xc8, 0xff, 0xba, 0xbd, 0xc8, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xb7, 0xbb, 0xc6, 0xff, 0xb7, 0xba, 0xc5, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xb7, 0xbc, 0xc6, 0xff, 0xb7, 0xbb, 0xc5, 0xff, 0xb5, 0xb9, 0xc4, 0xff, 0xaf, 0xb3, 0xbe, 0xff, 0xad, 0xb0, 0xbe, 0xff, 0xaf, 0xb2, 0xc1, 0xff, 0xae, 0xb3, 0xc0, 0xff, 0xad, 0xb4, 0xc1, 0xff, 0xac, 0xb2, 0xbe, 0xff, 0xa5, 0xab, 0xba, 0xff, 0x9d, 0xa4, 0xb4, 0xff, 0x97, 0x9d, 0xac, 0xff, 0x90, 0x99, 0xa9, 0xff, 0x88, 0x93, 0xa4, 0xff, 0x81, 0x8c, 0x9e, 0xff, 0x74, 0x80, 0x92, 0xff, 0x6a, 0x77, 0x8a, 0xff, 0x62, 0x6e, 0x82, 0xff, 0x5e, 0x6c, 0x80, 0xff, 0x59, 0x66, 0x78, 0xff, 0x53, 0x5c, 0x6c, 0xff, 0x41, 0x49, 0x56, 0xff, 0x36, 0x3f, 0x4d, 0xff, 0x34, 0x3d, 0x4d, 0xff, 0x38, 0x4a, 0x5d, 0xff, 0x40, 0x59, 0x70, 0xff, 0x35, 0x4f, 0x64, 0xff, 0x48, 0x67, 0x7b, 0xff, 0x64, 0x84, 0x9b, 0xff, 0x78, 0x9c, 0xb2, 0xff, 0x8a, 0xb1, 0xc7, 0xff, 0x8a, 0xb1, 0xc8, 0xff, 0x8b, 0xb0, 0xc6, 0xff, 0x67, 0x8a, 0xa0, 0xff, 0x27, 0x48, 0x5d, 0xff, 0x81, 0xa1, 0xb5, 0xff, 0x95, 0xb6, 0xcc, 0xff, 0x4e, 0x73, 0x88, 0xff, 0x6b, 0x8c, 0xa0, 0xff, 0x6a, 0x85, 0x97, 0xff, 0x33, 0x4c, 0x5c, 0xff, 0x1f, 0x35, 0x43, 0xff, 0x14, 0x22, 0x32, 0xff, 0x34, 0x44, 0x56, 0xff, 0x55, 0x6c, 0x7c, 0xff, 0x46, 0x63, 0x72, 0xff, 0x2f, 0x50, 0x60, 0xff, 0x58, 0x7d, 0x8d, 0xff, 0x61, 0x89, 0x99, 0xff, 0x6c, 0x94, 0xa0, 0xff, 0x81, 0xa0, 0xa7, 0xff, 0x8f, 0xa4, 0xa5, 0xff, 0x9b, 0xa7, 0xa5, 0xff, 0x98, 0x9f, 0x9e, 0xff, 0x99, 0x9f, 0x9b, 0xff, 0x95, 0x9c, 0x97, 0xff, 0x94, 0x9b, 0x96, 0xff, 0x96, 0x9d, 0x98, 0xff, 0x97, 0x9e, 0x99, 0xff, 0x9c, 0xa2, 0x9c, 0xff, 0xa1, 0xa5, 0xa0, 0xff, 0xa3, 0xa7, 0xa2, 0xff, 0xa8, 0xab, 0xa6, 0xff, 0xac, 0xb0, 0xab, 0xff, 0xaf, 0xb3, 0xae, 0xff, 0xb2, 0xb7, 0xb1, 0xff, 0xb6, 0xb9, 0xb5, 0xff, 0xba, 0xba, 0xb9, 0xff, 0xbb, 0xbd, 0xba, 0xc9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x56, 0x7d, 0x91, 0x4d, 0x49, 0x6f, 0x8c, 0xff, 0x3c, 0x61, 0x7d, 0xff, 0x22, 0x44, 0x5d, 0xff, 0x56, 0x74, 0x89, 0xff, 0x5a, 0x76, 0x8a, 0xff, 0x43, 0x5f, 0x70, 0xff, 0x33, 0x4d, 0x5d, 0xff, 0x44, 0x5c, 0x6c, 0xff, 0x4f, 0x64, 0x74, 0xff, 0x6c, 0x81, 0x90, 0xff, 0x6c, 0x83, 0x93, 0xff, 0x4b, 0x63, 0x73, 0xff, 0x6a, 0x80, 0x90, 0xff, 0x98, 0xa9, 0xb6, 0xff, 0xbc, 0xc9, 0xd4, 0xff, 0xdb, 0xe3, 0xeb, 0xff, 0xe3, 0xe8, 0xef, 0xff, 0xe1, 0xe7, 0xed, 0xff, 0xdc, 0xe2, 0xe7, 0xff, 0xd9, 0xde, 0xe2, 0xff, 0xda, 0xdf, 0xe2, 0xff, 0xd8, 0xde, 0xe0, 0xff, 0xd7, 0xdb, 0xdf, 0xff, 0xd4, 0xd8, 0xdd, 0xff, 0xcd, 0xd1, 0xd6, 0xff, 0xc9, 0xcc, 0xd4, 0xff, 0xcb, 0xce, 0xd6, 0xff, 0xcd, 0xd0, 0xd8, 0xff, 0xca, 0xcd, 0xd5, 0xff, 0xc8, 0xca, 0xd2, 0xff, 0xc6, 0xc9, 0xd1, 0xff, 0xc5, 0xc8, 0xd0, 0xff, 0xc6, 0xc8, 0xd0, 0xff, 0xc5, 0xc7, 0xd0, 0xff, 0xc3, 0xc4, 0xcf, 0xff, 0xc1, 0xc4, 0xce, 0xff, 0xc2, 0xc7, 0xd0, 0xff, 0xbf, 0xc3, 0xcc, 0xff, 0xbd, 0xc4, 0xcc, 0xff, 0xbd, 0xc2, 0xcc, 0xff, 0xbb, 0xbe, 0xc9, 0xff, 0xb7, 0xbb, 0xc6, 0xff, 0xb6, 0xba, 0xc5, 0xff, 0xb8, 0xbb, 0xc6, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xb7, 0xbb, 0xc5, 0xff, 0xb6, 0xba, 0xc5, 0xff, 0xb6, 0xba, 0xc5, 0xff, 0xb4, 0xb8, 0xc3, 0xff, 0xb2, 0xb5, 0xc3, 0xff, 0xb3, 0xb5, 0xc4, 0xff, 0xb0, 0xb4, 0xc2, 0xff, 0xad, 0xb3, 0xc0, 0xff, 0xad, 0xb3, 0xc0, 0xff, 0xaa, 0xb1, 0xbf, 0xff, 0xa1, 0xa8, 0xb7, 0xff, 0x99, 0xa0, 0xae, 0xff, 0x90, 0x99, 0xa9, 0xff, 0x89, 0x93, 0xa5, 0xff, 0x82, 0x8e, 0xa0, 0xff, 0x77, 0x83, 0x95, 0xff, 0x6f, 0x7b, 0x8d, 0xff, 0x64, 0x73, 0x86, 0xff, 0x61, 0x70, 0x83, 0xff, 0x5f, 0x6b, 0x7e, 0xff, 0x55, 0x60, 0x6f, 0xff, 0x43, 0x4e, 0x5a, 0xff, 0x32, 0x3f, 0x4b, 0xff, 0x30, 0x40, 0x4c, 0xff, 0x2c, 0x3e, 0x51, 0xff, 0x42, 0x58, 0x6e, 0xff, 0x35, 0x4d, 0x62, 0xff, 0x2d, 0x4a, 0x5e, 0xff, 0x3c, 0x5c, 0x72, 0xff, 0x5a, 0x7d, 0x94, 0xff, 0x76, 0x9b, 0xb3, 0xff, 0x88, 0xaf, 0xc6, 0xff, 0x90, 0xb5, 0xcb, 0xff, 0x9a, 0xbe, 0xd4, 0xff, 0x49, 0x6b, 0x7f, 0xff, 0x24, 0x44, 0x58, 0xff, 0x95, 0xb7, 0xca, 0xff, 0x87, 0xab, 0xbd, 0xff, 0x5e, 0x7f, 0x92, 0xff, 0x72, 0x8f, 0xa0, 0xff, 0x5d, 0x77, 0x88, 0xff, 0x36, 0x52, 0x63, 0xff, 0x09, 0x19, 0x2a, 0xff, 0x16, 0x21, 0x33, 0xff, 0x3f, 0x53, 0x65, 0xff, 0x58, 0x72, 0x83, 0xff, 0x3d, 0x5b, 0x6d, 0xff, 0x37, 0x58, 0x6c, 0xff, 0x60, 0x88, 0x99, 0xff, 0x5f, 0x8a, 0x98, 0xff, 0x74, 0x96, 0x9e, 0xff, 0x87, 0x9f, 0xa0, 0xff, 0x9c, 0xab, 0xa8, 0xff, 0x9a, 0x9f, 0x9e, 0xff, 0x94, 0x9a, 0x98, 0xff, 0x93, 0x9c, 0x96, 0xff, 0x90, 0x97, 0x92, 0xff, 0x93, 0x9a, 0x95, 0xff, 0x98, 0x9e, 0x99, 0xff, 0x9a, 0xa0, 0x9b, 0xff, 0x9c, 0xa2, 0x9d, 0xff, 0x9f, 0xa2, 0x9d, 0xff, 0xa5, 0xa9, 0xa4, 0xff, 0xab, 0xb0, 0xaa, 0xff, 0xae, 0xb2, 0xad, 0xff, 0xb0, 0xb4, 0xaf, 0xff, 0xb5, 0xb8, 0xb4, 0xff, 0xb9, 0xba, 0xb8, 0xff, 0xbb, 0xbc, 0xba, 0xff, 0xb9, 0xbc, 0xb9, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x77, 0x8c, 0xcd, 0x40, 0x65, 0x81, 0xff, 0x2c, 0x52, 0x6c, 0xff, 0x3b, 0x5e, 0x76, 0xff, 0x67, 0x88, 0x9d, 0xff, 0x4d, 0x6c, 0x7e, 0xff, 0x32, 0x4e, 0x61, 0xff, 0x3f, 0x58, 0x69, 0xff, 0x3d, 0x55, 0x65, 0xff, 0x4b, 0x64, 0x73, 0xff, 0x76, 0x8d, 0x9f, 0xff, 0x40, 0x55, 0x67, 0xff, 0x2e, 0x41, 0x4f, 0xff, 0x72, 0x81, 0x8e, 0xff, 0x99, 0xa7, 0xb4, 0xff, 0xbf, 0xcd, 0xd9, 0xff, 0xd9, 0xe3, 0xe8, 0xff, 0xe1, 0xe8, 0xec, 0xff, 0xdf, 0xe5, 0xea, 0xff, 0xdd, 0xe1, 0xe6, 0xff, 0xdc, 0xdf, 0xe4, 0xff, 0xda, 0xde, 0xe2, 0xff, 0xda, 0xdd, 0xe1, 0xff, 0xd7, 0xdb, 0xdf, 0xff, 0xd4, 0xd9, 0xdd, 0xff, 0xd0, 0xd3, 0xdb, 0xff, 0xcd, 0xd0, 0xda, 0xff, 0xcb, 0xcd, 0xd7, 0xff, 0xc9, 0xce, 0xd6, 0xff, 0xca, 0xd0, 0xd7, 0xff, 0xc9, 0xce, 0xd5, 0xff, 0xc6, 0xcc, 0xd3, 0xff, 0xc6, 0xcb, 0xd2, 0xff, 0xc5, 0xca, 0xd1, 0xff, 0xc1, 0xc7, 0xce, 0xff, 0xc0, 0xc5, 0xcd, 0xff, 0xbe, 0xc2, 0xcb, 0xff, 0xbe, 0xc4, 0xcd, 0xff, 0xbc, 0xc3, 0xcd, 0xff, 0xba, 0xc0, 0xca, 0xff, 0xb9, 0xbf, 0xca, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xb9, 0xbe, 0xc9, 0xff, 0xb9, 0xbe, 0xc9, 0xff, 0xb7, 0xbd, 0xc8, 0xff, 0xb6, 0xbc, 0xc7, 0xff, 0xb7, 0xbb, 0xc6, 0xff, 0xb9, 0xbc, 0xc8, 0xff, 0xb6, 0xba, 0xc5, 0xff, 0xb5, 0xb9, 0xc4, 0xff, 0xb6, 0xba, 0xc5, 0xff, 0xb5, 0xb9, 0xc4, 0xff, 0xb7, 0xb8, 0xc6, 0xff, 0xb4, 0xb5, 0xc3, 0xff, 0xae, 0xb2, 0xc0, 0xff, 0xae, 0xb5, 0xc3, 0xff, 0xa3, 0xac, 0xbb, 0xff, 0x9d, 0xa9, 0xb6, 0xff, 0x9b, 0xa3, 0xb3, 0xff, 0x94, 0x9c, 0xac, 0xff, 0x8b, 0x93, 0xa5, 0xff, 0x81, 0x8a, 0x9d, 0xff, 0x74, 0x80, 0x92, 0xff, 0x6c, 0x79, 0x8d, 0xff, 0x6b, 0x78, 0x8a, 0xff, 0x65, 0x71, 0x81, 0xff, 0x5b, 0x67, 0x77, 0xff, 0x50, 0x5d, 0x6c, 0xff, 0x3b, 0x49, 0x58, 0xff, 0x37, 0x44, 0x53, 0xff, 0x2c, 0x3a, 0x4a, 0xff, 0x43, 0x55, 0x66, 0xff, 0x40, 0x57, 0x6b, 0xff, 0x2a, 0x45, 0x5c, 0xff, 0x2a, 0x47, 0x5d, 0xff, 0x38, 0x51, 0x67, 0xff, 0x5d, 0x7a, 0x91, 0xff, 0x7b, 0xa1, 0xb8, 0xff, 0x8f, 0xb7, 0xce, 0xff, 0xa0, 0xc8, 0xe0, 0xff, 0x97, 0xb9, 0xcf, 0xff, 0x21, 0x39, 0x4b, 0xff, 0x48, 0x65, 0x78, 0xff, 0xa1, 0xc5, 0xda, 0xff, 0x7a, 0x9d, 0xb0, 0xff, 0x6a, 0x8a, 0x9e, 0xff, 0x62, 0x80, 0x95, 0xff, 0x52, 0x74, 0x8a, 0xff, 0x21, 0x36, 0x45, 0xff, 0x06, 0x0c, 0x13, 0xff, 0x1d, 0x2a, 0x35, 0xff, 0x36, 0x47, 0x56, 0xff, 0x5e, 0x7a, 0x8b, 0xff, 0x34, 0x56, 0x6a, 0xff, 0x57, 0x7e, 0x91, 0xff, 0x61, 0x89, 0x9a, 0xff, 0x62, 0x89, 0x97, 0xff, 0x7b, 0x9c, 0xa4, 0xff, 0x91, 0xa5, 0xa8, 0xff, 0x9b, 0xa1, 0x9e, 0xff, 0x94, 0x9a, 0x98, 0xff, 0x8f, 0x99, 0x98, 0xff, 0x90, 0x97, 0x94, 0xff, 0x93, 0x98, 0x93, 0xff, 0x98, 0x9c, 0x96, 0xff, 0x98, 0x9d, 0x9a, 0xff, 0x9a, 0x9f, 0x9b, 0xff, 0x9d, 0xa1, 0x9c, 0xff, 0xa4, 0xa9, 0xa3, 0xff, 0xab, 0xae, 0xa9, 0xff, 0xb0, 0xb0, 0xac, 0xff, 0xb2, 0xb3, 0xaf, 0xff, 0xb4, 0xb7, 0xb2, 0xff, 0xb6, 0xb9, 0xb5, 0xff, 0xb9, 0xbc, 0xb8, 0xff, 0xb9, 0xbb, 0xb8, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x97, 0xb3, 0xc3, 0x40, 0x4d, 0x71, 0x88, 0xff, 0x3d, 0x62, 0x7d, 0xff, 0x3a, 0x5f, 0x78, 0xff, 0x69, 0x8c, 0xa2, 0xff, 0x5a, 0x7b, 0x90, 0xff, 0x3e, 0x5c, 0x70, 0xff, 0x34, 0x51, 0x64, 0xff, 0x3b, 0x54, 0x66, 0xff, 0x54, 0x6d, 0x7d, 0xff, 0x78, 0x92, 0xa3, 0xff, 0x40, 0x54, 0x65, 0xff, 0x17, 0x29, 0x3a, 0xff, 0x39, 0x49, 0x57, 0xff, 0x6f, 0x7b, 0x88, 0xff, 0x97, 0xa4, 0xb0, 0xff, 0xc3, 0xcf, 0xdc, 0xff, 0xd4, 0xe0, 0xe4, 0xff, 0xdd, 0xe5, 0xe8, 0xff, 0xdc, 0xe2, 0xe7, 0xff, 0xdc, 0xe0, 0xe5, 0xff, 0xdb, 0xdf, 0xe4, 0xff, 0xda, 0xdd, 0xe2, 0xff, 0xdb, 0xdd, 0xe1, 0xff, 0xd8, 0xdb, 0xdf, 0xff, 0xd5, 0xd8, 0xdd, 0xff, 0xd0, 0xd3, 0xda, 0xff, 0xd0, 0xd2, 0xda, 0xff, 0xce, 0xcf, 0xd9, 0xff, 0xca, 0xcf, 0xd7, 0xff, 0xca, 0xd1, 0xd8, 0xff, 0xc6, 0xcd, 0xd3, 0xff, 0xc5, 0xcb, 0xd2, 0xff, 0xc6, 0xcd, 0xd4, 0xff, 0xc4, 0xca, 0xd1, 0xff, 0xc2, 0xc8, 0xcf, 0xff, 0xc4, 0xca, 0xd1, 0xff, 0xbd, 0xc2, 0xcb, 0xff, 0xb5, 0xbb, 0xc5, 0xff, 0xb9, 0xc0, 0xca, 0xff, 0xb9, 0xbf, 0xcb, 0xff, 0xbb, 0xc1, 0xcc, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xb9, 0xbf, 0xca, 0xff, 0xb6, 0xbc, 0xc7, 0xff, 0xb5, 0xbc, 0xc7, 0xff, 0xba, 0xbe, 0xc9, 0xff, 0xba, 0xbe, 0xc9, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xb5, 0xb9, 0xc4, 0xff, 0xb3, 0xb8, 0xc2, 0xff, 0xb4, 0xb9, 0xc3, 0xff, 0xba, 0xbd, 0xc8, 0xff, 0xb8, 0xba, 0xc4, 0xff, 0xb1, 0xb4, 0xc0, 0xff, 0xb1, 0xb5, 0xc2, 0xff, 0xad, 0xb4, 0xc1, 0xff, 0xa9, 0xaf, 0xbe, 0xff, 0xa3, 0xa9, 0xb8, 0xff, 0x9a, 0xa1, 0xb0, 0xff, 0x94, 0x99, 0xab, 0xff, 0x88, 0x90, 0xa1, 0xff, 0x7c, 0x86, 0x98, 0xff, 0x73, 0x7e, 0x91, 0xff, 0x71, 0x7d, 0x8d, 0xff, 0x6d, 0x7b, 0x8a, 0xff, 0x63, 0x71, 0x81, 0xff, 0x5b, 0x68, 0x79, 0xff, 0x46, 0x52, 0x63, 0xff, 0x3d, 0x49, 0x5a, 0xff, 0x36, 0x44, 0x54, 0xff, 0x41, 0x53, 0x62, 0xff, 0x4a, 0x5f, 0x74, 0xff, 0x2f, 0x49, 0x5f, 0xff, 0x2a, 0x45, 0x5b, 0xff, 0x33, 0x4b, 0x60, 0xff, 0x3e, 0x58, 0x6d, 0xff, 0x5c, 0x7b, 0x93, 0xff, 0x7e, 0xa2, 0xbb, 0xff, 0x90, 0xb7, 0xcf, 0xff, 0xb6, 0xd8, 0xef, 0xff, 0x74, 0x8f, 0xa2, 0xff, 0x15, 0x33, 0x46, 0xff, 0x78, 0x9d, 0xb1, 0xff, 0x98, 0xba, 0xce, 0xff, 0x73, 0x95, 0xa9, 0xff, 0x61, 0x81, 0x96, 0xff, 0x67, 0x89, 0xa1, 0xff, 0x48, 0x60, 0x6f, 0xff, 0x00, 0x04, 0x0a, 0xff, 0x03, 0x0b, 0x16, 0xff, 0x14, 0x21, 0x2d, 0xff, 0x41, 0x58, 0x68, 0xff, 0x5e, 0x7f, 0x93, 0xff, 0x45, 0x6a, 0x7e, 0xff, 0x67, 0x8d, 0x9f, 0xff, 0x61, 0x8a, 0x9a, 0xff, 0x68, 0x8f, 0x9a, 0xff, 0x8d, 0xa7, 0xab, 0xff, 0x9d, 0xa5, 0xa3, 0xff, 0x95, 0x9a, 0x9a, 0xff, 0x8e, 0x98, 0x98, 0xff, 0x92, 0x99, 0x96, 0xff, 0x93, 0x98, 0x92, 0xff, 0x97, 0x9b, 0x95, 0xff, 0x96, 0x9b, 0x98, 0xff, 0x9b, 0x9f, 0x9c, 0xff, 0x9f, 0xa2, 0x9d, 0xff, 0xa4, 0xa8, 0xa3, 0xff, 0xab, 0xad, 0xa9, 0xff, 0xaf, 0xaf, 0xac, 0xff, 0xb2, 0xb2, 0xae, 0xff, 0xb4, 0xb5, 0xb1, 0xff, 0xb4, 0xb9, 0xb3, 0xff, 0xb7, 0xbb, 0xb6, 0xff, 0xb8, 0xbc, 0xb7, 0xff, 0xb7, 0xbf, 0xb7, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8b, 0xac, 0xbd, 0xb3, 0x40, 0x65, 0x7d, 0xff, 0x3b, 0x62, 0x7c, 0xff, 0x5e, 0x84, 0x9c, 0xff, 0x64, 0x88, 0x9c, 0xff, 0x47, 0x66, 0x7b, 0xff, 0x4a, 0x68, 0x7d, 0xff, 0x32, 0x52, 0x65, 0xff, 0x44, 0x5f, 0x71, 0xff, 0x7d, 0x96, 0xa6, 0xff, 0x46, 0x5e, 0x6e, 0xff, 0x1d, 0x2a, 0x38, 0xff, 0x0d, 0x1b, 0x27, 0xff, 0x2e, 0x3e, 0x4b, 0xff, 0x69, 0x77, 0x85, 0xff, 0x99, 0xa6, 0xb3, 0xff, 0xc7, 0xd2, 0xdf, 0xff, 0xd9, 0xe0, 0xe8, 0xff, 0xdf, 0xe5, 0xea, 0xff, 0xd9, 0xdf, 0xe5, 0xff, 0xd9, 0xdf, 0xe5, 0xff, 0xd9, 0xde, 0xe4, 0xff, 0xd9, 0xde, 0xe4, 0xff, 0xd8, 0xdc, 0xe1, 0xff, 0xd5, 0xd9, 0xdc, 0xff, 0xd4, 0xd7, 0xdb, 0xff, 0xd1, 0xd5, 0xd9, 0xff, 0xd2, 0xd4, 0xd9, 0xff, 0xd0, 0xd2, 0xd8, 0xff, 0xcd, 0xd2, 0xd8, 0xff, 0xca, 0xd0, 0xd8, 0xff, 0xc8, 0xce, 0xd5, 0xff, 0xc7, 0xcd, 0xd4, 0xff, 0xc7, 0xcd, 0xd4, 0xff, 0xc4, 0xca, 0xd1, 0xff, 0xc6, 0xcc, 0xd3, 0xff, 0xc4, 0xc9, 0xd1, 0xff, 0xc1, 0xc6, 0xcf, 0xff, 0xbb, 0xc2, 0xca, 0xff, 0xb7, 0xbe, 0xc8, 0xff, 0xb9, 0xbf, 0xca, 0xff, 0xbb, 0xc1, 0xcc, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xb9, 0xbf, 0xca, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xb9, 0xbf, 0xca, 0xff, 0xbc, 0xc0, 0xcb, 0xff, 0xbb, 0xbf, 0xca, 0xff, 0xb9, 0xbd, 0xc8, 0xff, 0xb7, 0xbb, 0xc6, 0xff, 0xb4, 0xb8, 0xc3, 0xff, 0xb7, 0xbb, 0xc6, 0xff, 0xb7, 0xbe, 0xc6, 0xff, 0xb5, 0xbc, 0xc3, 0xff, 0xb7, 0xbc, 0xc5, 0xff, 0xb4, 0xb7, 0xc1, 0xff, 0xb5, 0xb6, 0xc1, 0xff, 0xb2, 0xb3, 0xbf, 0xff, 0xab, 0xae, 0xba, 0xff, 0xa1, 0xa6, 0xb5, 0xff, 0x9a, 0xa0, 0xae, 0xff, 0x91, 0x99, 0xa7, 0xff, 0x88, 0x91, 0xa0, 0xff, 0x80, 0x89, 0x98, 0xff, 0x76, 0x82, 0x91, 0xff, 0x73, 0x81, 0x92, 0xff, 0x6d, 0x7a, 0x8a, 0xff, 0x63, 0x70, 0x80, 0xff, 0x53, 0x60, 0x70, 0xff, 0x42, 0x4e, 0x5e, 0xff, 0x42, 0x50, 0x61, 0xff, 0x44, 0x56, 0x66, 0xff, 0x53, 0x69, 0x7d, 0xff, 0x3a, 0x55, 0x6a, 0xff, 0x38, 0x54, 0x69, 0xff, 0x2c, 0x47, 0x5b, 0xff, 0x3f, 0x56, 0x6b, 0xff, 0x40, 0x57, 0x70, 0xff, 0x5b, 0x79, 0x92, 0xff, 0x76, 0x9a, 0xb2, 0xff, 0x98, 0xbd, 0xd3, 0xff, 0xbc, 0xdf, 0xf2, 0xff, 0x4d, 0x6f, 0x82, 0xff, 0x2f, 0x50, 0x65, 0xff, 0x93, 0xb4, 0xc8, 0xff, 0x85, 0xa6, 0xba, 0xff, 0x70, 0x91, 0xa5, 0xff, 0x63, 0x85, 0x9b, 0xff, 0x64, 0x7f, 0x91, 0xff, 0x19, 0x2a, 0x35, 0xff, 0x07, 0x10, 0x1b, 0xff, 0x09, 0x15, 0x20, 0xff, 0x06, 0x19, 0x28, 0xff, 0x49, 0x66, 0x78, 0xff, 0x66, 0x87, 0x9a, 0xff, 0x50, 0x74, 0x87, 0xff, 0x62, 0x8a, 0x99, 0xff, 0x68, 0x8e, 0x9a, 0xff, 0x88, 0xa4, 0xaa, 0xff, 0x9b, 0xa9, 0xab, 0xff, 0x93, 0x9c, 0x9c, 0xff, 0x90, 0x99, 0x98, 0xff, 0x94, 0x9b, 0x98, 0xff, 0x94, 0x99, 0x93, 0xff, 0x99, 0x9d, 0x97, 0xff, 0x98, 0x9e, 0x9b, 0xff, 0x9c, 0xa0, 0x9d, 0xff, 0x9f, 0xa3, 0x9d, 0xff, 0xa3, 0xa8, 0xa3, 0xff, 0xaa, 0xac, 0xa8, 0xff, 0xaf, 0xaf, 0xab, 0xff, 0xb1, 0xb2, 0xae, 0xff, 0xb3, 0xb5, 0xb1, 0xff, 0xb4, 0xb8, 0xb3, 0xff, 0xb7, 0xbb, 0xb6, 0xff, 0xb7, 0xbb, 0xb6, 0xff, 0xb9, 0xbd, 0xb7, 0xb3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbb, 0xd9, 0xe1, 0x22, 0x73, 0x95, 0xa7, 0xfe, 0x3d, 0x65, 0x7c, 0xff, 0x47, 0x6e, 0x88, 0xff, 0x68, 0x8e, 0xa5, 0xff, 0x5c, 0x80, 0x93, 0xff, 0x51, 0x6f, 0x84, 0xff, 0x3e, 0x5e, 0x72, 0xff, 0x21, 0x41, 0x55, 0xff, 0x5c, 0x7b, 0x8c, 0xff, 0x53, 0x6a, 0x7a, 0xff, 0x30, 0x42, 0x54, 0xff, 0x1a, 0x24, 0x2c, 0xff, 0x00, 0x03, 0x09, 0xff, 0x2c, 0x38, 0x45, 0xff, 0x61, 0x6f, 0x7f, 0xff, 0xa0, 0xac, 0xbb, 0xff, 0xc8, 0xd1, 0xdd, 0xff, 0xd9, 0xdf, 0xe7, 0xff, 0xe0, 0xe5, 0xec, 0xff, 0xd8, 0xde, 0xe5, 0xff, 0xd8, 0xdf, 0xe6, 0xff, 0xd6, 0xde, 0xe5, 0xff, 0xd4, 0xdc, 0xe4, 0xff, 0xd2, 0xd9, 0xe0, 0xff, 0xd2, 0xd9, 0xe0, 0xff, 0xd0, 0xd6, 0xdc, 0xff, 0xcc, 0xd1, 0xd7, 0xff, 0xcf, 0xd2, 0xd7, 0xff, 0xd0, 0xd3, 0xd8, 0xff, 0xcb, 0xd0, 0xd6, 0xff, 0xc8, 0xce, 0xd6, 0xff, 0xca, 0xd0, 0xd7, 0xff, 0xc8, 0xce, 0xd5, 0xff, 0xc9, 0xcf, 0xd6, 0xff, 0xc8, 0xce, 0xd5, 0xff, 0xc8, 0xce, 0xd5, 0xff, 0xc4, 0xca, 0xd1, 0xff, 0xc4, 0xc8, 0xd1, 0xff, 0xc0, 0xc6, 0xcf, 0xff, 0xba, 0xc1, 0xcb, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xb9, 0xbf, 0xca, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xbb, 0xc0, 0xcb, 0xff, 0xbc, 0xc0, 0xcb, 0xff, 0xbb, 0xbf, 0xca, 0xff, 0xb9, 0xbd, 0xc8, 0xff, 0xb9, 0xbd, 0xc8, 0xff, 0xba, 0xbe, 0xc9, 0xff, 0xb9, 0xc0, 0xc8, 0xff, 0xb8, 0xc0, 0xc7, 0xff, 0xb9, 0xbf, 0xc8, 0xff, 0xb5, 0xb9, 0xc2, 0xff, 0xb5, 0xb6, 0xc1, 0xff, 0xb5, 0xb6, 0xc0, 0xff, 0xad, 0xb1, 0xbd, 0xff, 0xa5, 0xac, 0xb9, 0xff, 0xa1, 0xa9, 0xb5, 0xff, 0x96, 0x9e, 0xab, 0xff, 0x8f, 0x99, 0xa7, 0xff, 0x8a, 0x95, 0xa1, 0xff, 0x7f, 0x8c, 0x9b, 0xff, 0x75, 0x84, 0x95, 0xff, 0x6c, 0x7b, 0x8b, 0xff, 0x63, 0x71, 0x81, 0xff, 0x5f, 0x6e, 0x7e, 0xff, 0x4d, 0x5b, 0x6c, 0xff, 0x48, 0x58, 0x66, 0xff, 0x43, 0x57, 0x64, 0xff, 0x5b, 0x71, 0x85, 0xff, 0x3a, 0x55, 0x6a, 0xff, 0x3d, 0x59, 0x6d, 0xff, 0x33, 0x4f, 0x62, 0xff, 0x32, 0x48, 0x5d, 0xff, 0x40, 0x55, 0x6c, 0xff, 0x3d, 0x56, 0x6f, 0xff, 0x56, 0x76, 0x90, 0xff, 0x76, 0x9c, 0xb3, 0xff, 0xad, 0xd5, 0xe9, 0xff, 0x9b, 0xbf, 0xd2, 0xff, 0x20, 0x3f, 0x54, 0xff, 0x5d, 0x7e, 0x92, 0xff, 0x9a, 0xbb, 0xcf, 0xff, 0x79, 0x9a, 0xae, 0xff, 0x63, 0x85, 0x99, 0xff, 0x6e, 0x8e, 0xa2, 0xff, 0x3e, 0x52, 0x61, 0xff, 0x01, 0x0e, 0x19, 0xff, 0x0d, 0x16, 0x20, 0xff, 0x0b, 0x1a, 0x25, 0xff, 0x17, 0x2d, 0x3f, 0xff, 0x5e, 0x7c, 0x8f, 0xff, 0x64, 0x88, 0x9b, 0xff, 0x5b, 0x82, 0x93, 0xff, 0x70, 0x97, 0xa5, 0xff, 0x7c, 0x9c, 0xa4, 0xff, 0x92, 0xa9, 0xac, 0xff, 0x95, 0xa2, 0xa3, 0xff, 0x95, 0x9c, 0x9c, 0xff, 0x96, 0x9d, 0x9a, 0xff, 0x95, 0x9a, 0x94, 0xff, 0x98, 0x9c, 0x96, 0xff, 0x99, 0x9f, 0x9b, 0xff, 0x9b, 0xa0, 0x9d, 0xff, 0xa0, 0xa4, 0x9f, 0xff, 0xa3, 0xa8, 0xa2, 0xff, 0xa9, 0xac, 0xa7, 0xff, 0xad, 0xae, 0xaa, 0xff, 0xb0, 0xb1, 0xac, 0xff, 0xb1, 0xb4, 0xb0, 0xff, 0xb3, 0xb7, 0xb2, 0xff, 0xb6, 0xba, 0xb5, 0xff, 0xb8, 0xbc, 0xb7, 0xff, 0xb7, 0xbb, 0xb6, 0xfe, 0xbb, 0xbb, 0xb4, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb8, 0xd8, 0xe4, 0x86, 0x5c, 0x80, 0x94, 0xff, 0x42, 0x6a, 0x82, 0xff, 0x5a, 0x83, 0x9c, 0xff, 0x62, 0x88, 0x9f, 0xff, 0x5f, 0x82, 0x94, 0xff, 0x4b, 0x6a, 0x7e, 0xff, 0x27, 0x48, 0x5c, 0xff, 0x32, 0x55, 0x68, 0xff, 0x5b, 0x7b, 0x8c, 0xff, 0x49, 0x61, 0x72, 0xff, 0x38, 0x45, 0x57, 0xff, 0x00, 0x00, 0x02, 0xff, 0x03, 0x05, 0x06, 0xff, 0x24, 0x2f, 0x3b, 0xff, 0x4d, 0x5b, 0x6c, 0xff, 0xaa, 0xb4, 0xc3, 0xff, 0xc9, 0xd1, 0xdd, 0xff, 0xd5, 0xdb, 0xe2, 0xff, 0xdc, 0xe2, 0xe8, 0xff, 0xd7, 0xde, 0xe5, 0xff, 0xd7, 0xdf, 0xe6, 0xff, 0xd9, 0xe2, 0xe9, 0xff, 0xcf, 0xd9, 0xe0, 0xff, 0xc0, 0xcb, 0xd4, 0xff, 0xc0, 0xcc, 0xd7, 0xff, 0xc0, 0xca, 0xd4, 0xff, 0xc1, 0xca, 0xd3, 0xff, 0xc9, 0xd0, 0xd7, 0xff, 0xca, 0xd0, 0xd7, 0xff, 0xca, 0xd0, 0xd6, 0xff, 0xcb, 0xd1, 0xd8, 0xff, 0xca, 0xd0, 0xd7, 0xff, 0xc7, 0xcd, 0xd4, 0xff, 0xc7, 0xcc, 0xd3, 0xff, 0xc6, 0xcc, 0xd3, 0xff, 0xc8, 0xcd, 0xd5, 0xff, 0xc5, 0xcb, 0xd2, 0xff, 0xc3, 0xc7, 0xd0, 0xff, 0xbd, 0xc4, 0xcc, 0xff, 0xba, 0xc1, 0xca, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xb9, 0xbe, 0xca, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xb5, 0xbb, 0xc6, 0xff, 0xb7, 0xbd, 0xc8, 0xff, 0xbb, 0xc1, 0xcc, 0xff, 0xbb, 0xc1, 0xcc, 0xff, 0xbc, 0xc1, 0xcc, 0xff, 0xbc, 0xc0, 0xcb, 0xff, 0xbd, 0xc1, 0xcc, 0xff, 0xb9, 0xbd, 0xc8, 0xff, 0xba, 0xbe, 0xc8, 0xff, 0xbd, 0xc0, 0xcc, 0xff, 0xbb, 0xc1, 0xcb, 0xff, 0xba, 0xc1, 0xca, 0xff, 0xb6, 0xbd, 0xc7, 0xff, 0xb3, 0xba, 0xc6, 0xff, 0xb0, 0xb7, 0xc2, 0xff, 0xaa, 0xb1, 0xbe, 0xff, 0xa6, 0xad, 0xbb, 0xff, 0x9c, 0xa5, 0xb2, 0xff, 0x94, 0x9d, 0xac, 0xff, 0x8c, 0x97, 0xa5, 0xff, 0x7d, 0x89, 0x9a, 0xff, 0x75, 0x82, 0x93, 0xff, 0x6e, 0x7d, 0x8e, 0xff, 0x5e, 0x6d, 0x7d, 0xff, 0x55, 0x64, 0x74, 0xff, 0x57, 0x67, 0x77, 0xff, 0x5e, 0x6d, 0x7d, 0xff, 0x54, 0x63, 0x74, 0xff, 0x4a, 0x5a, 0x68, 0xff, 0x43, 0x56, 0x64, 0xff, 0x5a, 0x72, 0x83, 0xff, 0x4c, 0x67, 0x7b, 0xff, 0x3e, 0x59, 0x6e, 0xff, 0x46, 0x61, 0x74, 0xff, 0x29, 0x42, 0x53, 0xff, 0x32, 0x49, 0x5e, 0xff, 0x3d, 0x56, 0x70, 0xff, 0x3f, 0x5d, 0x77, 0xff, 0x5e, 0x7f, 0x98, 0xff, 0x87, 0xaf, 0xc5, 0xff, 0xb4, 0xd8, 0xec, 0xff, 0x58, 0x76, 0x8b, 0xff, 0x20, 0x40, 0x54, 0xff, 0x8c, 0xad, 0xc1, 0xff, 0x82, 0xa4, 0xb8, 0xff, 0x63, 0x86, 0x9a, 0xff, 0x74, 0x98, 0xad, 0xff, 0x59, 0x73, 0x88, 0xff, 0x00, 0x0f, 0x1a, 0xff, 0x1b, 0x25, 0x2c, 0xff, 0x1b, 0x1f, 0x28, 0xff, 0x14, 0x21, 0x31, 0xff, 0x25, 0x41, 0x55, 0xff, 0x63, 0x88, 0x9a, 0xff, 0x68, 0x90, 0xa2, 0xff, 0x62, 0x8e, 0x9c, 0xff, 0x78, 0x9c, 0xa6, 0xff, 0x89, 0xa7, 0xad, 0xff, 0x92, 0xa3, 0xa5, 0xff, 0x97, 0x9d, 0x9c, 0xff, 0x95, 0x9c, 0x99, 0xff, 0x98, 0x9d, 0x97, 0xff, 0x9a, 0x9e, 0x98, 0xff, 0x99, 0x9f, 0x9b, 0xff, 0x9c, 0xa1, 0x9d, 0xff, 0xa0, 0xa4, 0x9f, 0xff, 0xa3, 0xa8, 0xa2, 0xff, 0xa9, 0xac, 0xa7, 0xff, 0xac, 0xad, 0xa9, 0xff, 0xaf, 0xaf, 0xac, 0xff, 0xb1, 0xb3, 0xae, 0xff, 0xb2, 0xb6, 0xb1, 0xff, 0xb4, 0xb8, 0xb3, 0xff, 0xb7, 0xbb, 0xb6, 0xff, 0xb8, 0xbc, 0xb7, 0xff, 0xb7, 0xbc, 0xb7, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbf, 0xff, 0xff, 0x04, 0xaf, 0xcf, 0xdb, 0xe7, 0x4a, 0x71, 0x85, 0xff, 0x44, 0x6d, 0x84, 0xff, 0x5d, 0x87, 0x9f, 0xff, 0x5b, 0x81, 0x96, 0xff, 0x64, 0x85, 0x95, 0xff, 0x56, 0x75, 0x89, 0xff, 0x30, 0x53, 0x67, 0xff, 0x47, 0x6d, 0x7f, 0xff, 0x5b, 0x7c, 0x8c, 0xff, 0x4e, 0x66, 0x77, 0xff, 0x13, 0x1e, 0x30, 0xff, 0x00, 0x00, 0x00, 0xff, 0x15, 0x19, 0x19, 0xff, 0x16, 0x21, 0x2d, 0xff, 0x38, 0x45, 0x55, 0xff, 0xad, 0xb9, 0xc7, 0xff, 0xcd, 0xd5, 0xe1, 0xff, 0xd8, 0xdc, 0xe5, 0xff, 0xdb, 0xdf, 0xe8, 0xff, 0xd8, 0xde, 0xe6, 0xff, 0xda, 0xe2, 0xeb, 0xff, 0xcd, 0xd7, 0xdf, 0xff, 0xb4, 0xbd, 0xc4, 0xff, 0xa2, 0xb0, 0xbe, 0xff, 0xa2, 0xb2, 0xc1, 0xff, 0xa3, 0xaf, 0xbd, 0xff, 0xa4, 0xaf, 0xbc, 0xff, 0xad, 0xb6, 0xc0, 0xff, 0xb4, 0xbc, 0xc6, 0xff, 0xc1, 0xc7, 0xcf, 0xff, 0xc8, 0xcd, 0xd3, 0xff, 0xc5, 0xcb, 0xd1, 0xff, 0xc4, 0xc9, 0xd0, 0xff, 0xc3, 0xc9, 0xcf, 0xff, 0xc4, 0xca, 0xd1, 0xff, 0xc6, 0xcc, 0xd2, 0xff, 0xc2, 0xc8, 0xcf, 0xff, 0xbf, 0xc4, 0xcd, 0xff, 0xbd, 0xc3, 0xcc, 0xff, 0xbb, 0xc3, 0xcc, 0xff, 0xbc, 0xc1, 0xcd, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xb4, 0xba, 0xc5, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xbb, 0xc1, 0xcc, 0xff, 0xbb, 0xc2, 0xcd, 0xff, 0xbc, 0xc0, 0xcc, 0xff, 0xbb, 0xbe, 0xc9, 0xff, 0xbd, 0xc1, 0xcc, 0xff, 0xba, 0xbd, 0xc8, 0xff, 0xb8, 0xbc, 0xc7, 0xff, 0xba, 0xbe, 0xc8, 0xff, 0xb5, 0xb9, 0xc5, 0xff, 0xb0, 0xb5, 0xc2, 0xff, 0xad, 0xb3, 0xc0, 0xff, 0xa3, 0xad, 0xbb, 0xff, 0x92, 0x9f, 0xac, 0xff, 0x86, 0x96, 0xa3, 0xff, 0x82, 0x8e, 0x9c, 0xff, 0x7b, 0x84, 0x94, 0xff, 0x74, 0x80, 0x91, 0xff, 0x6b, 0x79, 0x8a, 0xff, 0x5c, 0x6c, 0x7e, 0xff, 0x4e, 0x5d, 0x71, 0xff, 0x46, 0x55, 0x67, 0xff, 0x3f, 0x4e, 0x5d, 0xff, 0x3c, 0x4b, 0x5b, 0xff, 0x40, 0x4f, 0x5f, 0xff, 0x44, 0x53, 0x63, 0xff, 0x48, 0x56, 0x67, 0xff, 0x43, 0x53, 0x61, 0xff, 0x3d, 0x50, 0x5e, 0xff, 0x54, 0x6d, 0x7c, 0xff, 0x62, 0x7e, 0x91, 0xff, 0x34, 0x4e, 0x64, 0xff, 0x4a, 0x66, 0x77, 0xff, 0x34, 0x4e, 0x5e, 0xff, 0x2d, 0x46, 0x58, 0xff, 0x2e, 0x48, 0x60, 0xff, 0x34, 0x4f, 0x6a, 0xff, 0x46, 0x64, 0x7e, 0xff, 0x6e, 0x92, 0xaa, 0xff, 0x9b, 0xbd, 0xd2, 0xff, 0x97, 0xb4, 0xc8, 0xff, 0x24, 0x44, 0x58, 0xff, 0x51, 0x72, 0x86, 0xff, 0x8b, 0xae, 0xc2, 0xff, 0x6f, 0x93, 0xa6, 0xff, 0x6e, 0x93, 0xaa, 0xff, 0x70, 0x91, 0xa7, 0xff, 0x22, 0x32, 0x3d, 0xff, 0x0f, 0x14, 0x19, 0xff, 0x1f, 0x26, 0x2e, 0xff, 0x14, 0x20, 0x31, 0xff, 0x28, 0x41, 0x57, 0xff, 0x3c, 0x60, 0x74, 0xff, 0x5c, 0x85, 0x98, 0xff, 0x5b, 0x88, 0x97, 0xff, 0x74, 0x9b, 0xa5, 0xff, 0x86, 0xa8, 0xae, 0xff, 0x95, 0xa8, 0xaa, 0xff, 0x9a, 0x9f, 0x9d, 0xff, 0x94, 0x9b, 0x97, 0xff, 0x99, 0x9e, 0x98, 0xff, 0x9c, 0xa0, 0x9a, 0xff, 0x99, 0x9f, 0x9b, 0xff, 0x9d, 0xa2, 0x9f, 0xff, 0xa0, 0xa4, 0x9f, 0xff, 0xa3, 0xa9, 0xa4, 0xff, 0xa9, 0xab, 0xa7, 0xff, 0xab, 0xac, 0xa8, 0xff, 0xaf, 0xaf, 0xac, 0xff, 0xb0, 0xb2, 0xae, 0xff, 0xb0, 0xb4, 0xaf, 0xff, 0xb3, 0xb7, 0xb2, 0xff, 0xb5, 0xb9, 0xb4, 0xff, 0xb7, 0xbb, 0xb6, 0xff, 0xb8, 0xbc, 0xb7, 0xe7, 0xbf, 0xbf, 0xbf, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb9, 0xd5, 0xdf, 0x49, 0xac, 0xcd, 0xdd, 0xff, 0x58, 0x7e, 0x93, 0xff, 0x44, 0x6d, 0x88, 0xff, 0x55, 0x7e, 0x99, 0xff, 0x69, 0x8e, 0xa4, 0xff, 0x68, 0x86, 0x98, 0xff, 0x40, 0x62, 0x74, 0xff, 0x35, 0x54, 0x68, 0xff, 0x52, 0x71, 0x85, 0xff, 0x57, 0x78, 0x8c, 0xff, 0x33, 0x4d, 0x5e, 0xff, 0x08, 0x12, 0x25, 0xff, 0x00, 0x00, 0x00, 0xff, 0x1b, 0x1d, 0x1c, 0xff, 0x05, 0x08, 0x0c, 0xff, 0x36, 0x42, 0x4f, 0xff, 0xb5, 0xc3, 0xd3, 0xff, 0xd2, 0xd9, 0xe3, 0xff, 0xd8, 0xe0, 0xe5, 0xff, 0xd9, 0xe3, 0xea, 0xff, 0xd9, 0xe2, 0xea, 0xff, 0xca, 0xd5, 0xdf, 0xff, 0xb3, 0xbe, 0xca, 0xff, 0xa0, 0xac, 0xb9, 0xff, 0x93, 0xa3, 0xb2, 0xff, 0x91, 0xa2, 0xb1, 0xff, 0x8a, 0x9a, 0xa9, 0xff, 0x83, 0x92, 0xa1, 0xff, 0x7d, 0x8c, 0x9a, 0xff, 0x87, 0x96, 0xa5, 0xff, 0x98, 0xa5, 0xb3, 0xff, 0xa0, 0xab, 0xb8, 0xff, 0xa7, 0xb1, 0xbd, 0xff, 0xac, 0xb5, 0xc1, 0xff, 0xb6, 0xbf, 0xc8, 0xff, 0xbd, 0xc5, 0xcf, 0xff, 0xc1, 0xc5, 0xcf, 0xff, 0xc0, 0xc2, 0xcb, 0xff, 0xbe, 0xc0, 0xc9, 0xff, 0xc0, 0xc5, 0xcc, 0xff, 0xc2, 0xc8, 0xcf, 0xff, 0xbf, 0xc6, 0xcc, 0xff, 0xbc, 0xc3, 0xcc, 0xff, 0xb8, 0xbe, 0xca, 0xff, 0xb6, 0xbc, 0xc7, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xb9, 0xbf, 0xca, 0xff, 0xb9, 0xbe, 0xc9, 0xff, 0xb6, 0xbd, 0xc7, 0xff, 0xb6, 0xbc, 0xc7, 0xff, 0xb9, 0xbe, 0xc9, 0xff, 0xb7, 0xbe, 0xc8, 0xff, 0xb2, 0xbb, 0xc7, 0xff, 0xac, 0xb3, 0xbf, 0xff, 0xa3, 0xa9, 0xb9, 0xff, 0xa0, 0xa6, 0xb8, 0xff, 0x98, 0xa0, 0xb1, 0xff, 0x84, 0x8e, 0x9e, 0xff, 0x74, 0x81, 0x92, 0xff, 0x6c, 0x7c, 0x8b, 0xff, 0x6a, 0x76, 0x87, 0xff, 0x69, 0x72, 0x84, 0xff, 0x66, 0x70, 0x82, 0xff, 0x5b, 0x66, 0x77, 0xff, 0x5a, 0x66, 0x75, 0xff, 0x56, 0x62, 0x70, 0xff, 0x4d, 0x5a, 0x6b, 0xff, 0x50, 0x5f, 0x72, 0xff, 0x4c, 0x5a, 0x6c, 0xff, 0x40, 0x4f, 0x5e, 0xff, 0x3e, 0x4d, 0x5d, 0xff, 0x3b, 0x4a, 0x5a, 0xff, 0x35, 0x45, 0x55, 0xff, 0x2f, 0x3e, 0x4e, 0xff, 0x48, 0x59, 0x69, 0xff, 0x66, 0x80, 0x92, 0xff, 0x2f, 0x52, 0x62, 0xff, 0x3d, 0x58, 0x6b, 0xff, 0x46, 0x5f, 0x73, 0xff, 0x29, 0x43, 0x57, 0xff, 0x2b, 0x40, 0x55, 0xff, 0x2a, 0x3c, 0x54, 0xff, 0x43, 0x5d, 0x73, 0xff, 0x68, 0x8c, 0xa1, 0xff, 0x76, 0x9c, 0xb3, 0xff, 0xaa, 0xce, 0xe5, 0xff, 0x59, 0x78, 0x8d, 0xff, 0x1c, 0x3b, 0x4e, 0xff, 0x80, 0x9f, 0xb3, 0xff, 0x8b, 0xac, 0xc1, 0xff, 0x6b, 0x91, 0xa4, 0xff, 0x80, 0xa3, 0xb6, 0xff, 0x49, 0x5f, 0x6f, 0xff, 0x00, 0x04, 0x11, 0xff, 0x0f, 0x1a, 0x24, 0xff, 0x1c, 0x26, 0x31, 0xff, 0x1f, 0x2a, 0x37, 0xff, 0x2e, 0x43, 0x55, 0xff, 0x4e, 0x72, 0x88, 0xff, 0x69, 0x95, 0xac, 0xff, 0x6b, 0x93, 0xa4, 0xff, 0x82, 0xa2, 0xaa, 0xff, 0x99, 0xaf, 0xb0, 0xff, 0x9b, 0xa5, 0xa3, 0xff, 0x99, 0x9d, 0x9b, 0xff, 0x98, 0x9c, 0x96, 0xff, 0x98, 0x9f, 0x9a, 0xff, 0x9e, 0x9e, 0x9d, 0xff, 0xa1, 0xa0, 0x9d, 0xff, 0x9e, 0xa3, 0x9e, 0xff, 0xa4, 0xa7, 0xa3, 0xff, 0xa9, 0xaa, 0xa6, 0xff, 0xac, 0xad, 0xa9, 0xff, 0xb1, 0xb0, 0xac, 0xff, 0xb1, 0xb1, 0xad, 0xff, 0xb1, 0xb3, 0xaf, 0xff, 0xb4, 0xb5, 0xb1, 0xff, 0xb5, 0xb6, 0xb2, 0xff, 0xb8, 0xba, 0xb6, 0xff, 0xb9, 0xbb, 0xb6, 0xff, 0xba, 0xba, 0xb6, 0x4a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa6, 0xc5, 0xd5, 0x9f, 0xa2, 0xc5, 0xd5, 0xff, 0x5e, 0x83, 0x9a, 0xff, 0x52, 0x7a, 0x94, 0xff, 0x65, 0x8d, 0xa8, 0xff, 0x6e, 0x91, 0xa9, 0xff, 0x5a, 0x78, 0x8a, 0xff, 0x34, 0x58, 0x69, 0xff, 0x3c, 0x5a, 0x6f, 0xff, 0x54, 0x71, 0x86, 0xff, 0x4a, 0x6b, 0x80, 0xff, 0x1f, 0x3b, 0x4c, 0xff, 0x0d, 0x19, 0x2b, 0xff, 0x00, 0x00, 0x05, 0xff, 0x13, 0x14, 0x12, 0xff, 0x00, 0x00, 0x00, 0xff, 0x3b, 0x47, 0x54, 0xff, 0xbd, 0xca, 0xdc, 0xff, 0xd3, 0xda, 0xe3, 0xff, 0xd6, 0xdf, 0xe4, 0xff, 0xd4, 0xdf, 0xe5, 0xff, 0xc3, 0xce, 0xd5, 0xff, 0xac, 0xb7, 0xc1, 0xff, 0xa0, 0xaa, 0xb8, 0xff, 0x9d, 0xa9, 0xb7, 0xff, 0x96, 0xa5, 0xb3, 0xff, 0x8e, 0x9e, 0xac, 0xff, 0x8a, 0x9a, 0xa7, 0xff, 0x7f, 0x8e, 0x9c, 0xff, 0x73, 0x83, 0x91, 0xff, 0x71, 0x81, 0x8f, 0xff, 0x76, 0x84, 0x94, 0xff, 0x7f, 0x8d, 0x9f, 0xff, 0x84, 0x90, 0xa0, 0xff, 0x89, 0x94, 0xa2, 0xff, 0x91, 0x9b, 0xa8, 0xff, 0x9a, 0xa4, 0xb2, 0xff, 0xa9, 0xb1, 0xbd, 0xff, 0xb3, 0xb8, 0xc4, 0xff, 0xb9, 0xbe, 0xc9, 0xff, 0xba, 0xc0, 0xc9, 0xff, 0xbe, 0xc4, 0xcc, 0xff, 0xbe, 0xc5, 0xca, 0xff, 0xbc, 0xc2, 0xcb, 0xff, 0xb9, 0xbf, 0xca, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xb5, 0xbb, 0xc6, 0xff, 0xb6, 0xbc, 0xc7, 0xff, 0xb6, 0xbc, 0xc7, 0xff, 0xb2, 0xb8, 0xc2, 0xff, 0xb2, 0xb9, 0xc3, 0xff, 0xb6, 0xbc, 0xc8, 0xff, 0xb5, 0xbe, 0xc8, 0xff, 0xaf, 0xb9, 0xc5, 0xff, 0xa4, 0xac, 0xb9, 0xff, 0x97, 0x9f, 0xaf, 0xff, 0x95, 0x9d, 0xb0, 0xff, 0x8a, 0x92, 0xa4, 0xff, 0x7c, 0x85, 0x97, 0xff, 0x74, 0x7e, 0x90, 0xff, 0x6f, 0x7c, 0x8d, 0xff, 0x6e, 0x79, 0x8b, 0xff, 0x66, 0x70, 0x81, 0xff, 0x65, 0x6f, 0x80, 0xff, 0x62, 0x6c, 0x7d, 0xff, 0x67, 0x72, 0x81, 0xff, 0x75, 0x7f, 0x8c, 0xff, 0x74, 0x81, 0x91, 0xff, 0x71, 0x80, 0x93, 0xff, 0x6e, 0x7d, 0x8e, 0xff, 0x62, 0x72, 0x82, 0xff, 0x5a, 0x69, 0x79, 0xff, 0x4c, 0x5c, 0x6c, 0xff, 0x35, 0x47, 0x56, 0xff, 0x29, 0x39, 0x48, 0xff, 0x41, 0x4f, 0x5f, 0xff, 0x5e, 0x78, 0x88, 0xff, 0x4b, 0x6d, 0x7c, 0xff, 0x33, 0x4b, 0x5f, 0xff, 0x53, 0x6a, 0x7f, 0xff, 0x35, 0x51, 0x64, 0xff, 0x2e, 0x41, 0x55, 0xff, 0x24, 0x36, 0x4b, 0xff, 0x31, 0x48, 0x5e, 0xff, 0x67, 0x87, 0x9b, 0xff, 0x6b, 0x92, 0xa9, 0xff, 0x8b, 0xb3, 0xca, 0xff, 0x98, 0xb9, 0xcc, 0xff, 0x24, 0x43, 0x55, 0xff, 0x5e, 0x7d, 0x90, 0xff, 0x96, 0xb7, 0xcc, 0xff, 0x6c, 0x90, 0xa3, 0xff, 0x85, 0xa7, 0xb9, 0xff, 0x5f, 0x7a, 0x8c, 0xff, 0x12, 0x24, 0x35, 0xff, 0x0d, 0x1b, 0x28, 0xff, 0x17, 0x20, 0x29, 0xff, 0x0a, 0x11, 0x19, 0xff, 0x2b, 0x38, 0x44, 0xff, 0x37, 0x55, 0x69, 0xff, 0x54, 0x7e, 0x96, 0xff, 0x6c, 0x95, 0xaa, 0xff, 0x7b, 0x9d, 0xaa, 0xff, 0x99, 0xb2, 0xb6, 0xff, 0x9b, 0xa7, 0xa6, 0xff, 0x9f, 0xa2, 0xa0, 0xff, 0x9b, 0x9f, 0x9a, 0xff, 0x98, 0xa1, 0x9c, 0xff, 0xa0, 0x9e, 0x9c, 0xff, 0xa0, 0x9d, 0x9b, 0xff, 0x9c, 0xa1, 0x9c, 0xff, 0xa2, 0xa3, 0x9f, 0xff, 0xa6, 0xa7, 0xa3, 0xff, 0xa9, 0xaa, 0xa6, 0xff, 0xae, 0xad, 0xa9, 0xff, 0xaf, 0xaf, 0xab, 0xff, 0xaf, 0xb0, 0xac, 0xff, 0xb3, 0xb3, 0xaf, 0xff, 0xb4, 0xb5, 0xb1, 0xff, 0xb6, 0xb7, 0xb3, 0xff, 0xb9, 0xba, 0xb6, 0xff, 0xba, 0xbb, 0xb6, 0x9f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb6, 0xda, 0xda, 0x07, 0xa2, 0xc2, 0xd2, 0xef, 0x8f, 0xb2, 0xc3, 0xff, 0x51, 0x77, 0x8c, 0xff, 0x5e, 0x86, 0x9e, 0xff, 0x71, 0x99, 0xb2, 0xff, 0x6b, 0x8f, 0xa5, 0xff, 0x56, 0x77, 0x8a, 0xff, 0x37, 0x5a, 0x6c, 0xff, 0x3f, 0x5d, 0x72, 0xff, 0x50, 0x6d, 0x84, 0xff, 0x39, 0x5c, 0x70, 0xff, 0x1b, 0x3a, 0x4c, 0xff, 0x17, 0x26, 0x37, 0xff, 0x08, 0x08, 0x0f, 0xff, 0x0f, 0x11, 0x0e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x3a, 0x45, 0x52, 0xff, 0xb9, 0xc7, 0xd7, 0xff, 0xd3, 0xda, 0xe3, 0xff, 0xd6, 0xde, 0xe4, 0xff, 0xc1, 0xca, 0xd3, 0xff, 0xa9, 0xb2, 0xbb, 0xff, 0xac, 0xb6, 0xbf, 0xff, 0xb5, 0xbf, 0xca, 0xff, 0xb6, 0xc0, 0xcd, 0xff, 0xb0, 0xbd, 0xca, 0xff, 0xa8, 0xb5, 0xc3, 0xff, 0xa6, 0xb2, 0xbf, 0xff, 0xa2, 0xae, 0xbb, 0xff, 0xa0, 0xad, 0xb9, 0xff, 0x95, 0xa1, 0xad, 0xff, 0x82, 0x8f, 0x9f, 0xff, 0x82, 0x8f, 0xa1, 0xff, 0x88, 0x94, 0xa5, 0xff, 0x85, 0x8f, 0x9f, 0xff, 0x84, 0x8f, 0x9f, 0xff, 0x85, 0x90, 0xa0, 0xff, 0x88, 0x94, 0xa3, 0xff, 0x92, 0x9f, 0xac, 0xff, 0xa9, 0xb4, 0xc0, 0xff, 0xaf, 0xb7, 0xc3, 0xff, 0xb6, 0xbb, 0xc7, 0xff, 0xbd, 0xc0, 0xca, 0xff, 0xb9, 0xbe, 0xc9, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xb7, 0xbd, 0xc8, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xb3, 0xb9, 0xc3, 0xff, 0xb2, 0xb9, 0xc3, 0xff, 0xb4, 0xbc, 0xc7, 0xff, 0xb3, 0xbd, 0xc7, 0xff, 0xaf, 0xb8, 0xc4, 0xff, 0xa7, 0xb0, 0xbd, 0xff, 0x9c, 0xa4, 0xb3, 0xff, 0x98, 0xa1, 0xb1, 0xff, 0x96, 0x9f, 0xae, 0xff, 0x95, 0x9e, 0xae, 0xff, 0x8e, 0x97, 0xa6, 0xff, 0x8c, 0x95, 0xa4, 0xff, 0x8c, 0x95, 0xa6, 0xff, 0x8b, 0x94, 0xa7, 0xff, 0x89, 0x92, 0xa3, 0xff, 0x88, 0x92, 0xa1, 0xff, 0x92, 0x9d, 0xab, 0xff, 0x90, 0x9a, 0xa7, 0xff, 0x94, 0x9f, 0xad, 0xff, 0x8f, 0x9b, 0xaa, 0xff, 0x83, 0x90, 0x9f, 0xff, 0x7b, 0x8a, 0x9a, 0xff, 0x6e, 0x7e, 0x8e, 0xff, 0x64, 0x75, 0x85, 0xff, 0x50, 0x62, 0x71, 0xff, 0x3c, 0x4f, 0x5b, 0xff, 0x4c, 0x5b, 0x6b, 0xff, 0x55, 0x6d, 0x7d, 0xff, 0x55, 0x73, 0x83, 0xff, 0x34, 0x46, 0x59, 0xff, 0x56, 0x6b, 0x7d, 0xff, 0x4a, 0x66, 0x78, 0xff, 0x2c, 0x41, 0x56, 0xff, 0x25, 0x3b, 0x4f, 0xff, 0x20, 0x38, 0x4e, 0xff, 0x45, 0x61, 0x75, 0xff, 0x78, 0x9a, 0xb2, 0xff, 0x6a, 0x8f, 0xa8, 0xff, 0xa2, 0xc5, 0xda, 0xff, 0x69, 0x8b, 0x9e, 0xff, 0x2b, 0x4c, 0x5f, 0xff, 0x86, 0xa7, 0xbc, 0xff, 0x7f, 0xa4, 0xb9, 0xff, 0x84, 0xa9, 0xba, 0xff, 0x6a, 0x86, 0x98, 0xff, 0x39, 0x4e, 0x5f, 0xff, 0x27, 0x38, 0x47, 0xff, 0x1a, 0x2a, 0x39, 0xff, 0x0e, 0x1a, 0x23, 0xff, 0x1f, 0x2c, 0x33, 0xff, 0x30, 0x49, 0x58, 0xff, 0x3f, 0x63, 0x79, 0xff, 0x76, 0x9d, 0xb1, 0xff, 0x72, 0x98, 0xaa, 0xff, 0x8d, 0xab, 0xb2, 0xff, 0xa0, 0xad, 0xac, 0xff, 0x9f, 0xa3, 0xa2, 0xff, 0x9b, 0x9e, 0x9a, 0xff, 0x9a, 0xa1, 0x9a, 0xff, 0x9f, 0x9e, 0x9b, 0xff, 0xa0, 0x9f, 0x9c, 0xff, 0x9d, 0xa2, 0x9d, 0xff, 0xa2, 0xa3, 0x9f, 0xff, 0xa6, 0xa6, 0xa3, 0xff, 0xa7, 0xa8, 0xa4, 0xff, 0xac, 0xab, 0xa7, 0xff, 0xae, 0xae, 0xaa, 0xff, 0xad, 0xae, 0xaa, 0xff, 0xaf, 0xb0, 0xac, 0xff, 0xb2, 0xb3, 0xaf, 0xff, 0xb6, 0xb7, 0xb3, 0xff, 0xb7, 0xb8, 0xb4, 0xff, 0xb8, 0xb9, 0xb6, 0xef, 0xb6, 0xb6, 0xb6, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0xb6, 0xc4, 0x46, 0x9e, 0xc0, 0xd3, 0xff, 0x82, 0xa6, 0xba, 0xff, 0x4a, 0x71, 0x85, 0xff, 0x60, 0x87, 0x9e, 0xff, 0x73, 0x98, 0xb0, 0xff, 0x6e, 0x92, 0xa8, 0xff, 0x5b, 0x7c, 0x90, 0xff, 0x2a, 0x4c, 0x61, 0xff, 0x36, 0x53, 0x6a, 0xff, 0x4a, 0x67, 0x7f, 0xff, 0x33, 0x57, 0x6c, 0xff, 0x1e, 0x3e, 0x50, 0xff, 0x20, 0x31, 0x44, 0xff, 0x0b, 0x0e, 0x15, 0xff, 0x01, 0x02, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x3d, 0x48, 0x55, 0xff, 0xb4, 0xc2, 0xd3, 0xff, 0xd4, 0xdc, 0xe4, 0xff, 0xd2, 0xda, 0xe1, 0xff, 0xb9, 0xc3, 0xcd, 0xff, 0xbb, 0xc4, 0xcd, 0xff, 0xc1, 0xca, 0xd3, 0xff, 0xc4, 0xcd, 0xd7, 0xff, 0xc2, 0xcb, 0xd6, 0xff, 0xbf, 0xc8, 0xd4, 0xff, 0xbd, 0xc6, 0xd2, 0xff, 0xb9, 0xc2, 0xce, 0xff, 0xb2, 0xbb, 0xc8, 0xff, 0xb6, 0xbe, 0xcb, 0xff, 0xbf, 0xc7, 0xd4, 0xff, 0xb4, 0xbe, 0xcc, 0xff, 0xac, 0xb7, 0xc6, 0xff, 0xaa, 0xb6, 0xc4, 0xff, 0xa7, 0xb1, 0xc2, 0xff, 0xa2, 0xab, 0xbd, 0xff, 0xa4, 0xae, 0xbf, 0xff, 0x95, 0xa3, 0xb1, 0xff, 0x8d, 0x9c, 0xaa, 0xff, 0x9c, 0xa7, 0xb5, 0xff, 0xac, 0xb4, 0xc2, 0xff, 0xb5, 0xbc, 0xc9, 0xff, 0xba, 0xbd, 0xcb, 0xff, 0xb7, 0xbd, 0xc9, 0xff, 0xb6, 0xbf, 0xc9, 0xff, 0xb6, 0xbe, 0xc8, 0xff, 0xb8, 0xc0, 0xca, 0xff, 0xb7, 0xc0, 0xca, 0xff, 0xb5, 0xbe, 0xc8, 0xff, 0xb5, 0xbd, 0xc7, 0xff, 0xb3, 0xbc, 0xc5, 0xff, 0xb1, 0xba, 0xc4, 0xff, 0xb1, 0xbb, 0xc5, 0xff, 0xad, 0xb6, 0xc3, 0xff, 0xa5, 0xae, 0xbb, 0xff, 0xa2, 0xab, 0xb8, 0xff, 0xa3, 0xac, 0xba, 0xff, 0xa0, 0xaa, 0xb7, 0xff, 0x9d, 0xa6, 0xb4, 0xff, 0x99, 0xa2, 0xb0, 0xff, 0x95, 0x9d, 0xab, 0xff, 0x9a, 0xa0, 0xaf, 0xff, 0xa2, 0xa8, 0xb6, 0xff, 0x9c, 0xa3, 0xb0, 0xff, 0x85, 0x8b, 0x98, 0xff, 0x87, 0x8d, 0x9a, 0xff, 0x88, 0x8e, 0x9a, 0xff, 0x8d, 0x93, 0x9f, 0xff, 0x81, 0x87, 0x93, 0xff, 0x77, 0x80, 0x8c, 0xff, 0x6c, 0x76, 0x84, 0xff, 0x75, 0x82, 0x90, 0xff, 0x71, 0x7d, 0x8c, 0xff, 0x65, 0x76, 0x82, 0xff, 0x4f, 0x60, 0x6c, 0xff, 0x4e, 0x5c, 0x6b, 0xff, 0x5d, 0x74, 0x84, 0xff, 0x5c, 0x79, 0x89, 0xff, 0x3f, 0x51, 0x63, 0xff, 0x48, 0x5b, 0x6d, 0xff, 0x57, 0x70, 0x82, 0xff, 0x34, 0x4c, 0x5e, 0xff, 0x25, 0x3d, 0x51, 0xff, 0x26, 0x3e, 0x52, 0xff, 0x2b, 0x43, 0x56, 0xff, 0x6b, 0x8b, 0xa3, 0xff, 0x71, 0x96, 0xaf, 0xff, 0x82, 0xa7, 0xbd, 0xff, 0x91, 0xb7, 0xc9, 0xff, 0x2e, 0x52, 0x65, 0xff, 0x4a, 0x6a, 0x81, 0xff, 0x8e, 0xb2, 0xc8, 0xff, 0x81, 0xa7, 0xb8, 0xff, 0x7e, 0x9c, 0xaf, 0xff, 0x41, 0x59, 0x6a, 0xff, 0x33, 0x47, 0x58, 0xff, 0x29, 0x3b, 0x4e, 0xff, 0x0a, 0x18, 0x21, 0xff, 0x09, 0x13, 0x17, 0xff, 0x2a, 0x3f, 0x4c, 0xff, 0x40, 0x5f, 0x71, 0xff, 0x5e, 0x81, 0x95, 0xff, 0x7c, 0xa5, 0xb7, 0xff, 0x7c, 0x9f, 0xa9, 0xff, 0x9d, 0xaf, 0xb1, 0xff, 0x9f, 0xa3, 0xa4, 0xff, 0x9c, 0x9d, 0x9a, 0xff, 0x9a, 0xa0, 0x9a, 0xff, 0x9e, 0x9e, 0x9a, 0xff, 0x9e, 0x9d, 0x99, 0xff, 0x9c, 0xa1, 0x9b, 0xff, 0xa0, 0xa1, 0x9d, 0xff, 0xa2, 0xa3, 0x9f, 0xff, 0xa7, 0xa8, 0xa4, 0xff, 0xac, 0xab, 0xa7, 0xff, 0xad, 0xad, 0xa9, 0xff, 0xad, 0xae, 0xaa, 0xff, 0xaf, 0xb0, 0xac, 0xff, 0xb0, 0xb1, 0xad, 0xff, 0xb3, 0xb4, 0xb0, 0xff, 0xb5, 0xb6, 0xb2, 0xff, 0xb6, 0xb7, 0xb3, 0xff, 0xb6, 0xb9, 0xb2, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0xa6, 0xb8, 0x8d, 0x9a, 0xbd, 0xd1, 0xff, 0x83, 0xaa, 0xc0, 0xff, 0x51, 0x78, 0x8f, 0xff, 0x6b, 0x90, 0xa6, 0xff, 0x78, 0x9c, 0xb1, 0xff, 0x75, 0x97, 0xaf, 0xff, 0x57, 0x79, 0x90, 0xff, 0x22, 0x43, 0x5a, 0xff, 0x35, 0x51, 0x6a, 0xff, 0x48, 0x65, 0x7e, 0xff, 0x37, 0x5b, 0x72, 0xff, 0x20, 0x41, 0x54, 0xff, 0x23, 0x37, 0x4a, 0xff, 0x0a, 0x0e, 0x15, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x39, 0x44, 0x51, 0xff, 0xb2, 0xc0, 0xd0, 0xff, 0xd0, 0xd7, 0xdf, 0xff, 0xcf, 0xd7, 0xe0, 0xff, 0xc1, 0xcc, 0xd6, 0xff, 0xc6, 0xcf, 0xd9, 0xff, 0xc5, 0xce, 0xd8, 0xff, 0xbf, 0xc5, 0xcf, 0xff, 0xba, 0xbf, 0xca, 0xff, 0xb8, 0xbe, 0xc8, 0xff, 0xbc, 0xc2, 0xcc, 0xff, 0xad, 0xb3, 0xbe, 0xff, 0xa3, 0xa9, 0xb3, 0xff, 0xa5, 0xaa, 0xb5, 0xff, 0xad, 0xb2, 0xbd, 0xff, 0xb1, 0xb8, 0xc3, 0xff, 0xb8, 0xc0, 0xcc, 0xff, 0xbf, 0xc6, 0xd2, 0xff, 0xbe, 0xc6, 0xd4, 0xff, 0xb9, 0xc3, 0xd1, 0xff, 0xb3, 0xbc, 0xcb, 0xff, 0xad, 0xb8, 0xc6, 0xff, 0xae, 0xb8, 0xc7, 0xff, 0xac, 0xb5, 0xc4, 0xff, 0xaf, 0xb7, 0xc4, 0xff, 0xb4, 0xbc, 0xc7, 0xff, 0xb7, 0xbe, 0xca, 0xff, 0xb8, 0xc0, 0xca, 0xff, 0xb6, 0xc0, 0xca, 0xff, 0xb4, 0xbd, 0xc7, 0xff, 0xb7, 0xc1, 0xca, 0xff, 0xb7, 0xc0, 0xca, 0xff, 0xb1, 0xba, 0xc4, 0xff, 0xb1, 0xbb, 0xc4, 0xff, 0xb1, 0xbb, 0xc4, 0xff, 0xb0, 0xba, 0xc3, 0xff, 0xaf, 0xb8, 0xc3, 0xff, 0xab, 0xb3, 0xc0, 0xff, 0xa2, 0xad, 0xba, 0xff, 0x9f, 0xaa, 0xb7, 0xff, 0xa0, 0xa9, 0xb7, 0xff, 0x9b, 0xa4, 0xb1, 0xff, 0x93, 0x9b, 0xa9, 0xff, 0x91, 0x98, 0xa6, 0xff, 0x87, 0x8e, 0x9d, 0xff, 0x81, 0x85, 0x91, 0xff, 0x78, 0x78, 0x81, 0xff, 0x5a, 0x5b, 0x64, 0xff, 0x3f, 0x40, 0x4a, 0xff, 0x38, 0x3a, 0x41, 0xff, 0x3e, 0x40, 0x47, 0xff, 0x3f, 0x41, 0x47, 0xff, 0x40, 0x41, 0x48, 0xff, 0x3b, 0x3e, 0x45, 0xff, 0x42, 0x47, 0x50, 0xff, 0x60, 0x67, 0x72, 0xff, 0x5c, 0x64, 0x70, 0xff, 0x59, 0x64, 0x70, 0xff, 0x4a, 0x55, 0x61, 0xff, 0x43, 0x4d, 0x5b, 0xff, 0x5f, 0x76, 0x84, 0xff, 0x60, 0x7e, 0x8d, 0xff, 0x4d, 0x62, 0x73, 0xff, 0x3a, 0x4e, 0x60, 0xff, 0x61, 0x7b, 0x8c, 0xff, 0x45, 0x5d, 0x6e, 0xff, 0x26, 0x3e, 0x51, 0xff, 0x22, 0x3a, 0x4f, 0xff, 0x2a, 0x3f, 0x52, 0xff, 0x45, 0x62, 0x79, 0xff, 0x7b, 0xa0, 0xbb, 0xff, 0x73, 0x9b, 0xb1, 0xff, 0x8c, 0xb6, 0xc8, 0xff, 0x67, 0x8d, 0xa1, 0xff, 0x2a, 0x4a, 0x63, 0xff, 0x6b, 0x8f, 0xa6, 0xff, 0x8a, 0xb1, 0xc3, 0xff, 0x8e, 0xad, 0xc0, 0xff, 0x5c, 0x75, 0x88, 0xff, 0x20, 0x35, 0x48, 0xff, 0x3d, 0x54, 0x69, 0xff, 0x24, 0x38, 0x43, 0xff, 0x10, 0x1d, 0x22, 0xff, 0x0c, 0x18, 0x24, 0xff, 0x2d, 0x41, 0x51, 0xff, 0x53, 0x74, 0x85, 0xff, 0x7d, 0xa4, 0xb4, 0xff, 0x80, 0xa5, 0xb0, 0xff, 0x9a, 0xae, 0xb2, 0xff, 0x9e, 0xa0, 0xa3, 0xff, 0x9f, 0x9d, 0x9c, 0xff, 0x99, 0x9d, 0x98, 0xff, 0x9b, 0x9b, 0x97, 0xff, 0x9a, 0x9b, 0x96, 0xff, 0x98, 0x9e, 0x99, 0xff, 0x9e, 0x9e, 0x9a, 0xff, 0x9f, 0xa0, 0x9c, 0xff, 0xa4, 0xa5, 0xa1, 0xff, 0xa7, 0xa6, 0xa2, 0xff, 0xa8, 0xa8, 0xa4, 0xff, 0xa8, 0xaa, 0xa6, 0xff, 0xaa, 0xab, 0xa7, 0xff, 0xac, 0xad, 0xa9, 0xff, 0xaf, 0xb0, 0xac, 0xff, 0xb2, 0xb3, 0xaf, 0xff, 0xb3, 0xb4, 0xaf, 0xff, 0xb4, 0xb4, 0xb1, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0xa7, 0xbc, 0xd7, 0x8d, 0xb1, 0xc6, 0xff, 0x7f, 0xa6, 0xbc, 0xff, 0x5d, 0x83, 0x9a, 0xff, 0x70, 0x94, 0xa9, 0xff, 0x7a, 0x9c, 0xaf, 0xff, 0x79, 0x9b, 0xb1, 0xff, 0x4e, 0x72, 0x89, 0xff, 0x20, 0x42, 0x58, 0xff, 0x31, 0x4d, 0x66, 0xff, 0x46, 0x62, 0x7d, 0xff, 0x39, 0x5e, 0x75, 0xff, 0x20, 0x42, 0x55, 0xff, 0x22, 0x38, 0x4a, 0xff, 0x11, 0x15, 0x1b, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x29, 0x34, 0x42, 0xff, 0xad, 0xba, 0xcb, 0xff, 0xcf, 0xd6, 0xde, 0xff, 0xcf, 0xd8, 0xe0, 0xff, 0xc9, 0xd4, 0xdf, 0xff, 0xbf, 0xc8, 0xd2, 0xff, 0xb3, 0xba, 0xc5, 0xff, 0xb5, 0xbb, 0xc4, 0xff, 0xba, 0xbf, 0xc7, 0xff, 0xab, 0xae, 0xb7, 0xff, 0x9a, 0x9d, 0xa6, 0xff, 0x7a, 0x7d, 0x87, 0xff, 0x5a, 0x5e, 0x68, 0xff, 0x5e, 0x61, 0x6b, 0xff, 0x53, 0x57, 0x60, 0xff, 0x56, 0x5a, 0x62, 0xff, 0x53, 0x56, 0x5f, 0xff, 0x5e, 0x61, 0x6c, 0xff, 0x76, 0x7b, 0x87, 0xff, 0x9c, 0xa2, 0xaf, 0xff, 0xb1, 0xb7, 0xc6, 0xff, 0xb8, 0xbe, 0xcc, 0xff, 0xb3, 0xb9, 0xc6, 0xff, 0xb3, 0xba, 0xc6, 0xff, 0xb3, 0xbb, 0xc6, 0xff, 0xb5, 0xbe, 0xc7, 0xff, 0xb8, 0xc1, 0xcb, 0xff, 0xb8, 0xc1, 0xcb, 0xff, 0xb9, 0xc2, 0xcc, 0xff, 0xb8, 0xc1, 0xcb, 0xff, 0xb8, 0xc1, 0xcb, 0xff, 0xb6, 0xbf, 0xca, 0xff, 0xb2, 0xbb, 0xc5, 0xff, 0xb2, 0xbb, 0xc4, 0xff, 0xb1, 0xba, 0xc4, 0xff, 0xae, 0xb8, 0xc2, 0xff, 0xad, 0xb6, 0xc0, 0xff, 0xa5, 0xb0, 0xbc, 0xff, 0x9f, 0xab, 0xb8, 0xff, 0x98, 0xa3, 0xaf, 0xff, 0x92, 0x9b, 0xa8, 0xff, 0x8a, 0x93, 0xa0, 0xff, 0x89, 0x92, 0x9f, 0xff, 0x84, 0x8b, 0x98, 0xff, 0x6c, 0x73, 0x81, 0xff, 0x45, 0x46, 0x50, 0xff, 0x29, 0x26, 0x2b, 0xff, 0x18, 0x17, 0x1c, 0xff, 0x1a, 0x1a, 0x1e, 0xff, 0x32, 0x32, 0x34, 0xff, 0x20, 0x20, 0x23, 0xff, 0x0e, 0x0c, 0x0d, 0xff, 0x0c, 0x08, 0x09, 0xff, 0x08, 0x08, 0x0b, 0xff, 0x15, 0x18, 0x1d, 0xff, 0x2b, 0x2f, 0x37, 0xff, 0x3d, 0x45, 0x4d, 0xff, 0x41, 0x48, 0x54, 0xff, 0x3a, 0x3d, 0x4c, 0xff, 0x23, 0x29, 0x37, 0xff, 0x43, 0x59, 0x66, 0xff, 0x59, 0x7a, 0x88, 0xff, 0x5e, 0x78, 0x87, 0xff, 0x33, 0x4a, 0x5a, 0xff, 0x5c, 0x76, 0x86, 0xff, 0x56, 0x6f, 0x81, 0xff, 0x2e, 0x47, 0x58, 0xff, 0x1d, 0x34, 0x48, 0xff, 0x35, 0x47, 0x5b, 0xff, 0x33, 0x4d, 0x66, 0xff, 0x6a, 0x8f, 0xab, 0xff, 0x73, 0x9a, 0xb3, 0xff, 0x82, 0xad, 0xc1, 0xff, 0x86, 0xac, 0xc1, 0xff, 0x3d, 0x5b, 0x75, 0xff, 0x48, 0x6b, 0x82, 0xff, 0x8d, 0xb6, 0xc8, 0xff, 0x8c, 0xad, 0xc1, 0xff, 0x7c, 0x95, 0xaa, 0xff, 0x39, 0x50, 0x65, 0xff, 0x2e, 0x48, 0x5f, 0xff, 0x3d, 0x53, 0x63, 0xff, 0x29, 0x37, 0x3f, 0xff, 0x1f, 0x26, 0x32, 0xff, 0x08, 0x18, 0x28, 0xff, 0x39, 0x58, 0x65, 0xff, 0x7a, 0xa0, 0xad, 0xff, 0x83, 0xa9, 0xb2, 0xff, 0x92, 0xa8, 0xae, 0xff, 0xa1, 0xa4, 0xa8, 0xff, 0x9f, 0x9d, 0x9d, 0xff, 0x98, 0x9c, 0x97, 0xff, 0x97, 0x98, 0x94, 0xff, 0x98, 0x99, 0x95, 0xff, 0x96, 0x9b, 0x95, 0xff, 0x9b, 0x9c, 0x98, 0xff, 0x9c, 0x9c, 0x99, 0xff, 0x9e, 0x9f, 0x9b, 0xff, 0xa2, 0xa0, 0x9c, 0xff, 0xa2, 0xa2, 0x9e, 0xff, 0xa2, 0xa3, 0x9f, 0xff, 0xa4, 0xa5, 0xa1, 0xff, 0xa8, 0xa9, 0xa5, 0xff, 0xab, 0xac, 0xa8, 0xff, 0xad, 0xae, 0xaa, 0xff, 0xaf, 0xb0, 0xab, 0xff, 0xaf, 0xb0, 0xad, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x7f, 0xa6, 0xba, 0x1a, 0x8a, 0xad, 0xc4, 0xff, 0x83, 0xa5, 0xbd, 0xff, 0x7f, 0xa3, 0xb6, 0xff, 0x67, 0x8b, 0x9c, 0xff, 0x72, 0x96, 0xa8, 0xff, 0x75, 0x9a, 0xac, 0xff, 0x7c, 0x9f, 0xb2, 0xff, 0x44, 0x68, 0x7c, 0xff, 0x22, 0x43, 0x58, 0xff, 0x2a, 0x4a, 0x5f, 0xff, 0x3e, 0x60, 0x76, 0xff, 0x39, 0x5c, 0x71, 0xff, 0x28, 0x45, 0x5a, 0xff, 0x26, 0x3e, 0x54, 0xff, 0x10, 0x18, 0x24, 0xff, 0x01, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x1d, 0x28, 0x35, 0xff, 0xa5, 0xb7, 0xcc, 0xff, 0xcd, 0xd8, 0xe0, 0xff, 0xcf, 0xd6, 0xdc, 0xff, 0xce, 0xd6, 0xde, 0xff, 0xb5, 0xbd, 0xc5, 0xff, 0xa3, 0xaa, 0xb2, 0xff, 0xb0, 0xb6, 0xbe, 0xff, 0xa5, 0xab, 0xb2, 0xff, 0x88, 0x87, 0x95, 0xff, 0x52, 0x4f, 0x5c, 0xff, 0x52, 0x52, 0x59, 0xff, 0x3c, 0x3e, 0x41, 0xff, 0x2b, 0x2e, 0x31, 0xff, 0x1f, 0x20, 0x25, 0xff, 0x18, 0x18, 0x1b, 0xff, 0x1a, 0x1a, 0x1b, 0xff, 0x18, 0x18, 0x1c, 0xff, 0x22, 0x23, 0x27, 0xff, 0x37, 0x36, 0x3f, 0xff, 0x60, 0x5e, 0x68, 0xff, 0xa0, 0x9f, 0xab, 0xff, 0xb8, 0xbb, 0xc9, 0xff, 0xaa, 0xb1, 0xbe, 0xff, 0xad, 0xb7, 0xc2, 0xff, 0xb3, 0xbe, 0xc6, 0xff, 0xbb, 0xc5, 0xcb, 0xff, 0xbd, 0xc8, 0xce, 0xff, 0xbc, 0xc5, 0xce, 0xff, 0xbd, 0xc6, 0xcf, 0xff, 0xbd, 0xc6, 0xcf, 0xff, 0xba, 0xc3, 0xcc, 0xff, 0xb8, 0xc1, 0xcc, 0xff, 0xb6, 0xbf, 0xc8, 0xff, 0xb1, 0xba, 0xc4, 0xff, 0xad, 0xb7, 0xc4, 0xff, 0xa7, 0xb2, 0xbf, 0xff, 0x9e, 0xa8, 0xb6, 0xff, 0x96, 0x9f, 0xab, 0xff, 0x8e, 0x95, 0xa2, 0xff, 0x86, 0x8c, 0x9b, 0xff, 0x81, 0x87, 0x96, 0xff, 0x7d, 0x82, 0x90, 0xff, 0x6e, 0x6e, 0x7a, 0xff, 0x42, 0x40, 0x49, 0xff, 0x2f, 0x2a, 0x2c, 0xff, 0x4d, 0x47, 0x45, 0xff, 0x43, 0x3f, 0x40, 0xff, 0x59, 0x56, 0x57, 0xff, 0x4f, 0x4d, 0x4f, 0xff, 0x3c, 0x3c, 0x3f, 0xff, 0x33, 0x31, 0x31, 0xff, 0x29, 0x23, 0x21, 0xff, 0x1b, 0x18, 0x17, 0xff, 0x04, 0x04, 0x05, 0xff, 0x0a, 0x0a, 0x0d, 0xff, 0x1c, 0x1e, 0x22, 0xff, 0x2d, 0x30, 0x37, 0xff, 0x2e, 0x33, 0x3c, 0xff, 0x11, 0x17, 0x21, 0xff, 0x30, 0x3c, 0x48, 0xff, 0x60, 0x73, 0x81, 0xff, 0x67, 0x80, 0x8f, 0xff, 0x35, 0x4b, 0x5c, 0xff, 0x56, 0x69, 0x7a, 0xff, 0x66, 0x7e, 0x8f, 0xff, 0x39, 0x53, 0x64, 0xff, 0x1c, 0x34, 0x46, 0xff, 0x32, 0x47, 0x59, 0xff, 0x36, 0x4e, 0x63, 0xff, 0x58, 0x76, 0x90, 0xff, 0x6c, 0x93, 0xab, 0xff, 0x79, 0xa5, 0xbb, 0xff, 0x90, 0xba, 0xcf, 0xff, 0x5d, 0x81, 0x93, 0xff, 0x22, 0x45, 0x5c, 0xff, 0x71, 0x94, 0xae, 0xff, 0x9a, 0xbb, 0xce, 0xff, 0x7d, 0x9b, 0xab, 0xff, 0x5b, 0x78, 0x88, 0xff, 0x31, 0x4e, 0x61, 0xff, 0x38, 0x52, 0x63, 0xff, 0x24, 0x37, 0x45, 0xff, 0x31, 0x3f, 0x4e, 0xff, 0x00, 0x0a, 0x1a, 0xff, 0x15, 0x2d, 0x3d, 0xff, 0x5b, 0x82, 0x96, 0xff, 0x7b, 0xa3, 0xb3, 0xff, 0x89, 0xa3, 0xaa, 0xff, 0x9b, 0xa7, 0xa9, 0xff, 0x9b, 0x99, 0x96, 0xff, 0x9d, 0x9c, 0x97, 0xff, 0x94, 0x99, 0x93, 0xff, 0x95, 0x98, 0x94, 0xff, 0x97, 0x97, 0x93, 0xff, 0x98, 0x9a, 0x96, 0xff, 0x97, 0x97, 0x94, 0xff, 0x9a, 0x99, 0x95, 0xff, 0x9c, 0x9b, 0x97, 0xff, 0x9c, 0x9b, 0x97, 0xff, 0x9e, 0x9d, 0x99, 0xff, 0xa0, 0xa0, 0x9c, 0xff, 0xa2, 0xa2, 0x9e, 0xff, 0xa4, 0xa5, 0xa1, 0xff, 0xa6, 0xa7, 0xa3, 0xff, 0xa8, 0xa9, 0xa6, 0xff, 0xaa, 0xab, 0xa9, 0xff, 0xaa, 0xaa, 0xaa, 0x1b, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x7f, 0xa5, 0xb9, 0x58, 0x81, 0xa6, 0xba, 0xff, 0x83, 0xa6, 0xbb, 0xff, 0x85, 0xa8, 0xb8, 0xff, 0x72, 0x97, 0xa5, 0xff, 0x73, 0x97, 0xa7, 0xff, 0x77, 0x9c, 0xae, 0xff, 0x7b, 0xa0, 0xb2, 0xff, 0x42, 0x65, 0x79, 0xff, 0x24, 0x43, 0x58, 0xff, 0x21, 0x43, 0x56, 0xff, 0x3e, 0x63, 0x75, 0xff, 0x42, 0x63, 0x77, 0xff, 0x2d, 0x48, 0x5e, 0xff, 0x21, 0x3b, 0x52, 0xff, 0x10, 0x1c, 0x2d, 0xff, 0x03, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x15, 0x1f, 0x2c, 0xff, 0x9b, 0xb0, 0xc5, 0xff, 0xc9, 0xd4, 0xdd, 0xff, 0xcd, 0xd5, 0xda, 0xff, 0xd1, 0xdb, 0xe1, 0xff, 0xc2, 0xcb, 0xd1, 0xff, 0x96, 0x9c, 0xa3, 0xff, 0x9e, 0xa1, 0xa8, 0xff, 0x81, 0x84, 0x8b, 0xff, 0x44, 0x43, 0x4e, 0xff, 0x4a, 0x49, 0x52, 0xff, 0x4d, 0x4c, 0x4e, 0xff, 0x17, 0x16, 0x14, 0xff, 0x00, 0x00, 0x00, 0xff, 0x28, 0x22, 0x22, 0xff, 0x21, 0x20, 0x1e, 0xff, 0x43, 0x45, 0x42, 0xff, 0x60, 0x61, 0x5f, 0xff, 0x5b, 0x5d, 0x5b, 0xff, 0x39, 0x3b, 0x3b, 0xff, 0x20, 0x23, 0x23, 0xff, 0x37, 0x36, 0x3c, 0xff, 0x83, 0x83, 0x8e, 0xff, 0xad, 0xb2, 0xbd, 0xff, 0xa7, 0xaf, 0xba, 0xff, 0xa9, 0xb2, 0xbb, 0xff, 0xbb, 0xc3, 0xc8, 0xff, 0xc1, 0xcb, 0xd1, 0xff, 0xc1, 0xcc, 0xd3, 0xff, 0xc4, 0xcd, 0xd6, 0xff, 0xc2, 0xcb, 0xd4, 0xff, 0xbe, 0xc7, 0xd0, 0xff, 0xba, 0xc3, 0xcd, 0xff, 0xb6, 0xbf, 0xc8, 0xff, 0xb2, 0xbb, 0xc5, 0xff, 0xac, 0xb6, 0xc4, 0xff, 0xa2, 0xae, 0xbd, 0xff, 0x97, 0xa0, 0xae, 0xff, 0x87, 0x8f, 0x9d, 0xff, 0x81, 0x85, 0x91, 0xff, 0x83, 0x84, 0x90, 0xff, 0x82, 0x84, 0x93, 0xff, 0x79, 0x7b, 0x88, 0xff, 0x50, 0x4e, 0x57, 0xff, 0x31, 0x2a, 0x31, 0xff, 0x89, 0x7f, 0x7c, 0xff, 0x9e, 0x93, 0x8c, 0xff, 0x84, 0x7c, 0x76, 0xff, 0x91, 0x8a, 0x87, 0xff, 0x19, 0x14, 0x13, 0xff, 0x35, 0x31, 0x31, 0xff, 0x59, 0x55, 0x53, 0xff, 0x41, 0x3b, 0x38, 0xff, 0x51, 0x4d, 0x4c, 0xff, 0x2e, 0x2b, 0x2a, 0xff, 0x0c, 0x0b, 0x0b, 0xff, 0x13, 0x11, 0x14, 0xff, 0x14, 0x15, 0x19, 0xff, 0x21, 0x25, 0x28, 0xff, 0x03, 0x07, 0x0f, 0xff, 0x27, 0x30, 0x3d, 0xff, 0x6d, 0x7b, 0x8c, 0xff, 0x6b, 0x85, 0x96, 0xff, 0x3b, 0x51, 0x63, 0xff, 0x4c, 0x5e, 0x6e, 0xff, 0x66, 0x7d, 0x8d, 0xff, 0x42, 0x5c, 0x6c, 0xff, 0x21, 0x3b, 0x4c, 0xff, 0x23, 0x39, 0x4a, 0xff, 0x26, 0x3b, 0x4f, 0xff, 0x51, 0x6c, 0x83, 0xff, 0x65, 0x8b, 0xa1, 0xff, 0x67, 0x91, 0xaa, 0xff, 0x89, 0xb4, 0xc9, 0xff, 0x79, 0x9f, 0xb1, 0xff, 0x19, 0x3d, 0x53, 0xff, 0x4c, 0x6d, 0x89, 0xff, 0x96, 0xb8, 0xcb, 0xff, 0x84, 0xa4, 0xb4, 0xff, 0x74, 0x94, 0xa3, 0xff, 0x3d, 0x5c, 0x6e, 0xff, 0x41, 0x5e, 0x70, 0xff, 0x2a, 0x40, 0x4f, 0xff, 0x35, 0x46, 0x55, 0xff, 0x10, 0x1d, 0x2c, 0xff, 0x00, 0x14, 0x27, 0xff, 0x37, 0x5c, 0x76, 0xff, 0x75, 0x9d, 0xb1, 0xff, 0x95, 0xb0, 0xb8, 0xff, 0x9b, 0xa9, 0xac, 0xff, 0x99, 0x99, 0x95, 0xff, 0x9a, 0x98, 0x92, 0xff, 0x93, 0x98, 0x93, 0xff, 0x90, 0x94, 0x90, 0xff, 0x95, 0x95, 0x91, 0xff, 0x96, 0x97, 0x92, 0xff, 0x93, 0x93, 0x8f, 0xff, 0x95, 0x94, 0x8f, 0xff, 0x97, 0x96, 0x92, 0xff, 0x97, 0x96, 0x91, 0xff, 0x99, 0x98, 0x93, 0xff, 0x9a, 0x98, 0x94, 0xff, 0x9a, 0x9a, 0x96, 0xff, 0x9b, 0x9c, 0x98, 0xff, 0x9d, 0x9e, 0x9a, 0xff, 0x9e, 0x9f, 0x9c, 0xff, 0xa1, 0xa2, 0xa1, 0xff, 0xa2, 0xa2, 0xa2, 0x58, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x7f, 0xaa, 0xbd, 0x90, 0x6e, 0x98, 0xaa, 0xff, 0x7a, 0xa1, 0xb4, 0xff, 0x8d, 0xb1, 0xc2, 0xff, 0x7b, 0x9f, 0xae, 0xff, 0x69, 0x8d, 0x9e, 0xff, 0x77, 0x9b, 0xad, 0xff, 0x7e, 0xa2, 0xb4, 0xff, 0x4b, 0x6e, 0x82, 0xff, 0x25, 0x46, 0x5a, 0xff, 0x1e, 0x3f, 0x53, 0xff, 0x41, 0x61, 0x76, 0xff, 0x55, 0x74, 0x88, 0xff, 0x30, 0x4d, 0x62, 0xff, 0x1f, 0x3c, 0x52, 0xff, 0x12, 0x23, 0x34, 0xff, 0x03, 0x03, 0x04, 0xff, 0x00, 0x00, 0x00, 0xff, 0x15, 0x1f, 0x2b, 0xff, 0x95, 0xa8, 0xbb, 0xff, 0xca, 0xd4, 0xdd, 0xff, 0xc9, 0xd5, 0xda, 0xff, 0xc5, 0xd4, 0xda, 0xff, 0xd6, 0xe0, 0xe7, 0xff, 0xac, 0xb1, 0xb9, 0xff, 0x57, 0x58, 0x60, 0xff, 0x40, 0x3f, 0x48, 0xff, 0x37, 0x36, 0x3b, 0xff, 0x28, 0x29, 0x2b, 0xff, 0x06, 0x04, 0x02, 0xff, 0x32, 0x2b, 0x26, 0xff, 0x98, 0x8c, 0x87, 0xff, 0x89, 0x79, 0x74, 0xff, 0x64, 0x5b, 0x57, 0xff, 0x87, 0x84, 0x80, 0xff, 0x38, 0x35, 0x30, 0xff, 0x42, 0x41, 0x3b, 0xff, 0x77, 0x77, 0x6f, 0xff, 0x58, 0x5b, 0x51, 0xff, 0x59, 0x57, 0x55, 0xff, 0x42, 0x41, 0x46, 0xff, 0x7f, 0x83, 0x8a, 0xff, 0xaa, 0xb0, 0xb9, 0xff, 0xa8, 0xad, 0xb6, 0xff, 0xb6, 0xb9, 0xc1, 0xff, 0xc0, 0xc8, 0xcf, 0xff, 0xc4, 0xcf, 0xd6, 0xff, 0xc5, 0xce, 0xd7, 0xff, 0xc2, 0xcb, 0xd4, 0xff, 0xbf, 0xc8, 0xd1, 0xff, 0xb6, 0xbf, 0xc9, 0xff, 0xb3, 0xbc, 0xc5, 0xff, 0xb2, 0xbb, 0xc5, 0xff, 0xa8, 0xb2, 0xc0, 0xff, 0x9a, 0xa5, 0xb4, 0xff, 0x88, 0x91, 0x9e, 0xff, 0x79, 0x82, 0x90, 0xff, 0x7d, 0x80, 0x8a, 0xff, 0x85, 0x86, 0x8e, 0xff, 0x84, 0x85, 0x8f, 0xff, 0x52, 0x53, 0x5e, 0xff, 0x36, 0x36, 0x3d, 0xff, 0x7e, 0x7a, 0x81, 0xff, 0xe0, 0xd4, 0xd2, 0xff, 0xb0, 0xa1, 0x98, 0xff, 0x7f, 0x72, 0x6b, 0xff, 0xa5, 0x9a, 0x93, 0xff, 0x46, 0x3e, 0x37, 0xff, 0x5e, 0x55, 0x4d, 0xff, 0x74, 0x6e, 0x69, 0xff, 0x41, 0x3c, 0x3a, 0xff, 0x60, 0x5c, 0x5b, 0xff, 0x46, 0x42, 0x43, 0xff, 0x0f, 0x0d, 0x0f, 0xff, 0x10, 0x0f, 0x12, 0xff, 0x0f, 0x11, 0x14, 0xff, 0x12, 0x15, 0x18, 0xff, 0x20, 0x23, 0x2b, 0xff, 0x3a, 0x45, 0x52, 0xff, 0x5c, 0x6f, 0x81, 0xff, 0x73, 0x90, 0xa5, 0xff, 0x45, 0x5c, 0x6f, 0xff, 0x38, 0x4a, 0x59, 0xff, 0x68, 0x80, 0x8f, 0xff, 0x58, 0x72, 0x82, 0xff, 0x32, 0x4b, 0x5d, 0xff, 0x1e, 0x35, 0x47, 0xff, 0x27, 0x3c, 0x50, 0xff, 0x3d, 0x58, 0x6d, 0xff, 0x65, 0x88, 0x9f, 0xff, 0x59, 0x80, 0x99, 0xff, 0x85, 0xad, 0xc4, 0xff, 0x7e, 0xa5, 0xb9, 0xff, 0x2f, 0x51, 0x66, 0xff, 0x23, 0x44, 0x5c, 0xff, 0x7a, 0x9e, 0xb1, 0xff, 0x90, 0xb2, 0xc4, 0xff, 0x84, 0xa5, 0xb6, 0xff, 0x63, 0x81, 0x94, 0xff, 0x3e, 0x5b, 0x6d, 0xff, 0x49, 0x63, 0x73, 0xff, 0x35, 0x49, 0x59, 0xff, 0x23, 0x35, 0x44, 0xff, 0x00, 0x0d, 0x1f, 0xff, 0x27, 0x4a, 0x60, 0xff, 0x63, 0x8a, 0x9c, 0xff, 0x93, 0xb3, 0xbe, 0xff, 0x9d, 0xad, 0xb1, 0xff, 0x99, 0x9c, 0x9a, 0xff, 0x94, 0x91, 0x8d, 0xff, 0x91, 0x93, 0x90, 0xff, 0x8d, 0x90, 0x8e, 0xff, 0x90, 0x90, 0x8d, 0xff, 0x91, 0x93, 0x90, 0xff, 0x90, 0x90, 0x8d, 0xff, 0x91, 0x8f, 0x8d, 0xff, 0x93, 0x91, 0x8f, 0xff, 0x90, 0x8f, 0x8c, 0xff, 0x92, 0x91, 0x8e, 0xff, 0x94, 0x92, 0x90, 0xff, 0x95, 0x94, 0x92, 0xff, 0x94, 0x95, 0x92, 0xff, 0x95, 0x96, 0x93, 0xff, 0x96, 0x97, 0x94, 0xff, 0x99, 0x9a, 0x97, 0xff, 0x9a, 0x9b, 0x98, 0x90, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x80, 0xac, 0xc1, 0xc6, 0x62, 0x8c, 0x9f, 0xff, 0x70, 0x97, 0xa9, 0xff, 0x91, 0xb4, 0xc5, 0xff, 0x7a, 0x9d, 0xad, 0xff, 0x67, 0x8b, 0x9d, 0xff, 0x78, 0x9c, 0xae, 0xff, 0x78, 0x9b, 0xae, 0xff, 0x4d, 0x70, 0x84, 0xff, 0x2d, 0x4e, 0x62, 0xff, 0x1f, 0x3e, 0x53, 0xff, 0x30, 0x4f, 0x64, 0xff, 0x60, 0x7e, 0x93, 0xff, 0x33, 0x50, 0x65, 0xff, 0x28, 0x48, 0x5e, 0xff, 0x11, 0x28, 0x3d, 0xff, 0x01, 0x04, 0x09, 0xff, 0x00, 0x00, 0x00, 0xff, 0x12, 0x19, 0x23, 0xff, 0x95, 0xa6, 0xba, 0xff, 0xcd, 0xd6, 0xe1, 0xff, 0xc7, 0xd4, 0xda, 0xff, 0xc4, 0xd5, 0xda, 0xff, 0xc9, 0xd3, 0xda, 0xff, 0xc7, 0xca, 0xd2, 0xff, 0x83, 0x83, 0x8c, 0xff, 0x2e, 0x2b, 0x35, 0xff, 0x13, 0x14, 0x16, 0xff, 0x08, 0x09, 0x07, 0xff, 0x41, 0x3d, 0x3c, 0xff, 0xd4, 0xcb, 0xc7, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xad, 0x9a, 0x92, 0xff, 0x8f, 0x7d, 0x77, 0xff, 0xb2, 0xa2, 0x9d, 0xff, 0x34, 0x26, 0x20, 0xff, 0x4b, 0x3f, 0x38, 0xff, 0x9a, 0x90, 0x88, 0xff, 0x6b, 0x61, 0x58, 0xff, 0xa4, 0x9f, 0x9c, 0xff, 0x6b, 0x6a, 0x6d, 0xff, 0x3b, 0x3c, 0x44, 0xff, 0x84, 0x86, 0x90, 0xff, 0xb5, 0xb7, 0xc1, 0xff, 0xb5, 0xb6, 0xbf, 0xff, 0xbe, 0xc6, 0xcd, 0xff, 0xc5, 0xd1, 0xd7, 0xff, 0xc7, 0xd0, 0xd9, 0xff, 0xc4, 0xcd, 0xd6, 0xff, 0xbe, 0xc7, 0xd0, 0xff, 0xb7, 0xc0, 0xca, 0xff, 0xb0, 0xb9, 0xc2, 0xff, 0xab, 0xb4, 0xbe, 0xff, 0xa2, 0xac, 0xba, 0xff, 0x91, 0x9c, 0xaa, 0xff, 0x81, 0x8b, 0x98, 0xff, 0x78, 0x80, 0x8e, 0xff, 0x82, 0x87, 0x8f, 0xff, 0x95, 0x96, 0x9d, 0xff, 0x63, 0x66, 0x70, 0xff, 0x26, 0x27, 0x31, 0xff, 0x44, 0x43, 0x4c, 0xff, 0x6d, 0x6b, 0x71, 0xff, 0xa5, 0x9a, 0x9a, 0xff, 0xc0, 0xb3, 0xae, 0xff, 0x6b, 0x5f, 0x5a, 0xff, 0x6a, 0x5f, 0x59, 0xff, 0x78, 0x6e, 0x68, 0xff, 0x68, 0x5e, 0x57, 0xff, 0x45, 0x3e, 0x3c, 0xff, 0x33, 0x30, 0x31, 0xff, 0x2e, 0x2d, 0x2d, 0xff, 0x16, 0x15, 0x17, 0xff, 0x0e, 0x0d, 0x11, 0xff, 0x0d, 0x0d, 0x13, 0xff, 0x17, 0x1a, 0x1e, 0xff, 0x31, 0x35, 0x39, 0xff, 0x4a, 0x4e, 0x57, 0xff, 0x3c, 0x45, 0x53, 0xff, 0x50, 0x64, 0x76, 0xff, 0x75, 0x93, 0xa9, 0xff, 0x56, 0x6d, 0x80, 0xff, 0x1d, 0x2f, 0x3f, 0xff, 0x68, 0x7f, 0x8f, 0xff, 0x63, 0x7d, 0x8d, 0xff, 0x46, 0x5f, 0x71, 0xff, 0x24, 0x3a, 0x4c, 0xff, 0x33, 0x4b, 0x5c, 0xff, 0x27, 0x43, 0x55, 0xff, 0x55, 0x73, 0x8b, 0xff, 0x57, 0x79, 0x92, 0xff, 0x78, 0x9f, 0xb7, 0xff, 0x8b, 0xb4, 0xca, 0xff, 0x57, 0x79, 0x8e, 0xff, 0x18, 0x37, 0x4b, 0xff, 0x50, 0x74, 0x88, 0xff, 0x89, 0xaf, 0xc3, 0xff, 0x86, 0xa8, 0xbc, 0xff, 0x96, 0xb6, 0xc9, 0xff, 0x59, 0x77, 0x8a, 0xff, 0x4f, 0x6b, 0x7d, 0xff, 0x3f, 0x58, 0x68, 0xff, 0x2b, 0x43, 0x53, 0xff, 0x06, 0x20, 0x30, 0xff, 0x16, 0x34, 0x48, 0xff, 0x48, 0x6e, 0x81, 0xff, 0x82, 0xa8, 0xb5, 0xff, 0x9b, 0xaf, 0xb5, 0xff, 0x96, 0x99, 0x99, 0xff, 0x93, 0x90, 0x8d, 0xff, 0x91, 0x93, 0x8f, 0xff, 0x8e, 0x90, 0x8f, 0xff, 0x8d, 0x8e, 0x8c, 0xff, 0x90, 0x92, 0x90, 0xff, 0x8f, 0x8e, 0x8d, 0xff, 0x8e, 0x8c, 0x8b, 0xff, 0x90, 0x8e, 0x8d, 0xff, 0x8d, 0x8b, 0x8b, 0xff, 0x8e, 0x8c, 0x8b, 0xff, 0x92, 0x8f, 0x8e, 0xff, 0x92, 0x92, 0x90, 0xff, 0x8f, 0x91, 0x8f, 0xff, 0x91, 0x93, 0x90, 0xff, 0x93, 0x94, 0x93, 0xff, 0x94, 0x95, 0x94, 0xff, 0x95, 0x95, 0x95, 0xc4, 0x00, 0x00, 0x00, 0x00,
    0x00, 0xff, 0xff, 0x01, 0x77, 0xa3, 0xb8, 0xf2, 0x5d, 0x86, 0x98, 0xff, 0x7b, 0x9d, 0xaf, 0xff, 0x8e, 0xb1, 0xc1, 0xff, 0x7b, 0x9f, 0xae, 0xff, 0x68, 0x8c, 0x9e, 0xff, 0x79, 0x9d, 0xaf, 0xff, 0x6f, 0x93, 0xa6, 0xff, 0x54, 0x77, 0x8c, 0xff, 0x38, 0x5a, 0x6e, 0xff, 0x23, 0x40, 0x56, 0xff, 0x22, 0x3f, 0x54, 0xff, 0x5f, 0x7b, 0x90, 0xff, 0x46, 0x64, 0x78, 0xff, 0x27, 0x4b, 0x60, 0xff, 0x18, 0x33, 0x4b, 0xff, 0x00, 0x05, 0x0c, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0d, 0x13, 0x1c, 0xff, 0x94, 0xa2, 0xb6, 0xff, 0xd0, 0xd8, 0xe4, 0xff, 0xc9, 0xd4, 0xdb, 0xff, 0xca, 0xd6, 0xdc, 0xff, 0xc9, 0xd2, 0xd8, 0xff, 0xba, 0xc0, 0xc7, 0xff, 0xa0, 0xa1, 0xaa, 0xff, 0x63, 0x64, 0x6d, 0xff, 0x44, 0x46, 0x49, 0xff, 0x4b, 0x4f, 0x50, 0xff, 0x3f, 0x3c, 0x42, 0xff, 0x8d, 0x85, 0x89, 0xff, 0xf0, 0xe3, 0xe4, 0xff, 0xf1, 0xe3, 0xde, 0xff, 0x99, 0x88, 0x81, 0xff, 0xad, 0x99, 0x93, 0xff, 0xa3, 0x93, 0x8e, 0xff, 0x9b, 0x8d, 0x8b, 0xff, 0x6f, 0x62, 0x61, 0xff, 0x7d, 0x70, 0x71, 0xff, 0x9b, 0x95, 0x99, 0xff, 0x63, 0x63, 0x6a, 0xff, 0x4a, 0x4c, 0x55, 0xff, 0x5c, 0x60, 0x6a, 0xff, 0xb3, 0xb7, 0xc0, 0xff, 0xbb, 0xbe, 0xc5, 0xff, 0xbc, 0xc4, 0xcb, 0xff, 0xc4, 0xcf, 0xd6, 0xff, 0xc7, 0xd0, 0xd9, 0xff, 0xc5, 0xce, 0xd7, 0xff, 0xbf, 0xc8, 0xd1, 0xff, 0xba, 0xc3, 0xcd, 0xff, 0xb2, 0xbb, 0xc4, 0xff, 0xa7, 0xb0, 0xba, 0xff, 0x9f, 0xa9, 0xb6, 0xff, 0x8f, 0x9a, 0xa9, 0xff, 0x7c, 0x85, 0x93, 0xff, 0x75, 0x7d, 0x8b, 0xff, 0x86, 0x8b, 0x93, 0xff, 0x96, 0x99, 0xa0, 0xff, 0x5d, 0x61, 0x6c, 0xff, 0x36, 0x39, 0x45, 0xff, 0x49, 0x4a, 0x54, 0xff, 0x49, 0x4a, 0x52, 0xff, 0x54, 0x50, 0x55, 0xff, 0x73, 0x6e, 0x72, 0xff, 0x5d, 0x58, 0x5c, 0xff, 0x30, 0x2b, 0x2f, 0xff, 0x37, 0x33, 0x36, 0xff, 0x2a, 0x25, 0x27, 0xff, 0x1b, 0x1a, 0x1c, 0xff, 0x12, 0x13, 0x16, 0xff, 0x0e, 0x0f, 0x14, 0xff, 0x1e, 0x20, 0x26, 0xff, 0x24, 0x28, 0x30, 0xff, 0x21, 0x24, 0x2d, 0xff, 0x32, 0x36, 0x41, 0xff, 0x45, 0x4c, 0x56, 0xff, 0x41, 0x49, 0x54, 0xff, 0x2c, 0x37, 0x46, 0xff, 0x52, 0x63, 0x76, 0xff, 0x71, 0x8b, 0x9c, 0xff, 0x6c, 0x81, 0x92, 0xff, 0x19, 0x2b, 0x3c, 0xff, 0x59, 0x70, 0x80, 0xff, 0x66, 0x80, 0x91, 0xff, 0x49, 0x62, 0x74, 0xff, 0x2f, 0x45, 0x57, 0xff, 0x39, 0x51, 0x62, 0xff, 0x27, 0x41, 0x53, 0xff, 0x49, 0x65, 0x7c, 0xff, 0x5d, 0x7c, 0x95, 0xff, 0x61, 0x86, 0x9e, 0xff, 0x94, 0xbf, 0xd7, 0xff, 0x74, 0x97, 0xac, 0xff, 0x1c, 0x3a, 0x4b, 0xff, 0x2b, 0x50, 0x65, 0xff, 0x74, 0x9c, 0xb4, 0xff, 0x7a, 0xa0, 0xb4, 0xff, 0x87, 0xa7, 0xb8, 0xff, 0x87, 0xa6, 0xb8, 0xff, 0x55, 0x74, 0x88, 0xff, 0x39, 0x55, 0x67, 0xff, 0x3a, 0x57, 0x68, 0xff, 0x2a, 0x47, 0x57, 0xff, 0x11, 0x2c, 0x3f, 0xff, 0x2d, 0x53, 0x69, 0xff, 0x6c, 0x96, 0xa7, 0xff, 0x9c, 0xb1, 0xba, 0xff, 0x96, 0x9a, 0x9c, 0xff, 0x90, 0x8e, 0x8c, 0xff, 0x8f, 0x8e, 0x8c, 0xff, 0x90, 0x91, 0x8f, 0xff, 0x8c, 0x8d, 0x8b, 0xff, 0x8e, 0x90, 0x8e, 0xff, 0x8d, 0x8d, 0x8b, 0xff, 0x8c, 0x8a, 0x89, 0xff, 0x8e, 0x8c, 0x8b, 0xff, 0x8d, 0x8b, 0x89, 0xff, 0x8c, 0x8a, 0x89, 0xff, 0x8e, 0x8c, 0x8a, 0xff, 0x8f, 0x8f, 0x8d, 0xff, 0x8d, 0x8f, 0x8d, 0xff, 0x8f, 0x90, 0x8e, 0xff, 0x91, 0x92, 0x90, 0xff, 0x92, 0x91, 0x92, 0xff, 0x92, 0x92, 0x92, 0xf2, 0xff, 0xff, 0xff, 0x01,
    0x73, 0x9a, 0xb1, 0x21, 0x64, 0x8e, 0xa5, 0xff, 0x63, 0x89, 0x9c, 0xff, 0x8d, 0xac, 0xbd, 0xff, 0x84, 0xa7, 0xb6, 0xff, 0x75, 0x9a, 0xa9, 0xff, 0x69, 0x8d, 0x9e, 0xff, 0x7c, 0xa0, 0xb2, 0xff, 0x5a, 0x7e, 0x91, 0xff, 0x4f, 0x73, 0x87, 0xff, 0x48, 0x6a, 0x7e, 0xff, 0x21, 0x40, 0x55, 0xff, 0x21, 0x3d, 0x52, 0xff, 0x52, 0x6b, 0x80, 0xff, 0x58, 0x76, 0x8a, 0xff, 0x2d, 0x53, 0x67, 0xff, 0x22, 0x3f, 0x59, 0xff, 0x06, 0x0f, 0x18, 0xff, 0x00, 0x00, 0x00, 0xff, 0x05, 0x0c, 0x13, 0xff, 0x8e, 0x9d, 0xaf, 0xff, 0xd3, 0xdb, 0xea, 0xff, 0xd0, 0xd7, 0xde, 0xff, 0xd0, 0xd7, 0xde, 0xff, 0xce, 0xd5, 0xdc, 0xff, 0xca, 0xd0, 0xd8, 0xff, 0xb9, 0xbd, 0xc4, 0xff, 0xa9, 0xaf, 0xb6, 0xff, 0xad, 0xb0, 0xb7, 0xff, 0x9b, 0x9d, 0xa5, 0xff, 0x5b, 0x58, 0x64, 0xff, 0x2e, 0x27, 0x31, 0xff, 0x53, 0x4c, 0x50, 0xff, 0x9c, 0x94, 0x93, 0xff, 0x88, 0x7f, 0x75, 0xff, 0x4d, 0x44, 0x3b, 0xff, 0x68, 0x62, 0x5d, 0xff, 0x5a, 0x55, 0x56, 0xff, 0x50, 0x4c, 0x52, 0xff, 0x6f, 0x6d, 0x75, 0xff, 0x5a, 0x58, 0x62, 0xff, 0x55, 0x53, 0x61, 0xff, 0x69, 0x6b, 0x79, 0xff, 0x82, 0x88, 0x94, 0xff, 0xb6, 0xbd, 0xc5, 0xff, 0xba, 0xbf, 0xc4, 0xff, 0xb8, 0xc0, 0xc6, 0xff, 0xc1, 0xcc, 0xd4, 0xff, 0xc6, 0xcf, 0xd8, 0xff, 0xc4, 0xcc, 0xd5, 0xff, 0xbf, 0xc8, 0xd2, 0xff, 0xb8, 0xc2, 0xcc, 0xff, 0xb1, 0xba, 0xc3, 0xff, 0xa8, 0xb0, 0xbb, 0xff, 0x98, 0xa2, 0xb0, 0xff, 0x8a, 0x96, 0xa4, 0xff, 0x79, 0x82, 0x8f, 0xff, 0x77, 0x7f, 0x8c, 0xff, 0x86, 0x8c, 0x95, 0xff, 0x8b, 0x8f, 0x97, 0xff, 0x89, 0x8f, 0x9c, 0xff, 0x82, 0x8a, 0x98, 0xff, 0x76, 0x7c, 0x88, 0xff, 0x7e, 0x81, 0x8c, 0xff, 0x86, 0x87, 0x95, 0xff, 0x72, 0x74, 0x84, 0xff, 0x5c, 0x5f, 0x6d, 0xff, 0x67, 0x68, 0x76, 0xff, 0x5a, 0x5b, 0x66, 0xff, 0x47, 0x49, 0x52, 0xff, 0x4a, 0x4d, 0x53, 0xff, 0x41, 0x44, 0x4a, 0xff, 0x46, 0x49, 0x51, 0xff, 0x49, 0x4e, 0x58, 0xff, 0x48, 0x4e, 0x5a, 0xff, 0x3b, 0x42, 0x4e, 0xff, 0x3f, 0x49, 0x56, 0xff, 0x43, 0x4d, 0x5b, 0xff, 0x47, 0x51, 0x5e, 0xff, 0x34, 0x40, 0x4e, 0xff, 0x45, 0x54, 0x63, 0xff, 0x6a, 0x80, 0x8e, 0xff, 0x7b, 0x90, 0xa0, 0xff, 0x2f, 0x41, 0x52, 0xff, 0x3d, 0x54, 0x64, 0xff, 0x6e, 0x88, 0x98, 0xff, 0x4b, 0x64, 0x76, 0xff, 0x3c, 0x52, 0x65, 0xff, 0x3f, 0x57, 0x68, 0xff, 0x2f, 0x49, 0x5b, 0xff, 0x39, 0x54, 0x6a, 0xff, 0x69, 0x87, 0xa0, 0xff, 0x57, 0x79, 0x92, 0xff, 0x81, 0xad, 0xc6, 0xff, 0x8d, 0xaf, 0xc2, 0xff, 0x25, 0x41, 0x50, 0xff, 0x10, 0x38, 0x4d, 0xff, 0x66, 0x90, 0xa7, 0xff, 0x82, 0xa8, 0xbe, 0xff, 0x6b, 0x8a, 0x9c, 0xff, 0x80, 0x9f, 0xb2, 0xff, 0x6d, 0x8e, 0xa4, 0xff, 0x45, 0x64, 0x76, 0xff, 0x3f, 0x5d, 0x6e, 0xff, 0x3c, 0x5a, 0x6b, 0xff, 0x0e, 0x29, 0x3c, 0xff, 0x15, 0x3c, 0x52, 0xff, 0x58, 0x86, 0x99, 0xff, 0x9a, 0xb0, 0xba, 0xff, 0x99, 0x9e, 0x9e, 0xff, 0x91, 0x8d, 0x8b, 0xff, 0x8d, 0x8b, 0x8a, 0xff, 0x8c, 0x8d, 0x8c, 0xff, 0x91, 0x92, 0x91, 0xff, 0x8c, 0x8d, 0x8d, 0xff, 0x8d, 0x8c, 0x8b, 0xff, 0x8d, 0x8a, 0x8a, 0xff, 0x8e, 0x8c, 0x8c, 0xff, 0x8c, 0x8a, 0x89, 0xff, 0x8c, 0x8a, 0x89, 0xff, 0x8c, 0x8a, 0x89, 0xff, 0x8d, 0x8c, 0x8c, 0xff, 0x8c, 0x8c, 0x8b, 0xff, 0x8c, 0x8c, 0x8b, 0xff, 0x8d, 0x8d, 0x8c, 0xff, 0x8d, 0x8d, 0x8d, 0xff, 0x8e, 0x8e, 0x8e, 0xff, 0x8b, 0x8b, 0x8b, 0x21,
    0x77, 0x9c, 0xb4, 0x4b, 0x65, 0x8b, 0xa1, 0xff, 0x72, 0x95, 0xa9, 0xff, 0x8c, 0xae, 0xc0, 0xff, 0x83, 0xa6, 0xb8, 0xff, 0x6c, 0x91, 0xa3, 0xff, 0x69, 0x8d, 0x9f, 0xff, 0x7b, 0x9e, 0xb2, 0xff, 0x49, 0x6c, 0x80, 0xff, 0x49, 0x6c, 0x80, 0xff, 0x4c, 0x6d, 0x81, 0xff, 0x1a, 0x3a, 0x4e, 0xff, 0x26, 0x40, 0x55, 0xff, 0x45, 0x5f, 0x74, 0xff, 0x5a, 0x75, 0x8b, 0xff, 0x46, 0x67, 0x7c, 0xff, 0x1c, 0x40, 0x5d, 0xff, 0x0d, 0x1f, 0x29, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x06, 0xff, 0x84, 0x92, 0xa2, 0xff, 0xcf, 0xdc, 0xe6, 0xff, 0xce, 0xd9, 0xde, 0xff, 0xcc, 0xd7, 0xdc, 0xff, 0xca, 0xd4, 0xdb, 0xff, 0xc9, 0xd2, 0xd8, 0xff, 0xcb, 0xd5, 0xdc, 0xff, 0xc4, 0xcd, 0xd4, 0xff, 0xbe, 0xc3, 0xc9, 0xff, 0xae, 0xb1, 0xb7, 0xff, 0xbc, 0xbe, 0xc5, 0xff, 0x9d, 0x9c, 0xa3, 0xff, 0x57, 0x54, 0x59, 0xff, 0x42, 0x3d, 0x42, 0xff, 0x59, 0x55, 0x5a, 0xff, 0x6b, 0x67, 0x6f, 0xff, 0x5e, 0x5c, 0x64, 0xff, 0x63, 0x62, 0x6e, 0xff, 0x7a, 0x7b, 0x88, 0xff, 0x6e, 0x71, 0x7f, 0xff, 0x75, 0x75, 0x81, 0xff, 0x85, 0x83, 0x8f, 0xff, 0x98, 0x99, 0xa5, 0xff, 0xac, 0xae, 0xb9, 0xff, 0xaf, 0xb5, 0xbe, 0xff, 0xb3, 0xba, 0xc2, 0xff, 0xba, 0xc2, 0xc9, 0xff, 0xc3, 0xcc, 0xd3, 0xff, 0xc8, 0xd0, 0xd9, 0xff, 0xc7, 0xce, 0xd7, 0xff, 0xc0, 0xc7, 0xd0, 0xff, 0xb8, 0xbe, 0xca, 0xff, 0xb1, 0xb9, 0xc2, 0xff, 0xa5, 0xac, 0xb7, 0xff, 0x95, 0x9f, 0xae, 0xff, 0x86, 0x93, 0xa4, 0xff, 0x77, 0x83, 0x93, 0xff, 0x74, 0x80, 0x90, 0xff, 0x7e, 0x87, 0x94, 0xff, 0x84, 0x8c, 0x97, 0xff, 0x88, 0x8f, 0x9a, 0xff, 0x92, 0x97, 0xa2, 0xff, 0x91, 0x93, 0x9f, 0xff, 0x93, 0x91, 0x9c, 0xff, 0x95, 0x96, 0xa1, 0xff, 0x8e, 0x92, 0x9e, 0xff, 0x87, 0x8a, 0x96, 0xff, 0x88, 0x8b, 0x97, 0xff, 0x83, 0x86, 0x92, 0xff, 0x81, 0x84, 0x92, 0xff, 0x75, 0x7a, 0x88, 0xff, 0x70, 0x77, 0x85, 0xff, 0x66, 0x6c, 0x7a, 0xff, 0x5c, 0x64, 0x72, 0xff, 0x57, 0x5f, 0x6f, 0xff, 0x54, 0x5b, 0x6b, 0xff, 0x49, 0x56, 0x65, 0xff, 0x3e, 0x4d, 0x5d, 0xff, 0x47, 0x50, 0x60, 0xff, 0x43, 0x4b, 0x5b, 0xff, 0x36, 0x45, 0x54, 0xff, 0x5a, 0x74, 0x81, 0xff, 0x73, 0x8d, 0x9d, 0xff, 0x48, 0x60, 0x72, 0xff, 0x2b, 0x45, 0x55, 0xff, 0x73, 0x8d, 0x9e, 0xff, 0x53, 0x6d, 0x7e, 0xff, 0x45, 0x5e, 0x6f, 0xff, 0x38, 0x51, 0x63, 0xff, 0x39, 0x50, 0x64, 0xff, 0x2e, 0x46, 0x5c, 0xff, 0x6b, 0x87, 0x9e, 0xff, 0x59, 0x7c, 0x95, 0xff, 0x67, 0x93, 0xab, 0xff, 0x95, 0xc2, 0xd5, 0xff, 0x3e, 0x5f, 0x6f, 0xff, 0x0c, 0x21, 0x38, 0xff, 0x45, 0x6b, 0x80, 0xff, 0x80, 0xb6, 0xc8, 0xff, 0x78, 0x9d, 0xaf, 0xff, 0x7f, 0x9b, 0xaf, 0xff, 0x7d, 0x9f, 0xb4, 0xff, 0x67, 0x88, 0x9b, 0xff, 0x4d, 0x6d, 0x7f, 0xff, 0x40, 0x61, 0x71, 0xff, 0x17, 0x34, 0x45, 0xff, 0x0d, 0x2e, 0x42, 0xff, 0x4f, 0x76, 0x8a, 0xff, 0x8c, 0xab, 0xb9, 0xff, 0x93, 0xa0, 0xa6, 0xff, 0x92, 0x8e, 0x91, 0xff, 0x90, 0x8b, 0x8b, 0xff, 0x8c, 0x8b, 0x8a, 0xff, 0x90, 0x90, 0x90, 0xff, 0x90, 0x90, 0x90, 0xff, 0x8d, 0x8d, 0x8d, 0xff, 0x8b, 0x8b, 0x8b, 0xff, 0x8d, 0x8d, 0x8d, 0xff, 0x8c, 0x8b, 0x8c, 0xff, 0x88, 0x88, 0x88, 0xff, 0x8a, 0x89, 0x89, 0xff, 0x8a, 0x8a, 0x8a, 0xff, 0x8a, 0x8a, 0x8a, 0xff, 0x8a, 0x8a, 0x89, 0xff, 0x8a, 0x8a, 0x8b, 0xff, 0x8a, 0x8c, 0x8c, 0xff, 0x8a, 0x8c, 0x8c, 0xff, 0x8b, 0x8b, 0x8b, 0x4b,
    0x6d, 0x93, 0xa6, 0x6b, 0x72, 0x94, 0xa9, 0xff, 0x85, 0xa6, 0xb7, 0xff, 0x8d, 0xad, 0xbd, 0xff, 0x7c, 0x9f, 0xb2, 0xff, 0x60, 0x84, 0x96, 0xff, 0x70, 0x94, 0xa7, 0xff, 0x72, 0x96, 0xa9, 0xff, 0x45, 0x68, 0x7c, 0xff, 0x40, 0x62, 0x76, 0xff, 0x4e, 0x6f, 0x83, 0xff, 0x27, 0x45, 0x5a, 0xff, 0x27, 0x42, 0x57, 0xff, 0x3c, 0x56, 0x6b, 0xff, 0x44, 0x5f, 0x73, 0xff, 0x59, 0x78, 0x8d, 0xff, 0x35, 0x5c, 0x77, 0xff, 0x17, 0x2d, 0x3a, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x76, 0x83, 0x91, 0xff, 0xc8, 0xd9, 0xe0, 0xff, 0xcb, 0xd8, 0xde, 0xff, 0xc7, 0xd3, 0xd9, 0xff, 0xc8, 0xd3, 0xda, 0xff, 0xc8, 0xd2, 0xd9, 0xff, 0xc1, 0xcb, 0xd3, 0xff, 0xc3, 0xcd, 0xd5, 0xff, 0xcb, 0xd1, 0xd6, 0xff, 0xca, 0xcf, 0xd2, 0xff, 0xce, 0xd3, 0xd6, 0xff, 0xbe, 0xc0, 0xc4, 0xff, 0xb8, 0xb6, 0xbc, 0xff, 0xad, 0xaa, 0xb0, 0xff, 0x9e, 0x9c, 0xa8, 0xff, 0xa3, 0xa2, 0xb1, 0xff, 0x9a, 0x99, 0xa7, 0xff, 0x98, 0x98, 0xa7, 0xff, 0x8a, 0x8a, 0x9a, 0xff, 0x8d, 0x8d, 0x9c, 0xff, 0x98, 0x98, 0xa3, 0xff, 0x9e, 0x9d, 0xa6, 0xff, 0x9e, 0x9f, 0xa9, 0xff, 0xa5, 0xa6, 0xb0, 0xff, 0xb1, 0xb7, 0xbf, 0xff, 0xb5, 0xbc, 0xc6, 0xff, 0xc0, 0xc7, 0xcf, 0xff, 0xc3, 0xcb, 0xd2, 0xff, 0xc6, 0xcd, 0xd6, 0xff, 0xc6, 0xcd, 0xd6, 0xff, 0xbe, 0xc4, 0xce, 0xff, 0xb7, 0xbc, 0xc8, 0xff, 0xb2, 0xb8, 0xc1, 0xff, 0xa2, 0xaa, 0xb4, 0xff, 0x92, 0x9b, 0xac, 0xff, 0x86, 0x92, 0xa5, 0xff, 0x7a, 0x87, 0x99, 0xff, 0x72, 0x7e, 0x8f, 0xff, 0x76, 0x7f, 0x8e, 0xff, 0x7c, 0x85, 0x92, 0xff, 0x81, 0x89, 0x95, 0xff, 0x8c, 0x8e, 0x99, 0xff, 0x90, 0x8f, 0x9a, 0xff, 0x8e, 0x8d, 0x97, 0xff, 0x91, 0x90, 0x9b, 0xff, 0x92, 0x94, 0x9e, 0xff, 0x97, 0x99, 0xa3, 0xff, 0x90, 0x93, 0x9e, 0xff, 0x8b, 0x8f, 0x9c, 0xff, 0x8f, 0x92, 0xa0, 0xff, 0x7a, 0x80, 0x90, 0xff, 0x71, 0x79, 0x89, 0xff, 0x6e, 0x76, 0x85, 0xff, 0x66, 0x6e, 0x7f, 0xff, 0x67, 0x6f, 0x81, 0xff, 0x61, 0x68, 0x7b, 0xff, 0x56, 0x64, 0x75, 0xff, 0x49, 0x59, 0x69, 0xff, 0x49, 0x53, 0x64, 0xff, 0x4b, 0x53, 0x65, 0xff, 0x28, 0x38, 0x48, 0xff, 0x48, 0x61, 0x6f, 0xff, 0x6a, 0x85, 0x95, 0xff, 0x5d, 0x78, 0x8a, 0xff, 0x29, 0x43, 0x54, 0xff, 0x69, 0x83, 0x94, 0xff, 0x56, 0x70, 0x81, 0xff, 0x4c, 0x66, 0x76, 0xff, 0x36, 0x4f, 0x62, 0xff, 0x3d, 0x53, 0x68, 0xff, 0x2d, 0x45, 0x5a, 0xff, 0x5a, 0x75, 0x8b, 0xff, 0x62, 0x86, 0x9e, 0xff, 0x56, 0x81, 0x99, 0xff, 0x91, 0xc4, 0xd8, 0xff, 0x5f, 0x82, 0x95, 0xff, 0x05, 0x12, 0x29, 0xff, 0x25, 0x47, 0x5b, 0xff, 0x6b, 0xa4, 0xb4, 0xff, 0x8b, 0xb2, 0xc5, 0xff, 0x7c, 0x97, 0xab, 0xff, 0x8b, 0xad, 0xc0, 0xff, 0x5e, 0x7f, 0x93, 0xff, 0x66, 0x86, 0x99, 0xff, 0x62, 0x84, 0x94, 0xff, 0x3c, 0x5d, 0x6b, 0xff, 0x0e, 0x2e, 0x40, 0xff, 0x39, 0x5f, 0x73, 0xff, 0x73, 0x98, 0xa6, 0xff, 0x8d, 0xa0, 0xa9, 0xff, 0x94, 0x94, 0x99, 0xff, 0x91, 0x8d, 0x8d, 0xff, 0x8c, 0x8d, 0x8b, 0xff, 0x8c, 0x8c, 0x8c, 0xff, 0x92, 0x92, 0x92, 0xff, 0x91, 0x91, 0x91, 0xff, 0x8b, 0x8c, 0x8c, 0xff, 0x8e, 0x8e, 0x8e, 0xff, 0x8c, 0x8d, 0x8d, 0xff, 0x8a, 0x8a, 0x8a, 0xff, 0x8c, 0x8c, 0x8c, 0xff, 0x8b, 0x8b, 0x8b, 0xff, 0x8b, 0x8b, 0x8b, 0xff, 0x8b, 0x8b, 0x8b, 0xff, 0x8b, 0x8b, 0x8c, 0xff, 0x8a, 0x8c, 0x8d, 0xff, 0x8a, 0x8c, 0x8d, 0xff, 0x8a, 0x8c, 0x8c, 0x6b,
    0x6c, 0x8f, 0xa3, 0x8b, 0x7d, 0x9d, 0xae, 0xff, 0x8a, 0xa8, 0xb6, 0xff, 0x96, 0xb3, 0xc1, 0xff, 0x6f, 0x92, 0xa4, 0xff, 0x56, 0x7a, 0x8d, 0xff, 0x7c, 0xa0, 0xb2, 0xff, 0x69, 0x8d, 0xa0, 0xff, 0x3a, 0x5d, 0x72, 0xff, 0x3a, 0x5d, 0x71, 0xff, 0x53, 0x74, 0x87, 0xff, 0x33, 0x53, 0x66, 0xff, 0x1f, 0x3c, 0x4f, 0xff, 0x3e, 0x58, 0x6c, 0xff, 0x3a, 0x55, 0x68, 0xff, 0x42, 0x60, 0x73, 0xff, 0x58, 0x7b, 0x93, 0xff, 0x3a, 0x53, 0x63, 0xff, 0x03, 0x09, 0x0e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x67, 0x72, 0x7e, 0xff, 0xcb, 0xdb, 0xe6, 0xff, 0xc7, 0xd3, 0xdc, 0xff, 0xc6, 0xd1, 0xd9, 0xff, 0xc7, 0xd1, 0xda, 0xff, 0xc6, 0xcf, 0xd8, 0xff, 0xc0, 0xc9, 0xd2, 0xff, 0xc1, 0xc9, 0xd2, 0xff, 0xc8, 0xce, 0xd5, 0xff, 0xc9, 0xd0, 0xd5, 0xff, 0xc8, 0xcc, 0xd3, 0xff, 0xc4, 0xc6, 0xcd, 0xff, 0xc4, 0xc5, 0xcc, 0xff, 0xbc, 0xbc, 0xc3, 0xff, 0xb7, 0xbb, 0xc3, 0xff, 0xaa, 0xaf, 0xb9, 0xff, 0xa4, 0xa7, 0xb1, 0xff, 0xa4, 0xa4, 0xb0, 0xff, 0xa0, 0xa0, 0xad, 0xff, 0xa3, 0xa0, 0xb0, 0xff, 0xa3, 0xa2, 0xae, 0xff, 0xa2, 0xa4, 0xad, 0xff, 0xaa, 0xab, 0xb5, 0xff, 0xb3, 0xb7, 0xc0, 0xff, 0xb5, 0xba, 0xc3, 0xff, 0xb8, 0xbf, 0xc8, 0xff, 0xc0, 0xc8, 0xcf, 0xff, 0xc6, 0xce, 0xd5, 0xff, 0xc8, 0xcf, 0xd8, 0xff, 0xc2, 0xca, 0xd2, 0xff, 0xbb, 0xc1, 0xcb, 0xff, 0xb6, 0xbc, 0xc7, 0xff, 0xb4, 0xba, 0xc3, 0xff, 0xa8, 0xae, 0xb9, 0xff, 0x94, 0x9e, 0xae, 0xff, 0x87, 0x93, 0xa6, 0xff, 0x80, 0x8c, 0x9e, 0xff, 0x79, 0x82, 0x94, 0xff, 0x79, 0x83, 0x91, 0xff, 0x79, 0x82, 0x8f, 0xff, 0x7d, 0x85, 0x93, 0xff, 0x88, 0x8d, 0x9a, 0xff, 0x8d, 0x91, 0x9c, 0xff, 0x8e, 0x90, 0x9b, 0xff, 0x91, 0x93, 0x9f, 0xff, 0x91, 0x95, 0x9f, 0xff, 0x8f, 0x93, 0x9e, 0xff, 0x8f, 0x93, 0xa0, 0xff, 0x8a, 0x90, 0x9d, 0xff, 0x80, 0x86, 0x93, 0xff, 0x7c, 0x83, 0x92, 0xff, 0x7e, 0x86, 0x94, 0xff, 0x77, 0x80, 0x90, 0xff, 0x6f, 0x78, 0x89, 0xff, 0x6a, 0x74, 0x85, 0xff, 0x67, 0x71, 0x84, 0xff, 0x60, 0x6d, 0x7e, 0xff, 0x54, 0x64, 0x74, 0xff, 0x4c, 0x58, 0x68, 0xff, 0x4d, 0x59, 0x69, 0xff, 0x2b, 0x3a, 0x4a, 0xff, 0x3e, 0x52, 0x61, 0xff, 0x60, 0x79, 0x89, 0xff, 0x62, 0x7d, 0x8e, 0xff, 0x35, 0x4f, 0x60, 0xff, 0x56, 0x70, 0x81, 0xff, 0x5b, 0x75, 0x86, 0xff, 0x4a, 0x64, 0x75, 0xff, 0x41, 0x5a, 0x6c, 0xff, 0x3a, 0x52, 0x67, 0xff, 0x31, 0x49, 0x5f, 0xff, 0x44, 0x5f, 0x75, 0xff, 0x6a, 0x8d, 0xa5, 0xff, 0x55, 0x7f, 0x97, 0xff, 0x89, 0xb9, 0xcc, 0xff, 0x7e, 0xa3, 0xb6, 0xff, 0x0f, 0x23, 0x38, 0xff, 0x0d, 0x2b, 0x3e, 0xff, 0x5d, 0x8a, 0x9b, 0xff, 0x89, 0xaf, 0xc3, 0xff, 0x70, 0x90, 0xa5, 0xff, 0x89, 0xab, 0xbf, 0xff, 0x76, 0x96, 0xaa, 0xff, 0x5b, 0x7b, 0x8f, 0xff, 0x79, 0x9a, 0xaa, 0xff, 0x62, 0x83, 0x92, 0xff, 0x16, 0x38, 0x4a, 0xff, 0x1a, 0x42, 0x56, 0xff, 0x5b, 0x84, 0x91, 0xff, 0x8c, 0xa4, 0xab, 0xff, 0x95, 0x9b, 0x9e, 0xff, 0x8f, 0x92, 0x90, 0xff, 0x8e, 0x91, 0x8f, 0xff, 0x92, 0x91, 0x91, 0xff, 0x91, 0x91, 0x91, 0xff, 0x93, 0x93, 0x93, 0xff, 0x92, 0x92, 0x92, 0xff, 0x91, 0x91, 0x91, 0xff, 0x8f, 0x8f, 0x8f, 0xff, 0x8e, 0x8e, 0x8e, 0xff, 0x90, 0x90, 0x90, 0xff, 0x8f, 0x8f, 0x8f, 0xff, 0x8e, 0x8e, 0x8e, 0xff, 0x8e, 0x8e, 0x8e, 0xff, 0x8d, 0x8e, 0x8e, 0xff, 0x8c, 0x8e, 0x8f, 0xff, 0x8c, 0x8e, 0x8f, 0xff, 0x8b, 0x8d, 0x8f, 0x8b,
    0x7f, 0xa0, 0xb1, 0xaa, 0x7e, 0x9a, 0xa7, 0xff, 0x98, 0xb3, 0xc1, 0xff, 0x8e, 0xaa, 0xb8, 0xff, 0x63, 0x85, 0x97, 0xff, 0x62, 0x87, 0x99, 0xff, 0x83, 0xa7, 0xb9, 0xff, 0x5c, 0x7f, 0x92, 0xff, 0x3e, 0x61, 0x76, 0xff, 0x33, 0x56, 0x6a, 0xff, 0x4c, 0x6d, 0x80, 0xff, 0x42, 0x62, 0x74, 0xff, 0x1f, 0x39, 0x4d, 0xff, 0x3a, 0x53, 0x66, 0xff, 0x4a, 0x64, 0x78, 0xff, 0x2d, 0x4a, 0x5d, 0xff, 0x4d, 0x6d, 0x82, 0xff, 0x5d, 0x7b, 0x8d, 0xff, 0x12, 0x24, 0x32, 0xff, 0x00, 0x00, 0x00, 0xff, 0x56, 0x5f, 0x67, 0xff, 0xcc, 0xda, 0xe9, 0xff, 0xc5, 0xd1, 0xdb, 0xff, 0xc7, 0xd2, 0xd9, 0xff, 0xc8, 0xd3, 0xda, 0xff, 0xc6, 0xcf, 0xd8, 0xff, 0xc1, 0xca, 0xd3, 0xff, 0xc2, 0xca, 0xd3, 0xff, 0xc5, 0xcb, 0xd3, 0xff, 0xc3, 0xcb, 0xd2, 0xff, 0xc8, 0xce, 0xd6, 0xff, 0xc4, 0xc9, 0xd1, 0xff, 0xc2, 0xc8, 0xcf, 0xff, 0xc1, 0xc6, 0xcd, 0xff, 0xbc, 0xc3, 0xcb, 0xff, 0xb2, 0xba, 0xc3, 0xff, 0xaf, 0xb5, 0xbe, 0xff, 0xac, 0xaf, 0xb9, 0xff, 0xaa, 0xa8, 0xb5, 0xff, 0xaa, 0xa8, 0xb5, 0xff, 0xa8, 0xa9, 0xb3, 0xff, 0xaf, 0xb2, 0xbb, 0xff, 0xb7, 0xbb, 0xc4, 0xff, 0xb8, 0xbd, 0xc6, 0xff, 0xb8, 0xbc, 0xc6, 0xff, 0xc0, 0xc6, 0xcf, 0xff, 0xc4, 0xcb, 0xd3, 0xff, 0xc9, 0xd0, 0xd7, 0xff, 0xcb, 0xd0, 0xd9, 0xff, 0xc3, 0xc8, 0xd1, 0xff, 0xbb, 0xc1, 0xca, 0xff, 0xb6, 0xbb, 0xc6, 0xff, 0xb2, 0xb7, 0xc0, 0xff, 0xa9, 0xb0, 0xba, 0xff, 0x99, 0xa2, 0xb2, 0xff, 0x8b, 0x96, 0xa9, 0xff, 0x84, 0x8f, 0xa1, 0xff, 0x81, 0x8a, 0x9b, 0xff, 0x80, 0x8a, 0x9b, 0xff, 0x7d, 0x88, 0x98, 0xff, 0x7a, 0x84, 0x92, 0xff, 0x82, 0x8b, 0x98, 0xff, 0x89, 0x91, 0x9e, 0xff, 0x8b, 0x93, 0x9f, 0xff, 0x8f, 0x95, 0x9f, 0xff, 0x92, 0x97, 0xa1, 0xff, 0x8f, 0x95, 0xa2, 0xff, 0x91, 0x98, 0xa5, 0xff, 0x8c, 0x93, 0xa1, 0xff, 0x85, 0x8b, 0x9a, 0xff, 0x84, 0x8d, 0x9d, 0xff, 0x7e, 0x89, 0x99, 0xff, 0x7b, 0x85, 0x96, 0xff, 0x77, 0x82, 0x93, 0xff, 0x71, 0x7c, 0x8e, 0xff, 0x66, 0x72, 0x85, 0xff, 0x5e, 0x6c, 0x7e, 0xff, 0x59, 0x68, 0x79, 0xff, 0x51, 0x5f, 0x71, 0xff, 0x51, 0x5f, 0x71, 0xff, 0x3c, 0x4b, 0x5c, 0xff, 0x31, 0x41, 0x51, 0xff, 0x5a, 0x71, 0x82, 0xff, 0x5a, 0x75, 0x86, 0xff, 0x46, 0x60, 0x71, 0xff, 0x44, 0x5e, 0x6f, 0xff, 0x5f, 0x79, 0x8a, 0xff, 0x46, 0x60, 0x71, 0xff, 0x46, 0x60, 0x72, 0xff, 0x41, 0x5b, 0x6f, 0xff, 0x41, 0x5b, 0x70, 0xff, 0x31, 0x4d, 0x63, 0xff, 0x66, 0x89, 0xa1, 0xff, 0x5a, 0x82, 0x9a, 0xff, 0x78, 0xa6, 0xbb, 0xff, 0x98, 0xc0, 0xd3, 0xff, 0x30, 0x49, 0x5d, 0xff, 0x00, 0x13, 0x24, 0xff, 0x3c, 0x5d, 0x6f, 0xff, 0x80, 0xa7, 0xbb, 0xff, 0x71, 0x96, 0xab, 0xff, 0x77, 0x9a, 0xae, 0xff, 0x8b, 0xad, 0xc0, 0xff, 0x59, 0x7a, 0x8d, 0xff, 0x52, 0x73, 0x85, 0xff, 0x6f, 0x90, 0x9f, 0xff, 0x3e, 0x5e, 0x70, 0xff, 0x11, 0x37, 0x4b, 0xff, 0x4c, 0x76, 0x83, 0xff, 0x90, 0xac, 0xb2, 0xff, 0x98, 0xa2, 0xa4, 0xff, 0x94, 0x99, 0x98, 0xff, 0x8f, 0x93, 0x91, 0xff, 0x94, 0x93, 0x93, 0xff, 0x94, 0x94, 0x94, 0xff, 0x96, 0x96, 0x96, 0xff, 0x96, 0x96, 0x96, 0xff, 0x94, 0x94, 0x94, 0xff, 0x93, 0x93, 0x93, 0xff, 0x90, 0x90, 0x90, 0xff, 0x91, 0x91, 0x91, 0xff, 0x91, 0x91, 0x91, 0xff, 0x92, 0x92, 0x92, 0xff, 0x92, 0x92, 0x92, 0xff, 0x91, 0x92, 0x93, 0xff, 0x90, 0x92, 0x93, 0xff, 0x8f, 0x91, 0x92, 0xff, 0x8d, 0x90, 0x90, 0xab,
    0x7f, 0x9a, 0xa6, 0xbe, 0x7f, 0x98, 0xa3, 0xff, 0x9c, 0xb7, 0xc5, 0xff, 0x7b, 0x98, 0xab, 0xff, 0x55, 0x78, 0x8b, 0xff, 0x73, 0x98, 0xa9, 0xff, 0x76, 0x9a, 0xac, 0xff, 0x49, 0x6c, 0x80, 0xff, 0x39, 0x5c, 0x70, 0xff, 0x31, 0x55, 0x69, 0xff, 0x4d, 0x6d, 0x7f, 0xff, 0x53, 0x70, 0x81, 0xff, 0x1f, 0x39, 0x4a, 0xff, 0x3d, 0x56, 0x67, 0xff, 0x56, 0x71, 0x82, 0xff, 0x25, 0x41, 0x52, 0xff, 0x2c, 0x4a, 0x5a, 0xff, 0x67, 0x89, 0x9e, 0xff, 0x3c, 0x59, 0x6f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x39, 0x3e, 0x44, 0xff, 0xc7, 0xd5, 0xe6, 0xff, 0xc5, 0xd1, 0xdd, 0xff, 0xc7, 0xd2, 0xd9, 0xff, 0xc9, 0xd3, 0xdc, 0xff, 0xc8, 0xd0, 0xda, 0xff, 0xc2, 0xcc, 0xd5, 0xff, 0xc0, 0xc8, 0xd2, 0xff, 0xc0, 0xc8, 0xcf, 0xff, 0xc0, 0xca, 0xd0, 0xff, 0xc2, 0xcb, 0xd2, 0xff, 0xbe, 0xc7, 0xcd, 0xff, 0xbf, 0xc7, 0xce, 0xff, 0xbc, 0xc5, 0xcc, 0xff, 0xba, 0xc2, 0xc9, 0xff, 0xb5, 0xbd, 0xc5, 0xff, 0xb1, 0xb7, 0xbf, 0xff, 0xad, 0xb2, 0xbb, 0xff, 0xac, 0xaf, 0xba, 0xff, 0xad, 0xb0, 0xbb, 0xff, 0xb1, 0xb6, 0xc0, 0xff, 0xb8, 0xbe, 0xc7, 0xff, 0xbd, 0xc3, 0xcc, 0xff, 0xbc, 0xc1, 0xca, 0xff, 0xbe, 0xc3, 0xcc, 0xff, 0xc5, 0xca, 0xd3, 0xff, 0xcb, 0xd1, 0xd8, 0xff, 0xce, 0xd4, 0xdb, 0xff, 0xcc, 0xd1, 0xda, 0xff, 0xc2, 0xc7, 0xd0, 0xff, 0xbc, 0xc1, 0xca, 0xff, 0xb7, 0xbb, 0xc6, 0xff, 0xb2, 0xb6, 0xbf, 0xff, 0xa8, 0xac, 0xb7, 0xff, 0x9b, 0xa2, 0xb3, 0xff, 0x8f, 0x98, 0xab, 0xff, 0x8a, 0x94, 0xa6, 0xff, 0x88, 0x8f, 0xa0, 0xff, 0x82, 0x8c, 0x9d, 0xff, 0x7f, 0x8d, 0x9d, 0xff, 0x7d, 0x89, 0x99, 0xff, 0x7d, 0x87, 0x97, 0xff, 0x80, 0x89, 0x97, 0xff, 0x85, 0x90, 0x9e, 0xff, 0x8e, 0x96, 0xa3, 0xff, 0x94, 0x9b, 0xa7, 0xff, 0x93, 0x9a, 0xa7, 0xff, 0x8c, 0x94, 0xa2, 0xff, 0x8f, 0x97, 0xa6, 0xff, 0x93, 0x9c, 0xa9, 0xff, 0x8a, 0x94, 0xa3, 0xff, 0x82, 0x8e, 0x9f, 0xff, 0x7f, 0x8b, 0x9c, 0xff, 0x7f, 0x8b, 0x9d, 0xff, 0x7a, 0x87, 0x99, 0xff, 0x69, 0x77, 0x8a, 0xff, 0x5e, 0x6d, 0x7f, 0xff, 0x5c, 0x6d, 0x7d, 0xff, 0x59, 0x68, 0x7a, 0xff, 0x50, 0x60, 0x72, 0xff, 0x49, 0x59, 0x6a, 0xff, 0x27, 0x34, 0x46, 0xff, 0x50, 0x65, 0x76, 0xff, 0x55, 0x70, 0x81, 0xff, 0x53, 0x6d, 0x7e, 0xff, 0x36, 0x50, 0x61, 0xff, 0x60, 0x7a, 0x8b, 0xff, 0x42, 0x5c, 0x6c, 0xff, 0x49, 0x63, 0x76, 0xff, 0x46, 0x61, 0x75, 0xff, 0x4b, 0x66, 0x7a, 0xff, 0x23, 0x41, 0x57, 0xff, 0x55, 0x76, 0x8e, 0xff, 0x65, 0x8b, 0xa3, 0xff, 0x69, 0x95, 0xac, 0xff, 0xa6, 0xd2, 0xe7, 0xff, 0x4a, 0x65, 0x76, 0xff, 0x00, 0x01, 0x10, 0xff, 0x1c, 0x33, 0x45, 0xff, 0x75, 0x9e, 0xb5, 0xff, 0x80, 0xaa, 0xc0, 0xff, 0x6a, 0x8c, 0x9f, 0xff, 0x8c, 0xb1, 0xc2, 0xff, 0x70, 0x93, 0xa5, 0xff, 0x49, 0x6b, 0x7c, 0xff, 0x5e, 0x81, 0x8f, 0xff, 0x5f, 0x7d, 0x90, 0xff, 0x1f, 0x3f, 0x54, 0xff, 0x46, 0x6e, 0x7c, 0xff, 0x8c, 0xaa, 0xb0, 0xff, 0x9a, 0xa4, 0xa6, 0xff, 0x9d, 0xa1, 0x9f, 0xff, 0x9b, 0x9d, 0x9c, 0xff, 0x98, 0x97, 0x98, 0xff, 0x99, 0x99, 0x99, 0xff, 0x98, 0x98, 0x98, 0xff, 0x98, 0x98, 0x98, 0xff, 0x97, 0x97, 0x97, 0xff, 0x96, 0x96, 0x96, 0xff, 0x94, 0x94, 0x94, 0xff, 0x95, 0x95, 0x95, 0xff, 0x95, 0x95, 0x95, 0xff, 0x95, 0x95, 0x95, 0xff, 0x95, 0x95, 0x95, 0xff, 0x94, 0x95, 0x97, 0xff, 0x93, 0x95, 0x96, 0xff, 0x91, 0x94, 0x95, 0xff, 0x90, 0x93, 0x93, 0xbe,
    0x6d, 0x86, 0x8d, 0xd3, 0x8a, 0xa3, 0xab, 0xff, 0x86, 0xa2, 0xb3, 0xff, 0x60, 0x80, 0x9a, 0xff, 0x64, 0x88, 0x9b, 0xff, 0x78, 0x9c, 0xad, 0xff, 0x50, 0x74, 0x85, 0xff, 0x49, 0x6c, 0x80, 0xff, 0x34, 0x57, 0x6b, 0xff, 0x32, 0x56, 0x6a, 0xff, 0x45, 0x66, 0x77, 0xff, 0x52, 0x6f, 0x7f, 0xff, 0x22, 0x3c, 0x4d, 0xff, 0x3c, 0x53, 0x65, 0xff, 0x57, 0x71, 0x82, 0xff, 0x28, 0x43, 0x53, 0xff, 0x12, 0x2e, 0x3c, 0xff, 0x54, 0x79, 0x91, 0xff, 0x5d, 0x81, 0x9c, 0xff, 0x07, 0x12, 0x1b, 0xff, 0x1f, 0x22, 0x25, 0xff, 0xbb, 0xc8, 0xdb, 0xff, 0xc5, 0xd0, 0xdf, 0xff, 0xc5, 0xcf, 0xd7, 0xff, 0xc9, 0xd3, 0xdd, 0xff, 0xca, 0xd3, 0xde, 0xff, 0xc7, 0xd0, 0xdb, 0xff, 0xc3, 0xc9, 0xd5, 0xff, 0xc0, 0xc8, 0xd1, 0xff, 0xbf, 0xca, 0xd1, 0xff, 0xbc, 0xc5, 0xcd, 0xff, 0xba, 0xc4, 0xcc, 0xff, 0xbd, 0xc8, 0xcf, 0xff, 0xbb, 0xc7, 0xce, 0xff, 0xbb, 0xc1, 0xc8, 0xff, 0xba, 0xbd, 0xc3, 0xff, 0xb3, 0xb8, 0xc0, 0xff, 0xb0, 0xb7, 0xbf, 0xff, 0xad, 0xb6, 0xbf, 0xff, 0xb1, 0xba, 0xc4, 0xff, 0xb9, 0xc1, 0xca, 0xff, 0xbe, 0xc5, 0xce, 0xff, 0xc0, 0xc8, 0xd1, 0xff, 0xc2, 0xc8, 0xd2, 0xff, 0xc8, 0xcd, 0xd6, 0xff, 0xcc, 0xd1, 0xda, 0xff, 0xce, 0xd4, 0xdb, 0xff, 0xd1, 0xd7, 0xde, 0xff, 0xcb, 0xd0, 0xd9, 0xff, 0xbf, 0xc4, 0xcd, 0xff, 0xbb, 0xc0, 0xca, 0xff, 0xb9, 0xbe, 0xc9, 0xff, 0xb6, 0xb8, 0xc2, 0xff, 0xa9, 0xac, 0xb7, 0xff, 0x99, 0xa0, 0xb1, 0xff, 0x93, 0x9c, 0xaf, 0xff, 0x91, 0x99, 0xab, 0xff, 0x8e, 0x94, 0xa5, 0xff, 0x85, 0x8f, 0xa1, 0xff, 0x82, 0x8f, 0xa2, 0xff, 0x7e, 0x8c, 0x9d, 0xff, 0x7c, 0x88, 0x99, 0xff, 0x80, 0x8c, 0x9c, 0xff, 0x82, 0x8f, 0x9d, 0xff, 0x89, 0x93, 0xa0, 0xff, 0x94, 0x9c, 0xa8, 0xff, 0x92, 0x99, 0xa6, 0xff, 0x91, 0x9a, 0xa7, 0xff, 0x90, 0x98, 0xa6, 0xff, 0x90, 0x99, 0xa7, 0xff, 0x91, 0x9c, 0xac, 0xff, 0x8e, 0x9b, 0xac, 0xff, 0x89, 0x95, 0xa7, 0xff, 0x81, 0x8f, 0xa1, 0xff, 0x7a, 0x88, 0x9c, 0xff, 0x6e, 0x7d, 0x90, 0xff, 0x63, 0x73, 0x84, 0xff, 0x5d, 0x6d, 0x7d, 0xff, 0x58, 0x68, 0x79, 0xff, 0x4c, 0x5d, 0x6d, 0xff, 0x4c, 0x5c, 0x6d, 0xff, 0x30, 0x3b, 0x4e, 0xff, 0x42, 0x56, 0x68, 0xff, 0x47, 0x65, 0x75, 0xff, 0x4f, 0x69, 0x7a, 0xff, 0x39, 0x53, 0x64, 0xff, 0x59, 0x73, 0x84, 0xff, 0x46, 0x60, 0x70, 0xff, 0x46, 0x61, 0x74, 0xff, 0x41, 0x5d, 0x71, 0xff, 0x46, 0x61, 0x75, 0xff, 0x2c, 0x4a, 0x60, 0xff, 0x4a, 0x6b, 0x84, 0xff, 0x6f, 0x93, 0xab, 0xff, 0x69, 0x94, 0xac, 0xff, 0x9f, 0xcc, 0xe3, 0xff, 0x5d, 0x7c, 0x8b, 0xff, 0x00, 0x04, 0x14, 0xff, 0x02, 0x0e, 0x21, 0xff, 0x55, 0x80, 0x98, 0xff, 0x8e, 0xbb, 0xd1, 0xff, 0x69, 0x8b, 0x9e, 0xff, 0x8a, 0xae, 0xc0, 0xff, 0x84, 0xa8, 0xba, 0xff, 0x5b, 0x7e, 0x8e, 0xff, 0x5f, 0x82, 0x90, 0xff, 0x58, 0x74, 0x87, 0xff, 0x32, 0x4d, 0x64, 0xff, 0x51, 0x77, 0x86, 0xff, 0x89, 0xa7, 0xae, 0xff, 0x9d, 0xa7, 0xa9, 0xff, 0xa3, 0xa3, 0xa2, 0xff, 0xa4, 0xa5, 0xa3, 0xff, 0xa0, 0x9f, 0xa0, 0xff, 0x9c, 0x9c, 0x9c, 0xff, 0x9d, 0x9d, 0x9d, 0xff, 0x9b, 0x9b, 0x9b, 0xff, 0x9a, 0x9a, 0x9a, 0xff, 0x9b, 0x9c, 0x9c, 0xff, 0x9c, 0x9c, 0x9c, 0xff, 0x9c, 0x9c, 0x9c, 0xff, 0x9c, 0x9c, 0x9c, 0xff, 0x9b, 0x9b, 0x9b, 0xff, 0x9b, 0x9b, 0x9b, 0xff, 0x99, 0x9b, 0x9b, 0xff, 0x97, 0x9a, 0x9b, 0xff, 0x96, 0x99, 0x9a, 0xff, 0x94, 0x98, 0x99, 0xd3,
    0x6e, 0x87, 0x91, 0xe7, 0x90, 0xac, 0xb8, 0xff, 0x79, 0x99, 0xa9, 0xff, 0x57, 0x79, 0x8b, 0xff, 0x7b, 0x9f, 0xb0, 0xff, 0x68, 0x8b, 0x9f, 0xff, 0x48, 0x6b, 0x81, 0xff, 0x47, 0x69, 0x7f, 0xff, 0x31, 0x50, 0x67, 0xff, 0x38, 0x56, 0x6a, 0xff, 0x39, 0x58, 0x69, 0xff, 0x50, 0x6d, 0x7e, 0xff, 0x2c, 0x47, 0x58, 0xff, 0x33, 0x4e, 0x60, 0xff, 0x52, 0x6c, 0x7d, 0xff, 0x34, 0x4f, 0x5f, 0xff, 0x0c, 0x22, 0x32, 0xff, 0x33, 0x4e, 0x62, 0xff, 0x66, 0x87, 0x9d, 0xff, 0x35, 0x53, 0x62, 0xff, 0x09, 0x15, 0x22, 0xff, 0xa0, 0xa9, 0xb9, 0xff, 0xc7, 0xd2, 0xde, 0xff, 0xc3, 0xcd, 0xd6, 0xff, 0xcb, 0xd1, 0xdb, 0xff, 0xcd, 0xd3, 0xdb, 0xff, 0xcb, 0xd4, 0xdb, 0xff, 0xc5, 0xd2, 0xd7, 0xff, 0xc4, 0xce, 0xd5, 0xff, 0xc2, 0xcb, 0xd5, 0xff, 0xbe, 0xc7, 0xd1, 0xff, 0xba, 0xc3, 0xcc, 0xff, 0xb9, 0xc3, 0xce, 0xff, 0xbb, 0xc5, 0xd1, 0xff, 0xb8, 0xbe, 0xcc, 0xff, 0xb8, 0xbe, 0xca, 0xff, 0xb7, 0xbf, 0xcb, 0xff, 0xb8, 0xbe, 0xc9, 0xff, 0xb6, 0xbd, 0xc7, 0xff, 0xbd, 0xc4, 0xcf, 0xff, 0xbd, 0xc4, 0xce, 0xff, 0xc1, 0xc8, 0xd1, 0xff, 0xc7, 0xce, 0xd6, 0xff, 0xc9, 0xcf, 0xd7, 0xff, 0xcf, 0xd5, 0xdc, 0xff, 0xd2, 0xd7, 0xdc, 0xff, 0xd2, 0xd6, 0xdb, 0xff, 0xd7, 0xdb, 0xe1, 0xff, 0xd0, 0xd3, 0xdb, 0xff, 0xc0, 0xc3, 0xcc, 0xff, 0xbb, 0xbd, 0xc8, 0xff, 0xba, 0xbc, 0xc7, 0xff, 0xb3, 0xb7, 0xc3, 0xff, 0xa7, 0xac, 0xb9, 0xff, 0x99, 0x9f, 0xae, 0xff, 0x94, 0x9c, 0xad, 0xff, 0x90, 0x97, 0xa9, 0xff, 0x8e, 0x97, 0xa9, 0xff, 0x89, 0x93, 0xa4, 0xff, 0x85, 0x90, 0xa1, 0xff, 0x85, 0x8f, 0xa1, 0xff, 0x85, 0x90, 0xa0, 0xff, 0x86, 0x90, 0xa1, 0xff, 0x86, 0x90, 0xa1, 0xff, 0x89, 0x92, 0xa0, 0xff, 0x90, 0x98, 0xa6, 0xff, 0x92, 0x9c, 0xac, 0xff, 0x93, 0x9c, 0xad, 0xff, 0x91, 0x9c, 0xac, 0xff, 0x88, 0x94, 0xa6, 0xff, 0x8e, 0x99, 0xad, 0xff, 0x91, 0x9c, 0xb0, 0xff, 0x89, 0x97, 0xa9, 0xff, 0x84, 0x92, 0xa5, 0xff, 0x7b, 0x8a, 0x9d, 0xff, 0x74, 0x83, 0x96, 0xff, 0x6d, 0x7b, 0x8e, 0xff, 0x63, 0x73, 0x84, 0xff, 0x5a, 0x6a, 0x7b, 0xff, 0x4e, 0x5e, 0x6f, 0xff, 0x47, 0x57, 0x68, 0xff, 0x37, 0x44, 0x55, 0xff, 0x39, 0x4c, 0x5c, 0xff, 0x49, 0x64, 0x72, 0xff, 0x46, 0x60, 0x72, 0xff, 0x39, 0x52, 0x66, 0xff, 0x56, 0x70, 0x83, 0xff, 0x50, 0x67, 0x78, 0xff, 0x43, 0x5d, 0x6f, 0xff, 0x41, 0x5d, 0x72, 0xff, 0x4a, 0x64, 0x7a, 0xff, 0x35, 0x51, 0x67, 0xff, 0x3c, 0x5b, 0x75, 0xff, 0x70, 0x94, 0xac, 0xff, 0x67, 0x89, 0xa1, 0xff, 0x93, 0xbc, 0xd4, 0xff, 0x6f, 0x9a, 0xad, 0xff, 0x06, 0x1a, 0x27, 0xff, 0x00, 0x00, 0x08, 0xff, 0x1e, 0x42, 0x57, 0xff, 0x81, 0xaf, 0xc7, 0xff, 0x71, 0x96, 0xae, 0xff, 0x77, 0x9b, 0xaf, 0xff, 0x87, 0xab, 0xbc, 0xff, 0x5a, 0x7e, 0x8d, 0xff, 0x69, 0x8f, 0xa1, 0xff, 0x4e, 0x73, 0x86, 0xff, 0x26, 0x47, 0x59, 0xff, 0x60, 0x80, 0x8c, 0xff, 0x91, 0xaa, 0xae, 0xff, 0x9c, 0xa8, 0xaa, 0xff, 0xa3, 0xa4, 0xa6, 0xff, 0xa6, 0xa5, 0xa4, 0xff, 0xa4, 0xa6, 0xa4, 0xff, 0xa2, 0xa3, 0xa1, 0xff, 0xa4, 0xa5, 0xa3, 0xff, 0xa1, 0xa2, 0xa0, 0xff, 0x9d, 0x9f, 0x9d, 0xff, 0xa1, 0xa1, 0xa0, 0xff, 0xa3, 0xa1, 0xa0, 0xff, 0xa2, 0xa0, 0x9f, 0xff, 0xa2, 0xa0, 0x9f, 0xff, 0xa1, 0xa0, 0x9f, 0xff, 0xa1, 0x9f, 0x9f, 0xff, 0xa1, 0xa0, 0x9e, 0xff, 0x9f, 0x9f, 0x9f, 0xff, 0x9e, 0x9d, 0x9d, 0xff, 0x9d, 0x9c, 0x9c, 0xe6,
    0x7f, 0x9d, 0xa6, 0xf0, 0x84, 0xa2, 0xb0, 0xff, 0x60, 0x82, 0x92, 0xff, 0x72, 0x94, 0xa2, 0xff, 0x72, 0x96, 0xa6, 0xff, 0x53, 0x75, 0x8b, 0xff, 0x51, 0x73, 0x8b, 0xff, 0x44, 0x65, 0x7e, 0xff, 0x30, 0x4d, 0x64, 0xff, 0x38, 0x52, 0x66, 0xff, 0x38, 0x55, 0x66, 0xff, 0x47, 0x65, 0x76, 0xff, 0x35, 0x51, 0x62, 0xff, 0x3e, 0x5a, 0x6b, 0xff, 0x4c, 0x66, 0x77, 0xff, 0x37, 0x51, 0x62, 0xff, 0x08, 0x1c, 0x2d, 0xff, 0x26, 0x3d, 0x4e, 0xff, 0x54, 0x75, 0x87, 0xff, 0x64, 0x86, 0x9a, 0xff, 0x0e, 0x27, 0x3b, 0xff, 0x76, 0x83, 0x94, 0xff, 0xc2, 0xd0, 0xdc, 0xff, 0xbe, 0xca, 0xd3, 0xff, 0xcb, 0xd1, 0xda, 0xff, 0xd0, 0xd5, 0xdc, 0xff, 0xcd, 0xd6, 0xdb, 0xff, 0xca, 0xd7, 0xda, 0xff, 0xcd, 0xd5, 0xdd, 0xff, 0xc4, 0xcc, 0xd7, 0xff, 0xbf, 0xc8, 0xd2, 0xff, 0xbd, 0xc6, 0xd1, 0xff, 0xbc, 0xc5, 0xd2, 0xff, 0xba, 0xc3, 0xd0, 0xff, 0xb8, 0xc0, 0xcf, 0xff, 0xb8, 0xc0, 0xd0, 0xff, 0xb9, 0xc2, 0xcf, 0xff, 0xb9, 0xc0, 0xce, 0xff, 0xbd, 0xc1, 0xcd, 0xff, 0xc0, 0xc5, 0xd0, 0xff, 0xc1, 0xc7, 0xd1, 0xff, 0xc4, 0xca, 0xd3, 0xff, 0xc7, 0xce, 0xd6, 0xff, 0xcb, 0xd0, 0xd7, 0xff, 0xd0, 0xd7, 0xdd, 0xff, 0xd0, 0xd5, 0xdb, 0xff, 0xd2, 0xd5, 0xd9, 0xff, 0xd7, 0xda, 0xdf, 0xff, 0xd1, 0xd3, 0xdc, 0xff, 0xc5, 0xc6, 0xd1, 0xff, 0xbe, 0xbf, 0xca, 0xff, 0xbb, 0xbb, 0xc8, 0xff, 0xb1, 0xb6, 0xc3, 0xff, 0xa7, 0xae, 0xbb, 0xff, 0x9d, 0xa3, 0xb2, 0xff, 0x93, 0x9b, 0xab, 0xff, 0x8a, 0x92, 0xa4, 0xff, 0x8d, 0x97, 0xaa, 0xff, 0x91, 0x9b, 0xac, 0xff, 0x8e, 0x97, 0xa8, 0xff, 0x8e, 0x98, 0xa9, 0xff, 0x91, 0x99, 0xaa, 0xff, 0x92, 0x9a, 0xab, 0xff, 0x8f, 0x96, 0xa8, 0xff, 0x8f, 0x97, 0xa6, 0xff, 0x8c, 0x95, 0xa5, 0xff, 0x8e, 0x98, 0xaa, 0xff, 0x8d, 0x98, 0xa9, 0xff, 0x8c, 0x98, 0xaa, 0xff, 0x8a, 0x97, 0xa9, 0xff, 0x8e, 0x99, 0xac, 0xff, 0x8b, 0x97, 0xab, 0xff, 0x85, 0x94, 0xa6, 0xff, 0x81, 0x90, 0xa2, 0xff, 0x7c, 0x8b, 0x9d, 0xff, 0x7a, 0x89, 0x9c, 0xff, 0x72, 0x81, 0x94, 0xff, 0x68, 0x77, 0x8a, 0xff, 0x5d, 0x6d, 0x7f, 0xff, 0x53, 0x63, 0x74, 0xff, 0x44, 0x54, 0x64, 0xff, 0x38, 0x47, 0x57, 0xff, 0x30, 0x44, 0x52, 0xff, 0x4e, 0x66, 0x74, 0xff, 0x49, 0x62, 0x75, 0xff, 0x34, 0x4d, 0x63, 0xff, 0x50, 0x69, 0x7d, 0xff, 0x50, 0x67, 0x77, 0xff, 0x3b, 0x53, 0x65, 0xff, 0x42, 0x5d, 0x72, 0xff, 0x4f, 0x6a, 0x7e, 0xff, 0x3d, 0x57, 0x6f, 0xff, 0x2e, 0x4c, 0x65, 0xff, 0x6c, 0x8f, 0xa7, 0xff, 0x5f, 0x81, 0x99, 0xff, 0x8a, 0xb3, 0xca, 0xff, 0x8a, 0xbb, 0xcf, 0xff, 0x1d, 0x33, 0x40, 0xff, 0x00, 0x00, 0x00, 0xff, 0x04, 0x1c, 0x30, 0xff, 0x6a, 0x96, 0xaf, 0xff, 0x80, 0xab, 0xc2, 0xff, 0x61, 0x87, 0x9c, 0xff, 0x8d, 0xb0, 0xc0, 0xff, 0x6d, 0x91, 0xa1, 0xff, 0x5a, 0x82, 0x94, 0xff, 0x4e, 0x77, 0x88, 0xff, 0x25, 0x48, 0x57, 0xff, 0x5b, 0x79, 0x83, 0xff, 0x9c, 0xb0, 0xb4, 0xff, 0xa0, 0xac, 0xad, 0xff, 0xa3, 0xa5, 0xa7, 0xff, 0xaa, 0xa9, 0xaa, 0xff, 0xa9, 0xab, 0xa8, 0xff, 0xa9, 0xab, 0xa8, 0xff, 0xa7, 0xa8, 0xa6, 0xff, 0xa6, 0xa7, 0xa5, 0xff, 0xa5, 0xa7, 0xa4, 0xff, 0xa5, 0xa5, 0xa3, 0xff, 0xa8, 0xa5, 0xa4, 0xff, 0xa6, 0xa4, 0xa3, 0xff, 0xa6, 0xa4, 0xa3, 0xff, 0xa6, 0xa4, 0xa3, 0xff, 0xa5, 0xa3, 0xa2, 0xff, 0xa5, 0xa2, 0xa1, 0xff, 0xa3, 0xa0, 0xa0, 0xff, 0xa2, 0x9f, 0x9f, 0xff, 0xa1, 0x9f, 0x9f, 0xf0,
    0x81, 0x9c, 0xa7, 0xf3, 0x75, 0x94, 0xa2, 0xff, 0x6d, 0x8e, 0x9e, 0xff, 0x79, 0x9c, 0xac, 0xff, 0x56, 0x7a, 0x8c, 0xff, 0x50, 0x73, 0x88, 0xff, 0x4a, 0x6c, 0x83, 0xff, 0x42, 0x64, 0x7c, 0xff, 0x2d, 0x4b, 0x62, 0xff, 0x3e, 0x59, 0x6d, 0xff, 0x3a, 0x58, 0x69, 0xff, 0x3d, 0x5b, 0x6c, 0xff, 0x38, 0x54, 0x65, 0xff, 0x48, 0x64, 0x75, 0xff, 0x3b, 0x55, 0x66, 0xff, 0x3c, 0x56, 0x67, 0xff, 0x08, 0x20, 0x2f, 0xff, 0x2a, 0x45, 0x54, 0xff, 0x3c, 0x5d, 0x70, 0xff, 0x6a, 0x8c, 0xa1, 0xff, 0x34, 0x4f, 0x65, 0xff, 0x40, 0x54, 0x6b, 0xff, 0xad, 0xbe, 0xcc, 0xff, 0xaa, 0xb9, 0xc4, 0xff, 0xc5, 0xd1, 0xdb, 0xff, 0xcd, 0xd5, 0xde, 0xff, 0xcb, 0xd2, 0xda, 0xff, 0xce, 0xd4, 0xdb, 0xff, 0xcc, 0xd2, 0xdc, 0xff, 0xc5, 0xcd, 0xd7, 0xff, 0xc5, 0xce, 0xd8, 0xff, 0xbf, 0xc7, 0xd4, 0xff, 0xba, 0xc3, 0xd0, 0xff, 0xba, 0xc4, 0xd1, 0xff, 0xb8, 0xc1, 0xcf, 0xff, 0xb9, 0xc1, 0xd0, 0xff, 0xbb, 0xc3, 0xd0, 0xff, 0xbc, 0xc5, 0xd1, 0xff, 0xbd, 0xc3, 0xd0, 0xff, 0xbd, 0xc2, 0xce, 0xff, 0xbe, 0xc6, 0xcf, 0xff, 0xc2, 0xca, 0xd3, 0xff, 0xc7, 0xcd, 0xd6, 0xff, 0xcd, 0xd3, 0xdc, 0xff, 0xd0, 0xd6, 0xdd, 0xff, 0xce, 0xd5, 0xdc, 0xff, 0xd2, 0xd6, 0xdd, 0xff, 0xd4, 0xd7, 0xde, 0xff, 0xcf, 0xd2, 0xdb, 0xff, 0xc7, 0xc9, 0xd3, 0xff, 0xbf, 0xc1, 0xcd, 0xff, 0xbb, 0xbb, 0xc9, 0xff, 0xb3, 0xb8, 0xc5, 0xff, 0xaa, 0xb0, 0xbd, 0xff, 0x9f, 0xa5, 0xb4, 0xff, 0x91, 0x98, 0xaa, 0xff, 0x89, 0x90, 0xa3, 0xff, 0x87, 0x90, 0xa4, 0xff, 0x8f, 0x99, 0xab, 0xff, 0x93, 0x9d, 0xae, 0xff, 0x92, 0x9d, 0xae, 0xff, 0x96, 0x9f, 0xb0, 0xff, 0x99, 0xa1, 0xb2, 0xff, 0x98, 0xa0, 0xb0, 0xff, 0x95, 0x9d, 0xae, 0xff, 0x91, 0x99, 0xaa, 0xff, 0x8f, 0x99, 0xaa, 0xff, 0x8e, 0x98, 0xaa, 0xff, 0x8b, 0x97, 0xa8, 0xff, 0x8c, 0x98, 0xab, 0xff, 0x8c, 0x97, 0xac, 0xff, 0x8c, 0x98, 0xad, 0xff, 0x8a, 0x97, 0xac, 0xff, 0x82, 0x90, 0xa4, 0xff, 0x7e, 0x8b, 0xa0, 0xff, 0x77, 0x85, 0x9a, 0xff, 0x6e, 0x7d, 0x91, 0xff, 0x6a, 0x79, 0x8c, 0xff, 0x63, 0x72, 0x85, 0xff, 0x57, 0x67, 0x78, 0xff, 0x43, 0x54, 0x64, 0xff, 0x32, 0x41, 0x52, 0xff, 0x26, 0x38, 0x47, 0xff, 0x4a, 0x61, 0x6f, 0xff, 0x49, 0x61, 0x74, 0xff, 0x38, 0x51, 0x65, 0xff, 0x4e, 0x66, 0x7a, 0xff, 0x58, 0x70, 0x81, 0xff, 0x3b, 0x55, 0x67, 0xff, 0x47, 0x62, 0x77, 0xff, 0x51, 0x6c, 0x80, 0xff, 0x4c, 0x67, 0x7e, 0xff, 0x20, 0x3f, 0x58, 0xff, 0x6b, 0x8d, 0xa5, 0xff, 0x5f, 0x86, 0x9c, 0xff, 0x73, 0x9f, 0xb6, 0xff, 0x99, 0xc3, 0xd9, 0xff, 0x40, 0x58, 0x67, 0xff, 0x00, 0x00, 0x05, 0xff, 0x00, 0x05, 0x17, 0xff, 0x50, 0x74, 0x8d, 0xff, 0x86, 0xb3, 0xca, 0xff, 0x60, 0x88, 0x9b, 0xff, 0x87, 0xad, 0xbd, 0xff, 0x79, 0x9f, 0xae, 0xff, 0x59, 0x82, 0x91, 0xff, 0x58, 0x7f, 0x8f, 0xff, 0x33, 0x55, 0x64, 0xff, 0x5a, 0x76, 0x7f, 0xff, 0x9a, 0xad, 0xb0, 0xff, 0xa5, 0xae, 0xae, 0xff, 0xa9, 0xa9, 0xaa, 0xff, 0xaf, 0xaf, 0xae, 0xff, 0xad, 0xae, 0xac, 0xff, 0xab, 0xac, 0xaa, 0xff, 0xac, 0xae, 0xac, 0xff, 0xad, 0xae, 0xac, 0xff, 0xab, 0xad, 0xaa, 0xff, 0xaa, 0xaa, 0xa8, 0xff, 0xab, 0xa9, 0xa8, 0xff, 0xaa, 0xa8, 0xa7, 0xff, 0xab, 0xa9, 0xa8, 0xff, 0xaa, 0xa8, 0xa7, 0xff, 0xaa, 0xa8, 0xa7, 0xff, 0xa9, 0xa7, 0xa7, 0xff, 0xa8, 0xa6, 0xa6, 0xff, 0xa7, 0xa5, 0xa5, 0xff, 0xa5, 0xa3, 0xa3, 0xf3,
    0x7a, 0x96, 0xa3, 0xff, 0x7d, 0x9d, 0xac, 0xff, 0x79, 0x9c, 0xac, 0xff, 0x56, 0x7a, 0x8c, 0xff, 0x4d, 0x71, 0x83, 0xff, 0x56, 0x78, 0x8d, 0xff, 0x4a, 0x6c, 0x84, 0xff, 0x34, 0x56, 0x6e, 0xff, 0x2a, 0x48, 0x5f, 0xff, 0x3c, 0x57, 0x6b, 0xff, 0x35, 0x53, 0x64, 0xff, 0x35, 0x53, 0x63, 0xff, 0x3c, 0x58, 0x69, 0xff, 0x42, 0x5e, 0x6f, 0xff, 0x3e, 0x58, 0x69, 0xff, 0x3c, 0x56, 0x67, 0xff, 0x0a, 0x23, 0x32, 0xff, 0x2b, 0x46, 0x55, 0xff, 0x3e, 0x5c, 0x6e, 0xff, 0x47, 0x65, 0x7a, 0xff, 0x4c, 0x66, 0x7a, 0xff, 0x4b, 0x61, 0x74, 0xff, 0xa5, 0xb7, 0xc5, 0xff, 0x8d, 0x9d, 0xaa, 0xff, 0xb4, 0xc2, 0xcf, 0xff, 0xcf, 0xd9, 0xe3, 0xff, 0xce, 0xd3, 0xdd, 0xff, 0xcf, 0xd2, 0xdb, 0xff, 0xcc, 0xd1, 0xdb, 0xff, 0xc9, 0xd3, 0xdd, 0xff, 0xc7, 0xcf, 0xdc, 0xff, 0xc3, 0xcb, 0xd8, 0xff, 0xbf, 0xc8, 0xd5, 0xff, 0xbe, 0xc7, 0xd5, 0xff, 0xbc, 0xc4, 0xd4, 0xff, 0xbc, 0xc5, 0xd4, 0xff, 0xba, 0xc3, 0xd1, 0xff, 0xbe, 0xc6, 0xd4, 0xff, 0xbe, 0xc6, 0xd3, 0xff, 0xbf, 0xc6, 0xd3, 0xff, 0xbe, 0xc7, 0xd1, 0xff, 0xbe, 0xc8, 0xd2, 0xff, 0xc5, 0xcd, 0xd7, 0xff, 0xca, 0xd1, 0xda, 0xff, 0xca, 0xd1, 0xd9, 0xff, 0xd0, 0xd6, 0xdd, 0xff, 0xd2, 0xd6, 0xdd, 0xff, 0xd2, 0xd4, 0xdc, 0xff, 0xcd, 0xd0, 0xd9, 0xff, 0xc6, 0xc8, 0xd2, 0xff, 0xc0, 0xc1, 0xcf, 0xff, 0xbb, 0xbb, 0xca, 0xff, 0xb4, 0xb7, 0xc6, 0xff, 0xa9, 0xaf, 0xbe, 0xff, 0x9d, 0xa2, 0xb3, 0xff, 0x90, 0x97, 0xaa, 0xff, 0x89, 0x90, 0xa4, 0xff, 0x81, 0x89, 0x9e, 0xff, 0x86, 0x8f, 0xa1, 0xff, 0x90, 0x9a, 0xab, 0xff, 0x95, 0x9f, 0xb0, 0xff, 0x9a, 0xa3, 0xb4, 0xff, 0x9d, 0xa4, 0xb5, 0xff, 0x9d, 0xa5, 0xb6, 0xff, 0x9d, 0xa5, 0xb6, 0xff, 0x99, 0xa1, 0xb3, 0xff, 0x93, 0x9d, 0xaf, 0xff, 0x91, 0x9b, 0xad, 0xff, 0x8f, 0x9a, 0xad, 0xff, 0x8b, 0x97, 0xaa, 0xff, 0x8b, 0x96, 0xab, 0xff, 0x8a, 0x95, 0xac, 0xff, 0x88, 0x95, 0xab, 0xff, 0x87, 0x94, 0xaa, 0xff, 0x80, 0x8d, 0xa3, 0xff, 0x78, 0x86, 0x9d, 0xff, 0x6d, 0x7b, 0x92, 0xff, 0x6a, 0x79, 0x8d, 0xff, 0x63, 0x72, 0x86, 0xff, 0x56, 0x65, 0x78, 0xff, 0x48, 0x58, 0x69, 0xff, 0x31, 0x41, 0x52, 0xff, 0x17, 0x27, 0x37, 0xff, 0x40, 0x53, 0x62, 0xff, 0x42, 0x58, 0x6b, 0xff, 0x31, 0x4a, 0x5f, 0xff, 0x4f, 0x68, 0x7c, 0xff, 0x61, 0x7a, 0x8a, 0xff, 0x3b, 0x56, 0x67, 0xff, 0x47, 0x62, 0x77, 0xff, 0x49, 0x64, 0x78, 0xff, 0x56, 0x71, 0x88, 0xff, 0x1e, 0x3d, 0x56, 0xff, 0x62, 0x84, 0x9c, 0xff, 0x5f, 0x88, 0x9d, 0xff, 0x65, 0x92, 0xa8, 0xff, 0x98, 0xbe, 0xd8, 0xff, 0x5f, 0x7c, 0x8e, 0xff, 0x0d, 0x17, 0x22, 0xff, 0x00, 0x00, 0x03, 0xff, 0x34, 0x52, 0x6b, 0xff, 0x7c, 0xac, 0xc2, 0xff, 0x69, 0x92, 0xa3, 0xff, 0x7d, 0xa3, 0xb1, 0xff, 0x70, 0x96, 0xa4, 0xff, 0x5f, 0x89, 0x98, 0xff, 0x6e, 0x97, 0xa7, 0xff, 0x46, 0x67, 0x76, 0xff, 0x62, 0x7b, 0x84, 0xff, 0xa0, 0xaf, 0xb2, 0xff, 0xaa, 0xb1, 0xb1, 0xff, 0xae, 0xaf, 0xaf, 0xff, 0xb1, 0xb0, 0xaf, 0xff, 0xb5, 0xb3, 0xb2, 0xff, 0xb0, 0xae, 0xad, 0xff, 0xb0, 0xae, 0xad, 0xff, 0xb1, 0xb0, 0xaf, 0xff, 0xaf, 0xae, 0xad, 0xff, 0xad, 0xac, 0xab, 0xff, 0xae, 0xac, 0xab, 0xff, 0xae, 0xac, 0xab, 0xff, 0xae, 0xac, 0xab, 0xff, 0xad, 0xab, 0xaa, 0xff, 0xac, 0xaa, 0xa9, 0xff, 0xac, 0xaa, 0xa9, 0xff, 0xac, 0xaa, 0xaa, 0xff, 0xab, 0xa9, 0xa9, 0xff, 0xaa, 0xa8, 0xa8, 0xff,
    0x83, 0xa1, 0xae, 0xff, 0x82, 0xa2, 0xb1, 0xff, 0x5e, 0x81, 0x93, 0xff, 0x4b, 0x70, 0x83, 0xff, 0x4d, 0x71, 0x83, 0xff, 0x53, 0x76, 0x8b, 0xff, 0x4f, 0x71, 0x89, 0xff, 0x2f, 0x51, 0x68, 0xff, 0x29, 0x46, 0x5d, 0xff, 0x36, 0x51, 0x65, 0xff, 0x30, 0x4e, 0x5f, 0xff, 0x2e, 0x4d, 0x5d, 0xff, 0x45, 0x61, 0x72, 0xff, 0x25, 0x3f, 0x50, 0xff, 0x4f, 0x69, 0x7a, 0xff, 0x3c, 0x56, 0x67, 0xff, 0x11, 0x2b, 0x3a, 0xff, 0x2f, 0x49, 0x59, 0xff, 0x30, 0x48, 0x5a, 0xff, 0x2c, 0x41, 0x53, 0xff, 0x84, 0x93, 0xa2, 0xff, 0xad, 0xba, 0xc5, 0xff, 0xa8, 0xb4, 0xc3, 0xff, 0x87, 0x94, 0xa4, 0xff, 0x9d, 0xab, 0xba, 0xff, 0xcc, 0xd9, 0xe5, 0xff, 0xcb, 0xd2, 0xdd, 0xff, 0xcd, 0xd1, 0xdb, 0xff, 0xce, 0xd3, 0xde, 0xff, 0xcb, 0xd3, 0xe0, 0xff, 0xc8, 0xd0, 0xdd, 0xff, 0xc5, 0xcd, 0xdb, 0xff, 0xc3, 0xcc, 0xda, 0xff, 0xc1, 0xca, 0xd7, 0xff, 0xbf, 0xc8, 0xd9, 0xff, 0xbf, 0xc8, 0xd9, 0xff, 0xbc, 0xc5, 0xd3, 0xff, 0xbd, 0xc5, 0xd4, 0xff, 0xbe, 0xc5, 0xd4, 0xff, 0xbc, 0xc5, 0xd2, 0xff, 0xbf, 0xc7, 0xd2, 0xff, 0xbe, 0xc7, 0xd0, 0xff, 0xc0, 0xc9, 0xd4, 0xff, 0xc0, 0xc8, 0xd2, 0xff, 0xc5, 0xcc, 0xd5, 0xff, 0xcd, 0xd4, 0xdd, 0xff, 0xd0, 0xd4, 0xdc, 0xff, 0xcf, 0xd0, 0xda, 0xff, 0xc8, 0xca, 0xd4, 0xff, 0xc3, 0xc5, 0xd0, 0xff, 0xbc, 0xbc, 0xcb, 0xff, 0xb9, 0xb8, 0xc9, 0xff, 0xb5, 0xb8, 0xc7, 0xff, 0xaa, 0xaf, 0xc0, 0xff, 0x9e, 0xa2, 0xb5, 0xff, 0x94, 0x9b, 0xae, 0xff, 0x8a, 0x91, 0xa5, 0xff, 0x80, 0x88, 0x9f, 0xff, 0x7f, 0x88, 0x9b, 0xff, 0x85, 0x8f, 0x9f, 0xff, 0x92, 0x9c, 0xad, 0xff, 0x9a, 0xa2, 0xb3, 0xff, 0x9f, 0xa7, 0xb8, 0xff, 0xa1, 0xa9, 0xba, 0xff, 0xa0, 0xa7, 0xb9, 0xff, 0xa0, 0xa7, 0xba, 0xff, 0x9b, 0xa5, 0xb7, 0xff, 0x96, 0xa0, 0xb3, 0xff, 0x93, 0x9e, 0xb2, 0xff, 0x93, 0x9e, 0xb2, 0xff, 0x90, 0x9b, 0xb1, 0xff, 0x8b, 0x96, 0xad, 0xff, 0x87, 0x94, 0xaa, 0xff, 0x86, 0x92, 0xa9, 0xff, 0x7d, 0x8a, 0xa0, 0xff, 0x79, 0x87, 0x9d, 0xff, 0x73, 0x81, 0x98, 0xff, 0x6a, 0x78, 0x8f, 0xff, 0x60, 0x6f, 0x84, 0xff, 0x52, 0x61, 0x74, 0xff, 0x48, 0x57, 0x6a, 0xff, 0x38, 0x47, 0x59, 0xff, 0x0b, 0x1a, 0x2a, 0xff, 0x32, 0x42, 0x51, 0xff, 0x50, 0x64, 0x78, 0xff, 0x27, 0x40, 0x55, 0xff, 0x47, 0x60, 0x74, 0xff, 0x5a, 0x73, 0x83, 0xff, 0x31, 0x4b, 0x5d, 0xff, 0x3e, 0x59, 0x6e, 0xff, 0x3b, 0x56, 0x6a, 0xff, 0x59, 0x74, 0x8b, 0xff, 0x2d, 0x4c, 0x65, 0xff, 0x56, 0x78, 0x91, 0xff, 0x5f, 0x8a, 0x9e, 0xff, 0x63, 0x91, 0xa6, 0xff, 0x99, 0xbc, 0xd9, 0xff, 0x63, 0x86, 0x99, 0xff, 0x22, 0x40, 0x48, 0xff, 0x00, 0x01, 0x0c, 0xff, 0x13, 0x2d, 0x43, 0xff, 0x6f, 0xa1, 0xb5, 0xff, 0x7b, 0xa5, 0xb5, 0xff, 0x6c, 0x94, 0xa1, 0xff, 0x72, 0x9b, 0xa7, 0xff, 0x5d, 0x89, 0x97, 0xff, 0x6b, 0x95, 0xa4, 0xff, 0x56, 0x77, 0x86, 0xff, 0x71, 0x87, 0x91, 0xff, 0xa9, 0xb2, 0xb6, 0xff, 0xaf, 0xb2, 0xb1, 0xff, 0xb4, 0xb5, 0xb5, 0xff, 0xb6, 0xb4, 0xb4, 0xff, 0xb6, 0xb3, 0xb2, 0xff, 0xb5, 0xb3, 0xb2, 0xff, 0xb5, 0xb2, 0xb2, 0xff, 0xb4, 0xb1, 0xb1, 0xff, 0xb3, 0xb0, 0xb0, 0xff, 0xb2, 0xb0, 0xaf, 0xff, 0xb2, 0xb0, 0xaf, 0xff, 0xb1, 0xaf, 0xae, 0xff, 0xb0, 0xae, 0xad, 0xff, 0xb0, 0xae, 0xad, 0xff, 0xaf, 0xad, 0xac, 0xff, 0xaf, 0xad, 0xab, 0xff, 0xae, 0xac, 0xac, 0xff, 0xad, 0xab, 0xab, 0xff, 0xac, 0xaa, 0xaa, 0xff,
    0x8a, 0xa7, 0xb6, 0xf3, 0x63, 0x84, 0x94, 0xff, 0x4f, 0x71, 0x84, 0xff, 0x4b, 0x71, 0x84, 0xff, 0x55, 0x79, 0x8c, 0xff, 0x5b, 0x7d, 0x92, 0xff, 0x43, 0x65, 0x7c, 0xff, 0x25, 0x47, 0x5f, 0xff, 0x28, 0x45, 0x5c, 0xff, 0x38, 0x54, 0x67, 0xff, 0x2e, 0x4b, 0x5d, 0xff, 0x3e, 0x5c, 0x6d, 0xff, 0x40, 0x5c, 0x6d, 0xff, 0x22, 0x3c, 0x4d, 0xff, 0x50, 0x6b, 0x7c, 0xff, 0x32, 0x4d, 0x5e, 0xff, 0x00, 0x10, 0x21, 0xff, 0x37, 0x4f, 0x60, 0xff, 0x5b, 0x6f, 0x81, 0xff, 0x87, 0x95, 0xa3, 0xff, 0xbe, 0xc7, 0xcf, 0xff, 0xb1, 0xb6, 0xb8, 0xff, 0xaa, 0xb0, 0xbe, 0xff, 0x95, 0x9e, 0xb1, 0xff, 0x87, 0x94, 0xa3, 0xff, 0xbe, 0xce, 0xda, 0xff, 0xc5, 0xd2, 0xdd, 0xff, 0xc8, 0xd0, 0xda, 0xff, 0xc9, 0xd0, 0xdb, 0xff, 0xc9, 0xd1, 0xde, 0xff, 0xc9, 0xd1, 0xde, 0xff, 0xc7, 0xce, 0xdc, 0xff, 0xc4, 0xcd, 0xdb, 0xff, 0xc3, 0xcc, 0xdb, 0xff, 0xc0, 0xca, 0xdb, 0xff, 0xbe, 0xc9, 0xda, 0xff, 0xc0, 0xc9, 0xd9, 0xff, 0xbf, 0xc7, 0xd6, 0xff, 0xbc, 0xc4, 0xd2, 0xff, 0xb9, 0xc0, 0xce, 0xff, 0xb8, 0xc0, 0xcd, 0xff, 0xbb, 0xc5, 0xcf, 0xff, 0xb8, 0xc1, 0xca, 0xff, 0xb5, 0xbf, 0xc8, 0xff, 0xc2, 0xc9, 0xd2, 0xff, 0xca, 0xd2, 0xdb, 0xff, 0xd0, 0xd3, 0xde, 0xff, 0xcb, 0xcc, 0xd6, 0xff, 0xc7, 0xca, 0xd4, 0xff, 0xc2, 0xc3, 0xd0, 0xff, 0xba, 0xba, 0xca, 0xff, 0xb8, 0xb7, 0xc7, 0xff, 0xb5, 0xb8, 0xc9, 0xff, 0xaf, 0xb3, 0xc5, 0xff, 0xa5, 0xab, 0xbd, 0xff, 0x9c, 0xa3, 0xb7, 0xff, 0x8b, 0x93, 0xa7, 0xff, 0x7d, 0x85, 0x9e, 0xff, 0x79, 0x83, 0x96, 0xff, 0x7a, 0x84, 0x94, 0xff, 0x88, 0x92, 0xa3, 0xff, 0x94, 0x9d, 0xae, 0xff, 0x9f, 0xa7, 0xb8, 0xff, 0xa4, 0xac, 0xbd, 0xff, 0xa3, 0xa9, 0xbc, 0xff, 0xa2, 0xa9, 0xbc, 0xff, 0xa1, 0xab, 0xbe, 0xff, 0xa1, 0xa9, 0xbe, 0xff, 0x9d, 0xa8, 0xbc, 0xff, 0x99, 0xa5, 0xbb, 0xff, 0x96, 0xa0, 0xb8, 0xff, 0x90, 0x9a, 0xb2, 0xff, 0x89, 0x95, 0xad, 0xff, 0x85, 0x91, 0xa9, 0xff, 0x81, 0x8d, 0xa5, 0xff, 0x77, 0x83, 0x9c, 0xff, 0x71, 0x7f, 0x96, 0xff, 0x69, 0x77, 0x8d, 0xff, 0x5f, 0x6c, 0x83, 0xff, 0x52, 0x61, 0x75, 0xff, 0x44, 0x53, 0x66, 0xff, 0x33, 0x42, 0x56, 0xff, 0x12, 0x21, 0x31, 0xff, 0x29, 0x38, 0x47, 0xff, 0x6b, 0x7d, 0x92, 0xff, 0x4a, 0x61, 0x77, 0xff, 0x3d, 0x56, 0x6a, 0xff, 0x51, 0x6a, 0x7a, 0xff, 0x2f, 0x49, 0x5b, 0xff, 0x40, 0x5c, 0x71, 0xff, 0x35, 0x51, 0x64, 0xff, 0x5b, 0x76, 0x8d, 0xff, 0x3d, 0x5b, 0x74, 0xff, 0x51, 0x73, 0x8c, 0xff, 0x63, 0x8e, 0xa1, 0xff, 0x62, 0x8d, 0xa3, 0xff, 0x8b, 0xae, 0xce, 0xff, 0x68, 0x91, 0xa3, 0xff, 0x18, 0x42, 0x48, 0xff, 0x04, 0x15, 0x20, 0xff, 0x0a, 0x27, 0x3b, 0xff, 0x58, 0x8b, 0xa0, 0xff, 0x7a, 0xa4, 0xb4, 0xff, 0x67, 0x90, 0x9c, 0xff, 0x76, 0xa0, 0xad, 0xff, 0x59, 0x86, 0x94, 0xff, 0x66, 0x90, 0x9e, 0xff, 0x5a, 0x7b, 0x88, 0xff, 0x77, 0x8c, 0x95, 0xff, 0xae, 0xb7, 0xb8, 0xff, 0xae, 0xb1, 0xb0, 0xff, 0xb5, 0xb6, 0xb5, 0xff, 0xbb, 0xba, 0xb9, 0xff, 0xb7, 0xb5, 0xb4, 0xff, 0xb8, 0xb6, 0xb5, 0xff, 0xba, 0xb8, 0xb7, 0xff, 0xb9, 0xb7, 0xb6, 0xff, 0xb8, 0xb6, 0xb5, 0xff, 0xb8, 0xb6, 0xb5, 0xff, 0xb6, 0xb4, 0xb3, 0xff, 0xb4, 0xb2, 0xb1, 0xff, 0xb2, 0xb0, 0xaf, 0xff, 0xb2, 0xb0, 0xaf, 0xff, 0xb2, 0xb0, 0xaf, 0xff, 0xb3, 0xb1, 0xb0, 0xff, 0xb1, 0xaf, 0xb0, 0xff, 0xb0, 0xae, 0xae, 0xff, 0xaf, 0xad, 0xad, 0xf3,
    0x6e, 0x8e, 0x9f, 0xf0, 0x4d, 0x70, 0x84, 0xff, 0x52, 0x74, 0x87, 0xff, 0x4a, 0x6c, 0x7d, 0xff, 0x5a, 0x7e, 0x92, 0xff, 0x59, 0x7e, 0x92, 0xff, 0x3c, 0x5d, 0x72, 0xff, 0x23, 0x43, 0x58, 0xff, 0x30, 0x4d, 0x62, 0xff, 0x34, 0x4f, 0x64, 0xff, 0x40, 0x5b, 0x6b, 0xff, 0x50, 0x6e, 0x7c, 0xff, 0x19, 0x38, 0x4a, 0xff, 0x2a, 0x48, 0x5b, 0xff, 0x41, 0x59, 0x6b, 0xff, 0x39, 0x4c, 0x5c, 0xff, 0x78, 0x84, 0x8e, 0xff, 0xb8, 0xc1, 0xca, 0xff, 0xc8, 0xd1, 0xdb, 0xff, 0xc2, 0xc8, 0xd2, 0xff, 0xb6, 0xbb, 0xc2, 0xff, 0xbb, 0xbf, 0xc3, 0xff, 0xab, 0xb2, 0xc1, 0xff, 0x8b, 0x96, 0xaa, 0xff, 0x65, 0x74, 0x86, 0xff, 0xa7, 0xb7, 0xc7, 0xff, 0xc6, 0xd2, 0xe2, 0xff, 0xc7, 0xce, 0xdb, 0xff, 0xc6, 0xcf, 0xdc, 0xff, 0xc8, 0xd1, 0xe0, 0xff, 0xc9, 0xd1, 0xdf, 0xff, 0xc8, 0xd0, 0xde, 0xff, 0xc6, 0xcf, 0xdd, 0xff, 0xc5, 0xce, 0xdc, 0xff, 0xc4, 0xcc, 0xdd, 0xff, 0xc0, 0xca, 0xdb, 0xff, 0xc0, 0xc8, 0xd9, 0xff, 0xbd, 0xc5, 0xd6, 0xff, 0xba, 0xc3, 0xd3, 0xff, 0xb7, 0xbe, 0xce, 0xff, 0xb5, 0xbc, 0xcc, 0xff, 0xb4, 0xbc, 0xcd, 0xff, 0xab, 0xb4, 0xc5, 0xff, 0xaa, 0xb4, 0xc3, 0xff, 0xba, 0xc2, 0xcc, 0xff, 0xc8, 0xce, 0xd2, 0xff, 0xd4, 0xd6, 0xdf, 0xff, 0xcc, 0xcd, 0xd9, 0xff, 0xc5, 0xc7, 0xd3, 0xff, 0xc0, 0xc2, 0xcd, 0xff, 0xbb, 0xbd, 0xc9, 0xff, 0xb9, 0xbd, 0xc9, 0xff, 0xb9, 0xba, 0xca, 0xff, 0xb4, 0xb3, 0xc4, 0xff, 0xae, 0xaf, 0xc0, 0xff, 0xa6, 0xa9, 0xbd, 0xff, 0x95, 0x99, 0xad, 0xff, 0x80, 0x85, 0x9b, 0xff, 0x78, 0x80, 0x94, 0xff, 0x76, 0x80, 0x92, 0xff, 0x7b, 0x86, 0x97, 0xff, 0x8b, 0x95, 0xa6, 0xff, 0x9b, 0xa4, 0xb6, 0xff, 0xa2, 0xab, 0xbc, 0xff, 0xa3, 0xab, 0xbd, 0xff, 0xa3, 0xab, 0xbc, 0xff, 0xa2, 0xaa, 0xbd, 0xff, 0xa5, 0xad, 0xc0, 0xff, 0xa4, 0xae, 0xc1, 0xff, 0x9e, 0xa8, 0xbc, 0xff, 0x98, 0xa1, 0xb7, 0xff, 0x93, 0x9e, 0xb5, 0xff, 0x8c, 0x98, 0xb0, 0xff, 0x84, 0x90, 0xa8, 0xff, 0x7e, 0x89, 0xa3, 0xff, 0x74, 0x81, 0x9b, 0xff, 0x70, 0x7d, 0x93, 0xff, 0x6d, 0x78, 0x8e, 0xff, 0x5f, 0x6c, 0x84, 0xff, 0x53, 0x60, 0x78, 0xff, 0x41, 0x4f, 0x64, 0xff, 0x27, 0x36, 0x4a, 0xff, 0x31, 0x3b, 0x4e, 0xff, 0x46, 0x4e, 0x60, 0xff, 0x5c, 0x6d, 0x7e, 0xff, 0x67, 0x7f, 0x8f, 0xff, 0x47, 0x62, 0x73, 0xff, 0x49, 0x65, 0x78, 0xff, 0x31, 0x4c, 0x60, 0xff, 0x48, 0x61, 0x75, 0xff, 0x37, 0x52, 0x67, 0xff, 0x55, 0x72, 0x88, 0xff, 0x49, 0x68, 0x81, 0xff, 0x55, 0x75, 0x8d, 0xff, 0x62, 0x8a, 0xa3, 0xff, 0x58, 0x81, 0x9a, 0xff, 0x85, 0xaa, 0xc0, 0xff, 0x6c, 0x95, 0xa9, 0xff, 0x1f, 0x42, 0x54, 0xff, 0x08, 0x14, 0x1f, 0xff, 0x19, 0x2d, 0x3c, 0xff, 0x43, 0x6d, 0x80, 0xff, 0x71, 0x9f, 0xad, 0xff, 0x6c, 0x99, 0xa5, 0xff, 0x72, 0x9a, 0xa6, 0xff, 0x58, 0x84, 0x91, 0xff, 0x5b, 0x89, 0x96, 0xff, 0x5e, 0x84, 0x90, 0xff, 0x7f, 0x93, 0x9a, 0xff, 0xb4, 0xb9, 0xb9, 0xff, 0xbb, 0xb9, 0xb8, 0xff, 0xb8, 0xb6, 0xb8, 0xff, 0xbb, 0xba, 0xbb, 0xff, 0xb9, 0xb7, 0xb6, 0xff, 0xb9, 0xb7, 0xb6, 0xff, 0xbe, 0xbc, 0xbb, 0xff, 0xbc, 0xba, 0xb9, 0xff, 0xba, 0xb8, 0xb7, 0xff, 0xba, 0xb7, 0xb6, 0xff, 0xbb, 0xb7, 0xb6, 0xff, 0xb9, 0xb5, 0xb4, 0xff, 0xb7, 0xb3, 0xb2, 0xff, 0xb6, 0xb3, 0xb2, 0xff, 0xb7, 0xb3, 0xb2, 0xff, 0xb6, 0xb3, 0xb2, 0xff, 0xb3, 0xb2, 0xb1, 0xff, 0xb3, 0xb1, 0xb0, 0xff, 0xb2, 0xb0, 0xaf, 0xf0,
    0x66, 0x88, 0x9a, 0xe7, 0x52, 0x76, 0x8b, 0xff, 0x46, 0x69, 0x7d, 0xff, 0x51, 0x72, 0x82, 0xff, 0x5b, 0x80, 0x93, 0xff, 0x4a, 0x6f, 0x83, 0xff, 0x32, 0x53, 0x67, 0xff, 0x27, 0x47, 0x5a, 0xff, 0x36, 0x53, 0x66, 0xff, 0x2f, 0x4a, 0x5e, 0xff, 0x45, 0x61, 0x70, 0xff, 0x40, 0x5e, 0x6d, 0xff, 0x0a, 0x28, 0x3b, 0xff, 0x13, 0x2d, 0x41, 0xff, 0x50, 0x63, 0x73, 0xff, 0xc5, 0xd0, 0xdd, 0xff, 0xf2, 0xf7, 0xfc, 0xff, 0xd6, 0xda, 0xdd, 0xff, 0xc2, 0xc6, 0xcc, 0xff, 0xbf, 0xc2, 0xcb, 0xff, 0xb5, 0xb9, 0xc4, 0xff, 0xab, 0xae, 0xb9, 0xff, 0x96, 0x9e, 0xb1, 0xff, 0x76, 0x82, 0x9a, 0xff, 0x3d, 0x4a, 0x61, 0xff, 0x8f, 0x9e, 0xb3, 0xff, 0xc4, 0xd0, 0xe1, 0xff, 0xc4, 0xcb, 0xda, 0xff, 0xc5, 0xce, 0xdc, 0xff, 0xc8, 0xd1, 0xdf, 0xff, 0xc8, 0xd2, 0xdf, 0xff, 0xc7, 0xd0, 0xde, 0xff, 0xc6, 0xcf, 0xdd, 0xff, 0xc5, 0xcf, 0xdc, 0xff, 0xc6, 0xce, 0xdd, 0xff, 0xc3, 0xcb, 0xdc, 0xff, 0xc1, 0xc9, 0xda, 0xff, 0xbd, 0xc5, 0xd7, 0xff, 0xb9, 0xc1, 0xd3, 0xff, 0xb5, 0xbd, 0xce, 0xff, 0xb4, 0xba, 0xcc, 0xff, 0xac, 0xb3, 0xc5, 0xff, 0x9f, 0xaa, 0xbe, 0xff, 0xa4, 0xaf, 0xc0, 0xff, 0xbe, 0xc6, 0xd1, 0xff, 0xd0, 0xd3, 0xd8, 0xff, 0xd9, 0xdb, 0xe0, 0xff, 0xd1, 0xd2, 0xdb, 0xff, 0xc7, 0xc8, 0xd2, 0xff, 0xc1, 0xc3, 0xce, 0xff, 0xbf, 0xc0, 0xcd, 0xff, 0xbd, 0xbe, 0xcc, 0xff, 0xbc, 0xbb, 0xc9, 0xff, 0xba, 0xb9, 0xc8, 0xff, 0xb5, 0xb5, 0xc6, 0xff, 0xac, 0xae, 0xc0, 0xff, 0x9c, 0xa0, 0xb3, 0xff, 0x8c, 0x90, 0xa4, 0xff, 0x81, 0x88, 0x9d, 0xff, 0x7a, 0x84, 0x98, 0xff, 0x77, 0x81, 0x93, 0xff, 0x82, 0x8c, 0x9e, 0xff, 0x90, 0x9a, 0xac, 0xff, 0x9e, 0xa8, 0xb9, 0xff, 0xa4, 0xae, 0xbe, 0xff, 0xa6, 0xad, 0xbe, 0xff, 0xa5, 0xab, 0xbe, 0xff, 0xa4, 0xad, 0xbf, 0xff, 0xa4, 0xae, 0xc0, 0xff, 0xa3, 0xab, 0xbe, 0xff, 0x9d, 0xa7, 0xbc, 0xff, 0x95, 0xa1, 0xb7, 0xff, 0x90, 0x9c, 0xb4, 0xff, 0x89, 0x95, 0xad, 0xff, 0x80, 0x8c, 0xa7, 0xff, 0x76, 0x83, 0x9f, 0xff, 0x74, 0x80, 0x96, 0xff, 0x6e, 0x78, 0x8d, 0xff, 0x5d, 0x6a, 0x82, 0xff, 0x51, 0x5e, 0x77, 0xff, 0x43, 0x50, 0x67, 0xff, 0x21, 0x2f, 0x41, 0xff, 0x3f, 0x47, 0x5a, 0xff, 0x7c, 0x83, 0x96, 0xff, 0x69, 0x78, 0x87, 0xff, 0x5e, 0x73, 0x82, 0xff, 0x4f, 0x69, 0x7a, 0xff, 0x4a, 0x68, 0x7c, 0xff, 0x2f, 0x4a, 0x5f, 0xff, 0x46, 0x5d, 0x71, 0xff, 0x37, 0x52, 0x67, 0xff, 0x50, 0x6f, 0x85, 0xff, 0x55, 0x74, 0x8d, 0xff, 0x4f, 0x6d, 0x85, 0xff, 0x5b, 0x80, 0x9d, 0xff, 0x50, 0x78, 0x93, 0xff, 0x89, 0xaf, 0xc0, 0xff, 0x6e, 0x97, 0xac, 0xff, 0x39, 0x5c, 0x74, 0xff, 0x01, 0x09, 0x16, 0xff, 0x04, 0x0e, 0x19, 0xff, 0x3a, 0x60, 0x71, 0xff, 0x68, 0x98, 0xa4, 0xff, 0x74, 0xa4, 0xb0, 0xff, 0x6d, 0x94, 0xa0, 0xff, 0x58, 0x82, 0x8e, 0xff, 0x5d, 0x8c, 0x9a, 0xff, 0x5f, 0x85, 0x92, 0xff, 0x83, 0x98, 0x9d, 0xff, 0xb7, 0xb9, 0xb9, 0xff, 0xc2, 0xbd, 0xbd, 0xff, 0xbe, 0xbb, 0xbd, 0xff, 0xbd, 0xbc, 0xbe, 0xff, 0xbf, 0xbc, 0xbb, 0xff, 0xb9, 0xb7, 0xb6, 0xff, 0xbd, 0xbb, 0xba, 0xff, 0xbf, 0xbd, 0xbc, 0xff, 0xbc, 0xbb, 0xba, 0xff, 0xbc, 0xb9, 0xb8, 0xff, 0xbe, 0xba, 0xb9, 0xff, 0xbd, 0xb9, 0xb8, 0xff, 0xbc, 0xb7, 0xb6, 0xff, 0xbb, 0xb7, 0xb6, 0xff, 0xbc, 0xb7, 0xb6, 0xff, 0xba, 0xb7, 0xb6, 0xff, 0xb8, 0xb6, 0xb5, 0xff, 0xb7, 0xb5, 0xb4, 0xff, 0xb4, 0xb2, 0xb2, 0xe6,
    0x66, 0x88, 0x99, 0xd3, 0x4e, 0x71, 0x86, 0xff, 0x4d, 0x6f, 0x83, 0xff, 0x5d, 0x7e, 0x8f, 0xff, 0x56, 0x79, 0x8d, 0xff, 0x46, 0x6a, 0x7e, 0xff, 0x25, 0x47, 0x5b, 0xff, 0x23, 0x42, 0x55, 0xff, 0x43, 0x5f, 0x72, 0xff, 0x3b, 0x56, 0x67, 0xff, 0x3c, 0x5b, 0x6e, 0xff, 0x2c, 0x4a, 0x5d, 0xff, 0x00, 0x16, 0x28, 0xff, 0x2a, 0x3d, 0x4b, 0xff, 0xd9, 0xe5, 0xec, 0xff, 0xed, 0xf5, 0xfa, 0xff, 0xce, 0xd4, 0xd6, 0xff, 0xc7, 0xce, 0xd0, 0xff, 0xc2, 0xc8, 0xce, 0xff, 0xbc, 0xc3, 0xce, 0xff, 0xb2, 0xb8, 0xc7, 0xff, 0xa1, 0xa7, 0xb9, 0xff, 0x86, 0x8f, 0xa9, 0xff, 0x58, 0x64, 0x7f, 0xff, 0x29, 0x35, 0x4f, 0xff, 0x7e, 0x8a, 0xa2, 0xff, 0xbb, 0xc7, 0xd7, 0xff, 0xbe, 0xc9, 0xd5, 0xff, 0xc2, 0xcb, 0xd9, 0xff, 0xc6, 0xce, 0xde, 0xff, 0xc7, 0xcf, 0xdf, 0xff, 0xc7, 0xcf, 0xde, 0xff, 0xc6, 0xcf, 0xde, 0xff, 0xc6, 0xcf, 0xde, 0xff, 0xc4, 0xcc, 0xdd, 0xff, 0xc2, 0xcb, 0xdc, 0xff, 0xc0, 0xc8, 0xd9, 0xff, 0xba, 0xc2, 0xd3, 0xff, 0xb6, 0xbe, 0xcf, 0xff, 0xb3, 0xbb, 0xcc, 0xff, 0xb0, 0xb8, 0xc9, 0xff, 0xa8, 0xb1, 0xc2, 0xff, 0x9c, 0xa6, 0xb7, 0xff, 0xaa, 0xb3, 0xc3, 0xff, 0xcd, 0xd3, 0xe0, 0xff, 0xd2, 0xd7, 0xe0, 0xff, 0xd7, 0xd9, 0xde, 0xff, 0xd6, 0xd9, 0xdd, 0xff, 0xce, 0xcf, 0xd7, 0xff, 0xca, 0xca, 0xd5, 0xff, 0xc4, 0xc3, 0xd2, 0xff, 0xbd, 0xbb, 0xcd, 0xff, 0xb7, 0xb7, 0xc7, 0xff, 0xba, 0xbc, 0xca, 0xff, 0xb5, 0xb9, 0xc7, 0xff, 0xab, 0xb0, 0xc0, 0xff, 0x9c, 0xa3, 0xb5, 0xff, 0x94, 0x9a, 0xad, 0xff, 0x8e, 0x95, 0xa9, 0xff, 0x85, 0x8e, 0xa2, 0xff, 0x77, 0x81, 0x93, 0xff, 0x7d, 0x87, 0x99, 0xff, 0x90, 0x9a, 0xab, 0xff, 0x9c, 0xa7, 0xb8, 0xff, 0xa4, 0xad, 0xbe, 0xff, 0xa7, 0xaf, 0xc0, 0xff, 0xa9, 0xaf, 0xc2, 0xff, 0xa6, 0xaf, 0xc1, 0xff, 0xa3, 0xae, 0xc0, 0xff, 0xa3, 0xac, 0xbf, 0xff, 0xa0, 0xaa, 0xbf, 0xff, 0x97, 0xa2, 0xb9, 0xff, 0x91, 0x9d, 0xb5, 0xff, 0x8a, 0x96, 0xae, 0xff, 0x81, 0x8c, 0xa7, 0xff, 0x78, 0x85, 0xa0, 0xff, 0x72, 0x7e, 0x95, 0xff, 0x68, 0x73, 0x89, 0xff, 0x5b, 0x67, 0x80, 0xff, 0x4c, 0x59, 0x72, 0xff, 0x45, 0x53, 0x68, 0xff, 0x1c, 0x28, 0x3a, 0xff, 0x35, 0x3e, 0x50, 0xff, 0x8e, 0x97, 0xa8, 0xff, 0x98, 0xa5, 0xb5, 0xff, 0x70, 0x82, 0x91, 0xff, 0x51, 0x68, 0x7a, 0xff, 0x56, 0x72, 0x89, 0xff, 0x2b, 0x46, 0x5b, 0xff, 0x36, 0x4e, 0x61, 0xff, 0x3a, 0x55, 0x6a, 0xff, 0x48, 0x67, 0x7d, 0xff, 0x5b, 0x7a, 0x93, 0xff, 0x51, 0x70, 0x88, 0xff, 0x5a, 0x80, 0x9b, 0xff, 0x4c, 0x73, 0x8d, 0xff, 0x8b, 0xb0, 0xc3, 0xff, 0x7f, 0xa8, 0xbd, 0xff, 0x49, 0x6d, 0x84, 0xff, 0x02, 0x0d, 0x19, 0xff, 0x00, 0x01, 0x0c, 0xff, 0x27, 0x4a, 0x5b, 0xff, 0x57, 0x85, 0x93, 0xff, 0x6c, 0x9c, 0xa8, 0xff, 0x70, 0x98, 0xa4, 0xff, 0x5e, 0x88, 0x92, 0xff, 0x60, 0x8e, 0x9a, 0xff, 0x60, 0x84, 0x90, 0xff, 0x88, 0x9b, 0x9f, 0xff, 0xbc, 0xbe, 0xbf, 0xff, 0xc3, 0xbe, 0xbe, 0xff, 0xc0, 0xbd, 0xbe, 0xff, 0xbc, 0xbb, 0xbc, 0xff, 0xc1, 0xbf, 0xbd, 0xff, 0xbf, 0xbd, 0xbc, 0xff, 0xbe, 0xbc, 0xbb, 0xff, 0xbe, 0xbc, 0xbb, 0xff, 0xbf, 0xbd, 0xbc, 0xff, 0xbf, 0xbc, 0xbb, 0xff, 0xbf, 0xbb, 0xba, 0xff, 0xbf, 0xbb, 0xba, 0xff, 0xbe, 0xba, 0xb9, 0xff, 0xbd, 0xb9, 0xb8, 0xff, 0xbe, 0xba, 0xb9, 0xff, 0xbc, 0xba, 0xb9, 0xff, 0xba, 0xb9, 0xb8, 0xff, 0xb9, 0xb7, 0xb6, 0xff, 0xb6, 0xb5, 0xb4, 0xd3,
    0x61, 0x83, 0x93, 0xbe, 0x52, 0x75, 0x8a, 0xff, 0x58, 0x7a, 0x8e, 0xff, 0x56, 0x77, 0x88, 0xff, 0x54, 0x78, 0x8a, 0xff, 0x47, 0x6a, 0x7d, 0xff, 0x28, 0x48, 0x5c, 0xff, 0x36, 0x55, 0x67, 0xff, 0x4d, 0x68, 0x79, 0xff, 0x36, 0x50, 0x61, 0xff, 0x34, 0x54, 0x68, 0xff, 0x1a, 0x37, 0x49, 0xff, 0x0f, 0x1e, 0x2d, 0xff, 0xbf, 0xc7, 0xcf, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xd4, 0xd8, 0xd9, 0xff, 0xd0, 0xd5, 0xd5, 0xff, 0xca, 0xd0, 0xd2, 0xff, 0xbc, 0xc4, 0xca, 0xff, 0xac, 0xb4, 0xc2, 0xff, 0xa8, 0xb1, 0xc5, 0xff, 0x96, 0xa0, 0xb6, 0xff, 0x5a, 0x65, 0x80, 0xff, 0x22, 0x2e, 0x4b, 0xff, 0x3b, 0x45, 0x61, 0xff, 0x7c, 0x86, 0x9e, 0xff, 0xab, 0xb7, 0xc8, 0xff, 0xb6, 0xc4, 0xd0, 0xff, 0xbe, 0xc9, 0xd8, 0xff, 0xc2, 0xcb, 0xdd, 0xff, 0xc1, 0xca, 0xdc, 0xff, 0xc4, 0xcd, 0xdf, 0xff, 0xc4, 0xcd, 0xdf, 0xff, 0xc4, 0xcd, 0xdf, 0xff, 0xc3, 0xcb, 0xdc, 0xff, 0xc3, 0xcb, 0xdc, 0xff, 0xbf, 0xc7, 0xd8, 0xff, 0xba, 0xc2, 0xd3, 0xff, 0xb4, 0xbc, 0xcd, 0xff, 0xaf, 0xb7, 0xc7, 0xff, 0xaa, 0xb3, 0xc5, 0xff, 0xa4, 0xae, 0xc0, 0xff, 0x9f, 0xa6, 0xb6, 0xff, 0xae, 0xb4, 0xc3, 0xff, 0xcf, 0xd5, 0xe2, 0xff, 0xca, 0xd0, 0xde, 0xff, 0xcd, 0xd2, 0xda, 0xff, 0xd1, 0xd6, 0xdd, 0xff, 0xcc, 0xd0, 0xd9, 0xff, 0xc8, 0xc9, 0xd5, 0xff, 0xc1, 0xc0, 0xd0, 0xff, 0xc1, 0xbe, 0xd1, 0xff, 0xba, 0xbb, 0xcb, 0xff, 0xb6, 0xbc, 0xc9, 0xff, 0xb2, 0xb7, 0xc7, 0xff, 0xa9, 0xb1, 0xc2, 0xff, 0xa0, 0xa7, 0xb9, 0xff, 0x98, 0x9f, 0xb3, 0xff, 0x91, 0x9a, 0xae, 0xff, 0x8b, 0x94, 0xa8, 0xff, 0x7b, 0x85, 0x97, 0xff, 0x7f, 0x89, 0x9b, 0xff, 0x90, 0x9a, 0xac, 0xff, 0x9c, 0xa6, 0xb7, 0xff, 0xa5, 0xae, 0xbe, 0xff, 0xa8, 0xaf, 0xc1, 0xff, 0xa8, 0xaf, 0xc2, 0xff, 0xa7, 0xb0, 0xc3, 0xff, 0xa6, 0xb0, 0xc2, 0xff, 0xa5, 0xae, 0xc2, 0xff, 0xa0, 0xaa, 0xbf, 0xff, 0x97, 0xa3, 0xb9, 0xff, 0x8f, 0x9b, 0xb3, 0xff, 0x8b, 0x97, 0xaf, 0xff, 0x80, 0x8b, 0xa5, 0xff, 0x75, 0x82, 0x9d, 0xff, 0x6f, 0x7b, 0x93, 0xff, 0x65, 0x71, 0x87, 0xff, 0x58, 0x64, 0x7d, 0xff, 0x4a, 0x58, 0x6f, 0xff, 0x45, 0x52, 0x65, 0xff, 0x1c, 0x26, 0x36, 0xff, 0x25, 0x2e, 0x3d, 0xff, 0x86, 0x8f, 0x9e, 0xff, 0xaa, 0xb3, 0xc1, 0xff, 0x90, 0x9d, 0xab, 0xff, 0x68, 0x7b, 0x8d, 0xff, 0x60, 0x7b, 0x94, 0xff, 0x38, 0x53, 0x6a, 0xff, 0x33, 0x4b, 0x5f, 0xff, 0x3e, 0x59, 0x6d, 0xff, 0x46, 0x65, 0x7b, 0xff, 0x57, 0x76, 0x8f, 0xff, 0x5a, 0x79, 0x91, 0xff, 0x63, 0x88, 0xa4, 0xff, 0x42, 0x69, 0x83, 0xff, 0x85, 0xaa, 0xbd, 0xff, 0x88, 0xb3, 0xc7, 0xff, 0x56, 0x7c, 0x93, 0xff, 0x11, 0x23, 0x2e, 0xff, 0x00, 0x00, 0x05, 0xff, 0x23, 0x3e, 0x4e, 0xff, 0x4b, 0x78, 0x85, 0xff, 0x62, 0x91, 0x9f, 0xff, 0x78, 0xa0, 0xad, 0xff, 0x68, 0x90, 0x97, 0xff, 0x67, 0x91, 0x9c, 0xff, 0x62, 0x85, 0x90, 0xff, 0x8f, 0xa1, 0xa4, 0xff, 0xc3, 0xc5, 0xc4, 0xff, 0xc8, 0xc2, 0xc1, 0xff, 0xc4, 0xc3, 0xc3, 0xff, 0xbd, 0xbe, 0xbd, 0xff, 0xc2, 0xbf, 0xbe, 0xff, 0xc1, 0xbf, 0xbe, 0xff, 0xc1, 0xbf, 0xbe, 0xff, 0xc2, 0xc0, 0xbf, 0xff, 0xc1, 0xbf, 0xbe, 0xff, 0xbf, 0xbd, 0xbc, 0xff, 0xc1, 0xbc, 0xbb, 0xff, 0xc1, 0xbd, 0xbc, 0xff, 0xbf, 0xbb, 0xba, 0xff, 0xbf, 0xbb, 0xba, 0xff, 0xbf, 0xba, 0xb9, 0xff, 0xbc, 0xb9, 0xb8, 0xff, 0xba, 0xb9, 0xb8, 0xff, 0xba, 0xb8, 0xb7, 0xff, 0xba, 0xb7, 0xb6, 0xbe,
    0x5b, 0x7c, 0x8d, 0xaa, 0x59, 0x7c, 0x91, 0xff, 0x55, 0x78, 0x8c, 0xff, 0x52, 0x73, 0x84, 0xff, 0x58, 0x7c, 0x8d, 0xff, 0x45, 0x66, 0x7a, 0xff, 0x39, 0x59, 0x6c, 0xff, 0x4c, 0x6b, 0x7c, 0xff, 0x47, 0x62, 0x73, 0xff, 0x24, 0x3e, 0x4f, 0xff, 0x2b, 0x49, 0x5a, 0xff, 0x0d, 0x24, 0x33, 0xff, 0x97, 0xa1, 0xaa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdc, 0xdc, 0xde, 0xff, 0xd4, 0xd9, 0xd9, 0xff, 0xd3, 0xd6, 0xda, 0xff, 0xca, 0xcd, 0xd2, 0xff, 0xb0, 0xb7, 0xc0, 0xff, 0xa1, 0xaa, 0xba, 0xff, 0x96, 0xa4, 0xb7, 0xff, 0x5c, 0x6a, 0x81, 0xff, 0x21, 0x32, 0x47, 0xff, 0x0e, 0x1d, 0x33, 0xff, 0x3e, 0x48, 0x60, 0xff, 0x80, 0x89, 0xa0, 0xff, 0x9d, 0xa9, 0xbd, 0xff, 0xac, 0xbc, 0xca, 0xff, 0xb8, 0xc4, 0xd3, 0xff, 0xbe, 0xc8, 0xd9, 0xff, 0xbe, 0xc8, 0xd9, 0xff, 0xc1, 0xcb, 0xdc, 0xff, 0xc3, 0xcd, 0xde, 0xff, 0xc3, 0xcd, 0xde, 0xff, 0xc3, 0xcb, 0xdc, 0xff, 0xc2, 0xc8, 0xd9, 0xff, 0xbe, 0xc6, 0xd7, 0xff, 0xb9, 0xc1, 0xd2, 0xff, 0xb3, 0xbb, 0xcc, 0xff, 0xaf, 0xb6, 0xc7, 0xff, 0xa7, 0xb1, 0xc4, 0xff, 0xa0, 0xac, 0xbe, 0xff, 0xa3, 0xaa, 0xba, 0xff, 0xa8, 0xad, 0xba, 0xff, 0xc2, 0xc6, 0xd5, 0xff, 0xbc, 0xc2, 0xd3, 0xff, 0xad, 0xb5, 0xc5, 0xff, 0xba, 0xc1, 0xd0, 0xff, 0xc4, 0xca, 0xd9, 0xff, 0xbd, 0xc1, 0xd2, 0xff, 0xbe, 0xbf, 0xd1, 0xff, 0xbe, 0xbd, 0xcf, 0xff, 0xb8, 0xba, 0xcc, 0xff, 0xac, 0xb1, 0xc3, 0xff, 0xa3, 0xa9, 0xbc, 0xff, 0x8c, 0x92, 0xa6, 0xff, 0x83, 0x8b, 0xa0, 0xff, 0x90, 0x99, 0xb1, 0xff, 0x93, 0x9d, 0xb2, 0xff, 0x84, 0x8d, 0xa0, 0xff, 0x7c, 0x86, 0x98, 0xff, 0x8b, 0x95, 0xa7, 0xff, 0x8d, 0x97, 0xa9, 0xff, 0x94, 0x9e, 0xaf, 0xff, 0xa0, 0xa9, 0xba, 0xff, 0xaa, 0xb1, 0xc2, 0xff, 0xab, 0xb1, 0xc4, 0xff, 0xa6, 0xaf, 0xc1, 0xff, 0xa5, 0xaf, 0xc1, 0xff, 0xa5, 0xad, 0xc1, 0xff, 0xa1, 0xab, 0xc0, 0xff, 0x9a, 0xa6, 0xbc, 0xff, 0x8f, 0x9b, 0xb3, 0xff, 0x89, 0x95, 0xad, 0xff, 0x7f, 0x8b, 0xa6, 0xff, 0x74, 0x81, 0x9c, 0xff, 0x6e, 0x7a, 0x92, 0xff, 0x63, 0x6f, 0x85, 0xff, 0x54, 0x61, 0x79, 0xff, 0x47, 0x55, 0x6a, 0xff, 0x41, 0x4b, 0x5d, 0xff, 0x1e, 0x24, 0x32, 0xff, 0x0e, 0x16, 0x23, 0xff, 0x72, 0x7c, 0x88, 0xff, 0xa6, 0xad, 0xb9, 0xff, 0x9f, 0xa8, 0xb6, 0xff, 0x7c, 0x8d, 0x9f, 0xff, 0x66, 0x81, 0x98, 0xff, 0x48, 0x63, 0x79, 0xff, 0x3b, 0x53, 0x67, 0xff, 0x36, 0x51, 0x66, 0xff, 0x4c, 0x6a, 0x80, 0xff, 0x60, 0x7f, 0x98, 0xff, 0x57, 0x76, 0x8e, 0xff, 0x63, 0x87, 0xa3, 0xff, 0x45, 0x6b, 0x86, 0xff, 0x7b, 0xa1, 0xb3, 0xff, 0x88, 0xb4, 0xc9, 0xff, 0x6a, 0x92, 0xa8, 0xff, 0x22, 0x38, 0x43, 0xff, 0x00, 0x00, 0x00, 0xff, 0x15, 0x2a, 0x39, 0xff, 0x3a, 0x68, 0x74, 0xff, 0x4e, 0x7d, 0x8b, 0xff, 0x78, 0xa0, 0xad, 0xff, 0x69, 0x90, 0x96, 0xff, 0x6d, 0x96, 0x9d, 0xff, 0x6d, 0x8c, 0x98, 0xff, 0x98, 0xa7, 0xab, 0xff, 0xc2, 0xc2, 0xc1, 0xff, 0xcb, 0xc5, 0xc3, 0xff, 0xc2, 0xc3, 0xc1, 0xff, 0xc0, 0xc1, 0xc0, 0xff, 0xc5, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc2, 0xc0, 0xbf, 0xff, 0xc3, 0xc1, 0xc0, 0xff, 0xc3, 0xc1, 0xc0, 0xff, 0xc1, 0xbe, 0xbd, 0xff, 0xc2, 0xbd, 0xbc, 0xff, 0xc3, 0xbf, 0xbe, 0xff, 0xc2, 0xbe, 0xbd, 0xff, 0xc0, 0xbc, 0xbb, 0xff, 0xc0, 0xbb, 0xba, 0xff, 0xbe, 0xbb, 0xba, 0xff, 0xbd, 0xbb, 0xba, 0xff, 0xbe, 0xbc, 0xbb, 0xff, 0xbd, 0xba, 0xba, 0xab,
    0x64, 0x84, 0x96, 0x8b, 0x60, 0x84, 0x99, 0xff, 0x52, 0x74, 0x88, 0xff, 0x5c, 0x7d, 0x8e, 0xff, 0x5b, 0x7d, 0x8f, 0xff, 0x38, 0x5a, 0x6d, 0xff, 0x32, 0x52, 0x63, 0xff, 0x5b, 0x78, 0x88, 0xff, 0x33, 0x4d, 0x5d, 0xff, 0x22, 0x3c, 0x4c, 0xff, 0x0d, 0x2a, 0x38, 0xff, 0x64, 0x79, 0x83, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe8, 0xe4, 0xe6, 0xff, 0xda, 0xda, 0xdb, 0xff, 0xd3, 0xd9, 0xda, 0xff, 0xce, 0xd0, 0xd9, 0xff, 0xbe, 0xc0, 0xca, 0xff, 0xaf, 0xb6, 0xc0, 0xff, 0xa0, 0xab, 0xbb, 0xff, 0x58, 0x67, 0x7a, 0xff, 0x12, 0x25, 0x38, 0xff, 0x22, 0x39, 0x46, 0xff, 0x1a, 0x2d, 0x3a, 0xff, 0x28, 0x34, 0x47, 0xff, 0x7a, 0x82, 0x97, 0xff, 0x96, 0xa0, 0xb3, 0xff, 0xa3, 0xb3, 0xc4, 0xff, 0xb0, 0xbd, 0xcd, 0xff, 0xb9, 0xc2, 0xd3, 0xff, 0xbb, 0xc4, 0xd6, 0xff, 0xbe, 0xc7, 0xd9, 0xff, 0xc1, 0xcb, 0xdd, 0xff, 0xc1, 0xcc, 0xdd, 0xff, 0xc1, 0xc9, 0xda, 0xff, 0xbf, 0xc6, 0xd8, 0xff, 0xbb, 0xc3, 0xd4, 0xff, 0xb6, 0xbe, 0xd0, 0xff, 0xb2, 0xba, 0xcb, 0xff, 0xaf, 0xb6, 0xc7, 0xff, 0xa3, 0xb1, 0xc4, 0xff, 0xa1, 0xac, 0xc0, 0xff, 0xaa, 0xb0, 0xbe, 0xff, 0xaa, 0xac, 0xb9, 0xff, 0xae, 0xb2, 0xc1, 0xff, 0xaa, 0xae, 0xc2, 0xff, 0x7e, 0x87, 0xa1, 0xff, 0x71, 0x7b, 0x94, 0xff, 0xa3, 0xaa, 0xc2, 0xff, 0xba, 0xbf, 0xd5, 0xff, 0xb2, 0xb6, 0xc9, 0xff, 0xb0, 0xb2, 0xc3, 0xff, 0xa7, 0xab, 0xbe, 0xff, 0x9a, 0xa0, 0xb4, 0xff, 0x81, 0x85, 0x9d, 0xff, 0x54, 0x5a, 0x74, 0xff, 0x58, 0x60, 0x7b, 0xff, 0x79, 0x83, 0xa0, 0xff, 0x82, 0x8b, 0xa3, 0xff, 0x7b, 0x85, 0x98, 0xff, 0x89, 0x93, 0xa6, 0xff, 0x92, 0x9c, 0xaf, 0xff, 0x90, 0x9a, 0xad, 0xff, 0x8e, 0x99, 0xaa, 0xff, 0x97, 0x9f, 0xb0, 0xff, 0xa4, 0xac, 0xbd, 0xff, 0xaa, 0xb0, 0xc4, 0xff, 0xa7, 0xb0, 0xc2, 0xff, 0xa4, 0xaf, 0xc1, 0xff, 0xa5, 0xae, 0xc1, 0xff, 0x9f, 0xaa, 0xbe, 0xff, 0x98, 0xa4, 0xba, 0xff, 0x8c, 0x98, 0xb0, 0xff, 0x85, 0x91, 0xa9, 0xff, 0x7f, 0x8a, 0xa5, 0xff, 0x75, 0x81, 0x9c, 0xff, 0x6c, 0x79, 0x90, 0xff, 0x5f, 0x6b, 0x83, 0xff, 0x4f, 0x5b, 0x74, 0xff, 0x40, 0x4d, 0x62, 0xff, 0x39, 0x43, 0x55, 0xff, 0x1f, 0x24, 0x30, 0xff, 0x00, 0x00, 0x06, 0xff, 0x4c, 0x56, 0x60, 0xff, 0xa3, 0xaa, 0xb5, 0xff, 0xa6, 0xad, 0xbb, 0xff, 0x81, 0x8f, 0xa2, 0xff, 0x64, 0x7d, 0x96, 0xff, 0x56, 0x71, 0x88, 0xff, 0x42, 0x5a, 0x6d, 0xff, 0x34, 0x4f, 0x64, 0xff, 0x45, 0x63, 0x79, 0xff, 0x66, 0x85, 0x9e, 0xff, 0x57, 0x76, 0x8e, 0xff, 0x63, 0x88, 0xa4, 0xff, 0x48, 0x6e, 0x89, 0xff, 0x6a, 0x90, 0xa2, 0xff, 0x88, 0xb5, 0xc9, 0xff, 0x70, 0x9b, 0xb0, 0xff, 0x3b, 0x51, 0x5c, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x23, 0x30, 0xff, 0x31, 0x5d, 0x6c, 0xff, 0x32, 0x60, 0x70, 0xff, 0x6f, 0x96, 0xa3, 0xff, 0x74, 0x9a, 0x9f, 0xff, 0x72, 0x98, 0xa0, 0xff, 0x73, 0x91, 0x9c, 0xff, 0x9c, 0xac, 0xaf, 0xff, 0xc5, 0xc6, 0xc3, 0xff, 0xcc, 0xc6, 0xc2, 0xff, 0xc2, 0xc4, 0xc2, 0xff, 0xc2, 0xc4, 0xc4, 0xff, 0xc5, 0xc2, 0xc1, 0xff, 0xc5, 0xc3, 0xc2, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc3, 0xc2, 0xff, 0xc5, 0xc4, 0xc3, 0xff, 0xc4, 0xc1, 0xc0, 0xff, 0xc4, 0xbf, 0xbe, 0xff, 0xc5, 0xc1, 0xc0, 0xff, 0xc2, 0xbe, 0xbd, 0xff, 0xc2, 0xbe, 0xbd, 0xff, 0xc5, 0xc0, 0xbf, 0xff, 0xc2, 0xbf, 0xbe, 0xff, 0xbf, 0xbd, 0xbc, 0xff, 0xbe, 0xbc, 0xbb, 0xff, 0xbc, 0xbb, 0xb9, 0x8b,
    0x6d, 0x8c, 0x98, 0x6b, 0x57, 0x77, 0x8a, 0xff, 0x4b, 0x6d, 0x80, 0xff, 0x54, 0x77, 0x89, 0xff, 0x46, 0x66, 0x78, 0xff, 0x26, 0x44, 0x57, 0xff, 0x46, 0x63, 0x74, 0xff, 0x5a, 0x75, 0x86, 0xff, 0x16, 0x30, 0x43, 0xff, 0x10, 0x2e, 0x42, 0xff, 0x31, 0x42, 0x4d, 0xff, 0xe5, 0xeb, 0xf0, 0xff, 0xf1, 0xf2, 0xf6, 0xff, 0xda, 0xdb, 0xde, 0xff, 0xd4, 0xd6, 0xd9, 0xff, 0xcf, 0xd4, 0xd9, 0xff, 0xc7, 0xcb, 0xd2, 0xff, 0xb8, 0xbb, 0xc6, 0xff, 0xb1, 0xb6, 0xc4, 0xff, 0x93, 0x9d, 0xaf, 0xff, 0x2d, 0x3b, 0x4e, 0xff, 0x09, 0x1d, 0x2f, 0xff, 0x2d, 0x42, 0x52, 0xff, 0x22, 0x31, 0x3c, 0xff, 0x19, 0x22, 0x2d, 0xff, 0x65, 0x71, 0x85, 0xff, 0x88, 0x9b, 0xb2, 0xff, 0x92, 0xa7, 0xba, 0xff, 0xa4, 0xae, 0xc7, 0xff, 0xac, 0xb6, 0xcd, 0xff, 0xb0, 0xbf, 0xcd, 0xff, 0xb5, 0xc4, 0xd0, 0xff, 0xbd, 0xc8, 0xd8, 0xff, 0xc1, 0xc6, 0xdb, 0xff, 0xbf, 0xc6, 0xd7, 0xff, 0xbc, 0xc5, 0xd3, 0xff, 0xba, 0xc1, 0xd0, 0xff, 0xb6, 0xbd, 0xcd, 0xff, 0xb1, 0xb9, 0xc9, 0xff, 0xa8, 0xb3, 0xc4, 0xff, 0xa5, 0xb0, 0xc0, 0xff, 0xa7, 0xaf, 0xbe, 0xff, 0xab, 0xb3, 0xc0, 0xff, 0xac, 0xb3, 0xc0, 0xff, 0xa9, 0xb0, 0xbd, 0xff, 0x9e, 0xa7, 0xb3, 0xff, 0xa1, 0xa6, 0xba, 0xff, 0x9b, 0x9f, 0xb4, 0xff, 0xab, 0xb0, 0xc2, 0xff, 0xbe, 0xc2, 0xd4, 0xff, 0xab, 0xae, 0xc1, 0xff, 0xa2, 0xa3, 0xb7, 0xff, 0x9b, 0x9e, 0xb4, 0xff, 0x88, 0x8c, 0xa4, 0xff, 0x7b, 0x7e, 0x98, 0xff, 0x75, 0x7a, 0x94, 0xff, 0x6f, 0x76, 0x8d, 0xff, 0x69, 0x70, 0x88, 0xff, 0x77, 0x7f, 0x92, 0xff, 0x8b, 0x94, 0xa3, 0xff, 0x96, 0x9f, 0xae, 0xff, 0x92, 0x9c, 0xab, 0xff, 0x96, 0x9f, 0xae, 0xff, 0x95, 0x9e, 0xad, 0xff, 0x95, 0x9f, 0xad, 0xff, 0x9e, 0xa8, 0xb9, 0xff, 0xa4, 0xad, 0xbe, 0xff, 0xa5, 0xaf, 0xc1, 0xff, 0xa2, 0xac, 0xbf, 0xff, 0xa1, 0xaa, 0xbf, 0xff, 0x9a, 0xa3, 0xb9, 0xff, 0x92, 0x9e, 0xb4, 0xff, 0x89, 0x95, 0xad, 0xff, 0x7f, 0x8b, 0xa4, 0xff, 0x7a, 0x86, 0xa1, 0xff, 0x6c, 0x7a, 0x96, 0xff, 0x61, 0x70, 0x89, 0xff, 0x58, 0x66, 0x7d, 0xff, 0x48, 0x55, 0x6c, 0xff, 0x3b, 0x48, 0x5b, 0xff, 0x30, 0x3b, 0x4b, 0xff, 0x1a, 0x23, 0x2f, 0xff, 0x00, 0x00, 0x09, 0xff, 0x25, 0x27, 0x33, 0xff, 0x97, 0x9c, 0xa6, 0xff, 0xa2, 0xac, 0xb8, 0xff, 0x80, 0x92, 0xa2, 0xff, 0x59, 0x76, 0x89, 0xff, 0x5a, 0x77, 0x8a, 0xff, 0x4f, 0x67, 0x7b, 0xff, 0x34, 0x50, 0x64, 0xff, 0x3a, 0x59, 0x6f, 0xff, 0x66, 0x85, 0x9e, 0xff, 0x5d, 0x7c, 0x95, 0xff, 0x6a, 0x8d, 0xa6, 0xff, 0x53, 0x79, 0x91, 0xff, 0x60, 0x88, 0x9c, 0xff, 0x87, 0xb2, 0xc5, 0xff, 0x7b, 0xa2, 0xb5, 0xff, 0x59, 0x79, 0x8b, 0xff, 0x00, 0x00, 0x07, 0xff, 0x0a, 0x13, 0x1a, 0xff, 0x31, 0x53, 0x63, 0xff, 0x28, 0x59, 0x6c, 0xff, 0x60, 0x92, 0xa0, 0xff, 0x70, 0x9a, 0x9f, 0xff, 0x71, 0x98, 0x9c, 0xff, 0x77, 0x97, 0x9c, 0xff, 0xa5, 0xb1, 0xb4, 0xff, 0xca, 0xcb, 0xca, 0xff, 0xc5, 0xc5, 0xc2, 0xff, 0xc7, 0xc5, 0xc5, 0xff, 0xc8, 0xc5, 0xc5, 0xff, 0xc4, 0xc3, 0xc2, 0xff, 0xc4, 0xc3, 0xc2, 0xff, 0xc5, 0xc2, 0xc2, 0xff, 0xc6, 0xc2, 0xc2, 0xff, 0xc8, 0xc3, 0xc4, 0xff, 0xc6, 0xc2, 0xc2, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc3, 0xc0, 0xbf, 0xff, 0xc4, 0xc1, 0xc0, 0xff, 0xc3, 0xc1, 0xc0, 0xff, 0xc3, 0xc0, 0xbf, 0xff, 0xc1, 0xbf, 0xbe, 0xff, 0xc0, 0xbe, 0xbd, 0xff, 0xbe, 0xbc, 0xbc, 0x6b,
    0x51, 0x70, 0x7d, 0x4b, 0x3d, 0x5a, 0x6b, 0xff, 0x40, 0x61, 0x74, 0xff, 0x4c, 0x6e, 0x81, 0xff, 0x3e, 0x5c, 0x6f, 0xff, 0x38, 0x53, 0x68, 0xff, 0x5f, 0x79, 0x8b, 0xff, 0x32, 0x4d, 0x5d, 0xff, 0x0f, 0x2a, 0x3d, 0xff, 0x10, 0x2d, 0x43, 0xff, 0xa3, 0xad, 0xb6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd8, 0xdc, 0xdd, 0xff, 0xd5, 0xda, 0xdb, 0xff, 0xcf, 0xd4, 0xd7, 0xff, 0xca, 0xd0, 0xd5, 0xff, 0xbf, 0xc5, 0xcc, 0xff, 0xb8, 0xbd, 0xc7, 0xff, 0xb1, 0xb8, 0xc7, 0xff, 0x6c, 0x76, 0x89, 0xff, 0x24, 0x33, 0x44, 0xff, 0x18, 0x2d, 0x3d, 0xff, 0x2e, 0x43, 0x57, 0xff, 0x27, 0x33, 0x40, 0xff, 0x05, 0x0a, 0x12, 0xff, 0x35, 0x41, 0x53, 0xff, 0x6f, 0x83, 0x9a, 0xff, 0x80, 0x93, 0xa4, 0xff, 0x8c, 0x96, 0xac, 0xff, 0x96, 0xa1, 0xb9, 0xff, 0xa1, 0xb1, 0xc4, 0xff, 0xa8, 0xb8, 0xca, 0xff, 0xb5, 0xc1, 0xd3, 0xff, 0xba, 0xc1, 0xd5, 0xff, 0xb9, 0xc2, 0xd0, 0xff, 0xba, 0xc2, 0xcf, 0xff, 0xba, 0xc1, 0xd0, 0xff, 0xb5, 0xbc, 0xcb, 0xff, 0xae, 0xb7, 0xc5, 0xff, 0xa7, 0xb4, 0xc2, 0xff, 0xa8, 0xb2, 0xc0, 0xff, 0xad, 0xb6, 0xc2, 0xff, 0xae, 0xb7, 0xc5, 0xff, 0xb1, 0xba, 0xc7, 0xff, 0xb2, 0xbc, 0xc6, 0xff, 0xb1, 0xba, 0xc2, 0xff, 0xb6, 0xb8, 0xc5, 0xff, 0xbc, 0xbd, 0xcc, 0xff, 0xbc, 0xbe, 0xca, 0xff, 0xb7, 0xba, 0xc6, 0xff, 0xb0, 0xb3, 0xc2, 0xff, 0xa5, 0xa6, 0xb9, 0xff, 0x9b, 0x9d, 0xb3, 0xff, 0x8f, 0x94, 0xaa, 0xff, 0x8c, 0x91, 0xa6, 0xff, 0x88, 0x8e, 0xa2, 0xff, 0x7d, 0x85, 0x97, 0xff, 0x81, 0x87, 0x99, 0xff, 0x8c, 0x94, 0xa3, 0xff, 0x94, 0x9d, 0xa9, 0xff, 0x96, 0x9f, 0xac, 0xff, 0x95, 0x9e, 0xab, 0xff, 0x97, 0xa0, 0xad, 0xff, 0x9a, 0xa3, 0xb0, 0xff, 0x99, 0xa3, 0xb1, 0xff, 0x9c, 0xa7, 0xb7, 0xff, 0xa2, 0xad, 0xbe, 0xff, 0xa2, 0xad, 0xbf, 0xff, 0x9e, 0xa7, 0xbb, 0xff, 0x97, 0xa1, 0xb7, 0xff, 0x8f, 0x9a, 0xb1, 0xff, 0x89, 0x96, 0xac, 0xff, 0x82, 0x8d, 0xa5, 0xff, 0x7b, 0x87, 0x9f, 0xff, 0x72, 0x7f, 0x99, 0xff, 0x66, 0x74, 0x90, 0xff, 0x59, 0x69, 0x83, 0xff, 0x4e, 0x5c, 0x74, 0xff, 0x3e, 0x4b, 0x60, 0xff, 0x33, 0x3f, 0x4f, 0xff, 0x2f, 0x38, 0x46, 0xff, 0x18, 0x23, 0x2e, 0xff, 0x06, 0x0b, 0x15, 0xff, 0x00, 0x00, 0x06, 0xff, 0x5d, 0x61, 0x69, 0xff, 0x9a, 0xa6, 0xae, 0xff, 0x85, 0x98, 0xa5, 0xff, 0x5d, 0x78, 0x8a, 0xff, 0x5b, 0x76, 0x8a, 0xff, 0x5f, 0x77, 0x8c, 0xff, 0x37, 0x53, 0x68, 0xff, 0x37, 0x56, 0x6c, 0xff, 0x68, 0x87, 0xa0, 0xff, 0x60, 0x7f, 0x98, 0xff, 0x6c, 0x8f, 0xa7, 0xff, 0x68, 0x8d, 0xa6, 0xff, 0x60, 0x87, 0x9d, 0xff, 0x86, 0xae, 0xc1, 0xff, 0x80, 0xa4, 0xb7, 0xff, 0x68, 0x8f, 0xa5, 0xff, 0x08, 0x16, 0x22, 0xff, 0x00, 0x00, 0x01, 0xff, 0x24, 0x43, 0x52, 0xff, 0x26, 0x57, 0x6a, 0xff, 0x59, 0x8d, 0x9b, 0xff, 0x74, 0x9e, 0xa4, 0xff, 0x68, 0x90, 0x92, 0xff, 0x7f, 0x9e, 0xa1, 0xff, 0xae, 0xb6, 0xb8, 0xff, 0xcc, 0xcc, 0xcd, 0xff, 0xc4, 0xc8, 0xc7, 0xff, 0xcb, 0xc6, 0xc7, 0xff, 0xca, 0xc5, 0xc5, 0xff, 0xc3, 0xc3, 0xc3, 0xff, 0xc5, 0xc3, 0xc3, 0xff, 0xc6, 0xc3, 0xc4, 0xff, 0xc8, 0xc2, 0xc3, 0xff, 0xc8, 0xc2, 0xc4, 0xff, 0xc6, 0xc2, 0xc2, 0xff, 0xc4, 0xc3, 0xc2, 0xff, 0xc4, 0xc3, 0xc2, 0xff, 0xc3, 0xc1, 0xc0, 0xff, 0xc3, 0xc1, 0xc0, 0xff, 0xc3, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc3, 0xc1, 0xc0, 0xff, 0xc2, 0xc0, 0xbf, 0xff, 0xc1, 0xbe, 0xbe, 0x4b,
    0x5c, 0x73, 0x83, 0x21, 0x51, 0x6f, 0x82, 0xff, 0x51, 0x72, 0x85, 0xff, 0x4e, 0x70, 0x83, 0xff, 0x3b, 0x58, 0x6b, 0xff, 0x36, 0x51, 0x63, 0xff, 0x48, 0x62, 0x73, 0xff, 0x1f, 0x36, 0x48, 0xff, 0x08, 0x1e, 0x2f, 0xff, 0x6d, 0x82, 0x90, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xde, 0xe3, 0xe3, 0xff, 0xd7, 0xdc, 0xdb, 0xff, 0xd4, 0xd9, 0xd7, 0xff, 0xcf, 0xd5, 0xd5, 0xff, 0xc2, 0xca, 0xcd, 0xff, 0xb4, 0xb9, 0xc2, 0xff, 0xb3, 0xb9, 0xc5, 0xff, 0x97, 0xa0, 0xb0, 0xff, 0x37, 0x45, 0x57, 0xff, 0x24, 0x34, 0x45, 0xff, 0x1d, 0x31, 0x41, 0xff, 0x39, 0x4f, 0x64, 0xff, 0x09, 0x16, 0x25, 0xff, 0x00, 0x02, 0x0b, 0xff, 0x72, 0x7a, 0x87, 0xff, 0x99, 0xa6, 0xb6, 0xff, 0xa6, 0xb0, 0xb7, 0xff, 0x9b, 0xa7, 0xaf, 0xff, 0x82, 0x8e, 0xa0, 0xff, 0x84, 0x90, 0xad, 0xff, 0x9a, 0xa7, 0xc6, 0xff, 0xa6, 0xb2, 0xcc, 0xff, 0xad, 0xbc, 0xcc, 0xff, 0xb0, 0xbd, 0xcc, 0xff, 0xb7, 0xc0, 0xcf, 0xff, 0xb6, 0xbd, 0xcc, 0xff, 0xb3, 0xba, 0xc9, 0xff, 0xaf, 0xb6, 0xc5, 0xff, 0xa9, 0xb2, 0xc0, 0xff, 0xac, 0xb6, 0xc4, 0xff, 0xb1, 0xba, 0xc7, 0xff, 0xb3, 0xbb, 0xc9, 0xff, 0xb2, 0xbb, 0xc7, 0xff, 0xb3, 0xbb, 0xc5, 0xff, 0xb8, 0xbf, 0xca, 0xff, 0xba, 0xbf, 0xcb, 0xff, 0xb9, 0xbc, 0xc8, 0xff, 0xba, 0xbe, 0xc8, 0xff, 0xb9, 0xbe, 0xc7, 0xff, 0xae, 0xb2, 0xbe, 0xff, 0xa3, 0xa6, 0xb6, 0xff, 0x9b, 0x9f, 0xb0, 0xff, 0x9b, 0xa1, 0xb0, 0xff, 0x96, 0x9d, 0xad, 0xff, 0x8f, 0x96, 0xa6, 0xff, 0x8d, 0x95, 0xa5, 0xff, 0x91, 0x9a, 0xa9, 0xff, 0x93, 0x9c, 0xab, 0xff, 0x93, 0x9c, 0xaa, 0xff, 0x95, 0x9e, 0xac, 0xff, 0x98, 0xa1, 0xaf, 0xff, 0x97, 0xa0, 0xae, 0xff, 0x9c, 0xa5, 0xb3, 0xff, 0x9e, 0xa7, 0xb5, 0xff, 0x9e, 0xa6, 0xb7, 0xff, 0x9d, 0xa6, 0xb8, 0xff, 0x9f, 0xa9, 0xbb, 0xff, 0x9a, 0xa4, 0xb8, 0xff, 0x92, 0x9d, 0xb2, 0xff, 0x86, 0x94, 0xaa, 0xff, 0x7f, 0x8c, 0xa2, 0xff, 0x78, 0x83, 0x9c, 0xff, 0x75, 0x82, 0x9a, 0xff, 0x6c, 0x7a, 0x92, 0xff, 0x65, 0x72, 0x8c, 0xff, 0x57, 0x64, 0x7d, 0xff, 0x45, 0x53, 0x68, 0xff, 0x35, 0x42, 0x55, 0xff, 0x2e, 0x38, 0x48, 0xff, 0x23, 0x2c, 0x38, 0xff, 0x10, 0x1a, 0x25, 0xff, 0x0a, 0x0f, 0x15, 0xff, 0x00, 0x02, 0x01, 0xff, 0x12, 0x16, 0x1a, 0xff, 0x7e, 0x88, 0x90, 0xff, 0x91, 0xa1, 0xaf, 0xff, 0x6b, 0x80, 0x96, 0xff, 0x61, 0x79, 0x8f, 0xff, 0x67, 0x81, 0x94, 0xff, 0x46, 0x61, 0x76, 0xff, 0x36, 0x55, 0x6b, 0xff, 0x5e, 0x7d, 0x96, 0xff, 0x67, 0x86, 0x9f, 0xff, 0x6f, 0x92, 0xaa, 0xff, 0x71, 0x97, 0xb0, 0xff, 0x62, 0x89, 0x9e, 0xff, 0x87, 0xb0, 0xc2, 0xff, 0x80, 0xa4, 0xb7, 0xff, 0x68, 0x8d, 0xa1, 0xff, 0x21, 0x32, 0x3e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x22, 0x3e, 0x4e, 0xff, 0x26, 0x4f, 0x62, 0xff, 0x44, 0x6f, 0x7e, 0xff, 0x73, 0x9c, 0xa6, 0xff, 0x6d, 0x94, 0x98, 0xff, 0x8a, 0xa6, 0xaa, 0xff, 0xb1, 0xb8, 0xbb, 0xff, 0xc9, 0xc9, 0xca, 0xff, 0xc6, 0xc9, 0xc7, 0xff, 0xcb, 0xc8, 0xc9, 0xff, 0xc9, 0xc5, 0xc6, 0xff, 0xc1, 0xc2, 0xc2, 0xff, 0xc6, 0xc4, 0xc4, 0xff, 0xc8, 0xc6, 0xc6, 0xff, 0xc8, 0xc4, 0xc5, 0xff, 0xc8, 0xc2, 0xc4, 0xff, 0xc6, 0xc2, 0xc2, 0xff, 0xc4, 0xc3, 0xc2, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc3, 0xc1, 0xc0, 0xff, 0xc1, 0xc1, 0xc1, 0x21,
    0x00, 0x00, 0xff, 0x01, 0x44, 0x61, 0x76, 0xf2, 0x4e, 0x6d, 0x81, 0xff, 0x3b, 0x5b, 0x6d, 0xff, 0x2e, 0x49, 0x59, 0xff, 0x35, 0x4f, 0x60, 0xff, 0x34, 0x4d, 0x5f, 0xff, 0x08, 0x1d, 0x2e, 0xff, 0x43, 0x50, 0x5e, 0xff, 0xe3, 0xee, 0xf4, 0xff, 0xee, 0xf4, 0xf7, 0xff, 0xd6, 0xdb, 0xda, 0xff, 0xd7, 0xdd, 0xda, 0xff, 0xd6, 0xda, 0xda, 0xff, 0xcd, 0xd3, 0xd5, 0xff, 0xbb, 0xc4, 0xc8, 0xff, 0xaa, 0xb1, 0xba, 0xff, 0xac, 0xb4, 0xc1, 0xff, 0x64, 0x72, 0x84, 0xff, 0x22, 0x33, 0x44, 0xff, 0x2a, 0x3c, 0x4e, 0xff, 0x20, 0x32, 0x42, 0xff, 0x23, 0x35, 0x48, 0xff, 0x1c, 0x26, 0x34, 0xff, 0xad, 0xaf, 0xb4, 0xff, 0xe7, 0xea, 0xf2, 0xff, 0xd6, 0xdc, 0xe6, 0xff, 0xd3, 0xd7, 0xd8, 0xff, 0xc4, 0xcc, 0xcd, 0xff, 0xa9, 0xb3, 0xc2, 0xff, 0x72, 0x7b, 0x99, 0xff, 0x6c, 0x77, 0x9b, 0xff, 0x90, 0x9b, 0xba, 0xff, 0x9e, 0xae, 0xc0, 0xff, 0xaa, 0xb8, 0xc8, 0xff, 0xaf, 0xb9, 0xca, 0xff, 0xb2, 0xb9, 0xc9, 0xff, 0xb3, 0xbb, 0xc9, 0xff, 0xaf, 0xb7, 0xc4, 0xff, 0xaa, 0xb3, 0xc0, 0xff, 0xb1, 0xb9, 0xc6, 0xff, 0xb2, 0xba, 0xc8, 0xff, 0xb1, 0xb9, 0xc6, 0xff, 0xb2, 0xb9, 0xc4, 0xff, 0xb5, 0xba, 0xc5, 0xff, 0xb4, 0xbb, 0xc4, 0xff, 0xb9, 0xbf, 0xc8, 0xff, 0xbf, 0xc3, 0xcc, 0xff, 0xc0, 0xc5, 0xcd, 0xff, 0xbe, 0xc4, 0xcc, 0xff, 0xb2, 0xb6, 0xc0, 0xff, 0xa1, 0xa4, 0xb2, 0xff, 0x9c, 0xa2, 0xae, 0xff, 0xa3, 0xaa, 0xb4, 0xff, 0xa6, 0xac, 0xb9, 0xff, 0x9a, 0xa2, 0xaf, 0xff, 0x95, 0x9d, 0xab, 0xff, 0x96, 0x9e, 0xad, 0xff, 0x98, 0xa1, 0xaf, 0xff, 0x95, 0x9e, 0xac, 0xff, 0x96, 0x9f, 0xad, 0xff, 0x9b, 0xa4, 0xb2, 0xff, 0x9e, 0xa7, 0xb5, 0xff, 0xa1, 0xaa, 0xb8, 0xff, 0x9f, 0xa7, 0xb6, 0xff, 0x9d, 0xa4, 0xb5, 0xff, 0x98, 0xa1, 0xb3, 0xff, 0x96, 0xa1, 0xb3, 0xff, 0x93, 0x9f, 0xb2, 0xff, 0x8c, 0x99, 0xad, 0xff, 0x7f, 0x8c, 0xa3, 0xff, 0x79, 0x86, 0x9d, 0xff, 0x77, 0x85, 0x9c, 0xff, 0x6f, 0x7d, 0x94, 0xff, 0x68, 0x74, 0x8b, 0xff, 0x5f, 0x6c, 0x82, 0xff, 0x4c, 0x5a, 0x6f, 0xff, 0x3c, 0x49, 0x5c, 0xff, 0x33, 0x3e, 0x4f, 0xff, 0x28, 0x31, 0x3f, 0xff, 0x09, 0x12, 0x1c, 0xff, 0x00, 0x00, 0x05, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x62, 0x6a, 0x72, 0xff, 0x93, 0xa3, 0xb3, 0xff, 0x69, 0x7c, 0x94, 0xff, 0x5f, 0x76, 0x8d, 0xff, 0x6f, 0x88, 0x9c, 0xff, 0x59, 0x74, 0x89, 0xff, 0x3b, 0x59, 0x6f, 0xff, 0x4b, 0x6a, 0x83, 0xff, 0x6b, 0x8a, 0xa3, 0xff, 0x74, 0x97, 0xaf, 0xff, 0x6f, 0x95, 0xad, 0xff, 0x64, 0x8b, 0xa1, 0xff, 0x81, 0xaa, 0xbd, 0xff, 0x8c, 0xb1, 0xc4, 0xff, 0x6d, 0x91, 0xa4, 0xff, 0x33, 0x48, 0x55, 0xff, 0x00, 0x00, 0x07, 0xff, 0x28, 0x3f, 0x50, 0xff, 0x25, 0x49, 0x5c, 0xff, 0x3a, 0x63, 0x73, 0xff, 0x63, 0x8d, 0x9b, 0xff, 0x71, 0x98, 0xa0, 0xff, 0x8c, 0xa6, 0xa9, 0xff, 0xb5, 0xbb, 0xbd, 0xff, 0xc5, 0xc5, 0xc5, 0xff, 0xc7, 0xcb, 0xc9, 0xff, 0xca, 0xc8, 0xc8, 0xff, 0xc9, 0xc5, 0xc6, 0xff, 0xc3, 0xc4, 0xc4, 0xff, 0xc4, 0xc4, 0xc4, 0xff, 0xc8, 0xc6, 0xc6, 0xff, 0xc7, 0xc5, 0xc5, 0xff, 0xc6, 0xc2, 0xc3, 0xff, 0xc5, 0xc3, 0xc3, 0xff, 0xc5, 0xc4, 0xc3, 0xff, 0xc6, 0xc4, 0xc3, 0xff, 0xc5, 0xc3, 0xc2, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc3, 0xc1, 0xc0, 0xf2, 0xff, 0xff, 0xff, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x37, 0x55, 0x69, 0xc6, 0x42, 0x60, 0x73, 0xff, 0x34, 0x52, 0x63, 0xff, 0x28, 0x43, 0x52, 0xff, 0x37, 0x50, 0x62, 0xff, 0x2b, 0x43, 0x56, 0xff, 0x05, 0x18, 0x28, 0xff, 0xb9, 0xc2, 0xc9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdc, 0xdf, 0xe2, 0xff, 0xd5, 0xd9, 0xdc, 0xff, 0xd4, 0xd8, 0xd9, 0xff, 0xd3, 0xd6, 0xd9, 0xff, 0xc3, 0xc9, 0xcf, 0xff, 0xb1, 0xba, 0xc2, 0xff, 0xac, 0xb3, 0xc0, 0xff, 0xa0, 0xaa, 0xba, 0xff, 0x39, 0x4b, 0x5c, 0xff, 0x20, 0x34, 0x46, 0xff, 0x27, 0x3b, 0x4c, 0xff, 0x19, 0x2b, 0x3c, 0xff, 0x23, 0x31, 0x40, 0xff, 0xb7, 0xbc, 0xc4, 0xff, 0xf5, 0xf2, 0xf3, 0xff, 0xce, 0xcd, 0xd3, 0xff, 0xd0, 0xd2, 0xda, 0xff, 0xcd, 0xce, 0xce, 0xff, 0xc8, 0xca, 0xcd, 0xff, 0xc9, 0xce, 0xd9, 0xff, 0xa9, 0xb0, 0xc4, 0xff, 0x4d, 0x57, 0x71, 0xff, 0x5d, 0x68, 0x85, 0xff, 0x8e, 0x99, 0xb3, 0xff, 0x9b, 0xa9, 0xbd, 0xff, 0xa6, 0xb2, 0xc4, 0xff, 0xb2, 0xb9, 0xca, 0xff, 0xb6, 0xbb, 0xca, 0xff, 0xb3, 0xba, 0xc6, 0xff, 0xb4, 0xbd, 0xc8, 0xff, 0xb7, 0xc0, 0xcb, 0xff, 0xb2, 0xbb, 0xc6, 0xff, 0xaf, 0xb6, 0xc1, 0xff, 0xb2, 0xb8, 0xc3, 0xff, 0xb6, 0xbc, 0xc6, 0xff, 0xb5, 0xbb, 0xc3, 0xff, 0xb7, 0xbb, 0xc6, 0xff, 0xba, 0xbe, 0xc9, 0xff, 0xbf, 0xc4, 0xcd, 0xff, 0xbb, 0xc0, 0xc8, 0xff, 0xad, 0xb0, 0xbc, 0xff, 0x9f, 0xa1, 0xb1, 0xff, 0xa5, 0xa9, 0xb5, 0xff, 0xaf, 0xb4, 0xbe, 0xff, 0xb0, 0xb5, 0xc1, 0xff, 0xa4, 0xaa, 0xb7, 0xff, 0x9b, 0xa2, 0xb2, 0xff, 0x9b, 0xa1, 0xb4, 0xff, 0x98, 0xa1, 0xb0, 0xff, 0x98, 0xa1, 0xaf, 0xff, 0x98, 0xa1, 0xaf, 0xff, 0x9c, 0xa5, 0xb3, 0xff, 0xa0, 0xa9, 0xb7, 0xff, 0xa1, 0xab, 0xb9, 0xff, 0x9d, 0xa4, 0xb3, 0xff, 0x99, 0x9f, 0xaf, 0xff, 0x97, 0xa0, 0xb1, 0xff, 0x8e, 0x9a, 0xac, 0xff, 0x8a, 0x98, 0xab, 0xff, 0x84, 0x93, 0xa6, 0xff, 0x79, 0x87, 0x9f, 0xff, 0x75, 0x83, 0x9b, 0xff, 0x73, 0x82, 0x97, 0xff, 0x6a, 0x77, 0x8d, 0xff, 0x5e, 0x6b, 0x80, 0xff, 0x52, 0x60, 0x73, 0xff, 0x42, 0x4f, 0x62, 0xff, 0x2f, 0x3b, 0x4e, 0xff, 0x12, 0x1c, 0x2d, 0xff, 0x04, 0x0e, 0x19, 0xff, 0x2c, 0x33, 0x3c, 0xff, 0x5f, 0x63, 0x6a, 0xff, 0x65, 0x6c, 0x72, 0xff, 0x3c, 0x44, 0x4b, 0xff, 0x07, 0x0b, 0x0f, 0xff, 0x4e, 0x58, 0x62, 0xff, 0x8a, 0x9b, 0xac, 0xff, 0x5e, 0x75, 0x8c, 0xff, 0x58, 0x70, 0x87, 0xff, 0x6f, 0x88, 0x9b, 0xff, 0x62, 0x7d, 0x92, 0xff, 0x44, 0x63, 0x79, 0xff, 0x41, 0x60, 0x79, 0xff, 0x67, 0x87, 0xa0, 0xff, 0x7a, 0x9d, 0xb5, 0xff, 0x74, 0x9a, 0xb2, 0xff, 0x64, 0x8b, 0xa0, 0xff, 0x76, 0x9f, 0xb2, 0xff, 0x92, 0xb7, 0xca, 0xff, 0x71, 0x93, 0xa6, 0xff, 0x2d, 0x46, 0x55, 0xff, 0x00, 0x04, 0x13, 0xff, 0x24, 0x3c, 0x4c, 0xff, 0x26, 0x49, 0x5a, 0xff, 0x3c, 0x63, 0x74, 0xff, 0x59, 0x86, 0x95, 0xff, 0x73, 0x9b, 0xa5, 0xff, 0x95, 0xab, 0xaf, 0xff, 0xb4, 0xb9, 0xbc, 0xff, 0xc4, 0xc1, 0xc2, 0xff, 0xc8, 0xcb, 0xca, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xca, 0xc7, 0xc8, 0xff, 0xc2, 0xc5, 0xc5, 0xff, 0xc4, 0xc5, 0xc5, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc5, 0xc5, 0xc5, 0xff, 0xc6, 0xc4, 0xc4, 0xff, 0xc6, 0xc4, 0xc4, 0xff, 0xc7, 0xc5, 0xc4, 0xff, 0xc6, 0xc4, 0xc3, 0xff, 0xc5, 0xc3, 0xc2, 0xff, 0xc6, 0xc4, 0xc3, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xc4, 0xc1, 0xc0, 0xc4, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x3c, 0x58, 0x6d, 0x90, 0x3f, 0x5e, 0x70, 0xff, 0x38, 0x56, 0x67, 0xff, 0x1c, 0x36, 0x46, 0xff, 0x30, 0x4a, 0x5a, 0xff, 0x17, 0x2e, 0x42, 0xff, 0x3c, 0x4c, 0x5c, 0xff, 0xf8, 0xfe, 0xff, 0xff, 0xf8, 0xf4, 0xf1, 0xff, 0xdf, 0xe0, 0xe2, 0xff, 0xd4, 0xd6, 0xdd, 0xff, 0xd5, 0xd6, 0xdb, 0xff, 0xd0, 0xd3, 0xd9, 0xff, 0xc0, 0xc6, 0xd0, 0xff, 0xa2, 0xab, 0xba, 0xff, 0xa4, 0xaa, 0xba, 0xff, 0x75, 0x7f, 0x91, 0xff, 0x26, 0x39, 0x4b, 0xff, 0x28, 0x3f, 0x51, 0xff, 0x1d, 0x34, 0x44, 0xff, 0x08, 0x1b, 0x2c, 0xff, 0x89, 0x93, 0xa0, 0xff, 0xf2, 0xf1, 0xf2, 0xff, 0xd9, 0xd1, 0xcf, 0xff, 0xd4, 0xd1, 0xd4, 0xff, 0xcb, 0xcf, 0xd5, 0xff, 0xcf, 0xd0, 0xd1, 0xff, 0xd3, 0xd0, 0xd5, 0xff, 0xc9, 0xc9, 0xd2, 0xff, 0xc6, 0xcc, 0xd6, 0xff, 0x8b, 0x95, 0xa4, 0xff, 0x35, 0x3e, 0x58, 0xff, 0x67, 0x6d, 0x92, 0xff, 0x90, 0x9c, 0xb4, 0xff, 0x9b, 0xaa, 0xb9, 0xff, 0xab, 0xb3, 0xc4, 0xff, 0xb5, 0xb8, 0xc8, 0xff, 0xb5, 0xbb, 0xc7, 0xff, 0xb8, 0xc0, 0xca, 0xff, 0xb5, 0xbf, 0xc8, 0xff, 0xb2, 0xb9, 0xc3, 0xff, 0xb1, 0xb7, 0xc3, 0xff, 0xb2, 0xb8, 0xc2, 0xff, 0xb6, 0xbc, 0xc4, 0xff, 0xb5, 0xba, 0xc3, 0xff, 0xb2, 0xb5, 0xc3, 0xff, 0xba, 0xbd, 0xcb, 0xff, 0xbc, 0xc1, 0xcb, 0xff, 0xb5, 0xb8, 0xc3, 0xff, 0xab, 0xaf, 0xbc, 0xff, 0x9c, 0x9e, 0xb1, 0xff, 0xa0, 0xa3, 0xb1, 0xff, 0xb7, 0xba, 0xc5, 0xff, 0xb8, 0xbc, 0xc9, 0xff, 0xa9, 0xae, 0xbd, 0xff, 0xa2, 0xa5, 0xb8, 0xff, 0x9c, 0xa1, 0xb5, 0xff, 0x99, 0xa2, 0xb2, 0xff, 0x9a, 0xa4, 0xb1, 0xff, 0x9a, 0xa3, 0xb1, 0xff, 0x99, 0xa2, 0xb0, 0xff, 0x98, 0xa1, 0xaf, 0xff, 0x9d, 0xa6, 0xb4, 0xff, 0xa1, 0xa7, 0xb5, 0xff, 0x9c, 0xa0, 0xb0, 0xff, 0x95, 0x9c, 0xad, 0xff, 0x87, 0x92, 0xa4, 0xff, 0x80, 0x8f, 0xa2, 0xff, 0x77, 0x87, 0x9b, 0xff, 0x71, 0x80, 0x98, 0xff, 0x6e, 0x7e, 0x95, 0xff, 0x6b, 0x78, 0x8f, 0xff, 0x66, 0x73, 0x89, 0xff, 0x5a, 0x68, 0x7b, 0xff, 0x3e, 0x4b, 0x5e, 0xff, 0x1b, 0x27, 0x3a, 0xff, 0x15, 0x1f, 0x32, 0xff, 0x39, 0x42, 0x53, 0xff, 0x7a, 0x82, 0x8f, 0xff, 0xb7, 0xbe, 0xc6, 0xff, 0xce, 0xd2, 0xd6, 0xff, 0xc5, 0xcc, 0xd5, 0xff, 0xa9, 0xb4, 0xc1, 0xff, 0x68, 0x73, 0x7e, 0xff, 0x4a, 0x58, 0x66, 0xff, 0x74, 0x87, 0x97, 0xff, 0x5e, 0x76, 0x8b, 0xff, 0x51, 0x6b, 0x80, 0xff, 0x6f, 0x88, 0x9c, 0xff, 0x68, 0x83, 0x98, 0xff, 0x4d, 0x6c, 0x82, 0xff, 0x3e, 0x5d, 0x76, 0xff, 0x65, 0x84, 0x9d, 0xff, 0x7d, 0x9f, 0xb7, 0xff, 0x7b, 0xa1, 0xb9, 0xff, 0x67, 0x8f, 0xa4, 0xff, 0x78, 0xa1, 0xb3, 0xff, 0x97, 0xbb, 0xce, 0xff, 0x6d, 0x8e, 0xa0, 0xff, 0x29, 0x45, 0x55, 0xff, 0x02, 0x16, 0x26, 0xff, 0x19, 0x35, 0x45, 0xff, 0x29, 0x4b, 0x5c, 0xff, 0x32, 0x5a, 0x6c, 0xff, 0x48, 0x79, 0x8a, 0xff, 0x6c, 0x96, 0xa0, 0xff, 0xa7, 0xb9, 0xbd, 0xff, 0xbe, 0xc1, 0xc5, 0xff, 0xc3, 0xc0, 0xc2, 0xff, 0xc7, 0xca, 0xc8, 0xff, 0xcb, 0xca, 0xc9, 0xff, 0xca, 0xc8, 0xc9, 0xff, 0xc2, 0xc5, 0xc5, 0xff, 0xc4, 0xc6, 0xc6, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc5, 0xc5, 0xc5, 0xff, 0xc5, 0xc5, 0xc5, 0xff, 0xc9, 0xc6, 0xc6, 0xff, 0xc8, 0xc6, 0xc5, 0xff, 0xc7, 0xc5, 0xc4, 0xff, 0xc9, 0xc7, 0xc5, 0xff, 0xc8, 0xc6, 0xc5, 0xff, 0xc6, 0xc3, 0xc2, 0xff, 0xc5, 0xc3, 0xc2, 0xff, 0xc5, 0xc3, 0xc2, 0xff, 0xc4, 0xc2, 0xc2, 0x90, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x34, 0x51, 0x5f, 0x58, 0x3c, 0x58, 0x69, 0xff, 0x37, 0x53, 0x64, 0xff, 0x1e, 0x33, 0x40, 0xff, 0x28, 0x40, 0x50, 0xff, 0x05, 0x1e, 0x32, 0xff, 0x61, 0x73, 0x82, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xf2, 0xec, 0xeb, 0xff, 0xe3, 0xe4, 0xe4, 0xff, 0xd9, 0xdd, 0xe1, 0xff, 0xd6, 0xd9, 0xdb, 0xff, 0xd0, 0xd4, 0xd9, 0xff, 0xc4, 0xc8, 0xd0, 0xff, 0xaa, 0xae, 0xbb, 0xff, 0x8f, 0x95, 0xa2, 0xff, 0x3b, 0x4c, 0x5c, 0xff, 0x23, 0x3f, 0x51, 0xff, 0x2e, 0x45, 0x58, 0xff, 0x09, 0x15, 0x26, 0xff, 0x4e, 0x56, 0x63, 0xff, 0xe2, 0xe8, 0xed, 0xff, 0xd5, 0xd7, 0xd6, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd4, 0xd1, 0xd3, 0xff, 0xd0, 0xcf, 0xd2, 0xff, 0xd3, 0xd2, 0xd3, 0xff, 0xd3, 0xcf, 0xd7, 0xff, 0xc9, 0xc7, 0xd0, 0xff, 0xbd, 0xc2, 0xc6, 0xff, 0xbf, 0xc6, 0xd0, 0xff, 0x74, 0x7b, 0x8f, 0xff, 0x36, 0x3b, 0x59, 0xff, 0x77, 0x81, 0x96, 0xff, 0x93, 0x9f, 0xb0, 0xff, 0xa1, 0xaa, 0xba, 0xff, 0xac, 0xb3, 0xc1, 0xff, 0xb1, 0xb7, 0xc1, 0xff, 0xb6, 0xbc, 0xc4, 0xff, 0xb2, 0xba, 0xc6, 0xff, 0xae, 0xb5, 0xc2, 0xff, 0xaf, 0xb7, 0xc1, 0xff, 0xb2, 0xb8, 0xc0, 0xff, 0xb8, 0xbb, 0xc3, 0xff, 0xbd, 0xbf, 0xc7, 0xff, 0xb8, 0xbe, 0xc4, 0xff, 0xb9, 0xc2, 0xc8, 0xff, 0xbc, 0xc3, 0xca, 0xff, 0xb4, 0xbc, 0xc3, 0xff, 0xac, 0xb2, 0xbc, 0xff, 0x9b, 0xa1, 0xad, 0xff, 0x9d, 0x9f, 0xa9, 0xff, 0xbc, 0xbc, 0xc6, 0xff, 0xba, 0xbc, 0xcb, 0xff, 0xaa, 0xae, 0xc1, 0xff, 0xa9, 0xac, 0xbd, 0xff, 0xa4, 0xa8, 0xb6, 0xff, 0x9f, 0xa4, 0xb4, 0xff, 0x9b, 0xa2, 0xb2, 0xff, 0x98, 0xa0, 0xb0, 0xff, 0x96, 0x9e, 0xae, 0xff, 0x94, 0x9e, 0xac, 0xff, 0x94, 0x9e, 0xab, 0xff, 0x95, 0x9d, 0xad, 0xff, 0x94, 0x9c, 0xae, 0xff, 0x8e, 0x96, 0xa8, 0xff, 0x83, 0x8d, 0xa0, 0xff, 0x76, 0x84, 0x95, 0xff, 0x70, 0x7b, 0x8f, 0xff, 0x6a, 0x78, 0x91, 0xff, 0x5f, 0x71, 0x8a, 0xff, 0x5b, 0x6e, 0x83, 0xff, 0x55, 0x68, 0x7a, 0xff, 0x37, 0x44, 0x55, 0xff, 0x2c, 0x33, 0x44, 0xff, 0x56, 0x5c, 0x68, 0xff, 0x91, 0x99, 0x9e, 0xff, 0xc5, 0xcd, 0xd1, 0xff, 0xd8, 0xdf, 0xe2, 0xff, 0xc8, 0xce, 0xd2, 0xff, 0xb9, 0xbe, 0xc2, 0xff, 0xb4, 0xb8, 0xc1, 0xff, 0xa1, 0xa8, 0xb6, 0xff, 0x88, 0x91, 0xa2, 0xff, 0x57, 0x65, 0x79, 0xff, 0x4d, 0x62, 0x76, 0xff, 0x53, 0x6c, 0x82, 0xff, 0x4a, 0x64, 0x78, 0xff, 0x6d, 0x86, 0x9a, 0xff, 0x67, 0x7f, 0x94, 0xff, 0x55, 0x70, 0x86, 0xff, 0x3d, 0x5c, 0x74, 0xff, 0x59, 0x7d, 0x95, 0xff, 0x78, 0x9e, 0xb6, 0xff, 0x7e, 0xa6, 0xbe, 0xff, 0x6d, 0x96, 0xac, 0xff, 0x7a, 0xa3, 0xb8, 0xff, 0x93, 0xb7, 0xcc, 0xff, 0x77, 0x95, 0xac, 0xff, 0x47, 0x68, 0x7a, 0xff, 0x15, 0x2b, 0x39, 0xff, 0x0f, 0x20, 0x33, 0xff, 0x27, 0x4c, 0x60, 0xff, 0x24, 0x51, 0x64, 0xff, 0x43, 0x72, 0x84, 0xff, 0x5c, 0x83, 0x8f, 0xff, 0xa3, 0xb8, 0xbe, 0xff, 0xc0, 0xc3, 0xc5, 0xff, 0xc8, 0xc4, 0xc3, 0xff, 0xc6, 0xc7, 0xc5, 0xff, 0xc6, 0xc7, 0xc8, 0xff, 0xc7, 0xc6, 0xc8, 0xff, 0xc5, 0xc6, 0xc6, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc4, 0xc4, 0xc4, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc8, 0xc8, 0xc7, 0xff, 0xc7, 0xc6, 0xc6, 0xff, 0xc6, 0xc6, 0xc5, 0xff, 0xc7, 0xc7, 0xc6, 0xff, 0xc7, 0xc6, 0xc6, 0xff, 0xc5, 0xc5, 0xc5, 0xff, 0xc6, 0xc4, 0xc3, 0xff, 0xc7, 0xc3, 0xc2, 0xff, 0xc5, 0xc2, 0xc2, 0x58, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x38, 0x55, 0x5e, 0x1b, 0x42, 0x5b, 0x6c, 0xff, 0x30, 0x48, 0x5a, 0xff, 0x16, 0x27, 0x34, 0xff, 0x24, 0x3a, 0x49, 0xff, 0x05, 0x20, 0x34, 0xff, 0x64, 0x7a, 0x89, 0xff, 0xf3, 0xfb, 0xff, 0xff, 0xee, 0xe9, 0xe9, 0xff, 0xe2, 0xe3, 0xe3, 0xff, 0xd9, 0xdf, 0xdf, 0xff, 0xd4, 0xd8, 0xd9, 0xff, 0xcc, 0xd1, 0xd2, 0xff, 0xc4, 0xc7, 0xcd, 0xff, 0xb8, 0xb8, 0xc2, 0xff, 0xac, 0xaf, 0xbb, 0xff, 0x49, 0x5d, 0x6c, 0xff, 0x1f, 0x3e, 0x4e, 0xff, 0x1f, 0x34, 0x46, 0xff, 0x24, 0x28, 0x37, 0xff, 0xc0, 0xc1, 0xca, 0xff, 0xe1, 0xe6, 0xe7, 0xff, 0xc9, 0xce, 0xce, 0xff, 0xd1, 0xd3, 0xd4, 0xff, 0xd7, 0xd4, 0xd6, 0xff, 0xd7, 0xd2, 0xd4, 0xff, 0xd1, 0xd1, 0xd2, 0xff, 0xcd, 0xce, 0xd4, 0xff, 0xc4, 0xc6, 0xcc, 0xff, 0xbb, 0xc0, 0xc4, 0xff, 0xb4, 0xbb, 0xc3, 0xff, 0xb4, 0xba, 0xc9, 0xff, 0x58, 0x5f, 0x73, 0xff, 0x40, 0x4b, 0x5d, 0xff, 0x89, 0x95, 0xa7, 0xff, 0x94, 0x9f, 0xaf, 0xff, 0x9d, 0xa9, 0xb8, 0xff, 0xa4, 0xaf, 0xbc, 0xff, 0xa8, 0xb1, 0xbd, 0xff, 0xa8, 0xb1, 0xc1, 0xff, 0xa7, 0xb0, 0xc0, 0xff, 0xa7, 0xaf, 0xba, 0xff, 0xaf, 0xb6, 0xbe, 0xff, 0xb8, 0xbc, 0xc5, 0xff, 0xbd, 0xbe, 0xc9, 0xff, 0xc1, 0xc4, 0xce, 0xff, 0xc2, 0xc6, 0xce, 0xff, 0xc1, 0xc4, 0xd0, 0xff, 0xb9, 0xbb, 0xca, 0xff, 0xb1, 0xb2, 0xc2, 0xff, 0xaa, 0xac, 0xbd, 0xff, 0xb3, 0xb3, 0xc3, 0xff, 0xbc, 0xbc, 0xce, 0xff, 0xb0, 0xb3, 0xc7, 0xff, 0xa5, 0xa9, 0xc1, 0xff, 0x9c, 0xa1, 0xb8, 0xff, 0xa1, 0xa5, 0xb8, 0xff, 0xa2, 0xa6, 0xb9, 0xff, 0x9f, 0xa5, 0xb9, 0xff, 0x96, 0x9d, 0xb0, 0xff, 0x8a, 0x93, 0xa4, 0xff, 0x87, 0x92, 0xa1, 0xff, 0x7f, 0x8b, 0x9b, 0xff, 0x7b, 0x86, 0x98, 0xff, 0x7f, 0x8a, 0x9d, 0xff, 0x81, 0x8e, 0xa0, 0xff, 0x76, 0x82, 0x94, 0xff, 0x67, 0x73, 0x85, 0xff, 0x62, 0x6d, 0x7f, 0xff, 0x5c, 0x68, 0x7d, 0xff, 0x52, 0x60, 0x77, 0xff, 0x44, 0x53, 0x67, 0xff, 0x36, 0x47, 0x57, 0xff, 0x55, 0x61, 0x6f, 0xff, 0x96, 0x9c, 0xaa, 0xff, 0xc9, 0xcd, 0xd5, 0xff, 0xd4, 0xda, 0xdc, 0xff, 0xc7, 0xce, 0xce, 0xff, 0xc4, 0xcd, 0xca, 0xff, 0xc7, 0xcd, 0xcf, 0xff, 0xc3, 0xc8, 0xcc, 0xff, 0xb8, 0xbc, 0xc2, 0xff, 0xa2, 0xa7, 0xb2, 0xff, 0x82, 0x8b, 0x9c, 0xff, 0x65, 0x72, 0x88, 0xff, 0x3c, 0x50, 0x68, 0xff, 0x3d, 0x54, 0x6b, 0xff, 0x3f, 0x58, 0x6c, 0xff, 0x66, 0x7f, 0x93, 0xff, 0x6d, 0x84, 0x9a, 0xff, 0x54, 0x6d, 0x85, 0xff, 0x3d, 0x5c, 0x74, 0xff, 0x50, 0x76, 0x8e, 0xff, 0x79, 0x9f, 0xb8, 0xff, 0x84, 0xae, 0xc4, 0xff, 0x71, 0x9b, 0xb2, 0xff, 0x72, 0x9b, 0xb2, 0xff, 0x91, 0xb6, 0xcc, 0xff, 0x7b, 0x99, 0xb3, 0xff, 0x51, 0x75, 0x88, 0xff, 0x2b, 0x40, 0x4e, 0xff, 0x0a, 0x15, 0x2a, 0xff, 0x29, 0x4f, 0x63, 0xff, 0x21, 0x50, 0x62, 0xff, 0x4c, 0x7a, 0x8b, 0xff, 0x50, 0x79, 0x85, 0xff, 0x91, 0xac, 0xb2, 0xff, 0xba, 0xbf, 0xc1, 0xff, 0xc8, 0xc6, 0xc4, 0xff, 0xc6, 0xc9, 0xc6, 0xff, 0xca, 0xca, 0xca, 0xff, 0xc8, 0xc6, 0xc8, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc7, 0xc7, 0xff, 0xc6, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc5, 0xc6, 0xc6, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc5, 0xc6, 0xc6, 0xff, 0xc6, 0xc5, 0xc5, 0xff, 0xc8, 0xc4, 0xc2, 0xff, 0xc6, 0xc6, 0xc6, 0x1b, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x49, 0x62, 0x73, 0xd7, 0x26, 0x3d, 0x50, 0xff, 0x17, 0x2a, 0x37, 0xff, 0x2b, 0x40, 0x50, 0xff, 0x14, 0x2d, 0x41, 0xff, 0x50, 0x67, 0x77, 0xff, 0xe4, 0xec, 0xf5, 0xff, 0xf4, 0xf0, 0xef, 0xff, 0xe5, 0xe4, 0xe4, 0xff, 0xda, 0xde, 0xdf, 0xff, 0xd4, 0xd8, 0xd9, 0xff, 0xcd, 0xd1, 0xd3, 0xff, 0xc5, 0xc9, 0xcf, 0xff, 0xba, 0xbb, 0xc6, 0xff, 0xb6, 0xb8, 0xc5, 0xff, 0x73, 0x83, 0x91, 0xff, 0x1e, 0x38, 0x48, 0xff, 0x07, 0x18, 0x2a, 0xff, 0x7f, 0x83, 0x91, 0xff, 0xef, 0xf1, 0xf9, 0xff, 0xca, 0xcf, 0xd1, 0xff, 0xd0, 0xd3, 0xd3, 0xff, 0xd4, 0xd4, 0xd6, 0xff, 0xd8, 0xd5, 0xd7, 0xff, 0xd4, 0xd3, 0xd5, 0xff, 0xd0, 0xd2, 0xd2, 0xff, 0xcc, 0xd0, 0xd2, 0xff, 0xbf, 0xc3, 0xc8, 0xff, 0xb7, 0xbc, 0xc2, 0xff, 0xac, 0xb3, 0xba, 0xff, 0xab, 0xb4, 0xc0, 0xff, 0x9c, 0xa4, 0xb5, 0xff, 0x3f, 0x49, 0x5b, 0xff, 0x5d, 0x69, 0x7c, 0xff, 0x88, 0x95, 0xa8, 0xff, 0x8d, 0x9c, 0xae, 0xff, 0x94, 0xa3, 0xb7, 0xff, 0x91, 0xa2, 0xb6, 0xff, 0x93, 0x9f, 0xb3, 0xff, 0x97, 0xa1, 0xb2, 0xff, 0x9d, 0xa7, 0xb5, 0xff, 0xa8, 0xb1, 0xbe, 0xff, 0xb2, 0xba, 0xc8, 0xff, 0xb9, 0xc0, 0xcf, 0xff, 0xbf, 0xc0, 0xd6, 0xff, 0xb5, 0xb2, 0xcc, 0xff, 0xa6, 0xa4, 0xbd, 0xff, 0x91, 0x8f, 0xaa, 0xff, 0x88, 0x85, 0xa2, 0xff, 0x9d, 0x99, 0xb8, 0xff, 0xae, 0xaf, 0xcb, 0xff, 0x8c, 0x93, 0xaf, 0xff, 0x6d, 0x74, 0x93, 0xff, 0x61, 0x6a, 0x8b, 0xff, 0x60, 0x69, 0x88, 0xff, 0x69, 0x70, 0x8e, 0xff, 0x7a, 0x80, 0x99, 0xff, 0x88, 0x8e, 0xa3, 0xff, 0x85, 0x8d, 0xa3, 0xff, 0x82, 0x8c, 0xa0, 0xff, 0x7a, 0x84, 0x97, 0xff, 0x68, 0x72, 0x85, 0xff, 0x5f, 0x6c, 0x7e, 0xff, 0x6b, 0x78, 0x8a, 0xff, 0x6f, 0x7b, 0x8d, 0xff, 0x5f, 0x6b, 0x7d, 0xff, 0x53, 0x5e, 0x70, 0xff, 0x6c, 0x7a, 0x8b, 0xff, 0x97, 0x9e, 0xae, 0xff, 0xa1, 0xa5, 0xb3, 0xff, 0x7f, 0x86, 0x94, 0xff, 0x6e, 0x78, 0x83, 0xff, 0xaa, 0xb4, 0xbe, 0xff, 0xc0, 0xca, 0xd3, 0xff, 0xb5, 0xbd, 0xc7, 0xff, 0xbc, 0xc2, 0xcb, 0xff, 0xbe, 0xc5, 0xca, 0xff, 0xc7, 0xce, 0xd0, 0xff, 0xc9, 0xcf, 0xd0, 0xff, 0xc9, 0xcd, 0xd1, 0xff, 0xbb, 0xc0, 0xc5, 0xff, 0xa4, 0xaa, 0xb3, 0xff, 0x8f, 0x97, 0xa6, 0xff, 0x6d, 0x7a, 0x8f, 0xff, 0x45, 0x56, 0x6e, 0xff, 0x38, 0x50, 0x66, 0xff, 0x35, 0x4e, 0x62, 0xff, 0x56, 0x6f, 0x83, 0xff, 0x70, 0x88, 0x9e, 0xff, 0x54, 0x6d, 0x87, 0xff, 0x42, 0x61, 0x7a, 0xff, 0x4a, 0x6d, 0x87, 0xff, 0x80, 0xa6, 0xbf, 0xff, 0x8a, 0xb2, 0xc9, 0xff, 0x70, 0x99, 0xb0, 0xff, 0x6f, 0x98, 0xaf, 0xff, 0x93, 0xb8, 0xce, 0xff, 0x70, 0x94, 0xab, 0xff, 0x42, 0x63, 0x76, 0xff, 0x41, 0x5d, 0x6c, 0xff, 0x11, 0x29, 0x3d, 0xff, 0x2c, 0x4f, 0x64, 0xff, 0x2c, 0x5c, 0x6e, 0xff, 0x48, 0x76, 0x89, 0xff, 0x3e, 0x6a, 0x79, 0xff, 0x76, 0x97, 0x9f, 0xff, 0xbc, 0xc5, 0xc9, 0xff, 0xbd, 0xbf, 0xbd, 0xff, 0xc5, 0xc8, 0xc7, 0xff, 0xca, 0xc9, 0xca, 0xff, 0xca, 0xc8, 0xc9, 0xff, 0xc6, 0xc7, 0xc6, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc8, 0xc8, 0xc8, 0xff, 0xc8, 0xc8, 0xc8, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc7, 0xc6, 0xc5, 0xff, 0xc8, 0xc3, 0xc2, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x5d, 0x6d, 0x8e, 0x14, 0x2a, 0x3d, 0xff, 0x14, 0x23, 0x30, 0xff, 0x29, 0x3e, 0x4d, 0xff, 0x19, 0x32, 0x46, 0xff, 0x3d, 0x53, 0x63, 0xff, 0xcb, 0xd4, 0xdb, 0xff, 0xfc, 0xfa, 0xf9, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xde, 0xe0, 0xe1, 0xff, 0xd7, 0xdb, 0xdc, 0xff, 0xd0, 0xd5, 0xd8, 0xff, 0xc9, 0xcb, 0xd2, 0xff, 0xb9, 0xba, 0xc5, 0xff, 0xaf, 0xb1, 0xbe, 0xff, 0x87, 0x95, 0xa4, 0xff, 0x1a, 0x2f, 0x3f, 0xff, 0x2e, 0x3a, 0x4a, 0xff, 0xda, 0xdc, 0xe6, 0xff, 0xdb, 0xde, 0xe1, 0xff, 0xd0, 0xd4, 0xd4, 0xff, 0xd4, 0xd5, 0xd6, 0xff, 0xd5, 0xd3, 0xd5, 0xff, 0xd5, 0xd3, 0xd5, 0xff, 0xd2, 0xd3, 0xd4, 0xff, 0xcf, 0xd3, 0xd3, 0xff, 0xc6, 0xcc, 0xce, 0xff, 0xb4, 0xbb, 0xc0, 0xff, 0xb1, 0xb7, 0xbf, 0xff, 0x9f, 0xa6, 0xaf, 0xff, 0x9b, 0xa4, 0xb0, 0xff, 0xa4, 0xad, 0xb9, 0xff, 0x6b, 0x75, 0x85, 0xff, 0x3f, 0x49, 0x5d, 0xff, 0x79, 0x84, 0x98, 0xff, 0x7b, 0x89, 0x9d, 0xff, 0x7b, 0x8a, 0xa0, 0xff, 0x6f, 0x80, 0x98, 0xff, 0x74, 0x80, 0x94, 0xff, 0x70, 0x7a, 0x89, 0xff, 0x72, 0x7d, 0x8b, 0xff, 0x98, 0xa1, 0xb1, 0xff, 0xab, 0xb2, 0xc6, 0xff, 0x9d, 0xa3, 0xb8, 0xff, 0x85, 0x8d, 0xa8, 0xff, 0x69, 0x73, 0x92, 0xff, 0x5e, 0x66, 0x86, 0xff, 0x55, 0x5d, 0x7e, 0xff, 0x56, 0x5e, 0x81, 0xff, 0x5f, 0x66, 0x8c, 0xff, 0x65, 0x6b, 0x8e, 0xff, 0x55, 0x5f, 0x7f, 0xff, 0x4a, 0x55, 0x79, 0xff, 0x42, 0x4c, 0x72, 0xff, 0x3c, 0x45, 0x68, 0xff, 0x34, 0x3d, 0x5d, 0xff, 0x3b, 0x41, 0x5e, 0xff, 0x43, 0x48, 0x63, 0xff, 0x48, 0x4f, 0x67, 0xff, 0x52, 0x59, 0x70, 0xff, 0x4b, 0x53, 0x68, 0xff, 0x2e, 0x35, 0x48, 0xff, 0x1e, 0x28, 0x39, 0xff, 0x33, 0x3f, 0x4d, 0xff, 0x48, 0x51, 0x60, 0xff, 0x60, 0x69, 0x77, 0xff, 0x8d, 0x96, 0xa3, 0xff, 0xbd, 0xc8, 0xd4, 0xff, 0xd4, 0xd6, 0xe0, 0xff, 0xcd, 0xc8, 0xd1, 0xff, 0xbd, 0xbe, 0xc6, 0xff, 0xa3, 0xa6, 0xaf, 0xff, 0x95, 0x9c, 0xa5, 0xff, 0x97, 0xa2, 0xa8, 0xff, 0xa0, 0xa8, 0xb5, 0xff, 0xa3, 0xa9, 0xb7, 0xff, 0xae, 0xb5, 0xbd, 0xff, 0xc7, 0xcc, 0xd0, 0xff, 0xcb, 0xd0, 0xd3, 0xff, 0xcb, 0xce, 0xd2, 0xff, 0xbe, 0xc1, 0xc5, 0xff, 0xaa, 0xaf, 0xb6, 0xff, 0x96, 0x9e, 0xab, 0xff, 0x76, 0x82, 0x96, 0xff, 0x52, 0x63, 0x7b, 0xff, 0x33, 0x4a, 0x61, 0xff, 0x2d, 0x45, 0x5c, 0xff, 0x46, 0x5e, 0x74, 0xff, 0x6d, 0x84, 0x9b, 0xff, 0x51, 0x69, 0x83, 0xff, 0x42, 0x5f, 0x7a, 0xff, 0x48, 0x6c, 0x86, 0xff, 0x7e, 0xa4, 0xbd, 0xff, 0x8e, 0xb3, 0xcc, 0xff, 0x74, 0x9d, 0xb4, 0xff, 0x73, 0x9e, 0xb4, 0xff, 0x96, 0xbe, 0xd3, 0xff, 0x70, 0x95, 0xac, 0xff, 0x35, 0x59, 0x6b, 0xff, 0x3b, 0x5a, 0x69, 0xff, 0x26, 0x40, 0x56, 0xff, 0x2c, 0x4f, 0x66, 0xff, 0x30, 0x61, 0x75, 0xff, 0x4a, 0x77, 0x8a, 0xff, 0x49, 0x79, 0x88, 0xff, 0x50, 0x7b, 0x85, 0xff, 0xb0, 0xbf, 0xc5, 0xff, 0xbd, 0xc2, 0xc2, 0xff, 0xc1, 0xc5, 0xc4, 0xff, 0xc7, 0xc6, 0xc7, 0xff, 0xca, 0xc8, 0xc9, 0xff, 0xc8, 0xc8, 0xc8, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc7, 0xc7, 0xff, 0xc7, 0xc6, 0xc6, 0xff, 0xc9, 0xc5, 0xc3, 0x8e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x45, 0x5e, 0x6d, 0x46, 0x0c, 0x20, 0x32, 0xff, 0x0c, 0x1a, 0x27, 0xff, 0x20, 0x34, 0x44, 0xff, 0x24, 0x3e, 0x51, 0xff, 0x27, 0x3e, 0x4e, 0xff, 0xa8, 0xb2, 0xb9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0xe5, 0xe5, 0xff, 0xdf, 0xe0, 0xe2, 0xff, 0xd9, 0xdb, 0xdb, 0xff, 0xce, 0xd0, 0xd3, 0xff, 0xc4, 0xc6, 0xce, 0xff, 0xbd, 0xbd, 0xc8, 0xff, 0xb2, 0xb4, 0xc1, 0xff, 0x8f, 0x9b, 0xab, 0xff, 0x1a, 0x2c, 0x3c, 0xff, 0x82, 0x8a, 0x99, 0xff, 0xf6, 0xf6, 0xff, 0xff, 0xcf, 0xd4, 0xd2, 0xff, 0xd4, 0xd7, 0xd7, 0xff, 0xd6, 0xd5, 0xd7, 0xff, 0xd5, 0xd2, 0xd4, 0xff, 0xd6, 0xd4, 0xd6, 0xff, 0xd2, 0xd4, 0xd5, 0xff, 0xcc, 0xd1, 0xd1, 0xff, 0xbe, 0xc6, 0xca, 0xff, 0xaf, 0xb6, 0xbf, 0xff, 0xa8, 0xad, 0xba, 0xff, 0x89, 0x90, 0x9f, 0xff, 0x8e, 0x95, 0xa4, 0xff, 0x92, 0x9a, 0xa6, 0xff, 0x88, 0x90, 0xa0, 0xff, 0x3d, 0x44, 0x56, 0xff, 0x44, 0x4c, 0x5c, 0xff, 0x70, 0x7a, 0x8b, 0xff, 0x76, 0x81, 0x92, 0xff, 0x65, 0x6e, 0x80, 0xff, 0x3f, 0x49, 0x58, 0xff, 0x22, 0x2b, 0x39, 0xff, 0x23, 0x2c, 0x39, 0xff, 0x3d, 0x46, 0x56, 0xff, 0x53, 0x59, 0x6f, 0xff, 0x47, 0x4b, 0x64, 0xff, 0x37, 0x42, 0x5e, 0xff, 0x37, 0x47, 0x62, 0xff, 0x3d, 0x49, 0x67, 0xff, 0x3a, 0x47, 0x67, 0xff, 0x3f, 0x4c, 0x6c, 0xff, 0x3a, 0x46, 0x68, 0xff, 0x27, 0x2f, 0x51, 0xff, 0x2c, 0x34, 0x55, 0xff, 0x36, 0x40, 0x63, 0xff, 0x36, 0x40, 0x62, 0xff, 0x38, 0x3f, 0x60, 0xff, 0x38, 0x3e, 0x5b, 0xff, 0x3b, 0x40, 0x5c, 0xff, 0x39, 0x3c, 0x58, 0xff, 0x35, 0x38, 0x51, 0xff, 0x2b, 0x2f, 0x45, 0xff, 0x26, 0x2a, 0x3d, 0xff, 0x31, 0x33, 0x46, 0xff, 0x1c, 0x22, 0x31, 0xff, 0x2e, 0x38, 0x42, 0xff, 0x83, 0x8b, 0x94, 0xff, 0xc1, 0xc6, 0xce, 0xff, 0xdc, 0xe1, 0xe8, 0xff, 0xd5, 0xdb, 0xdf, 0xff, 0xcb, 0xcc, 0xd0, 0xff, 0xc4, 0xc0, 0xc5, 0xff, 0xc1, 0xbe, 0xc3, 0xff, 0xb5, 0xb4, 0xbb, 0xff, 0xa7, 0xaa, 0xb4, 0xff, 0x84, 0x8d, 0x94, 0xff, 0x70, 0x79, 0x87, 0xff, 0x7b, 0x81, 0x95, 0xff, 0x9d, 0xa3, 0xaf, 0xff, 0xc1, 0xc6, 0xcb, 0xff, 0xcd, 0xd0, 0xd4, 0xff, 0xce, 0xd0, 0xd3, 0xff, 0xc1, 0xc4, 0xc6, 0xff, 0xaf, 0xb5, 0xb9, 0xff, 0x97, 0x9e, 0xaa, 0xff, 0x7d, 0x89, 0x9b, 0xff, 0x5a, 0x6b, 0x82, 0xff, 0x33, 0x47, 0x61, 0xff, 0x2f, 0x46, 0x5e, 0xff, 0x43, 0x5b, 0x71, 0xff, 0x6c, 0x83, 0x9c, 0xff, 0x4e, 0x67, 0x80, 0xff, 0x38, 0x55, 0x70, 0xff, 0x4a, 0x6e, 0x8a, 0xff, 0x7c, 0xa2, 0xbc, 0xff, 0x90, 0xb6, 0xce, 0xff, 0x7b, 0xa5, 0xbc, 0xff, 0x6b, 0x96, 0xac, 0xff, 0x8a, 0xb3, 0xc9, 0xff, 0x77, 0x9f, 0xb6, 0xff, 0x34, 0x59, 0x6c, 0xff, 0x2b, 0x49, 0x5a, 0xff, 0x33, 0x4c, 0x62, 0xff, 0x36, 0x5b, 0x72, 0xff, 0x2e, 0x5e, 0x73, 0xff, 0x4c, 0x79, 0x8c, 0xff, 0x4a, 0x7d, 0x8f, 0xff, 0x40, 0x73, 0x7f, 0xff, 0x91, 0xa5, 0xad, 0xff, 0xc3, 0xcb, 0xcc, 0xff, 0xbb, 0xc0, 0xbe, 0xff, 0xca, 0xc8, 0xc9, 0xff, 0xca, 0xc6, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc8, 0xc8, 0xc8, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc7, 0xff, 0xc8, 0xc6, 0xc6, 0xff, 0xc8, 0xc4, 0xc4, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x48, 0x6d, 0x07, 0x0c, 0x20, 0x33, 0xef, 0x0d, 0x19, 0x26, 0xff, 0x18, 0x2c, 0x3c, 0xff, 0x20, 0x3a, 0x4e, 0xff, 0x0c, 0x24, 0x33, 0xff, 0x92, 0x9e, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6, 0xe4, 0xe4, 0xff, 0xdf, 0xde, 0xe0, 0xff, 0xd8, 0xdb, 0xdb, 0xff, 0xd3, 0xd4, 0xd7, 0xff, 0xc7, 0xc8, 0xd0, 0xff, 0xbb, 0xba, 0xc7, 0xff, 0xb4, 0xb3, 0xc1, 0xff, 0x80, 0x8a, 0x99, 0xff, 0x4f, 0x5f, 0x6e, 0xff, 0xcd, 0xd2, 0xe1, 0xff, 0xdf, 0xdd, 0xe5, 0xff, 0xd2, 0xd6, 0xd4, 0xff, 0xd3, 0xd6, 0xd5, 0xff, 0xd6, 0xd4, 0xd6, 0xff, 0xd8, 0xd3, 0xd5, 0xff, 0xd5, 0xd3, 0xd5, 0xff, 0xd1, 0xd3, 0xd4, 0xff, 0xca, 0xcf, 0xcf, 0xff, 0xb7, 0xbe, 0xc5, 0xff, 0xa9, 0xb0, 0xbc, 0xff, 0x9c, 0xa2, 0xb2, 0xff, 0x70, 0x76, 0x8a, 0xff, 0x7d, 0x83, 0x95, 0xff, 0x7c, 0x83, 0x92, 0xff, 0x66, 0x6d, 0x7d, 0xff, 0x4b, 0x52, 0x63, 0xff, 0x6e, 0x74, 0x82, 0xff, 0xaa, 0xad, 0xba, 0xff, 0xb2, 0xb5, 0xbe, 0xff, 0xb5, 0xb8, 0xbe, 0xff, 0x99, 0x9f, 0xaa, 0xff, 0x55, 0x5d, 0x6a, 0xff, 0x47, 0x4f, 0x5a, 0xff, 0x4b, 0x53, 0x61, 0xff, 0x3e, 0x45, 0x59, 0xff, 0x52, 0x57, 0x73, 0xff, 0x64, 0x6b, 0x85, 0xff, 0x62, 0x6a, 0x82, 0xff, 0x5e, 0x65, 0x7f, 0xff, 0x5a, 0x61, 0x7d, 0xff, 0x5b, 0x61, 0x7f, 0xff, 0x5a, 0x5f, 0x7e, 0xff, 0x56, 0x5b, 0x77, 0xff, 0x55, 0x5d, 0x79, 0xff, 0x5c, 0x65, 0x82, 0xff, 0x68, 0x70, 0x8d, 0xff, 0x6c, 0x74, 0x8d, 0xff, 0x75, 0x78, 0x8d, 0xff, 0x70, 0x72, 0x8b, 0xff, 0x74, 0x74, 0x90, 0xff, 0x6b, 0x6d, 0x84, 0xff, 0x66, 0x67, 0x7a, 0xff, 0x63, 0x63, 0x75, 0xff, 0x5a, 0x58, 0x69, 0xff, 0x71, 0x75, 0x80, 0xff, 0xb6, 0xbf, 0xc4, 0xff, 0xe3, 0xe7, 0xed, 0xff, 0xe2, 0xe4, 0xe8, 0xff, 0xdc, 0xdd, 0xde, 0xff, 0xdb, 0xdb, 0xda, 0xff, 0xd1, 0xd3, 0xd2, 0xff, 0xc8, 0xc9, 0xcb, 0xff, 0xc7, 0xc4, 0xc9, 0xff, 0xbe, 0xba, 0xc2, 0xff, 0xb3, 0xb2, 0xbe, 0xff, 0xa3, 0xaa, 0xb4, 0xff, 0x56, 0x5d, 0x71, 0xff, 0x46, 0x4a, 0x63, 0xff, 0x99, 0x9f, 0xac, 0xff, 0xba, 0xbf, 0xc4, 0xff, 0xc9, 0xcc, 0xd0, 0xff, 0xce, 0xcf, 0xd0, 0xff, 0xc6, 0xca, 0xc9, 0xff, 0xb8, 0xbd, 0xbf, 0xff, 0x9f, 0xa5, 0xb0, 0xff, 0x81, 0x8d, 0xa0, 0xff, 0x61, 0x72, 0x8b, 0xff, 0x38, 0x4c, 0x67, 0xff, 0x2c, 0x44, 0x5b, 0xff, 0x3b, 0x54, 0x6b, 0xff, 0x6a, 0x80, 0x99, 0xff, 0x5a, 0x72, 0x8c, 0xff, 0x33, 0x50, 0x6c, 0xff, 0x48, 0x6c, 0x89, 0xff, 0x77, 0x9b, 0xb5, 0xff, 0x93, 0xb8, 0xcf, 0xff, 0x7d, 0xa6, 0xbe, 0xff, 0x69, 0x94, 0xaa, 0xff, 0x7f, 0xab, 0xbf, 0xff, 0x7c, 0xa6, 0xbd, 0xff, 0x2b, 0x50, 0x62, 0xff, 0x28, 0x47, 0x57, 0xff, 0x3e, 0x59, 0x6f, 0xff, 0x3d, 0x62, 0x7a, 0xff, 0x2f, 0x60, 0x76, 0xff, 0x4d, 0x7a, 0x8d, 0xff, 0x42, 0x7a, 0x8b, 0xff, 0x42, 0x7c, 0x8a, 0xff, 0x72, 0x87, 0x91, 0xff, 0xca, 0xd1, 0xd3, 0xff, 0xb6, 0xbe, 0xbc, 0xff, 0xc8, 0xc6, 0xc8, 0xff, 0xcb, 0xc6, 0xc8, 0xff, 0xc5, 0xc7, 0xc6, 0xff, 0xc8, 0xc8, 0xc8, 0xff, 0xc8, 0xc8, 0xc8, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc7, 0xc6, 0xc6, 0xef, 0xda, 0xb6, 0xb6, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x21, 0x2f, 0xa0, 0x04, 0x14, 0x24, 0xff, 0x10, 0x22, 0x32, 0xff, 0x1d, 0x30, 0x41, 0xff, 0x00, 0x12, 0x24, 0xff, 0x7f, 0x8b, 0x94, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4, 0xe2, 0xe2, 0xff, 0xdb, 0xdd, 0xdf, 0xff, 0xd9, 0xda, 0xde, 0xff, 0xd6, 0xd6, 0xdb, 0xff, 0xcb, 0xcc, 0xd4, 0xff, 0xb7, 0xb9, 0xc1, 0xff, 0xa7, 0xaa, 0xbc, 0xff, 0x6a, 0x71, 0x83, 0xff, 0x9c, 0xa2, 0xae, 0xff, 0xef, 0xf0, 0xf8, 0xff, 0xd4, 0xd5, 0xd8, 0xff, 0xd3, 0xd6, 0xd5, 0xff, 0xd4, 0xd2, 0xd3, 0xff, 0xd4, 0xd1, 0xd3, 0xff, 0xd4, 0xd2, 0xd4, 0xff, 0xd1, 0xd3, 0xd5, 0xff, 0xce, 0xd2, 0xd4, 0xff, 0xc5, 0xca, 0xcb, 0xff, 0xab, 0xb6, 0xbe, 0xff, 0x9d, 0xa9, 0xb7, 0xff, 0x7f, 0x88, 0x9b, 0xff, 0x49, 0x4f, 0x66, 0xff, 0x4c, 0x53, 0x66, 0xff, 0x56, 0x5f, 0x6e, 0xff, 0x81, 0x87, 0x92, 0xff, 0xb6, 0xb9, 0xc3, 0xff, 0xca, 0xce, 0xd6, 0xff, 0xc4, 0xc6, 0xcb, 0xff, 0xc3, 0xc5, 0xca, 0xff, 0xc5, 0xc8, 0xcb, 0xff, 0xd1, 0xd3, 0xd8, 0xff, 0xc1, 0xc7, 0xcd, 0xff, 0x73, 0x7c, 0x87, 0xff, 0x61, 0x69, 0x7a, 0xff, 0x77, 0x7b, 0x8e, 0xff, 0x88, 0x85, 0x9c, 0xff, 0x9e, 0x9e, 0xb3, 0xff, 0xa0, 0xa2, 0xb5, 0xff, 0x98, 0x99, 0xad, 0xff, 0x86, 0x87, 0x9e, 0xff, 0x8e, 0x8e, 0xa7, 0xff, 0x9a, 0x98, 0xb4, 0xff, 0x98, 0x9b, 0xb3, 0xff, 0x92, 0x98, 0xb0, 0xff, 0x91, 0x95, 0xae, 0xff, 0x94, 0x97, 0xb0, 0xff, 0x92, 0x93, 0xac, 0xff, 0x95, 0x94, 0xab, 0xff, 0x8e, 0x8b, 0xa6, 0xff, 0x78, 0x74, 0x93, 0xff, 0x7b, 0x78, 0x94, 0xff, 0x7c, 0x7a, 0x94, 0xff, 0x5d, 0x5d, 0x70, 0xff, 0x7f, 0x82, 0x8e, 0xff, 0xd8, 0xdc, 0xe1, 0xff, 0xe4, 0xe8, 0xeb, 0xff, 0xd6, 0xdb, 0xde, 0xff, 0xd8, 0xdc, 0xdd, 0xff, 0xda, 0xdf, 0xdd, 0xff, 0xdb, 0xe0, 0xdd, 0xff, 0xd4, 0xd8, 0xd8, 0xff, 0xce, 0xd0, 0xd2, 0xff, 0xc9, 0xc9, 0xcd, 0xff, 0xc1, 0xc1, 0xc6, 0xff, 0xb4, 0xb5, 0xbf, 0xff, 0xbb, 0xbd, 0xc5, 0xff, 0x79, 0x80, 0x8e, 0xff, 0x2a, 0x32, 0x46, 0xff, 0x8a, 0x8f, 0xa2, 0xff, 0xb4, 0xb7, 0xbf, 0xff, 0xc8, 0xcc, 0xcb, 0xff, 0xcb, 0xcf, 0xd0, 0xff, 0xc4, 0xc6, 0xcb, 0xff, 0xb8, 0xbc, 0xc2, 0xff, 0xa6, 0xac, 0xb4, 0xff, 0x89, 0x92, 0xa0, 0xff, 0x6f, 0x7d, 0x91, 0xff, 0x48, 0x59, 0x72, 0xff, 0x28, 0x3d, 0x57, 0xff, 0x2e, 0x48, 0x5e, 0xff, 0x5a, 0x72, 0x8b, 0xff, 0x60, 0x7b, 0x96, 0xff, 0x34, 0x53, 0x70, 0xff, 0x46, 0x67, 0x85, 0xff, 0x6c, 0x91, 0xac, 0xff, 0x90, 0xb9, 0xcf, 0xff, 0x7f, 0xa8, 0xc0, 0xff, 0x6c, 0x96, 0xad, 0xff, 0x7b, 0xa5, 0xbb, 0xff, 0x81, 0xac, 0xc3, 0xff, 0x31, 0x55, 0x6b, 0xff, 0x25, 0x40, 0x56, 0xff, 0x46, 0x65, 0x7b, 0xff, 0x38, 0x62, 0x78, 0xff, 0x33, 0x67, 0x7d, 0xff, 0x4b, 0x76, 0x8d, 0xff, 0x4a, 0x74, 0x8b, 0xff, 0x52, 0x84, 0x96, 0xff, 0x4e, 0x77, 0x80, 0xff, 0xa4, 0xb6, 0xb8, 0xff, 0xcb, 0xca, 0xca, 0xff, 0xc4, 0xc4, 0xc4, 0xff, 0xc6, 0xc8, 0xc8, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc8, 0xc8, 0xc8, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc6, 0xff, 0xc7, 0xc7, 0xc5, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x22, 0x30, 0x4a, 0x00, 0x0b, 0x1a, 0xff, 0x0f, 0x1f, 0x2b, 0xff, 0x22, 0x2d, 0x3a, 0xff, 0x00, 0x09, 0x1b, 0xff, 0x72, 0x7d, 0x89, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5, 0xe3, 0xe3, 0xff, 0xdb, 0xdd, 0xdf, 0xff, 0xd8, 0xd9, 0xdd, 0xff, 0xd3, 0xd3, 0xda, 0xff, 0xca, 0xcc, 0xd4, 0xff, 0xbc, 0xbe, 0xc5, 0xff, 0x95, 0x9d, 0xaf, 0xff, 0x76, 0x7c, 0x8f, 0xff, 0xd1, 0xd2, 0xda, 0xff, 0xe3, 0xe4, 0xe5, 0xff, 0xd3, 0xd6, 0xd4, 0xff, 0xd5, 0xd7, 0xd7, 0xff, 0xd5, 0xd4, 0xd6, 0xff, 0xd1, 0xcf, 0xd2, 0xff, 0xce, 0xcf, 0xd3, 0xff, 0xcc, 0xd0, 0xd4, 0xff, 0xc8, 0xcc, 0xd1, 0xff, 0xb9, 0xbf, 0xc3, 0xff, 0xa0, 0xab, 0xb6, 0xff, 0x92, 0x9e, 0xb0, 0xff, 0x44, 0x4d, 0x60, 0xff, 0x09, 0x10, 0x22, 0xff, 0x5d, 0x65, 0x74, 0xff, 0xa8, 0xb1, 0xbb, 0xff, 0xcb, 0xce, 0xd5, 0xff, 0xc7, 0xc9, 0xcf, 0xff, 0xbc, 0xbf, 0xc3, 0xff, 0xbf, 0xc3, 0xc6, 0xff, 0xc4, 0xc7, 0xcc, 0xff, 0xc4, 0xc7, 0xcc, 0xff, 0xc9, 0xc8, 0xca, 0xff, 0xca, 0xcb, 0xd0, 0xff, 0x8e, 0x96, 0xa3, 0xff, 0x44, 0x4d, 0x5f, 0xff, 0x62, 0x67, 0x7b, 0xff, 0x8c, 0x88, 0x9c, 0xff, 0x98, 0x98, 0xaa, 0xff, 0xa0, 0xa4, 0xb5, 0xff, 0xa3, 0xa6, 0xb9, 0xff, 0x98, 0x98, 0xaf, 0xff, 0x92, 0x91, 0xaa, 0xff, 0x99, 0x98, 0xb3, 0xff, 0x8c, 0x8e, 0xa8, 0xff, 0x8c, 0x90, 0xa9, 0xff, 0x8a, 0x8d, 0xa6, 0xff, 0x8a, 0x8a, 0xa3, 0xff, 0x8e, 0x8e, 0xa8, 0xff, 0x7d, 0x7d, 0x98, 0xff, 0x76, 0x77, 0x91, 0xff, 0x78, 0x78, 0x93, 0xff, 0x7d, 0x7e, 0x98, 0xff, 0x54, 0x59, 0x6d, 0xff, 0x78, 0x7e, 0x89, 0xff, 0xd3, 0xdc, 0xdd, 0xff, 0xd8, 0xdd, 0xdf, 0xff, 0xcf, 0xd0, 0xd7, 0xff, 0xcf, 0xd1, 0xd5, 0xff, 0xce, 0xd1, 0xd4, 0xff, 0xda, 0xdb, 0xde, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xd9, 0xda, 0xdb, 0xff, 0xd3, 0xd4, 0xd5, 0xff, 0xcd, 0xcf, 0xd2, 0xff, 0xc7, 0xc9, 0xce, 0xff, 0xbb, 0xbe, 0xc5, 0xff, 0xb8, 0xb8, 0xbf, 0xff, 0xae, 0xb7, 0xbd, 0xff, 0x49, 0x54, 0x63, 0xff, 0x5d, 0x62, 0x78, 0xff, 0xac, 0xaf, 0xba, 0xff, 0xc6, 0xcb, 0xca, 0xff, 0xc9, 0xce, 0xce, 0xff, 0xc1, 0xc4, 0xcc, 0xff, 0xba, 0xbd, 0xc6, 0xff, 0xab, 0xb1, 0xb6, 0xff, 0x93, 0x9a, 0xa5, 0xff, 0x77, 0x83, 0x93, 0xff, 0x54, 0x64, 0x7c, 0xff, 0x2c, 0x3f, 0x58, 0xff, 0x2b, 0x46, 0x5a, 0xff, 0x43, 0x5e, 0x75, 0xff, 0x62, 0x7f, 0x9a, 0xff, 0x39, 0x58, 0x75, 0xff, 0x38, 0x58, 0x77, 0xff, 0x64, 0x8a, 0xa5, 0xff, 0x91, 0xbd, 0xd2, 0xff, 0x82, 0xab, 0xc2, 0xff, 0x6c, 0x94, 0xab, 0xff, 0x75, 0x9e, 0xb5, 0xff, 0x7b, 0xa6, 0xbd, 0xff, 0x48, 0x6c, 0x84, 0xff, 0x28, 0x44, 0x5c, 0xff, 0x4c, 0x6b, 0x82, 0xff, 0x36, 0x62, 0x78, 0xff, 0x37, 0x6b, 0x81, 0xff, 0x46, 0x71, 0x88, 0xff, 0x59, 0x7b, 0x95, 0xff, 0x5c, 0x8c, 0x9e, 0xff, 0x47, 0x7d, 0x85, 0xff, 0x7a, 0x93, 0x96, 0xff, 0xd9, 0xd5, 0xd6, 0xff, 0xc1, 0xc1, 0xc0, 0xff, 0xc3, 0xc8, 0xc6, 0xff, 0xc6, 0xc5, 0xc5, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc8, 0xc8, 0xc8, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc4, 0xc7, 0xc4, 0x4a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3f, 0x3f, 0x04, 0x00, 0x05, 0x0f, 0xe7, 0x0b, 0x16, 0x1c, 0xff, 0x15, 0x20, 0x28, 0xff, 0x00, 0x0c, 0x1a, 0xff, 0x5f, 0x69, 0x74, 0xff, 0xfd, 0xfc, 0xf9, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xdd, 0xdf, 0xe0, 0xff, 0xd7, 0xd9, 0xdb, 0xff, 0xd2, 0xd3, 0xd9, 0xff, 0xcb, 0xce, 0xd4, 0xff, 0xc2, 0xc3, 0xcc, 0xff, 0x8c, 0x93, 0xa1, 0xff, 0x9f, 0xa4, 0xb2, 0xff, 0xeb, 0xec, 0xf3, 0xff, 0xd6, 0xd8, 0xd9, 0xff, 0xd4, 0xd4, 0xd4, 0xff, 0xd4, 0xd6, 0xd6, 0xff, 0xd2, 0xd3, 0xd6, 0xff, 0xd1, 0xd2, 0xd7, 0xff, 0xcc, 0xce, 0xd4, 0xff, 0xc6, 0xc9, 0xce, 0xff, 0xc0, 0xc4, 0xcb, 0xff, 0xb0, 0xb5, 0xbc, 0xff, 0x97, 0xa0, 0xad, 0xff, 0x42, 0x4d, 0x5f, 0xff, 0x00, 0x01, 0x10, 0xff, 0x6e, 0x73, 0x7f, 0xff, 0xd2, 0xd6, 0xde, 0xff, 0xd3, 0xd6, 0xda, 0xff, 0xc2, 0xc4, 0xc8, 0xff, 0xbe, 0xc1, 0xc6, 0xff, 0xc2, 0xc4, 0xc9, 0xff, 0xc5, 0xc7, 0xcd, 0xff, 0xc4, 0xc7, 0xcc, 0xff, 0xc2, 0xc6, 0xca, 0xff, 0xc8, 0xc7, 0xcd, 0xff, 0xbf, 0xbf, 0xc7, 0xff, 0xa8, 0xad, 0xbc, 0xff, 0x4c, 0x55, 0x66, 0xff, 0x3d, 0x46, 0x59, 0xff, 0x6a, 0x71, 0x84, 0xff, 0x83, 0x87, 0x9b, 0xff, 0x83, 0x87, 0x9d, 0xff, 0x87, 0x89, 0xa0, 0xff, 0x92, 0x94, 0xad, 0xff, 0x8c, 0x8e, 0xa9, 0xff, 0x89, 0x8b, 0xa8, 0xff, 0x7e, 0x7f, 0x9c, 0xff, 0x7e, 0x7d, 0x9a, 0xff, 0x80, 0x82, 0x9f, 0xff, 0x7c, 0x7e, 0x9c, 0xff, 0x75, 0x79, 0x95, 0xff, 0x6b, 0x6f, 0x8d, 0xff, 0x6c, 0x76, 0x8f, 0xff, 0x75, 0x80, 0x95, 0xff, 0x52, 0x5d, 0x6f, 0xff, 0x5d, 0x67, 0x75, 0xff, 0xc3, 0xcc, 0xd2, 0xff, 0xcd, 0xd8, 0xd5, 0xff, 0xc0, 0xc9, 0xce, 0xff, 0xba, 0xc0, 0xcb, 0xff, 0xbf, 0xc4, 0xcc, 0xff, 0xc2, 0xc3, 0xcb, 0xff, 0xd5, 0xd4, 0xda, 0xff, 0xe2, 0xde, 0xe3, 0xff, 0xe1, 0xde, 0xe0, 0xff, 0xd7, 0xd7, 0xd8, 0xff, 0xd0, 0xd1, 0xd4, 0xff, 0xca, 0xcb, 0xd0, 0xff, 0xc5, 0xc6, 0xcc, 0xff, 0xb8, 0xb9, 0xc1, 0xff, 0xb7, 0xbf, 0xc6, 0xff, 0x7c, 0x86, 0x94, 0xff, 0x46, 0x4b, 0x60, 0xff, 0x9a, 0xa0, 0xac, 0xff, 0xc0, 0xc7, 0xc7, 0xff, 0xcb, 0xce, 0xcf, 0xff, 0xc6, 0xca, 0xce, 0xff, 0xbc, 0xbf, 0xc5, 0xff, 0xad, 0xb1, 0xb9, 0xff, 0x9b, 0xa2, 0xac, 0xff, 0x7d, 0x88, 0x99, 0xff, 0x5d, 0x6d, 0x85, 0xff, 0x2b, 0x40, 0x57, 0xff, 0x27, 0x40, 0x54, 0xff, 0x36, 0x50, 0x66, 0xff, 0x5f, 0x7c, 0x94, 0xff, 0x44, 0x65, 0x7f, 0xff, 0x31, 0x52, 0x70, 0xff, 0x5c, 0x82, 0x9c, 0xff, 0x8c, 0xb6, 0xcc, 0xff, 0x85, 0xae, 0xc5, 0xff, 0x6d, 0x96, 0xad, 0xff, 0x70, 0x99, 0xb0, 0xff, 0x70, 0x9b, 0xb2, 0xff, 0x5e, 0x85, 0x9b, 0xff, 0x2b, 0x4a, 0x61, 0xff, 0x4a, 0x68, 0x7f, 0xff, 0x35, 0x5d, 0x74, 0xff, 0x37, 0x69, 0x7f, 0xff, 0x46, 0x70, 0x88, 0xff, 0x5d, 0x86, 0x9d, 0xff, 0x4c, 0x80, 0x91, 0xff, 0x57, 0x87, 0x91, 0xff, 0x64, 0x81, 0x86, 0xff, 0xc0, 0xc6, 0xc9, 0xff, 0xca, 0xc8, 0xc8, 0xff, 0xc2, 0xc1, 0xc1, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc6, 0xe7, 0xcc, 0xcc, 0xcc, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x0b, 0x87, 0x04, 0x08, 0x0b, 0xff, 0x13, 0x1a, 0x20, 0xff, 0x00, 0x0d, 0x1b, 0xff, 0x45, 0x50, 0x5b, 0xff, 0xef, 0xf1, 0xee, 0xff, 0xec, 0xec, 0xea, 0xff, 0xdc, 0xde, 0xdf, 0xff, 0xd6, 0xd8, 0xd9, 0xff, 0xd2, 0xd4, 0xd7, 0xff, 0xca, 0xcd, 0xd2, 0xff, 0xbf, 0xc1, 0xc7, 0xff, 0x97, 0x9b, 0xa5, 0xff, 0xcc, 0xcf, 0xd9, 0xff, 0xe8, 0xe7, 0xeb, 0xff, 0xd5, 0xd3, 0xd4, 0xff, 0xd2, 0xd1, 0xd1, 0xff, 0xd0, 0xd1, 0xd1, 0xff, 0xcc, 0xce, 0xd1, 0xff, 0xcd, 0xce, 0xd4, 0xff, 0xc5, 0xc6, 0xcc, 0xff, 0xbc, 0xbf, 0xc5, 0xff, 0xb7, 0xba, 0xc1, 0xff, 0xae, 0xb0, 0xb8, 0xff, 0x59, 0x5e, 0x6b, 0xff, 0x09, 0x10, 0x1e, 0xff, 0x8a, 0x90, 0x9a, 0xff, 0xea, 0xed, 0xf2, 0xff, 0xd0, 0xd2, 0xd3, 0xff, 0xc9, 0xc9, 0xc8, 0xff, 0xcb, 0xca, 0xcc, 0xff, 0xcc, 0xcb, 0xce, 0xff, 0xce, 0xcd, 0xd2, 0xff, 0xcc, 0xcd, 0xd2, 0xff, 0xc8, 0xcb, 0xd0, 0xff, 0xc4, 0xc8, 0xcc, 0xff, 0xc1, 0xc1, 0xc9, 0xff, 0xb8, 0xb7, 0xc3, 0xff, 0xae, 0xb2, 0xc1, 0xff, 0x59, 0x60, 0x74, 0xff, 0x34, 0x40, 0x52, 0xff, 0x4b, 0x58, 0x68, 0xff, 0x64, 0x6b, 0x80, 0xff, 0x6f, 0x74, 0x8d, 0xff, 0x6f, 0x74, 0x8c, 0xff, 0x6c, 0x72, 0x89, 0xff, 0x72, 0x77, 0x90, 0xff, 0x72, 0x76, 0x90, 0xff, 0x6a, 0x6c, 0x89, 0xff, 0x63, 0x64, 0x83, 0xff, 0x6a, 0x6c, 0x8a, 0xff, 0x6a, 0x6d, 0x8a, 0xff, 0x65, 0x6b, 0x85, 0xff, 0x63, 0x6b, 0x85, 0xff, 0x51, 0x5c, 0x76, 0xff, 0x38, 0x44, 0x5c, 0xff, 0x2c, 0x34, 0x4a, 0xff, 0x9a, 0xa2, 0xb3, 0xff, 0xc8, 0xd0, 0xdc, 0xff, 0xb4, 0xbc, 0xc5, 0xff, 0xab, 0xbb, 0xc4, 0xff, 0xa3, 0xb7, 0xc2, 0xff, 0xa0, 0xb0, 0xb9, 0xff, 0xa3, 0xad, 0xb5, 0xff, 0xca, 0xd0, 0xd5, 0xff, 0xd8, 0xdb, 0xdf, 0xff, 0xdf, 0xe0, 0xe1, 0xff, 0xdc, 0xdb, 0xdb, 0xff, 0xd6, 0xd5, 0xd7, 0xff, 0xcc, 0xcd, 0xd1, 0xff, 0xc8, 0xc8, 0xce, 0xff, 0xc0, 0xc0, 0xc7, 0xff, 0xb3, 0xb8, 0xbc, 0xff, 0xa4, 0xac, 0xb7, 0xff, 0x53, 0x59, 0x6e, 0xff, 0x7a, 0x82, 0x91, 0xff, 0xbc, 0xc4, 0xc6, 0xff, 0xc9, 0xcc, 0xcc, 0xff, 0xc7, 0xc9, 0xcd, 0xff, 0xbb, 0xbf, 0xc3, 0xff, 0xaf, 0xb3, 0xb9, 0xff, 0xa0, 0xa5, 0xb0, 0xff, 0x86, 0x92, 0xa2, 0xff, 0x6a, 0x79, 0x90, 0xff, 0x31, 0x43, 0x59, 0xff, 0x25, 0x3a, 0x4f, 0xff, 0x34, 0x4c, 0x62, 0xff, 0x53, 0x6f, 0x88, 0xff, 0x4a, 0x6b, 0x84, 0xff, 0x33, 0x55, 0x72, 0xff, 0x55, 0x7c, 0x96, 0xff, 0x89, 0xb2, 0xc9, 0xff, 0x87, 0xb0, 0xc7, 0xff, 0x7b, 0xa4, 0xbb, 0xff, 0x74, 0x9d, 0xb4, 0xff, 0x61, 0x8b, 0xa2, 0xff, 0x63, 0x8c, 0xa3, 0xff, 0x3a, 0x5c, 0x72, 0xff, 0x40, 0x5e, 0x75, 0xff, 0x3f, 0x66, 0x7c, 0xff, 0x32, 0x62, 0x78, 0xff, 0x42, 0x6d, 0x85, 0xff, 0x50, 0x7c, 0x90, 0xff, 0x5d, 0x8e, 0x9e, 0xff, 0x65, 0x90, 0x9e, 0xff, 0x5c, 0x7f, 0x88, 0xff, 0x83, 0x96, 0x9b, 0xff, 0xd7, 0xd6, 0xda, 0xff, 0xc1, 0xbd, 0xbe, 0xff, 0xc5, 0xc6, 0xc5, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc6, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x07, 0x07, 0x22, 0x03, 0x04, 0x05, 0xfe, 0x0a, 0x10, 0x16, 0xff, 0x01, 0x0f, 0x1e, 0xff, 0x37, 0x46, 0x53, 0xff, 0xd2, 0xda, 0xdb, 0xff, 0xf3, 0xf4, 0xf2, 0xff, 0xde, 0xe0, 0xdf, 0xff, 0xd6, 0xd9, 0xd9, 0xff, 0xcf, 0xd0, 0xd3, 0xff, 0xc4, 0xc7, 0xcc, 0xff, 0xac, 0xaf, 0xb4, 0xff, 0xb5, 0xb7, 0xbc, 0xff, 0xe3, 0xe6, 0xe8, 0xff, 0xd7, 0xd6, 0xd5, 0xff, 0xd1, 0xce, 0xcd, 0xff, 0xd7, 0xd6, 0xd5, 0xff, 0xce, 0xcd, 0xcf, 0xff, 0xd3, 0xd3, 0xd5, 0xff, 0xd3, 0xd2, 0xd4, 0xff, 0xc4, 0xc3, 0xc6, 0xff, 0xd7, 0xd7, 0xda, 0xff, 0xd3, 0xd2, 0xd6, 0xff, 0xc3, 0xc2, 0xc7, 0xff, 0x88, 0x86, 0x90, 0xff, 0xa8, 0xa7, 0xb0, 0xff, 0xe5, 0xe5, 0xe8, 0xff, 0xc3, 0xc6, 0xc5, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xcd, 0xcd, 0xcd, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xd0, 0xcd, 0xce, 0xff, 0xcd, 0xcc, 0xcf, 0xff, 0xcd, 0xce, 0xd2, 0xff, 0xc9, 0xcc, 0xd2, 0xff, 0xc2, 0xc5, 0xcb, 0xff, 0xb1, 0xb5, 0xbf, 0xff, 0xa8, 0xab, 0xba, 0xff, 0xa9, 0xae, 0xbf, 0xff, 0x63, 0x6b, 0x7e, 0xff, 0x38, 0x42, 0x55, 0xff, 0x4a, 0x58, 0x68, 0xff, 0x53, 0x5d, 0x70, 0xff, 0x5d, 0x66, 0x7b, 0xff, 0x5f, 0x69, 0x7d, 0xff, 0x59, 0x62, 0x76, 0xff, 0x57, 0x60, 0x74, 0xff, 0x55, 0x5e, 0x72, 0xff, 0x51, 0x5b, 0x72, 0xff, 0x51, 0x5b, 0x73, 0xff, 0x50, 0x59, 0x70, 0xff, 0x57, 0x5f, 0x74, 0xff, 0x53, 0x5b, 0x6e, 0xff, 0x40, 0x49, 0x5b, 0xff, 0x54, 0x59, 0x6d, 0xff, 0x62, 0x64, 0x79, 0xff, 0x6f, 0x73, 0x84, 0xff, 0xa7, 0xad, 0xbb, 0xff, 0xa0, 0xa5, 0xb5, 0xff, 0x9e, 0xa3, 0xb7, 0xff, 0x8e, 0x9c, 0xab, 0xff, 0x82, 0x95, 0xa0, 0xff, 0x72, 0x80, 0x8a, 0xff, 0x7f, 0x8a, 0x90, 0xff, 0xbe, 0xc6, 0xc7, 0xff, 0xcd, 0xd2, 0xd3, 0xff, 0xda, 0xdb, 0xdb, 0xff, 0xd9, 0xd6, 0xd6, 0xff, 0xd1, 0xd0, 0xd1, 0xff, 0xca, 0xc9, 0xcd, 0xff, 0xc7, 0xc7, 0xcc, 0xff, 0xc2, 0xc1, 0xc9, 0xff, 0xbc, 0xbe, 0xbf, 0xff, 0xb5, 0xba, 0xc3, 0xff, 0x6d, 0x72, 0x89, 0xff, 0x5e, 0x67, 0x77, 0xff, 0xb1, 0xbb, 0xbf, 0xff, 0xc6, 0xc9, 0xcc, 0xff, 0xc5, 0xc5, 0xca, 0xff, 0xbf, 0xc2, 0xc6, 0xff, 0xb5, 0xb8, 0xbc, 0xff, 0xa1, 0xa7, 0xb0, 0xff, 0x89, 0x94, 0xa3, 0xff, 0x69, 0x78, 0x8e, 0xff, 0x3c, 0x4c, 0x61, 0xff, 0x2a, 0x3d, 0x51, 0xff, 0x25, 0x3c, 0x50, 0xff, 0x42, 0x5f, 0x76, 0xff, 0x4f, 0x71, 0x88, 0xff, 0x3b, 0x5f, 0x79, 0xff, 0x4b, 0x73, 0x8c, 0xff, 0x84, 0xae, 0xc5, 0xff, 0x89, 0xb2, 0xc9, 0xff, 0x7f, 0xa8, 0xbf, 0xff, 0x7d, 0xa6, 0xbd, 0xff, 0x5c, 0x85, 0x9c, 0xff, 0x65, 0x90, 0xa6, 0xff, 0x3e, 0x63, 0x79, 0xff, 0x35, 0x51, 0x69, 0xff, 0x46, 0x69, 0x81, 0xff, 0x3f, 0x6c, 0x84, 0xff, 0x43, 0x6f, 0x86, 0xff, 0x3e, 0x6a, 0x7e, 0xff, 0x6b, 0x94, 0xa5, 0xff, 0x56, 0x7a, 0x8b, 0xff, 0x65, 0x8c, 0x99, 0xff, 0x60, 0x82, 0x87, 0xff, 0xbc, 0xc2, 0xc7, 0xff, 0xcb, 0xc6, 0xc9, 0xff, 0xc2, 0xc2, 0xc2, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc6, 0xfe, 0xc4, 0xc4, 0xc4, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x04, 0x05, 0xb4, 0x07, 0x10, 0x16, 0xff, 0x0e, 0x20, 0x30, 0xff, 0x1e, 0x30, 0x42, 0xff, 0x9c, 0xa6, 0xad, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdd, 0xdf, 0xde, 0xff, 0xd3, 0xd5, 0xd6, 0xff, 0xc7, 0xca, 0xcc, 0xff, 0xc2, 0xc6, 0xcb, 0xff, 0xbf, 0xc3, 0xc9, 0xff, 0xd4, 0xd7, 0xd8, 0xff, 0xe6, 0xe7, 0xe6, 0xff, 0xda, 0xd8, 0xd5, 0xff, 0xdb, 0xd8, 0xd3, 0xff, 0xe1, 0xe0, 0xde, 0xff, 0xd6, 0xd6, 0xd9, 0xff, 0xd5, 0xd1, 0xd2, 0xff, 0xe0, 0xdd, 0xdb, 0xff, 0xdf, 0xdb, 0xdb, 0xff, 0xda, 0xd5, 0xd6, 0xff, 0xda, 0xd4, 0xd6, 0xff, 0xe1, 0xda, 0xde, 0xff, 0xe9, 0xe0, 0xe7, 0xff, 0xe6, 0xdf, 0xe3, 0xff, 0xcc, 0xc8, 0xc6, 0xff, 0xca, 0xcb, 0xc6, 0xff, 0xce, 0xcf, 0xce, 0xff, 0xc9, 0xcc, 0xd0, 0xff, 0xd1, 0xce, 0xcf, 0xff, 0xd0, 0xcb, 0xcb, 0xff, 0xca, 0xc8, 0xc9, 0xff, 0xc3, 0xc4, 0xc8, 0xff, 0xb7, 0xbb, 0xc1, 0xff, 0xa9, 0xac, 0xb5, 0xff, 0x93, 0x9f, 0xa9, 0xff, 0x7e, 0x8b, 0x9a, 0xff, 0x93, 0x9b, 0xaf, 0xff, 0x6a, 0x71, 0x86, 0xff, 0x34, 0x3c, 0x4f, 0xff, 0x4a, 0x55, 0x65, 0xff, 0x52, 0x5e, 0x6f, 0xff, 0x54, 0x61, 0x73, 0xff, 0x56, 0x62, 0x73, 0xff, 0x57, 0x64, 0x74, 0xff, 0x51, 0x5e, 0x6f, 0xff, 0x4f, 0x5c, 0x6d, 0xff, 0x4c, 0x5e, 0x6e, 0xff, 0x4a, 0x5d, 0x6d, 0xff, 0x4f, 0x60, 0x6f, 0xff, 0x4a, 0x58, 0x65, 0xff, 0x48, 0x52, 0x5f, 0xff, 0x81, 0x8a, 0x97, 0xff, 0xc3, 0xc4, 0xcc, 0xff, 0xcf, 0xcd, 0xd0, 0xff, 0xcc, 0xcf, 0xd0, 0xff, 0xba, 0xc0, 0xc2, 0xff, 0x92, 0x99, 0xa2, 0xff, 0x89, 0x90, 0xa1, 0xff, 0x82, 0x86, 0x99, 0xff, 0x82, 0x85, 0x93, 0xff, 0x7b, 0x7a, 0x87, 0xff, 0xaf, 0xad, 0xb5, 0xff, 0xd0, 0xce, 0xcf, 0xff, 0xd3, 0xd0, 0xcf, 0xff, 0xe0, 0xde, 0xdc, 0xff, 0xd7, 0xd6, 0xd5, 0xff, 0xd0, 0xce, 0xcf, 0xff, 0xcf, 0xcf, 0xd1, 0xff, 0xc5, 0xc5, 0xc9, 0xff, 0xc0, 0xbf, 0xc7, 0xff, 0xb8, 0xbb, 0xbc, 0xff, 0xb3, 0xb8, 0xbf, 0xff, 0x8b, 0x92, 0xaa, 0xff, 0x58, 0x62, 0x74, 0xff, 0x9d, 0xa8, 0xac, 0xff, 0xc2, 0xc4, 0xc7, 0xff, 0xc4, 0xc4, 0xc8, 0xff, 0xc0, 0xc2, 0xc5, 0xff, 0xb5, 0xb7, 0xbc, 0xff, 0xa8, 0xac, 0xb5, 0xff, 0x8f, 0x99, 0xa8, 0xff, 0x68, 0x78, 0x8d, 0xff, 0x3e, 0x4e, 0x63, 0xff, 0x33, 0x45, 0x57, 0xff, 0x23, 0x39, 0x4d, 0xff, 0x30, 0x4a, 0x61, 0xff, 0x59, 0x7a, 0x93, 0xff, 0x54, 0x77, 0x91, 0xff, 0x45, 0x6d, 0x85, 0xff, 0x80, 0xaa, 0xc0, 0xff, 0x94, 0xbd, 0xd5, 0xff, 0x7a, 0xa4, 0xbb, 0xff, 0x7c, 0xa6, 0xbd, 0xff, 0x64, 0x8e, 0xa4, 0xff, 0x60, 0x8e, 0xa4, 0xff, 0x42, 0x6a, 0x7f, 0xff, 0x2d, 0x48, 0x60, 0xff, 0x3f, 0x62, 0x79, 0xff, 0x48, 0x73, 0x8a, 0xff, 0x4f, 0x79, 0x92, 0xff, 0x3e, 0x67, 0x7a, 0xff, 0x68, 0x8a, 0x9c, 0xff, 0x62, 0x82, 0x97, 0xff, 0x5b, 0x87, 0x97, 0xff, 0x5a, 0x86, 0x8e, 0xff, 0x8a, 0x99, 0x9c, 0xff, 0xd1, 0xcd, 0xce, 0xff, 0xbf, 0xbe, 0xbd, 0xff, 0xc5, 0xc6, 0xc6, 0xff, 0xc5, 0xc6, 0xc6, 0xff, 0xc7, 0xc6, 0xc6, 0xff, 0xc8, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc6, 0xc6, 0xc6, 0xb5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x40, 0x05, 0x08, 0x10, 0xff, 0x1b, 0x29, 0x3a, 0xff, 0x0d, 0x20, 0x32, 0xff, 0x76, 0x85, 0x8e, 0xff, 0xfa, 0xfb, 0xfb, 0xff, 0xda, 0xda, 0xd8, 0xff, 0xda, 0xda, 0xda, 0xff, 0xde, 0xde, 0xde, 0xff, 0xe2, 0xe1, 0xe2, 0xff, 0xdf, 0xdf, 0xe0, 0xff, 0xe5, 0xe5, 0xe4, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xd3, 0xd0, 0xce, 0xff, 0xe1, 0xdf, 0xdc, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xd2, 0xd1, 0xd1, 0xff, 0xd1, 0xce, 0xcd, 0xff, 0xe6, 0xe3, 0xe1, 0xff, 0xe5, 0xe2, 0xe1, 0xff, 0xcb, 0xc8, 0xc7, 0xff, 0xd3, 0xcf, 0xcf, 0xff, 0xe2, 0xdd, 0xde, 0xff, 0xdc, 0xd5, 0xd6, 0xff, 0xd3, 0xcc, 0xcc, 0xff, 0xde, 0xda, 0xd8, 0xff, 0xdb, 0xd8, 0xd5, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xcf, 0xcd, 0xce, 0xff, 0xd9, 0xd6, 0xd7, 0xff, 0xd5, 0xd1, 0xd2, 0xff, 0xcc, 0xca, 0xca, 0xff, 0xc6, 0xc5, 0xc6, 0xff, 0xbd, 0xbd, 0xbf, 0xff, 0xb2, 0xb1, 0xb3, 0xff, 0x97, 0x96, 0xa0, 0xff, 0x8e, 0x8e, 0x9e, 0xff, 0x9f, 0x9f, 0xb0, 0xff, 0x7c, 0x7d, 0x91, 0xff, 0x34, 0x39, 0x4e, 0xff, 0x3a, 0x41, 0x55, 0xff, 0x44, 0x4e, 0x61, 0xff, 0x57, 0x63, 0x74, 0xff, 0x58, 0x65, 0x77, 0xff, 0x58, 0x65, 0x78, 0xff, 0x5d, 0x69, 0x7b, 0xff, 0x60, 0x69, 0x78, 0xff, 0x5a, 0x6b, 0x7b, 0xff, 0x56, 0x68, 0x79, 0xff, 0x56, 0x60, 0x6f, 0xff, 0x65, 0x69, 0x75, 0xff, 0xa8, 0xa8, 0xae, 0xff, 0xe8, 0xe6, 0xe9, 0xff, 0xe5, 0xe1, 0xe1, 0xff, 0xdb, 0xd7, 0xd3, 0xff, 0xd2, 0xd0, 0xcd, 0xff, 0xdd, 0xdb, 0xd9, 0xff, 0xd4, 0xd4, 0xd4, 0xff, 0xc8, 0xc9, 0xcb, 0xff, 0xc7, 0xc3, 0xc7, 0xff, 0xd4, 0xce, 0xd3, 0xff, 0xd4, 0xcf, 0xd2, 0xff, 0xdf, 0xd9, 0xdb, 0xff, 0xe0, 0xda, 0xda, 0xff, 0xe3, 0xde, 0xdc, 0xff, 0xda, 0xd5, 0xd5, 0xff, 0xda, 0xd6, 0xd7, 0xff, 0xde, 0xda, 0xdb, 0xff, 0xdd, 0xda, 0xdb, 0xff, 0xd2, 0xcf, 0xd0, 0xff, 0xd4, 0xd0, 0xd3, 0xff, 0xc5, 0xc5, 0xc6, 0xff, 0xb9, 0xba, 0xc0, 0xff, 0xa4, 0xa5, 0xb5, 0xff, 0x65, 0x69, 0x79, 0xff, 0x7f, 0x85, 0x8e, 0xff, 0xb5, 0xbb, 0xc0, 0xff, 0xc0, 0xc4, 0xc8, 0xff, 0xbf, 0xc0, 0xc5, 0xff, 0xb4, 0xb7, 0xbd, 0xff, 0xa9, 0xae, 0xb7, 0xff, 0x92, 0x9c, 0xaa, 0xff, 0x6f, 0x7f, 0x93, 0xff, 0x3d, 0x4d, 0x63, 0xff, 0x2e, 0x3e, 0x52, 0xff, 0x35, 0x4a, 0x60, 0xff, 0x2a, 0x44, 0x5c, 0xff, 0x53, 0x73, 0x8c, 0xff, 0x66, 0x8a, 0xa3, 0xff, 0x4a, 0x6e, 0x88, 0xff, 0x75, 0x9b, 0xb3, 0xff, 0x9b, 0xc2, 0xd7, 0xff, 0x87, 0xad, 0xc2, 0xff, 0x7f, 0xa5, 0xbb, 0xff, 0x6b, 0x90, 0xa9, 0xff, 0x60, 0x87, 0x9e, 0xff, 0x55, 0x7b, 0x90, 0xff, 0x2b, 0x4b, 0x62, 0xff, 0x40, 0x63, 0x7a, 0xff, 0x47, 0x6f, 0x86, 0xff, 0x4f, 0x7c, 0x94, 0xff, 0x34, 0x5d, 0x71, 0xff, 0x6c, 0x89, 0x9a, 0xff, 0x8e, 0xaa, 0xba, 0xff, 0x4c, 0x79, 0x82, 0xff, 0x5d, 0x90, 0x97, 0xff, 0x65, 0x7d, 0x89, 0xff, 0xb8, 0xba, 0xc2, 0xff, 0xc9, 0xc5, 0xc5, 0xff, 0xcc, 0xc6, 0xc6, 0xff, 0xc7, 0xc5, 0xc4, 0xff, 0xc4, 0xc8, 0xc7, 0xff, 0xc2, 0xc6, 0xc7, 0xff, 0xc6, 0xc7, 0xc7, 0xff, 0xc8, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x0d, 0x15, 0xcd, 0x3f, 0x4a, 0x58, 0xff, 0x5f, 0x70, 0x7e, 0xff, 0x91, 0x9f, 0xa3, 0xff, 0xe7, 0xea, 0xe7, 0xff, 0xe8, 0xe8, 0xe5, 0xff, 0xe6, 0xe7, 0xe4, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xe7, 0xe4, 0xe2, 0xff, 0xdb, 0xd8, 0xd6, 0xff, 0xe8, 0xe5, 0xe4, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xe4, 0xe3, 0xe2, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xe4, 0xe3, 0xe2, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xd5, 0xd4, 0xd3, 0xff, 0xd0, 0xcd, 0xcb, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xdd, 0xd9, 0xd8, 0xff, 0xd8, 0xd3, 0xd3, 0xff, 0xd1, 0xcd, 0xcc, 0xff, 0xdd, 0xd8, 0xd6, 0xff, 0xdb, 0xd8, 0xd8, 0xff, 0xd1, 0xd0, 0xd0, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd7, 0xd4, 0xd3, 0xff, 0xd6, 0xd4, 0xd2, 0xff, 0xc9, 0xc1, 0xc4, 0xff, 0xd7, 0xcf, 0xd4, 0xff, 0xd2, 0xcf, 0xd5, 0xff, 0xbc, 0xbc, 0xc5, 0xff, 0x99, 0x9b, 0xa5, 0xff, 0x87, 0x8b, 0x96, 0xff, 0x6a, 0x6f, 0x7d, 0xff, 0x54, 0x5e, 0x6d, 0xff, 0x60, 0x6b, 0x7d, 0xff, 0x69, 0x73, 0x87, 0xff, 0x6f, 0x77, 0x8b, 0xff, 0x75, 0x7a, 0x8a, 0xff, 0x76, 0x81, 0x91, 0xff, 0x61, 0x6c, 0x7e, 0xff, 0x73, 0x74, 0x84, 0xff, 0xce, 0xc9, 0xd3, 0xff, 0xec, 0xe6, 0xe7, 0xff, 0xde, 0xd9, 0xd5, 0xff, 0xe6, 0xe2, 0xdf, 0xff, 0xde, 0xda, 0xd9, 0xff, 0xd4, 0xcf, 0xcf, 0xff, 0xdf, 0xda, 0xd9, 0xff, 0xe7, 0xe2, 0xdf, 0xff, 0xe1, 0xdb, 0xd8, 0xff, 0xd6, 0xd1, 0xce, 0xff, 0xe6, 0xe2, 0xdf, 0xff, 0xe4, 0xe0, 0xdd, 0xff, 0xd0, 0xcc, 0xca, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xe5, 0xe1, 0xe0, 0xff, 0xcc, 0xc7, 0xc8, 0xff, 0xda, 0xd4, 0xd6, 0xff, 0xdd, 0xd8, 0xd9, 0xff, 0xd7, 0xd1, 0xd2, 0xff, 0xdd, 0xd8, 0xd8, 0xff, 0xdc, 0xd6, 0xd6, 0xff, 0xd8, 0xd3, 0xd4, 0xff, 0xd1, 0xcf, 0xd2, 0xff, 0xc7, 0xc4, 0xcb, 0xff, 0x9c, 0x9a, 0xa5, 0xff, 0x8b, 0x8d, 0x96, 0xff, 0xa5, 0xab, 0xb0, 0xff, 0xb4, 0xb9, 0xbe, 0xff, 0xb8, 0xba, 0xbf, 0xff, 0xb4, 0xb7, 0xbc, 0xff, 0xa4, 0xa9, 0xb1, 0xff, 0x8c, 0x97, 0xa5, 0xff, 0x6e, 0x7e, 0x91, 0xff, 0x3f, 0x4e, 0x62, 0xff, 0x22, 0x33, 0x47, 0xff, 0x41, 0x54, 0x69, 0xff, 0x2e, 0x48, 0x5f, 0xff, 0x3a, 0x5a, 0x72, 0xff, 0x68, 0x8b, 0xa3, 0xff, 0x5b, 0x7e, 0x99, 0xff, 0x67, 0x8a, 0xa3, 0xff, 0x98, 0xbe, 0xd2, 0xff, 0x96, 0xbb, 0xce, 0xff, 0x85, 0xa9, 0xbe, 0xff, 0x63, 0x86, 0x9f, 0xff, 0x61, 0x83, 0x9c, 0xff, 0x66, 0x8b, 0xa0, 0xff, 0x31, 0x55, 0x6b, 0xff, 0x42, 0x66, 0x7d, 0xff, 0x3c, 0x63, 0x7b, 0xff, 0x50, 0x7d, 0x94, 0xff, 0x35, 0x62, 0x78, 0xff, 0x62, 0x81, 0x94, 0xff, 0xa3, 0xbb, 0xc7, 0xff, 0x60, 0x84, 0x88, 0xff, 0x58, 0x84, 0x89, 0xff, 0x6e, 0x8f, 0x9e, 0xff, 0x8b, 0x99, 0xa5, 0xff, 0xcc, 0xcc, 0xcd, 0xff, 0xcb, 0xc2, 0xc3, 0xff, 0xca, 0xc5, 0xc4, 0xff, 0xc2, 0xc8, 0xc6, 0xff, 0xc2, 0xc7, 0xc8, 0xff, 0xc5, 0xc5, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xff, 0xc7, 0xc7, 0xc7, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x09, 0x12, 0x51, 0x1e, 0x29, 0x34, 0xff, 0xb6, 0xc2, 0xcb, 0xff, 0xf1, 0xf6, 0xf5, 0xff, 0xe9, 0xea, 0xe7, 0xff, 0xe5, 0xe6, 0xe4, 0xff, 0xe2, 0xe3, 0xe1, 0xff, 0xe7, 0xe6, 0xe4, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xcb, 0xc8, 0xc7, 0xff, 0xde, 0xda, 0xd9, 0xff, 0xdc, 0xd8, 0xd7, 0xff, 0xcf, 0xcb, 0xca, 0xff, 0xd6, 0xd2, 0xd1, 0xff, 0xdd, 0xd9, 0xd8, 0xff, 0xd7, 0xd4, 0xd4, 0xff, 0xcd, 0xcc, 0xcc, 0xff, 0xd0, 0xce, 0xce, 0xff, 0xd9, 0xd7, 0xd7, 0xff, 0xd3, 0xd1, 0xd1, 0xff, 0xcd, 0xca, 0xcc, 0xff, 0xd5, 0xd6, 0xd1, 0xff, 0xd9, 0xda, 0xd1, 0xff, 0xd5, 0xd6, 0xd0, 0xff, 0xd8, 0xd8, 0xd4, 0xff, 0xd7, 0xd6, 0xd4, 0xff, 0xdb, 0xdb, 0xd8, 0xff, 0xc0, 0xc3, 0xc9, 0xff, 0x79, 0x7d, 0x8b, 0xff, 0x6c, 0x71, 0x80, 0xff, 0x7b, 0x81, 0x93, 0xff, 0x7d, 0x81, 0x94, 0xff, 0x84, 0x85, 0x96, 0xff, 0x85, 0x88, 0x97, 0xff, 0x86, 0x89, 0x96, 0xff, 0xc2, 0xc2, 0xcc, 0xff, 0xea, 0xe7, 0xeb, 0xff, 0xdd, 0xda, 0xda, 0xff, 0xda, 0xd9, 0xd6, 0xff, 0xe6, 0xe3, 0xe0, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xde, 0xda, 0xd9, 0xff, 0xe4, 0xe0, 0xdf, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xdf, 0xdb, 0xda, 0xff, 0xe2, 0xde, 0xdd, 0xff, 0xd1, 0xcc, 0xcd, 0xff, 0xd6, 0xd1, 0xd3, 0xff, 0xe1, 0xdc, 0xdd, 0xff, 0xce, 0xc9, 0xca, 0xff, 0xd3, 0xce, 0xcf, 0xff, 0xdb, 0xd6, 0xd7, 0xff, 0xd2, 0xcc, 0xcd, 0xff, 0xd2, 0xcd, 0xce, 0xff, 0xd7, 0xd2, 0xd6, 0xff, 0xcf, 0xcc, 0xd1, 0xff, 0xc7, 0xc6, 0xcb, 0xff, 0xb8, 0xb9, 0xbc, 0xff, 0xb4, 0xb5, 0xb9, 0xff, 0xb0, 0xb0, 0xb6, 0xff, 0xa8, 0xab, 0xaf, 0xff, 0x98, 0x9b, 0xa2, 0xff, 0x83, 0x8b, 0x96, 0xff, 0x68, 0x75, 0x84, 0xff, 0x39, 0x46, 0x58, 0xff, 0x21, 0x2d, 0x41, 0xff, 0x3e, 0x4f, 0x62, 0xff, 0x32, 0x4c, 0x60, 0xff, 0x2f, 0x4e, 0x65, 0xff, 0x57, 0x7a, 0x93, 0xff, 0x67, 0x8b, 0xa5, 0xff, 0x66, 0x8a, 0xa2, 0xff, 0x8d, 0xb3, 0xc8, 0xff, 0x97, 0xbc, 0xcf, 0xff, 0x86, 0xab, 0xc1, 0xff, 0x60, 0x83, 0x9d, 0xff, 0x61, 0x86, 0x9e, 0xff, 0x62, 0x88, 0x9e, 0xff, 0x32, 0x58, 0x6d, 0xff, 0x41, 0x65, 0x7c, 0xff, 0x3b, 0x62, 0x7a, 0xff, 0x55, 0x7f, 0x96, 0xff, 0x47, 0x76, 0x8d, 0xff, 0x59, 0x83, 0x96, 0xff, 0x91, 0xaa, 0xb8, 0xff, 0x96, 0xac, 0xb4, 0xff, 0x5a, 0x77, 0x7d, 0xff, 0x6e, 0x94, 0x9d, 0xff, 0x71, 0x8d, 0x95, 0xff, 0xae, 0xb8, 0xba, 0xff, 0xcd, 0xca, 0xcb, 0xff, 0xca, 0xc5, 0xc5, 0xff, 0xc4, 0xc4, 0xc4, 0xff, 0xc8, 0xc7, 0xc7, 0xff, 0xc8, 0xc5, 0xc7, 0xff, 0xc5, 0xc6, 0xc6, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc6, 0xc6, 0xc6, 0x51, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x09, 0x12, 0xce, 0x8e, 0x99, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb, 0xeb, 0xe7, 0xff, 0xe5, 0xe7, 0xe5, 0xff, 0xe7, 0xe8, 0xe6, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xce, 0xcd, 0xcc, 0xff, 0xd6, 0xd2, 0xd1, 0xff, 0xe0, 0xdb, 0xda, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xcc, 0xc8, 0xc7, 0xff, 0xde, 0xda, 0xd9, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xd2, 0xcf, 0xcf, 0xff, 0xd1, 0xcf, 0xcf, 0xff, 0xd8, 0xd6, 0xd6, 0xff, 0xd7, 0xd5, 0xd5, 0xff, 0xd2, 0xd0, 0xd0, 0xff, 0xd0, 0xce, 0xce, 0xff, 0xd8, 0xd8, 0xd5, 0xff, 0xd2, 0xd2, 0xcd, 0xff, 0xcd, 0xca, 0xc8, 0xff, 0xcf, 0xca, 0xc9, 0xff, 0xd8, 0xd1, 0xd1, 0xff, 0xd8, 0xd1, 0xcf, 0xff, 0xd4, 0xd1, 0xd4, 0xff, 0xb2, 0xb1, 0xba, 0xff, 0x79, 0x7a, 0x87, 0xff, 0x77, 0x78, 0x88, 0xff, 0x83, 0x83, 0x96, 0xff, 0x8a, 0x88, 0x9a, 0xff, 0x81, 0x7f, 0x8d, 0xff, 0x9e, 0x9c, 0xa7, 0xff, 0xd7, 0xd4, 0xd9, 0xff, 0xe4, 0xe2, 0xe0, 0xff, 0xdd, 0xdc, 0xd8, 0xff, 0xdd, 0xde, 0xd8, 0xff, 0xe4, 0xe2, 0xe0, 0xff, 0xdf, 0xda, 0xd9, 0xff, 0xdf, 0xdb, 0xda, 0xff, 0xe4, 0xe0, 0xdf, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xe0, 0xdc, 0xdb, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xce, 0xca, 0xc9, 0xff, 0xdd, 0xd9, 0xd8, 0xff, 0xdf, 0xdb, 0xda, 0xff, 0xce, 0xc9, 0xc9, 0xff, 0xda, 0xd5, 0xd6, 0xff, 0xe8, 0xe3, 0xe4, 0xff, 0xcf, 0xca, 0xcb, 0xff, 0xcf, 0xca, 0xcb, 0xff, 0xdd, 0xd8, 0xd9, 0xff, 0xce, 0xc9, 0xc9, 0xff, 0xd0, 0xca, 0xc9, 0xff, 0xd8, 0xd3, 0xd2, 0xff, 0xca, 0xc5, 0xc5, 0xff, 0xc8, 0xc4, 0xc5, 0xff, 0xd4, 0xcf, 0xd0, 0xff, 0xc1, 0xc0, 0xc4, 0xff, 0xba, 0xba, 0xbf, 0xff, 0xb5, 0xb5, 0xb9, 0xff, 0xa8, 0xaa, 0xaf, 0xff, 0xa0, 0xa6, 0xac, 0xff, 0x91, 0x9a, 0xa7, 0xff, 0x64, 0x6d, 0x7c, 0xff, 0x29, 0x33, 0x42, 0xff, 0x26, 0x35, 0x46, 0xff, 0x31, 0x45, 0x5a, 0xff, 0x2f, 0x4b, 0x63, 0xff, 0x4e, 0x6f, 0x87, 0xff, 0x6b, 0x8f, 0xa8, 0xff, 0x6c, 0x90, 0xa9, 0xff, 0x83, 0xa8, 0xbd, 0xff, 0x95, 0xba, 0xcd, 0xff, 0x91, 0xb6, 0xca, 0xff, 0x73, 0x97, 0xb0, 0xff, 0x66, 0x8c, 0xa4, 0xff, 0x5d, 0x86, 0x9b, 0xff, 0x33, 0x58, 0x6e, 0xff, 0x4c, 0x70, 0x87, 0xff, 0x4c, 0x71, 0x8a, 0xff, 0x4a, 0x72, 0x8a, 0xff, 0x46, 0x76, 0x8c, 0xff, 0x45, 0x75, 0x88, 0xff, 0x85, 0xa2, 0xb0, 0xff, 0xa7, 0xb7, 0xc0, 0xff, 0x83, 0x95, 0x9d, 0xff, 0x55, 0x79, 0x82, 0xff, 0x63, 0x8b, 0x92, 0xff, 0x7e, 0x93, 0x97, 0xff, 0xca, 0xcc, 0xce, 0xff, 0xca, 0xc6, 0xc7, 0xff, 0xc5, 0xc4, 0xc4, 0xff, 0xc9, 0xc5, 0xc6, 0xff, 0xca, 0xc6, 0xc7, 0xff, 0xc5, 0xc6, 0xc6, 0xff, 0xc6, 0xc6, 0xc6, 0xce, 0xff, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0x28, 0x33, 0x46, 0x5d, 0x6a, 0x75, 0xff, 0xf2, 0xf2, 0xf1, 0xff, 0xf7, 0xf7, 0xf3, 0xff, 0xe9, 0xea, 0xe8, 0xff, 0xe8, 0xea, 0xe8, 0xff, 0xeb, 0xea, 0xe9, 0xff, 0xe3, 0xe0, 0xdf, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xe4, 0xe0, 0xdf, 0xff, 0xd4, 0xd0, 0xcf, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xdc, 0xd8, 0xd7, 0xff, 0xd5, 0xd2, 0xd2, 0xff, 0xd6, 0xd4, 0xd5, 0xff, 0xd8, 0xd6, 0xd6, 0xff, 0xd0, 0xce, 0xce, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xd6, 0xd5, 0xd4, 0xff, 0xd3, 0xcf, 0xd1, 0xff, 0xd2, 0xce, 0xd2, 0xff, 0xd6, 0xd0, 0xd3, 0xff, 0xd1, 0xcb, 0xce, 0xff, 0xd2, 0xcc, 0xcf, 0xff, 0xd0, 0xc8, 0xcc, 0xff, 0xce, 0xc8, 0xc9, 0xff, 0xbe, 0xbc, 0xbe, 0xff, 0x8d, 0x8e, 0x97, 0xff, 0x76, 0x76, 0x84, 0xff, 0x83, 0x83, 0x94, 0xff, 0x87, 0x85, 0x98, 0xff, 0x7f, 0x7f, 0x8e, 0xff, 0xa2, 0xa3, 0xad, 0xff, 0xd8, 0xd7, 0xd8, 0xff, 0xde, 0xdd, 0xd9, 0xff, 0xe0, 0xde, 0xda, 0xff, 0xdd, 0xdd, 0xdb, 0xff, 0xe1, 0xde, 0xde, 0xff, 0xdf, 0xdb, 0xd9, 0xff, 0xdf, 0xdb, 0xda, 0xff, 0xe3, 0xdf, 0xde, 0xff, 0xdc, 0xd8, 0xd7, 0xff, 0xd4, 0xd0, 0xcf, 0xff, 0xdc, 0xd8, 0xd7, 0xff, 0xe2, 0xde, 0xdd, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xcf, 0xcb, 0xca, 0xff, 0xdb, 0xd7, 0xd6, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd1, 0xcd, 0xcd, 0xff, 0xdc, 0xd7, 0xd7, 0xff, 0xde, 0xd9, 0xda, 0xff, 0xd0, 0xcb, 0xcc, 0xff, 0xd7, 0xd2, 0xd3, 0xff, 0xe0, 0xdb, 0xdc, 0xff, 0xcf, 0xcb, 0xcc, 0xff, 0xd0, 0xcd, 0xcc, 0xff, 0xd9, 0xd5, 0xd3, 0xff, 0xcf, 0xca, 0xc9, 0xff, 0xca, 0xc4, 0xc5, 0xff, 0xcf, 0xc8, 0xcc, 0xff, 0xc4, 0xc0, 0xc5, 0xff, 0xbd, 0xbd, 0xbf, 0xff, 0xc9, 0xc6, 0xc8, 0xff, 0xbd, 0xbd, 0xbe, 0xff, 0xb2, 0xb6, 0xbb, 0xff, 0xc3, 0xc8, 0xd0, 0xff, 0xa7, 0xad, 0xb9, 0xff, 0x6e, 0x76, 0x82, 0xff, 0x41, 0x4e, 0x5c, 0xff, 0x42, 0x55, 0x68, 0xff, 0x3a, 0x55, 0x6b, 0xff, 0x40, 0x5f, 0x78, 0xff, 0x63, 0x85, 0x9f, 0xff, 0x75, 0x99, 0xb2, 0xff, 0x7e, 0xa3, 0xb9, 0xff, 0x92, 0xb7, 0xca, 0xff, 0x8e, 0xb3, 0xc9, 0xff, 0x7c, 0x9f, 0xb9, 0xff, 0x67, 0x8d, 0xa5, 0xff, 0x62, 0x8c, 0xa1, 0xff, 0x3a, 0x60, 0x76, 0xff, 0x54, 0x77, 0x8e, 0xff, 0x42, 0x67, 0x7f, 0xff, 0x40, 0x68, 0x7f, 0xff, 0x4f, 0x79, 0x91, 0xff, 0x36, 0x61, 0x77, 0xff, 0x77, 0x9d, 0xa9, 0xff, 0x95, 0xab, 0xb4, 0xff, 0xab, 0xba, 0xc1, 0xff, 0x63, 0x82, 0x8a, 0xff, 0x53, 0x7e, 0x84, 0xff, 0x70, 0x8c, 0x91, 0xff, 0xaa, 0xb0, 0xb4, 0xff, 0xd1, 0xcd, 0xce, 0xff, 0xc6, 0xc6, 0xc6, 0xff, 0xc6, 0xc5, 0xc6, 0xff, 0xc9, 0xc7, 0xc7, 0xff, 0xc5, 0xc6, 0xc6, 0xff, 0xc5, 0xc5, 0xc5, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x31, 0x42, 0xb4, 0xd0, 0xd1, 0xd8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe8, 0xe9, 0xe7, 0xff, 0xe5, 0xe7, 0xe4, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xe6, 0xe3, 0xe2, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xec, 0xea, 0xe9, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd4, 0xd3, 0xd2, 0xff, 0xe4, 0xe0, 0xdf, 0xff, 0xe3, 0xde, 0xdd, 0xff, 0xd6, 0xd2, 0xd1, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xe3, 0xdf, 0xde, 0xff, 0xda, 0xd5, 0xd4, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xd8, 0xd6, 0xd7, 0xff, 0xd4, 0xd2, 0xd2, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xd1, 0xcf, 0xcf, 0xff, 0xd5, 0xd3, 0xd3, 0xff, 0xd4, 0xd0, 0xd0, 0xff, 0xd6, 0xd1, 0xd2, 0xff, 0xd4, 0xcf, 0xd1, 0xff, 0xd8, 0xd4, 0xd5, 0xff, 0xd2, 0xd1, 0xd0, 0xff, 0xca, 0xca, 0xc8, 0xff, 0xc7, 0xc5, 0xc0, 0xff, 0xd0, 0xce, 0xcb, 0xff, 0xa4, 0xa6, 0xab, 0xff, 0x68, 0x69, 0x77, 0xff, 0x76, 0x76, 0x87, 0xff, 0x81, 0x7f, 0x90, 0xff, 0x70, 0x75, 0x87, 0xff, 0x8c, 0x94, 0x9d, 0xff, 0xcc, 0xcf, 0xce, 0xff, 0xd5, 0xd5, 0xd0, 0xff, 0xdb, 0xda, 0xd8, 0xff, 0xdd, 0xdb, 0xdf, 0xff, 0xe1, 0xdc, 0xde, 0xff, 0xde, 0xda, 0xd8, 0xff, 0xdf, 0xda, 0xd9, 0xff, 0xe2, 0xde, 0xdd, 0xff, 0xdb, 0xd7, 0xd6, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xe2, 0xde, 0xdd, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd1, 0xcd, 0xcc, 0xff, 0xdd, 0xd9, 0xd8, 0xff, 0xda, 0xd7, 0xd5, 0xff, 0xd0, 0xcb, 0xcb, 0xff, 0xd7, 0xd1, 0xd3, 0xff, 0xd8, 0xd2, 0xd4, 0xff, 0xce, 0xc9, 0xca, 0xff, 0xd3, 0xce, 0xcf, 0xff, 0xdb, 0xd5, 0xd6, 0xff, 0xd2, 0xce, 0xd1, 0xff, 0xcd, 0xcc, 0xcd, 0xff, 0xd4, 0xd2, 0xd0, 0xff, 0xd1, 0xcf, 0xcd, 0xff, 0xc6, 0xc3, 0xc2, 0xff, 0xca, 0xc3, 0xca, 0xff, 0xc7, 0xc3, 0xc9, 0xff, 0xc0, 0xbf, 0xbf, 0xff, 0xc6, 0xc4, 0xc3, 0xff, 0xc1, 0xbf, 0xbf, 0xff, 0xab, 0xac, 0xaf, 0xff, 0xbc, 0xc1, 0xc5, 0xff, 0xa8, 0xad, 0xb5, 0xff, 0x8e, 0x91, 0x9d, 0xff, 0x6d, 0x79, 0x86, 0xff, 0x4c, 0x60, 0x71, 0xff, 0x44, 0x5f, 0x75, 0xff, 0x34, 0x54, 0x6b, 0xff, 0x47, 0x68, 0x82, 0xff, 0x77, 0x9c, 0xb5, 0xff, 0x7c, 0xa1, 0xb6, 0xff, 0x8c, 0xb1, 0xc4, 0xff, 0x82, 0xa7, 0xbc, 0xff, 0x82, 0xa4, 0xbe, 0xff, 0x6a, 0x91, 0xa9, 0xff, 0x66, 0x92, 0xa7, 0xff, 0x43, 0x69, 0x7f, 0xff, 0x48, 0x6c, 0x83, 0xff, 0x47, 0x6a, 0x82, 0xff, 0x43, 0x6b, 0x82, 0xff, 0x54, 0x77, 0x91, 0xff, 0x36, 0x5b, 0x74, 0xff, 0x4a, 0x7b, 0x86, 0xff, 0x85, 0xa5, 0xa9, 0xff, 0xac, 0xb9, 0xbf, 0xff, 0x7f, 0x9c, 0xa3, 0xff, 0x43, 0x6e, 0x73, 0xff, 0x82, 0x9e, 0xa4, 0xff, 0x9b, 0x9e, 0xa3, 0xff, 0xc2, 0xbe, 0xbf, 0xff, 0xca, 0xcb, 0xcb, 0xff, 0xc0, 0xc5, 0xc4, 0xff, 0xc6, 0xc7, 0xc6, 0xff, 0xc6, 0xc6, 0xc6, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x0d, 0x27, 0xa3, 0xac, 0xb0, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xe7, 0xe8, 0xe7, 0xff, 0xe6, 0xe6, 0xe5, 0xff, 0xec, 0xec, 0xeb, 0xff, 0xe4, 0xe3, 0xe1, 0xff, 0xe4, 0xe1, 0xe0, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xea, 0xe8, 0xe7, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xea, 0xe8, 0xe7, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xe0, 0xdd, 0xdc, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xe5, 0xe2, 0xe1, 0xff, 0xdb, 0xd8, 0xd7, 0xff, 0xd8, 0xd5, 0xd4, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xdb, 0xda, 0xd9, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd7, 0xd5, 0xd5, 0xff, 0xc8, 0xc6, 0xc5, 0xff, 0xd1, 0xcf, 0xcf, 0xff, 0xd4, 0xd1, 0xd0, 0xff, 0xd7, 0xd1, 0xd1, 0xff, 0xd1, 0xce, 0xcd, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd4, 0xd3, 0xd1, 0xff, 0xc7, 0xc8, 0xc5, 0xff, 0xc5, 0xc3, 0xbf, 0xff, 0xcb, 0xc8, 0xc7, 0xff, 0xa6, 0xa8, 0xad, 0xff, 0x61, 0x62, 0x6f, 0xff, 0x65, 0x66, 0x75, 0xff, 0x6a, 0x69, 0x7a, 0xff, 0x5b, 0x61, 0x6f, 0xff, 0x7c, 0x84, 0x8d, 0xff, 0xa8, 0xac, 0xb0, 0xff, 0xc4, 0xc3, 0xc2, 0xff, 0xd9, 0xd5, 0xd3, 0xff, 0xe3, 0xdd, 0xde, 0xff, 0xe0, 0xdd, 0xdd, 0xff, 0xdc, 0xd9, 0xd8, 0xff, 0xe0, 0xdd, 0xdc, 0xff, 0xdf, 0xdc, 0xdb, 0xff, 0xdb, 0xd8, 0xd7, 0xff, 0xd6, 0xd3, 0xd2, 0xff, 0xde, 0xdb, 0xda, 0xff, 0xdf, 0xdb, 0xda, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xce, 0xca, 0xc9, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xdd, 0xd8, 0xd8, 0xff, 0xdc, 0xd8, 0xd7, 0xff, 0xcd, 0xc9, 0xc8, 0xff, 0xcb, 0xc7, 0xc7, 0xff, 0xdd, 0xd9, 0xd8, 0xff, 0xd2, 0xce, 0xd0, 0xff, 0xcf, 0xca, 0xcd, 0xff, 0xd7, 0xd4, 0xd4, 0xff, 0xd6, 0xd3, 0xd3, 0xff, 0xc6, 0xc2, 0xc3, 0xff, 0xc9, 0xc3, 0xc6, 0xff, 0xc6, 0xc1, 0xc3, 0xff, 0xbb, 0xb8, 0xba, 0xff, 0xc1, 0xbe, 0xbf, 0xff, 0xbe, 0xbd, 0xbe, 0xff, 0xac, 0xac, 0xaf, 0xff, 0xb3, 0xb6, 0xba, 0xff, 0xaf, 0xb2, 0xb8, 0xff, 0x95, 0x99, 0xa2, 0xff, 0x7d, 0x84, 0x8e, 0xff, 0x43, 0x52, 0x61, 0xff, 0x3a, 0x52, 0x66, 0xff, 0x33, 0x51, 0x6a, 0xff, 0x31, 0x51, 0x6c, 0xff, 0x66, 0x89, 0xa1, 0xff, 0x7a, 0x9f, 0xb6, 0xff, 0x88, 0xaf, 0xc5, 0xff, 0x85, 0xac, 0xc2, 0xff, 0x7a, 0x9e, 0xb7, 0xff, 0x7b, 0xa1, 0xb9, 0xff, 0x6e, 0x96, 0xad, 0xff, 0x49, 0x6f, 0x86, 0xff, 0x41, 0x67, 0x7e, 0xff, 0x54, 0x7a, 0x92, 0xff, 0x3a, 0x61, 0x79, 0xff, 0x44, 0x68, 0x81, 0xff, 0x49, 0x6f, 0x85, 0xff, 0x39, 0x67, 0x78, 0xff, 0x5f, 0x85, 0x8f, 0xff, 0xa6, 0xbc, 0xc4, 0xff, 0xb2, 0xbd, 0xc2, 0xff, 0x66, 0x86, 0x8b, 0xff, 0x53, 0x7c, 0x84, 0xff, 0x96, 0xa0, 0xa5, 0xff, 0xa1, 0xa2, 0xa3, 0xff, 0xc9, 0xc9, 0xc7, 0xff, 0xca, 0xc7, 0xca, 0xff, 0xc7, 0xc3, 0xc6, 0xfa, 0xc5, 0xc5, 0xc5, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8a, 0x94, 0x94, 0x88, 0xff, 0xff, 0xff, 0xff, 0xe8, 0xe9, 0xe9, 0xff, 0xe8, 0xe6, 0xe7, 0xff, 0xea, 0xec, 0xea, 0xff, 0xe3, 0xe4, 0xe2, 0xff, 0xde, 0xdb, 0xda, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xee, 0xec, 0xeb, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xe2, 0xe1, 0xe0, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xdb, 0xda, 0xd9, 0xff, 0xe7, 0xe6, 0xe5, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xc9, 0xc7, 0xc6, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd8, 0xd3, 0xd4, 0xff, 0xd6, 0xd1, 0xd2, 0xff, 0xcf, 0xcc, 0xca, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xc9, 0xc7, 0xc6, 0xff, 0xc4, 0xc4, 0xc3, 0xff, 0xc7, 0xc6, 0xc5, 0xff, 0xb0, 0xaf, 0xaf, 0xff, 0x94, 0x95, 0x9c, 0xff, 0x5d, 0x5f, 0x6b, 0xff, 0x56, 0x57, 0x65, 0xff, 0x5c, 0x5b, 0x6b, 0xff, 0x50, 0x56, 0x63, 0xff, 0x6f, 0x76, 0x80, 0xff, 0x84, 0x86, 0x8e, 0xff, 0x9c, 0x9a, 0x9e, 0xff, 0xc9, 0xc6, 0xc6, 0xff, 0xe3, 0xde, 0xdb, 0xff, 0xdd, 0xda, 0xd8, 0xff, 0xde, 0xdd, 0xdc, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xdf, 0xde, 0xdd, 0xff, 0xdb, 0xda, 0xd9, 0xff, 0xd2, 0xd1, 0xd0, 0xff, 0xdd, 0xda, 0xd9, 0xff, 0xdf, 0xdb, 0xda, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xcf, 0xcb, 0xca, 0xff, 0xdc, 0xd8, 0xd7, 0xff, 0xde, 0xd9, 0xd9, 0xff, 0xd3, 0xcf, 0xcd, 0xff, 0xd4, 0xd0, 0xce, 0xff, 0xdc, 0xd8, 0xd7, 0xff, 0xd4, 0xd0, 0xcf, 0xff, 0xcf, 0xcb, 0xca, 0xff, 0xdd, 0xd9, 0xd8, 0xff, 0xd3, 0xcf, 0xcf, 0xff, 0xcd, 0xc8, 0xc9, 0xff, 0xd5, 0xd0, 0xd1, 0xff, 0xdd, 0xd7, 0xd9, 0xff, 0xcc, 0xc7, 0xc8, 0xff, 0xd0, 0xcb, 0xcb, 0xff, 0xd6, 0xd1, 0xd1, 0xff, 0xc3, 0xbf, 0xc1, 0xff, 0xbf, 0xbd, 0xc0, 0xff, 0xc0, 0xbf, 0xc1, 0xff, 0xb3, 0xb3, 0xb7, 0xff, 0xba, 0xbb, 0xbf, 0xff, 0xb1, 0xb4, 0xb9, 0xff, 0xa0, 0xa4, 0xad, 0xff, 0x8b, 0x91, 0x9a, 0xff, 0x58, 0x65, 0x71, 0xff, 0x3c, 0x51, 0x64, 0xff, 0x35, 0x50, 0x6b, 0xff, 0x2e, 0x4d, 0x67, 0xff, 0x4b, 0x6c, 0x82, 0xff, 0x73, 0x98, 0xaf, 0xff, 0x7b, 0xa4, 0xbb, 0xff, 0x8a, 0xb2, 0xc8, 0xff, 0x7a, 0xa0, 0xb6, 0xff, 0x7b, 0xa0, 0xb6, 0xff, 0x7e, 0xa4, 0xbb, 0xff, 0x55, 0x7b, 0x92, 0xff, 0x41, 0x68, 0x7f, 0xff, 0x59, 0x7f, 0x96, 0xff, 0x3a, 0x60, 0x77, 0xff, 0x35, 0x5c, 0x72, 0xff, 0x4a, 0x71, 0x85, 0xff, 0x50, 0x79, 0x8d, 0xff, 0x47, 0x6d, 0x7e, 0xff, 0x86, 0xa4, 0xad, 0xff, 0xc1, 0xc6, 0xc9, 0xff, 0x93, 0xab, 0xad, 0xff, 0x4a, 0x76, 0x7d, 0xff, 0x87, 0x96, 0x9d, 0xff, 0x9a, 0xa1, 0xa5, 0xff, 0xb0, 0xb4, 0xb5, 0xff, 0xd0, 0xc9, 0xca, 0xff, 0xcc, 0xc5, 0xc5, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0x71, 0x71, 0x09, 0xff, 0xff, 0xff, 0xdb, 0xe5, 0xe6, 0xe6, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xe6, 0xe7, 0xe6, 0xff, 0xe6, 0xe7, 0xe5, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xdc, 0xd8, 0xd7, 0xff, 0xd8, 0xd3, 0xd2, 0xff, 0xcf, 0xcc, 0xcb, 0xff, 0xcd, 0xcb, 0xcb, 0xff, 0xc7, 0xc6, 0xc5, 0xff, 0xc5, 0xc4, 0xc4, 0xff, 0xab, 0xa9, 0xa9, 0xff, 0x99, 0x99, 0x9b, 0xff, 0x79, 0x7b, 0x84, 0xff, 0x4e, 0x50, 0x5c, 0xff, 0x45, 0x47, 0x54, 0xff, 0x4d, 0x4d, 0x5b, 0xff, 0x41, 0x47, 0x55, 0xff, 0x62, 0x6b, 0x77, 0xff, 0x73, 0x77, 0x83, 0xff, 0x57, 0x5b, 0x63, 0xff, 0x86, 0x88, 0x8d, 0xff, 0xe6, 0xe5, 0xe7, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xdc, 0xd9, 0xd8, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xe2, 0xdf, 0xde, 0xff, 0xe4, 0xe0, 0xdf, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xe3, 0xdf, 0xde, 0xff, 0xe2, 0xde, 0xdd, 0xff, 0xcf, 0xcb, 0xca, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xdf, 0xdb, 0xda, 0xff, 0xd1, 0xcd, 0xcc, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xd5, 0xd0, 0xd0, 0xff, 0xcd, 0xc8, 0xc9, 0xff, 0xd6, 0xd1, 0xd2, 0xff, 0xd9, 0xd4, 0xd5, 0xff, 0xd2, 0xcd, 0xce, 0xff, 0xd4, 0xcf, 0xd0, 0xff, 0xda, 0xd5, 0xd6, 0xff, 0xc7, 0xc3, 0xc5, 0xff, 0xc2, 0xbf, 0xc1, 0xff, 0xc1, 0xc0, 0xc2, 0xff, 0xb0, 0xb0, 0xb4, 0xff, 0xb8, 0xba, 0xbd, 0xff, 0xb6, 0xb8, 0xbf, 0xff, 0xa0, 0xa4, 0xae, 0xff, 0x88, 0x90, 0x99, 0xff, 0x67, 0x74, 0x7f, 0xff, 0x43, 0x57, 0x69, 0xff, 0x34, 0x4f, 0x67, 0xff, 0x2d, 0x4b, 0x65, 0xff, 0x38, 0x58, 0x6f, 0xff, 0x6a, 0x8c, 0xa3, 0xff, 0x75, 0x9c, 0xb2, 0xff, 0x81, 0xa8, 0xbe, 0xff, 0x89, 0xaf, 0xc5, 0xff, 0x78, 0x9d, 0xb3, 0xff, 0x82, 0xa7, 0xbd, 0xff, 0x5d, 0x82, 0x98, 0xff, 0x47, 0x6c, 0x82, 0xff, 0x5e, 0x83, 0x99, 0xff, 0x4d, 0x72, 0x88, 0xff, 0x35, 0x5a, 0x70, 0xff, 0x43, 0x6a, 0x80, 0xff, 0x63, 0x8e, 0xa1, 0xff, 0x4e, 0x77, 0x88, 0xff, 0x63, 0x81, 0x8c, 0xff, 0xa8, 0xb4, 0xbb, 0xff, 0x9c, 0xb0, 0xb1, 0xff, 0x80, 0x9d, 0x9d, 0xff, 0x7c, 0x89, 0x95, 0xff, 0x78, 0x84, 0x92, 0xff, 0x85, 0x92, 0x9a, 0xff, 0xc1, 0xc2, 0xbe, 0xdb, 0xc6, 0xc6, 0xc6, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x41, 0xee, 0xf0, 0xef, 0xfe, 0xe6, 0xe6, 0xe6, 0xff, 0xe7, 0xe8, 0xe6, 0xff, 0xe8, 0xe8, 0xe6, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xc7, 0xc5, 0xc4, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xea, 0xe8, 0xe7, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd6, 0xd5, 0xd4, 0xff, 0xe0, 0xdd, 0xdc, 0xff, 0xd2, 0xce, 0xcc, 0xff, 0xd6, 0xd2, 0xd2, 0xff, 0xcc, 0xca, 0xca, 0xff, 0xca, 0xc7, 0xc8, 0xff, 0xa9, 0xa7, 0xa7, 0xff, 0x7d, 0x7d, 0x7f, 0xff, 0x6f, 0x71, 0x76, 0xff, 0x64, 0x68, 0x71, 0xff, 0x3d, 0x41, 0x4c, 0xff, 0x33, 0x36, 0x41, 0xff, 0x3e, 0x3f, 0x48, 0xff, 0x32, 0x37, 0x43, 0xff, 0x4f, 0x57, 0x65, 0xff, 0x65, 0x6c, 0x7c, 0xff, 0x50, 0x57, 0x66, 0xff, 0x42, 0x44, 0x4d, 0xff, 0x83, 0x83, 0x89, 0xff, 0xda, 0xd8, 0xd9, 0xff, 0xe1, 0xdf, 0xdd, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xe7, 0xe4, 0xe3, 0xff, 0xe4, 0xdf, 0xde, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xdf, 0xdb, 0xda, 0xff, 0xe4, 0xe0, 0xdf, 0xff, 0xdd, 0xd9, 0xd8, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xdf, 0xdb, 0xda, 0xff, 0xd0, 0xcc, 0xcb, 0xff, 0xd1, 0xcd, 0xcc, 0xff, 0xdd, 0xd9, 0xd7, 0xff, 0xd5, 0xd0, 0xd0, 0xff, 0xd1, 0xcb, 0xcd, 0xff, 0xd9, 0xd4, 0xd5, 0xff, 0xd8, 0xd3, 0xd4, 0xff, 0xd0, 0xcb, 0xcc, 0xff, 0xd2, 0xcd, 0xce, 0xff, 0xd7, 0xd2, 0xd3, 0xff, 0xc2, 0xbe, 0xc0, 0xff, 0xc4, 0xc1, 0xc3, 0xff, 0xc6, 0xc5, 0xc7, 0xff, 0xb5, 0xb6, 0xb9, 0xff, 0xb1, 0xb3, 0xb6, 0xff, 0xa3, 0xa6, 0xad, 0xff, 0x9d, 0xa1, 0xab, 0xff, 0x8f, 0x96, 0x9e, 0xff, 0x71, 0x7c, 0x88, 0xff, 0x45, 0x58, 0x68, 0xff, 0x26, 0x40, 0x55, 0xff, 0x2c, 0x47, 0x5e, 0xff, 0x35, 0x4f, 0x67, 0xff, 0x5a, 0x7b, 0x92, 0xff, 0x75, 0x9a, 0xb0, 0xff, 0x7c, 0xa2, 0xb8, 0xff, 0x7f, 0xa4, 0xba, 0xff, 0x7a, 0x9f, 0xb5, 0xff, 0x7d, 0xa2, 0xb8, 0xff, 0x6d, 0x92, 0xa8, 0xff, 0x4e, 0x73, 0x89, 0xff, 0x60, 0x85, 0x9b, 0xff, 0x55, 0x7a, 0x90, 0xff, 0x3c, 0x5f, 0x75, 0xff, 0x2b, 0x50, 0x67, 0xff, 0x61, 0x8b, 0xa0, 0xff, 0x61, 0x8b, 0x9c, 0xff, 0x5e, 0x7e, 0x8c, 0xff, 0x86, 0x97, 0xa3, 0xff, 0x8c, 0x9f, 0xa2, 0xff, 0x9b, 0xaf, 0xaf, 0xff, 0xa0, 0xac, 0xb8, 0xff, 0x74, 0x82, 0x92, 0xff, 0x67, 0x79, 0x81, 0xfe, 0xac, 0xb4, 0xb0, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x8f, 0xe9, 0xe8, 0xe8, 0xff, 0xe6, 0xe7, 0xe6, 0xff, 0xe4, 0xe4, 0xe2, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xef, 0xed, 0xec, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xca, 0xc8, 0xc7, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd8, 0xd5, 0xd3, 0xff, 0xcf, 0xc9, 0xc9, 0xff, 0xd7, 0xd3, 0xd4, 0xff, 0xd0, 0xce, 0xcf, 0xff, 0xa6, 0xa2, 0xa6, 0xff, 0x64, 0x61, 0x65, 0xff, 0x4a, 0x4b, 0x51, 0xff, 0x43, 0x46, 0x4e, 0xff, 0x44, 0x48, 0x52, 0xff, 0x2c, 0x31, 0x3b, 0xff, 0x30, 0x33, 0x3d, 0xff, 0x36, 0x39, 0x3f, 0xff, 0x2d, 0x2f, 0x37, 0xff, 0x40, 0x44, 0x51, 0xff, 0x56, 0x5e, 0x6e, 0xff, 0x50, 0x56, 0x67, 0xff, 0x33, 0x36, 0x42, 0xff, 0x34, 0x33, 0x38, 0xff, 0xcc, 0xca, 0xca, 0xff, 0xe8, 0xe6, 0xe4, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xe6, 0xe3, 0xe2, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xe4, 0xe0, 0xdf, 0xff, 0xe2, 0xde, 0xdd, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xd2, 0xce, 0xcd, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xde, 0xdb, 0xd9, 0xff, 0xd5, 0xd1, 0xd1, 0xff, 0xd5, 0xd0, 0xd1, 0xff, 0xd6, 0xd1, 0xd2, 0xff, 0xd6, 0xd1, 0xd2, 0xff, 0xce, 0xc9, 0xca, 0xff, 0xd0, 0xcb, 0xcc, 0xff, 0xd3, 0xce, 0xcf, 0xff, 0xc1, 0xbd, 0xbf, 0xff, 0xcb, 0xc9, 0xcb, 0xff, 0xc6, 0xc5, 0xc7, 0xff, 0xb2, 0xb2, 0xb6, 0xff, 0xbb, 0xbc, 0xc0, 0xff, 0x9b, 0xa0, 0xa6, 0xff, 0x96, 0x9b, 0xa5, 0xff, 0x83, 0x8a, 0x93, 0xff, 0x73, 0x7e, 0x88, 0xff, 0x48, 0x59, 0x69, 0xff, 0x1d, 0x36, 0x4a, 0xff, 0x26, 0x3e, 0x54, 0xff, 0x3c, 0x52, 0x69, 0xff, 0x48, 0x65, 0x7b, 0xff, 0x61, 0x84, 0x98, 0xff, 0x81, 0xa5, 0xba, 0xff, 0x7b, 0xa0, 0xb5, 0xff, 0x7b, 0x9f, 0xb3, 0xff, 0x76, 0x9a, 0xae, 0xff, 0x7d, 0xa0, 0xb5, 0xff, 0x69, 0x8d, 0xa1, 0xff, 0x63, 0x86, 0x9a, 0xff, 0x57, 0x7b, 0x8e, 0xff, 0x32, 0x54, 0x6a, 0xff, 0x34, 0x57, 0x6e, 0xff, 0x44, 0x6e, 0x84, 0xff, 0x5f, 0x89, 0x9c, 0xff, 0x71, 0x94, 0xa3, 0xff, 0x76, 0x8d, 0x9a, 0xff, 0x69, 0x81, 0x88, 0xff, 0x7e, 0x96, 0x9b, 0xff, 0xbd, 0xc4, 0xcd, 0xff, 0xa6, 0xad, 0xb7, 0xff, 0x6d, 0x7b, 0x83, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xfe, 0xfe, 0x07, 0xf4, 0xf5, 0xf5, 0xd1, 0xe7, 0xe8, 0xe8, 0xff, 0xe2, 0xe1, 0xe0, 0xff, 0xe7, 0xe4, 0xe3, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xc7, 0xc5, 0xc4, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xd0, 0xcb, 0xca, 0xff, 0xd2, 0xcf, 0xcf, 0xff, 0xdb, 0xd8, 0xda, 0xff, 0x88, 0x82, 0x89, 0xff, 0x21, 0x1e, 0x25, 0xff, 0x30, 0x31, 0x39, 0xff, 0x3f, 0x42, 0x4c, 0xff, 0x30, 0x35, 0x41, 0xff, 0x1b, 0x21, 0x2c, 0xff, 0x28, 0x2e, 0x35, 0xff, 0x2d, 0x31, 0x35, 0xff, 0x2a, 0x29, 0x2f, 0xff, 0x33, 0x33, 0x3e, 0xff, 0x4b, 0x51, 0x61, 0xff, 0x41, 0x49, 0x5b, 0xff, 0x2f, 0x31, 0x40, 0xff, 0x8c, 0x87, 0x8d, 0xff, 0xe9, 0xe6, 0xe5, 0xff, 0xdd, 0xdc, 0xdb, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xda, 0xd9, 0xd8, 0xff, 0xe3, 0xdf, 0xde, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xe4, 0xe0, 0xdf, 0xff, 0xe4, 0xe0, 0xdf, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xd4, 0xd0, 0xce, 0xff, 0xdd, 0xd9, 0xd7, 0xff, 0xd6, 0xd2, 0xd1, 0xff, 0xd2, 0xce, 0xcd, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd7, 0xd4, 0xd2, 0xff, 0xd1, 0xcd, 0xcd, 0xff, 0xd3, 0xce, 0xcf, 0xff, 0xdb, 0xd6, 0xd7, 0xff, 0xd4, 0xcf, 0xd0, 0xff, 0xce, 0xc9, 0xca, 0xff, 0xd4, 0xcf, 0xd0, 0xff, 0xce, 0xc9, 0xc9, 0xff, 0xc4, 0xc0, 0xc2, 0xff, 0xcc, 0xca, 0xcc, 0xff, 0xb9, 0xb8, 0xb9, 0xff, 0xb3, 0xb4, 0xb7, 0xff, 0xbb, 0xbc, 0xbf, 0xff, 0x9a, 0x9e, 0xa5, 0xff, 0x95, 0x9b, 0xa5, 0xff, 0x7d, 0x84, 0x8d, 0xff, 0x73, 0x7e, 0x88, 0xff, 0x3c, 0x4e, 0x5c, 0xff, 0x2a, 0x42, 0x54, 0xff, 0x25, 0x3b, 0x51, 0xff, 0x2f, 0x43, 0x5b, 0xff, 0x3f, 0x5a, 0x6f, 0xff, 0x52, 0x76, 0x89, 0xff, 0x78, 0x9e, 0xb2, 0xff, 0x82, 0xa9, 0xbd, 0xff, 0x83, 0xa8, 0xbc, 0xff, 0x6f, 0x91, 0xa5, 0xff, 0x6f, 0x92, 0xa6, 0xff, 0x86, 0xa9, 0xbc, 0xff, 0x6c, 0x8f, 0xa2, 0xff, 0x52, 0x75, 0x88, 0xff, 0x34, 0x54, 0x6a, 0xff, 0x43, 0x65, 0x7c, 0xff, 0x3a, 0x65, 0x7b, 0xff, 0x3b, 0x67, 0x7d, 0xff, 0x6d, 0x91, 0xa2, 0xff, 0x84, 0x9b, 0xa7, 0xff, 0x6a, 0x88, 0x95, 0xff, 0x58, 0x7a, 0x85, 0xff, 0x9f, 0xa1, 0xa6, 0xff, 0xdc, 0xdb, 0xd9, 0xd1, 0x9f, 0x9f, 0x9f, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x2a, 0xeb, 0xed, 0xe6, 0xf3, 0xdf, 0xe4, 0xdf, 0xff, 0xe6, 0xe2, 0xe2, 0xff, 0xdf, 0xdc, 0xdc, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xed, 0xeb, 0xea, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xdb, 0xd8, 0xd7, 0xff, 0xce, 0xcb, 0xcb, 0xff, 0xc8, 0xc6, 0xc6, 0xff, 0xdd, 0xdb, 0xdc, 0xff, 0xc9, 0xc5, 0xc8, 0xff, 0x47, 0x43, 0x47, 0xff, 0x2f, 0x31, 0x38, 0xff, 0x48, 0x4b, 0x56, 0xff, 0x20, 0x24, 0x2d, 0xff, 0x13, 0x17, 0x1d, 0xff, 0x1e, 0x23, 0x28, 0xff, 0x22, 0x26, 0x28, 0xff, 0x23, 0x26, 0x28, 0xff, 0x24, 0x26, 0x2b, 0xff, 0x35, 0x38, 0x41, 0xff, 0x30, 0x31, 0x3c, 0xff, 0x69, 0x68, 0x70, 0xff, 0xe6, 0xe1, 0xe3, 0xff, 0xe3, 0xe0, 0xdf, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xe0, 0xdd, 0xdc, 0xff, 0xda, 0xd5, 0xd4, 0xff, 0xdb, 0xd7, 0xd6, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xdb, 0xd7, 0xd6, 0xff, 0xe3, 0xdf, 0xdf, 0xff, 0xd4, 0xcf, 0xcf, 0xff, 0xca, 0xc5, 0xc5, 0xff, 0xda, 0xd5, 0xd6, 0xff, 0xd8, 0xd3, 0xd4, 0xff, 0xd2, 0xcc, 0xcd, 0xff, 0xd6, 0xd1, 0xd2, 0xff, 0xd7, 0xd2, 0xd3, 0xff, 0xcf, 0xca, 0xcb, 0xff, 0xcc, 0xc7, 0xc8, 0xff, 0xd4, 0xcf, 0xd0, 0xff, 0xc7, 0xc2, 0xc3, 0xff, 0xc4, 0xc1, 0xc2, 0xff, 0xc8, 0xc6, 0xc8, 0xff, 0xb2, 0xb1, 0xb5, 0xff, 0xbb, 0xba, 0xbf, 0xff, 0xa9, 0xaa, 0xaf, 0xff, 0x99, 0x9b, 0xa4, 0xff, 0x96, 0x9b, 0xa4, 0xff, 0x7b, 0x81, 0x88, 0xff, 0x70, 0x78, 0x82, 0xff, 0x33, 0x43, 0x50, 0xff, 0x26, 0x3b, 0x4c, 0xff, 0x31, 0x48, 0x5b, 0xff, 0x1d, 0x34, 0x48, 0xff, 0x35, 0x4f, 0x63, 0xff, 0x4d, 0x6c, 0x81, 0xff, 0x67, 0x89, 0x9d, 0xff, 0x7c, 0xa0, 0xb3, 0xff, 0x87, 0xa9, 0xbd, 0xff, 0x81, 0xa2, 0xb6, 0xff, 0x6d, 0x90, 0xa6, 0xff, 0x77, 0x9b, 0xb2, 0xff, 0x88, 0xac, 0xc2, 0xff, 0x5c, 0x80, 0x94, 0xff, 0x31, 0x54, 0x68, 0xff, 0x48, 0x6c, 0x82, 0xff, 0x4e, 0x74, 0x89, 0xff, 0x47, 0x6e, 0x81, 0xff, 0x44, 0x67, 0x78, 0xff, 0x58, 0x76, 0x87, 0xff, 0x66, 0x88, 0x97, 0xff, 0x7c, 0x9a, 0xa4, 0xff, 0x88, 0x95, 0x98, 0xf4, 0x9d, 0xa3, 0x9d, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf3, 0xf6, 0xf0, 0x55, 0xdb, 0xe4, 0xde, 0xfe, 0xe1, 0xdd, 0xdd, 0xff, 0xea, 0xe6, 0xe5, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd1, 0xd0, 0xcf, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xd1, 0xcf, 0xcf, 0xff, 0xe0, 0xdf, 0xde, 0xff, 0xa3, 0xa0, 0xa0, 0xff, 0x35, 0x36, 0x3c, 0xff, 0x22, 0x26, 0x2e, 0xff, 0x07, 0x0a, 0x0f, 0xff, 0x0e, 0x11, 0x13, 0xff, 0x11, 0x13, 0x17, 0xff, 0x13, 0x16, 0x19, 0xff, 0x16, 0x1c, 0x1b, 0xff, 0x16, 0x1b, 0x1b, 0xff, 0x08, 0x08, 0x0c, 0xff, 0x41, 0x3f, 0x44, 0xff, 0xd1, 0xcc, 0xcf, 0xff, 0xe7, 0xe2, 0xe1, 0xff, 0xd5, 0xd2, 0xd0, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xdf, 0xde, 0xdd, 0xff, 0xd9, 0xd6, 0xd5, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xdc, 0xd8, 0xd7, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xdd, 0xd9, 0xd8, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xd9, 0xd4, 0xd4, 0xff, 0xdc, 0xd7, 0xd9, 0xff, 0xd5, 0xcf, 0xd1, 0xff, 0xcf, 0xc9, 0xcb, 0xff, 0xd7, 0xd1, 0xd3, 0xff, 0xd7, 0xd2, 0xd3, 0xff, 0xd1, 0xcc, 0xcd, 0xff, 0xd8, 0xd3, 0xd4, 0xff, 0xd9, 0xd4, 0xd5, 0xff, 0xcd, 0xc8, 0xc9, 0xff, 0xce, 0xc9, 0xca, 0xff, 0xd0, 0xcb, 0xcc, 0xff, 0xbc, 0xb7, 0xb8, 0xff, 0xc8, 0xc5, 0xc5, 0xff, 0xc6, 0xc4, 0xc6, 0xff, 0xba, 0xb9, 0xbe, 0xff, 0xbb, 0xba, 0xc0, 0xff, 0x98, 0x99, 0xa0, 0xff, 0x9a, 0x9b, 0xa5, 0xff, 0x87, 0x8c, 0x94, 0xff, 0x84, 0x8a, 0x90, 0xff, 0x6a, 0x72, 0x7b, 0xff, 0x3a, 0x49, 0x56, 0xff, 0x12, 0x24, 0x37, 0xff, 0x2c, 0x43, 0x54, 0xff, 0x2d, 0x45, 0x55, 0xff, 0x2a, 0x43, 0x58, 0xff, 0x3e, 0x58, 0x6e, 0xff, 0x58, 0x76, 0x8b, 0xff, 0x68, 0x88, 0x9b, 0xff, 0x8a, 0xab, 0xbd, 0xff, 0x82, 0xa3, 0xb6, 0xff, 0x7a, 0x9c, 0xb4, 0xff, 0x68, 0x8a, 0xa4, 0xff, 0x80, 0xa3, 0xba, 0xff, 0x81, 0xa5, 0xb8, 0xff, 0x38, 0x5d, 0x6f, 0xff, 0x4f, 0x73, 0x87, 0xff, 0x5f, 0x81, 0x95, 0xff, 0x5f, 0x81, 0x94, 0xff, 0x4c, 0x6e, 0x80, 0xff, 0x37, 0x5a, 0x70, 0xff, 0x3f, 0x5f, 0x6e, 0xff, 0x74, 0x8c, 0x91, 0xff, 0xa0, 0xb1, 0xb7, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe0, 0xe3, 0xe0, 0x8d, 0xdf, 0xdb, 0xda, 0xff, 0xe5, 0xe2, 0xe1, 0xff, 0xd8, 0xd6, 0xd4, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xe2, 0xe0, 0xde, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xd9, 0xd7, 0xd7, 0xff, 0xd3, 0xd1, 0xd1, 0xff, 0xdb, 0xd8, 0xda, 0xff, 0x4b, 0x4b, 0x50, 0xff, 0x00, 0x00, 0x00, 0xff, 0x08, 0x0a, 0x0f, 0xff, 0x0b, 0x0e, 0x12, 0xff, 0x0a, 0x0d, 0x11, 0xff, 0x0a, 0x0d, 0x11, 0xff, 0x0d, 0x10, 0x12, 0xff, 0x0a, 0x0d, 0x0d, 0xff, 0x00, 0x00, 0x00, 0xff, 0x7e, 0x7c, 0x80, 0xff, 0xf2, 0xef, 0xf0, 0xff, 0xd6, 0xd1, 0xd2, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xd5, 0xd4, 0xd2, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd8, 0xd7, 0xd6, 0xff, 0xd5, 0xd2, 0xd1, 0xff, 0xd2, 0xce, 0xcd, 0xff, 0xdb, 0xd7, 0xd6, 0xff, 0xdf, 0xdb, 0xda, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xcf, 0xcb, 0xca, 0xff, 0xd8, 0xd3, 0xd4, 0xff, 0xde, 0xd9, 0xda, 0xff, 0xce, 0xc9, 0xca, 0xff, 0xd4, 0xcf, 0xd0, 0xff, 0xd6, 0xd1, 0xd2, 0xff, 0xcd, 0xc8, 0xc9, 0xff, 0xcc, 0xc7, 0xc8, 0xff, 0xdb, 0xd6, 0xd7, 0xff, 0xd9, 0xd4, 0xd5, 0xff, 0xce, 0xc9, 0xca, 0xff, 0xd4, 0xcf, 0xd0, 0xff, 0xc7, 0xc2, 0xc3, 0xff, 0xc2, 0xbd, 0xbe, 0xff, 0xcd, 0xca, 0xca, 0xff, 0xba, 0xb8, 0xba, 0xff, 0xc2, 0xc1, 0xc5, 0xff, 0xb5, 0xb4, 0xba, 0xff, 0xa3, 0xa4, 0xaa, 0xff, 0x93, 0x96, 0x9f, 0xff, 0x7c, 0x81, 0x89, 0xff, 0x90, 0x96, 0x9d, 0xff, 0x6e, 0x76, 0x7f, 0xff, 0x40, 0x4f, 0x5c, 0xff, 0x0d, 0x20, 0x32, 0xff, 0x1d, 0x34, 0x45, 0xff, 0x3d, 0x53, 0x63, 0xff, 0x32, 0x49, 0x5d, 0xff, 0x30, 0x49, 0x5e, 0xff, 0x4d, 0x69, 0x7e, 0xff, 0x48, 0x69, 0x7b, 0xff, 0x8c, 0xae, 0xbf, 0xff, 0x76, 0x97, 0xab, 0xff, 0x65, 0x85, 0x9b, 0xff, 0x83, 0xa2, 0xba, 0xff, 0x6c, 0x8d, 0xa2, 0xff, 0x8b, 0xaf, 0xc1, 0xff, 0x6f, 0x92, 0xa5, 0xff, 0x4e, 0x71, 0x85, 0xff, 0x55, 0x78, 0x8c, 0xff, 0x67, 0x89, 0x9d, 0xff, 0x61, 0x82, 0x95, 0xff, 0x44, 0x66, 0x7b, 0xff, 0x4c, 0x6a, 0x7a, 0xff, 0x73, 0x89, 0x92, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xe5, 0xe2, 0xdf, 0xb1, 0xdc, 0xda, 0xd8, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xca, 0xc8, 0xc8, 0xff, 0xdb, 0xd9, 0xd9, 0xff, 0xce, 0xcc, 0xcd, 0xff, 0xdf, 0xdc, 0xde, 0xff, 0xa1, 0x9f, 0xa0, 0xff, 0x0d, 0x0b, 0x0d, 0xff, 0x0a, 0x0a, 0x0e, 0xff, 0x0d, 0x0e, 0x12, 0xff, 0x0d, 0x0d, 0x11, 0xff, 0x0b, 0x0d, 0x11, 0xff, 0x0f, 0x10, 0x13, 0xff, 0x00, 0x00, 0x00, 0xff, 0x11, 0x10, 0x11, 0xff, 0xb9, 0xb7, 0xb7, 0xff, 0xe9, 0xe7, 0xe6, 0xff, 0xd9, 0xd7, 0xd7, 0xff, 0xd6, 0xd3, 0xd3, 0xff, 0xe1, 0xdf, 0xdd, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xe3, 0xdf, 0xde, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xcd, 0xc9, 0xc8, 0xff, 0xd1, 0xcd, 0xcb, 0xff, 0xdf, 0xda, 0xda, 0xff, 0xd9, 0xd4, 0xd5, 0xff, 0xcf, 0xca, 0xcb, 0xff, 0xdb, 0xd6, 0xd7, 0xff, 0xdb, 0xd6, 0xd7, 0xff, 0xc7, 0xc2, 0xc3, 0xff, 0xd5, 0xd0, 0xd1, 0xff, 0xde, 0xd9, 0xda, 0xff, 0xcf, 0xca, 0xcb, 0xff, 0xd0, 0xcb, 0xcc, 0xff, 0xdc, 0xd7, 0xd8, 0xff, 0xc4, 0xbe, 0xc0, 0xff, 0xc7, 0xc2, 0xc2, 0xff, 0xc0, 0xbd, 0xbd, 0xff, 0xb5, 0xb3, 0xb5, 0xff, 0xbf, 0xbe, 0xc2, 0xff, 0xa2, 0xa2, 0xa7, 0xff, 0xa0, 0xa1, 0xa8, 0xff, 0x82, 0x84, 0x8e, 0xff, 0x85, 0x8a, 0x92, 0xff, 0x79, 0x7f, 0x85, 0xff, 0x59, 0x61, 0x6a, 0xff, 0x44, 0x52, 0x5f, 0xff, 0x15, 0x29, 0x3b, 0xff, 0x15, 0x2a, 0x3b, 0xff, 0x2b, 0x40, 0x4f, 0xff, 0x41, 0x57, 0x6a, 0xff, 0x33, 0x4b, 0x5f, 0xff, 0x40, 0x5a, 0x6e, 0xff, 0x3c, 0x59, 0x6a, 0xff, 0x79, 0x99, 0xab, 0xff, 0x81, 0xa2, 0xb6, 0xff, 0x58, 0x76, 0x8a, 0xff, 0x88, 0xa5, 0xb9, 0xff, 0x82, 0xa2, 0xb6, 0xff, 0x81, 0xa3, 0xb6, 0xff, 0x85, 0xa8, 0xbc, 0xff, 0x6c, 0x8f, 0xa3, 0xff, 0x4e, 0x6f, 0x84, 0xff, 0x4f, 0x70, 0x83, 0xff, 0x60, 0x81, 0x94, 0xff, 0x4a, 0x6b, 0x7f, 0xff, 0x66, 0x83, 0x94, 0xb1, 0xaa, 0xaa, 0xaa, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe5, 0xe5, 0xe5, 0x0a, 0xdc, 0xdc, 0xda, 0xc8, 0xe4, 0xe1, 0xe1, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xf1, 0xef, 0xee, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xe7, 0xe5, 0xe4, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd1, 0xcf, 0xcf, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xd9, 0xd7, 0xd7, 0xff, 0xca, 0xc7, 0xc8, 0xff, 0xca, 0xc8, 0xca, 0xff, 0xd9, 0xd5, 0xd6, 0xff, 0x5a, 0x54, 0x56, 0xff, 0x00, 0x00, 0x00, 0xff, 0x10, 0x0f, 0x12, 0xff, 0x0d, 0x0e, 0x12, 0xff, 0x0c, 0x0e, 0x12, 0xff, 0x06, 0x04, 0x08, 0xff, 0x0e, 0x09, 0x0d, 0xff, 0x9e, 0x9c, 0x9c, 0xff, 0xde, 0xdc, 0xda, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xdc, 0xda, 0xdb, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xd3, 0xd0, 0xcf, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xe1, 0xdd, 0xdc, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xcf, 0xcb, 0xca, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xdb, 0xd6, 0xd6, 0xff, 0xca, 0xc5, 0xc6, 0xff, 0xd1, 0xcc, 0xcd, 0xff, 0xd7, 0xd2, 0xd3, 0xff, 0xd2, 0xcd, 0xce, 0xff, 0xcc, 0xc7, 0xc8, 0xff, 0xd6, 0xd1, 0xd2, 0xff, 0xd1, 0xcc, 0xcd, 0xff, 0xbf, 0xba, 0xbb, 0xff, 0xcd, 0xc8, 0xc9, 0xff, 0xd0, 0xcb, 0xcc, 0xff, 0xc0, 0xbb, 0xbc, 0xff, 0xcc, 0xc7, 0xc8, 0xff, 0xb5, 0xb2, 0xb2, 0xff, 0xb6, 0xb4, 0xb7, 0xff, 0xa3, 0xa2, 0xa6, 0xff, 0xa3, 0xa2, 0xa8, 0xff, 0x8d, 0x8d, 0x93, 0xff, 0x7b, 0x7d, 0x86, 0xff, 0x6c, 0x71, 0x78, 0xff, 0x57, 0x5d, 0x64, 0xff, 0x5a, 0x62, 0x6b, 0xff, 0x41, 0x4f, 0x5c, 0xff, 0x14, 0x28, 0x3a, 0xff, 0x17, 0x2d, 0x3e, 0xff, 0x1a, 0x2c, 0x3b, 0xff, 0x39, 0x4d, 0x60, 0xff, 0x4e, 0x65, 0x7a, 0xff, 0x32, 0x4b, 0x5f, 0xff, 0x3e, 0x5a, 0x6a, 0xff, 0x69, 0x89, 0x9b, 0xff, 0x7b, 0x9d, 0xb1, 0xff, 0x65, 0x80, 0x92, 0xff, 0x80, 0x99, 0xa9, 0xff, 0x7d, 0x99, 0xab, 0xff, 0x74, 0x96, 0xa9, 0xff, 0x7e, 0xa1, 0xb5, 0xff, 0x7d, 0x9e, 0xb2, 0xff, 0x64, 0x85, 0x99, 0xff, 0x3e, 0x5e, 0x72, 0xff, 0x59, 0x78, 0x8b, 0xff, 0x55, 0x76, 0x8a, 0xc8, 0x4c, 0x66, 0x7f, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xda, 0xda, 0xda, 0x15, 0xe2, 0xde, 0xdd, 0xd7, 0xdb, 0xd9, 0xd8, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xe8, 0xe6, 0xe5, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xcd, 0xcb, 0xcb, 0xff, 0xd5, 0xd3, 0xd3, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xc9, 0xc6, 0xc9, 0xff, 0xd9, 0xd4, 0xd5, 0xff, 0xa0, 0x98, 0x99, 0xff, 0x15, 0x10, 0x11, 0xff, 0x08, 0x06, 0x08, 0xff, 0x0f, 0x0f, 0x13, 0xff, 0x09, 0x0b, 0x10, 0xff, 0x00, 0x00, 0x00, 0xff, 0x67, 0x60, 0x63, 0xff, 0xf2, 0xee, 0xed, 0xff, 0xd9, 0xd8, 0xd3, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd5, 0xd2, 0xd4, 0xff, 0xdc, 0xda, 0xda, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xcc, 0xc9, 0xc8, 0xff, 0xdd, 0xd8, 0xd7, 0xff, 0xdb, 0xd7, 0xd6, 0xff, 0xce, 0xca, 0xc9, 0xff, 0xcf, 0xcb, 0xca, 0xff, 0xde, 0xda, 0xd9, 0xff, 0xd8, 0xd3, 0xd3, 0xff, 0xcb, 0xc5, 0xc7, 0xff, 0xdc, 0xd7, 0xd8, 0xff, 0xe2, 0xdc, 0xdd, 0xff, 0xcb, 0xc6, 0xc7, 0xff, 0xc7, 0xc2, 0xc3, 0xff, 0xce, 0xc9, 0xca, 0xff, 0xc0, 0xbc, 0xbd, 0xff, 0xc2, 0xbd, 0xbe, 0xff, 0xc4, 0xbf, 0xc0, 0xff, 0xbc, 0xb7, 0xb8, 0xff, 0xc6, 0xc1, 0xc1, 0xff, 0xc1, 0xbc, 0xbd, 0xff, 0xb6, 0xb3, 0xb3, 0xff, 0xba, 0xb7, 0xbb, 0xff, 0xa2, 0xa1, 0xa5, 0xff, 0x99, 0x98, 0x9d, 0xff, 0x80, 0x81, 0x87, 0xff, 0x7e, 0x7f, 0x8a, 0xff, 0x55, 0x58, 0x61, 0xff, 0x73, 0x79, 0x81, 0xff, 0x64, 0x6c, 0x76, 0xff, 0x25, 0x34, 0x41, 0xff, 0x1d, 0x31, 0x44, 0xff, 0x1e, 0x33, 0x43, 0xff, 0x1d, 0x2e, 0x3c, 0xff, 0x2c, 0x3f, 0x4f, 0xff, 0x50, 0x65, 0x7a, 0xff, 0x33, 0x4b, 0x5e, 0xff, 0x3c, 0x55, 0x64, 0xff, 0x56, 0x76, 0x88, 0xff, 0x66, 0x87, 0x9c, 0xff, 0x5f, 0x78, 0x89, 0xff, 0x7b, 0x92, 0xa1, 0xff, 0x6c, 0x87, 0x98, 0xff, 0x67, 0x8b, 0x9f, 0xff, 0x7a, 0x9c, 0xb1, 0xff, 0x7a, 0x99, 0xae, 0xff, 0x5e, 0x7e, 0x93, 0xff, 0x4e, 0x6d, 0x81, 0xff, 0x4c, 0x6c, 0x7f, 0xd8, 0x55, 0x6d, 0x85, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe3, 0xda, 0xda, 0x1c, 0xe4, 0xe2, 0xe0, 0xdc, 0xd9, 0xd7, 0xd6, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd1, 0xcf, 0xcf, 0xff, 0xce, 0xcc, 0xcc, 0xff, 0xcd, 0xcb, 0xcb, 0xff, 0xca, 0xc8, 0xc8, 0xff, 0xc6, 0xc4, 0xc4, 0xff, 0xca, 0xc5, 0xc7, 0xff, 0xc9, 0xc4, 0xc6, 0xff, 0x55, 0x52, 0x53, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x0f, 0x14, 0xff, 0x00, 0x00, 0x00, 0xff, 0x23, 0x21, 0x23, 0xff, 0xce, 0xc9, 0xc9, 0xff, 0xe4, 0xe1, 0xdf, 0xff, 0xd8, 0xd6, 0xd3, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd4, 0xd1, 0xd1, 0xff, 0xde, 0xdc, 0xdc, 0xff, 0xdb, 0xd9, 0xd7, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xcf, 0xcc, 0xcb, 0xff, 0xdf, 0xdc, 0xdb, 0xff, 0xd7, 0xd4, 0xd3, 0xff, 0xc8, 0xc5, 0xc4, 0xff, 0xd3, 0xd0, 0xcf, 0xff, 0xde, 0xdb, 0xda, 0xff, 0xcb, 0xc7, 0xc7, 0xff, 0xcb, 0xc8, 0xc8, 0xff, 0xde, 0xda, 0xdb, 0xff, 0xe1, 0xdd, 0xde, 0xff, 0xce, 0xca, 0xcb, 0xff, 0xce, 0xcc, 0xcc, 0xff, 0xce, 0xca, 0xcb, 0xff, 0xc5, 0xc0, 0xc1, 0xff, 0xc2, 0xbd, 0xbe, 0xff, 0xb7, 0xb2, 0xb3, 0xff, 0xb8, 0xb3, 0xb5, 0xff, 0xc3, 0xbe, 0xc1, 0xff, 0xb1, 0xac, 0xad, 0xff, 0xbf, 0xbc, 0xbb, 0xff, 0xaf, 0xae, 0xaf, 0xff, 0xa9, 0xa8, 0xab, 0xff, 0x8d, 0x8c, 0x92, 0xff, 0x79, 0x79, 0x81, 0xff, 0x6a, 0x6f, 0x76, 0xff, 0x6f, 0x78, 0x7d, 0xff, 0x7d, 0x84, 0x89, 0xff, 0x5a, 0x62, 0x6a, 0xff, 0x20, 0x2d, 0x3b, 0xff, 0x23, 0x38, 0x4a, 0xff, 0x20, 0x32, 0x44, 0xff, 0x2e, 0x3e, 0x4f, 0xff, 0x2b, 0x3e, 0x50, 0xff, 0x3c, 0x52, 0x65, 0xff, 0x3b, 0x53, 0x65, 0xff, 0x42, 0x5d, 0x6d, 0xff, 0x3f, 0x5b, 0x6c, 0xff, 0x45, 0x60, 0x73, 0xff, 0x5f, 0x77, 0x89, 0xff, 0x63, 0x7a, 0x8c, 0xff, 0x63, 0x7d, 0x8d, 0xff, 0x69, 0x85, 0x95, 0xff, 0x74, 0x95, 0xa7, 0xff, 0x7a, 0xa0, 0xb3, 0xff, 0x65, 0x86, 0x9b, 0xff, 0x4d, 0x6c, 0x83, 0xdd, 0x3f, 0x64, 0x76, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe7, 0xe7, 0xe0, 0x21, 0xd7, 0xd5, 0xd4, 0xde, 0xdc, 0xda, 0xd9, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd3, 0xd1, 0xcf, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xcb, 0xca, 0xc7, 0xff, 0xcd, 0xc9, 0xc9, 0xff, 0xd6, 0xd1, 0xd4, 0xff, 0x91, 0x8e, 0x90, 0xff, 0x0c, 0x0a, 0x0d, 0xff, 0x01, 0x01, 0x04, 0xff, 0x00, 0x00, 0x00, 0xff, 0x8a, 0x89, 0x89, 0xff, 0xe8, 0xe6, 0xe4, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xda, 0xd7, 0xd7, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xdd, 0xdb, 0xd9, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xdb, 0xda, 0xd9, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xd2, 0xd1, 0xd0, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xc5, 0xc4, 0xc4, 0xff, 0xcd, 0xcb, 0xcb, 0xff, 0xd5, 0xd4, 0xd4, 0xff, 0xc6, 0xc4, 0xc4, 0xff, 0xca, 0xc9, 0xc9, 0xff, 0xd8, 0xd8, 0xd7, 0xff, 0xc9, 0xc4, 0xc5, 0xff, 0xcb, 0xc5, 0xc6, 0xff, 0xc9, 0xc4, 0xc5, 0xff, 0xb9, 0xb3, 0xb5, 0xff, 0xbe, 0xba, 0xbc, 0xff, 0xb2, 0xb0, 0xb3, 0xff, 0xb3, 0xb1, 0xb1, 0xff, 0xc4, 0xc2, 0xc1, 0xff, 0xab, 0xa9, 0xa9, 0xff, 0xa7, 0xa7, 0xaa, 0xff, 0x8e, 0x8f, 0x95, 0xff, 0x76, 0x76, 0x7f, 0xff, 0x70, 0x79, 0x7c, 0xff, 0x7f, 0x8b, 0x8c, 0xff, 0x20, 0x29, 0x2e, 0xff, 0x20, 0x28, 0x32, 0xff, 0x2c, 0x39, 0x47, 0xff, 0x29, 0x3d, 0x4e, 0xff, 0x26, 0x3a, 0x4d, 0xff, 0x34, 0x45, 0x59, 0xff, 0x29, 0x3d, 0x50, 0xff, 0x32, 0x4a, 0x5c, 0xff, 0x23, 0x3d, 0x4e, 0xff, 0x55, 0x71, 0x81, 0xff, 0x39, 0x52, 0x63, 0xff, 0x1c, 0x32, 0x44, 0xff, 0x5f, 0x77, 0x89, 0xff, 0x54, 0x6c, 0x7f, 0xff, 0x57, 0x70, 0x80, 0xff, 0x66, 0x7c, 0x8b, 0xff, 0x5f, 0x7f, 0x8e, 0xff, 0x75, 0x9e, 0xae, 0xff, 0x7e, 0x9f, 0xb5, 0xde, 0x55, 0x73, 0x8b, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe1, 0xe1, 0xe1, 0x1a, 0xe2, 0xe1, 0xe0, 0xd7, 0xe7, 0xe5, 0xe4, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xe6, 0xe4, 0xe3, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xce, 0xcd, 0xcb, 0xff, 0xcd, 0xc8, 0xc8, 0xff, 0xc9, 0xc3, 0xc4, 0xff, 0xc0, 0xbb, 0xbd, 0xff, 0x6c, 0x67, 0x69, 0xff, 0x00, 0x00, 0x00, 0xff, 0x43, 0x3e, 0x45, 0xff, 0xdb, 0xd8, 0xda, 0xff, 0xd6, 0xd5, 0xd3, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xc8, 0xc6, 0xc5, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xce, 0xcc, 0xcc, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xc5, 0xc3, 0xc3, 0xff, 0xd0, 0xce, 0xce, 0xff, 0xcb, 0xca, 0xca, 0xff, 0xc6, 0xc2, 0xc2, 0xff, 0xd1, 0xcb, 0xcc, 0xff, 0xbe, 0xb9, 0xba, 0xff, 0xc3, 0xbe, 0xc0, 0xff, 0xc9, 0xc6, 0xc8, 0xff, 0xaf, 0xab, 0xad, 0xff, 0xb6, 0xb4, 0xb4, 0xff, 0xa1, 0x9f, 0xa0, 0xff, 0xa0, 0xa0, 0xa3, 0xff, 0x93, 0x95, 0x99, 0xff, 0x7a, 0x7d, 0x82, 0xff, 0x7d, 0x7f, 0x85, 0xff, 0x97, 0x9e, 0xa2, 0xff, 0x3b, 0x45, 0x48, 0xff, 0x00, 0x00, 0x08, 0xff, 0x1b, 0x25, 0x31, 0xff, 0x2c, 0x3b, 0x4a, 0xff, 0x2b, 0x3f, 0x51, 0xff, 0x2d, 0x43, 0x55, 0xff, 0x30, 0x43, 0x55, 0xff, 0x2e, 0x42, 0x54, 0xff, 0x29, 0x40, 0x52, 0xff, 0x13, 0x2b, 0x3d, 0xff, 0x55, 0x6f, 0x80, 0xff, 0x44, 0x5c, 0x6d, 0xff, 0x11, 0x26, 0x38, 0xff, 0x43, 0x5a, 0x6b, 0xff, 0x56, 0x6e, 0x80, 0xff, 0x4d, 0x65, 0x77, 0xff, 0x5b, 0x74, 0x85, 0xff, 0x4e, 0x6d, 0x7c, 0xff, 0x6f, 0x91, 0xa3, 0xd7, 0x8d, 0xaa, 0xbc, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe7, 0xe7, 0xe7, 0x16, 0xe5, 0xe4, 0xe3, 0xca, 0xe4, 0xe2, 0xe1, 0xff, 0xe2, 0xe0, 0xdf, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xcc, 0xcb, 0xca, 0xff, 0xcc, 0xc7, 0xc7, 0xff, 0xca, 0xc3, 0xc4, 0xff, 0xd1, 0xcc, 0xcd, 0xff, 0xb0, 0xab, 0xac, 0xff, 0x48, 0x40, 0x44, 0xff, 0xb0, 0xa7, 0xac, 0xff, 0xdd, 0xda, 0xda, 0xff, 0xd5, 0xd4, 0xd2, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xca, 0xc8, 0xc8, 0xff, 0xca, 0xc8, 0xc9, 0xff, 0xc4, 0xc2, 0xc2, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xc2, 0xc1, 0xc1, 0xff, 0xcc, 0xc8, 0xc9, 0xff, 0xc9, 0xc3, 0xc4, 0xff, 0xb6, 0xb1, 0xb2, 0xff, 0xc8, 0xc5, 0xc6, 0xff, 0xbc, 0xba, 0xbc, 0xff, 0xb9, 0xb7, 0xb9, 0xff, 0xb1, 0xaf, 0xb2, 0xff, 0x96, 0x96, 0x9a, 0xff, 0x96, 0x97, 0x9b, 0xff, 0x7d, 0x80, 0x86, 0xff, 0x7b, 0x7f, 0x84, 0xff, 0x86, 0x8b, 0x90, 0xff, 0x9b, 0xa0, 0xa3, 0xff, 0x26, 0x2e, 0x32, 0xff, 0x03, 0x0c, 0x17, 0xff, 0x28, 0x34, 0x43, 0xff, 0x2e, 0x3f, 0x50, 0xff, 0x2a, 0x3f, 0x50, 0xff, 0x34, 0x4a, 0x5b, 0xff, 0x32, 0x47, 0x59, 0xff, 0x35, 0x4a, 0x5c, 0xff, 0x22, 0x39, 0x4b, 0xff, 0x23, 0x3b, 0x4d, 0xff, 0x45, 0x5e, 0x71, 0xff, 0x45, 0x5b, 0x6c, 0xff, 0x23, 0x35, 0x45, 0xff, 0x0f, 0x24, 0x35, 0xff, 0x42, 0x5a, 0x6c, 0xff, 0x58, 0x70, 0x82, 0xff, 0x53, 0x6c, 0x7f, 0xff, 0x4a, 0x66, 0x76, 0xca, 0x5c, 0x7f, 0x8b, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe7, 0xe7, 0xe7, 0x0b, 0xe0, 0xdd, 0xdd, 0xaf, 0xe6, 0xe4, 0xe3, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xeb, 0xe9, 0xe8, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xc9, 0xc7, 0xc7, 0xff, 0xd2, 0xce, 0xce, 0xff, 0x8c, 0x86, 0x87, 0xff, 0x71, 0x6c, 0x6e, 0xff, 0xcc, 0xc5, 0xc8, 0xff, 0xd2, 0xcf, 0xcf, 0xff, 0xd7, 0xd6, 0xd5, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xc8, 0xc6, 0xc6, 0xff, 0xc8, 0xc6, 0xc6, 0xff, 0xd0, 0xce, 0xce, 0xff, 0xcd, 0xcb, 0xcb, 0xff, 0xc8, 0xc7, 0xc6, 0xff, 0xc6, 0xc3, 0xc4, 0xff, 0xb5, 0xb1, 0xb2, 0xff, 0xb4, 0xb3, 0xb2, 0xff, 0xbc, 0xb9, 0xba, 0xff, 0xb0, 0xae, 0xb0, 0xff, 0xc6, 0xc5, 0xc6, 0xff, 0xaa, 0xaa, 0xae, 0xff, 0xa2, 0xa2, 0xaa, 0xff, 0x82, 0x85, 0x89, 0xff, 0x7a, 0x7f, 0x83, 0xff, 0x80, 0x85, 0x8b, 0xff, 0x8c, 0x95, 0x9a, 0xff, 0x8e, 0x94, 0x96, 0xff, 0x1b, 0x1e, 0x21, 0xff, 0x0e, 0x16, 0x22, 0xff, 0x28, 0x36, 0x48, 0xff, 0x2f, 0x42, 0x55, 0xff, 0x34, 0x4b, 0x5b, 0xff, 0x38, 0x4f, 0x60, 0xff, 0x35, 0x4c, 0x5e, 0xff, 0x32, 0x48, 0x5a, 0xff, 0x1a, 0x30, 0x42, 0xff, 0x41, 0x58, 0x6a, 0xff, 0x39, 0x51, 0x63, 0xff, 0x36, 0x4a, 0x5b, 0xff, 0x3b, 0x4b, 0x5a, 0xff, 0x03, 0x15, 0x25, 0xff, 0x0e, 0x1f, 0x30, 0xff, 0x57, 0x6f, 0x83, 0xff, 0x5f, 0x79, 0x8f, 0xb0, 0x45, 0x5c, 0x73, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x03, 0xe8, 0xe7, 0xe5, 0x8b, 0xe8, 0xe6, 0xe5, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd3, 0xd0, 0xd0, 0xff, 0xcb, 0xcc, 0xca, 0xff, 0xbe, 0xc3, 0xc0, 0xff, 0xb2, 0xb3, 0xb2, 0xff, 0x6d, 0x6b, 0x6c, 0xff, 0x7d, 0x7c, 0x7c, 0xff, 0xc8, 0xc3, 0xc5, 0xff, 0xd2, 0xce, 0xcf, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xca, 0xc8, 0xc7, 0xff, 0xcd, 0xcb, 0xcb, 0xff, 0xc9, 0xc7, 0xc7, 0xff, 0xcd, 0xcb, 0xcb, 0xff, 0xd6, 0xd4, 0xd4, 0xff, 0xca, 0xc8, 0xc8, 0xff, 0xc3, 0xc1, 0xc1, 0xff, 0xb9, 0xb7, 0xb7, 0xff, 0xbb, 0xb9, 0xb8, 0xff, 0xa9, 0xa8, 0xa9, 0xff, 0xa7, 0xa7, 0xa9, 0xff, 0xa9, 0xa7, 0xa8, 0xff, 0x9b, 0x9c, 0xa2, 0xff, 0x8f, 0x91, 0x9b, 0xff, 0x7d, 0x80, 0x85, 0xff, 0x86, 0x8a, 0x8f, 0xff, 0x8d, 0x95, 0x9a, 0xff, 0x84, 0x90, 0x95, 0xff, 0x24, 0x2a, 0x2b, 0xff, 0x00, 0x00, 0x02, 0xff, 0x20, 0x28, 0x35, 0xff, 0x31, 0x41, 0x54, 0xff, 0x2a, 0x40, 0x53, 0xff, 0x3a, 0x52, 0x61, 0xff, 0x36, 0x4e, 0x5e, 0xff, 0x39, 0x52, 0x64, 0xff, 0x29, 0x3f, 0x52, 0xff, 0x25, 0x39, 0x4c, 0xff, 0x52, 0x67, 0x79, 0xff, 0x31, 0x47, 0x5a, 0xff, 0x29, 0x3d, 0x4d, 0xff, 0x3f, 0x4f, 0x5e, 0xff, 0x20, 0x33, 0x43, 0xff, 0x00, 0x07, 0x18, 0xff, 0x26, 0x3e, 0x52, 0x8b, 0x55, 0xaa, 0xaa, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe1, 0xde, 0xde, 0x57, 0xde, 0xdc, 0xdb, 0xf3, 0xdf, 0xdd, 0xdc, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xe5, 0xe3, 0xe2, 0xff, 0xe1, 0xdf, 0xde, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xce, 0xcb, 0xca, 0xff, 0xc9, 0xc9, 0xc8, 0xff, 0xbe, 0xbf, 0xbf, 0xff, 0xb3, 0xb3, 0xb2, 0xff, 0x78, 0x77, 0x77, 0xff, 0x91, 0x90, 0x8f, 0xff, 0xcd, 0xca, 0xca, 0xff, 0xd0, 0xcd, 0xcd, 0xff, 0xd6, 0xd5, 0xd3, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xcd, 0xcb, 0xcb, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xcc, 0xca, 0xca, 0xff, 0xc7, 0xc5, 0xc5, 0xff, 0xc8, 0xc6, 0xc6, 0xff, 0xc7, 0xc5, 0xc4, 0xff, 0xc0, 0xbe, 0xbe, 0xff, 0xbf, 0xbd, 0xbd, 0xff, 0xbe, 0xbc, 0xbc, 0xff, 0xb6, 0xb4, 0xb5, 0xff, 0xb1, 0xaf, 0xb2, 0xff, 0x98, 0x95, 0x96, 0xff, 0x91, 0x91, 0x95, 0xff, 0x80, 0x84, 0x8a, 0xff, 0x77, 0x7c, 0x83, 0xff, 0x85, 0x8d, 0x93, 0xff, 0x88, 0x90, 0x94, 0xff, 0x36, 0x3d, 0x3d, 0xff, 0x00, 0x00, 0x00, 0xff, 0x08, 0x0e, 0x14, 0xff, 0x29, 0x37, 0x45, 0xff, 0x2e, 0x41, 0x57, 0xff, 0x30, 0x46, 0x5b, 0xff, 0x3d, 0x52, 0x61, 0xff, 0x36, 0x4b, 0x5c, 0xff, 0x3b, 0x52, 0x66, 0xff, 0x29, 0x43, 0x54, 0xff, 0x36, 0x50, 0x62, 0xff, 0x42, 0x5b, 0x6c, 0xff, 0x2f, 0x47, 0x58, 0xff, 0x2a, 0x3b, 0x4b, 0xff, 0x26, 0x33, 0x41, 0xff, 0x21, 0x33, 0x43, 0xf3, 0x1a, 0x2e, 0x3d, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe1, 0xe1, 0xdb, 0x2b, 0xe1, 0xdf, 0xdf, 0xd2, 0xe2, 0xe0, 0xdf, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xe4, 0xe2, 0xe1, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xce, 0xcc, 0xca, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xcb, 0xc8, 0xc9, 0xff, 0xbf, 0xbd, 0xbd, 0xff, 0x76, 0x74, 0x74, 0xff, 0x8e, 0x8c, 0x8c, 0xff, 0xcd, 0xcb, 0xcb, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd1, 0xcf, 0xcd, 0xff, 0xcf, 0xcd, 0xcb, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xd1, 0xcf, 0xcd, 0xff, 0xca, 0xc8, 0xc7, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xc7, 0xc5, 0xc5, 0xff, 0xbd, 0xbb, 0xbb, 0xff, 0xbe, 0xbd, 0xbc, 0xff, 0xbb, 0xb9, 0xba, 0xff, 0xb7, 0xb4, 0xb6, 0xff, 0xab, 0xa7, 0xa8, 0xff, 0x97, 0x97, 0x99, 0xff, 0x89, 0x8d, 0x90, 0xff, 0x83, 0x89, 0x8f, 0xff, 0x61, 0x6a, 0x70, 0xff, 0x23, 0x29, 0x2c, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x05, 0x04, 0xff, 0x13, 0x1f, 0x29, 0xff, 0x32, 0x43, 0x53, 0xff, 0x26, 0x3e, 0x53, 0xff, 0x3b, 0x53, 0x67, 0xff, 0x3d, 0x52, 0x61, 0xff, 0x3e, 0x51, 0x62, 0xff, 0x28, 0x3c, 0x51, 0xff, 0x36, 0x51, 0x62, 0xff, 0x4c, 0x68, 0x79, 0xff, 0x40, 0x5b, 0x6b, 0xff, 0x29, 0x41, 0x52, 0xff, 0x19, 0x29, 0x38, 0xff, 0x24, 0x30, 0x3d, 0xd2, 0x22, 0x34, 0x45, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdf, 0xdf, 0xdf, 0x08, 0xe7, 0xe4, 0xe4, 0x8f, 0xdc, 0xda, 0xd9, 0xfe, 0xd9, 0xd7, 0xd6, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xdf, 0xdd, 0xdc, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xcb, 0xc9, 0xca, 0xff, 0xc2, 0xc0, 0xc0, 0xff, 0x7e, 0x7c, 0x7c, 0xff, 0x9a, 0x98, 0x98, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xd4, 0xd2, 0xd2, 0xff, 0xd6, 0xd4, 0xd2, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xc7, 0xc5, 0xc4, 0xff, 0xc5, 0xc3, 0xc3, 0xff, 0xc3, 0xc1, 0xc0, 0xff, 0xb6, 0xb4, 0xb5, 0xff, 0xb0, 0xad, 0xb0, 0xff, 0xad, 0xaa, 0xab, 0xff, 0x9f, 0x9d, 0xa0, 0xff, 0x95, 0x95, 0x9a, 0xff, 0x74, 0x77, 0x7c, 0xff, 0x19, 0x1c, 0x21, 0xff, 0x00, 0x00, 0x00, 0xff, 0x09, 0x08, 0x07, 0xff, 0x04, 0x0a, 0x0b, 0xff, 0x24, 0x34, 0x3d, 0xff, 0x32, 0x45, 0x53, 0xff, 0x36, 0x4e, 0x61, 0xff, 0x36, 0x4f, 0x61, 0xff, 0x35, 0x4e, 0x5d, 0xff, 0x35, 0x4b, 0x5b, 0xff, 0x2c, 0x42, 0x55, 0xff, 0x53, 0x6a, 0x7d, 0xff, 0x56, 0x6f, 0x7f, 0xff, 0x38, 0x50, 0x5f, 0xff, 0x16, 0x2a, 0x3b, 0xfe, 0x15, 0x25, 0x33, 0x8f, 0x1f, 0x3f, 0x3f, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xdc, 0xd8, 0xd8, 0x42, 0xde, 0xdc, 0xda, 0xdb, 0xd9, 0xd7, 0xd6, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xe3, 0xe1, 0xe0, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xca, 0xc8, 0xc7, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xc7, 0xc5, 0xc5, 0xff, 0xc1, 0xbf, 0xbf, 0xff, 0x85, 0x83, 0x83, 0xff, 0xa4, 0xa2, 0xa2, 0xff, 0xd1, 0xcf, 0xcf, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xd4, 0xd2, 0xd0, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xe0, 0xde, 0xdd, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xca, 0xc8, 0xc7, 0xff, 0xc3, 0xc1, 0xc1, 0xff, 0xc7, 0xc5, 0xc5, 0xff, 0xc4, 0xc2, 0xc2, 0xff, 0xb9, 0xb7, 0xb8, 0xff, 0xb7, 0xb4, 0xb7, 0xff, 0xb4, 0xb1, 0xb2, 0xff, 0x9e, 0x9c, 0x9f, 0xff, 0x8a, 0x88, 0x8d, 0xff, 0x3f, 0x3e, 0x42, 0xff, 0x00, 0x00, 0x00, 0xff, 0x07, 0x08, 0x0c, 0xff, 0x04, 0x03, 0x05, 0xff, 0x0e, 0x18, 0x1f, 0xff, 0x2e, 0x42, 0x4d, 0xff, 0x2c, 0x40, 0x4e, 0xff, 0x34, 0x4b, 0x5d, 0xff, 0x30, 0x49, 0x5a, 0xff, 0x2e, 0x47, 0x56, 0xff, 0x3a, 0x53, 0x63, 0xff, 0x4b, 0x63, 0x76, 0xff, 0x5e, 0x74, 0x86, 0xff, 0x42, 0x57, 0x67, 0xff, 0x0c, 0x1e, 0x2e, 0xdb, 0x13, 0x26, 0x36, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe2, 0xe2, 0xe2, 0x09, 0xd9, 0xd7, 0xd7, 0x87, 0xd6, 0xd3, 0xd2, 0xf9, 0xd7, 0xd5, 0xd4, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xc7, 0xc5, 0xc5, 0xff, 0xcc, 0xca, 0xca, 0xff, 0xc8, 0xc6, 0xc6, 0xff, 0x89, 0x87, 0x87, 0xff, 0xa5, 0xa3, 0xa3, 0xff, 0xd1, 0xcf, 0xcf, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdc, 0xda, 0xd9, 0xff, 0xde, 0xdc, 0xdb, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xcb, 0xc9, 0xc7, 0xff, 0xc5, 0xc3, 0xc2, 0xff, 0xc6, 0xc4, 0xc4, 0xff, 0xc5, 0xc4, 0xc3, 0xff, 0xbd, 0xbb, 0xbc, 0xff, 0xb3, 0xb0, 0xb2, 0xff, 0xb4, 0xb1, 0xb3, 0xff, 0xa5, 0xa4, 0xa8, 0xff, 0x55, 0x55, 0x59, 0xff, 0x10, 0x0d, 0x0f, 0xff, 0x01, 0x01, 0x02, 0xff, 0x07, 0x09, 0x0d, 0xff, 0x05, 0x08, 0x0d, 0xff, 0x24, 0x2f, 0x3a, 0xff, 0x2c, 0x3f, 0x4d, 0xff, 0x30, 0x42, 0x4f, 0xff, 0x2a, 0x3e, 0x4d, 0xff, 0x2a, 0x40, 0x4f, 0xff, 0x3c, 0x53, 0x63, 0xff, 0x43, 0x5c, 0x6c, 0xff, 0x58, 0x72, 0x83, 0xff, 0x52, 0x68, 0x78, 0xf9, 0x12, 0x23, 0x34, 0x88, 0x00, 0x1c, 0x1c, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd5, 0xd5, 0xd5, 0x25, 0xd2, 0xd2, 0xd1, 0xb7, 0xd2, 0xd1, 0xd0, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xd0, 0xcd, 0xcd, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd4, 0xd1, 0xd0, 0xff, 0xd6, 0xd3, 0xd2, 0xff, 0xd5, 0xd2, 0xd1, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xda, 0xd8, 0xd7, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xc9, 0xc7, 0xc5, 0xff, 0xc8, 0xc6, 0xc6, 0xff, 0xca, 0xc9, 0xc8, 0xff, 0xc3, 0xc1, 0xc1, 0xff, 0x97, 0x95, 0x95, 0xff, 0xb8, 0xb6, 0xb6, 0xff, 0xd4, 0xd2, 0xd2, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xdc, 0xd9, 0xd9, 0xff, 0xdb, 0xd9, 0xd8, 0xff, 0xdc, 0xd9, 0xd8, 0xff, 0xd5, 0xd2, 0xd1, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd9, 0xd7, 0xd6, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xc7, 0xc5, 0xc4, 0xff, 0xcf, 0xce, 0xcd, 0xff, 0xd1, 0xd0, 0xcf, 0xff, 0xc9, 0xc7, 0xc6, 0xff, 0xce, 0xcd, 0xcc, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xd3, 0xd2, 0xd0, 0xff, 0xd1, 0xd0, 0xce, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xc7, 0xc6, 0xc5, 0xff, 0xc3, 0xc1, 0xc1, 0xff, 0xc4, 0xc3, 0xc2, 0xff, 0xb7, 0xb6, 0xb6, 0xff, 0xaf, 0xac, 0xaf, 0xff, 0xaa, 0xa4, 0xa6, 0xff, 0x75, 0x75, 0x79, 0xff, 0x19, 0x1e, 0x20, 0xff, 0x00, 0x00, 0x00, 0xff, 0x07, 0x09, 0x07, 0xff, 0x00, 0x04, 0x05, 0xff, 0x1a, 0x22, 0x27, 0xff, 0x32, 0x3e, 0x4c, 0xff, 0x25, 0x36, 0x47, 0xff, 0x29, 0x39, 0x46, 0xff, 0x28, 0x38, 0x44, 0xff, 0x3a, 0x4a, 0x59, 0xff, 0x34, 0x47, 0x57, 0xff, 0x43, 0x5b, 0x6c, 0xff, 0x52, 0x6f, 0x7d, 0xb7, 0x2e, 0x43, 0x50, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcf, 0xcf, 0xcc, 0x46, 0xc9, 0xc8, 0xc7, 0xca, 0xd2, 0xd1, 0xd0, 0xff, 0xd4, 0xd4, 0xd3, 0xff, 0xd1, 0xd1, 0xd0, 0xff, 0xd1, 0xd1, 0xcf, 0xff, 0xca, 0xcb, 0xc9, 0xff, 0xd4, 0xd3, 0xd2, 0xff, 0xdd, 0xdb, 0xda, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xca, 0xc8, 0xc9, 0xff, 0xc9, 0xc7, 0xc8, 0xff, 0xaf, 0xad, 0xad, 0xff, 0x95, 0x93, 0x93, 0xff, 0xc7, 0xc5, 0xc5, 0xff, 0xd2, 0xd0, 0xd0, 0xff, 0xd4, 0xd3, 0xd2, 0xff, 0xd1, 0xd1, 0xd0, 0xff, 0xd3, 0xd3, 0xd2, 0xff, 0xd3, 0xd3, 0xd1, 0xff, 0xd3, 0xd3, 0xd1, 0xff, 0xcf, 0xcf, 0xcd, 0xff, 0xd0, 0xcf, 0xce, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xd4, 0xd1, 0xd0, 0xff, 0xcf, 0xcc, 0xcb, 0xff, 0xd0, 0xcd, 0xcc, 0xff, 0xcc, 0xc9, 0xc8, 0xff, 0xcb, 0xc8, 0xc7, 0xff, 0xd3, 0xd0, 0xcf, 0xff, 0xd4, 0xd1, 0xd0, 0xff, 0xd1, 0xce, 0xcd, 0xff, 0xd6, 0xd3, 0xd2, 0xff, 0xd6, 0xd2, 0xd2, 0xff, 0xd6, 0xd2, 0xd2, 0xff, 0xd0, 0xcd, 0xcd, 0xff, 0xc6, 0xc2, 0xc2, 0xff, 0xc4, 0xc1, 0xc1, 0xff, 0xc4, 0xbf, 0xbf, 0xff, 0xb9, 0xb4, 0xb5, 0xff, 0xb4, 0xaf, 0xb2, 0xff, 0x9a, 0x98, 0x9a, 0xff, 0x2b, 0x2e, 0x30, 0xff, 0x00, 0x00, 0x00, 0xff, 0x05, 0x06, 0x06, 0xff, 0x07, 0x0a, 0x09, 0xff, 0x05, 0x0c, 0x10, 0xff, 0x24, 0x2f, 0x3a, 0xff, 0x2c, 0x39, 0x47, 0xff, 0x29, 0x3a, 0x46, 0xff, 0x28, 0x38, 0x45, 0xff, 0x29, 0x3a, 0x47, 0xff, 0x2d, 0x3f, 0x4f, 0xff, 0x36, 0x4a, 0x5a, 0xca, 0x50, 0x66, 0x78, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcd, 0xca, 0xca, 0x4d, 0xd4, 0xd3, 0xd2, 0xcc, 0xd1, 0xd0, 0xcf, 0xff, 0xd1, 0xd0, 0xcf, 0xff, 0xd2, 0xd2, 0xd0, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd8, 0xd5, 0xd5, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xca, 0xc8, 0xc7, 0xff, 0xca, 0xc7, 0xc9, 0xff, 0xc7, 0xc4, 0xc6, 0xff, 0xa6, 0xa4, 0xa4, 0xff, 0xa2, 0xa0, 0xa0, 0xff, 0xcf, 0xcd, 0xcd, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xd0, 0xcf, 0xcd, 0xff, 0xd0, 0xd0, 0xce, 0xff, 0xcd, 0xcc, 0xcb, 0xff, 0xd2, 0xd1, 0xd0, 0xff, 0xd3, 0xd2, 0xd1, 0xff, 0xd7, 0xd7, 0xd5, 0xff, 0xdb, 0xda, 0xd9, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd3, 0xd0, 0xcf, 0xff, 0xd0, 0xcb, 0xca, 0xff, 0xd1, 0xcd, 0xcc, 0xff, 0xd2, 0xcd, 0xcc, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xd3, 0xce, 0xcd, 0xff, 0xd0, 0xcc, 0xcb, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xd6, 0xd1, 0xd1, 0xff, 0xd6, 0xd0, 0xd2, 0xff, 0xd0, 0xca, 0xcb, 0xff, 0xc8, 0xc3, 0xc4, 0xff, 0xc1, 0xbc, 0xbd, 0xff, 0xbc, 0xb5, 0xb5, 0xff, 0xba, 0xb2, 0xb4, 0xff, 0xa8, 0xa3, 0xa5, 0xff, 0x61, 0x66, 0x66, 0xff, 0x0e, 0x11, 0x14, 0xff, 0x04, 0x02, 0x05, 0xff, 0x0c, 0x0b, 0x0b, 0xff, 0x02, 0x03, 0x06, 0xff, 0x11, 0x18, 0x21, 0xff, 0x28, 0x34, 0x45, 0xff, 0x1f, 0x2e, 0x3a, 0xff, 0x30, 0x41, 0x47, 0xff, 0x26, 0x37, 0x42, 0xff, 0x1b, 0x2f, 0x3d, 0xcc, 0x31, 0x45, 0x56, 0x4d, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcf, 0xcc, 0xcc, 0x41, 0xce, 0xcc, 0xca, 0xb4, 0xd3, 0xd1, 0xd0, 0xfe, 0xce, 0xcc, 0xcb, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xc9, 0xc7, 0xc6, 0xff, 0xc7, 0xc5, 0xc3, 0xff, 0xc7, 0xc4, 0xc5, 0xff, 0xc4, 0xc1, 0xc3, 0xff, 0x99, 0x97, 0x97, 0xff, 0xaa, 0xa8, 0xa8, 0xff, 0xd3, 0xd1, 0xd1, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd0, 0xcd, 0xcd, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd4, 0xd1, 0xd0, 0xff, 0xd6, 0xd4, 0xd3, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd8, 0xd6, 0xd5, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xcb, 0xc9, 0xc8, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xcd, 0xca, 0xc9, 0xff, 0xcc, 0xc7, 0xc6, 0xff, 0xcf, 0xcb, 0xca, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xc9, 0xc5, 0xc4, 0xff, 0xcc, 0xc9, 0xc7, 0xff, 0xd2, 0xcd, 0xce, 0xff, 0xcc, 0xc7, 0xc8, 0xff, 0xcb, 0xc6, 0xc7, 0xff, 0xcb, 0xc6, 0xc7, 0xff, 0xc2, 0xbc, 0xbd, 0xff, 0xb9, 0xb4, 0xb6, 0xff, 0xa9, 0xa4, 0xa6, 0xff, 0x63, 0x61, 0x62, 0xff, 0x1d, 0x20, 0x22, 0xff, 0x04, 0x07, 0x0a, 0xff, 0x09, 0x0a, 0x0b, 0xff, 0x07, 0x09, 0x0a, 0xff, 0x02, 0x06, 0x0b, 0xff, 0x22, 0x2b, 0x34, 0xff, 0x26, 0x32, 0x3f, 0xff, 0x2b, 0x38, 0x45, 0xfe, 0x2b, 0x3b, 0x45, 0xb4, 0x17, 0x27, 0x36, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xcc, 0xcc, 0x23, 0xca, 0xc8, 0xc8, 0x88, 0xd0, 0xce, 0xcd, 0xe6, 0xc9, 0xc7, 0xc6, 0xff, 0xcc, 0xca, 0xc9, 0xff, 0xc9, 0xc7, 0xc6, 0xff, 0xc6, 0xc4, 0xc2, 0xff, 0xc3, 0xc0, 0xc1, 0xff, 0xc0, 0xbd, 0xbf, 0xff, 0xa0, 0x9e, 0x9e, 0xff, 0xaf, 0xad, 0xad, 0xff, 0xd4, 0xd2, 0xd2, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd7, 0xd6, 0xd5, 0xff, 0xd4, 0xd3, 0xd2, 0xff, 0xd2, 0xd0, 0xcf, 0xff, 0xd4, 0xd2, 0xd1, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd1, 0xd0, 0xcf, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xd0, 0xce, 0xcd, 0xff, 0xc6, 0xc3, 0xc2, 0xff, 0xcb, 0xc7, 0xc6, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xd0, 0xcc, 0xcb, 0xff, 0xce, 0xca, 0xc9, 0xff, 0xd6, 0xd2, 0xd1, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xd1, 0xcd, 0xcc, 0xff, 0xd1, 0xce, 0xcc, 0xff, 0xd6, 0xd2, 0xd2, 0xff, 0xcf, 0xca, 0xcb, 0xff, 0xc8, 0xc3, 0xc4, 0xff, 0xc6, 0xc1, 0xc1, 0xff, 0xc6, 0xc2, 0xc4, 0xff, 0xb4, 0xb1, 0xb3, 0xff, 0x5f, 0x5e, 0x61, 0xff, 0x23, 0x24, 0x28, 0xff, 0x09, 0x0c, 0x10, 0xff, 0x04, 0x07, 0x09, 0xff, 0x06, 0x09, 0x09, 0xff, 0x00, 0x02, 0x06, 0xff, 0x0e, 0x14, 0x1d, 0xff, 0x24, 0x2f, 0x3a, 0xe7, 0x23, 0x30, 0x3d, 0x88, 0x24, 0x33, 0x41, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xcc, 0xcc, 0xcc, 0x05, 0xcb, 0xcb, 0xcb, 0x4a, 0xcd, 0xcc, 0xca, 0xa0, 0xcc, 0xca, 0xc9, 0xef, 0xc3, 0xc1, 0xc0, 0xff, 0xc3, 0xc0, 0xc2, 0xff, 0xbc, 0xb9, 0xbb, 0xff, 0xa3, 0xa1, 0xa1, 0xff, 0xbc, 0xba, 0xba, 0xff, 0xd7, 0xd5, 0xd5, 0xff, 0xd5, 0xd3, 0xd2, 0xff, 0xd4, 0xd1, 0xd0, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd1, 0xce, 0xcd, 0xff, 0xd2, 0xce, 0xcd, 0xff, 0xd2, 0xce, 0xcd, 0xff, 0xd1, 0xcd, 0xcc, 0xff, 0xd0, 0xcd, 0xcc, 0xff, 0xce, 0xcc, 0xcb, 0xff, 0xc6, 0xc4, 0xc3, 0xff, 0xcd, 0xcb, 0xca, 0xff, 0xd7, 0xd5, 0xd4, 0xff, 0xcf, 0xcd, 0xcc, 0xff, 0xce, 0xcb, 0xca, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xdb, 0xd7, 0xd6, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xd4, 0xd0, 0xcf, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xd8, 0xd3, 0xd4, 0xff, 0xd0, 0xcb, 0xcc, 0xff, 0xca, 0xc5, 0xc6, 0xff, 0xc2, 0xbd, 0xbe, 0xff, 0xc3, 0xc0, 0xc2, 0xff, 0x6f, 0x70, 0x72, 0xff, 0x22, 0x25, 0x2a, 0xff, 0x25, 0x27, 0x2c, 0xff, 0x08, 0x0c, 0x10, 0xff, 0x05, 0x07, 0x09, 0xff, 0x03, 0x07, 0x07, 0xef, 0x03, 0x09, 0x0e, 0xa0, 0x11, 0x1f, 0x29, 0x4a, 0x33, 0x33, 0x33, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xda, 0xb6, 0xb6, 0x07, 0xc1, 0xc1, 0xbd, 0x46, 0xc3, 0xc0, 0xc1, 0x8e, 0xb5, 0xb0, 0xb4, 0xd7, 0xa3, 0xa1, 0xa1, 0xff, 0xcb, 0xc9, 0xc9, 0xff, 0xd4, 0xd2, 0xd3, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xd0, 0xcc, 0xcb, 0xff, 0xd4, 0xcf, 0xce, 0xff, 0xd4, 0xcf, 0xce, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xd2, 0xcf, 0xce, 0xff, 0xd1, 0xcf, 0xce, 0xff, 0xca, 0xc9, 0xc8, 0xff, 0xcc, 0xcb, 0xca, 0xff, 0xd3, 0xd1, 0xd0, 0xff, 0xce, 0xcd, 0xcc, 0xff, 0xd6, 0xd3, 0xd2, 0xff, 0xda, 0xd5, 0xd4, 0xff, 0xdb, 0xd7, 0xd6, 0xff, 0xda, 0xd6, 0xd5, 0xff, 0xd9, 0xd5, 0xd4, 0xff, 0xd5, 0xd1, 0xd0, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd4, 0xd1, 0xcf, 0xff, 0xc9, 0xc5, 0xc4, 0xff, 0xc6, 0xc0, 0xc2, 0xff, 0xc2, 0xbc, 0xbd, 0xff, 0xc5, 0xbe, 0xbf, 0xff, 0x82, 0x7e, 0x80, 0xff, 0x12, 0x16, 0x18, 0xff, 0x12, 0x19, 0x1c, 0xff, 0x10, 0x16, 0x1a, 0xd7, 0x03, 0x07, 0x0e, 0x8e, 0x07, 0x0a, 0x0a, 0x46, 0x00, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, 0xa0, 0xa0, 0x1b, 0xcf, 0xcf, 0xcf, 0x55, 0xd2, 0xd0, 0xd0, 0x8f, 0xce, 0xce, 0xcd, 0xc2, 0xcf, 0xcb, 0xca, 0xea, 0xd2, 0xcd, 0xcc, 0xff, 0xd4, 0xd0, 0xcf, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xdc, 0xd8, 0xd7, 0xff, 0xe2, 0xde, 0xdd, 0xff, 0xdb, 0xd8, 0xd7, 0xff, 0xd8, 0xd5, 0xd4, 0xff, 0xda, 0xd7, 0xd6, 0xff, 0xd2, 0xcf, 0xce, 0xff, 0xd8, 0xd5, 0xd4, 0xff, 0xd9, 0xd6, 0xd5, 0xff, 0xd9, 0xd6, 0xd5, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xd6, 0xd2, 0xd1, 0xff, 0xd8, 0xd4, 0xd3, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xd3, 0xcf, 0xce, 0xff, 0xd2, 0xce, 0xcd, 0xff, 0xd5, 0xd0, 0xcf, 0xff, 0xcb, 0xc7, 0xc7, 0xff, 0xbf, 0xbb, 0xbb, 0xff, 0xbe, 0xbb, 0xbc, 0xff, 0xc3, 0xbe, 0xbf, 0xea, 0x9b, 0x98, 0x9c, 0xc2, 0x1e, 0x20, 0x25, 0x8f, 0x00, 0x03, 0x09, 0x55, 0x12, 0x12, 0x1c, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xd2, 0xd2, 0xd2, 0x11, 0xd0, 0xcc, 0xcc, 0x37, 0xd6, 0xd1, 0xd1, 0x5f, 0xd8, 0xd5, 0xd3, 0x86, 0xdc, 0xd7, 0xd7, 0xa8, 0xd9, 0xd5, 0xd4, 0xc0, 0xd8, 0xd3, 0xd2, 0xd3, 0xd2, 0xcf, 0xce, 0xe6, 0xd5, 0xd0, 0xcf, 0xee, 0xd8, 0xd5, 0xd4, 0xf5, 0xd8, 0xd3, 0xd2, 0xff, 0xd7, 0xd3, 0xd2, 0xff, 0xd5, 0xd1, 0xd0, 0xf5, 0xd4, 0xcf, 0xce, 0xee, 0xd5, 0xd1, 0xd1, 0xe6, 0xd3, 0xce, 0xcd, 0xd3, 0xd3, 0xcf, 0xcd, 0xc0, 0xd2, 0xce, 0xcc, 0xa8, 0xd3, 0xcd, 0xcb, 0x86, 0xc6, 0xc1, 0xc3, 0x5f, 0xc2, 0xbe, 0xbe, 0x37, 0xc3, 0xc3, 0xc3, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

const lv_image_dsc_t img_multilang_avatar_4 = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 128,
    .header.h = 128,
    .header.stride = 512,
    .data = img_multilang_avatar_4_map,
    .data_size = sizeof(img_multilang_avatar_4_map),
};

#endif
