#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_15
    #define LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_15
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_MULTILANG_AVATAR_15 uint8_t
img_multilang_avatar_15_map[] = {
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x69, 0x69, 0x11, 0x6f, 0x66, 0x66, 0x37, 0x6f, 0x67, 0x69, 0x5e, 0x6e, 0x66, 0x6e, 0x86, 0x6d, 0x64, 0x6e, 0xa8, 0x62, 0x5e, 0x71, 0xbf, 0x5d, 0x5d, 0x72, 0xd2, 0x66, 0x66, 0x78, 0xe5, 0x7e, 0x78, 0x89, 0xee, 0x8f, 0x83, 0x8e, 0xf2, 0x9f, 0x8f, 0x96, 0xff, 0xaa, 0x9a, 0x98, 0xff, 0xb4, 0xa5, 0x9b, 0xf2, 0xb9, 0xa9, 0x9b, 0xee, 0xbb, 0xaa, 0x98, 0xe5, 0xbc, 0xad, 0x9b, 0xd2, 0xbd, 0xb1, 0xa0, 0xbf, 0xbf, 0xb3, 0x9f, 0xa8, 0xbe, 0xb2, 0x9a, 0x86, 0xbb, 0xb0, 0x9a, 0x5e, 0xb9, 0xb0, 0x99, 0x37, 0xb4, 0xb4, 0x96, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x66, 0x7a, 0x19, 0x66, 0x63, 0x72, 0x55, 0x69, 0x63, 0x72, 0x8f, 0x6b, 0x64, 0x70, 0xc1, 0x6e, 0x65, 0x6e, 0xe9, 0x72, 0x67, 0x71, 0xff, 0x6a, 0x62, 0x6c, 0xff, 0x69, 0x63, 0x6c, 0xff, 0x68, 0x62, 0x70, 0xff, 0x66, 0x60, 0x73, 0xff, 0x61, 0x60, 0x73, 0xff, 0x5e, 0x5e, 0x73, 0xff, 0x67, 0x66, 0x7a, 0xff, 0x7f, 0x7a, 0x8b, 0xff, 0x93, 0x89, 0x93, 0xff, 0xa1, 0x92, 0x98, 0xff, 0xae, 0x9d, 0x9c, 0xff, 0xb6, 0xa5, 0x9d, 0xff, 0xbc, 0xab, 0x9e, 0xff, 0xc2, 0xb0, 0x9f, 0xff, 0xc4, 0xb4, 0xa1, 0xff, 0xc4, 0xb5, 0xa4, 0xff, 0xc3, 0xb6, 0xa3, 0xff, 0xbf, 0xb5, 0x9e, 0xff, 0xba, 0xb0, 0x9a, 0xff, 0xb7, 0xb0, 0x9a, 0xff, 0xb5, 0xb0, 0x99, 0xff, 0xb3, 0xb1, 0x9a, 0xe9, 0xb7, 0xb2, 0x9e, 0xc1, 0xbe, 0xb5, 0xa0, 0x8f, 0xc0, 0xba, 0xa2, 0x55, 0xc1, 0xb7, 0xa3, 0x19, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x48, 0x6d, 0x91, 0x07, 0x5b, 0x5e, 0x83, 0x46, 0x5f, 0x62, 0x84, 0x8e, 0x60, 0x61, 0x83, 0xd7, 0x60, 0x61, 0x80, 0xff, 0x60, 0x60, 0x7c, 0xff, 0x61, 0x60, 0x79, 0xff, 0x62, 0x5f, 0x76, 0xff, 0x65, 0x61, 0x74, 0xff, 0x65, 0x60, 0x72, 0xff, 0x64, 0x62, 0x74, 0xff, 0x67, 0x66, 0x76, 0xff, 0x5e, 0x5d, 0x71, 0xff, 0x5f, 0x5e, 0x74, 0xff, 0x5e, 0x5f, 0x77, 0xff, 0x5d, 0x5f, 0x77, 0xff, 0x63, 0x63, 0x7a, 0xff, 0x77, 0x75, 0x86, 0xff, 0x92, 0x8a, 0x95, 0xff, 0xa0, 0x94, 0x99, 0xff, 0xad, 0x9c, 0x9c, 0xff, 0xb8, 0xa4, 0x9e, 0xff, 0xbe, 0xaa, 0xa1, 0xff, 0xc5, 0xb1, 0xa1, 0xff, 0xc7, 0xb4, 0xa1, 0xff, 0xc7, 0xb5, 0xa2, 0xff, 0xc3, 0xb5, 0xa3, 0xff, 0xbf, 0xb4, 0xa2, 0xff, 0xbe, 0xb3, 0xa1, 0xff, 0xbe, 0xb4, 0xa2, 0xff, 0xba, 0xb2, 0xa0, 0xff, 0xb7, 0xb0, 0x9d, 0xff, 0xb7, 0xb1, 0x9e, 0xff, 0xb9, 0xb3, 0xa1, 0xff, 0xbc, 0xb6, 0xa4, 0xff, 0xbd, 0xb7, 0xa4, 0xff, 0xbc, 0xb5, 0xa2, 0xd7, 0xbc, 0xb7, 0xa3, 0x8e, 0xbd, 0xb9, 0xa3, 0x46, 0xb6, 0xb6, 0x91, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x66, 0x99, 0x05, 0x5c, 0x63, 0x83, 0x48, 0x5b, 0x61, 0x85, 0x9f, 0x59, 0x61, 0x85, 0xef, 0x5b, 0x61, 0x87, 0xff, 0x5c, 0x62, 0x87, 0xff, 0x5d, 0x62, 0x88, 0xff, 0x5e, 0x62, 0x86, 0xff, 0x5d, 0x60, 0x81, 0xff, 0x5c, 0x5e, 0x7d, 0xff, 0x59, 0x5c, 0x7b, 0xff, 0x5b, 0x5d, 0x79, 0xff, 0x5d, 0x5d, 0x76, 0xff, 0x6f, 0x71, 0x89, 0xff, 0x60, 0x63, 0x79, 0xff, 0x58, 0x5b, 0x72, 0xff, 0x5c, 0x5d, 0x79, 0xff, 0x5d, 0x60, 0x7d, 0xff, 0x5c, 0x60, 0x7a, 0xff, 0x6c, 0x6e, 0x88, 0xff, 0x76, 0x76, 0x89, 0xff, 0x86, 0x80, 0x8c, 0xff, 0x9e, 0x95, 0x9b, 0xff, 0xab, 0x9e, 0x9d, 0xff, 0xb5, 0xa4, 0x9f, 0xff, 0xbc, 0xaa, 0xa0, 0xff, 0xc4, 0xaf, 0xa0, 0xff, 0xc8, 0xb3, 0xa0, 0xff, 0xc6, 0xb4, 0x9f, 0xff, 0xc4, 0xb6, 0xa3, 0xff, 0xc1, 0xb6, 0xa4, 0xff, 0xc0, 0xb5, 0xa3, 0xff, 0xc1, 0xb6, 0xa4, 0xff, 0xc1, 0xb7, 0xa5, 0xff, 0xc1, 0xb6, 0xa4, 0xff, 0xbd, 0xb4, 0xa3, 0xff, 0xbb, 0xb4, 0xa3, 0xff, 0xba, 0xb3, 0xa2, 0xff, 0xba, 0xb4, 0xa3, 0xff, 0xba, 0xb6, 0xa4, 0xff, 0xb9, 0xb6, 0xa7, 0xff, 0xb9, 0xb6, 0xa4, 0xff, 0xb9, 0xb6, 0xa3, 0xef, 0xbb, 0xb5, 0xa3, 0x9f, 0xbf, 0xb8, 0xa2, 0x48, 0xcc, 0xcc, 0x99, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x57, 0x6d, 0x23, 0x55, 0x59, 0x77, 0x86, 0x59, 0x5d, 0x7b, 0xe5, 0x5c, 0x60, 0x7f, 0xff, 0x5e, 0x63, 0x85, 0xff, 0x5e, 0x64, 0x87, 0xff, 0x5d, 0x63, 0x88, 0xff, 0x5d, 0x64, 0x8a, 0xff, 0x5d, 0x64, 0x88, 0xff, 0x5a, 0x60, 0x85, 0xff, 0x58, 0x5f, 0x81, 0xff, 0x58, 0x5e, 0x7e, 0xff, 0x55, 0x5c, 0x7c, 0xff, 0x57, 0x5b, 0x7a, 0xff, 0x5b, 0x5f, 0x7b, 0xff, 0x6f, 0x73, 0x8e, 0xff, 0x5f, 0x63, 0x7c, 0xff, 0x58, 0x5c, 0x76, 0xff, 0x5c, 0x5e, 0x7c, 0xff, 0x5b, 0x5f, 0x7e, 0xff, 0x5b, 0x5f, 0x7c, 0xff, 0x70, 0x75, 0x90, 0xff, 0x8f, 0x92, 0xa6, 0xff, 0x8b, 0x88, 0x95, 0xff, 0x92, 0x8c, 0x92, 0xff, 0xa9, 0xa0, 0xa0, 0xff, 0xb0, 0xa4, 0xa1, 0xff, 0xb9, 0xaa, 0xa0, 0xff, 0xc1, 0xaf, 0x9f, 0xff, 0xc6, 0xb5, 0xa0, 0xff, 0xc6, 0xb7, 0xa0, 0xff, 0xc5, 0xb9, 0xa3, 0xff, 0xc5, 0xbb, 0xa6, 0xff, 0xc4, 0xb9, 0xa4, 0xff, 0xc4, 0xba, 0xa5, 0xff, 0xc5, 0xba, 0xa5, 0xff, 0xc4, 0xb8, 0xa2, 0xff, 0xc4, 0xb8, 0xa4, 0xff, 0xc3, 0xba, 0xa7, 0xff, 0xc1, 0xba, 0xa7, 0xff, 0xbd, 0xb7, 0xa6, 0xff, 0xbb, 0xb7, 0xa6, 0xff, 0xb8, 0xb5, 0xa6, 0xff, 0xb5, 0xb4, 0xa4, 0xff, 0xb5, 0xb4, 0xa3, 0xff, 0xb9, 0xb5, 0xa3, 0xff, 0xbe, 0xb9, 0xa5, 0xff, 0xc0, 0xbb, 0xa3, 0xe5, 0xc2, 0xbc, 0xa5, 0x86, 0xc4, 0xbd, 0xa7, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x62, 0x71, 0x41, 0x61, 0x60, 0x71, 0xb4, 0x5f, 0x60, 0x72, 0xfe, 0x5c, 0x5f, 0x78, 0xff, 0x5c, 0x5e, 0x7b, 0xff, 0x5e, 0x61, 0x7f, 0xff, 0x5e, 0x63, 0x82, 0xff, 0x60, 0x65, 0x86, 0xff, 0x5f, 0x65, 0x88, 0xff, 0x5f, 0x65, 0x89, 0xff, 0x60, 0x65, 0x89, 0xff, 0x5d, 0x63, 0x86, 0xff, 0x5a, 0x61, 0x83, 0xff, 0x57, 0x5f, 0x7e, 0xff, 0x55, 0x5d, 0x7c, 0xff, 0x58, 0x5c, 0x7c, 0xff, 0x59, 0x5c, 0x7c, 0xff, 0x6b, 0x6f, 0x8d, 0xff, 0x66, 0x6a, 0x86, 0xff, 0x5e, 0x61, 0x7d, 0xff, 0x57, 0x59, 0x77, 0xff, 0x58, 0x5d, 0x7c, 0xff, 0x5d, 0x63, 0x82, 0xff, 0x5d, 0x62, 0x7e, 0xff, 0x75, 0x79, 0x8d, 0xff, 0x99, 0x97, 0xa5, 0xff, 0x9f, 0x9b, 0x9f, 0xff, 0xa6, 0xa0, 0xa0, 0xff, 0xb4, 0xac, 0xa8, 0xff, 0xb6, 0xab, 0xa1, 0xff, 0xbe, 0xb0, 0x9e, 0xff, 0xc2, 0xb5, 0xa0, 0xff, 0xc6, 0xb9, 0xa2, 0xff, 0xc5, 0xbb, 0xa4, 0xff, 0xc6, 0xbd, 0xa7, 0xff, 0xc7, 0xbb, 0xa4, 0xff, 0xc6, 0xba, 0xa3, 0xff, 0xc6, 0xbb, 0xa4, 0xff, 0xc6, 0xba, 0xa4, 0xff, 0xcc, 0xc0, 0xac, 0xff, 0xc9, 0xbe, 0xab, 0xff, 0xc6, 0xbb, 0xa9, 0xff, 0xc4, 0xbb, 0xa9, 0xff, 0xc2, 0xbb, 0xaa, 0xff, 0xc1, 0xbb, 0xa8, 0xff, 0xba, 0xb8, 0xa7, 0xff, 0xb4, 0xb5, 0xa5, 0xff, 0xb6, 0xb3, 0xa2, 0xff, 0xbb, 0xb6, 0xa2, 0xff, 0xbf, 0xb9, 0xa2, 0xff, 0xc2, 0xbc, 0xa3, 0xff, 0xc4, 0xbc, 0xa4, 0xfe, 0xc4, 0xbc, 0xa7, 0xb4, 0xc4, 0xbc, 0xa8, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x75, 0x6e, 0x4c, 0x78, 0x6e, 0x6c, 0xcb, 0x74, 0x6f, 0x72, 0xff, 0x72, 0x6e, 0x78, 0xff, 0x6b, 0x68, 0x77, 0xff, 0x65, 0x64, 0x76, 0xff, 0x60, 0x63, 0x77, 0xff, 0x5f, 0x64, 0x7d, 0xff, 0x5e, 0x64, 0x82, 0xff, 0x5f, 0x64, 0x84, 0xff, 0x61, 0x64, 0x84, 0xff, 0x63, 0x65, 0x86, 0xff, 0x64, 0x66, 0x88, 0xff, 0x62, 0x65, 0x87, 0xff, 0x5f, 0x63, 0x83, 0xff, 0x5b, 0x61, 0x7f, 0xff, 0x56, 0x5d, 0x7b, 0xff, 0x5b, 0x5d, 0x7f, 0xff, 0x5a, 0x5b, 0x7f, 0xff, 0x6b, 0x71, 0x92, 0xff, 0x5c, 0x63, 0x83, 0xff, 0x61, 0x67, 0x85, 0xff, 0x6d, 0x71, 0x8f, 0xff, 0x5e, 0x62, 0x81, 0xff, 0x57, 0x5c, 0x7c, 0xff, 0x5f, 0x63, 0x80, 0xff, 0x68, 0x6a, 0x81, 0xff, 0x86, 0x84, 0x91, 0xff, 0xa2, 0x9c, 0xa5, 0xff, 0xaf, 0xa9, 0xaa, 0xff, 0xb1, 0xad, 0xa9, 0xff, 0xb7, 0xaf, 0xa9, 0xff, 0xbd, 0xb3, 0xa9, 0xff, 0xc1, 0xb5, 0xa8, 0xff, 0xc7, 0xb8, 0xa8, 0xff, 0xc6, 0xbb, 0xa9, 0xff, 0xc8, 0xbd, 0xa9, 0xff, 0xd0, 0xbc, 0xa7, 0xff, 0xd2, 0xbd, 0xa6, 0xff, 0xca, 0xbb, 0xa6, 0xff, 0xc6, 0xbf, 0xae, 0xff, 0xc9, 0xc5, 0xb3, 0xff, 0xc6, 0xbc, 0xab, 0xff, 0xce, 0xbc, 0xad, 0xff, 0xd3, 0xbd, 0xae, 0xff, 0xd0, 0xbf, 0xae, 0xff, 0xc9, 0xbf, 0xaa, 0xff, 0xc3, 0xbe, 0xa9, 0xff, 0xc1, 0xbb, 0xaa, 0xff, 0xbf, 0xb8, 0xa6, 0xff, 0xbe, 0xb7, 0xa4, 0xff, 0xbe, 0xb8, 0xa5, 0xff, 0xbf, 0xb9, 0xa6, 0xff, 0xc2, 0xba, 0xa8, 0xff, 0xc3, 0xba, 0xa8, 0xff, 0xc5, 0xbc, 0xa7, 0xff, 0xc6, 0xbe, 0xa7, 0xcb, 0xca, 0xc0, 0xa8, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x99, 0x78, 0x46, 0x9f, 0x90, 0x78, 0xc9, 0x92, 0x82, 0x73, 0xff, 0x88, 0x7b, 0x6f, 0xff, 0x84, 0x7a, 0x76, 0xff, 0x82, 0x79, 0x7c, 0xff, 0x7c, 0x74, 0x7a, 0xff, 0x70, 0x6d, 0x79, 0xff, 0x67, 0x69, 0x7b, 0xff, 0x62, 0x66, 0x7c, 0xff, 0x5f, 0x65, 0x81, 0xff, 0x61, 0x65, 0x85, 0xff, 0x64, 0x67, 0x85, 0xff, 0x65, 0x68, 0x86, 0xff, 0x64, 0x68, 0x86, 0xff, 0x63, 0x67, 0x85, 0xff, 0x61, 0x64, 0x82, 0xff, 0x5d, 0x61, 0x7e, 0xff, 0x5f, 0x65, 0x80, 0xff, 0x62, 0x63, 0x83, 0xff, 0x5a, 0x5b, 0x7d, 0xff, 0x68, 0x6d, 0x8e, 0xff, 0x5d, 0x66, 0x84, 0xff, 0x66, 0x6c, 0x8b, 0xff, 0x72, 0x77, 0x96, 0xff, 0x77, 0x79, 0x99, 0xff, 0x71, 0x74, 0x93, 0xff, 0x6b, 0x70, 0x8d, 0xff, 0x72, 0x75, 0x8c, 0xff, 0x8a, 0x88, 0x97, 0xff, 0x89, 0x82, 0x8d, 0xff, 0xa2, 0x9c, 0x9d, 0xff, 0xb2, 0xaa, 0xa8, 0xff, 0xb7, 0xae, 0xaa, 0xff, 0xb9, 0xaf, 0xa5, 0xff, 0xc5, 0xb7, 0xa8, 0xff, 0xcc, 0xbe, 0xaa, 0xff, 0xcc, 0xbf, 0xa9, 0xff, 0xca, 0xbc, 0xa8, 0xff, 0xc9, 0xbb, 0xaa, 0xff, 0xce, 0xc0, 0xb0, 0xff, 0xca, 0xbc, 0xaa, 0xff, 0xcd, 0xc3, 0xae, 0xff, 0xcd, 0xc4, 0xab, 0xff, 0xca, 0xc0, 0xa6, 0xff, 0xcc, 0xc2, 0xac, 0xff, 0xcd, 0xc3, 0xae, 0xff, 0xcd, 0xc3, 0xb0, 0xff, 0xca, 0xc3, 0xaf, 0xff, 0xc9, 0xc1, 0xae, 0xff, 0xc8, 0xbf, 0xac, 0xff, 0xc5, 0xbd, 0xaa, 0xff, 0xc4, 0xbd, 0xa9, 0xff, 0xc5, 0xbd, 0xaa, 0xff, 0xc4, 0xbc, 0xa9, 0xff, 0xc4, 0xbb, 0xa9, 0xff, 0xc3, 0xba, 0xa8, 0xff, 0xc3, 0xbb, 0xa5, 0xff, 0xc4, 0xbd, 0xa6, 0xff, 0xc7, 0xbe, 0xab, 0xff, 0xc9, 0xbf, 0xad, 0xc9, 0xc8, 0xbd, 0xae, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc6, 0xaa, 0x86, 0x24, 0xbf, 0xa5, 0x82, 0xb4, 0xb9, 0xa4, 0x84, 0xff, 0xb1, 0x9d, 0x7e, 0xff, 0xa5, 0x90, 0x75, 0xff, 0x9b, 0x88, 0x73, 0xff, 0x97, 0x85, 0x78, 0xff, 0x93, 0x84, 0x7e, 0xff, 0x8f, 0x80, 0x7d, 0xff, 0x7e, 0x77, 0x7f, 0xff, 0x70, 0x71, 0x80, 0xff, 0x67, 0x6a, 0x7d, 0xff, 0x64, 0x67, 0x81, 0xff, 0x66, 0x6a, 0x86, 0xff, 0x69, 0x6d, 0x88, 0xff, 0x69, 0x6c, 0x88, 0xff, 0x68, 0x6b, 0x87, 0xff, 0x66, 0x6b, 0x84, 0xff, 0x66, 0x69, 0x82, 0xff, 0x60, 0x63, 0x7a, 0xff, 0x68, 0x6b, 0x80, 0xff, 0x76, 0x75, 0x90, 0xff, 0x55, 0x55, 0x73, 0xff, 0x65, 0x69, 0x86, 0xff, 0x6c, 0x72, 0x90, 0xff, 0x5c, 0x62, 0x81, 0xff, 0x62, 0x66, 0x86, 0xff, 0x74, 0x77, 0x95, 0xff, 0x73, 0x77, 0x94, 0xff, 0x64, 0x68, 0x84, 0xff, 0x82, 0x83, 0x9b, 0xff, 0x93, 0x91, 0xa1, 0xff, 0x84, 0x7f, 0x89, 0xff, 0xa2, 0x9b, 0xa1, 0xff, 0xa9, 0xa1, 0xa3, 0xff, 0xb1, 0xa8, 0xa7, 0xff, 0xba, 0xaf, 0xa5, 0xff, 0xbe, 0xb2, 0xa1, 0xff, 0xc4, 0xb8, 0xa3, 0xff, 0xca, 0xbc, 0xa3, 0xff, 0xcb, 0xbe, 0xaa, 0xff, 0xc3, 0xbc, 0xb2, 0xff, 0xc4, 0xc0, 0xb9, 0xff, 0xc8, 0xc1, 0xb3, 0xff, 0xce, 0xc1, 0xa9, 0xff, 0xd5, 0xc0, 0xa3, 0xff, 0xd8, 0xc3, 0xa4, 0xff, 0xce, 0xc6, 0xa7, 0xff, 0xc8, 0xc7, 0xab, 0xff, 0xcb, 0xc6, 0xb0, 0xff, 0xd1, 0xc4, 0xb3, 0xff, 0xd2, 0xc4, 0xb2, 0xff, 0xcf, 0xc4, 0xae, 0xff, 0xcc, 0xc2, 0xac, 0xff, 0xcb, 0xc0, 0xab, 0xff, 0xcb, 0xc0, 0xab, 0xff, 0xcb, 0xc1, 0xab, 0xff, 0xca, 0xc0, 0xac, 0xff, 0xc9, 0xbe, 0xaa, 0xff, 0xc8, 0xbe, 0xa8, 0xff, 0xc8, 0xbe, 0xa9, 0xff, 0xc8, 0xbd, 0xad, 0xff, 0xc9, 0xbe, 0xad, 0xff, 0xc4, 0xba, 0xb2, 0xff, 0xbf, 0xb7, 0xb7, 0xb5, 0xbf, 0xb8, 0xb8, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc6, 0xaa, 0x8d, 0x09, 0xc8, 0xab, 0x8d, 0x87, 0xcc, 0xb1, 0x8a, 0xf9, 0xc9, 0xaf, 0x88, 0xff, 0xc4, 0xab, 0x87, 0xff, 0xbf, 0xa7, 0x81, 0xff, 0xb4, 0x9c, 0x77, 0xff, 0xab, 0x94, 0x77, 0xff, 0xa7, 0x92, 0x7d, 0xff, 0xa1, 0x90, 0x82, 0xff, 0x9f, 0x8b, 0x83, 0xff, 0x8c, 0x82, 0x85, 0xff, 0x7a, 0x79, 0x82, 0xff, 0x6d, 0x6d, 0x7d, 0xff, 0x68, 0x6a, 0x80, 0xff, 0x6a, 0x6d, 0x87, 0xff, 0x6c, 0x6f, 0x8b, 0xff, 0x6e, 0x71, 0x8a, 0xff, 0x6e, 0x70, 0x87, 0xff, 0x6c, 0x6f, 0x84, 0xff, 0x6b, 0x6c, 0x80, 0xff, 0x69, 0x6b, 0x7c, 0xff, 0x62, 0x63, 0x72, 0xff, 0x73, 0x73, 0x86, 0xff, 0x7e, 0x80, 0x96, 0xff, 0x7e, 0x83, 0x9a, 0xff, 0x8a, 0x91, 0xab, 0xff, 0x61, 0x67, 0x84, 0xff, 0x5c, 0x61, 0x80, 0xff, 0x7e, 0x83, 0xa0, 0xff, 0x7d, 0x81, 0x9d, 0xff, 0x69, 0x6e, 0x89, 0xff, 0x80, 0x84, 0x9b, 0xff, 0x9c, 0x9f, 0xaf, 0xff, 0xa1, 0xa0, 0xaa, 0xff, 0xad, 0xaa, 0xb4, 0xff, 0xb4, 0xaf, 0xb6, 0xff, 0xb8, 0xb4, 0xb6, 0xff, 0xbf, 0xba, 0xb6, 0xff, 0xc3, 0xba, 0xb0, 0xff, 0xbd, 0xb5, 0xa8, 0xff, 0xc0, 0xb4, 0xa3, 0xff, 0xc5, 0xb9, 0xa9, 0xff, 0xc2, 0xbf, 0xb9, 0xff, 0xc3, 0xc3, 0xc1, 0xff, 0xc9, 0xc9, 0xc0, 0xff, 0xca, 0xc5, 0xb8, 0xff, 0xd4, 0xbf, 0xab, 0xff, 0xda, 0xbf, 0xa5, 0xff, 0xd3, 0xc3, 0xa7, 0xff, 0xd1, 0xc7, 0xa9, 0xff, 0xd4, 0xc6, 0xad, 0xff, 0xdb, 0xc3, 0xb1, 0xff, 0xd7, 0xc5, 0xb1, 0xff, 0xd2, 0xc6, 0xaf, 0xff, 0xd2, 0xc5, 0xae, 0xff, 0xd0, 0xc3, 0xac, 0xff, 0xcf, 0xc2, 0xab, 0xff, 0xce, 0xc1, 0xab, 0xff, 0xce, 0xc1, 0xab, 0xff, 0xcf, 0xc2, 0xab, 0xff, 0xce, 0xc1, 0xab, 0xff, 0xcd, 0xc1, 0xac, 0xff, 0xca, 0xbf, 0xaf, 0xff, 0xc9, 0xbc, 0xb0, 0xff, 0xc1, 0xb5, 0xb3, 0xff, 0xb8, 0xb0, 0xb5, 0xff, 0xb5, 0xac, 0xb5, 0xf9, 0xb1, 0xaa, 0xb7, 0x87, 0xaa, 0xaa, 0xc6, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbf, 0xab, 0x9b, 0x40, 0xc6, 0xb0, 0x96, 0xda, 0xd0, 0xb4, 0x93, 0xff, 0xd4, 0xb7, 0x8f, 0xff, 0xd0, 0xb4, 0x8c, 0xff, 0xc8, 0xb0, 0x8a, 0xff, 0xc3, 0xad, 0x83, 0xff, 0xba, 0xa3, 0x7a, 0xff, 0xb2, 0x9c, 0x7c, 0xff, 0xaf, 0x9a, 0x85, 0xff, 0xac, 0x99, 0x8b, 0xff, 0xa7, 0x94, 0x8c, 0xff, 0x96, 0x8c, 0x8a, 0xff, 0x86, 0x80, 0x85, 0xff, 0x74, 0x70, 0x7d, 0xff, 0x6b, 0x6e, 0x7f, 0xff, 0x6d, 0x6f, 0x86, 0xff, 0x70, 0x71, 0x8b, 0xff, 0x72, 0x74, 0x8a, 0xff, 0x73, 0x77, 0x89, 0xff, 0x73, 0x75, 0x87, 0xff, 0x72, 0x72, 0x80, 0xff, 0x6f, 0x70, 0x7b, 0xff, 0x6b, 0x6a, 0x74, 0xff, 0x6e, 0x6e, 0x79, 0xff, 0x88, 0x8a, 0x97, 0xff, 0x81, 0x88, 0x96, 0xff, 0x77, 0x80, 0x93, 0xff, 0x8f, 0x97, 0xaf, 0xff, 0x77, 0x7e, 0x99, 0xff, 0x68, 0x70, 0x8c, 0xff, 0x7e, 0x88, 0xa4, 0xff, 0x8b, 0x95, 0xb0, 0xff, 0x96, 0xa0, 0xb6, 0xff, 0xad, 0xb6, 0xc7, 0xff, 0xcd, 0xd2, 0xdd, 0xff, 0xc9, 0xce, 0xd9, 0xff, 0xc8, 0xce, 0xd9, 0xff, 0xd3, 0xd7, 0xe0, 0xff, 0xc7, 0xc7, 0xcc, 0xff, 0xc5, 0xc4, 0xc7, 0xff, 0xbe, 0xbc, 0xbe, 0xff, 0xbf, 0xb8, 0xb3, 0xff, 0xbd, 0xb4, 0xa9, 0xff, 0xba, 0xb8, 0xad, 0xff, 0xc7, 0xc8, 0xc1, 0xff, 0xc2, 0xc7, 0xc6, 0xff, 0xa2, 0xac, 0xb4, 0xff, 0xb8, 0xba, 0xba, 0xff, 0xd1, 0xc5, 0xb8, 0xff, 0xce, 0xbf, 0xac, 0xff, 0xd6, 0xc1, 0xa7, 0xff, 0xda, 0xc4, 0xaa, 0xff, 0xd7, 0xc4, 0xad, 0xff, 0xd4, 0xc5, 0xae, 0xff, 0xd3, 0xc5, 0xae, 0xff, 0xd3, 0xc4, 0xad, 0xff, 0xd3, 0xc4, 0xad, 0xff, 0xd2, 0xc2, 0xab, 0xff, 0xd2, 0xc2, 0xab, 0xff, 0xd1, 0xc1, 0xaa, 0xff, 0xd0, 0xc2, 0xaa, 0xff, 0xcf, 0xc2, 0xab, 0xff, 0xce, 0xc2, 0xaf, 0xff, 0xcb, 0xc0, 0xb2, 0xff, 0xca, 0xbc, 0xb5, 0xff, 0xc0, 0xb5, 0xb5, 0xff, 0xb8, 0xb1, 0xb7, 0xff, 0xb1, 0xaa, 0xb7, 0xff, 0xa7, 0xa1, 0xb9, 0xff, 0x9f, 0x9b, 0xbd, 0xda, 0x9c, 0x99, 0xc0, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb6, 0xb6, 0xb6, 0x07, 0xc5, 0xae, 0xa1, 0x8e, 0xc4, 0xb1, 0x9d, 0xfe, 0xcb, 0xb6, 0x9c, 0xff, 0xd5, 0xba, 0x98, 0xff, 0xda, 0xbc, 0x94, 0xff, 0xd4, 0xb8, 0x90, 0xff, 0xcb, 0xb3, 0x8c, 0xff, 0xc5, 0xaf, 0x85, 0xff, 0xbc, 0xa7, 0x7f, 0xff, 0xb5, 0xa0, 0x81, 0xff, 0xb2, 0x9f, 0x89, 0xff, 0xaf, 0x9e, 0x91, 0xff, 0xab, 0x98, 0x91, 0xff, 0x9e, 0x92, 0x8f, 0xff, 0x8c, 0x86, 0x88, 0xff, 0x79, 0x76, 0x7f, 0xff, 0x70, 0x71, 0x82, 0xff, 0x71, 0x73, 0x89, 0xff, 0x72, 0x75, 0x8e, 0xff, 0x74, 0x78, 0x8b, 0xff, 0x77, 0x7b, 0x8c, 0xff, 0x79, 0x7b, 0x8a, 0xff, 0x77, 0x77, 0x82, 0xff, 0x78, 0x77, 0x7e, 0xff, 0x78, 0x75, 0x7b, 0xff, 0x74, 0x74, 0x79, 0xff, 0x61, 0x64, 0x6a, 0xff, 0x6f, 0x76, 0x80, 0xff, 0x82, 0x8d, 0x9b, 0xff, 0xb4, 0xbd, 0xd0, 0xff, 0xca, 0xd0, 0xe8, 0xff, 0xb8, 0xc1, 0xd7, 0xff, 0xc1, 0xcd, 0xe4, 0xff, 0xca, 0xd6, 0xee, 0xff, 0xd2, 0xdf, 0xf4, 0xff, 0xe1, 0xed, 0xfb, 0xff, 0xe8, 0xee, 0xf7, 0xff, 0xdd, 0xe7, 0xf3, 0xff, 0xcd, 0xdb, 0xe9, 0xff, 0xcc, 0xd7, 0xe3, 0xff, 0xcf, 0xd5, 0xe2, 0xff, 0xd1, 0xd4, 0xe0, 0xff, 0xcb, 0xcc, 0xd8, 0xff, 0xc0, 0xbd, 0xc3, 0xff, 0xbe, 0xbc, 0xb5, 0xff, 0xbe, 0xbc, 0xaf, 0xff, 0xbc, 0xbc, 0xb0, 0xff, 0xb8, 0xc1, 0xc3, 0xff, 0x99, 0xaa, 0xbf, 0xff, 0x82, 0x9a, 0xac, 0xff, 0x98, 0xa9, 0xac, 0xff, 0xc8, 0xc3, 0xb9, 0xff, 0xda, 0xc4, 0xac, 0xff, 0xd5, 0xc1, 0xa2, 0xff, 0xcf, 0xc6, 0xa6, 0xff, 0xd0, 0xc4, 0xa8, 0xff, 0xd4, 0xc3, 0xaa, 0xff, 0xd3, 0xc3, 0xaa, 0xff, 0xd2, 0xc3, 0xaa, 0xff, 0xd1, 0xc2, 0xaa, 0xff, 0xd1, 0xc2, 0xa8, 0xff, 0xd1, 0xc1, 0xa9, 0xff, 0xd2, 0xc2, 0xab, 0xff, 0xd1, 0xc3, 0xab, 0xff, 0xd0, 0xc3, 0xb1, 0xff, 0xcc, 0xc0, 0xb5, 0xff, 0xcb, 0xbc, 0xb6, 0xff, 0xc2, 0xb8, 0xb7, 0xff, 0xba, 0xb4, 0xbc, 0xff, 0xb4, 0xad, 0xbc, 0xff, 0xa9, 0xa3, 0xc0, 0xff, 0x9b, 0x98, 0xc3, 0xff, 0x93, 0x8f, 0xc2, 0xfe, 0x88, 0x88, 0xc3, 0x8e, 0x6d, 0x6d, 0xb6, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xae, 0xa7, 0xa7, 0x29, 0xbe, 0xad, 0xa4, 0xd1, 0xc4, 0xaf, 0xa0, 0xff, 0xc8, 0xb1, 0x9b, 0xff, 0xcc, 0xb6, 0x98, 0xff, 0xd4, 0xbb, 0x96, 0xff, 0xd9, 0xbe, 0x96, 0xff, 0xd7, 0xbc, 0x94, 0xff, 0xd1, 0xb5, 0x91, 0xff, 0xca, 0xb2, 0x8e, 0xff, 0xbf, 0xab, 0x86, 0xff, 0xbb, 0xa6, 0x84, 0xff, 0xb8, 0xa4, 0x8b, 0xff, 0xb3, 0xa0, 0x91, 0xff, 0xab, 0x9c, 0x91, 0xff, 0x9e, 0x96, 0x94, 0xff, 0x8e, 0x8a, 0x92, 0xff, 0x7e, 0x7d, 0x8c, 0xff, 0x72, 0x76, 0x8d, 0xff, 0x74, 0x79, 0x92, 0xff, 0x75, 0x7d, 0x95, 0xff, 0x78, 0x7f, 0x92, 0xff, 0x7f, 0x81, 0x90, 0xff, 0x83, 0x81, 0x8c, 0xff, 0x82, 0x7d, 0x82, 0xff, 0x80, 0x79, 0x7c, 0xff, 0x79, 0x70, 0x72, 0xff, 0x73, 0x73, 0x78, 0xff, 0x87, 0x8d, 0x95, 0xff, 0xaa, 0xb4, 0xc2, 0xff, 0xc4, 0xd2, 0xe4, 0xff, 0xbe, 0xcb, 0xdd, 0xff, 0xd5, 0xe1, 0xf3, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf2, 0xfa, 0xff, 0xff, 0xe8, 0xf4, 0xff, 0xff, 0xd7, 0xe4, 0xf8, 0xff, 0xda, 0xe7, 0xf6, 0xff, 0xe9, 0xef, 0xf9, 0xff, 0xe0, 0xf2, 0xff, 0xff, 0xcb, 0xe4, 0xf8, 0xff, 0xbe, 0xd0, 0xde, 0xff, 0xbd, 0xc8, 0xd3, 0xff, 0xc7, 0xcb, 0xd3, 0xff, 0xd0, 0xd2, 0xd7, 0xff, 0xca, 0xd2, 0xdd, 0xff, 0xb5, 0xc1, 0xcd, 0xff, 0xb3, 0xbb, 0xc2, 0xff, 0xb7, 0xbd, 0xc0, 0xff, 0xb6, 0xba, 0xba, 0xff, 0xba, 0xb7, 0xb3, 0xff, 0x93, 0xa5, 0xb4, 0xff, 0x69, 0x88, 0xa3, 0xff, 0x80, 0x90, 0x9e, 0xff, 0xb8, 0xb9, 0xb9, 0xff, 0xdc, 0xce, 0xbc, 0xff, 0xdc, 0xc3, 0xa2, 0xff, 0xda, 0xc0, 0x9f, 0xff, 0xd7, 0xc2, 0xa7, 0xff, 0xd1, 0xc3, 0xa7, 0xff, 0xcc, 0xc3, 0xa8, 0xff, 0xcc, 0xc3, 0xa9, 0xff, 0xd1, 0xc3, 0xa8, 0xff, 0xd2, 0xc3, 0xa9, 0xff, 0xd3, 0xc3, 0xab, 0xff, 0xd4, 0xc3, 0xac, 0xff, 0xd3, 0xc2, 0xb0, 0xff, 0xce, 0xc1, 0xb6, 0xff, 0xc8, 0xbc, 0xb8, 0xff, 0xc3, 0xba, 0xba, 0xff, 0xbe, 0xb5, 0xbd, 0xff, 0xb5, 0xaa, 0xbe, 0xff, 0xa9, 0xa2, 0xbe, 0xff, 0x9d, 0x9a, 0xbe, 0xff, 0x97, 0x90, 0xc5, 0xff, 0x89, 0x87, 0xca, 0xff, 0x78, 0x80, 0xc8, 0xd1, 0x76, 0x7c, 0xc7, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9a, 0x97, 0xa6, 0x56, 0xae, 0xa4, 0xaa, 0xf3, 0xbc, 0xad, 0xa7, 0xff, 0xc1, 0xad, 0xa1, 0xff, 0xc4, 0xaf, 0x99, 0xff, 0xc8, 0xb2, 0x94, 0xff, 0xce, 0xb7, 0x91, 0xff, 0xd4, 0xba, 0x92, 0xff, 0xd5, 0xba, 0x93, 0xff, 0xd3, 0xb7, 0x94, 0xff, 0xcd, 0xb3, 0x90, 0xff, 0xc4, 0xae, 0x89, 0xff, 0xbf, 0xa8, 0x88, 0xff, 0xbc, 0xa5, 0x8d, 0xff, 0xb4, 0xa3, 0x93, 0xff, 0xaa, 0x9d, 0x92, 0xff, 0x9c, 0x95, 0x94, 0xff, 0x93, 0x8e, 0x98, 0xff, 0x87, 0x86, 0x96, 0xff, 0x7c, 0x7d, 0x96, 0xff, 0x7b, 0x80, 0x9a, 0xff, 0x7d, 0x84, 0x9b, 0xff, 0x81, 0x85, 0x99, 0xff, 0x88, 0x88, 0x97, 0xff, 0x8a, 0x89, 0x93, 0xff, 0x89, 0x86, 0x8c, 0xff, 0x80, 0x7e, 0x83, 0xff, 0x7e, 0x7d, 0x80, 0xff, 0x93, 0x9b, 0xa4, 0xff, 0xbe, 0xcb, 0xdc, 0xff, 0xc5, 0xd5, 0xe9, 0xff, 0xc1, 0xd5, 0xec, 0xff, 0xc9, 0xde, 0xf4, 0xff, 0xc4, 0xd7, 0xec, 0xff, 0xae, 0xc1, 0xd1, 0xff, 0xae, 0xc5, 0xd4, 0xff, 0xbc, 0xd5, 0xe9, 0xff, 0xac, 0xc7, 0xdd, 0xff, 0xab, 0xc5, 0xdd, 0xff, 0xb4, 0xcd, 0xdf, 0xff, 0xa7, 0xc2, 0xd9, 0xff, 0xa2, 0xbf, 0xd9, 0xff, 0xb8, 0xce, 0xe4, 0xff, 0xb6, 0xc7, 0xd9, 0xff, 0xbe, 0xcb, 0xd8, 0xff, 0xc1, 0xca, 0xd6, 0xff, 0xc1, 0xca, 0xdb, 0xff, 0xbf, 0xc9, 0xdc, 0xff, 0xb3, 0xbc, 0xca, 0xff, 0xb7, 0xbb, 0xc6, 0xff, 0xbd, 0xbc, 0xc2, 0xff, 0xb7, 0xb3, 0xb6, 0xff, 0xb2, 0xae, 0xa5, 0xff, 0xba, 0xc1, 0xbc, 0xff, 0x74, 0x8a, 0x9e, 0xff, 0x3f, 0x5b, 0x78, 0xff, 0x8b, 0x9b, 0xa8, 0xff, 0xcf, 0xca, 0xbd, 0xff, 0xd7, 0xc6, 0xb0, 0xff, 0xcf, 0xbc, 0xa4, 0xff, 0xd1, 0xc1, 0xa2, 0xff, 0xd2, 0xc2, 0xa0, 0xff, 0xd1, 0xc1, 0xa1, 0xff, 0xd0, 0xc1, 0xa5, 0xff, 0xd2, 0xc3, 0xa9, 0xff, 0xd3, 0xc4, 0xab, 0xff, 0xd5, 0xc4, 0xad, 0xff, 0xd4, 0xc2, 0xb0, 0xff, 0xce, 0xc0, 0xb5, 0xff, 0xc8, 0xbc, 0xb7, 0xff, 0xc2, 0xb9, 0xb8, 0xff, 0xbb, 0xb3, 0xbb, 0xff, 0xb2, 0xa8, 0xbe, 0xff, 0xa7, 0xa2, 0xbc, 0xff, 0x9b, 0x9a, 0xbc, 0xff, 0x94, 0x8d, 0xc3, 0xff, 0x88, 0x85, 0xc8, 0xff, 0x7a, 0x80, 0xc6, 0xff, 0x7a, 0x7e, 0xc8, 0xf3, 0x7c, 0x82, 0xcc, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0xaa, 0xaa, 0x03, 0x8a, 0x8a, 0x9e, 0x8a, 0x99, 0x95, 0xa4, 0xfe, 0xaa, 0xa2, 0xa8, 0xff, 0xb6, 0xa9, 0xa5, 0xff, 0xbc, 0xaa, 0xa1, 0xff, 0xbf, 0xac, 0x98, 0xff, 0xc4, 0xb0, 0x92, 0xff, 0xca, 0xb3, 0x8f, 0xff, 0xcf, 0xb6, 0x8d, 0xff, 0xd0, 0xb6, 0x8e, 0xff, 0xce, 0xb3, 0x8f, 0xff, 0xc9, 0xb0, 0x8b, 0xff, 0xc4, 0xac, 0x87, 0xff, 0xbe, 0xa7, 0x87, 0xff, 0xb9, 0xa4, 0x8c, 0xff, 0xae, 0xa0, 0x92, 0xff, 0xa5, 0x99, 0x90, 0xff, 0x97, 0x92, 0x92, 0xff, 0x8d, 0x8b, 0x95, 0xff, 0x87, 0x85, 0x98, 0xff, 0x82, 0x82, 0x9b, 0xff, 0x83, 0x84, 0x9e, 0xff, 0x82, 0x83, 0x9c, 0xff, 0x7e, 0x7e, 0x93, 0xff, 0x7b, 0x7b, 0x8c, 0xff, 0x83, 0x83, 0x8f, 0xff, 0x9b, 0x9f, 0xa8, 0xff, 0xbd, 0xc4, 0xcd, 0xff, 0xd8, 0xe1, 0xeb, 0xff, 0xd9, 0xe8, 0xf4, 0xff, 0xdf, 0xf0, 0xff, 0xff, 0xd4, 0xe6, 0xf8, 0xff, 0xb9, 0xd0, 0xe3, 0xff, 0xbc, 0xd3, 0xe9, 0xff, 0xb3, 0xc9, 0xdf, 0xff, 0x9f, 0xbc, 0xd5, 0xff, 0x8e, 0xb0, 0xcb, 0xff, 0x66, 0x89, 0xa4, 0xff, 0x7c, 0x9f, 0xbc, 0xff, 0x7f, 0xa3, 0xbf, 0xff, 0x7f, 0xa4, 0xc0, 0xff, 0x97, 0xb8, 0xd6, 0xff, 0x75, 0x92, 0xb2, 0xff, 0x60, 0x7d, 0x9c, 0xff, 0x93, 0xac, 0xc9, 0xff, 0xa7, 0xbd, 0xd6, 0xff, 0xac, 0xbe, 0xd6, 0xff, 0xbe, 0xcd, 0xe2, 0xff, 0xc2, 0xcf, 0xe3, 0xff, 0xc1, 0xcd, 0xe0, 0xff, 0xb7, 0xc0, 0xd0, 0xff, 0xb7, 0xbd, 0xcc, 0xff, 0xad, 0xb8, 0xcb, 0xff, 0xaf, 0xa7, 0x9d, 0xff, 0xcc, 0xc0, 0xad, 0xff, 0xae, 0xbc, 0xcb, 0xff, 0x5f, 0x7d, 0x9c, 0xff, 0x30, 0x4d, 0x6a, 0xff, 0x4b, 0x63, 0x75, 0xff, 0xb1, 0xb7, 0xb7, 0xff, 0xd5, 0xcb, 0xbb, 0xff, 0xd0, 0xbf, 0xa3, 0xff, 0xd8, 0xc0, 0x99, 0xff, 0xd2, 0xbc, 0x96, 0xff, 0xcf, 0xbd, 0xa1, 0xff, 0xcf, 0xc1, 0xa8, 0xff, 0xd1, 0xc3, 0xa9, 0xff, 0xd3, 0xc3, 0xab, 0xff, 0xd2, 0xc2, 0xae, 0xff, 0xcf, 0xc0, 0xb2, 0xff, 0xc9, 0xbd, 0xb4, 0xff, 0xc3, 0xb9, 0xb6, 0xff, 0xbc, 0xb1, 0xba, 0xff, 0xb0, 0xa7, 0xbb, 0xff, 0xa5, 0xa0, 0xba, 0xff, 0x98, 0x99, 0xbb, 0xff, 0x8d, 0x8b, 0xc1, 0xff, 0x83, 0x83, 0xc3, 0xff, 0x7e, 0x82, 0xc4, 0xff, 0x7f, 0x82, 0xc9, 0xff, 0x7f, 0x81, 0xcd, 0xfe, 0x7e, 0x82, 0xd1, 0x8b, 0x55, 0xaa, 0xaa, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x73, 0x73, 0x8b, 0x0b, 0x82, 0x7f, 0x95, 0xae, 0x8a, 0x89, 0x9e, 0xff, 0x97, 0x94, 0xa5, 0xff, 0xa5, 0xa0, 0xa8, 0xff, 0xb2, 0xa7, 0xa5, 0xff, 0xb7, 0xa7, 0xa1, 0xff, 0xbc, 0xa9, 0x97, 0xff, 0xc0, 0xac, 0x91, 0xff, 0xc3, 0xad, 0x8b, 0xff, 0xc7, 0xb1, 0x87, 0xff, 0xc9, 0xb3, 0x89, 0xff, 0xc7, 0xb0, 0x89, 0xff, 0xc4, 0xae, 0x87, 0xff, 0xc1, 0xa8, 0x84, 0xff, 0xbb, 0xa4, 0x84, 0xff, 0xb4, 0xa1, 0x89, 0xff, 0xa9, 0x9d, 0x8f, 0xff, 0xa0, 0x96, 0x8f, 0xff, 0x94, 0x8e, 0x92, 0xff, 0x8a, 0x88, 0x95, 0xff, 0x83, 0x83, 0x97, 0xff, 0x7f, 0x80, 0x9b, 0xff, 0x7e, 0x81, 0x9b, 0xff, 0x83, 0x88, 0x9f, 0xff, 0x96, 0x9a, 0xae, 0xff, 0xa2, 0xa5, 0xb7, 0xff, 0xa2, 0xa7, 0xb6, 0xff, 0xbd, 0xc6, 0xd2, 0xff, 0xe5, 0xf2, 0xff, 0xff, 0xd8, 0xec, 0xfb, 0xff, 0xd7, 0xe7, 0xf2, 0xff, 0xdf, 0xec, 0xf3, 0xff, 0xe3, 0xf2, 0xfc, 0xff, 0xcf, 0xdf, 0xed, 0xff, 0xb4, 0xc6, 0xd7, 0xff, 0xb8, 0xcd, 0xdc, 0xff, 0x99, 0xb3, 0xcd, 0xff, 0x97, 0xb3, 0xd4, 0xff, 0x7f, 0x9a, 0xb8, 0xff, 0x47, 0x63, 0x82, 0xff, 0x54, 0x70, 0x90, 0xff, 0x48, 0x65, 0x86, 0xff, 0x69, 0x85, 0xa6, 0xff, 0x8e, 0xaa, 0xc8, 0xff, 0x52, 0x6f, 0x90, 0xff, 0x2c, 0x49, 0x6b, 0xff, 0x56, 0x71, 0x90, 0xff, 0x71, 0x87, 0xa3, 0xff, 0x81, 0x9a, 0xb2, 0xff, 0xa0, 0xbb, 0xd2, 0xff, 0x9e, 0xb8, 0xcf, 0xff, 0xab, 0xc5, 0xda, 0xff, 0xa9, 0xc3, 0xd7, 0xff, 0xa9, 0xc3, 0xd6, 0xff, 0x9f, 0xad, 0xc3, 0xff, 0xa9, 0xab, 0xbd, 0xff, 0xc6, 0xc6, 0xce, 0xff, 0xb9, 0xb9, 0xbf, 0xff, 0x81, 0x8b, 0x9b, 0xff, 0x24, 0x3e, 0x61, 0xff, 0x35, 0x4c, 0x63, 0xff, 0x98, 0x9f, 0x9e, 0xff, 0xd0, 0xc8, 0xbc, 0xff, 0xd6, 0xc2, 0xa8, 0xff, 0xcd, 0xb8, 0x99, 0xff, 0xcb, 0xb9, 0x9e, 0xff, 0xcc, 0xbf, 0xa5, 0xff, 0xce, 0xc0, 0xa6, 0xff, 0xd0, 0xc1, 0xa7, 0xff, 0xd1, 0xc0, 0xab, 0xff, 0xcf, 0xbf, 0xae, 0xff, 0xca, 0xbc, 0xb0, 0xff, 0xc5, 0xb9, 0xb2, 0xff, 0xbe, 0xb0, 0xb6, 0xff, 0xb3, 0xa7, 0xb8, 0xff, 0xa4, 0x9f, 0xb5, 0xff, 0x96, 0x98, 0xb6, 0xff, 0x8c, 0x8c, 0xbd, 0xff, 0x82, 0x83, 0xc0, 0xff, 0x7f, 0x82, 0xc1, 0xff, 0x81, 0x83, 0xc7, 0xff, 0x81, 0x83, 0xcd, 0xff, 0x82, 0x84, 0xd1, 0xff, 0x81, 0x84, 0xd3, 0xaf, 0x8b, 0x8b, 0xd0, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x73, 0x73, 0x7f, 0x16, 0x78, 0x78, 0x8a, 0xc9, 0x80, 0x80, 0x95, 0xff, 0x88, 0x87, 0x9c, 0xff, 0x93, 0x92, 0xa4, 0xff, 0xa1, 0x9c, 0xa5, 0xff, 0xad, 0xa2, 0xa3, 0xff, 0xb1, 0xa4, 0x9f, 0xff, 0xb7, 0xa5, 0x96, 0xff, 0xb9, 0xa5, 0x8e, 0xff, 0xba, 0xa6, 0x86, 0xff, 0xc0, 0xaa, 0x82, 0xff, 0xc2, 0xad, 0x83, 0xff, 0xc1, 0xac, 0x84, 0xff, 0xc0, 0xa8, 0x82, 0xff, 0xbc, 0xa2, 0x7f, 0xff, 0xb6, 0x9f, 0x7f, 0xff, 0xae, 0x9c, 0x84, 0xff, 0xa4, 0x97, 0x8c, 0xff, 0x9b, 0x92, 0x8d, 0xff, 0x92, 0x89, 0x92, 0xff, 0x8c, 0x86, 0x96, 0xff, 0x83, 0x83, 0x98, 0xff, 0x78, 0x7e, 0x98, 0xff, 0x84, 0x8f, 0xa7, 0xff, 0xa3, 0xb1, 0xc6, 0xff, 0xc6, 0xd2, 0xe4, 0xff, 0xdf, 0xe9, 0xfa, 0xff, 0xe2, 0xed, 0xfd, 0xff, 0xcc, 0xda, 0xe8, 0xff, 0xc2, 0xd3, 0xe4, 0xff, 0x9f, 0xb6, 0xcb, 0xff, 0x8b, 0x9e, 0xae, 0xff, 0xb0, 0xbd, 0xca, 0xff, 0xb6, 0xc5, 0xd1, 0xff, 0xbf, 0xcf, 0xdd, 0xff, 0xa8, 0xbc, 0xcf, 0xff, 0x8d, 0xa5, 0xba, 0xff, 0x8c, 0xa3, 0xbc, 0xff, 0x4e, 0x63, 0x7e, 0xff, 0x5a, 0x6e, 0x87, 0xff, 0x66, 0x7a, 0x92, 0xff, 0x43, 0x59, 0x74, 0xff, 0x36, 0x4f, 0x6f, 0xff, 0x14, 0x27, 0x40, 0xff, 0x42, 0x56, 0x6c, 0xff, 0x6e, 0x8a, 0xa8, 0xff, 0x4a, 0x68, 0x89, 0xff, 0x15, 0x30, 0x4d, 0xff, 0x34, 0x47, 0x5f, 0xff, 0x41, 0x5c, 0x74, 0xff, 0x53, 0x73, 0x8b, 0xff, 0x75, 0x94, 0xac, 0xff, 0x91, 0xb1, 0xca, 0xff, 0x99, 0xb8, 0xd0, 0xff, 0x8e, 0xac, 0xc3, 0xff, 0x96, 0xb6, 0xd9, 0xff, 0x94, 0xab, 0xce, 0xff, 0xba, 0xc1, 0xcc, 0xff, 0xd3, 0xd0, 0xd0, 0xff, 0xa8, 0xab, 0xb4, 0xff, 0x67, 0x79, 0x96, 0xff, 0x34, 0x51, 0x6f, 0xff, 0x2e, 0x48, 0x5c, 0xff, 0x72, 0x7d, 0x89, 0xff, 0xbe, 0xba, 0xba, 0xff, 0xce, 0xc3, 0xb3, 0xff, 0xc3, 0xb1, 0x97, 0xff, 0xc8, 0xb9, 0x9d, 0xff, 0xc8, 0xbd, 0xa3, 0xff, 0xcd, 0xbe, 0xa4, 0xff, 0xcd, 0xbd, 0xa4, 0xff, 0xcc, 0xbc, 0xa9, 0xff, 0xc8, 0xb9, 0xaa, 0xff, 0xc5, 0xb6, 0xab, 0xff, 0xbe, 0xae, 0xaf, 0xff, 0xb3, 0xa5, 0xb2, 0xff, 0xa3, 0x9d, 0xae, 0xff, 0x94, 0x94, 0xae, 0xff, 0x8b, 0x8b, 0xb5, 0xff, 0x83, 0x82, 0xbb, 0xff, 0x7f, 0x81, 0xbe, 0xff, 0x83, 0x82, 0xc5, 0xff, 0x83, 0x83, 0xcd, 0xff, 0x84, 0x85, 0xd1, 0xff, 0x84, 0x87, 0xd2, 0xff, 0x82, 0x89, 0xd5, 0xc9, 0x7f, 0x8b, 0xdc, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x75, 0x7f, 0x1a, 0x7d, 0x77, 0x85, 0xd6, 0x7c, 0x7a, 0x8d, 0xff, 0x7e, 0x7f, 0x94, 0xff, 0x85, 0x85, 0x9b, 0xff, 0x90, 0x90, 0xa1, 0xff, 0x9c, 0x97, 0xa2, 0xff, 0xa7, 0x9e, 0xa1, 0xff, 0xac, 0xa0, 0x9c, 0xff, 0xb1, 0xa1, 0x93, 0xff, 0xb3, 0xa1, 0x8a, 0xff, 0xb5, 0xa2, 0x81, 0xff, 0xb7, 0xa2, 0x7b, 0xff, 0xb9, 0xa5, 0x7b, 0xff, 0xb9, 0xa6, 0x7e, 0xff, 0xba, 0xa1, 0x7b, 0xff, 0xb6, 0x9d, 0x78, 0xff, 0xb1, 0x9b, 0x7c, 0xff, 0xa8, 0x97, 0x82, 0xff, 0x9d, 0x92, 0x88, 0xff, 0x95, 0x8e, 0x89, 0xff, 0x90, 0x87, 0x8f, 0xff, 0x87, 0x81, 0x92, 0xff, 0x7f, 0x82, 0x98, 0xff, 0x85, 0x90, 0xa9, 0xff, 0xa0, 0xb3, 0xca, 0xff, 0xa8, 0xbf, 0xd3, 0xff, 0xae, 0xc2, 0xd2, 0xff, 0xb5, 0xc4, 0xd4, 0xff, 0xb6, 0xc6, 0xd6, 0xff, 0xb7, 0xc8, 0xd9, 0xff, 0xae, 0xc1, 0xd5, 0xff, 0x78, 0x8f, 0xa7, 0xff, 0x2f, 0x46, 0x60, 0xff, 0x2b, 0x40, 0x58, 0xff, 0x58, 0x6c, 0x81, 0xff, 0x41, 0x56, 0x6c, 0xff, 0x6c, 0x83, 0x9f, 0xff, 0x5a, 0x77, 0x9b, 0xff, 0x3f, 0x5a, 0x75, 0xff, 0x4f, 0x65, 0x79, 0xff, 0x03, 0x16, 0x28, 0xff, 0x27, 0x39, 0x49, 0xff, 0x42, 0x59, 0x6d, 0xff, 0x34, 0x4e, 0x6a, 0xff, 0x25, 0x35, 0x48, 0xff, 0x00, 0x05, 0x14, 0xff, 0x26, 0x3d, 0x56, 0xff, 0x4f, 0x6a, 0x86, 0xff, 0x37, 0x50, 0x69, 0xff, 0x16, 0x29, 0x3b, 0xff, 0x39, 0x4e, 0x63, 0xff, 0x31, 0x48, 0x62, 0xff, 0x33, 0x4a, 0x63, 0xff, 0x57, 0x6f, 0x8a, 0xff, 0x8b, 0xa3, 0xbf, 0xff, 0x8b, 0xa4, 0xc2, 0xff, 0x87, 0xa5, 0xbc, 0xff, 0x82, 0xa1, 0xb5, 0xff, 0x92, 0xae, 0xc3, 0xff, 0xa7, 0xbf, 0xd2, 0xff, 0xab, 0xc2, 0xd4, 0xff, 0x81, 0x92, 0xa2, 0xff, 0x69, 0x86, 0x9f, 0xff, 0x2d, 0x52, 0x76, 0xff, 0x1b, 0x35, 0x54, 0xff, 0x48, 0x54, 0x69, 0xff, 0xab, 0xa9, 0xa8, 0xff, 0xda, 0xca, 0xb4, 0xff, 0xc3, 0xb2, 0x95, 0xff, 0xc2, 0xb5, 0x99, 0xff, 0xca, 0xb9, 0x9e, 0xff, 0xcb, 0xba, 0xa0, 0xff, 0xca, 0xb8, 0xa5, 0xff, 0xc5, 0xb6, 0xa7, 0xff, 0xc3, 0xb3, 0xa6, 0xff, 0xbe, 0xad, 0xa8, 0xff, 0xb4, 0xa4, 0xac, 0xff, 0xa3, 0x99, 0xa8, 0xff, 0x93, 0x8f, 0xa6, 0xff, 0x89, 0x87, 0xae, 0xff, 0x82, 0x81, 0xb7, 0xff, 0x7e, 0x81, 0xbc, 0xff, 0x83, 0x83, 0xc4, 0xff, 0x84, 0x83, 0xcc, 0xff, 0x86, 0x85, 0xd2, 0xff, 0x86, 0x87, 0xd3, 0xff, 0x85, 0x89, 0xd5, 0xff, 0x85, 0x8b, 0xda, 0xd6, 0x89, 0x89, 0xd7, 0x1a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8f, 0x7f, 0x7f, 0x20, 0x82, 0x7c, 0x80, 0xde, 0x7e, 0x7a, 0x85, 0xff, 0x7c, 0x7b, 0x89, 0xff, 0x7d, 0x7d, 0x94, 0xff, 0x81, 0x83, 0x99, 0xff, 0x89, 0x8d, 0xa0, 0xff, 0x94, 0x94, 0xa2, 0xff, 0xa0, 0x9a, 0x9f, 0xff, 0xa7, 0x9c, 0x9c, 0xff, 0xac, 0x9e, 0x92, 0xff, 0xaf, 0x9f, 0x87, 0xff, 0xb1, 0x9f, 0x7d, 0xff, 0xb2, 0x9e, 0x75, 0xff, 0xb2, 0x9e, 0x77, 0xff, 0xb0, 0x9a, 0x7d, 0xff, 0xae, 0x9b, 0x73, 0xff, 0xab, 0x99, 0x70, 0xff, 0xa4, 0x95, 0x7b, 0xff, 0x9d, 0x91, 0x83, 0xff, 0x96, 0x8c, 0x86, 0xff, 0x93, 0x8a, 0x83, 0xff, 0x8c, 0x85, 0x87, 0xff, 0x7a, 0x7a, 0x88, 0xff, 0x85, 0x8d, 0xa1, 0xff, 0xa9, 0xba, 0xd2, 0xff, 0xb9, 0xcd, 0xe3, 0xff, 0xbb, 0xd0, 0xe3, 0xff, 0xce, 0xdc, 0xf0, 0xff, 0xbd, 0xca, 0xe1, 0xff, 0x98, 0xa9, 0xc0, 0xff, 0x96, 0xaa, 0xc2, 0xff, 0x92, 0xa9, 0xc3, 0xff, 0x7f, 0x9b, 0xb6, 0xff, 0x4e, 0x65, 0x80, 0xff, 0x0b, 0x1d, 0x35, 0xff, 0x1d, 0x2f, 0x43, 0xff, 0x11, 0x21, 0x34, 0xff, 0x0e, 0x1f, 0x34, 0xff, 0x54, 0x68, 0x81, 0xff, 0x3d, 0x54, 0x6f, 0xff, 0x30, 0x46, 0x5d, 0xff, 0x24, 0x2e, 0x40, 0xff, 0x00, 0x02, 0x0f, 0xff, 0x0d, 0x15, 0x22, 0xff, 0x20, 0x29, 0x38, 0xff, 0x30, 0x48, 0x58, 0xff, 0x10, 0x21, 0x31, 0xff, 0x00, 0x00, 0x10, 0xff, 0x11, 0x18, 0x2b, 0xff, 0x3c, 0x4d, 0x62, 0xff, 0x4c, 0x68, 0x83, 0xff, 0x1e, 0x31, 0x46, 0xff, 0x23, 0x2f, 0x40, 0xff, 0x00, 0x0f, 0x20, 0xff, 0x17, 0x27, 0x3a, 0xff, 0x62, 0x76, 0x8d, 0xff, 0x71, 0x8a, 0xa8, 0xff, 0x69, 0x83, 0x99, 0xff, 0x8b, 0xa6, 0xb8, 0xff, 0x8f, 0xae, 0xc7, 0xff, 0x68, 0x89, 0xa5, 0xff, 0x82, 0xa1, 0xbc, 0xff, 0x9a, 0xb4, 0xca, 0xff, 0x71, 0x8d, 0xa6, 0xff, 0x38, 0x57, 0x7a, 0xff, 0x20, 0x3c, 0x5d, 0xff, 0x16, 0x2b, 0x47, 0xff, 0x24, 0x31, 0x3f, 0xff, 0x94, 0x9d, 0xa2, 0xff, 0xd4, 0xcf, 0xba, 0xff, 0xce, 0xbe, 0x9a, 0xff, 0xc9, 0xb5, 0x9a, 0xff, 0xcb, 0xb4, 0x9e, 0xff, 0xc8, 0xb1, 0x9f, 0xff, 0xc4, 0xb1, 0xa1, 0xff, 0xc3, 0xaf, 0xa0, 0xff, 0xbb, 0xaa, 0x9f, 0xff, 0xaf, 0xa2, 0xa0, 0xff, 0xa6, 0x98, 0x9b, 0xff, 0x9b, 0x8d, 0x9c, 0xff, 0x8b, 0x86, 0xa9, 0xff, 0x82, 0x82, 0xb1, 0xff, 0x83, 0x82, 0xb6, 0xff, 0x84, 0x84, 0xbf, 0xff, 0x86, 0x85, 0xc8, 0xff, 0x87, 0x86, 0xce, 0xff, 0x87, 0x87, 0xd2, 0xff, 0x87, 0x8a, 0xd4, 0xff, 0x8a, 0x8d, 0xd9, 0xff, 0x8a, 0x8d, 0xdb, 0xde, 0x87, 0x8f, 0xd7, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa3, 0x91, 0x7f, 0x1c, 0x99, 0x8a, 0x81, 0xdb, 0x8b, 0x82, 0x82, 0xff, 0x81, 0x7c, 0x86, 0xff, 0x80, 0x7b, 0x8c, 0xff, 0x7d, 0x7d, 0x94, 0xff, 0x7c, 0x81, 0x96, 0xff, 0x82, 0x8a, 0x9d, 0xff, 0x8e, 0x93, 0xa1, 0xff, 0x9b, 0x9a, 0x9e, 0xff, 0xa2, 0x9b, 0x9d, 0xff, 0xa9, 0x9b, 0x93, 0xff, 0xac, 0x9c, 0x87, 0xff, 0xad, 0x9c, 0x7c, 0xff, 0xae, 0x9c, 0x74, 0xff, 0xab, 0x98, 0x72, 0xff, 0xa8, 0x93, 0x72, 0xff, 0xa7, 0x93, 0x70, 0xff, 0xa8, 0x93, 0x71, 0xff, 0xa7, 0x8f, 0x76, 0xff, 0xa1, 0x8c, 0x7d, 0xff, 0x96, 0x88, 0x84, 0xff, 0x87, 0x7e, 0x82, 0xff, 0x8f, 0x8c, 0x94, 0xff, 0x9e, 0xa2, 0xb0, 0xff, 0xae, 0xb9, 0xcd, 0xff, 0xb9, 0xcb, 0xe1, 0xff, 0xbf, 0xd1, 0xe6, 0xff, 0xc9, 0xd8, 0xe7, 0xff, 0xc3, 0xd3, 0xe5, 0xff, 0xcf, 0xe1, 0xf8, 0xff, 0xd0, 0xe4, 0xfb, 0xff, 0xa9, 0xc1, 0xd9, 0xff, 0x6d, 0x85, 0xa0, 0xff, 0x38, 0x54, 0x71, 0xff, 0x3b, 0x50, 0x69, 0xff, 0x37, 0x45, 0x58, 0xff, 0x0f, 0x1c, 0x2f, 0xff, 0x20, 0x2e, 0x3f, 0xff, 0x08, 0x16, 0x27, 0xff, 0x0f, 0x1d, 0x30, 0xff, 0x3f, 0x54, 0x6d, 0xff, 0x31, 0x42, 0x5b, 0xff, 0x23, 0x2d, 0x40, 0xff, 0x0c, 0x10, 0x1e, 0xff, 0x02, 0x06, 0x12, 0xff, 0x0e, 0x15, 0x1f, 0xff, 0x0b, 0x1a, 0x2a, 0xff, 0x2e, 0x41, 0x54, 0xff, 0x05, 0x0c, 0x1b, 0xff, 0x00, 0x02, 0x11, 0xff, 0x4a, 0x57, 0x69, 0xff, 0x25, 0x37, 0x4f, 0xff, 0x1f, 0x2d, 0x41, 0xff, 0x19, 0x23, 0x30, 0xff, 0x0f, 0x1c, 0x2a, 0xff, 0x09, 0x18, 0x27, 0xff, 0x34, 0x47, 0x5b, 0xff, 0x3b, 0x53, 0x6d, 0xff, 0x34, 0x4c, 0x63, 0xff, 0x7b, 0x93, 0xa5, 0xff, 0x87, 0xa1, 0xb9, 0xff, 0x65, 0x81, 0x9c, 0xff, 0x5a, 0x76, 0x90, 0xff, 0x8a, 0xa5, 0xbe, 0xff, 0x66, 0x81, 0x9c, 0xff, 0x4a, 0x66, 0x84, 0xff, 0x2c, 0x45, 0x64, 0xff, 0x15, 0x2b, 0x47, 0xff, 0x18, 0x28, 0x3c, 0xff, 0x2e, 0x3d, 0x49, 0xff, 0x84, 0x8c, 0x89, 0xff, 0xc3, 0xbd, 0xae, 0xff, 0xdc, 0xcd, 0xb9, 0xff, 0xd0, 0xb6, 0x9d, 0xff, 0xcd, 0xb4, 0x9b, 0xff, 0xc6, 0xb1, 0x9a, 0xff, 0xc3, 0xaf, 0x98, 0xff, 0xbb, 0xaa, 0x96, 0xff, 0xaf, 0x9f, 0x93, 0xff, 0xa9, 0x97, 0x8e, 0xff, 0xa0, 0x8d, 0x90, 0xff, 0x8e, 0x84, 0x9b, 0xff, 0x84, 0x81, 0xa9, 0xff, 0x86, 0x82, 0xb2, 0xff, 0x88, 0x84, 0xba, 0xff, 0x89, 0x85, 0xc5, 0xff, 0x8a, 0x87, 0xcc, 0xff, 0x8a, 0x89, 0xd0, 0xff, 0x89, 0x8b, 0xd6, 0xff, 0x8b, 0x8d, 0xdb, 0xff, 0x8c, 0x8e, 0xdc, 0xff, 0x8b, 0x8d, 0xdb, 0xdc, 0x88, 0x88, 0xda, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb6, 0x9d, 0x79, 0x15, 0xad, 0x94, 0x7b, 0xd6, 0xa2, 0x8e, 0x7c, 0xff, 0x93, 0x85, 0x80, 0xff, 0x86, 0x7c, 0x86, 0xff, 0x82, 0x7c, 0x8f, 0xff, 0x7b, 0x7e, 0x93, 0xff, 0x78, 0x7f, 0x94, 0xff, 0x7f, 0x87, 0x98, 0xff, 0x87, 0x90, 0x9c, 0xff, 0x93, 0x97, 0x9b, 0xff, 0x9a, 0x98, 0x99, 0xff, 0xa3, 0x97, 0x93, 0xff, 0xa6, 0x98, 0x8a, 0xff, 0xa8, 0x98, 0x7d, 0xff, 0xa8, 0x98, 0x71, 0xff, 0xa6, 0x95, 0x6d, 0xff, 0xa6, 0x95, 0x6c, 0xff, 0xa6, 0x93, 0x70, 0xff, 0xac, 0x91, 0x70, 0xff, 0xae, 0x8f, 0x6d, 0xff, 0xa3, 0x88, 0x70, 0xff, 0x9b, 0x8e, 0x88, 0xff, 0x92, 0x92, 0x9c, 0xff, 0x98, 0x9d, 0xad, 0xff, 0xb6, 0xbf, 0xd1, 0xff, 0xc3, 0xd1, 0xe3, 0xff, 0xcc, 0xdd, 0xf0, 0xff, 0xdb, 0xeb, 0xfd, 0xff, 0xdd, 0xec, 0xf8, 0xff, 0x9c, 0xb3, 0xc1, 0xff, 0x69, 0x83, 0x97, 0xff, 0x7a, 0x92, 0xa6, 0xff, 0x70, 0x8a, 0xa1, 0xff, 0x72, 0x89, 0xa4, 0xff, 0x3d, 0x55, 0x72, 0xff, 0x11, 0x23, 0x39, 0xff, 0x15, 0x21, 0x33, 0xff, 0x0d, 0x18, 0x29, 0xff, 0x16, 0x21, 0x32, 0xff, 0x24, 0x31, 0x43, 0xff, 0x13, 0x21, 0x35, 0xff, 0x28, 0x3b, 0x51, 0xff, 0x25, 0x35, 0x4b, 0xff, 0x1e, 0x28, 0x3a, 0xff, 0x19, 0x20, 0x30, 0xff, 0x04, 0x0c, 0x1a, 0xff, 0x0e, 0x18, 0x24, 0xff, 0x20, 0x28, 0x37, 0xff, 0x0d, 0x14, 0x25, 0xff, 0x31, 0x45, 0x51, 0xff, 0x34, 0x45, 0x52, 0xff, 0x13, 0x1c, 0x2b, 0xff, 0x06, 0x05, 0x15, 0xff, 0x23, 0x28, 0x38, 0xff, 0x24, 0x2e, 0x3c, 0xff, 0x1a, 0x24, 0x30, 0xff, 0x12, 0x1b, 0x28, 0xff, 0x04, 0x10, 0x20, 0xff, 0x42, 0x57, 0x6d, 0xff, 0x48, 0x5a, 0x6c, 0xff, 0x36, 0x48, 0x58, 0xff, 0x64, 0x7c, 0x91, 0xff, 0x54, 0x6d, 0x86, 0xff, 0x52, 0x6d, 0x86, 0xff, 0x83, 0x9d, 0xb4, 0xff, 0x69, 0x84, 0x9c, 0xff, 0x47, 0x60, 0x7c, 0xff, 0x32, 0x49, 0x66, 0xff, 0x20, 0x36, 0x51, 0xff, 0x17, 0x27, 0x3e, 0xff, 0x14, 0x21, 0x2e, 0xff, 0x2e, 0x3e, 0x50, 0xff, 0x69, 0x75, 0x85, 0xff, 0xa8, 0xa2, 0x9d, 0xff, 0xe5, 0xd0, 0xb8, 0xff, 0xd8, 0xc0, 0xa1, 0xff, 0xcc, 0xb6, 0x98, 0xff, 0xc9, 0xb3, 0x95, 0xff, 0xc2, 0xad, 0x8f, 0xff, 0xb4, 0xa1, 0x8a, 0xff, 0xad, 0x97, 0x81, 0xff, 0xa2, 0x8a, 0x7f, 0xff, 0x8f, 0x7f, 0x88, 0xff, 0x84, 0x7c, 0x9a, 0xff, 0x85, 0x7f, 0xa8, 0xff, 0x8b, 0x83, 0xb4, 0xff, 0x8c, 0x87, 0xc1, 0xff, 0x8b, 0x88, 0xcb, 0xff, 0x8e, 0x8a, 0xd0, 0xff, 0x8c, 0x8c, 0xd6, 0xff, 0x8a, 0x8d, 0xdc, 0xff, 0x8c, 0x8e, 0xdd, 0xff, 0x8b, 0x8c, 0xdc, 0xff, 0x8a, 0x8b, 0xdb, 0xd7, 0x85, 0x85, 0xda, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb2, 0x99, 0x7f, 0x0a, 0xaa, 0x96, 0x7a, 0xc8, 0xa8, 0x94, 0x7b, 0xff, 0xa2, 0x8e, 0x7c, 0xff, 0x96, 0x85, 0x7f, 0xff, 0x89, 0x7c, 0x86, 0xff, 0x81, 0x7a, 0x8d, 0xff, 0x7b, 0x7e, 0x92, 0xff, 0x7a, 0x81, 0x95, 0xff, 0x7f, 0x89, 0x99, 0xff, 0x85, 0x8f, 0x9a, 0xff, 0x8c, 0x92, 0x99, 0xff, 0x93, 0x92, 0x95, 0xff, 0x9c, 0x94, 0x91, 0xff, 0xa0, 0x95, 0x89, 0xff, 0xa3, 0x96, 0x7f, 0xff, 0xa7, 0x97, 0x75, 0xff, 0xa7, 0x97, 0x6e, 0xff, 0xa6, 0x97, 0x6d, 0xff, 0xa8, 0x96, 0x6f, 0xff, 0xaa, 0x93, 0x6c, 0xff, 0xad, 0x96, 0x71, 0xff, 0xa5, 0x95, 0x7b, 0xff, 0x96, 0x94, 0x8d, 0xff, 0x9c, 0xa9, 0xb0, 0xff, 0xbf, 0xd0, 0xe1, 0xff, 0xc6, 0xd6, 0xe9, 0xff, 0xc4, 0xd7, 0xe9, 0xff, 0xc5, 0xd9, 0xe7, 0xff, 0xc0, 0xd4, 0xe5, 0xff, 0xb4, 0xc7, 0xd7, 0xff, 0x9e, 0xb9, 0xcc, 0xff, 0x54, 0x72, 0x88, 0xff, 0x2f, 0x4a, 0x5f, 0xff, 0x0f, 0x24, 0x3a, 0xff, 0x2f, 0x42, 0x59, 0xff, 0x44, 0x54, 0x6c, 0xff, 0x1d, 0x2b, 0x3f, 0xff, 0x02, 0x0f, 0x20, 0xff, 0x18, 0x22, 0x34, 0xff, 0x24, 0x2f, 0x40, 0xff, 0x17, 0x25, 0x38, 0xff, 0x11, 0x21, 0x38, 0xff, 0x2a, 0x3c, 0x52, 0xff, 0x1d, 0x2e, 0x43, 0xff, 0x13, 0x21, 0x36, 0xff, 0x1e, 0x2a, 0x3e, 0xff, 0x16, 0x20, 0x32, 0xff, 0x12, 0x1d, 0x2c, 0xff, 0x21, 0x29, 0x38, 0xff, 0x0a, 0x0f, 0x1f, 0xff, 0x15, 0x25, 0x35, 0xff, 0x47, 0x59, 0x68, 0xff, 0x02, 0x08, 0x13, 0xff, 0x00, 0x00, 0x04, 0xff, 0x32, 0x33, 0x40, 0xff, 0x1a, 0x22, 0x30, 0xff, 0x08, 0x0d, 0x17, 0xff, 0x0b, 0x11, 0x1b, 0xff, 0x09, 0x0f, 0x1c, 0xff, 0x5c, 0x6d, 0x7e, 0xff, 0x22, 0x31, 0x3e, 0xff, 0x25, 0x32, 0x3f, 0xff, 0x32, 0x44, 0x56, 0xff, 0x39, 0x50, 0x66, 0xff, 0x57, 0x70, 0x86, 0xff, 0x73, 0x8d, 0xa2, 0xff, 0x39, 0x53, 0x6b, 0xff, 0x37, 0x51, 0x6b, 0xff, 0x35, 0x4c, 0x67, 0xff, 0x14, 0x25, 0x3e, 0xff, 0x14, 0x23, 0x38, 0xff, 0x0a, 0x16, 0x26, 0xff, 0x15, 0x27, 0x40, 0xff, 0x47, 0x5b, 0x79, 0xff, 0x5c, 0x61, 0x69, 0xff, 0x98, 0x91, 0x85, 0xff, 0xd5, 0xc5, 0xab, 0xff, 0xd4, 0xc0, 0xa3, 0xff, 0xca, 0xb3, 0x94, 0xff, 0xc3, 0xad, 0x8c, 0xff, 0xb7, 0xa6, 0x87, 0xff, 0xaf, 0x97, 0x78, 0xff, 0xa2, 0x87, 0x72, 0xff, 0x95, 0x80, 0x81, 0xff, 0x88, 0x7d, 0x94, 0xff, 0x86, 0x80, 0xa3, 0xff, 0x8d, 0x84, 0xb0, 0xff, 0x8f, 0x88, 0xc1, 0xff, 0x8f, 0x89, 0xcc, 0xff, 0x91, 0x8c, 0xd2, 0xff, 0x8e, 0x8d, 0xd7, 0xff, 0x8b, 0x8d, 0xdd, 0xff, 0x8c, 0x8e, 0xdd, 0xff, 0x8c, 0x8d, 0xdd, 0xff, 0x8b, 0x8c, 0xdd, 0xff, 0x8a, 0x8a, 0xdc, 0xc8, 0x7f, 0x7f, 0xe5, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xaa, 0xaa, 0xaa, 0x03, 0xaa, 0xa1, 0x9b, 0xb1, 0xa6, 0x9a, 0x8f, 0xff, 0x9f, 0x91, 0x84, 0xff, 0x9b, 0x8b, 0x7f, 0xff, 0x95, 0x85, 0x81, 0xff, 0x87, 0x7d, 0x84, 0xff, 0x7f, 0x7b, 0x8b, 0xff, 0x7e, 0x7e, 0x91, 0xff, 0x7c, 0x81, 0x93, 0xff, 0x7f, 0x89, 0x99, 0xff, 0x87, 0x8f, 0x9d, 0xff, 0x91, 0x96, 0x9d, 0xff, 0x97, 0x96, 0x9a, 0xff, 0x9b, 0x96, 0x92, 0xff, 0x9d, 0x97, 0x8a, 0xff, 0xa1, 0x97, 0x81, 0xff, 0xa7, 0x99, 0x7a, 0xff, 0xa7, 0x97, 0x73, 0xff, 0xa6, 0x98, 0x70, 0xff, 0xae, 0x9b, 0x73, 0xff, 0xb2, 0x9d, 0x7c, 0xff, 0x9c, 0x92, 0x7d, 0xff, 0x91, 0x94, 0x8f, 0xff, 0xa7, 0xb4, 0xbc, 0xff, 0xc3, 0xd9, 0xe5, 0xff, 0xda, 0xf1, 0xff, 0xff, 0xd8, 0xee, 0xff, 0xff, 0xc6, 0xdd, 0xf2, 0xff, 0x9f, 0xb8, 0xcc, 0xff, 0x85, 0x9f, 0xb6, 0xff, 0x81, 0x9f, 0xb6, 0xff, 0x72, 0x90, 0xab, 0xff, 0x4c, 0x6a, 0x85, 0xff, 0x31, 0x4a, 0x62, 0xff, 0x11, 0x23, 0x37, 0xff, 0x00, 0x08, 0x18, 0xff, 0x16, 0x20, 0x2e, 0xff, 0x12, 0x1e, 0x30, 0xff, 0x28, 0x36, 0x4a, 0xff, 0x20, 0x2a, 0x3c, 0xff, 0x00, 0x07, 0x1a, 0xff, 0x05, 0x13, 0x29, 0xff, 0x16, 0x28, 0x43, 0xff, 0x18, 0x2c, 0x44, 0xff, 0x1a, 0x2c, 0x43, 0xff, 0x08, 0x18, 0x33, 0xff, 0x05, 0x11, 0x2b, 0xff, 0x0c, 0x1a, 0x31, 0xff, 0x0b, 0x1b, 0x2c, 0xff, 0x01, 0x0d, 0x1d, 0xff, 0x0b, 0x15, 0x27, 0xff, 0x00, 0x06, 0x19, 0xff, 0x20, 0x2e, 0x3f, 0xff, 0x10, 0x18, 0x24, 0xff, 0x0b, 0x0e, 0x16, 0xff, 0x2b, 0x30, 0x39, 0xff, 0x00, 0x03, 0x0d, 0xff, 0x07, 0x0a, 0x12, 0xff, 0x08, 0x0c, 0x14, 0xff, 0x44, 0x4d, 0x56, 0xff, 0x2d, 0x39, 0x46, 0xff, 0x0b, 0x13, 0x1e, 0xff, 0x26, 0x2f, 0x39, 0xff, 0x09, 0x18, 0x28, 0xff, 0x33, 0x49, 0x5e, 0xff, 0x58, 0x71, 0x86, 0xff, 0x3d, 0x55, 0x6a, 0xff, 0x1f, 0x3a, 0x53, 0xff, 0x28, 0x44, 0x5f, 0xff, 0x34, 0x48, 0x60, 0xff, 0x15, 0x22, 0x36, 0xff, 0x04, 0x0d, 0x1e, 0xff, 0x19, 0x20, 0x2e, 0xff, 0x23, 0x2f, 0x3f, 0xff, 0x22, 0x32, 0x48, 0xff, 0x48, 0x58, 0x69, 0xff, 0x63, 0x6d, 0x76, 0xff, 0x83, 0x80, 0x79, 0xff, 0xc5, 0xb4, 0xa2, 0xff, 0xd1, 0xba, 0x9f, 0xff, 0xba, 0xa8, 0x8a, 0xff, 0xb5, 0xa6, 0x87, 0xff, 0xaf, 0x99, 0x79, 0xff, 0xa7, 0x8d, 0x77, 0xff, 0xa0, 0x88, 0x89, 0xff, 0x92, 0x87, 0x9d, 0xff, 0x8c, 0x87, 0xa8, 0xff, 0x90, 0x88, 0xb2, 0xff, 0x91, 0x8a, 0xc2, 0xff, 0x8f, 0x8b, 0xcf, 0xff, 0x91, 0x8d, 0xd5, 0xff, 0x8e, 0x8d, 0xda, 0xff, 0x8b, 0x8d, 0xdf, 0xff, 0x8d, 0x8e, 0xde, 0xff, 0x8d, 0x8e, 0xdf, 0xff, 0x8c, 0x8e, 0xde, 0xff, 0x8c, 0x8d, 0xde, 0xff, 0x88, 0x8b, 0xdc, 0xb1, 0xaa, 0xaa, 0xff, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa0, 0xa6, 0xba, 0x8d, 0xa3, 0xa3, 0xb0, 0xff, 0xa5, 0x9f, 0xa4, 0xff, 0x9f, 0x96, 0x96, 0xff, 0x98, 0x8b, 0x88, 0xff, 0x8f, 0x81, 0x7e, 0xff, 0x82, 0x7a, 0x7f, 0xff, 0x80, 0x7d, 0x88, 0xff, 0x7f, 0x7e, 0x90, 0xff, 0x7e, 0x81, 0x93, 0xff, 0x80, 0x87, 0x98, 0xff, 0x86, 0x8d, 0x9b, 0xff, 0x94, 0x95, 0x9e, 0xff, 0x9a, 0x97, 0x9c, 0xff, 0x99, 0x99, 0x91, 0xff, 0x9b, 0x99, 0x87, 0xff, 0x9f, 0x96, 0x82, 0xff, 0xa7, 0x98, 0x7e, 0xff, 0xab, 0x9c, 0x7a, 0xff, 0xa9, 0x9a, 0x74, 0xff, 0xb0, 0x98, 0x74, 0xff, 0xaf, 0x9a, 0x85, 0xff, 0xaa, 0xa9, 0xab, 0xff, 0xbb, 0xca, 0xe1, 0xff, 0xd6, 0xef, 0xff, 0xff, 0xd8, 0xf3, 0xff, 0xff, 0xbc, 0xd6, 0xe8, 0xff, 0xb7, 0xd1, 0xe3, 0xff, 0xc1, 0xda, 0xf0, 0xff, 0xc6, 0xe3, 0xfc, 0xff, 0xa7, 0xc9, 0xe5, 0xff, 0x7c, 0xa0, 0xc0, 0xff, 0x74, 0x94, 0xb7, 0xff, 0x62, 0x7d, 0x9d, 0xff, 0x43, 0x59, 0x73, 0xff, 0x1b, 0x2b, 0x3f, 0xff, 0x03, 0x0e, 0x1a, 0xff, 0x00, 0x00, 0x05, 0xff, 0x1b, 0x25, 0x38, 0xff, 0x1b, 0x27, 0x3f, 0xff, 0x00, 0x0b, 0x1f, 0xff, 0x1d, 0x2a, 0x41, 0xff, 0x1c, 0x2d, 0x47, 0xff, 0x15, 0x29, 0x47, 0xff, 0x10, 0x23, 0x3e, 0xff, 0x19, 0x2e, 0x45, 0xff, 0x18, 0x2b, 0x48, 0xff, 0x04, 0x16, 0x35, 0xff, 0x00, 0x13, 0x2d, 0xff, 0x02, 0x14, 0x28, 0xff, 0x0d, 0x20, 0x32, 0xff, 0x18, 0x29, 0x3d, 0xff, 0x0e, 0x18, 0x2e, 0xff, 0x07, 0x0f, 0x23, 0xff, 0x02, 0x0c, 0x19, 0xff, 0x2e, 0x3c, 0x40, 0xff, 0x32, 0x3c, 0x43, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0d, 0x0e, 0x14, 0xff, 0x3f, 0x42, 0x48, 0xff, 0x22, 0x27, 0x2e, 0xff, 0x00, 0x00, 0x09, 0xff, 0x25, 0x28, 0x31, 0xff, 0x0a, 0x0f, 0x16, 0xff, 0x13, 0x20, 0x2e, 0xff, 0x53, 0x65, 0x78, 0xff, 0x29, 0x41, 0x56, 0xff, 0x27, 0x40, 0x53, 0xff, 0x1d, 0x39, 0x52, 0xff, 0x0e, 0x29, 0x46, 0xff, 0x14, 0x26, 0x3b, 0xff, 0x1f, 0x28, 0x37, 0xff, 0x0b, 0x0e, 0x19, 0xff, 0x0b, 0x0c, 0x15, 0xff, 0x15, 0x17, 0x1c, 0xff, 0x22, 0x29, 0x30, 0xff, 0x12, 0x27, 0x3b, 0xff, 0x56, 0x6d, 0x85, 0xff, 0x69, 0x72, 0x7d, 0xff, 0x7c, 0x70, 0x69, 0xff, 0xc0, 0xaa, 0x96, 0xff, 0xc4, 0xb1, 0x99, 0xff, 0xb2, 0xa5, 0x8a, 0xff, 0xae, 0x9b, 0x7c, 0xff, 0xaa, 0x92, 0x7d, 0xff, 0xa2, 0x8a, 0x90, 0xff, 0x96, 0x8a, 0xa2, 0xff, 0x91, 0x8d, 0xad, 0xff, 0x93, 0x8c, 0xb6, 0xff, 0x92, 0x8d, 0xc5, 0xff, 0x90, 0x8e, 0xd2, 0xff, 0x8f, 0x8e, 0xd8, 0xff, 0x8d, 0x8e, 0xdc, 0xff, 0x8c, 0x8e, 0xdf, 0xff, 0x8e, 0x8f, 0xdf, 0xff, 0x8e, 0x8f, 0xe0, 0xff, 0x8e, 0x8f, 0xe0, 0xff, 0x8d, 0x8d, 0xe1, 0xff, 0x8a, 0x8c, 0xdf, 0xff, 0x87, 0x89, 0xda, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9c, 0xb1, 0xdb, 0x55, 0x93, 0xa7, 0xc5, 0xfe, 0x97, 0xa0, 0xb5, 0xff, 0x9b, 0xa0, 0xab, 0xff, 0x9c, 0x9a, 0xa8, 0xff, 0x94, 0x8c, 0x9a, 0xff, 0x86, 0x7d, 0x80, 0xff, 0x7b, 0x76, 0x76, 0xff, 0x7d, 0x78, 0x80, 0xff, 0x7d, 0x7b, 0x8c, 0xff, 0x7e, 0x7f, 0x90, 0xff, 0x84, 0x86, 0x93, 0xff, 0x8a, 0x8e, 0x96, 0xff, 0x94, 0x94, 0x99, 0xff, 0x96, 0x95, 0x96, 0xff, 0x9c, 0x98, 0x8c, 0xff, 0xa0, 0x98, 0x85, 0xff, 0xa4, 0x97, 0x81, 0xff, 0xa9, 0x98, 0x7d, 0xff, 0xad, 0x9b, 0x7c, 0xff, 0xaf, 0x9c, 0x7a, 0xff, 0xa7, 0x8d, 0x77, 0xff, 0xb3, 0xb4, 0xae, 0xff, 0xe4, 0xf1, 0xfd, 0xff, 0xee, 0xfa, 0xff, 0xff, 0xc7, 0xeb, 0xff, 0xff, 0xa6, 0xc8, 0xe7, 0xff, 0x90, 0xac, 0xcd, 0xff, 0x70, 0x91, 0xaf, 0xff, 0x53, 0x73, 0x94, 0xff, 0x41, 0x61, 0x83, 0xff, 0x60, 0x7f, 0xa2, 0xff, 0x66, 0x84, 0xa7, 0xff, 0x47, 0x69, 0x8d, 0xff, 0x37, 0x54, 0x76, 0xff, 0x28, 0x38, 0x53, 0xff, 0x1d, 0x26, 0x3c, 0xff, 0x0e, 0x19, 0x2f, 0xff, 0x11, 0x21, 0x39, 0xff, 0x15, 0x28, 0x41, 0xff, 0x03, 0x19, 0x36, 0xff, 0x3c, 0x53, 0x79, 0xff, 0x41, 0x59, 0x85, 0xff, 0x36, 0x50, 0x7c, 0xff, 0x51, 0x6d, 0x97, 0xff, 0x53, 0x74, 0x9e, 0xff, 0x4b, 0x70, 0x9b, 0xff, 0x45, 0x67, 0x92, 0xff, 0x48, 0x69, 0x94, 0xff, 0x46, 0x65, 0x8d, 0xff, 0x1d, 0x39, 0x61, 0xff, 0x08, 0x29, 0x4e, 0xff, 0x44, 0x66, 0x88, 0xff, 0x51, 0x6d, 0x90, 0xff, 0x2d, 0x44, 0x61, 0xff, 0x09, 0x18, 0x2b, 0xff, 0x34, 0x46, 0x51, 0xff, 0x23, 0x2c, 0x35, 0xff, 0x09, 0x0b, 0x0f, 0xff, 0x30, 0x31, 0x39, 0xff, 0x20, 0x21, 0x29, 0xff, 0x00, 0x00, 0x00, 0xff, 0x19, 0x1e, 0x23, 0xff, 0x1e, 0x20, 0x27, 0xff, 0x00, 0x00, 0x00, 0xff, 0x41, 0x48, 0x4f, 0xff, 0x5b, 0x69, 0x70, 0xff, 0x08, 0x16, 0x24, 0xff, 0x14, 0x24, 0x38, 0xff, 0x1c, 0x30, 0x47, 0xff, 0x16, 0x2b, 0x40, 0xff, 0x0b, 0x19, 0x29, 0xff, 0x08, 0x0e, 0x19, 0xff, 0x1a, 0x1b, 0x25, 0xff, 0x0b, 0x0b, 0x15, 0xff, 0x00, 0x00, 0x07, 0xff, 0x0e, 0x14, 0x1c, 0xff, 0x02, 0x07, 0x14, 0xff, 0x34, 0x3f, 0x4e, 0xff, 0x75, 0x80, 0x8f, 0xff, 0x5e, 0x68, 0x76, 0xff, 0x73, 0x71, 0x76, 0xff, 0xbc, 0xab, 0xa4, 0xff, 0xc0, 0xac, 0x96, 0xff, 0xb7, 0xa1, 0x79, 0xff, 0xae, 0x9a, 0x7b, 0xff, 0xa3, 0x8e, 0x92, 0xff, 0x9b, 0x8d, 0xa4, 0xff, 0x97, 0x90, 0xae, 0xff, 0x99, 0x8e, 0xbd, 0xff, 0x97, 0x8f, 0xc8, 0xff, 0x94, 0x92, 0xcf, 0xff, 0x92, 0x90, 0xd6, 0xff, 0x8e, 0x8e, 0xdd, 0xff, 0x8e, 0x8f, 0xe0, 0xff, 0x90, 0x90, 0xe0, 0xff, 0x8d, 0x8f, 0xe0, 0xff, 0x8c, 0x8f, 0xe0, 0xff, 0x8b, 0x8e, 0xe0, 0xff, 0x8a, 0x8c, 0xde, 0xff, 0x87, 0x8b, 0xdb, 0xfe, 0x87, 0x8a, 0xdb, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0xba, 0xf2, 0x29, 0x8e, 0xb4, 0xe2, 0xf3, 0x8e, 0xae, 0xd1, 0xff, 0x91, 0xa2, 0xbe, 0xff, 0x93, 0x9f, 0xb1, 0xff, 0x94, 0x9a, 0xb0, 0xff, 0x8f, 0x8c, 0xa1, 0xff, 0x80, 0x7c, 0x84, 0xff, 0x75, 0x71, 0x72, 0xff, 0x76, 0x71, 0x7c, 0xff, 0x79, 0x77, 0x88, 0xff, 0x7c, 0x7d, 0x8c, 0xff, 0x83, 0x85, 0x90, 0xff, 0x89, 0x8b, 0x94, 0xff, 0x91, 0x92, 0x94, 0xff, 0x96, 0x93, 0x91, 0xff, 0x9c, 0x95, 0x8a, 0xff, 0xa0, 0x96, 0x85, 0xff, 0xa5, 0x97, 0x81, 0xff, 0xa9, 0x99, 0x7e, 0xff, 0xac, 0x99, 0x7c, 0xff, 0xaa, 0x95, 0x74, 0xff, 0xad, 0xa4, 0x9e, 0xff, 0xac, 0xc7, 0xd9, 0xff, 0x8c, 0xa5, 0xbb, 0xff, 0x7d, 0x89, 0x9b, 0xff, 0x51, 0x6e, 0x78, 0xff, 0x49, 0x5d, 0x6b, 0xff, 0x54, 0x6a, 0x85, 0xff, 0x4e, 0x6e, 0x8e, 0xff, 0x42, 0x5f, 0x7d, 0xff, 0x28, 0x43, 0x64, 0xff, 0x12, 0x28, 0x4b, 0xff, 0x21, 0x38, 0x5a, 0xff, 0x2e, 0x47, 0x68, 0xff, 0x2d, 0x46, 0x68, 0xff, 0x15, 0x2a, 0x4d, 0xff, 0x00, 0x0e, 0x33, 0xff, 0x00, 0x0f, 0x33, 0xff, 0x34, 0x4e, 0x71, 0xff, 0x4c, 0x69, 0x87, 0xff, 0x7a, 0x9a, 0xbc, 0xff, 0x9f, 0xbf, 0xed, 0xff, 0x87, 0xa9, 0xdd, 0xff, 0x7f, 0xa5, 0xd7, 0xff, 0x8c, 0xb2, 0xe0, 0xff, 0x7b, 0xa2, 0xd8, 0xff, 0x64, 0x8d, 0xc9, 0xff, 0x71, 0x98, 0xd2, 0xff, 0x7e, 0xa4, 0xdf, 0xff, 0x71, 0x96, 0xd1, 0xff, 0x73, 0x97, 0xd4, 0xff, 0x4c, 0x70, 0xa6, 0xff, 0x4b, 0x6f, 0xa0, 0xff, 0x4a, 0x6d, 0x9f, 0xff, 0x47, 0x66, 0x94, 0xff, 0x2c, 0x46, 0x6d, 0xff, 0x21, 0x36, 0x59, 0xff, 0x12, 0x20, 0x38, 0xff, 0x1d, 0x2a, 0x3d, 0xff, 0x1e, 0x24, 0x35, 0xff, 0x00, 0x00, 0x00, 0xff, 0x10, 0x13, 0x1e, 0xff, 0x32, 0x39, 0x42, 0xff, 0x0e, 0x12, 0x1c, 0xff, 0x18, 0x1b, 0x23, 0xff, 0x3f, 0x45, 0x4d, 0xff, 0x0f, 0x17, 0x1b, 0xff, 0x05, 0x0b, 0x14, 0xff, 0x12, 0x1b, 0x29, 0xff, 0x11, 0x1f, 0x32, 0xff, 0x11, 0x21, 0x35, 0xff, 0x0e, 0x19, 0x27, 0xff, 0x07, 0x0c, 0x16, 0xff, 0x0e, 0x0f, 0x18, 0xff, 0x10, 0x13, 0x1c, 0xff, 0x08, 0x0a, 0x13, 0xff, 0x02, 0x05, 0x0c, 0xff, 0x01, 0x03, 0x0b, 0xff, 0x0d, 0x11, 0x1b, 0xff, 0x4e, 0x56, 0x63, 0xff, 0x63, 0x71, 0x85, 0xff, 0x59, 0x65, 0x73, 0xff, 0x8f, 0x8c, 0x8f, 0xff, 0xc9, 0xb6, 0xa8, 0xff, 0xbc, 0xa1, 0x7f, 0xff, 0xb2, 0x9b, 0x7e, 0xff, 0xa6, 0x91, 0x91, 0xff, 0x9d, 0x91, 0xa4, 0xff, 0x9a, 0x91, 0xb1, 0xff, 0x9d, 0x8f, 0xc0, 0xff, 0x99, 0x90, 0xc9, 0xff, 0x95, 0x94, 0xce, 0xff, 0x94, 0x91, 0xd6, 0xff, 0x8f, 0x8e, 0xdf, 0xff, 0x8e, 0x90, 0xe0, 0xff, 0x90, 0x90, 0xe0, 0xff, 0x8e, 0x90, 0xe0, 0xff, 0x8d, 0x90, 0xe1, 0xff, 0x8c, 0x8f, 0xe0, 0xff, 0x8b, 0x8e, 0xdf, 0xff, 0x8a, 0x8d, 0xde, 0xff, 0x87, 0x8c, 0xdc, 0xf4, 0x85, 0x8b, 0xda, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0xb6, 0xfe, 0x07, 0x78, 0xba, 0xf6, 0xd1, 0x7a, 0xb6, 0xec, 0xff, 0x84, 0xb3, 0xe0, 0xff, 0x84, 0xa3, 0xca, 0xff, 0x82, 0x9b, 0xba, 0xff, 0x85, 0x97, 0xb7, 0xff, 0x85, 0x8d, 0xa8, 0xff, 0x78, 0x7a, 0x86, 0xff, 0x6c, 0x6c, 0x70, 0xff, 0x6f, 0x6d, 0x78, 0xff, 0x74, 0x73, 0x80, 0xff, 0x79, 0x79, 0x85, 0xff, 0x80, 0x81, 0x8c, 0xff, 0x84, 0x87, 0x8e, 0xff, 0x8d, 0x8e, 0x8f, 0xff, 0x92, 0x8f, 0x8d, 0xff, 0x96, 0x90, 0x86, 0xff, 0x9a, 0x91, 0x83, 0xff, 0xa1, 0x95, 0x81, 0xff, 0xa7, 0x98, 0x80, 0xff, 0xa5, 0x96, 0x7b, 0xff, 0xae, 0x9b, 0x7c, 0xff, 0xc0, 0xc3, 0xc6, 0xff, 0x62, 0x8a, 0xa7, 0xff, 0x09, 0x27, 0x45, 0xff, 0x21, 0x29, 0x3c, 0xff, 0x11, 0x23, 0x2d, 0xff, 0x1a, 0x24, 0x2f, 0xff, 0x34, 0x43, 0x5b, 0xff, 0x1c, 0x36, 0x52, 0xff, 0x10, 0x26, 0x40, 0xff, 0x18, 0x2b, 0x46, 0xff, 0x21, 0x32, 0x50, 0xff, 0x11, 0x24, 0x43, 0xff, 0x09, 0x18, 0x34, 0xff, 0x01, 0x14, 0x36, 0xff, 0x05, 0x23, 0x51, 0xff, 0x0f, 0x32, 0x67, 0xff, 0x3c, 0x5b, 0x8c, 0xff, 0x7d, 0x97, 0xbf, 0xff, 0xb5, 0xcf, 0xea, 0xff, 0xd0, 0xee, 0xff, 0xff, 0xb3, 0xd5, 0xfb, 0xff, 0xae, 0xd4, 0xff, 0xff, 0xaa, 0xd1, 0xfd, 0xff, 0x9e, 0xc5, 0xed, 0xff, 0x9f, 0xc3, 0xf2, 0xff, 0x93, 0xb6, 0xed, 0xff, 0x9f, 0xc3, 0xf9, 0xff, 0x9f, 0xc4, 0xfa, 0xff, 0x80, 0xa8, 0xe1, 0xff, 0x8c, 0xb5, 0xf0, 0xff, 0x97, 0xbb, 0xf5, 0xff, 0x67, 0x89, 0xc2, 0xff, 0x50, 0x75, 0xb0, 0xff, 0x49, 0x6d, 0xa8, 0xff, 0x37, 0x58, 0x90, 0xff, 0x35, 0x53, 0x8a, 0xff, 0x24, 0x3f, 0x68, 0xff, 0x1d, 0x34, 0x51, 0xff, 0x3d, 0x4e, 0x6c, 0xff, 0x28, 0x31, 0x49, 0xff, 0x37, 0x42, 0x58, 0xff, 0x04, 0x0e, 0x21, 0xff, 0x06, 0x0f, 0x22, 0xff, 0x2b, 0x35, 0x49, 0xff, 0x16, 0x1e, 0x2d, 0xff, 0x03, 0x08, 0x11, 0xff, 0x15, 0x1d, 0x25, 0xff, 0x11, 0x15, 0x1c, 0xff, 0x04, 0x0d, 0x1c, 0xff, 0x15, 0x20, 0x34, 0xff, 0x13, 0x1b, 0x28, 0xff, 0x0b, 0x11, 0x19, 0xff, 0x05, 0x09, 0x10, 0xff, 0x08, 0x0f, 0x16, 0xff, 0x0a, 0x0d, 0x11, 0xff, 0x0d, 0x0c, 0x0c, 0xff, 0x0b, 0x0d, 0x12, 0xff, 0x00, 0x02, 0x0c, 0xff, 0x0d, 0x15, 0x20, 0xff, 0x3a, 0x42, 0x52, 0xff, 0x42, 0x55, 0x65, 0xff, 0x54, 0x66, 0x72, 0xff, 0xa3, 0x98, 0x96, 0xff, 0xc8, 0xad, 0x99, 0xff, 0xb5, 0x9a, 0x85, 0xff, 0xa6, 0x96, 0x8e, 0xff, 0x9e, 0x95, 0xa2, 0xff, 0x9b, 0x92, 0xb4, 0xff, 0x9e, 0x91, 0xc1, 0xff, 0x9b, 0x93, 0xca, 0xff, 0x96, 0x95, 0xcf, 0xff, 0x95, 0x93, 0xd8, 0xff, 0x91, 0x90, 0xdf, 0xff, 0x8f, 0x90, 0xe1, 0xff, 0x90, 0x91, 0xe1, 0xff, 0x8f, 0x91, 0xe1, 0xff, 0x8e, 0x91, 0xe2, 0xff, 0x8d, 0x90, 0xe1, 0xff, 0x8c, 0x8f, 0xe0, 0xff, 0x8b, 0x8e, 0xdf, 0xff, 0x8a, 0x8d, 0xde, 0xff, 0x89, 0x8c, 0xdc, 0xd1, 0x91, 0x91, 0xda, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x54, 0xc0, 0xff, 0x8e, 0x5c, 0xbb, 0xfd, 0xff, 0x61, 0xb9, 0xf6, 0xff, 0x70, 0xb7, 0xec, 0xff, 0x71, 0xa3, 0xd5, 0xff, 0x6e, 0x97, 0xc1, 0xff, 0x74, 0x93, 0xbe, 0xff, 0x7a, 0x8c, 0xaf, 0xff, 0x75, 0x7e, 0x8d, 0xff, 0x67, 0x6b, 0x6f, 0xff, 0x68, 0x67, 0x70, 0xff, 0x6f, 0x6d, 0x77, 0xff, 0x74, 0x75, 0x7c, 0xff, 0x7a, 0x7d, 0x85, 0xff, 0x80, 0x82, 0x87, 0xff, 0x87, 0x86, 0x87, 0xff, 0x8a, 0x88, 0x86, 0xff, 0x8f, 0x8a, 0x81, 0xff, 0x95, 0x8e, 0x80, 0xff, 0x9a, 0x90, 0x7e, 0xff, 0x9f, 0x91, 0x7b, 0xff, 0xa1, 0x95, 0x7e, 0xff, 0xb2, 0xa3, 0x8a, 0xff, 0xbb, 0xb6, 0xb1, 0xff, 0x6a, 0x85, 0x93, 0xff, 0x29, 0x44, 0x5b, 0xff, 0x3c, 0x49, 0x62, 0xff, 0x4a, 0x68, 0x7d, 0xff, 0x5b, 0x71, 0x8c, 0xff, 0x47, 0x58, 0x75, 0xff, 0x23, 0x38, 0x52, 0xff, 0x15, 0x25, 0x3d, 0xff, 0x1e, 0x2d, 0x43, 0xff, 0x1f, 0x2f, 0x4a, 0xff, 0x0c, 0x1d, 0x3b, 0xff, 0x00, 0x0a, 0x2a, 0xff, 0x0e, 0x22, 0x48, 0xff, 0x42, 0x62, 0x92, 0xff, 0x71, 0x95, 0xc8, 0xff, 0x9d, 0xbb, 0xe7, 0xff, 0xc1, 0xd7, 0xf8, 0xff, 0xe8, 0xf6, 0xff, 0xff, 0xe9, 0xf9, 0xff, 0xff, 0xc7, 0xe1, 0xf6, 0xff, 0xbe, 0xde, 0xf8, 0xff, 0xbf, 0xdd, 0xf8, 0xff, 0xc8, 0xe4, 0xfd, 0xff, 0xd1, 0xed, 0xff, 0xff, 0xbe, 0xdd, 0xf8, 0xff, 0xb8, 0xd7, 0xf5, 0xff, 0xc3, 0xe4, 0xff, 0xff, 0xb8, 0xdb, 0xff, 0xff, 0x9f, 0xc4, 0xed, 0xff, 0x9c, 0xc1, 0xf0, 0xff, 0x9a, 0xbf, 0xf3, 0xff, 0x76, 0x9e, 0xd4, 0xff, 0x63, 0x8c, 0xc3, 0xff, 0x57, 0x80, 0xb4, 0xff, 0x5a, 0x80, 0xb4, 0xff, 0x5a, 0x7e, 0xac, 0xff, 0x50, 0x70, 0x99, 0xff, 0x64, 0x7f, 0xa7, 0xff, 0x39, 0x4f, 0x75, 0xff, 0x20, 0x33, 0x55, 0xff, 0x0e, 0x22, 0x41, 0xff, 0x13, 0x26, 0x45, 0xff, 0x25, 0x36, 0x52, 0xff, 0x14, 0x23, 0x3d, 0xff, 0x0b, 0x16, 0x2b, 0xff, 0x0b, 0x12, 0x1f, 0xff, 0x15, 0x1a, 0x24, 0xff, 0x11, 0x18, 0x26, 0xff, 0x18, 0x22, 0x34, 0xff, 0x0a, 0x11, 0x1c, 0xff, 0x08, 0x0b, 0x12, 0xff, 0x08, 0x0d, 0x11, 0xff, 0x05, 0x0b, 0x11, 0xff, 0x09, 0x09, 0x0b, 0xff, 0x0d, 0x0b, 0x09, 0xff, 0x07, 0x08, 0x0a, 0xff, 0x06, 0x08, 0x0f, 0xff, 0x00, 0x00, 0x07, 0xff, 0x11, 0x15, 0x21, 0xff, 0x21, 0x33, 0x3f, 0xff, 0x00, 0x08, 0x13, 0xff, 0x53, 0x4f, 0x56, 0xff, 0xcf, 0xbd, 0xb3, 0xff, 0xae, 0x9b, 0x8c, 0xff, 0xa9, 0x98, 0x90, 0xff, 0xa1, 0x97, 0xa2, 0xff, 0x9c, 0x94, 0xb3, 0xff, 0x9e, 0x92, 0xc0, 0xff, 0x9a, 0x93, 0xc8, 0xff, 0x96, 0x95, 0xcd, 0xff, 0x94, 0x93, 0xd6, 0xff, 0x91, 0x91, 0xdf, 0xff, 0x8f, 0x91, 0xe2, 0xff, 0x91, 0x91, 0xe1, 0xff, 0x90, 0x92, 0xe2, 0xff, 0x8e, 0x91, 0xe2, 0xff, 0x8e, 0x91, 0xe2, 0xff, 0x8c, 0x8f, 0xe0, 0xff, 0x8b, 0x8e, 0xdf, 0xff, 0x8b, 0x8e, 0xdf, 0xff, 0x8c, 0x8e, 0xdf, 0xff, 0x8c, 0x8e, 0xde, 0x8f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3b, 0xc3, 0xff, 0x40, 0x40, 0xbe, 0xff, 0xfe, 0x45, 0xbb, 0xff, 0xff, 0x4b, 0xbb, 0xfd, 0xff, 0x5f, 0xbb, 0xf7, 0xff, 0x5f, 0xa4, 0xdc, 0xff, 0x61, 0x96, 0xc8, 0xff, 0x6b, 0x93, 0xc5, 0xff, 0x71, 0x8a, 0xb1, 0xff, 0x72, 0x7e, 0x8f, 0xff, 0x68, 0x6d, 0x6e, 0xff, 0x6a, 0x67, 0x6c, 0xff, 0x6f, 0x6d, 0x73, 0xff, 0x6f, 0x71, 0x76, 0xff, 0x75, 0x77, 0x7d, 0xff, 0x7d, 0x7f, 0x83, 0xff, 0x84, 0x82, 0x82, 0xff, 0x87, 0x82, 0x80, 0xff, 0x87, 0x83, 0x7c, 0xff, 0x8d, 0x87, 0x7b, 0xff, 0x93, 0x89, 0x79, 0xff, 0x97, 0x8c, 0x79, 0xff, 0x99, 0x8f, 0x7b, 0xff, 0x9d, 0x96, 0x80, 0xff, 0xa4, 0x92, 0x84, 0xff, 0x9c, 0xa4, 0xa4, 0xff, 0x91, 0xa3, 0xb3, 0xff, 0x67, 0x73, 0x89, 0xff, 0x2b, 0x4d, 0x63, 0xff, 0x3e, 0x5b, 0x7d, 0xff, 0x29, 0x40, 0x60, 0xff, 0x13, 0x2a, 0x43, 0xff, 0x1e, 0x2e, 0x44, 0xff, 0x27, 0x36, 0x4b, 0xff, 0x11, 0x19, 0x33, 0xff, 0x00, 0x00, 0x1a, 0xff, 0x26, 0x3f, 0x6a, 0xff, 0x88, 0xa5, 0xd4, 0xff, 0xa7, 0xc4, 0xec, 0xff, 0xb8, 0xd2, 0xf2, 0xff, 0xd3, 0xe4, 0xf7, 0xff, 0xf2, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf9, 0xf9, 0xff, 0xd5, 0xe8, 0xf0, 0xff, 0xca, 0xe3, 0xf2, 0xff, 0xd2, 0xea, 0xf9, 0xff, 0xdd, 0xee, 0xfc, 0xff, 0xd4, 0xed, 0xf7, 0xff, 0xcc, 0xee, 0xf8, 0xff, 0xc7, 0xe6, 0xf6, 0xff, 0xc2, 0xe0, 0xf9, 0xff, 0xbb, 0xd9, 0xf9, 0xff, 0xb6, 0xd2, 0xf6, 0xff, 0xa6, 0xc7, 0xf1, 0xff, 0x9f, 0xc5, 0xf2, 0xff, 0x91, 0xb8, 0xe6, 0xff, 0x83, 0xaa, 0xd8, 0xff, 0x74, 0x9a, 0xc6, 0xff, 0x89, 0xad, 0xd7, 0xff, 0x77, 0x9b, 0xc8, 0xff, 0x5f, 0x84, 0xb3, 0xff, 0x63, 0x82, 0xb1, 0xff, 0x37, 0x51, 0x7f, 0xff, 0x2f, 0x48, 0x74, 0xff, 0x2e, 0x49, 0x74, 0xff, 0x27, 0x43, 0x6b, 0xff, 0x27, 0x40, 0x66, 0xff, 0x1d, 0x33, 0x59, 0xff, 0x19, 0x2a, 0x4d, 0xff, 0x18, 0x26, 0x3f, 0xff, 0x12, 0x1f, 0x32, 0xff, 0x18, 0x26, 0x36, 0xff, 0x1c, 0x27, 0x35, 0xff, 0x0f, 0x16, 0x20, 0xff, 0x05, 0x07, 0x0d, 0xff, 0x06, 0x06, 0x0a, 0xff, 0x04, 0x05, 0x09, 0xff, 0x07, 0x08, 0x0b, 0xff, 0x05, 0x04, 0x07, 0xff, 0x05, 0x06, 0x0a, 0xff, 0x05, 0x07, 0x0a, 0xff, 0x01, 0x04, 0x07, 0xff, 0x08, 0x09, 0x0e, 0xff, 0x08, 0x0f, 0x12, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x01, 0x06, 0xff, 0x86, 0x86, 0x83, 0xff, 0xc0, 0xb8, 0xaf, 0xff, 0xad, 0x99, 0x98, 0xff, 0xa3, 0x94, 0xa2, 0xff, 0x9a, 0x93, 0xb1, 0xff, 0x9e, 0x91, 0xc0, 0xff, 0x9b, 0x93, 0xc9, 0xff, 0x95, 0x95, 0xcd, 0xff, 0x93, 0x93, 0xd5, 0xff, 0x91, 0x91, 0xdf, 0xff, 0x90, 0x91, 0xe3, 0xff, 0x92, 0x92, 0xe2, 0xff, 0x90, 0x92, 0xe2, 0xff, 0x8e, 0x92, 0xe3, 0xff, 0x8e, 0x91, 0xe2, 0xff, 0x8d, 0x90, 0xe1, 0xff, 0x8c, 0x8f, 0xe0, 0xff, 0x8c, 0x8f, 0xe0, 0xff, 0x8d, 0x8f, 0xe0, 0xff, 0x8d, 0x8e, 0xdf, 0xfe, 0x8d, 0x8d, 0xdf, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1c, 0xc6, 0xff, 0x09, 0x30, 0xc5, 0xff, 0xdb, 0x38, 0xbf, 0xff, 0xff, 0x3c, 0xbd, 0xff, 0xff, 0x41, 0xbd, 0xff, 0xff, 0x52, 0xbb, 0xf9, 0xff, 0x54, 0xa8, 0xe1, 0xff, 0x5f, 0x9c, 0xd1, 0xff, 0x6c, 0x99, 0xce, 0xff, 0x71, 0x8d, 0xb6, 0xff, 0x70, 0x7d, 0x8d, 0xff, 0x6c, 0x6e, 0x6e, 0xff, 0x76, 0x70, 0x70, 0xff, 0x76, 0x74, 0x77, 0xff, 0x74, 0x75, 0x7a, 0xff, 0x74, 0x74, 0x7a, 0xff, 0x78, 0x79, 0x7e, 0xff, 0x82, 0x7f, 0x80, 0xff, 0x85, 0x80, 0x7e, 0xff, 0x84, 0x7f, 0x78, 0xff, 0x88, 0x81, 0x75, 0xff, 0x8c, 0x85, 0x75, 0xff, 0x92, 0x88, 0x77, 0xff, 0x90, 0x88, 0x75, 0xff, 0x92, 0x8b, 0x78, 0xff, 0x96, 0x81, 0x74, 0xff, 0x96, 0x9a, 0x9a, 0xff, 0xbc, 0xcb, 0xda, 0xff, 0xc4, 0xcc, 0xde, 0xff, 0x87, 0xa5, 0xb3, 0xff, 0x47, 0x60, 0x75, 0xff, 0x1b, 0x35, 0x54, 0xff, 0x12, 0x2e, 0x4b, 0xff, 0x1f, 0x31, 0x47, 0xff, 0x00, 0x0a, 0x1f, 0xff, 0x00, 0x00, 0x16, 0xff, 0x4a, 0x5f, 0x80, 0xff, 0x99, 0xbd, 0xf2, 0xff, 0xa2, 0xc7, 0xfc, 0xff, 0xb5, 0xd0, 0xee, 0xff, 0xe6, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfe, 0xf9, 0xff, 0xf2, 0xf6, 0xf8, 0xff, 0xdb, 0xeb, 0xf4, 0xff, 0xd6, 0xee, 0xfd, 0xff, 0xd7, 0xf0, 0xff, 0xff, 0xdc, 0xeb, 0xf8, 0xff, 0xc9, 0xe6, 0xf3, 0xff, 0xbd, 0xe3, 0xf5, 0xff, 0xbc, 0xdd, 0xf5, 0xff, 0xb7, 0xd4, 0xf6, 0xff, 0xb1, 0xcd, 0xf8, 0xff, 0xb2, 0xcc, 0xfe, 0xff, 0xa7, 0xc3, 0xf3, 0xff, 0x9c, 0xbe, 0xeb, 0xff, 0x9f, 0xc1, 0xef, 0xff, 0xa1, 0xc2, 0xf1, 0xff, 0x86, 0xa5, 0xd2, 0xff, 0x9d, 0xb7, 0xdf, 0xff, 0x80, 0xa1, 0xcc, 0xff, 0x69, 0x8e, 0xbd, 0xff, 0x7e, 0x9e, 0xce, 0xff, 0x5c, 0x7a, 0xac, 0xff, 0x46, 0x63, 0x96, 0xff, 0x40, 0x5f, 0x94, 0xff, 0x38, 0x58, 0x88, 0xff, 0x2e, 0x4c, 0x78, 0xff, 0x2c, 0x46, 0x75, 0xff, 0x2e, 0x45, 0x72, 0xff, 0x20, 0x34, 0x5a, 0xff, 0x0d, 0x22, 0x3f, 0xff, 0x10, 0x23, 0x36, 0xff, 0x0f, 0x1c, 0x28, 0xff, 0x08, 0x0f, 0x17, 0xff, 0x09, 0x0b, 0x10, 0xff, 0x0b, 0x08, 0x0b, 0xff, 0x07, 0x01, 0x04, 0xff, 0x05, 0x04, 0x0a, 0xff, 0x00, 0x04, 0x09, 0xff, 0x00, 0x02, 0x05, 0xff, 0x01, 0x02, 0x03, 0xff, 0x07, 0x07, 0x06, 0xff, 0x0a, 0x09, 0x09, 0xff, 0x0d, 0x0a, 0x06, 0xff, 0x06, 0x07, 0x05, 0xff, 0x00, 0x00, 0x00, 0xff, 0x08, 0x10, 0x0e, 0xff, 0x8c, 0x8e, 0x8a, 0xff, 0xc1, 0xaa, 0xb0, 0xff, 0xae, 0x9b, 0xac, 0xff, 0x9a, 0x94, 0xb1, 0xff, 0x9c, 0x90, 0xbf, 0xff, 0x9a, 0x93, 0xc8, 0xff, 0x95, 0x94, 0xcd, 0xff, 0x92, 0x92, 0xd5, 0xff, 0x91, 0x92, 0xdf, 0xff, 0x91, 0x92, 0xe4, 0xff, 0x92, 0x93, 0xe3, 0xff, 0x91, 0x93, 0xe3, 0xff, 0x8f, 0x92, 0xe3, 0xff, 0x8f, 0x92, 0xe3, 0xff, 0x8e, 0x91, 0xe2, 0xff, 0x8e, 0x91, 0xe2, 0xff, 0x8d, 0x90, 0xe1, 0xff, 0x8e, 0x90, 0xe1, 0xff, 0x8f, 0x90, 0xe1, 0xff, 0x8f, 0x90, 0xe0, 0xdb, 0x8d, 0x8d, 0xe2, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1a, 0xc4, 0xff, 0x88, 0x26, 0xc6, 0xff, 0xff, 0x34, 0xc2, 0xfc, 0xff, 0x3a, 0xc0, 0xfb, 0xff, 0x3a, 0xbe, 0xff, 0xff, 0x45, 0xbc, 0xfe, 0xff, 0x51, 0xaf, 0xec, 0xff, 0x5f, 0xa3, 0xd8, 0xff, 0x6a, 0x9d, 0xd1, 0xff, 0x6f, 0x8f, 0xb8, 0xff, 0x6c, 0x7b, 0x89, 0xff, 0x70, 0x72, 0x70, 0xff, 0x82, 0x7b, 0x79, 0xff, 0x84, 0x7f, 0x7e, 0xff, 0x81, 0x7d, 0x7d, 0xff, 0x7c, 0x7b, 0x7a, 0xff, 0x7b, 0x7a, 0x79, 0xff, 0x82, 0x7f, 0x7c, 0xff, 0x86, 0x81, 0x7f, 0xff, 0x88, 0x80, 0x78, 0xff, 0x89, 0x7f, 0x76, 0xff, 0x87, 0x7f, 0x77, 0xff, 0x8a, 0x84, 0x78, 0xff, 0x8d, 0x86, 0x76, 0xff, 0x8c, 0x82, 0x6d, 0xff, 0x8d, 0x8b, 0x89, 0xff, 0xa7, 0xb2, 0xbe, 0xff, 0xaa, 0xbe, 0xd0, 0xff, 0xaf, 0xc6, 0xe0, 0xff, 0x91, 0xae, 0xcc, 0xff, 0x42, 0x60, 0x7d, 0xff, 0x1a, 0x35, 0x5e, 0xff, 0x19, 0x2e, 0x50, 0xff, 0x07, 0x17, 0x2b, 0xff, 0x00, 0x05, 0x1e, 0xff, 0x5b, 0x7a, 0xa2, 0xff, 0xaa, 0xcc, 0xff, 0xff, 0xba, 0xd3, 0xf7, 0xff, 0xd2, 0xe2, 0xf2, 0xff, 0xf0, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xfd, 0xfd, 0xf8, 0xff, 0xfc, 0xfd, 0xfa, 0xff, 0xf1, 0xf7, 0xf6, 0xff, 0xe9, 0xf6, 0xfe, 0xff, 0xd9, 0xed, 0xfb, 0xff, 0xd2, 0xe8, 0xf9, 0xff, 0xd5, 0xe9, 0xf7, 0xff, 0xcb, 0xe5, 0xf9, 0xff, 0xba, 0xd8, 0xf5, 0xff, 0xb4, 0xd2, 0xf4, 0xff, 0xaf, 0xcd, 0xf6, 0xff, 0xa8, 0xc4, 0xf3, 0xff, 0xa9, 0xc4, 0xf4, 0xff, 0xa6, 0xc2, 0xec, 0xff, 0xa7, 0xc5, 0xed, 0xff, 0x9d, 0xbd, 0xea, 0xff, 0x98, 0xb9, 0xe8, 0xff, 0x9a, 0xb6, 0xe2, 0xff, 0x9d, 0xb3, 0xd9, 0xff, 0x8e, 0xa9, 0xcf, 0xff, 0x94, 0xb2, 0xdc, 0xff, 0x8f, 0xad, 0xd6, 0xff, 0x72, 0x90, 0xbc, 0xff, 0x65, 0x84, 0xb2, 0xff, 0x54, 0x73, 0xa5, 0xff, 0x4d, 0x6b, 0x9a, 0xff, 0x48, 0x65, 0x93, 0xff, 0x40, 0x5d, 0x90, 0xff, 0x32, 0x4c, 0x7f, 0xff, 0x2c, 0x43, 0x70, 0xff, 0x1c, 0x34, 0x58, 0xff, 0x10, 0x22, 0x3a, 0xff, 0x07, 0x11, 0x20, 0xff, 0x02, 0x07, 0x12, 0xff, 0x0c, 0x0d, 0x13, 0xff, 0x0d, 0x0d, 0x0f, 0xff, 0x03, 0x05, 0x05, 0xff, 0x03, 0x05, 0x08, 0xff, 0x05, 0x08, 0x09, 0xff, 0x03, 0x06, 0x06, 0xff, 0x02, 0x03, 0x04, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x05, 0x05, 0xff, 0x0b, 0x09, 0x0b, 0xff, 0x06, 0x05, 0x08, 0xff, 0x04, 0x05, 0x0a, 0xff, 0x09, 0x09, 0x0c, 0xff, 0x35, 0x38, 0x3c, 0xff, 0x8a, 0x8b, 0x8e, 0xff, 0xac, 0xa7, 0xb6, 0xff, 0xa5, 0x9e, 0xbc, 0xff, 0x9d, 0x98, 0xbd, 0xff, 0x99, 0x97, 0xc7, 0xff, 0x96, 0x95, 0xd1, 0xff, 0x96, 0x94, 0xd7, 0xff, 0x95, 0x93, 0xdb, 0xff, 0x93, 0x93, 0xe0, 0xff, 0x92, 0x92, 0xe3, 0xff, 0x91, 0x91, 0xe6, 0xff, 0x90, 0x91, 0xe7, 0xff, 0x8f, 0x91, 0xe7, 0xff, 0x8d, 0x92, 0xe5, 0xff, 0x8d, 0x90, 0xe4, 0xff, 0x8f, 0x91, 0xe3, 0xff, 0x8f, 0x92, 0xe2, 0xff, 0x91, 0x91, 0xe1, 0xff, 0x92, 0x92, 0xe0, 0xff, 0x90, 0x90, 0xdf, 0x88, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0xbb, 0xf8, 0x26, 0x0b, 0xc0, 0xfd, 0xfa, 0x1b, 0xc6, 0xff, 0xff, 0x30, 0xc8, 0xfe, 0xff, 0x37, 0xc4, 0xfc, 0xff, 0x37, 0xc1, 0xff, 0xff, 0x49, 0xc1, 0xff, 0xff, 0x59, 0xb3, 0xf1, 0xff, 0x62, 0xa5, 0xd8, 0xff, 0x6a, 0x9e, 0xd0, 0xff, 0x70, 0x90, 0xb7, 0xff, 0x75, 0x83, 0x90, 0xff, 0x7e, 0x7f, 0x79, 0xff, 0x8b, 0x83, 0x80, 0xff, 0x91, 0x88, 0x86, 0xff, 0x8d, 0x88, 0x84, 0xff, 0x88, 0x84, 0x80, 0xff, 0x81, 0x7f, 0x7a, 0xff, 0x83, 0x80, 0x7c, 0xff, 0x8d, 0x86, 0x83, 0xff, 0x8c, 0x86, 0x7e, 0xff, 0x87, 0x80, 0x76, 0xff, 0x8b, 0x81, 0x74, 0xff, 0x8c, 0x83, 0x75, 0xff, 0x81, 0x7a, 0x70, 0xff, 0x84, 0x80, 0x79, 0xff, 0xae, 0xb5, 0xb9, 0xff, 0xcf, 0xdc, 0xea, 0xff, 0xbb, 0xcf, 0xe3, 0xff, 0x97, 0xb5, 0xd1, 0xff, 0x76, 0x93, 0xb7, 0xff, 0x41, 0x62, 0x85, 0xff, 0x22, 0x39, 0x5a, 0xff, 0x09, 0x1c, 0x3a, 0xff, 0x07, 0x22, 0x42, 0xff, 0x64, 0x89, 0xb1, 0xff, 0xc0, 0xdf, 0xff, 0xff, 0xd5, 0xe5, 0xfd, 0xff, 0xed, 0xf6, 0xfe, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xfd, 0xf9, 0xff, 0xfe, 0xfc, 0xf9, 0xff, 0xfb, 0xfd, 0xfd, 0xff, 0xf7, 0xfc, 0xfd, 0xff, 0xef, 0xf8, 0xfa, 0xff, 0xe0, 0xee, 0xf6, 0xff, 0xd4, 0xe5, 0xf3, 0xff, 0xd5, 0xe9, 0xf7, 0xff, 0xd1, 0xe5, 0xf2, 0xff, 0xca, 0xe1, 0xf4, 0xff, 0xbd, 0xd7, 0xf3, 0xff, 0xb6, 0xd3, 0xf3, 0xff, 0xb5, 0xd1, 0xf9, 0xff, 0xae, 0xc9, 0xf5, 0xff, 0xaf, 0xc8, 0xf4, 0xff, 0xae, 0xca, 0xf3, 0xff, 0xa7, 0xc6, 0xee, 0xff, 0x9b, 0xba, 0xe6, 0xff, 0x96, 0xb6, 0xe3, 0xff, 0x97, 0xb2, 0xde, 0xff, 0xa0, 0xb5, 0xdd, 0xff, 0xa6, 0xbd, 0xe3, 0xff, 0xa4, 0xbe, 0xe3, 0xff, 0x98, 0xb3, 0xda, 0xff, 0x91, 0xac, 0xd4, 0xff, 0x85, 0xa1, 0xcb, 0xff, 0x77, 0x94, 0xc0, 0xff, 0x64, 0x80, 0xab, 0xff, 0x54, 0x71, 0x9e, 0xff, 0x53, 0x71, 0xa1, 0xff, 0x3e, 0x59, 0x8b, 0xff, 0x2f, 0x47, 0x73, 0xff, 0x30, 0x46, 0x6c, 0xff, 0x0e, 0x21, 0x3a, 0xff, 0x05, 0x11, 0x22, 0xff, 0x10, 0x18, 0x25, 0xff, 0x15, 0x15, 0x1c, 0xff, 0x0c, 0x0c, 0x0f, 0xff, 0x05, 0x08, 0x08, 0xff, 0x06, 0x09, 0x0a, 0xff, 0x07, 0x09, 0x09, 0xff, 0x0c, 0x0e, 0x0f, 0xff, 0x0a, 0x0c, 0x0d, 0xff, 0x04, 0x06, 0x07, 0xff, 0x01, 0x04, 0x04, 0xff, 0x02, 0x03, 0x05, 0xff, 0x0c, 0x0c, 0x11, 0xff, 0x0f, 0x10, 0x15, 0xff, 0x13, 0x13, 0x18, 0xff, 0x1c, 0x1e, 0x25, 0xff, 0x3a, 0x42, 0x43, 0xff, 0x7b, 0x7b, 0x88, 0xff, 0xad, 0xa7, 0xc3, 0xff, 0xa5, 0xa1, 0xc1, 0xff, 0x9f, 0x9d, 0xc7, 0xff, 0x9e, 0x9b, 0xd2, 0xff, 0x9e, 0x99, 0xd8, 0xff, 0x9a, 0x97, 0xdd, 0xff, 0x96, 0x96, 0xe0, 0xff, 0x95, 0x94, 0xe2, 0xff, 0x93, 0x93, 0xe6, 0xff, 0x90, 0x91, 0xe6, 0xff, 0x8e, 0x91, 0xe4, 0xff, 0x8d, 0x91, 0xe4, 0xff, 0x8e, 0x91, 0xe5, 0xff, 0x8e, 0x92, 0xe4, 0xff, 0x8f, 0x91, 0xe2, 0xff, 0x90, 0x91, 0xe1, 0xff, 0x91, 0x91, 0xe1, 0xff, 0x90, 0x8f, 0xdf, 0xfa, 0x93, 0x93, 0xdd, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbd, 0xfc, 0xb3, 0x00, 0xc3, 0xff, 0xff, 0x08, 0xc6, 0xfe, 0xff, 0x1b, 0xc7, 0xfd, 0xff, 0x28, 0xc6, 0xfd, 0xff, 0x2d, 0xc5, 0xff, 0xff, 0x45, 0xc1, 0xff, 0xff, 0x54, 0xaf, 0xeb, 0xff, 0x5e, 0x9e, 0xd1, 0xff, 0x67, 0x97, 0xc9, 0xff, 0x75, 0x92, 0xb7, 0xff, 0x89, 0x94, 0xa1, 0xff, 0x91, 0x8f, 0x88, 0xff, 0x96, 0x8c, 0x85, 0xff, 0x9b, 0x91, 0x8a, 0xff, 0x99, 0x93, 0x8c, 0xff, 0x94, 0x90, 0x88, 0xff, 0x8b, 0x86, 0x7f, 0xff, 0x89, 0x83, 0x7d, 0xff, 0x93, 0x8a, 0x85, 0xff, 0x8e, 0x8b, 0x85, 0xff, 0x8a, 0x88, 0x7b, 0xff, 0x92, 0x88, 0x73, 0xff, 0x89, 0x7f, 0x6f, 0xff, 0x7f, 0x7d, 0x7c, 0xff, 0xb3, 0xbb, 0xc9, 0xff, 0xda, 0xe4, 0xf2, 0xff, 0xdf, 0xec, 0xf9, 0xff, 0xd2, 0xe5, 0xf9, 0xff, 0xad, 0xc9, 0xe3, 0xff, 0x83, 0xa2, 0xc1, 0xff, 0x52, 0x74, 0x96, 0xff, 0x2d, 0x4b, 0x6d, 0xff, 0x21, 0x3c, 0x5b, 0xff, 0x81, 0x9e, 0xbc, 0xff, 0xdf, 0xfb, 0xff, 0xff, 0xe6, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xfa, 0xfd, 0xff, 0xff, 0xfe, 0xfb, 0xfa, 0xff, 0xff, 0xfd, 0xf8, 0xff, 0xfe, 0xfc, 0xfb, 0xff, 0xfb, 0xfc, 0xfe, 0xff, 0xf2, 0xfb, 0xff, 0xff, 0xe0, 0xec, 0xf5, 0xff, 0xd2, 0xe0, 0xeb, 0xff, 0xdb, 0xeb, 0xf8, 0xff, 0xd8, 0xea, 0xf7, 0xff, 0xd3, 0xe5, 0xf0, 0xff, 0xc9, 0xdf, 0xf0, 0xff, 0xbf, 0xd9, 0xf3, 0xff, 0xb8, 0xd2, 0xf2, 0xff, 0xb6, 0xd1, 0xf7, 0xff, 0xb0, 0xc9, 0xf4, 0xff, 0xb4, 0xcb, 0xf6, 0xff, 0xb0, 0xca, 0xf7, 0xff, 0xa7, 0xc4, 0xf2, 0xff, 0xa3, 0xc0, 0xee, 0xff, 0xa3, 0xbf, 0xec, 0xff, 0x9e, 0xb7, 0xe2, 0xff, 0xa8, 0xbf, 0xe9, 0xff, 0xb9, 0xd1, 0xf7, 0xff, 0xa8, 0xc3, 0xe6, 0xff, 0x92, 0xad, 0xd2, 0xff, 0x9d, 0xb7, 0xdd, 0xff, 0x96, 0xaf, 0xd5, 0xff, 0x90, 0xab, 0xd0, 0xff, 0x83, 0x9d, 0xc5, 0xff, 0x66, 0x83, 0xad, 0xff, 0x5a, 0x77, 0xa2, 0xff, 0x49, 0x65, 0x8f, 0xff, 0x3c, 0x55, 0x7c, 0xff, 0x31, 0x45, 0x6a, 0xff, 0x35, 0x4c, 0x65, 0xff, 0x19, 0x2d, 0x3d, 0xff, 0x02, 0x09, 0x17, 0xff, 0x09, 0x0a, 0x10, 0xff, 0x05, 0x02, 0x07, 0xff, 0x06, 0x04, 0x07, 0xff, 0x05, 0x07, 0x07, 0xff, 0x04, 0x06, 0x07, 0xff, 0x06, 0x08, 0x09, 0xff, 0x09, 0x0b, 0x0c, 0xff, 0x0b, 0x0d, 0x0e, 0xff, 0x07, 0x09, 0x0a, 0xff, 0x00, 0x01, 0x01, 0xff, 0x07, 0x08, 0x0a, 0xff, 0x14, 0x16, 0x1a, 0xff, 0x1e, 0x1e, 0x23, 0xff, 0x08, 0x0a, 0x0f, 0xff, 0x00, 0x00, 0x02, 0xff, 0x17, 0x18, 0x22, 0xff, 0x86, 0x83, 0x97, 0xff, 0xb6, 0xb2, 0xcd, 0xff, 0xa9, 0xa3, 0xc8, 0xff, 0xa8, 0xa0, 0xd1, 0xff, 0xa8, 0x9f, 0xd7, 0xff, 0xa2, 0x9e, 0xdc, 0xff, 0x9d, 0x9d, 0xe3, 0xff, 0x9c, 0x9b, 0xe3, 0xff, 0x98, 0x98, 0xe4, 0xff, 0x96, 0x97, 0xe4, 0xff, 0x92, 0x96, 0xe1, 0xff, 0x91, 0x94, 0xe2, 0xff, 0x91, 0x93, 0xe4, 0xff, 0x8f, 0x92, 0xe2, 0xff, 0x8f, 0x90, 0xe1, 0xff, 0x8f, 0x91, 0xe1, 0xff, 0x90, 0x90, 0xe1, 0xff, 0x91, 0x90, 0xde, 0xff, 0x91, 0x91, 0xdb, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb7, 0xfb, 0x44, 0x00, 0xc2, 0xff, 0xff, 0x00, 0xc4, 0xff, 0xff, 0x00, 0xc5, 0xfe, 0xff, 0x04, 0xc4, 0xf8, 0xff, 0x16, 0xc5, 0xfa, 0xff, 0x1e, 0xc4, 0xff, 0xff, 0x3a, 0xc0, 0xff, 0xff, 0x4d, 0xaf, 0xe8, 0xff, 0x61, 0x9e, 0xd1, 0xff, 0x70, 0x99, 0xcb, 0xff, 0x7c, 0x97, 0xbb, 0xff, 0x92, 0x9d, 0xa4, 0xff, 0x9b, 0x98, 0x8d, 0xff, 0xa1, 0x94, 0x8a, 0xff, 0xa4, 0x99, 0x8b, 0xff, 0xa3, 0x9b, 0x8d, 0xff, 0xa1, 0x9b, 0x8e, 0xff, 0x98, 0x92, 0x86, 0xff, 0x95, 0x8c, 0x82, 0xff, 0xa0, 0x94, 0x8c, 0xff, 0x99, 0x95, 0x8c, 0xff, 0x93, 0x91, 0x82, 0xff, 0x8d, 0x87, 0x76, 0xff, 0x86, 0x82, 0x78, 0xff, 0xac, 0xb2, 0xbc, 0xff, 0xce, 0xe1, 0xfc, 0xff, 0xd6, 0xe7, 0xfb, 0xff, 0xe1, 0xf1, 0xff, 0xff, 0xca, 0xdf, 0xf1, 0xff, 0xb8, 0xd2, 0xea, 0xff, 0x96, 0xb5, 0xd2, 0xff, 0x70, 0x93, 0xb4, 0xff, 0x3e, 0x6d, 0x9e, 0xff, 0x79, 0x9d, 0xc1, 0xff, 0xf3, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xfd, 0xff, 0xf9, 0xfe, 0xfd, 0xff, 0xfd, 0xfd, 0xfb, 0xff, 0xff, 0xfd, 0xf9, 0xff, 0xff, 0xfd, 0xf9, 0xff, 0xff, 0xfd, 0xfa, 0xff, 0xfc, 0xfc, 0xfb, 0xff, 0xfb, 0xfd, 0xfe, 0xff, 0xf1, 0xfb, 0xff, 0xff, 0xe3, 0xf0, 0xf9, 0xff, 0xe3, 0xee, 0xf7, 0xff, 0xe4, 0xf3, 0xfb, 0xff, 0xde, 0xee, 0xfa, 0xff, 0xdb, 0xed, 0xf8, 0xff, 0xce, 0xe5, 0xf6, 0xff, 0xc4, 0xdf, 0xf7, 0xff, 0xbd, 0xd8, 0xf6, 0xff, 0xb6, 0xd3, 0xf7, 0xff, 0xb4, 0xce, 0xf6, 0xff, 0xb4, 0xcd, 0xf5, 0xff, 0xaa, 0xc7, 0xf3, 0xff, 0xa6, 0xc4, 0xf3, 0xff, 0xa6, 0xc0, 0xeb, 0xff, 0xae, 0xc7, 0xf0, 0xff, 0xaf, 0xc8, 0xf0, 0xff, 0xb7, 0xd0, 0xf7, 0xff, 0xbe, 0xd7, 0xfe, 0xff, 0xaa, 0xc3, 0xeb, 0xff, 0x96, 0xb0, 0xd5, 0xff, 0x9e, 0xb9, 0xdc, 0xff, 0xa3, 0xbd, 0xe0, 0xff, 0x9c, 0xb4, 0xd5, 0xff, 0x95, 0xae, 0xd2, 0xff, 0x92, 0xad, 0xd2, 0xff, 0x82, 0x9d, 0xc3, 0xff, 0x58, 0x74, 0x99, 0xff, 0x45, 0x60, 0x82, 0xff, 0x33, 0x4c, 0x6e, 0xff, 0x50, 0x66, 0x7f, 0xff, 0x46, 0x59, 0x6a, 0xff, 0x05, 0x0a, 0x19, 0xff, 0x02, 0x02, 0x0b, 0xff, 0x08, 0x05, 0x0a, 0xff, 0x05, 0x03, 0x07, 0xff, 0x03, 0x04, 0x06, 0xff, 0x02, 0x04, 0x05, 0xff, 0x00, 0x01, 0x02, 0xff, 0x04, 0x06, 0x07, 0xff, 0x06, 0x08, 0x09, 0xff, 0x04, 0x06, 0x07, 0xff, 0x0b, 0x0b, 0x0d, 0xff, 0x08, 0x08, 0x09, 0xff, 0x04, 0x06, 0x08, 0xff, 0x1a, 0x1b, 0x1f, 0xff, 0x2c, 0x2f, 0x33, 0xff, 0x08, 0x0b, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x66, 0x66, 0x71, 0xff, 0xc4, 0xbf, 0xd2, 0xff, 0xb4, 0xaa, 0xc6, 0xff, 0xb1, 0xa8, 0xcf, 0xff, 0xb1, 0xa7, 0xd5, 0xff, 0xab, 0xa5, 0xd9, 0xff, 0xa5, 0xa4, 0xe0, 0xff, 0xa6, 0xa3, 0xe2, 0xff, 0xa2, 0xa0, 0xe2, 0xff, 0xa0, 0x9f, 0xe2, 0xff, 0x9d, 0x9f, 0xe1, 0xff, 0x9b, 0x9c, 0xe0, 0xff, 0x99, 0x99, 0xe1, 0xff, 0x98, 0x97, 0xe0, 0xff, 0x96, 0x95, 0xde, 0xff, 0x94, 0x94, 0xdd, 0xff, 0x92, 0x93, 0xdb, 0xff, 0x93, 0x94, 0xdb, 0xff, 0x94, 0x94, 0xda, 0xff, 0x92, 0x92, 0xd9, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xb8, 0xfd, 0xc8, 0x00, 0xc0, 0xfe, 0xff, 0x00, 0xc3, 0xff, 0xff, 0x00, 0xc6, 0xff, 0xff, 0x0a, 0xc7, 0xfd, 0xff, 0x18, 0xc6, 0xfc, 0xff, 0x21, 0xc4, 0xff, 0xff, 0x3f, 0xc2, 0xff, 0xff, 0x59, 0xb3, 0xec, 0xff, 0x6b, 0xa4, 0xd6, 0xff, 0x7a, 0xa1, 0xd2, 0xff, 0x8b, 0xa2, 0xc4, 0xff, 0x9f, 0xa5, 0xa9, 0xff, 0xa5, 0x9e, 0x91, 0xff, 0xab, 0x9c, 0x8f, 0xff, 0xad, 0x9f, 0x8e, 0xff, 0xab, 0xa1, 0x8e, 0xff, 0xaa, 0xa3, 0x91, 0xff, 0xa0, 0x9a, 0x8a, 0xff, 0x9e, 0x94, 0x86, 0xff, 0xad, 0x9d, 0x93, 0xff, 0xaf, 0xa3, 0x94, 0xff, 0x9d, 0x99, 0x88, 0xff, 0x8c, 0x8c, 0x86, 0xff, 0xa8, 0xb0, 0xb6, 0xff, 0xc9, 0xdb, 0xed, 0xff, 0xbc, 0xd5, 0xf0, 0xff, 0xc6, 0xdc, 0xf0, 0xff, 0xd1, 0xe3, 0xf3, 0xff, 0xca, 0xdf, 0xf2, 0xff, 0xbf, 0xd7, 0xee, 0xff, 0x9d, 0xbd, 0xd9, 0xff, 0x75, 0x9a, 0xbc, 0xff, 0x58, 0x86, 0xb2, 0xff, 0xc6, 0xe3, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xfd, 0xfa, 0xff, 0xf4, 0xfb, 0xff, 0xff, 0xfb, 0xfc, 0xfc, 0xff, 0xff, 0xfd, 0xf8, 0xff, 0xfe, 0xfc, 0xfb, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfd, 0xff, 0xfc, 0xfc, 0xfd, 0xff, 0xf4, 0xf9, 0xfc, 0xff, 0xf0, 0xf9, 0xfc, 0xff, 0xf1, 0xfa, 0xfc, 0xff, 0xe9, 0xf4, 0xf6, 0xff, 0xe3, 0xf1, 0xf9, 0xff, 0xda, 0xeb, 0xf7, 0xff, 0xce, 0xe9, 0xf8, 0xff, 0xc4, 0xe3, 0xf8, 0xff, 0xba, 0xda, 0xf5, 0xff, 0xb2, 0xd4, 0xf5, 0xff, 0xb0, 0xcf, 0xf5, 0xff, 0xae, 0xcc, 0xf0, 0xff, 0xaa, 0xc8, 0xf4, 0xff, 0xa6, 0xc4, 0xf3, 0xff, 0xb0, 0xca, 0xf1, 0xff, 0xba, 0xd3, 0xf5, 0xff, 0xbd, 0xd7, 0xf9, 0xff, 0xc2, 0xdd, 0xfd, 0xff, 0xbc, 0xd5, 0xfd, 0xff, 0xa6, 0xbf, 0xec, 0xff, 0x99, 0xb2, 0xda, 0xff, 0xa3, 0xbd, 0xe1, 0xff, 0x9f, 0xb9, 0xdc, 0xff, 0x9a, 0xb4, 0xd4, 0xff, 0x99, 0xb4, 0xd4, 0xff, 0x94, 0xaf, 0xd0, 0xff, 0x92, 0xaf, 0xcf, 0xff, 0x90, 0xad, 0xce, 0xff, 0x5b, 0x78, 0x96, 0xff, 0x23, 0x3d, 0x5e, 0xff, 0x0f, 0x22, 0x3d, 0xff, 0x0d, 0x17, 0x29, 0xff, 0x08, 0x10, 0x1f, 0xff, 0x0b, 0x0c, 0x16, 0xff, 0x09, 0x0a, 0x0f, 0xff, 0x01, 0x04, 0x08, 0xff, 0x02, 0x05, 0x07, 0xff, 0x03, 0x05, 0x06, 0xff, 0x01, 0x02, 0x03, 0xff, 0x00, 0x01, 0x02, 0xff, 0x02, 0x04, 0x05, 0xff, 0x06, 0x09, 0x09, 0xff, 0x0b, 0x0c, 0x0d, 0xff, 0x07, 0x08, 0x09, 0xff, 0x01, 0x02, 0x03, 0xff, 0x03, 0x04, 0x06, 0xff, 0x13, 0x14, 0x18, 0xff, 0x16, 0x19, 0x1e, 0xff, 0x0e, 0x12, 0x13, 0xff, 0x82, 0x85, 0x88, 0xff, 0xd5, 0xcd, 0xd9, 0xff, 0xb9, 0xab, 0xc1, 0xff, 0xba, 0xac, 0xcd, 0xff, 0xb8, 0xaf, 0xd4, 0xff, 0xb7, 0xb0, 0xdb, 0xff, 0xb2, 0xad, 0xdd, 0xff, 0xb1, 0xab, 0xdf, 0xff, 0xae, 0xa7, 0xe0, 0xff, 0xaa, 0xa6, 0xe0, 0xff, 0xa9, 0xa6, 0xde, 0xff, 0xa7, 0xa6, 0xde, 0xff, 0xa5, 0xa2, 0xdd, 0xff, 0xa3, 0xa1, 0xdc, 0xff, 0xa0, 0x9f, 0xdc, 0xff, 0x9d, 0x9c, 0xda, 0xff, 0x9a, 0x9a, 0xd7, 0xff, 0x97, 0x99, 0xd7, 0xff, 0x93, 0x97, 0xd5, 0xff, 0x93, 0x94, 0xd3, 0xc9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x17, 0xaf, 0xf8, 0x4d, 0x06, 0xb9, 0xfc, 0xff, 0x03, 0xbf, 0xfc, 0xff, 0x07, 0xc3, 0xff, 0xff, 0x0c, 0xc7, 0xff, 0xff, 0x1c, 0xc8, 0xff, 0xff, 0x27, 0xc5, 0xfe, 0xff, 0x2f, 0xc2, 0xff, 0xff, 0x4f, 0xc0, 0xff, 0xff, 0x6d, 0xb4, 0xf0, 0xff, 0x76, 0xaa, 0xdb, 0xff, 0x7e, 0xa7, 0xd6, 0xff, 0x95, 0xaa, 0xcb, 0xff, 0xa2, 0xa7, 0xab, 0xff, 0xa9, 0xa0, 0x91, 0xff, 0xb3, 0xa2, 0x92, 0xff, 0xb4, 0xa5, 0x91, 0xff, 0xb3, 0xa7, 0x91, 0xff, 0xb1, 0xa8, 0x94, 0xff, 0xa6, 0x9e, 0x8c, 0xff, 0xa7, 0x9a, 0x8b, 0xff, 0xb5, 0xa5, 0x98, 0xff, 0xbb, 0xa9, 0x92, 0xff, 0xa3, 0x99, 0x89, 0xff, 0xa3, 0xa9, 0xad, 0xff, 0xbd, 0xd1, 0xe3, 0xff, 0xb9, 0xd2, 0xea, 0xff, 0xb4, 0xcd, 0xe4, 0xff, 0xb4, 0xcb, 0xdf, 0xff, 0xbd, 0xd3, 0xe5, 0xff, 0xbe, 0xd5, 0xe6, 0xff, 0xb9, 0xd1, 0xe7, 0xff, 0xa4, 0xc3, 0xe0, 0xff, 0x71, 0x9a, 0xc0, 0xff, 0xa4, 0xc1, 0xd6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfe, 0xff, 0xff, 0xf9, 0xfa, 0xfd, 0xff, 0xfa, 0xfa, 0xfe, 0xff, 0xfd, 0xfc, 0xfa, 0xff, 0xff, 0xfd, 0xfa, 0xff, 0xfd, 0xfc, 0xfd, 0xff, 0xfc, 0xfb, 0xfe, 0xff, 0xfa, 0xfb, 0xff, 0xff, 0xfc, 0xfb, 0xfd, 0xff, 0xfe, 0xfb, 0xfd, 0xff, 0xf9, 0xfb, 0xfa, 0xff, 0xf9, 0xfd, 0xf8, 0xff, 0xf9, 0xfd, 0xf9, 0xff, 0xef, 0xf7, 0xf6, 0xff, 0xe1, 0xef, 0xf5, 0xff, 0xd7, 0xea, 0xf7, 0xff, 0xc6, 0xe4, 0xf3, 0xff, 0xba, 0xde, 0xef, 0xff, 0xb4, 0xd9, 0xf2, 0xff, 0xaf, 0xd4, 0xf4, 0xff, 0xae, 0xd2, 0xf6, 0xff, 0xa9, 0xcc, 0xef, 0xff, 0xa7, 0xc8, 0xf3, 0xff, 0xad, 0xcb, 0xf8, 0xff, 0xb8, 0xd2, 0xf6, 0xff, 0xc1, 0xda, 0xf7, 0xff, 0xc5, 0xde, 0xfc, 0xff, 0xbc, 0xd9, 0xf6, 0xff, 0xad, 0xc9, 0xf2, 0xff, 0xa2, 0xbc, 0xec, 0xff, 0x8b, 0xa6, 0xd1, 0xff, 0x88, 0xa3, 0xc9, 0xff, 0x9a, 0xb5, 0xd8, 0xff, 0xa2, 0xbf, 0xde, 0xff, 0x9f, 0xba, 0xd9, 0xff, 0x9f, 0xb9, 0xd8, 0xff, 0x83, 0xa0, 0xbd, 0xff, 0xa5, 0xc5, 0xdf, 0xff, 0x88, 0xa5, 0xc1, 0xff, 0x56, 0x70, 0x91, 0xff, 0x17, 0x2a, 0x47, 0xff, 0x0e, 0x19, 0x2c, 0xff, 0x11, 0x15, 0x25, 0xff, 0x08, 0x0a, 0x13, 0xff, 0x0d, 0x12, 0x16, 0xff, 0x03, 0x0b, 0x0f, 0xff, 0x00, 0x04, 0x06, 0xff, 0x03, 0x05, 0x05, 0xff, 0x03, 0x05, 0x06, 0xff, 0x00, 0x01, 0x02, 0xff, 0x01, 0x03, 0x04, 0xff, 0x0a, 0x0d, 0x0e, 0xff, 0x05, 0x06, 0x06, 0xff, 0x02, 0x03, 0x03, 0xff, 0x0a, 0x0c, 0x0d, 0xff, 0x12, 0x14, 0x15, 0xff, 0x0e, 0x11, 0x13, 0xff, 0x1a, 0x1d, 0x22, 0xff, 0x25, 0x2a, 0x2c, 0xff, 0x62, 0x66, 0x65, 0xff, 0xb9, 0xb1, 0xb9, 0xff, 0xca, 0xbb, 0xcd, 0xff, 0xbf, 0xaf, 0xca, 0xff, 0xbe, 0xb2, 0xd2, 0xff, 0xbd, 0xb5, 0xda, 0xff, 0xbb, 0xb3, 0xdb, 0xff, 0xba, 0xb2, 0xdf, 0xff, 0xb8, 0xaf, 0xe0, 0xff, 0xb5, 0xad, 0xe0, 0xff, 0xb3, 0xad, 0xde, 0xff, 0xb2, 0xae, 0xdc, 0xff, 0xaf, 0xab, 0xdc, 0xff, 0xad, 0xa9, 0xdc, 0xff, 0xa9, 0xa7, 0xdb, 0xff, 0xa5, 0xa3, 0xd8, 0xff, 0xa2, 0xa0, 0xd5, 0xff, 0x9c, 0x9e, 0xd5, 0xff, 0x96, 0x9b, 0xd4, 0xff, 0x94, 0x98, 0xd2, 0xff, 0x91, 0x95, 0xd3, 0x4d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xae, 0xf7, 0xcd, 0x0b, 0xb7, 0xfd, 0xff, 0x05, 0xbf, 0xfa, 0xff, 0x06, 0xc8, 0xfd, 0xff, 0x16, 0xca, 0xff, 0xff, 0x21, 0xc8, 0xfd, 0xff, 0x26, 0xc5, 0xfd, 0xff, 0x33, 0xc3, 0xff, 0xff, 0x54, 0xc3, 0xff, 0xff, 0x69, 0xbb, 0xf1, 0xff, 0x7d, 0xb1, 0xe1, 0xff, 0x88, 0xac, 0xde, 0xff, 0x96, 0xac, 0xce, 0xff, 0x9f, 0xa8, 0xac, 0xff, 0xa9, 0xa5, 0x92, 0xff, 0xb5, 0xa8, 0x91, 0xff, 0xb7, 0xa8, 0x94, 0xff, 0xb7, 0xab, 0x97, 0xff, 0xb6, 0xaa, 0x98, 0xff, 0xad, 0xa1, 0x8f, 0xff, 0xad, 0xa1, 0x90, 0xff, 0xb6, 0xac, 0x9b, 0xff, 0xb3, 0xaa, 0x99, 0xff, 0xbb, 0xa7, 0x97, 0xff, 0xb4, 0xba, 0xcc, 0xff, 0x9f, 0xc7, 0xe7, 0xff, 0xa3, 0xc0, 0xcb, 0xff, 0x9e, 0xb6, 0xc9, 0xff, 0xaa, 0xbb, 0xe6, 0xff, 0xb3, 0xce, 0xe0, 0xff, 0xbc, 0xe1, 0xe4, 0xff, 0xc4, 0xd7, 0xf3, 0xff, 0xa3, 0xb7, 0xe5, 0xff, 0x73, 0xaa, 0xcb, 0xff, 0xd5, 0xeb, 0xee, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xfc, 0xfd, 0xff, 0xfa, 0xfb, 0xfe, 0xff, 0xfd, 0xfb, 0xfd, 0xff, 0xff, 0xfc, 0xfa, 0xff, 0xfc, 0xfa, 0xfd, 0xff, 0xfa, 0xfa, 0xff, 0xff, 0xfe, 0xfa, 0xff, 0xff, 0xfe, 0xfa, 0xfe, 0xff, 0xff, 0xfb, 0xfc, 0xff, 0xff, 0xfb, 0xfa, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xf8, 0xfc, 0xfd, 0xff, 0xf0, 0xf9, 0xf8, 0xff, 0xe2, 0xf3, 0xf7, 0xff, 0xd5, 0xea, 0xf5, 0xff, 0xcf, 0xe4, 0xf7, 0xff, 0xc3, 0xe2, 0xf6, 0xff, 0xb7, 0xdc, 0xf0, 0xff, 0xb5, 0xd9, 0xef, 0xff, 0xb1, 0xd4, 0xef, 0xff, 0xae, 0xd0, 0xf0, 0xff, 0xab, 0xcd, 0xf0, 0xff, 0xa8, 0xcc, 0xef, 0xff, 0xa9, 0xcb, 0xef, 0xff, 0xb1, 0xcd, 0xf4, 0xff, 0xb5, 0xcf, 0xf8, 0xff, 0xb5, 0xd2, 0xf9, 0xff, 0xb1, 0xcf, 0xf4, 0xff, 0xa2, 0xc0, 0xed, 0xff, 0x8f, 0xac, 0xdc, 0xff, 0x7b, 0x93, 0xc0, 0xff, 0x77, 0x8e, 0xb7, 0xff, 0x8d, 0xa6, 0xcb, 0xff, 0x9f, 0xba, 0xde, 0xff, 0x96, 0xb3, 0xd5, 0xff, 0x9f, 0xb9, 0xd8, 0xff, 0xa3, 0xbb, 0xd5, 0xff, 0xad, 0xc4, 0xdc, 0xff, 0xa4, 0xbc, 0xd5, 0xff, 0x7f, 0x9a, 0xb6, 0xff, 0x33, 0x4a, 0x68, 0xff, 0x28, 0x36, 0x51, 0xff, 0x12, 0x19, 0x2a, 0xff, 0x00, 0x02, 0x08, 0xff, 0x0a, 0x09, 0x0d, 0xff, 0x03, 0x04, 0x0a, 0xff, 0x01, 0x02, 0x04, 0xff, 0x00, 0x02, 0x02, 0xff, 0x01, 0x03, 0x04, 0xff, 0x03, 0x04, 0x05, 0xff, 0x03, 0x03, 0x04, 0xff, 0x07, 0x06, 0x09, 0xff, 0x10, 0x10, 0x11, 0xff, 0x0b, 0x0b, 0x0b, 0xff, 0x0f, 0x0f, 0x0f, 0xff, 0x09, 0x09, 0x09, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x01, 0x02, 0xff, 0x22, 0x22, 0x25, 0xff, 0x66, 0x65, 0x69, 0xff, 0xbd, 0xb9, 0xbf, 0xff, 0xc6, 0xbd, 0xca, 0xff, 0xc6, 0xb7, 0xc8, 0xff, 0xc5, 0xb8, 0xd0, 0xff, 0xc2, 0xb9, 0xd8, 0xff, 0xc0, 0xb7, 0xdb, 0xff, 0xbc, 0xb5, 0xdb, 0xff, 0xbd, 0xb6, 0xdc, 0xff, 0xc0, 0xb5, 0xd9, 0xff, 0xbe, 0xb4, 0xd7, 0xff, 0xbb, 0xb4, 0xdb, 0xff, 0xb9, 0xb2, 0xd8, 0xff, 0xb4, 0xb0, 0xd6, 0xff, 0xae, 0xac, 0xd4, 0xff, 0xa9, 0xa8, 0xd1, 0xff, 0xa5, 0xa6, 0xce, 0xff, 0xa2, 0xa1, 0xcd, 0xff, 0x9f, 0x9e, 0xce, 0xff, 0x99, 0x9a, 0xce, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x93, 0xd3, 0x40, 0x22, 0xab, 0xf8, 0xff, 0x11, 0xb5, 0xff, 0xff, 0x08, 0xbe, 0xfc, 0xff, 0x05, 0xc6, 0xfe, 0xff, 0x12, 0xc9, 0xff, 0xff, 0x1b, 0xc9, 0xfd, 0xff, 0x20, 0xc7, 0xfd, 0xff, 0x2c, 0xc4, 0xff, 0xff, 0x4a, 0xc4, 0xff, 0xff, 0x5d, 0xbd, 0xf2, 0xff, 0x78, 0xb0, 0xdf, 0xff, 0x85, 0xaa, 0xda, 0xff, 0x8e, 0xa5, 0xc8, 0xff, 0xa1, 0xa8, 0xad, 0xff, 0xad, 0xa8, 0x95, 0xff, 0xb6, 0xa8, 0x91, 0xff, 0xb8, 0xa9, 0x96, 0xff, 0xb8, 0xaa, 0x98, 0xff, 0xb7, 0xa9, 0x97, 0xff, 0xb1, 0xa4, 0x92, 0xff, 0xb3, 0xa7, 0x97, 0xff, 0xb7, 0xad, 0x9b, 0xff, 0xbb, 0xaf, 0xa3, 0xff, 0xbd, 0xb3, 0xa6, 0xff, 0xa4, 0xb9, 0xcc, 0xff, 0x94, 0xb5, 0xd8, 0xff, 0xa2, 0xb0, 0xba, 0xff, 0x9b, 0xb7, 0xcc, 0xff, 0xa5, 0xbf, 0xe7, 0xff, 0xc5, 0xdc, 0xef, 0xff, 0xc5, 0xe6, 0xf1, 0xff, 0xa5, 0xc4, 0xe7, 0xff, 0x7f, 0x9e, 0xcd, 0xff, 0x99, 0xc4, 0xdc, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xfb, 0xfc, 0xfb, 0xff, 0xfd, 0xfc, 0xfb, 0xff, 0xfe, 0xfc, 0xfc, 0xff, 0xff, 0xfc, 0xfc, 0xff, 0xfa, 0xfb, 0xfe, 0xff, 0xf9, 0xfc, 0xfe, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfd, 0xfc, 0xfb, 0xff, 0xfa, 0xfb, 0xfa, 0xff, 0xfa, 0xfe, 0xfc, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf4, 0xf6, 0xf8, 0xff, 0xe5, 0xef, 0xef, 0xff, 0xdc, 0xed, 0xf0, 0xff, 0xd6, 0xe9, 0xf2, 0xff, 0xd4, 0xe7, 0xf5, 0xff, 0xc3, 0xdd, 0xf4, 0xff, 0xba, 0xd8, 0xf3, 0xff, 0xbe, 0xdb, 0xf4, 0xff, 0xbd, 0xd9, 0xf3, 0xff, 0xb8, 0xd5, 0xf1, 0xff, 0xb8, 0xd7, 0xf5, 0xff, 0xbd, 0xd9, 0xf7, 0xff, 0xb9, 0xd4, 0xf4, 0xff, 0xb4, 0xd0, 0xf3, 0xff, 0xb3, 0xcf, 0xf6, 0xff, 0xad, 0xc6, 0xf2, 0xff, 0xb2, 0xc7, 0xf5, 0xff, 0xa3, 0xbe, 0xee, 0xff, 0x8b, 0xa9, 0xd8, 0xff, 0x6c, 0x83, 0xb1, 0xff, 0x6d, 0x84, 0xb0, 0xff, 0x7b, 0x93, 0xbd, 0xff, 0x6f, 0x8a, 0xb4, 0xff, 0x70, 0x8e, 0xb6, 0xff, 0x77, 0x92, 0xb7, 0xff, 0x6d, 0x85, 0xa6, 0xff, 0x9d, 0xb2, 0xce, 0xff, 0xb9, 0xd1, 0xec, 0xff, 0x89, 0xa6, 0xc2, 0xff, 0x3b, 0x55, 0x75, 0xff, 0x30, 0x40, 0x5f, 0xff, 0x09, 0x13, 0x25, 0xff, 0x04, 0x06, 0x0e, 0xff, 0x05, 0x03, 0x09, 0xff, 0x04, 0x05, 0x0c, 0xff, 0x01, 0x04, 0x07, 0xff, 0x01, 0x03, 0x03, 0xff, 0x01, 0x03, 0x04, 0xff, 0x02, 0x03, 0x04, 0xff, 0x05, 0x03, 0x05, 0xff, 0x06, 0x04, 0x07, 0xff, 0x0e, 0x0d, 0x0e, 0xff, 0x0a, 0x0a, 0x0a, 0xff, 0x0a, 0x0a, 0x0a, 0xff, 0x04, 0x04, 0x03, 0xff, 0x0e, 0x0d, 0x0d, 0xff, 0x11, 0x11, 0x10, 0xff, 0x32, 0x33, 0x34, 0xff, 0x37, 0x3a, 0x3b, 0xff, 0x07, 0x0c, 0x0c, 0xff, 0x65, 0x67, 0x67, 0xff, 0xc4, 0xbc, 0xc2, 0xff, 0xd6, 0xc7, 0xd1, 0xff, 0xcf, 0xbe, 0xcd, 0xff, 0xcb, 0xbe, 0xd3, 0xff, 0xc7, 0xbd, 0xd7, 0xff, 0xc1, 0xbb, 0xd8, 0xff, 0xc2, 0xbb, 0xd8, 0xff, 0xc4, 0xbc, 0xd6, 0xff, 0xc3, 0xba, 0xd5, 0xff, 0xc3, 0xba, 0xd7, 0xff, 0xbe, 0xb7, 0xd4, 0xff, 0xb9, 0xb4, 0xd2, 0xff, 0xb3, 0xb1, 0xcf, 0xff, 0xae, 0xae, 0xcb, 0xff, 0xab, 0xab, 0xca, 0xff, 0xa8, 0xa6, 0xc9, 0xff, 0xa3, 0xa3, 0xc9, 0xff, 0x9e, 0x9e, 0xcb, 0xff, 0x97, 0x9b, 0xcb, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x95, 0xd2, 0xb3, 0x2d, 0xa9, 0xf6, 0xff, 0x18, 0xb3, 0xfc, 0xff, 0x0c, 0xbc, 0xfc, 0xff, 0x04, 0xc1, 0xfe, 0xff, 0x0d, 0xc4, 0xff, 0xff, 0x18, 0xc5, 0xff, 0xff, 0x1c, 0xc5, 0xff, 0xff, 0x26, 0xc2, 0xff, 0xff, 0x42, 0xc1, 0xff, 0xff, 0x51, 0xb6, 0xec, 0xff, 0x67, 0xa5, 0xd1, 0xff, 0x77, 0xa0, 0xcc, 0xff, 0x85, 0x9e, 0xbf, 0xff, 0xa1, 0xa8, 0xae, 0xff, 0xaf, 0xa6, 0x97, 0xff, 0xb7, 0xa2, 0x90, 0xff, 0xb6, 0xa5, 0x93, 0xff, 0xb6, 0xa8, 0x95, 0xff, 0xb9, 0xaa, 0x98, 0xff, 0xb4, 0xa7, 0x95, 0xff, 0xb4, 0xa7, 0x96, 0xff, 0xbe, 0xaf, 0x9e, 0xff, 0xc0, 0xad, 0xa5, 0xff, 0xa7, 0xb4, 0xb1, 0xff, 0x91, 0xbc, 0xcc, 0xff, 0x99, 0xa9, 0xc6, 0xff, 0xb6, 0xb0, 0xc4, 0xff, 0x97, 0xb9, 0xcf, 0xff, 0xa1, 0xc9, 0xe0, 0xff, 0xd1, 0xe1, 0xf7, 0xff, 0xb6, 0xca, 0xea, 0xff, 0x7b, 0xae, 0xd7, 0xff, 0x69, 0x9e, 0xc1, 0xff, 0xd1, 0xe1, 0xec, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfc, 0xfb, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfb, 0xfb, 0xfb, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfe, 0xfc, 0xfc, 0xff, 0xfd, 0xfd, 0xf9, 0xff, 0xfd, 0xfe, 0xf8, 0xff, 0xff, 0xfe, 0xfa, 0xff, 0xfb, 0xfc, 0xfb, 0xff, 0xf7, 0xfe, 0xfe, 0xff, 0xf2, 0xfd, 0xfc, 0xff, 0xec, 0xf6, 0xf9, 0xff, 0xe4, 0xeb, 0xf2, 0xff, 0xe2, 0xec, 0xf2, 0xff, 0xe4, 0xf3, 0xf8, 0xff, 0xe1, 0xf2, 0xfc, 0xff, 0xdb, 0xee, 0xf9, 0xff, 0xcc, 0xe5, 0xfb, 0xff, 0xc6, 0xe1, 0xfd, 0xff, 0xcb, 0xe5, 0xff, 0xff, 0xd0, 0xec, 0xff, 0xff, 0xdb, 0xf8, 0xff, 0xff, 0xe0, 0xff, 0xff, 0xff, 0xe4, 0xff, 0xff, 0xff, 0xe9, 0xff, 0xff, 0xff, 0xe3, 0xff, 0xff, 0xff, 0xd4, 0xf4, 0xff, 0xff, 0xcc, 0xe5, 0xff, 0xff, 0xc1, 0xd2, 0xff, 0xff, 0xa8, 0xbe, 0xec, 0xff, 0x97, 0xb2, 0xdd, 0xff, 0x7a, 0x93, 0xc1, 0xff, 0x5d, 0x75, 0xa6, 0xff, 0x56, 0x6f, 0xa0, 0xff, 0x53, 0x6e, 0xa0, 0xff, 0x5a, 0x77, 0xa8, 0xff, 0x64, 0x80, 0xae, 0xff, 0x4d, 0x68, 0x93, 0xff, 0x43, 0x5e, 0x83, 0xff, 0x77, 0x94, 0xb6, 0xff, 0x9e, 0xbd, 0xde, 0xff, 0x5f, 0x79, 0x9b, 0xff, 0x2b, 0x3e, 0x5c, 0xff, 0x0e, 0x1a, 0x30, 0xff, 0x1b, 0x21, 0x2d, 0xff, 0x02, 0x05, 0x0d, 0xff, 0x06, 0x08, 0x12, 0xff, 0x06, 0x08, 0x0e, 0xff, 0x03, 0x06, 0x08, 0xff, 0x01, 0x03, 0x06, 0xff, 0x01, 0x02, 0x05, 0xff, 0x01, 0x02, 0x04, 0xff, 0x02, 0x01, 0x02, 0xff, 0x00, 0x00, 0x00, 0xff, 0x02, 0x02, 0x02, 0xff, 0x09, 0x09, 0x09, 0xff, 0x0b, 0x0b, 0x0b, 0xff, 0x39, 0x39, 0x39, 0xff, 0x54, 0x53, 0x53, 0xff, 0x7e, 0x80, 0x80, 0xff, 0x37, 0x3e, 0x3c, 0xff, 0x02, 0x09, 0x06, 0xff, 0x55, 0x57, 0x53, 0xff, 0xad, 0xa7, 0xa6, 0xff, 0xe5, 0xd7, 0xdc, 0xff, 0xd8, 0xc7, 0xcd, 0xff, 0xd4, 0xc6, 0xcc, 0xff, 0xd1, 0xc6, 0xcf, 0xff, 0xcc, 0xc3, 0xd2, 0xff, 0xca, 0xc2, 0xd4, 0xff, 0xc8, 0xc2, 0xd5, 0xff, 0xc8, 0xc0, 0xd2, 0xff, 0xca, 0xc0, 0xd0, 0xff, 0xc6, 0xbd, 0xce, 0xff, 0xc0, 0xba, 0xcb, 0xff, 0xba, 0xb7, 0xc9, 0xff, 0xb4, 0xb4, 0xc6, 0xff, 0xb0, 0xb0, 0xc4, 0xff, 0xac, 0xaa, 0xc4, 0xff, 0xa8, 0xa8, 0xc4, 0xff, 0xa1, 0xa3, 0xc4, 0xff, 0x9b, 0x9e, 0xc6, 0xb3, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x87, 0xb4, 0x22, 0x49, 0x99, 0xcf, 0xfe, 0x37, 0xaa, 0xef, 0xff, 0x24, 0xb4, 0xf9, 0xff, 0x17, 0xbb, 0xfb, 0xff, 0x0d, 0xbe, 0xff, 0xff, 0x14, 0xc0, 0xff, 0xff, 0x1d, 0xc0, 0xfd, 0xff, 0x22, 0xc0, 0xfd, 0xff, 0x2a, 0xbd, 0xff, 0xff, 0x44, 0xbc, 0xfe, 0xff, 0x4f, 0xaf, 0xe4, 0xff, 0x63, 0xa0, 0xc9, 0xff, 0x73, 0x9d, 0xc6, 0xff, 0x84, 0x9c, 0xbc, 0xff, 0x9d, 0xa4, 0xaa, 0xff, 0xa4, 0x9b, 0x8f, 0xff, 0xab, 0x97, 0x89, 0xff, 0xad, 0x9c, 0x8d, 0xff, 0xac, 0x9e, 0x8e, 0xff, 0xae, 0xa1, 0x92, 0xff, 0xad, 0xa0, 0x92, 0xff, 0xb1, 0xa5, 0x97, 0xff, 0xbd, 0xaf, 0xa1, 0xff, 0xb8, 0xa9, 0xa4, 0xff, 0x9d, 0xb7, 0xc1, 0xff, 0x86, 0xb4, 0xc3, 0xff, 0x9e, 0xa7, 0xb9, 0xff, 0xb7, 0xb2, 0xcd, 0xff, 0xa3, 0xc3, 0xde, 0xff, 0xab, 0xd6, 0xe8, 0xff, 0xbd, 0xd0, 0xeb, 0xff, 0x9f, 0xb6, 0xe2, 0xff, 0x5b, 0x95, 0xc1, 0xff, 0x7d, 0xb3, 0xcb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfa, 0xfc, 0xfc, 0xff, 0xff, 0xfd, 0xf9, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xfd, 0xfc, 0xff, 0xff, 0xfd, 0xfe, 0xff, 0xfb, 0xfd, 0xff, 0xff, 0xea, 0xf0, 0xf4, 0xff, 0xd8, 0xe7, 0xf5, 0xff, 0xd7, 0xea, 0xfb, 0xff, 0xdd, 0xee, 0xfc, 0xff, 0xe1, 0xf2, 0xfe, 0xff, 0xe1, 0xf5, 0xff, 0xff, 0xe6, 0xfe, 0xff, 0xff, 0xe3, 0xfe, 0xff, 0xff, 0xc9, 0xe4, 0xf7, 0xff, 0xbc, 0xd8, 0xe9, 0xff, 0xaa, 0xc7, 0xd6, 0xff, 0x95, 0xb4, 0xbf, 0xff, 0x8e, 0xb1, 0xb9, 0xff, 0x8b, 0xaa, 0xb5, 0xff, 0x88, 0xa2, 0xaa, 0xff, 0x8f, 0xa8, 0xa8, 0xff, 0xaa, 0xc3, 0xc1, 0xff, 0xab, 0xc2, 0xd0, 0xff, 0xaf, 0xc6, 0xe7, 0xff, 0xbe, 0xd6, 0xf9, 0xff, 0x9b, 0xb5, 0xd5, 0xff, 0x76, 0x8e, 0xb8, 0xff, 0x61, 0x7a, 0xac, 0xff, 0x54, 0x6f, 0xa4, 0xff, 0x5b, 0x75, 0xac, 0xff, 0x59, 0x74, 0xaa, 0xff, 0x4f, 0x6d, 0xa0, 0xff, 0x44, 0x63, 0x94, 0xff, 0x2b, 0x49, 0x77, 0xff, 0x21, 0x3f, 0x6a, 0xff, 0x4a, 0x68, 0x91, 0xff, 0x70, 0x8b, 0xb0, 0xff, 0x4e, 0x62, 0x83, 0xff, 0x1c, 0x2b, 0x45, 0xff, 0x0c, 0x16, 0x28, 0xff, 0x02, 0x08, 0x15, 0xff, 0x09, 0x0b, 0x18, 0xff, 0x09, 0x0b, 0x13, 0xff, 0x04, 0x06, 0x0b, 0xff, 0x01, 0x05, 0x0a, 0xff, 0x03, 0x04, 0x09, 0xff, 0x02, 0x04, 0x05, 0xff, 0x01, 0x02, 0x03, 0xff, 0x04, 0x04, 0x05, 0xff, 0x08, 0x07, 0x07, 0xff, 0x10, 0x10, 0x10, 0xff, 0x0d, 0x0d, 0x0d, 0xff, 0x0b, 0x0b, 0x0b, 0xff, 0x19, 0x19, 0x19, 0xff, 0x22, 0x24, 0x23, 0xff, 0x12, 0x18, 0x16, 0xff, 0x51, 0x58, 0x53, 0xff, 0x7c, 0x7e, 0x77, 0xff, 0x9c, 0x98, 0x94, 0xff, 0xd5, 0xc9, 0xc8, 0xff, 0xd7, 0xc9, 0xc7, 0xff, 0xd9, 0xcc, 0xc6, 0xff, 0xd9, 0xcc, 0xc9, 0xff, 0xd6, 0xca, 0xca, 0xff, 0xd2, 0xc9, 0xcd, 0xff, 0xd0, 0xc9, 0xd2, 0xff, 0xd2, 0xc9, 0xcf, 0xff, 0xd3, 0xc9, 0xcb, 0xff, 0xce, 0xc5, 0xc8, 0xff, 0xc8, 0xc1, 0xc5, 0xff, 0xc2, 0xbe, 0xc2, 0xff, 0xbd, 0xbc, 0xbf, 0xff, 0xb7, 0xb5, 0xbc, 0xff, 0xb2, 0xb0, 0xbd, 0xff, 0xad, 0xae, 0xbb, 0xff, 0xa5, 0xa8, 0xbb, 0xff, 0x9d, 0xa2, 0xbb, 0xfe, 0x9d, 0xa5, 0xbb, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6a, 0x94, 0xb2, 0x86, 0x5d, 0xa1, 0xcc, 0xff, 0x49, 0xab, 0xe5, 0xff, 0x3e, 0xb3, 0xf1, 0xff, 0x33, 0xb8, 0xf6, 0xff, 0x29, 0xb9, 0xfc, 0xff, 0x29, 0xbb, 0xff, 0xff, 0x2d, 0xbb, 0xfb, 0xff, 0x31, 0xbb, 0xfa, 0xff, 0x39, 0xb8, 0xfd, 0xff, 0x50, 0xb7, 0xf8, 0xff, 0x5b, 0xa8, 0xde, 0xff, 0x67, 0x9d, 0xc6, 0xff, 0x75, 0x9c, 0xc4, 0xff, 0x82, 0x9b, 0xba, 0xff, 0x8f, 0x97, 0x9d, 0xff, 0x8f, 0x8a, 0x7e, 0xff, 0xa0, 0x8f, 0x83, 0xff, 0xa2, 0x94, 0x89, 0xff, 0xa0, 0x95, 0x8a, 0xff, 0x9f, 0x93, 0x8a, 0xff, 0xa0, 0x96, 0x8c, 0xff, 0xaf, 0xa5, 0x9c, 0xff, 0xb0, 0xa3, 0x9a, 0xff, 0xa8, 0xa3, 0xa1, 0xff, 0x92, 0xae, 0xc6, 0xff, 0x84, 0xa1, 0xb4, 0xff, 0xa4, 0xaa, 0xb4, 0xff, 0xaa, 0xbb, 0xd9, 0xff, 0xaf, 0xce, 0xed, 0xff, 0xb5, 0xd6, 0xec, 0xff, 0xa1, 0xc0, 0xe3, 0xff, 0x7e, 0xa4, 0xd2, 0xff, 0x56, 0x89, 0xae, 0xff, 0xb5, 0xd9, 0xe7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xfb, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfd, 0xfc, 0xfd, 0xff, 0xfa, 0xfc, 0xfc, 0xff, 0xfa, 0xfd, 0xfc, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xf8, 0xfb, 0xff, 0xff, 0xf8, 0xf9, 0xff, 0xff, 0xf7, 0xf9, 0xff, 0xff, 0xe8, 0xef, 0xf8, 0xff, 0xdf, 0xe9, 0xf3, 0xff, 0xca, 0xdc, 0xee, 0xff, 0xd8, 0xeb, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xea, 0xf4, 0xfc, 0xff, 0xc9, 0xd7, 0xe2, 0xff, 0x97, 0xac, 0xb6, 0xff, 0x79, 0x8c, 0x93, 0xff, 0x5e, 0x6d, 0x72, 0xff, 0x36, 0x4a, 0x4d, 0xff, 0x26, 0x3a, 0x3c, 0xff, 0x1f, 0x35, 0x34, 0xff, 0x21, 0x36, 0x35, 0xff, 0x0d, 0x25, 0x28, 0xff, 0x17, 0x29, 0x23, 0xff, 0x5f, 0x5f, 0x4a, 0xff, 0xa5, 0xa0, 0x88, 0xff, 0x4e, 0x52, 0x49, 0xff, 0x1e, 0x31, 0x3f, 0xff, 0x72, 0x8e, 0x9d, 0xff, 0xbf, 0xd6, 0xe9, 0xff, 0x9c, 0xb3, 0xd5, 0xff, 0x71, 0x8a, 0xb7, 0xff, 0x64, 0x7c, 0xb0, 0xff, 0x62, 0x79, 0xad, 0xff, 0x5a, 0x74, 0xa7, 0xff, 0x4b, 0x68, 0x9b, 0xff, 0x38, 0x56, 0x8a, 0xff, 0x2d, 0x4c, 0x81, 0xff, 0x1b, 0x38, 0x6b, 0xff, 0x0e, 0x28, 0x58, 0xff, 0x2d, 0x46, 0x6e, 0xff, 0x63, 0x7a, 0x9b, 0xff, 0x40, 0x54, 0x72, 0xff, 0x0a, 0x18, 0x30, 0xff, 0x0b, 0x14, 0x26, 0xff, 0x0c, 0x11, 0x1e, 0xff, 0x09, 0x0b, 0x14, 0xff, 0x07, 0x09, 0x11, 0xff, 0x05, 0x08, 0x0e, 0xff, 0x07, 0x0a, 0x0e, 0xff, 0x09, 0x0b, 0x0c, 0xff, 0x04, 0x06, 0x07, 0xff, 0x05, 0x06, 0x06, 0xff, 0x04, 0x04, 0x04, 0xff, 0x0d, 0x0d, 0x0d, 0xff, 0x18, 0x18, 0x18, 0xff, 0x14, 0x14, 0x14, 0xff, 0x13, 0x14, 0x13, 0xff, 0x12, 0x10, 0x12, 0xff, 0x17, 0x15, 0x17, 0xff, 0x4f, 0x52, 0x4e, 0xff, 0x7e, 0x80, 0x79, 0xff, 0xb0, 0xb0, 0xa9, 0xff, 0x9b, 0x95, 0x91, 0xff, 0xc9, 0xbf, 0xba, 0xff, 0xdc, 0xd0, 0xc6, 0xff, 0xda, 0xcc, 0xc2, 0xff, 0xdc, 0xcc, 0xc2, 0xff, 0xdb, 0xcd, 0xc5, 0xff, 0xd9, 0xcd, 0xca, 0xff, 0xd9, 0xce, 0xca, 0xff, 0xda, 0xce, 0xc7, 0xff, 0xd4, 0xca, 0xc3, 0xff, 0xce, 0xc6, 0xc0, 0xff, 0xc7, 0xc1, 0xbc, 0xff, 0xc2, 0xbe, 0xb9, 0xff, 0xbb, 0xb9, 0xb5, 0xff, 0xb4, 0xb7, 0xb3, 0xff, 0xaf, 0xb2, 0xb3, 0xff, 0xaa, 0xad, 0xb3, 0xff, 0xa2, 0xa9, 0xb5, 0xff, 0x9c, 0xa4, 0xb5, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x7f, 0xbf, 0x04, 0x7c, 0x9a, 0xaf, 0xe7, 0x71, 0xa3, 0xc8, 0xff, 0x5c, 0xa8, 0xdc, 0xff, 0x54, 0xae, 0xe5, 0xff, 0x4e, 0xb1, 0xed, 0xff, 0x48, 0xb3, 0xf9, 0xff, 0x3c, 0xb8, 0xff, 0xff, 0x3a, 0xb7, 0xf8, 0xff, 0x3f, 0xb5, 0xf6, 0xff, 0x45, 0xb3, 0xf9, 0xff, 0x5c, 0xb3, 0xf5, 0xff, 0x6a, 0xa7, 0xdc, 0xff, 0x70, 0xa0, 0xc7, 0xff, 0x78, 0x9e, 0xc6, 0xff, 0x7e, 0x95, 0xb5, 0xff, 0x79, 0x81, 0x88, 0xff, 0x7f, 0x7c, 0x71, 0xff, 0x90, 0x84, 0x78, 0xff, 0x8e, 0x82, 0x7b, 0xff, 0x8c, 0x81, 0x7b, 0xff, 0x8f, 0x85, 0x80, 0xff, 0x98, 0x8e, 0x89, 0xff, 0xa5, 0x9b, 0x97, 0xff, 0xa2, 0x97, 0x91, 0xff, 0x99, 0xa0, 0xa0, 0xff, 0x82, 0x9b, 0xbb, 0xff, 0x90, 0x99, 0xb0, 0xff, 0xa7, 0xb1, 0xb3, 0xff, 0x9b, 0xc2, 0xdf, 0xff, 0xb5, 0xcf, 0xf1, 0xff, 0xc0, 0xd4, 0xef, 0xff, 0x8d, 0xb8, 0xe2, 0xff, 0x53, 0x89, 0xb4, 0xff, 0x76, 0x9e, 0xbc, 0xff, 0xef, 0xfd, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfe, 0xfd, 0xfb, 0xff, 0xff, 0xfc, 0xfc, 0xff, 0xfe, 0xfc, 0xfd, 0xff, 0xfb, 0xfb, 0xfc, 0xff, 0xfb, 0xfe, 0xfe, 0xff, 0xfd, 0xfd, 0xfb, 0xff, 0xe1, 0xf0, 0xf8, 0xff, 0xcb, 0xe0, 0xee, 0xff, 0xdf, 0xec, 0xf7, 0xff, 0xea, 0xf5, 0xfd, 0xff, 0xd8, 0xe9, 0xf4, 0xff, 0xc5, 0xdf, 0xf0, 0xff, 0xdb, 0xf0, 0xfa, 0xff, 0xde, 0xe6, 0xeb, 0xff, 0xbd, 0xbe, 0xbf, 0xff, 0x8b, 0x8b, 0x88, 0xff, 0x54, 0x59, 0x56, 0xff, 0x15, 0x22, 0x1d, 0xff, 0x22, 0x27, 0x21, 0xff, 0x2d, 0x2a, 0x24, 0xff, 0x0d, 0x0d, 0x07, 0xff, 0x05, 0x08, 0x02, 0xff, 0x17, 0x1a, 0x15, 0xff, 0x0c, 0x0e, 0x0a, 0xff, 0x00, 0x08, 0x05, 0xff, 0x1e, 0x1e, 0x16, 0xff, 0xb4, 0x91, 0x73, 0xff, 0xeb, 0xbf, 0x9a, 0xff, 0x49, 0x31, 0x1e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x4c, 0x5f, 0x68, 0xff, 0xbb, 0xd6, 0xef, 0xff, 0x87, 0xa1, 0xc9, 0xff, 0x6a, 0x83, 0xb2, 0xff, 0x61, 0x77, 0xa9, 0xff, 0x5c, 0x74, 0xa3, 0xff, 0x4a, 0x67, 0x95, 0xff, 0x37, 0x55, 0x8a, 0xff, 0x2c, 0x4c, 0x85, 0xff, 0x29, 0x45, 0x7d, 0xff, 0x26, 0x3b, 0x73, 0xff, 0x0d, 0x22, 0x4c, 0xff, 0x27, 0x3f, 0x60, 0xff, 0x44, 0x59, 0x7a, 0xff, 0x14, 0x25, 0x40, 0xff, 0x1c, 0x28, 0x3b, 0xff, 0x0c, 0x13, 0x1f, 0xff, 0x0b, 0x0e, 0x18, 0xff, 0x0c, 0x0e, 0x17, 0xff, 0x08, 0x0b, 0x12, 0xff, 0x04, 0x07, 0x0c, 0xff, 0x05, 0x08, 0x09, 0xff, 0x05, 0x08, 0x08, 0xff, 0x07, 0x08, 0x08, 0xff, 0x06, 0x06, 0x06, 0xff, 0x07, 0x07, 0x07, 0xff, 0x0e, 0x0e, 0x0e, 0xff, 0x0f, 0x0f, 0x0f, 0xff, 0x0d, 0x0e, 0x0d, 0xff, 0x0e, 0x0c, 0x0e, 0xff, 0x0e, 0x07, 0x0c, 0xff, 0x2b, 0x2b, 0x29, 0xff, 0x54, 0x56, 0x51, 0xff, 0x72, 0x75, 0x6c, 0xff, 0x75, 0x76, 0x70, 0xff, 0xaf, 0xab, 0xa5, 0xff, 0xca, 0xbf, 0xb7, 0xff, 0xdf, 0xcf, 0xc3, 0xff, 0xe0, 0xcd, 0xbd, 0xff, 0xe2, 0xce, 0xbf, 0xff, 0xe0, 0xcf, 0xc3, 0xff, 0xdc, 0xce, 0xc3, 0xff, 0xda, 0xcd, 0xc1, 0xff, 0xd7, 0xcb, 0xc2, 0xff, 0xd1, 0xc8, 0xbe, 0xff, 0xcb, 0xc5, 0xba, 0xff, 0xc5, 0xc0, 0xb7, 0xff, 0xbe, 0xbc, 0xb1, 0xff, 0xb6, 0xb9, 0xac, 0xff, 0xb1, 0xb4, 0xad, 0xff, 0xad, 0xb1, 0xb0, 0xff, 0xa6, 0xac, 0xaf, 0xff, 0x9d, 0xa6, 0xaf, 0xe7, 0x7f, 0xbf, 0xbf, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8f, 0x99, 0x92, 0x49, 0x85, 0x9c, 0xa5, 0xff, 0x7b, 0xa2, 0xb8, 0xff, 0x6e, 0xa7, 0xca, 0xff, 0x60, 0xaa, 0xda, 0xff, 0x57, 0xac, 0xe6, 0xff, 0x59, 0xb1, 0xf1, 0xff, 0x53, 0xb6, 0xf2, 0xff, 0x4f, 0xb5, 0xf2, 0xff, 0x50, 0xb2, 0xf4, 0xff, 0x56, 0xb1, 0xf3, 0xff, 0x65, 0xb1, 0xe9, 0xff, 0x69, 0xa8, 0xd5, 0xff, 0x6b, 0xa1, 0xc6, 0xff, 0x72, 0x9a, 0xb7, 0xff, 0x6f, 0x80, 0x90, 0xff, 0x67, 0x69, 0x68, 0xff, 0x6e, 0x69, 0x62, 0xff, 0x75, 0x71, 0x6b, 0xff, 0x78, 0x72, 0x6e, 0xff, 0x78, 0x71, 0x6f, 0xff, 0x7c, 0x76, 0x75, 0xff, 0x8c, 0x86, 0x86, 0xff, 0x8f, 0x88, 0x89, 0xff, 0x97, 0x8d, 0x8c, 0xff, 0x90, 0x9e, 0xaf, 0xff, 0x7b, 0x97, 0xb4, 0xff, 0x80, 0x9c, 0xaf, 0xff, 0x92, 0xb4, 0xc5, 0xff, 0xa0, 0xc6, 0xe3, 0xff, 0xb1, 0xcd, 0xf0, 0xff, 0xae, 0xca, 0xea, 0xff, 0x75, 0xa2, 0xcd, 0xff, 0x51, 0x81, 0xb1, 0xff, 0xaf, 0xc8, 0xde, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xf9, 0xff, 0xff, 0xfe, 0xf6, 0xff, 0xf8, 0xf8, 0xff, 0xff, 0xf3, 0xf9, 0xfe, 0xff, 0xfe, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xec, 0xf9, 0xf6, 0xff, 0xdf, 0xe4, 0xee, 0xff, 0xd1, 0xde, 0xe4, 0xff, 0xb4, 0xd0, 0xdf, 0xff, 0x9a, 0xc1, 0xdc, 0xff, 0xa0, 0xbf, 0xde, 0xff, 0xb5, 0xc3, 0xc0, 0xff, 0x79, 0x7f, 0x6e, 0xff, 0x6a, 0x70, 0x64, 0xff, 0x49, 0x4c, 0x41, 0xff, 0x2e, 0x32, 0x28, 0xff, 0x41, 0x47, 0x42, 0xff, 0x2a, 0x2c, 0x2a, 0xff, 0x16, 0x14, 0x12, 0xff, 0x10, 0x0d, 0x0b, 0xff, 0x08, 0x06, 0x04, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x01, 0x00, 0xff, 0x35, 0x2a, 0x1d, 0xff, 0xa6, 0x85, 0x67, 0xff, 0x8f, 0x74, 0x5a, 0xff, 0x09, 0x01, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x00, 0x00, 0xff, 0x00, 0x02, 0x07, 0xff, 0x6c, 0x81, 0x90, 0xff, 0x82, 0xb7, 0xdb, 0xff, 0x5b, 0x77, 0xaa, 0xff, 0x65, 0x77, 0xa5, 0xff, 0x60, 0x75, 0xa3, 0xff, 0x53, 0x6f, 0x9e, 0xff, 0x4d, 0x66, 0x96, 0xff, 0x38, 0x53, 0x84, 0xff, 0x2e, 0x4c, 0x7f, 0xff, 0x20, 0x40, 0x78, 0xff, 0x18, 0x34, 0x66, 0xff, 0x16, 0x2f, 0x59, 0xff, 0x25, 0x3b, 0x5f, 0xff, 0x1b, 0x2c, 0x47, 0xff, 0x1c, 0x2b, 0x3e, 0xff, 0x0a, 0x19, 0x28, 0xff, 0x0a, 0x11, 0x1c, 0xff, 0x10, 0x10, 0x1a, 0xff, 0x07, 0x09, 0x11, 0xff, 0x03, 0x06, 0x0b, 0xff, 0x06, 0x08, 0x0c, 0xff, 0x06, 0x08, 0x0a, 0xff, 0x06, 0x08, 0x08, 0xff, 0x08, 0x08, 0x09, 0xff, 0x03, 0x02, 0x04, 0xff, 0x0e, 0x0e, 0x0f, 0xff, 0x08, 0x07, 0x07, 0xff, 0x1c, 0x1b, 0x1b, 0xff, 0x12, 0x0f, 0x10, 0xff, 0x26, 0x21, 0x21, 0xff, 0x57, 0x57, 0x53, 0xff, 0x33, 0x35, 0x2e, 0xff, 0x4c, 0x4b, 0x45, 0xff, 0xb1, 0xae, 0xa9, 0xff, 0xa4, 0x9f, 0x9b, 0xff, 0xaa, 0xa1, 0x9e, 0xff, 0xda, 0xca, 0xc1, 0xff, 0xe1, 0xd0, 0xba, 0xff, 0xe1, 0xd2, 0xb4, 0xff, 0xe1, 0xd0, 0xbb, 0xff, 0xde, 0xd0, 0xbd, 0xff, 0xdd, 0xd0, 0xbb, 0xff, 0xdb, 0xcd, 0xbb, 0xff, 0xd5, 0xca, 0xb9, 0xff, 0xce, 0xc6, 0xb6, 0xff, 0xc7, 0xc4, 0xb5, 0xff, 0xc2, 0xbf, 0xb5, 0xff, 0xbc, 0xb9, 0xb1, 0xff, 0xb3, 0xb5, 0xad, 0xff, 0xab, 0xb3, 0xac, 0xff, 0xa5, 0xaf, 0xaa, 0xff, 0xa1, 0xab, 0xa9, 0xff, 0x9e, 0xa8, 0xac, 0x4a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x98, 0x9e, 0x93, 0x9f, 0x92, 0xa1, 0x9e, 0xff, 0x8e, 0xa6, 0xac, 0xff, 0x81, 0xa9, 0xbd, 0xff, 0x71, 0xaa, 0xcf, 0xff, 0x69, 0xab, 0xdb, 0xff, 0x69, 0xad, 0xe1, 0xff, 0x61, 0xae, 0xe3, 0xff, 0x5f, 0xb0, 0xe8, 0xff, 0x60, 0xb0, 0xec, 0xff, 0x69, 0xb3, 0xe9, 0xff, 0x70, 0xaf, 0xd7, 0xff, 0x6d, 0xa3, 0xc3, 0xff, 0x6c, 0x99, 0xb3, 0xff, 0x68, 0x87, 0x96, 0xff, 0x65, 0x70, 0x71, 0xff, 0x65, 0x61, 0x5b, 0xff, 0x5f, 0x5a, 0x55, 0xff, 0x5e, 0x5d, 0x5c, 0xff, 0x69, 0x67, 0x67, 0xff, 0x6f, 0x6b, 0x6b, 0xff, 0x78, 0x74, 0x76, 0xff, 0x84, 0x81, 0x83, 0xff, 0x7e, 0x7c, 0x80, 0xff, 0x8a, 0x84, 0x89, 0xff, 0x87, 0x97, 0xaf, 0xff, 0x79, 0x95, 0xb4, 0xff, 0x7c, 0x9e, 0xbd, 0xff, 0x90, 0xb6, 0xdc, 0xff, 0x9f, 0xc1, 0xe5, 0xff, 0xa4, 0xc5, 0xe8, 0xff, 0x92, 0xba, 0xe2, 0xff, 0x6d, 0x9e, 0xcd, 0xff, 0x6e, 0x99, 0xc2, 0xff, 0xd9, 0xee, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xfb, 0xfb, 0xff, 0xff, 0xfc, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xfb, 0xff, 0xfa, 0xff, 0xf3, 0xff, 0xe9, 0xeb, 0xf4, 0xff, 0xd1, 0xe2, 0xf5, 0xff, 0xd8, 0xdf, 0xf1, 0xff, 0xcd, 0xd2, 0xe2, 0xff, 0xa5, 0xc1, 0xe3, 0xff, 0x86, 0xb1, 0xea, 0xff, 0x91, 0xb9, 0xd2, 0xff, 0xdc, 0xe5, 0xe0, 0xff, 0xab, 0xa9, 0x99, 0xff, 0x56, 0x5a, 0x45, 0xff, 0x6e, 0x71, 0x5e, 0xff, 0x34, 0x37, 0x26, 0xff, 0x2a, 0x2c, 0x21, 0xff, 0x33, 0x38, 0x31, 0xff, 0x05, 0x09, 0x09, 0xff, 0x05, 0x07, 0x09, 0xff, 0x09, 0x0a, 0x0a, 0xff, 0x0b, 0x0b, 0x0c, 0xff, 0x09, 0x0a, 0x0a, 0xff, 0x04, 0x06, 0x06, 0xff, 0x02, 0x06, 0x0b, 0xff, 0x25, 0x23, 0x1f, 0xff, 0x86, 0x75, 0x62, 0xff, 0x4c, 0x44, 0x38, 0xff, 0x00, 0x00, 0x00, 0xff, 0x05, 0x00, 0x01, 0xff, 0x12, 0x1a, 0x1a, 0xff, 0x02, 0x01, 0x02, 0xff, 0x58, 0x4e, 0x5d, 0xff, 0xad, 0xc2, 0xe1, 0xff, 0x87, 0x8d, 0xc0, 0xff, 0x61, 0x81, 0xab, 0xff, 0x5a, 0x7b, 0xa5, 0xff, 0x5a, 0x72, 0xa2, 0xff, 0x51, 0x6b, 0x9a, 0xff, 0x41, 0x5c, 0x8a, 0xff, 0x32, 0x4f, 0x81, 0xff, 0x25, 0x45, 0x7b, 0xff, 0x20, 0x3f, 0x75, 0xff, 0x1f, 0x39, 0x6a, 0xff, 0x12, 0x28, 0x50, 0xff, 0x0d, 0x1f, 0x3c, 0xff, 0x11, 0x1d, 0x30, 0xff, 0x0c, 0x17, 0x23, 0xff, 0x0d, 0x12, 0x1c, 0xff, 0x0f, 0x10, 0x19, 0xff, 0x0a, 0x0c, 0x13, 0xff, 0x06, 0x09, 0x0e, 0xff, 0x07, 0x09, 0x0d, 0xff, 0x06, 0x07, 0x0a, 0xff, 0x07, 0x09, 0x0a, 0xff, 0x0c, 0x0e, 0x0f, 0xff, 0x09, 0x08, 0x0a, 0xff, 0x04, 0x03, 0x05, 0xff, 0x04, 0x03, 0x03, 0xff, 0x07, 0x05, 0x05, 0xff, 0x13, 0x10, 0x0f, 0xff, 0x2a, 0x28, 0x26, 0xff, 0x32, 0x32, 0x2e, 0xff, 0x42, 0x44, 0x3d, 0xff, 0x7a, 0x77, 0x71, 0xff, 0xcc, 0xc6, 0xbf, 0xff, 0xaf, 0xa9, 0xa5, 0xff, 0xb0, 0xa9, 0xa6, 0xff, 0xc7, 0xba, 0xb4, 0xff, 0xda, 0xcc, 0xb6, 0xff, 0xd9, 0xce, 0xac, 0xff, 0xda, 0xcb, 0xb4, 0xff, 0xdb, 0xcd, 0xb9, 0xff, 0xdb, 0xce, 0xb6, 0xff, 0xdc, 0xce, 0xb8, 0xff, 0xd7, 0xcc, 0xb9, 0xff, 0xcf, 0xc7, 0xb6, 0xff, 0xc6, 0xc5, 0xb4, 0xff, 0xc3, 0xc1, 0xb6, 0xff, 0xbd, 0xba, 0xb4, 0xff, 0xb4, 0xb6, 0xb0, 0xff, 0xac, 0xb5, 0xae, 0xff, 0xa7, 0xb2, 0xab, 0xff, 0xa3, 0xad, 0xa9, 0xff, 0xa5, 0xad, 0xa8, 0x9f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x91, 0xb6, 0x91, 0x07, 0x9e, 0xa6, 0x97, 0xef, 0xa4, 0xa8, 0x9f, 0xff, 0xa0, 0xa9, 0xa7, 0xff, 0x92, 0xa9, 0xb2, 0xff, 0x89, 0xab, 0xc1, 0xff, 0x84, 0xac, 0xcc, 0xff, 0x7f, 0xa8, 0xcf, 0xff, 0x74, 0xa7, 0xd4, 0xff, 0x72, 0xa9, 0xd7, 0xff, 0x75, 0xaa, 0xd7, 0xff, 0x7c, 0xab, 0xcf, 0xff, 0x7e, 0xa3, 0xb9, 0xff, 0x7d, 0x9b, 0xa8, 0xff, 0x81, 0x97, 0x9c, 0xff, 0x7b, 0x88, 0x87, 0xff, 0x75, 0x77, 0x71, 0xff, 0x71, 0x6c, 0x63, 0xff, 0x61, 0x5d, 0x59, 0xff, 0x56, 0x55, 0x57, 0xff, 0x5a, 0x5a, 0x5b, 0xff, 0x5f, 0x5d, 0x5f, 0xff, 0x6c, 0x6d, 0x71, 0xff, 0x72, 0x74, 0x7a, 0xff, 0x71, 0x74, 0x7c, 0xff, 0x7d, 0x80, 0x88, 0xff, 0x75, 0x8e, 0xa5, 0xff, 0x77, 0x9b, 0xbf, 0xff, 0x89, 0xaa, 0xd6, 0xff, 0x8d, 0xb1, 0xe3, 0xff, 0x92, 0xbb, 0xe5, 0xff, 0x8e, 0xba, 0xda, 0xff, 0x75, 0xa3, 0xce, 0xff, 0x69, 0x96, 0xbe, 0xff, 0xae, 0xcd, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xcc, 0xe8, 0xe1, 0xff, 0xba, 0xd0, 0xeb, 0xff, 0xc3, 0xd2, 0xf3, 0xff, 0xcc, 0xdf, 0xe6, 0xff, 0xe2, 0xf5, 0xf9, 0xff, 0xe3, 0xf4, 0xff, 0xff, 0xc3, 0xed, 0xf4, 0xff, 0x9d, 0xcd, 0xfa, 0xff, 0x8e, 0xab, 0xee, 0xff, 0xcf, 0xdd, 0xdc, 0xff, 0xff, 0xfa, 0xec, 0xff, 0xfb, 0xec, 0xdd, 0xff, 0xae, 0xad, 0x90, 0xff, 0x77, 0x73, 0x59, 0xff, 0x5f, 0x5c, 0x45, 0xff, 0x2a, 0x2a, 0x1a, 0xff, 0x15, 0x1b, 0x0e, 0xff, 0x05, 0x08, 0x05, 0xff, 0x05, 0x06, 0x08, 0xff, 0x09, 0x0c, 0x0c, 0xff, 0x07, 0x0c, 0x0b, 0xff, 0x09, 0x0b, 0x0b, 0xff, 0x08, 0x09, 0x08, 0xff, 0x00, 0x01, 0x06, 0xff, 0x00, 0x00, 0x00, 0xff, 0x2f, 0x20, 0x14, 0xff, 0x14, 0x0e, 0x06, 0xff, 0x01, 0x04, 0x04, 0xff, 0x0c, 0x09, 0x0b, 0xff, 0x0f, 0x19, 0x16, 0xff, 0x00, 0x00, 0x00, 0xff, 0x81, 0x64, 0x76, 0xff, 0xe7, 0xe1, 0xf8, 0xff, 0x78, 0x73, 0xa3, 0xff, 0x37, 0x63, 0x8c, 0xff, 0x42, 0x6e, 0x95, 0xff, 0x4f, 0x66, 0x99, 0xff, 0x47, 0x64, 0x97, 0xff, 0x3f, 0x59, 0x8f, 0xff, 0x3d, 0x55, 0x8c, 0xff, 0x39, 0x4e, 0x86, 0xff, 0x2b, 0x45, 0x7f, 0xff, 0x21, 0x3f, 0x74, 0xff, 0x11, 0x27, 0x55, 0xff, 0x04, 0x15, 0x36, 0xff, 0x07, 0x10, 0x24, 0xff, 0x10, 0x13, 0x1b, 0xff, 0x10, 0x12, 0x1a, 0xff, 0x0d, 0x0e, 0x19, 0xff, 0x08, 0x0b, 0x12, 0xff, 0x04, 0x08, 0x0c, 0xff, 0x05, 0x06, 0x0b, 0xff, 0x05, 0x07, 0x09, 0xff, 0x09, 0x0b, 0x0d, 0xff, 0x09, 0x0a, 0x0d, 0xff, 0x07, 0x09, 0x0b, 0xff, 0x06, 0x05, 0x07, 0xff, 0x0f, 0x0e, 0x0f, 0xff, 0x1b, 0x1a, 0x1a, 0xff, 0x37, 0x36, 0x35, 0xff, 0x15, 0x13, 0x12, 0xff, 0x11, 0x0e, 0x0e, 0xff, 0x4d, 0x4c, 0x48, 0xff, 0x6a, 0x68, 0x62, 0xff, 0xc1, 0xb8, 0xb1, 0xff, 0xc1, 0xb9, 0xb0, 0xff, 0xba, 0xb4, 0xac, 0xff, 0xc0, 0xb6, 0xae, 0xff, 0xcf, 0xc4, 0xaf, 0xff, 0xd7, 0xcd, 0xad, 0xff, 0xd4, 0xc7, 0xaf, 0xff, 0xd4, 0xc6, 0xb2, 0xff, 0xd5, 0xc8, 0xb1, 0xff, 0xd5, 0xc7, 0xb3, 0xff, 0xd4, 0xc9, 0xb6, 0xff, 0xcf, 0xc7, 0xb7, 0xff, 0xc5, 0xc3, 0xb3, 0xff, 0xc1, 0xbf, 0xb5, 0xff, 0xbb, 0xba, 0xb3, 0xff, 0xb3, 0xb8, 0xb1, 0xff, 0xad, 0xb6, 0xb0, 0xff, 0xa8, 0xb2, 0xac, 0xff, 0xa7, 0xb0, 0xae, 0xff, 0xa8, 0xb2, 0xab, 0xef, 0xb6, 0xb6, 0xb6, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0xab, 0x95, 0x46, 0xac, 0xb0, 0x9d, 0xff, 0xae, 0xb1, 0xa6, 0xff, 0xa9, 0xaf, 0xa9, 0xff, 0x9d, 0xaa, 0xa9, 0xff, 0x99, 0xad, 0xb5, 0xff, 0x95, 0xab, 0xbd, 0xff, 0x90, 0xa7, 0xbf, 0xff, 0x8e, 0xa6, 0xbe, 0xff, 0x8c, 0xa7, 0xbe, 0xff, 0x8c, 0xa6, 0xbb, 0xff, 0x8e, 0xa4, 0xb2, 0xff, 0x90, 0xa0, 0xa3, 0xff, 0x93, 0x9e, 0x9a, 0xff, 0x98, 0x9f, 0x93, 0xff, 0x94, 0x95, 0x87, 0xff, 0x87, 0x85, 0x76, 0xff, 0x7a, 0x78, 0x69, 0xff, 0x6a, 0x67, 0x60, 0xff, 0x5c, 0x5a, 0x59, 0xff, 0x59, 0x58, 0x5a, 0xff, 0x62, 0x61, 0x66, 0xff, 0x6b, 0x6f, 0x73, 0xff, 0x68, 0x6e, 0x78, 0xff, 0x6d, 0x76, 0x80, 0xff, 0x81, 0x89, 0x93, 0xff, 0x73, 0x98, 0xad, 0xff, 0x76, 0xa5, 0xc6, 0xff, 0x81, 0xa7, 0xcf, 0xff, 0x87, 0xad, 0xd6, 0xff, 0x9a, 0xc4, 0xe4, 0xff, 0x96, 0xc7, 0xdb, 0xff, 0x9f, 0xc5, 0xd8, 0xff, 0xc7, 0xdb, 0xe4, 0xff, 0xe6, 0xee, 0xe5, 0xff, 0xeb, 0xeb, 0xd9, 0xff, 0xd8, 0xdf, 0xd1, 0xff, 0xbf, 0xc2, 0xc0, 0xff, 0x8e, 0xb4, 0xad, 0xff, 0x8c, 0xbd, 0xc9, 0xff, 0x92, 0xae, 0xf6, 0xff, 0x9d, 0xbd, 0xf4, 0xff, 0xde, 0xff, 0xfc, 0xff, 0xbb, 0xc8, 0xd3, 0xff, 0x6d, 0x7d, 0xbb, 0xff, 0x4f, 0x80, 0xb4, 0xff, 0x84, 0xb2, 0xe1, 0xff, 0xc9, 0xd6, 0xe8, 0xff, 0xfb, 0xf6, 0xce, 0xff, 0xfc, 0xf6, 0xda, 0xff, 0xfe, 0xf4, 0xdf, 0xff, 0xd6, 0xd0, 0xad, 0xff, 0xb4, 0xae, 0x8f, 0xff, 0x9a, 0x96, 0x7d, 0xff, 0x7c, 0x7b, 0x69, 0xff, 0x1b, 0x20, 0x13, 0xff, 0x03, 0x05, 0x03, 0xff, 0x06, 0x08, 0x09, 0xff, 0x00, 0x05, 0x04, 0xff, 0x00, 0x02, 0x01, 0xff, 0x03, 0x08, 0x07, 0xff, 0x08, 0x0b, 0x0d, 0xff, 0x0a, 0x07, 0x03, 0xff, 0x45, 0x31, 0x1e, 0xff, 0x52, 0x3a, 0x25, 0xff, 0x0a, 0x06, 0x02, 0xff, 0x01, 0x07, 0x0b, 0xff, 0x0a, 0x0f, 0x19, 0xff, 0x07, 0x09, 0x09, 0xff, 0x00, 0x00, 0x00, 0xff, 0x43, 0x3f, 0x4d, 0xff, 0x75, 0x94, 0xb9, 0xff, 0x29, 0x3c, 0x71, 0xff, 0x27, 0x48, 0x73, 0xff, 0x34, 0x55, 0x80, 0xff, 0x5d, 0x77, 0xac, 0xff, 0x40, 0x5d, 0x95, 0xff, 0x3c, 0x59, 0x94, 0xff, 0x40, 0x58, 0x93, 0xff, 0x40, 0x52, 0x8b, 0xff, 0x32, 0x4b, 0x86, 0xff, 0x22, 0x40, 0x7c, 0xff, 0x1d, 0x35, 0x69, 0xff, 0x0c, 0x1d, 0x44, 0xff, 0x0a, 0x13, 0x29, 0xff, 0x0f, 0x0f, 0x18, 0xff, 0x0c, 0x0b, 0x13, 0xff, 0x07, 0x09, 0x14, 0xff, 0x04, 0x07, 0x0e, 0xff, 0x06, 0x09, 0x0e, 0xff, 0x07, 0x08, 0x0d, 0xff, 0x07, 0x08, 0x0b, 0xff, 0x05, 0x07, 0x09, 0xff, 0x05, 0x08, 0x0d, 0xff, 0x07, 0x08, 0x0d, 0xff, 0x0c, 0x0d, 0x10, 0xff, 0x18, 0x17, 0x19, 0xff, 0x15, 0x14, 0x16, 0xff, 0x13, 0x12, 0x13, 0xff, 0x3e, 0x3c, 0x3a, 0xff, 0x34, 0x33, 0x33, 0xff, 0x2e, 0x2c, 0x2c, 0xff, 0x34, 0x2f, 0x29, 0xff, 0x9a, 0x91, 0x87, 0xff, 0xc7, 0xbf, 0xb1, 0xff, 0xb7, 0xaf, 0xa5, 0xff, 0xbc, 0xb3, 0xab, 0xff, 0xcc, 0xc1, 0xad, 0xff, 0xd3, 0xc8, 0xaa, 0xff, 0xd5, 0xc5, 0xad, 0xff, 0xd4, 0xc3, 0xaf, 0xff, 0xd1, 0xc5, 0xae, 0xff, 0xd2, 0xc4, 0xb0, 0xff, 0xcf, 0xc3, 0xb1, 0xff, 0xca, 0xc1, 0xb1, 0xff, 0xc2, 0xc0, 0xaf, 0xff, 0xbc, 0xbc, 0xb1, 0xff, 0xb6, 0xb8, 0xb0, 0xff, 0xaf, 0xb6, 0xaf, 0xff, 0xaa, 0xb4, 0xad, 0xff, 0xa8, 0xb2, 0xac, 0xff, 0xa9, 0xb2, 0xaf, 0xff, 0xaa, 0xb3, 0xaf, 0xff, 0xab, 0xb2, 0xae, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb6, 0xb3, 0x9d, 0x8d, 0xb7, 0xb8, 0xa3, 0xff, 0xb6, 0xb8, 0xaa, 0xff, 0xb2, 0xb5, 0xa8, 0xff, 0xa7, 0xae, 0x9f, 0xff, 0xa2, 0xaa, 0x9d, 0xff, 0xa1, 0xaa, 0xa4, 0xff, 0xa0, 0xa9, 0xa8, 0xff, 0xa2, 0xa9, 0xa4, 0xff, 0xa1, 0xa8, 0xa3, 0xff, 0x9b, 0xa4, 0xa0, 0xff, 0x9c, 0xa5, 0x9f, 0xff, 0xa3, 0xa9, 0xa0, 0xff, 0xa3, 0xa8, 0x9a, 0xff, 0xa6, 0xa8, 0x97, 0xff, 0xa0, 0xa0, 0x8e, 0xff, 0x90, 0x92, 0x7d, 0xff, 0x80, 0x82, 0x71, 0xff, 0x6f, 0x6f, 0x62, 0xff, 0x62, 0x60, 0x57, 0xff, 0x61, 0x5d, 0x5d, 0xff, 0x68, 0x66, 0x69, 0xff, 0x6e, 0x71, 0x75, 0xff, 0x6f, 0x77, 0x7f, 0xff, 0x76, 0x81, 0x8d, 0xff, 0x87, 0x93, 0x9f, 0xff, 0x7b, 0xa1, 0xb9, 0xff, 0x64, 0x90, 0xad, 0xff, 0x85, 0xa6, 0xba, 0xff, 0xbe, 0xd8, 0xe5, 0xff, 0xd8, 0xed, 0xf0, 0xff, 0xe5, 0xfb, 0xfb, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xdf, 0xbd, 0xff, 0x76, 0x7c, 0x5f, 0xff, 0x29, 0x34, 0x2a, 0xff, 0x0c, 0x12, 0x0e, 0xff, 0x31, 0x31, 0x28, 0xff, 0x71, 0x74, 0x72, 0xff, 0xa4, 0xb9, 0xc5, 0xff, 0xb2, 0xe6, 0xf1, 0xff, 0x80, 0xb9, 0xd1, 0xff, 0x56, 0x71, 0xc0, 0xff, 0x7d, 0x95, 0xca, 0xff, 0x4f, 0x79, 0xb1, 0xff, 0x54, 0x7b, 0xaa, 0xff, 0xe9, 0xea, 0xd5, 0xff, 0xff, 0xef, 0xcf, 0xff, 0xf3, 0xed, 0xe5, 0xff, 0xb8, 0xbc, 0xa6, 0xff, 0xd5, 0xd1, 0xad, 0xff, 0xf0, 0xed, 0xcf, 0xff, 0x9e, 0x9a, 0x85, 0xff, 0x9a, 0x99, 0x8e, 0xff, 0x23, 0x26, 0x20, 0xff, 0x06, 0x06, 0x06, 0xff, 0x06, 0x08, 0x09, 0xff, 0x08, 0x0f, 0x0e, 0xff, 0x28, 0x32, 0x30, 0xff, 0x1f, 0x28, 0x27, 0xff, 0x13, 0x17, 0x19, 0xff, 0x29, 0x22, 0x17, 0xff, 0x81, 0x69, 0x50, 0xff, 0x48, 0x30, 0x1e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x09, 0x0f, 0xff, 0x01, 0x07, 0x0e, 0xff, 0x08, 0x03, 0x04, 0xff, 0x00, 0x00, 0x00, 0xff, 0x35, 0x44, 0x4f, 0xff, 0x2e, 0x68, 0x89, 0xff, 0x24, 0x45, 0x7b, 0xff, 0x52, 0x6a, 0x9c, 0xff, 0x4b, 0x63, 0x91, 0xff, 0x52, 0x6e, 0xa2, 0xff, 0x57, 0x74, 0xb0, 0xff, 0x47, 0x69, 0xa8, 0xff, 0x35, 0x55, 0x93, 0xff, 0x3e, 0x58, 0x91, 0xff, 0x3a, 0x54, 0x8f, 0xff, 0x2d, 0x4a, 0x86, 0xff, 0x22, 0x40, 0x75, 0xff, 0x18, 0x2f, 0x59, 0xff, 0x0c, 0x18, 0x32, 0xff, 0x0a, 0x0d, 0x19, 0xff, 0x0b, 0x0c, 0x14, 0xff, 0x04, 0x05, 0x10, 0xff, 0x05, 0x07, 0x0f, 0xff, 0x06, 0x09, 0x0e, 0xff, 0x08, 0x09, 0x0f, 0xff, 0x09, 0x09, 0x0c, 0xff, 0x02, 0x05, 0x07, 0xff, 0x03, 0x06, 0x0b, 0xff, 0x04, 0x07, 0x0b, 0xff, 0x08, 0x0a, 0x0f, 0xff, 0x07, 0x09, 0x0a, 0xff, 0x0d, 0x0d, 0x0f, 0xff, 0x06, 0x04, 0x05, 0xff, 0x17, 0x15, 0x13, 0xff, 0x4d, 0x4b, 0x4d, 0xff, 0x45, 0x41, 0x42, 0xff, 0x39, 0x33, 0x2e, 0xff, 0xa0, 0x97, 0x8a, 0xff, 0xcf, 0xc4, 0xb2, 0xff, 0xbe, 0xb1, 0xa3, 0xff, 0xb7, 0xaa, 0xa2, 0xff, 0xcf, 0xc2, 0xb0, 0xff, 0xd3, 0xc6, 0xa9, 0xff, 0xda, 0xc2, 0xaa, 0xff, 0xd4, 0xc0, 0xab, 0xff, 0xcc, 0xc1, 0xaa, 0xff, 0xce, 0xc0, 0xad, 0xff, 0xcb, 0xc0, 0xae, 0xff, 0xc6, 0xbf, 0xae, 0xff, 0xc0, 0xbd, 0xad, 0xff, 0xb8, 0xb9, 0xae, 0xff, 0xaf, 0xb5, 0xac, 0xff, 0xab, 0xb3, 0xab, 0xff, 0xa7, 0xb2, 0xab, 0xff, 0xa7, 0xb2, 0xac, 0xff, 0xa9, 0xb2, 0xae, 0xff, 0xa9, 0xb1, 0xaf, 0xff, 0xa8, 0xb1, 0xaf, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xbd, 0xb7, 0xa2, 0xd7, 0xbd, 0xbc, 0xa9, 0xff, 0xbe, 0xbd, 0xa7, 0xff, 0xbd, 0xbb, 0xa0, 0xff, 0xb4, 0xb3, 0x92, 0xff, 0xaf, 0xae, 0x8a, 0xff, 0xad, 0xac, 0x8b, 0xff, 0xab, 0xaa, 0x8d, 0xff, 0xab, 0xaa, 0x8e, 0xff, 0xa9, 0xab, 0x90, 0xff, 0xa8, 0xac, 0x94, 0xff, 0xab, 0xaf, 0x9d, 0xff, 0xaf, 0xb2, 0xa3, 0xff, 0xad, 0xb2, 0xa1, 0xff, 0xae, 0xb2, 0xa3, 0xff, 0xa3, 0xaa, 0x9a, 0xff, 0x93, 0x9b, 0x88, 0xff, 0x84, 0x8a, 0x77, 0xff, 0x76, 0x76, 0x65, 0xff, 0x6c, 0x68, 0x58, 0xff, 0x68, 0x62, 0x5f, 0xff, 0x6b, 0x69, 0x6b, 0xff, 0x73, 0x76, 0x78, 0xff, 0x7a, 0x82, 0x87, 0xff, 0x7f, 0x8d, 0x97, 0xff, 0x8d, 0x9d, 0xa9, 0xff, 0x7a, 0x99, 0xb6, 0xff, 0x83, 0xa2, 0xbb, 0xff, 0xd0, 0xe2, 0xe3, 0xff, 0xf6, 0xfa, 0xeb, 0xff, 0xfe, 0xfa, 0xea, 0xff, 0xff, 0xfc, 0xf4, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xfa, 0xf6, 0xd3, 0xff, 0x46, 0x53, 0x41, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x07, 0x09, 0xff, 0x2e, 0x22, 0x29, 0xff, 0x4b, 0x41, 0x36, 0xff, 0x56, 0x6d, 0x43, 0xff, 0xaa, 0xd0, 0xd2, 0xff, 0x6b, 0x82, 0xca, 0xff, 0xcf, 0xdd, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc2, 0xcd, 0xff, 0xff, 0x57, 0x8a, 0xca, 0xff, 0xb6, 0xd2, 0xbb, 0xff, 0xff, 0xf1, 0xe5, 0xff, 0xe2, 0xd4, 0xc4, 0xff, 0x7a, 0x82, 0x5e, 0xff, 0x63, 0x67, 0x48, 0xff, 0x79, 0x79, 0x5e, 0xff, 0x52, 0x51, 0x3d, 0xff, 0x09, 0x09, 0x04, 0xff, 0x0a, 0x0b, 0x0d, 0xff, 0x11, 0x10, 0x11, 0xff, 0x07, 0x08, 0x08, 0xff, 0x09, 0x11, 0x10, 0xff, 0x18, 0x23, 0x20, 0xff, 0x0c, 0x11, 0x10, 0xff, 0x07, 0x05, 0x06, 0xff, 0x05, 0x06, 0x09, 0xff, 0x14, 0x0f, 0x11, 0xff, 0x18, 0x0c, 0x0b, 0xff, 0x07, 0x0a, 0x0c, 0xff, 0x02, 0x0f, 0x13, 0xff, 0x07, 0x03, 0x00, 0xff, 0x04, 0x03, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x46, 0x49, 0x5b, 0xff, 0x38, 0x60, 0x87, 0xff, 0x35, 0x49, 0x85, 0xff, 0x45, 0x60, 0x96, 0xff, 0x59, 0x74, 0xa3, 0xff, 0x4b, 0x64, 0x98, 0xff, 0x40, 0x61, 0x9f, 0xff, 0x4b, 0x70, 0xb3, 0xff, 0x4f, 0x74, 0xb3, 0xff, 0x47, 0x68, 0xa2, 0xff, 0x35, 0x53, 0x8a, 0xff, 0x32, 0x50, 0x87, 0xff, 0x2b, 0x4b, 0x80, 0xff, 0x21, 0x3c, 0x69, 0xff, 0x10, 0x23, 0x3f, 0xff, 0x08, 0x11, 0x20, 0xff, 0x0c, 0x10, 0x19, 0xff, 0x07, 0x08, 0x11, 0xff, 0x04, 0x06, 0x0d, 0xff, 0x05, 0x09, 0x0d, 0xff, 0x08, 0x09, 0x0e, 0xff, 0x08, 0x08, 0x0c, 0xff, 0x04, 0x06, 0x0a, 0xff, 0x03, 0x07, 0x0c, 0xff, 0x05, 0x08, 0x0d, 0xff, 0x05, 0x07, 0x0c, 0xff, 0x06, 0x07, 0x0a, 0xff, 0x08, 0x0a, 0x0c, 0xff, 0x0e, 0x0e, 0x0e, 0xff, 0x09, 0x09, 0x08, 0xff, 0x22, 0x1f, 0x23, 0xff, 0x64, 0x60, 0x63, 0xff, 0x63, 0x5b, 0x57, 0xff, 0x70, 0x66, 0x56, 0xff, 0xa4, 0x96, 0x82, 0xff, 0xd6, 0xc4, 0xb4, 0xff, 0xbd, 0xab, 0xa2, 0xff, 0xcb, 0xbc, 0xad, 0xff, 0xd9, 0xc8, 0xac, 0xff, 0xe2, 0xc4, 0xad, 0xff, 0xd7, 0xc0, 0xac, 0xff, 0xc9, 0xbf, 0xa8, 0xff, 0xc9, 0xbc, 0xa9, 0xff, 0xc7, 0xbc, 0xaa, 0xff, 0xc3, 0xbb, 0xaa, 0xff, 0xbe, 0xbb, 0xab, 0xff, 0xb6, 0xb8, 0xad, 0xff, 0xac, 0xb4, 0xac, 0xff, 0xa9, 0xb2, 0xaa, 0xff, 0xa5, 0xb1, 0xaa, 0xff, 0xa5, 0xb1, 0xab, 0xff, 0xa9, 0xb0, 0xae, 0xff, 0xa9, 0xb2, 0xad, 0xff, 0xa4, 0xb5, 0xa9, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xba, 0xb0, 0x9c, 0x1a, 0xbf, 0xbc, 0xa5, 0xff, 0xc1, 0xc0, 0xa9, 0xff, 0xc4, 0xc0, 0xa8, 0xff, 0xc3, 0xbe, 0xa3, 0xff, 0xbb, 0xb8, 0x97, 0xff, 0xb6, 0xb4, 0x90, 0xff, 0xb5, 0xb2, 0x8c, 0xff, 0xb4, 0xaf, 0x8c, 0xff, 0xb3, 0xb1, 0x92, 0xff, 0xb4, 0xb3, 0x98, 0xff, 0xb4, 0xb4, 0x9e, 0xff, 0xb5, 0xb6, 0xa5, 0xff, 0xb7, 0xb9, 0xa9, 0xff, 0xb7, 0xbd, 0xac, 0xff, 0xb6, 0xbb, 0xa9, 0xff, 0xac, 0xb3, 0x9f, 0xff, 0x9f, 0xa5, 0x90, 0xff, 0x91, 0x94, 0x7f, 0xff, 0x7f, 0x7f, 0x6d, 0xff, 0x73, 0x6f, 0x60, 0xff, 0x6b, 0x64, 0x68, 0xff, 0x73, 0x71, 0x67, 0xff, 0x7e, 0x7d, 0x7d, 0xff, 0x81, 0x88, 0x9b, 0xff, 0x7f, 0x98, 0x99, 0xff, 0x86, 0x99, 0xa4, 0xff, 0xaf, 0xbd, 0xcc, 0xff, 0xe4, 0xf1, 0xed, 0xff, 0xef, 0xf4, 0xde, 0xff, 0xf8, 0xf3, 0xd4, 0xff, 0xff, 0xfb, 0xe7, 0xff, 0xff, 0xf7, 0xf3, 0xff, 0xfe, 0xf7, 0xeb, 0xff, 0xfd, 0xfa, 0xe5, 0xff, 0xf1, 0xf0, 0xd9, 0xff, 0x94, 0x9e, 0x8b, 0xff, 0x20, 0x2c, 0x24, 0xff, 0x00, 0x0d, 0x0a, 0xff, 0x0d, 0x10, 0x0b, 0xff, 0x12, 0x13, 0x0d, 0xff, 0x29, 0x34, 0x10, 0xff, 0x8f, 0xa3, 0xb0, 0xff, 0x51, 0x7c, 0xc5, 0xff, 0xda, 0xf0, 0xf8, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xb3, 0xcd, 0xf4, 0xff, 0x79, 0xac, 0xf2, 0xff, 0xb5, 0xc6, 0xc6, 0xff, 0xff, 0xf6, 0xd7, 0xff, 0xca, 0xc7, 0xac, 0xff, 0x92, 0x9f, 0x79, 0xff, 0x6a, 0x6e, 0x52, 0xff, 0x2a, 0x2e, 0x1d, 0xff, 0x0d, 0x12, 0x0c, 0xff, 0x02, 0x06, 0x04, 0xff, 0x06, 0x0a, 0x07, 0xff, 0x0e, 0x0c, 0x0a, 0xff, 0x10, 0x0b, 0x0b, 0xff, 0x0d, 0x0e, 0x0e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x25, 0x17, 0x0d, 0xff, 0x31, 0x26, 0x17, 0xff, 0x03, 0x02, 0x04, 0xff, 0x06, 0x08, 0x0b, 0xff, 0x02, 0x06, 0x05, 0xff, 0x03, 0x04, 0x01, 0xff, 0x07, 0x03, 0x09, 0xff, 0x00, 0x01, 0x00, 0xff, 0x46, 0x59, 0x5d, 0xff, 0x35, 0x4b, 0x85, 0xff, 0x2f, 0x45, 0x7f, 0xff, 0x3a, 0x5c, 0x90, 0xff, 0x52, 0x71, 0xa9, 0xff, 0x59, 0x74, 0xa9, 0xff, 0x4a, 0x68, 0xa0, 0xff, 0x41, 0x60, 0x98, 0xff, 0x4d, 0x6d, 0xa8, 0xff, 0x53, 0x75, 0xb2, 0xff, 0x4e, 0x6c, 0xaf, 0xff, 0x37, 0x56, 0x98, 0xff, 0x23, 0x44, 0x7e, 0xff, 0x26, 0x40, 0x74, 0xff, 0x1b, 0x2a, 0x50, 0xff, 0x0e, 0x13, 0x21, 0xff, 0x0d, 0x10, 0x18, 0xff, 0x04, 0x0b, 0x16, 0xff, 0x00, 0x08, 0x0d, 0xff, 0x01, 0x0d, 0x07, 0xff, 0x07, 0x0a, 0x08, 0xff, 0x13, 0x03, 0x12, 0xff, 0x0c, 0x04, 0x10, 0xff, 0x04, 0x08, 0x0b, 0xff, 0x07, 0x09, 0x0d, 0xff, 0x09, 0x0b, 0x0e, 0xff, 0x09, 0x09, 0x0e, 0xff, 0x07, 0x08, 0x0d, 0xff, 0x0d, 0x0f, 0x10, 0xff, 0x11, 0x11, 0x10, 0xff, 0x08, 0x07, 0x07, 0xff, 0x1b, 0x18, 0x18, 0xff, 0x65, 0x61, 0x60, 0xff, 0x42, 0x3e, 0x39, 0xff, 0x33, 0x2c, 0x23, 0xff, 0xb4, 0xac, 0xa1, 0xff, 0xc1, 0xb8, 0xae, 0xff, 0xca, 0xb9, 0xa5, 0xff, 0xdf, 0xc9, 0xa9, 0xff, 0xdd, 0xc8, 0xae, 0xff, 0xd2, 0xc2, 0xac, 0xff, 0xce, 0xbf, 0xa7, 0xff, 0xcb, 0xbd, 0xa7, 0xff, 0xc6, 0xbc, 0xa9, 0xff, 0xc2, 0xbb, 0xaa, 0xff, 0xbf, 0xba, 0xaa, 0xff, 0xb7, 0xb8, 0xab, 0xff, 0xad, 0xb4, 0xaa, 0xff, 0xaa, 0xb1, 0xa6, 0xff, 0xa9, 0xb2, 0xaa, 0xff, 0xa7, 0xb2, 0xab, 0xff, 0xa4, 0xb0, 0xab, 0xff, 0xa7, 0xb3, 0xac, 0xff, 0xaa, 0xb4, 0xaa, 0xff, 0xaa, 0xb3, 0xaa, 0x1b, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xbc, 0xb6, 0x9c, 0x58, 0xc1, 0xbd, 0xa5, 0xff, 0xc3, 0xc1, 0xa9, 0xff, 0xc5, 0xc0, 0xac, 0xff, 0xc3, 0xbf, 0xa7, 0xff, 0xbc, 0xba, 0x9b, 0xff, 0xb9, 0xb7, 0x95, 0xff, 0xb8, 0xb5, 0x92, 0xff, 0xb9, 0xb3, 0x92, 0xff, 0xb9, 0xb6, 0x99, 0xff, 0xbb, 0xb9, 0xa1, 0xff, 0xbc, 0xba, 0xa8, 0xff, 0xbf, 0xbe, 0xb0, 0xff, 0xc0, 0xc1, 0xb2, 0xff, 0xbd, 0xc1, 0xb1, 0xff, 0xbb, 0xc0, 0xac, 0xff, 0xb4, 0xb8, 0xa2, 0xff, 0xa7, 0xaa, 0x94, 0xff, 0x9a, 0x9a, 0x86, 0xff, 0x88, 0x86, 0x75, 0xff, 0x77, 0x74, 0x66, 0xff, 0x6e, 0x68, 0x6a, 0xff, 0x77, 0x73, 0x6e, 0xff, 0x83, 0x83, 0x83, 0xff, 0x85, 0x91, 0xa0, 0xff, 0x7b, 0x92, 0x98, 0xff, 0x9e, 0xae, 0xb2, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xef, 0xe9, 0xcf, 0xff, 0xf9, 0xf1, 0xd7, 0xff, 0xfc, 0xf8, 0xe6, 0xff, 0xf5, 0xf4, 0xec, 0xff, 0xf9, 0xf2, 0xe8, 0xff, 0xf7, 0xed, 0xdc, 0xff, 0xff, 0xfe, 0xe8, 0xff, 0xe7, 0xe4, 0xc7, 0xff, 0xa4, 0xa0, 0x83, 0xff, 0x7a, 0x73, 0x59, 0xff, 0x27, 0x2a, 0x1c, 0xff, 0x00, 0x01, 0x03, 0xff, 0x13, 0x1d, 0x0a, 0xff, 0x92, 0xa9, 0xbb, 0xff, 0x46, 0x7c, 0xb6, 0xff, 0xd4, 0xe6, 0xec, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb9, 0xdf, 0xf3, 0xff, 0x74, 0xb2, 0xea, 0xff, 0x79, 0x99, 0x9f, 0xff, 0xe8, 0xe0, 0xbb, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xf2, 0xf7, 0xd9, 0xff, 0xb7, 0xbb, 0xa1, 0xff, 0x42, 0x47, 0x3c, 0xff, 0x00, 0x03, 0x03, 0xff, 0x0a, 0x0f, 0x0f, 0xff, 0x1a, 0x1f, 0x1a, 0xff, 0x15, 0x16, 0x11, 0xff, 0x0d, 0x0b, 0x09, 0xff, 0x09, 0x0a, 0x08, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x06, 0x07, 0x07, 0xff, 0x61, 0x51, 0x3d, 0xff, 0x72, 0x66, 0x4d, 0xff, 0x00, 0x01, 0x01, 0xff, 0x02, 0x04, 0x07, 0xff, 0x03, 0x02, 0x02, 0xff, 0x01, 0x06, 0x07, 0xff, 0x07, 0x01, 0x0b, 0xff, 0x01, 0x04, 0x00, 0xff, 0x4b, 0x67, 0x6a, 0xff, 0x31, 0x48, 0x85, 0xff, 0x34, 0x4d, 0x85, 0xff, 0x3a, 0x59, 0x8c, 0xff, 0x3e, 0x5d, 0x96, 0xff, 0x51, 0x71, 0xa5, 0xff, 0x56, 0x72, 0xa7, 0xff, 0x50, 0x6c, 0xa0, 0xff, 0x43, 0x61, 0x9a, 0xff, 0x3c, 0x5e, 0x9e, 0xff, 0x45, 0x64, 0xab, 0xff, 0x48, 0x68, 0xab, 0xff, 0x37, 0x5c, 0x96, 0xff, 0x23, 0x40, 0x79, 0xff, 0x11, 0x20, 0x51, 0xff, 0x0d, 0x18, 0x2f, 0xff, 0x10, 0x10, 0x21, 0xff, 0x0c, 0x0a, 0x16, 0xff, 0x07, 0x0a, 0x0d, 0xff, 0x03, 0x05, 0x10, 0xff, 0x05, 0x05, 0x11, 0xff, 0x09, 0x0f, 0x03, 0xff, 0x07, 0x0c, 0x03, 0xff, 0x08, 0x08, 0x0f, 0xff, 0x0c, 0x0d, 0x10, 0xff, 0x0c, 0x0c, 0x10, 0xff, 0x08, 0x09, 0x0d, 0xff, 0x06, 0x07, 0x0c, 0xff, 0x0d, 0x0d, 0x10, 0xff, 0x08, 0x08, 0x08, 0xff, 0x15, 0x15, 0x14, 0xff, 0x20, 0x1f, 0x1d, 0xff, 0x3f, 0x3e, 0x3e, 0xff, 0x68, 0x67, 0x69, 0xff, 0x30, 0x2d, 0x29, 0xff, 0x64, 0x61, 0x59, 0xff, 0xae, 0xa9, 0xa0, 0xff, 0xcd, 0xbc, 0xa8, 0xff, 0xd7, 0xc1, 0xa0, 0xff, 0xd4, 0xc2, 0xa7, 0xff, 0xd0, 0xc1, 0xab, 0xff, 0xd1, 0xc2, 0xa7, 0xff, 0xcc, 0xbe, 0xa6, 0xff, 0xc6, 0xbc, 0xa8, 0xff, 0xc2, 0xbb, 0xaa, 0xff, 0xc0, 0xb9, 0xa8, 0xff, 0xb9, 0xb7, 0xa8, 0xff, 0xb0, 0xb5, 0xa9, 0xff, 0xad, 0xb3, 0xa7, 0xff, 0xab, 0xb3, 0xa9, 0xff, 0xa9, 0xb3, 0xaa, 0xff, 0xa4, 0xb2, 0xaa, 0xff, 0xa6, 0xb1, 0xab, 0xff, 0xab, 0xb1, 0xab, 0xff, 0xaa, 0xb0, 0xaa, 0x58, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xc1, 0xb8, 0x9d, 0x90, 0xc5, 0xc0, 0xa7, 0xff, 0xc6, 0xc3, 0xab, 0xff, 0xc8, 0xc3, 0xae, 0xff, 0xc7, 0xc1, 0xa9, 0xff, 0xbf, 0xbc, 0x9e, 0xff, 0xbc, 0xb8, 0x97, 0xff, 0xba, 0xb4, 0x93, 0xff, 0xb9, 0xb4, 0x93, 0xff, 0xbb, 0xb8, 0x9b, 0xff, 0xbf, 0xbc, 0xa4, 0xff, 0xc0, 0xbe, 0xaa, 0xff, 0xc3, 0xc3, 0xb2, 0xff, 0xc3, 0xc6, 0xb4, 0xff, 0xc1, 0xc4, 0xb2, 0xff, 0xbe, 0xc3, 0xaf, 0xff, 0xb7, 0xbb, 0xa7, 0xff, 0xac, 0xae, 0x99, 0xff, 0x9f, 0xa0, 0x8b, 0xff, 0x8f, 0x8d, 0x7c, 0xff, 0x7e, 0x7a, 0x6a, 0xff, 0x71, 0x72, 0x68, 0xff, 0x76, 0x72, 0x7d, 0xff, 0x84, 0x89, 0x8a, 0xff, 0x85, 0x97, 0x94, 0xff, 0x8b, 0x95, 0xa8, 0xff, 0xeb, 0xf1, 0xe8, 0xff, 0xfc, 0xfa, 0xdb, 0xff, 0xfa, 0xf0, 0xd5, 0xff, 0xff, 0xf5, 0xe2, 0xff, 0xf7, 0xe9, 0xda, 0xff, 0xf8, 0xf0, 0xe0, 0xff, 0xf4, 0xf3, 0xe1, 0xff, 0xf4, 0xed, 0xdf, 0xff, 0xf6, 0xed, 0xdd, 0xff, 0xe8, 0xe2, 0xc8, 0xff, 0xd3, 0xce, 0xae, 0xff, 0xe5, 0xdf, 0xc0, 0xff, 0xb2, 0xa9, 0x8c, 0xff, 0xac, 0xaa, 0x92, 0xff, 0x14, 0x1c, 0x13, 0xff, 0x0b, 0x20, 0x13, 0xff, 0x8d, 0xaf, 0xc8, 0xff, 0x2f, 0x56, 0x89, 0xff, 0xea, 0xef, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0xe4, 0xfa, 0xff, 0x98, 0xd7, 0xfe, 0xff, 0x4e, 0x82, 0x8f, 0xff, 0x39, 0x46, 0x2b, 0xff, 0xb6, 0xb0, 0x97, 0xff, 0x9c, 0x9c, 0x86, 0xff, 0x1b, 0x1d, 0x12, 0xff, 0x00, 0x04, 0x00, 0xff, 0x08, 0x0a, 0x09, 0xff, 0x0e, 0x11, 0x0f, 0xff, 0x10, 0x13, 0x10, 0xff, 0x08, 0x0b, 0x06, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0c, 0x12, 0x0e, 0xff, 0x14, 0x18, 0x14, 0xff, 0x08, 0x0a, 0x07, 0xff, 0x2e, 0x30, 0x2e, 0xff, 0x6a, 0x67, 0x56, 0xff, 0x43, 0x42, 0x36, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0b, 0x0d, 0x13, 0xff, 0x0f, 0x0e, 0x10, 0xff, 0x02, 0x05, 0x0a, 0xff, 0x02, 0x00, 0x03, 0xff, 0x17, 0x17, 0x14, 0xff, 0x61, 0x79, 0x8c, 0xff, 0x25, 0x3c, 0x73, 0xff, 0x35, 0x4a, 0x84, 0xff, 0x43, 0x5b, 0x92, 0xff, 0x41, 0x5f, 0x96, 0xff, 0x52, 0x73, 0xa7, 0xff, 0x59, 0x79, 0xae, 0xff, 0x53, 0x72, 0xa6, 0xff, 0x4d, 0x6e, 0xa6, 0xff, 0x43, 0x67, 0xa4, 0xff, 0x3b, 0x5c, 0x9a, 0xff, 0x35, 0x57, 0x92, 0xff, 0x37, 0x5e, 0x97, 0xff, 0x3b, 0x5e, 0x9b, 0xff, 0x2c, 0x44, 0x7d, 0xff, 0x03, 0x13, 0x40, 0xff, 0x00, 0x06, 0x09, 0xff, 0x0a, 0x0f, 0x06, 0xff, 0x10, 0x0a, 0x1b, 0xff, 0x0d, 0x05, 0x14, 0xff, 0x04, 0x06, 0x09, 0xff, 0x00, 0x0b, 0x0b, 0xff, 0x03, 0x0a, 0x0f, 0xff, 0x0b, 0x0a, 0x0e, 0xff, 0x0a, 0x0b, 0x0f, 0xff, 0x08, 0x09, 0x0d, 0xff, 0x0b, 0x0c, 0x10, 0xff, 0x09, 0x0a, 0x0e, 0xff, 0x07, 0x08, 0x0a, 0xff, 0x09, 0x0a, 0x0a, 0xff, 0x0b, 0x0c, 0x0c, 0xff, 0x1c, 0x1b, 0x1b, 0xff, 0x29, 0x27, 0x26, 0xff, 0x5e, 0x5c, 0x5d, 0xff, 0x61, 0x5c, 0x58, 0xff, 0x4f, 0x4c, 0x45, 0xff, 0x85, 0x82, 0x7c, 0xff, 0xd3, 0xc5, 0xb5, 0xff, 0xcf, 0xb9, 0x9d, 0xff, 0xcd, 0xba, 0xa0, 0xff, 0xca, 0xba, 0xa2, 0xff, 0xcb, 0xbb, 0xa1, 0xff, 0xc9, 0xbb, 0xa3, 0xff, 0xc7, 0xbc, 0xa5, 0xff, 0xc4, 0xbc, 0xa8, 0xff, 0xc1, 0xb8, 0xa6, 0xff, 0xb9, 0xb5, 0xa5, 0xff, 0xb3, 0xb5, 0xa8, 0xff, 0xb2, 0xb5, 0xa8, 0xff, 0xad, 0xb3, 0xa8, 0xff, 0xaa, 0xb3, 0xa9, 0xff, 0xa9, 0xb3, 0xaa, 0xff, 0xa7, 0xb0, 0xa7, 0xff, 0xa8, 0xaf, 0xa7, 0xff, 0xa8, 0xb1, 0xa8, 0x90, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xc2, 0xb8, 0x9b, 0xc6, 0xc7, 0xc0, 0xa7, 0xff, 0xc8, 0xc4, 0xab, 0xff, 0xca, 0xc4, 0xaf, 0xff, 0xc9, 0xc4, 0xac, 0xff, 0xc2, 0xbf, 0xa0, 0xff, 0xbd, 0xb8, 0x97, 0xff, 0xb9, 0xb4, 0x93, 0xff, 0xbb, 0xb5, 0x95, 0xff, 0xbe, 0xba, 0x9d, 0xff, 0xc1, 0xbe, 0xa5, 0xff, 0xc3, 0xc1, 0xad, 0xff, 0xc4, 0xc4, 0xb2, 0xff, 0xc5, 0xc7, 0xb4, 0xff, 0xc4, 0xc7, 0xb4, 0xff, 0xc3, 0xc6, 0xb2, 0xff, 0xbc, 0xbe, 0xaa, 0xff, 0xb0, 0xb2, 0x9d, 0xff, 0xa5, 0xa6, 0x91, 0xff, 0x97, 0x93, 0x82, 0xff, 0x83, 0x7d, 0x6e, 0xff, 0x75, 0x76, 0x68, 0xff, 0x7a, 0x75, 0x84, 0xff, 0x85, 0x8c, 0x90, 0xff, 0x7e, 0x93, 0x8d, 0xff, 0xc1, 0xc6, 0xd5, 0xff, 0xf4, 0xf2, 0xd8, 0xff, 0x56, 0x5d, 0x2b, 0xff, 0x86, 0x89, 0x67, 0xff, 0xf6, 0xee, 0xda, 0xff, 0xfc, 0xf0, 0xe2, 0xff, 0xfe, 0xee, 0xdf, 0xff, 0xfa, 0xec, 0xd9, 0xff, 0xf1, 0xe9, 0xd2, 0xff, 0xf9, 0xf8, 0xde, 0xff, 0x9f, 0xa4, 0x89, 0xff, 0x87, 0x93, 0x7b, 0xff, 0xcb, 0xdc, 0xce, 0xff, 0x80, 0x97, 0x93, 0xff, 0x52, 0x59, 0x4e, 0xff, 0x09, 0x0a, 0x07, 0xff, 0x2a, 0x43, 0x3e, 0xff, 0x8f, 0xb6, 0xdf, 0xff, 0x72, 0x8b, 0xb0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xc3, 0xe3, 0xff, 0xff, 0xa1, 0xda, 0xff, 0xff, 0x76, 0xaf, 0xd2, 0xff, 0x0a, 0x28, 0x22, 0xff, 0x14, 0x13, 0x00, 0xff, 0x10, 0x0a, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0a, 0x0c, 0x08, 0xff, 0x0a, 0x0d, 0x0c, 0xff, 0x07, 0x0b, 0x09, 0xff, 0x08, 0x06, 0x05, 0xff, 0x02, 0x06, 0x04, 0xff, 0x13, 0x1e, 0x1b, 0xff, 0x4c, 0x55, 0x50, 0xff, 0x2e, 0x35, 0x2f, 0xff, 0x1a, 0x1f, 0x19, 0xff, 0x2c, 0x30, 0x2b, 0xff, 0x8a, 0x8d, 0x80, 0xff, 0x32, 0x36, 0x30, 0xff, 0x00, 0x01, 0x04, 0xff, 0x06, 0x08, 0x0e, 0xff, 0x0f, 0x0e, 0x0f, 0xff, 0x06, 0x08, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x25, 0x28, 0x2e, 0xff, 0x7d, 0x94, 0xb6, 0xff, 0x4c, 0x65, 0x98, 0xff, 0x4d, 0x60, 0x9c, 0xff, 0x4f, 0x65, 0x9d, 0xff, 0x55, 0x73, 0xa8, 0xff, 0x49, 0x68, 0x9f, 0xff, 0x4e, 0x6b, 0xa0, 0xff, 0x5a, 0x79, 0xac, 0xff, 0x58, 0x77, 0xaf, 0xff, 0x51, 0x72, 0xae, 0xff, 0x4e, 0x6e, 0xa9, 0xff, 0x3f, 0x5f, 0x98, 0xff, 0x24, 0x4b, 0x83, 0xff, 0x23, 0x47, 0x84, 0xff, 0x31, 0x4c, 0x88, 0xff, 0x3b, 0x4f, 0x7c, 0xff, 0x25, 0x31, 0x52, 0xff, 0x00, 0x02, 0x13, 0xff, 0x00, 0x05, 0x02, 0xff, 0x08, 0x0d, 0x07, 0xff, 0x0a, 0x06, 0x0a, 0xff, 0x09, 0x03, 0x10, 0xff, 0x09, 0x08, 0x11, 0xff, 0x09, 0x0a, 0x0e, 0xff, 0x0a, 0x0a, 0x10, 0xff, 0x08, 0x08, 0x0e, 0xff, 0x0b, 0x0b, 0x11, 0xff, 0x07, 0x07, 0x0d, 0xff, 0x0e, 0x10, 0x13, 0xff, 0x09, 0x0c, 0x0c, 0xff, 0x08, 0x09, 0x0a, 0xff, 0x0b, 0x0b, 0x0b, 0xff, 0x1d, 0x1d, 0x1c, 0xff, 0x5c, 0x5b, 0x5a, 0xff, 0x73, 0x6f, 0x6b, 0xff, 0x5d, 0x5a, 0x57, 0xff, 0x69, 0x68, 0x68, 0xff, 0xbe, 0xb4, 0xa8, 0xff, 0xd8, 0xc3, 0xa9, 0xff, 0xd2, 0xbb, 0xa2, 0xff, 0xc9, 0xb7, 0x9f, 0xff, 0xc7, 0xb8, 0x9d, 0xff, 0xc7, 0xb9, 0x9f, 0xff, 0xc8, 0xbb, 0xa3, 0xff, 0xc6, 0xbb, 0xa5, 0xff, 0xc2, 0xb9, 0xa3, 0xff, 0xbc, 0xb6, 0xa3, 0xff, 0xb6, 0xb5, 0xa6, 0xff, 0xb4, 0xb4, 0xa4, 0xff, 0xb1, 0xb2, 0xa5, 0xff, 0xb0, 0xb4, 0xa8, 0xff, 0xad, 0xb3, 0xa7, 0xff, 0xa8, 0xaf, 0xa4, 0xff, 0xa7, 0xb0, 0xa5, 0xff, 0xa9, 0xb0, 0xa6, 0xc4, 0x00, 0x00, 0x00, 0x00,
    0xff, 0xff, 0xff, 0x01, 0xc5, 0xb9, 0x9d, 0xf2, 0xcb, 0xc2, 0xa7, 0xff, 0xcb, 0xc6, 0xac, 0xff, 0xcb, 0xc5, 0xb0, 0xff, 0xcc, 0xc4, 0xae, 0xff, 0xc5, 0xbf, 0xa1, 0xff, 0xbe, 0xba, 0x98, 0xff, 0xbc, 0xb7, 0x95, 0xff, 0xbf, 0xb7, 0x98, 0xff, 0xc0, 0xbc, 0x9e, 0xff, 0xc3, 0xc0, 0xa5, 0xff, 0xc7, 0xc2, 0xae, 0xff, 0xc8, 0xc6, 0xb5, 0xff, 0xc8, 0xc8, 0xb6, 0xff, 0xc7, 0xc9, 0xb6, 0xff, 0xc6, 0xc8, 0xb4, 0xff, 0xbf, 0xc1, 0xad, 0xff, 0xb4, 0xb5, 0xa0, 0xff, 0xaa, 0xa9, 0x94, 0xff, 0x9c, 0x98, 0x86, 0xff, 0x88, 0x82, 0x73, 0xff, 0x7b, 0x75, 0x6c, 0xff, 0x81, 0x7c, 0x7f, 0xff, 0x85, 0x8b, 0x94, 0xff, 0x8c, 0x9c, 0xa4, 0xff, 0xf6, 0xff, 0xf9, 0xff, 0xb1, 0xad, 0x81, 0xff, 0x17, 0x1a, 0x00, 0xff, 0x19, 0x21, 0x04, 0xff, 0x8a, 0x8f, 0x73, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xf0, 0xeb, 0xd8, 0xff, 0xf0, 0xea, 0xd2, 0xff, 0xdd, 0xda, 0xc1, 0xff, 0x89, 0x92, 0x79, 0xff, 0x61, 0x74, 0x64, 0xff, 0x57, 0x73, 0x6f, 0xff, 0x31, 0x54, 0x5b, 0xff, 0x0b, 0x1b, 0x1e, 0xff, 0x02, 0x00, 0x00, 0xff, 0x4a, 0x62, 0x5e, 0xff, 0x9d, 0xc3, 0xeb, 0xff, 0xd5, 0xe1, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xf6, 0xfa, 0xff, 0xaf, 0xc9, 0xf6, 0xff, 0x74, 0x9a, 0xdc, 0xff, 0x8b, 0xba, 0xfa, 0xff, 0x4c, 0x70, 0x85, 0xff, 0x00, 0x06, 0x00, 0xff, 0x0d, 0x05, 0x06, 0xff, 0x10, 0x0f, 0x0f, 0xff, 0x09, 0x0b, 0x0a, 0xff, 0x04, 0x06, 0x06, 0xff, 0x02, 0x03, 0x03, 0xff, 0x0f, 0x0b, 0x0b, 0xff, 0x2a, 0x33, 0x32, 0xff, 0x52, 0x62, 0x60, 0xff, 0x40, 0x47, 0x45, 0xff, 0x27, 0x2a, 0x27, 0xff, 0x63, 0x66, 0x60, 0xff, 0x31, 0x36, 0x30, 0xff, 0x56, 0x58, 0x50, 0xff, 0x16, 0x19, 0x18, 0xff, 0x00, 0x03, 0x09, 0xff, 0x05, 0x05, 0x07, 0xff, 0x0c, 0x08, 0x07, 0xff, 0x03, 0x05, 0x0a, 0xff, 0x00, 0x00, 0x00, 0xff, 0x3c, 0x43, 0x53, 0xff, 0x89, 0xa1, 0xd4, 0xff, 0x6e, 0x8d, 0xba, 0xff, 0x79, 0x8f, 0xcc, 0xff, 0x66, 0x7d, 0xb7, 0xff, 0x5f, 0x7b, 0xae, 0xff, 0x56, 0x70, 0xa8, 0xff, 0x49, 0x61, 0x96, 0xff, 0x45, 0x5f, 0x93, 0xff, 0x4a, 0x66, 0x9c, 0xff, 0x4d, 0x6d, 0xa6, 0xff, 0x4f, 0x6c, 0xa7, 0xff, 0x48, 0x66, 0x9e, 0xff, 0x3b, 0x5f, 0x95, 0xff, 0x28, 0x4a, 0x84, 0xff, 0x0b, 0x23, 0x5b, 0xff, 0x17, 0x28, 0x46, 0xff, 0x39, 0x3d, 0x6d, 0xff, 0x3b, 0x3d, 0x7b, 0xff, 0x1d, 0x26, 0x46, 0xff, 0x00, 0x02, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x11, 0x0d, 0x04, 0xff, 0x10, 0x0c, 0x0d, 0xff, 0x08, 0x09, 0x11, 0xff, 0x08, 0x08, 0x0f, 0xff, 0x0c, 0x0c, 0x12, 0xff, 0x0d, 0x0d, 0x13, 0xff, 0x0a, 0x0a, 0x11, 0xff, 0x0c, 0x0d, 0x11, 0xff, 0x0e, 0x10, 0x11, 0xff, 0x08, 0x0a, 0x0b, 0xff, 0x0b, 0x0c, 0x0c, 0xff, 0x07, 0x07, 0x07, 0xff, 0x39, 0x39, 0x3a, 0xff, 0x77, 0x75, 0x72, 0xff, 0x50, 0x4e, 0x4e, 0xff, 0x4b, 0x4b, 0x50, 0xff, 0xa3, 0x9d, 0x94, 0xff, 0xde, 0xca, 0xb4, 0xff, 0xd6, 0xbf, 0xa6, 0xff, 0xcd, 0xba, 0xa1, 0xff, 0xc9, 0xba, 0x9f, 0xff, 0xc9, 0xba, 0x9f, 0xff, 0xcb, 0xbd, 0xa4, 0xff, 0xc9, 0xbc, 0xa4, 0xff, 0xc5, 0xb9, 0x9f, 0xff, 0xbf, 0xb7, 0xa2, 0xff, 0xb9, 0xb5, 0xa4, 0xff, 0xb7, 0xb4, 0xa1, 0xff, 0xb3, 0xb2, 0xa3, 0xff, 0xb2, 0xb3, 0xa5, 0xff, 0xb0, 0xb4, 0xa5, 0xff, 0xaa, 0xae, 0xa3, 0xff, 0xa7, 0xac, 0xa4, 0xff, 0xa8, 0xad, 0xa5, 0xf2, 0xff, 0xff, 0xff, 0x01,
    0xc1, 0xb1, 0x92, 0x21, 0xc6, 0xba, 0x9d, 0xff, 0xcd, 0xc3, 0xa8, 0xff, 0xce, 0xc7, 0xac, 0xff, 0xce, 0xc6, 0xb1, 0xff, 0xce, 0xc5, 0xae, 0xff, 0xc6, 0xc0, 0xa2, 0xff, 0xc2, 0xbc, 0x9b, 0xff, 0xc1, 0xba, 0x98, 0xff, 0xc1, 0xb9, 0x9b, 0xff, 0xc2, 0xbd, 0x9f, 0xff, 0xc5, 0xc1, 0xa7, 0xff, 0xc8, 0xc3, 0xaf, 0xff, 0xcb, 0xca, 0xb6, 0xff, 0xcc, 0xcc, 0xba, 0xff, 0xca, 0xcc, 0xb8, 0xff, 0xc8, 0xc9, 0xb7, 0xff, 0xc2, 0xc3, 0xaf, 0xff, 0xb9, 0xb9, 0xa4, 0xff, 0xae, 0xac, 0x97, 0xff, 0x9e, 0x9b, 0x89, 0xff, 0x8c, 0x85, 0x78, 0xff, 0x83, 0x76, 0x74, 0xff, 0x89, 0x86, 0x79, 0xff, 0x79, 0x7d, 0x88, 0xff, 0xa4, 0xb1, 0xcb, 0xff, 0xd9, 0xea, 0xce, 0xff, 0x5b, 0x5a, 0x20, 0xff, 0x33, 0x25, 0x0a, 0xff, 0x15, 0x0e, 0x00, 0xff, 0x55, 0x5c, 0x3b, 0xff, 0xe5, 0xf2, 0xd3, 0xff, 0x8b, 0xa2, 0x84, 0xff, 0x84, 0xa0, 0x89, 0xff, 0xfb, 0xfa, 0xea, 0xff, 0xfb, 0xeb, 0xdc, 0xff, 0xdc, 0xdb, 0xc6, 0xff, 0xc3, 0xc9, 0xb3, 0xff, 0x48, 0x52, 0x44, 0xff, 0x00, 0x01, 0x00, 0xff, 0x00, 0x0a, 0x12, 0xff, 0x00, 0x00, 0x01, 0xff, 0x61, 0x7a, 0x79, 0xff, 0xcd, 0xed, 0xfd, 0xff, 0xfd, 0xfe, 0xfd, 0xff, 0xff, 0xfe, 0xf5, 0xff, 0xd0, 0xec, 0xf8, 0xff, 0xa1, 0xb6, 0xf2, 0xff, 0x60, 0x72, 0xc2, 0xff, 0x5f, 0x7f, 0xda, 0xff, 0x67, 0x86, 0xb0, 0xff, 0x14, 0x1b, 0x18, 0xff, 0x15, 0x0c, 0x16, 0xff, 0x09, 0x07, 0x0c, 0xff, 0x04, 0x07, 0x0a, 0xff, 0x00, 0x02, 0x02, 0xff, 0x06, 0x06, 0x05, 0xff, 0x0c, 0x07, 0x07, 0xff, 0x21, 0x29, 0x2a, 0xff, 0x14, 0x20, 0x21, 0xff, 0x06, 0x0a, 0x0a, 0xff, 0x28, 0x26, 0x24, 0xff, 0x35, 0x34, 0x31, 0xff, 0x0b, 0x0e, 0x09, 0xff, 0x07, 0x05, 0x00, 0xff, 0x1e, 0x1e, 0x1e, 0xff, 0x0b, 0x0d, 0x13, 0xff, 0x02, 0x00, 0x01, 0xff, 0x05, 0x02, 0x00, 0xff, 0x04, 0x09, 0x0b, 0xff, 0x00, 0x02, 0x00, 0xff, 0x5c, 0x66, 0x7e, 0xff, 0x9f, 0xb9, 0xf5, 0xff, 0x6b, 0x91, 0xba, 0xff, 0x55, 0x6d, 0xab, 0xff, 0x61, 0x7a, 0xb5, 0xff, 0x65, 0x7f, 0xb1, 0xff, 0x66, 0x7a, 0xb2, 0xff, 0x67, 0x7b, 0xaf, 0xff, 0x57, 0x6b, 0x9f, 0xff, 0x4a, 0x62, 0x98, 0xff, 0x46, 0x63, 0x9a, 0xff, 0x4a, 0x64, 0xa0, 0xff, 0x4b, 0x65, 0xa0, 0xff, 0x3d, 0x5f, 0x93, 0xff, 0x35, 0x53, 0x8a, 0xff, 0x2c, 0x41, 0x6f, 0xff, 0x06, 0x17, 0x34, 0xff, 0x00, 0x02, 0x04, 0xff, 0x19, 0x1e, 0x2e, 0xff, 0x38, 0x3c, 0x72, 0xff, 0x32, 0x3c, 0x6f, 0xff, 0x14, 0x1b, 0x37, 0xff, 0x00, 0x01, 0x05, 0xff, 0x09, 0x06, 0x0b, 0xff, 0x09, 0x09, 0x0f, 0xff, 0x09, 0x09, 0x0f, 0xff, 0x0c, 0x0c, 0x13, 0xff, 0x0f, 0x0f, 0x15, 0xff, 0x10, 0x0f, 0x16, 0xff, 0x06, 0x07, 0x0d, 0xff, 0x06, 0x09, 0x0d, 0xff, 0x08, 0x0b, 0x0c, 0xff, 0x07, 0x0a, 0x0b, 0xff, 0x04, 0x04, 0x05, 0xff, 0x08, 0x09, 0x09, 0xff, 0x57, 0x54, 0x53, 0xff, 0x4a, 0x4a, 0x4b, 0xff, 0x31, 0x35, 0x3c, 0xff, 0x97, 0x8f, 0x89, 0xff, 0xd8, 0xc4, 0xb0, 0xff, 0xd9, 0xc1, 0xa7, 0xff, 0xd2, 0xbf, 0xa4, 0xff, 0xcb, 0xbb, 0xa0, 0xff, 0xcb, 0xba, 0xa1, 0xff, 0xcf, 0xc1, 0xa6, 0xff, 0xca, 0xbd, 0xa3, 0xff, 0xc6, 0xb8, 0x9e, 0xff, 0xc2, 0xb7, 0xa0, 0xff, 0xbb, 0xb5, 0xa3, 0xff, 0xb9, 0xb4, 0xa1, 0xff, 0xb6, 0xb3, 0xa2, 0xff, 0xb5, 0xb4, 0xa5, 0xff, 0xb1, 0xb2, 0xa2, 0xff, 0xa9, 0xad, 0xa1, 0xff, 0xa5, 0xaa, 0xa1, 0xff, 0xa7, 0xac, 0xa2, 0xff, 0xaa, 0xb1, 0xa2, 0x21,
    0xc1, 0xb4, 0x95, 0x4b, 0xc5, 0xba, 0x9e, 0xff, 0xcd, 0xc3, 0xa8, 0xff, 0xcf, 0xc6, 0xac, 0xff, 0xcf, 0xc7, 0xb0, 0xff, 0xce, 0xc7, 0xae, 0xff, 0xc9, 0xc2, 0xa5, 0xff, 0xc7, 0xbe, 0x9f, 0xff, 0xc4, 0xbb, 0x99, 0xff, 0xc4, 0xbb, 0x99, 0xff, 0xc5, 0xbf, 0xa1, 0xff, 0xc7, 0xc2, 0xa9, 0xff, 0xca, 0xc4, 0xaf, 0xff, 0xcc, 0xc9, 0xb6, 0xff, 0xcd, 0xcd, 0xb9, 0xff, 0xcb, 0xcd, 0xb9, 0xff, 0xc9, 0xcb, 0xb9, 0xff, 0xc4, 0xc5, 0xb3, 0xff, 0xbb, 0xbb, 0xa6, 0xff, 0xb0, 0xae, 0x99, 0xff, 0xa0, 0x9d, 0x8a, 0xff, 0x8d, 0x87, 0x76, 0xff, 0x87, 0x77, 0x70, 0xff, 0x7a, 0x7b, 0x76, 0xff, 0x98, 0xad, 0xb3, 0xff, 0xd6, 0xed, 0xf3, 0xff, 0x5e, 0x6d, 0x54, 0xff, 0x3e, 0x37, 0x05, 0xff, 0x43, 0x3b, 0x1d, 0xff, 0x1c, 0x17, 0x09, 0xff, 0x23, 0x26, 0x16, 0xff, 0x42, 0x60, 0x49, 0xff, 0x0b, 0x3a, 0x25, 0xff, 0x0c, 0x3f, 0x35, 0xff, 0x6e, 0x87, 0x6e, 0xff, 0xdc, 0xdf, 0xbe, 0xff, 0xe3, 0xdf, 0xc5, 0xff, 0x72, 0x70, 0x5d, 0xff, 0x1c, 0x1d, 0x14, 0xff, 0x05, 0x02, 0x02, 0xff, 0x01, 0x07, 0x04, 0xff, 0x0c, 0x14, 0x16, 0xff, 0x8d, 0xab, 0xbf, 0xff, 0xdc, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xf8, 0xf6, 0xff, 0xbd, 0xd8, 0xf6, 0xff, 0xa6, 0xb9, 0xf3, 0xff, 0x7f, 0x92, 0xd6, 0xff, 0x5e, 0x74, 0xbd, 0xff, 0x6e, 0x89, 0xba, 0xff, 0x32, 0x3f, 0x57, 0xff, 0x02, 0x06, 0x0e, 0xff, 0x11, 0x13, 0x12, 0xff, 0x14, 0x10, 0x11, 0xff, 0x11, 0x0f, 0x13, 0xff, 0x05, 0x07, 0x09, 0xff, 0x01, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0a, 0x05, 0xff, 0x04, 0x01, 0x00, 0xff, 0x07, 0x06, 0x04, 0xff, 0x05, 0x04, 0x09, 0xff, 0x00, 0x01, 0x06, 0xff, 0x1b, 0x21, 0x22, 0xff, 0x18, 0x25, 0x1d, 0xff, 0x0f, 0x20, 0x23, 0xff, 0x81, 0x96, 0xb7, 0xff, 0x7d, 0x95, 0xc4, 0xff, 0x77, 0x90, 0xc6, 0xff, 0x5f, 0x77, 0xb4, 0xff, 0x43, 0x60, 0x9d, 0xff, 0x51, 0x69, 0xa4, 0xff, 0x5c, 0x71, 0xad, 0xff, 0x5a, 0x72, 0xab, 0xff, 0x5d, 0x77, 0xae, 0xff, 0x52, 0x6e, 0xa5, 0xff, 0x50, 0x6f, 0xa6, 0xff, 0x4e, 0x6b, 0xa2, 0xff, 0x46, 0x61, 0x96, 0xff, 0x3c, 0x5b, 0x90, 0xff, 0x31, 0x52, 0x87, 0xff, 0x1e, 0x39, 0x65, 0xff, 0x14, 0x1f, 0x37, 0xff, 0x0e, 0x11, 0x19, 0xff, 0x02, 0x02, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x19, 0x1e, 0x3e, 0xff, 0x3a, 0x3f, 0x6c, 0xff, 0x1f, 0x25, 0x38, 0xff, 0x01, 0x05, 0x04, 0xff, 0x01, 0x06, 0x09, 0xff, 0x08, 0x08, 0x0a, 0xff, 0x11, 0x10, 0x11, 0xff, 0x0e, 0x0f, 0x13, 0xff, 0x08, 0x08, 0x13, 0xff, 0x04, 0x09, 0x13, 0xff, 0x00, 0x03, 0x0a, 0xff, 0x07, 0x07, 0x0a, 0xff, 0x0e, 0x0c, 0x0d, 0xff, 0x08, 0x08, 0x07, 0xff, 0x00, 0x05, 0x02, 0xff, 0x1e, 0x23, 0x26, 0xff, 0x5a, 0x55, 0x55, 0xff, 0x30, 0x32, 0x30, 0xff, 0x6b, 0x76, 0x79, 0xff, 0xc5, 0xbc, 0xaa, 0xff, 0xda, 0xc3, 0xa5, 0xff, 0xd0, 0xbe, 0xa7, 0xff, 0xcd, 0xbe, 0xa6, 0xff, 0xcb, 0xbe, 0xa5, 0xff, 0xd7, 0xc3, 0xa9, 0xff, 0xd7, 0xc0, 0xa5, 0xff, 0xca, 0xb9, 0x9c, 0xff, 0xc0, 0xb8, 0x9e, 0xff, 0xb8, 0xb5, 0xa1, 0xff, 0xb5, 0xb2, 0x9f, 0xff, 0xb4, 0xb4, 0xa2, 0xff, 0xb6, 0xb6, 0xa6, 0xff, 0xb2, 0xaf, 0xa2, 0xff, 0xaa, 0xaa, 0x9f, 0xff, 0xa4, 0xa9, 0x9d, 0xff, 0xa6, 0xab, 0x9f, 0xff, 0xa6, 0xad, 0x9f, 0x4b,
    0xc3, 0xb7, 0x96, 0x6b, 0xc7, 0xbc, 0xa0, 0xff, 0xce, 0xc4, 0xa9, 0xff, 0xd1, 0xc7, 0xae, 0xff, 0xd0, 0xc9, 0xb0, 0xff, 0xce, 0xc8, 0xae, 0xff, 0xca, 0xc2, 0xa6, 0xff, 0xc9, 0xbf, 0xa1, 0xff, 0xc7, 0xbd, 0x9b, 0xff, 0xc7, 0xbd, 0x99, 0xff, 0xc6, 0xbf, 0xa0, 0xff, 0xc8, 0xc2, 0xa8, 0xff, 0xca, 0xc5, 0xae, 0xff, 0xcc, 0xc7, 0xb5, 0xff, 0xcb, 0xcb, 0xb8, 0xff, 0xcb, 0xcc, 0xb7, 0xff, 0xc9, 0xcb, 0xb8, 0xff, 0xc4, 0xc5, 0xb3, 0xff, 0xbb, 0xbb, 0xa7, 0xff, 0xb1, 0xae, 0x99, 0xff, 0xa4, 0x9f, 0x8b, 0xff, 0x90, 0x8a, 0x77, 0xff, 0x88, 0x79, 0x6e, 0xff, 0x73, 0x71, 0x74, 0xff, 0xbe, 0xd8, 0xdd, 0xff, 0xab, 0xc8, 0xc7, 0xff, 0x10, 0x1e, 0x11, 0xff, 0x4d, 0x47, 0x25, 0xff, 0x30, 0x30, 0x13, 0xff, 0x12, 0x11, 0x05, 0xff, 0x1c, 0x1c, 0x17, 0xff, 0x09, 0x21, 0x15, 0xff, 0x10, 0x39, 0x2b, 0xff, 0x11, 0x3d, 0x3b, 0xff, 0x00, 0x0d, 0x00, 0xff, 0x2c, 0x35, 0x20, 0xff, 0x40, 0x3b, 0x2f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0c, 0x0d, 0x12, 0xff, 0x00, 0x00, 0x00, 0xff, 0x2d, 0x39, 0x3e, 0xff, 0xc3, 0xe1, 0xf6, 0xff, 0xec, 0xf9, 0xfb, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xe4, 0xf0, 0xf7, 0xff, 0xc3, 0xd8, 0xfa, 0xff, 0xb1, 0xc4, 0xf4, 0xff, 0x90, 0xa6, 0xde, 0xff, 0x7e, 0x93, 0xce, 0xff, 0x6c, 0x84, 0xb6, 0xff, 0x57, 0x71, 0x98, 0xff, 0x11, 0x1a, 0x27, 0xff, 0x0f, 0x0e, 0x0b, 0xff, 0x2a, 0x25, 0x20, 0xff, 0x19, 0x18, 0x1b, 0xff, 0x07, 0x0c, 0x14, 0xff, 0x08, 0x09, 0x0d, 0xff, 0x0d, 0x0c, 0x10, 0xff, 0x14, 0x14, 0x19, 0xff, 0x16, 0x16, 0x1b, 0xff, 0x01, 0x02, 0x06, 0xff, 0x00, 0x00, 0x02, 0xff, 0x06, 0x04, 0x09, 0xff, 0x04, 0x03, 0x06, 0xff, 0x00, 0x00, 0x00, 0xff, 0x02, 0x02, 0x06, 0xff, 0x0c, 0x11, 0x18, 0xff, 0x21, 0x29, 0x35, 0xff, 0x00, 0x00, 0x00, 0xff, 0x40, 0x51, 0x67, 0xff, 0xa8, 0xc0, 0xe4, 0xff, 0x9c, 0xaf, 0xd8, 0xff, 0x88, 0x9a, 0xd4, 0xff, 0x86, 0x9b, 0xd7, 0xff, 0x68, 0x84, 0xbb, 0xff, 0x4a, 0x62, 0x9d, 0xff, 0x4d, 0x63, 0xa1, 0xff, 0x4e, 0x69, 0xa3, 0xff, 0x50, 0x6c, 0xa5, 0xff, 0x50, 0x6e, 0xa5, 0xff, 0x49, 0x69, 0x9f, 0xff, 0x45, 0x65, 0x9b, 0xff, 0x44, 0x61, 0x96, 0xff, 0x3f, 0x5d, 0x93, 0xff, 0x2e, 0x51, 0x89, 0xff, 0x1a, 0x37, 0x66, 0xff, 0x0e, 0x18, 0x31, 0xff, 0x05, 0x07, 0x13, 0xff, 0x0d, 0x0d, 0x14, 0xff, 0x0d, 0x0d, 0x10, 0xff, 0x00, 0x00, 0x04, 0xff, 0x00, 0x02, 0x0f, 0xff, 0x0c, 0x0f, 0x1d, 0xff, 0x0c, 0x0d, 0x1a, 0xff, 0x08, 0x0a, 0x1a, 0xff, 0x01, 0x02, 0x0a, 0xff, 0x0a, 0x0b, 0x0c, 0xff, 0x0c, 0x11, 0x11, 0xff, 0x01, 0x0a, 0x0d, 0xff, 0x08, 0x0c, 0x0b, 0xff, 0x0d, 0x08, 0x03, 0xff, 0x05, 0x00, 0x01, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0c, 0x0c, 0x0d, 0xff, 0x28, 0x20, 0x1f, 0xff, 0x32, 0x30, 0x2f, 0xff, 0x4d, 0x5b, 0x6d, 0xff, 0xac, 0xa5, 0x9e, 0xff, 0xd4, 0xbd, 0xa4, 0xff, 0xcc, 0xba, 0xa7, 0xff, 0xcc, 0xbc, 0xa3, 0xff, 0xcb, 0xbf, 0xa5, 0xff, 0xdb, 0xc6, 0xac, 0xff, 0xde, 0xc2, 0xa8, 0xff, 0xcb, 0xba, 0x9d, 0xff, 0xbc, 0xb6, 0x9b, 0xff, 0xb7, 0xb4, 0x9c, 0xff, 0xb3, 0xb1, 0x9b, 0xff, 0xb3, 0xb2, 0x9f, 0xff, 0xb5, 0xb5, 0xa5, 0xff, 0xb3, 0xae, 0xa0, 0xff, 0xa8, 0xa7, 0x9a, 0xff, 0xa0, 0xa5, 0x99, 0xff, 0xa5, 0xa8, 0x9d, 0xff, 0xa6, 0xab, 0x9d, 0x6b,
    0xc6, 0xb9, 0x98, 0x8b, 0xc9, 0xbe, 0xa3, 0xff, 0xd1, 0xc7, 0xac, 0xff, 0xd3, 0xca, 0xb1, 0xff, 0xd3, 0xcb, 0xb3, 0xff, 0xd0, 0xca, 0xb0, 0xff, 0xca, 0xc2, 0xa6, 0xff, 0xc7, 0xbd, 0x9f, 0xff, 0xc7, 0xbe, 0x9c, 0xff, 0xc8, 0xbe, 0x9b, 0xff, 0xc7, 0xc0, 0x9f, 0xff, 0xc9, 0xc3, 0xa7, 0xff, 0xcb, 0xc5, 0xad, 0xff, 0xcc, 0xc8, 0xb4, 0xff, 0xcc, 0xcb, 0xb7, 0xff, 0xcb, 0xcb, 0xb6, 0xff, 0xca, 0xca, 0xb6, 0xff, 0xc5, 0xc4, 0xb1, 0xff, 0xbb, 0xba, 0xa4, 0xff, 0xb1, 0xae, 0x97, 0xff, 0xa5, 0x9f, 0x8b, 0xff, 0x92, 0x8b, 0x78, 0xff, 0x8a, 0x79, 0x70, 0xff, 0x7c, 0x75, 0x78, 0xff, 0x91, 0xa4, 0xad, 0xff, 0xae, 0xca, 0xd3, 0xff, 0x40, 0x52, 0x4f, 0xff, 0x2d, 0x2f, 0x1c, 0xff, 0x1c, 0x1b, 0x08, 0xff, 0x16, 0x15, 0x0e, 0xff, 0x20, 0x21, 0x1e, 0xff, 0x0a, 0x14, 0x0a, 0xff, 0x13, 0x27, 0x1b, 0xff, 0x17, 0x34, 0x31, 0xff, 0x00, 0x14, 0x18, 0xff, 0x00, 0x01, 0x07, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0d, 0x0d, 0x0d, 0xff, 0x0c, 0x11, 0x13, 0xff, 0x0e, 0x12, 0x17, 0xff, 0x00, 0x00, 0x00, 0xff, 0x55, 0x6c, 0x78, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xfe, 0xf9, 0xff, 0xd5, 0xe7, 0xf7, 0xff, 0xc9, 0xd8, 0xf8, 0xff, 0xac, 0xbf, 0xe7, 0xff, 0x8c, 0xa1, 0xd5, 0xff, 0x75, 0x8c, 0xc7, 0xff, 0x68, 0x80, 0xb5, 0xff, 0x58, 0x75, 0xa3, 0xff, 0x58, 0x66, 0x85, 0xff, 0x0f, 0x0d, 0x13, 0xff, 0x00, 0x01, 0x00, 0xff, 0x0b, 0x17, 0x0f, 0xff, 0x1f, 0x2a, 0x2d, 0xff, 0x74, 0x77, 0x81, 0xff, 0x55, 0x56, 0x5a, 0xff, 0x0d, 0x0e, 0x0d, 0xff, 0x1b, 0x20, 0x21, 0xff, 0x05, 0x09, 0x0c, 0xff, 0x00, 0x01, 0x05, 0xff, 0x0c, 0x11, 0x17, 0xff, 0x10, 0x16, 0x21, 0xff, 0x0a, 0x13, 0x1c, 0xff, 0x17, 0x22, 0x23, 0xff, 0x0d, 0x18, 0x1a, 0xff, 0x00, 0x04, 0x12, 0xff, 0x58, 0x54, 0x71, 0xff, 0x8f, 0x99, 0xbb, 0xff, 0x86, 0x9a, 0xbf, 0xff, 0x95, 0xa6, 0xd3, 0xff, 0x96, 0xa9, 0xdb, 0xff, 0x74, 0x89, 0xbd, 0xff, 0x73, 0x8b, 0xbb, 0xff, 0x73, 0x8b, 0xc0, 0xff, 0x54, 0x6b, 0xa6, 0xff, 0x4e, 0x67, 0xa0, 0xff, 0x54, 0x6f, 0xa8, 0xff, 0x52, 0x70, 0xa7, 0xff, 0x44, 0x64, 0x9a, 0xff, 0x42, 0x61, 0x9b, 0xff, 0x44, 0x61, 0x9b, 0xff, 0x45, 0x62, 0x9a, 0xff, 0x3a, 0x5a, 0x94, 0xff, 0x28, 0x43, 0x78, 0xff, 0x19, 0x24, 0x45, 0xff, 0x06, 0x0d, 0x1e, 0xff, 0x09, 0x0e, 0x14, 0xff, 0x10, 0x10, 0x12, 0xff, 0x09, 0x0c, 0x13, 0xff, 0x02, 0x0a, 0x14, 0xff, 0x00, 0x02, 0x04, 0xff, 0x04, 0x03, 0x09, 0xff, 0x06, 0x04, 0x12, 0xff, 0x02, 0x01, 0x0a, 0xff, 0x00, 0x00, 0x04, 0xff, 0x02, 0x05, 0x06, 0xff, 0x00, 0x07, 0x05, 0xff, 0x04, 0x03, 0x00, 0xff, 0x05, 0x00, 0x00, 0xff, 0x00, 0x00, 0x07, 0xff, 0x10, 0x15, 0x28, 0xff, 0x26, 0x2b, 0x3c, 0xff, 0x30, 0x2d, 0x37, 0xff, 0x14, 0x13, 0x16, 0xff, 0x05, 0x00, 0x00, 0xff, 0x2e, 0x2b, 0x31, 0xff, 0x44, 0x4b, 0x64, 0xff, 0xa2, 0x96, 0x94, 0xff, 0xcd, 0xb9, 0xa3, 0xff, 0xc8, 0xb6, 0xa1, 0xff, 0xc9, 0xb7, 0x9b, 0xff, 0xc6, 0xba, 0xa0, 0xff, 0xd4, 0xc0, 0xa7, 0xff, 0xd8, 0xc0, 0xa6, 0xff, 0xcb, 0xbb, 0x9f, 0xff, 0xbf, 0xb7, 0x9a, 0xff, 0xbc, 0xb4, 0x9a, 0xff, 0xb6, 0xb1, 0x99, 0xff, 0xb6, 0xb4, 0x9d, 0xff, 0xb9, 0xb7, 0xa4, 0xff, 0xb5, 0xb0, 0x9e, 0xff, 0xa6, 0xa4, 0x94, 0xff, 0x9b, 0xa2, 0x94, 0xff, 0xa2, 0xa6, 0x9a, 0xff, 0xa3, 0xa8, 0x9b, 0x8b,
    0xc7, 0xba, 0x97, 0xaa, 0xc9, 0xbd, 0xa3, 0xff, 0xd0, 0xc6, 0xab, 0xff, 0xd3, 0xca, 0xb1, 0xff, 0xd4, 0xcc, 0xb4, 0xff, 0xd2, 0xcb, 0xb2, 0xff, 0xcb, 0xc3, 0xa7, 0xff, 0xc7, 0xbe, 0x9f, 0xff, 0xc7, 0xbe, 0x9c, 0xff, 0xc8, 0xbe, 0x9c, 0xff, 0xc9, 0xc0, 0x9e, 0xff, 0xca, 0xc2, 0xa4, 0xff, 0xcb, 0xc4, 0xab, 0xff, 0xce, 0xc8, 0xb1, 0xff, 0xce, 0xcb, 0xb4, 0xff, 0xcd, 0xcb, 0xb4, 0xff, 0xca, 0xc9, 0xb4, 0xff, 0xc5, 0xc3, 0xaf, 0xff, 0xbd, 0xba, 0xa2, 0xff, 0xb2, 0xae, 0x94, 0xff, 0xa3, 0x9d, 0x87, 0xff, 0x93, 0x8c, 0x77, 0xff, 0x89, 0x79, 0x6f, 0xff, 0x80, 0x78, 0x7a, 0xff, 0x80, 0x8c, 0x97, 0xff, 0xb4, 0xca, 0xd5, 0xff, 0x66, 0x77, 0x7b, 0xff, 0x2a, 0x31, 0x28, 0xff, 0x1d, 0x1c, 0x10, 0xff, 0x0b, 0x0e, 0x0c, 0xff, 0x0e, 0x12, 0x13, 0xff, 0x1b, 0x1a, 0x16, 0xff, 0x20, 0x26, 0x1e, 0xff, 0x28, 0x40, 0x3d, 0xff, 0x12, 0x2b, 0x2f, 0xff, 0x12, 0x1e, 0x23, 0xff, 0x16, 0x16, 0x18, 0xff, 0x1a, 0x1d, 0x1a, 0xff, 0x10, 0x17, 0x14, 0xff, 0x05, 0x0a, 0x08, 0xff, 0x00, 0x00, 0x00, 0xff, 0x93, 0xaa, 0xb7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd9, 0xef, 0xf6, 0xff, 0xc1, 0xd7, 0xf9, 0xff, 0xc0, 0xcc, 0xf3, 0xff, 0xa9, 0xbc, 0xe5, 0xff, 0x96, 0xaa, 0xdc, 0xff, 0x6f, 0x85, 0xbd, 0xff, 0x53, 0x6b, 0xa1, 0xff, 0x57, 0x71, 0xa3, 0xff, 0x49, 0x66, 0x98, 0xff, 0x4a, 0x5e, 0x7d, 0xff, 0x1f, 0x26, 0x27, 0xff, 0x00, 0x04, 0x00, 0xff, 0x04, 0x12, 0x09, 0xff, 0x1a, 0x1b, 0x16, 0xff, 0x2f, 0x31, 0x27, 0xff, 0x00, 0x06, 0x00, 0xff, 0x00, 0x02, 0x00, 0xff, 0x07, 0x0f, 0x08, 0xff, 0x11, 0x1b, 0x17, 0xff, 0x08, 0x13, 0x0d, 0xff, 0x05, 0x15, 0x1c, 0xff, 0x00, 0x0a, 0x13, 0xff, 0x00, 0x0e, 0x0c, 0xff, 0x10, 0x20, 0x21, 0xff, 0x3c, 0x47, 0x57, 0xff, 0xac, 0xae, 0xcf, 0xff, 0xa1, 0xa9, 0xd4, 0xff, 0x8e, 0x9f, 0xca, 0xff, 0x7b, 0x8a, 0xb8, 0xff, 0x76, 0x87, 0xb7, 0xff, 0x7d, 0x91, 0xc1, 0xff, 0x74, 0x8a, 0xb7, 0xff, 0x65, 0x7b, 0xaf, 0xff, 0x59, 0x72, 0xab, 0xff, 0x56, 0x70, 0xa8, 0xff, 0x52, 0x6c, 0xa4, 0xff, 0x4f, 0x6d, 0xa4, 0xff, 0x4f, 0x6e, 0xa4, 0xff, 0x4e, 0x6d, 0xa8, 0xff, 0x47, 0x65, 0xa1, 0xff, 0x48, 0x65, 0x9d, 0xff, 0x41, 0x5f, 0x9a, 0xff, 0x30, 0x4c, 0x83, 0xff, 0x24, 0x33, 0x59, 0xff, 0x11, 0x1d, 0x33, 0xff, 0x0a, 0x12, 0x1c, 0xff, 0x15, 0x14, 0x17, 0xff, 0x0d, 0x0e, 0x10, 0xff, 0x06, 0x0b, 0x10, 0xff, 0x08, 0x09, 0x0b, 0xff, 0x04, 0x01, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x05, 0xff, 0x00, 0x00, 0x0b, 0xff, 0x01, 0x00, 0x0e, 0xff, 0x07, 0x07, 0x16, 0xff, 0x16, 0x20, 0x3a, 0xff, 0x39, 0x4d, 0x75, 0xff, 0x56, 0x71, 0xa6, 0xff, 0x68, 0x80, 0xb6, 0xff, 0x85, 0x95, 0xc8, 0xff, 0x79, 0x84, 0xa2, 0xff, 0x1b, 0x1e, 0x1d, 0xff, 0x0e, 0x10, 0x16, 0xff, 0x2d, 0x37, 0x48, 0xff, 0xa1, 0x98, 0x8c, 0xff, 0xd2, 0xc0, 0x9d, 0xff, 0xc8, 0xb6, 0x9b, 0xff, 0xcb, 0xb4, 0x9a, 0xff, 0xc1, 0xb6, 0x9b, 0xff, 0xcb, 0xbb, 0xa1, 0xff, 0xd3, 0xbc, 0xa2, 0xff, 0xc9, 0xb9, 0x9e, 0xff, 0xc2, 0xb8, 0x9c, 0xff, 0xc0, 0xb5, 0x9b, 0xff, 0xb9, 0xb3, 0x9a, 0xff, 0xb7, 0xb5, 0x9d, 0xff, 0xb8, 0xb7, 0xa2, 0xff, 0xb5, 0xb2, 0x9f, 0xff, 0xa7, 0xa6, 0x96, 0xff, 0x9b, 0xa0, 0x91, 0xff, 0x9f, 0xa3, 0x93, 0xff, 0xa1, 0xa4, 0x96, 0xab,
    0xc7, 0xba, 0x99, 0xbe, 0xc8, 0xbd, 0xa2, 0xff, 0xcf, 0xc5, 0xaa, 0xff, 0xd2, 0xc8, 0xaf, 0xff, 0xd2, 0xcb, 0xb3, 0xff, 0xd2, 0xcb, 0xb2, 0xff, 0xcb, 0xc3, 0xa8, 0xff, 0xc7, 0xbe, 0x9f, 0xff, 0xc7, 0xbe, 0x9c, 0xff, 0xc8, 0xbe, 0x9c, 0xff, 0xc9, 0xbf, 0x9d, 0xff, 0xcb, 0xc0, 0xa1, 0xff, 0xcb, 0xc2, 0xa8, 0xff, 0xcd, 0xc7, 0xaf, 0xff, 0xcf, 0xca, 0xb2, 0xff, 0xce, 0xcb, 0xb1, 0xff, 0xca, 0xc9, 0xb2, 0xff, 0xc6, 0xc3, 0xab, 0xff, 0xbc, 0xb9, 0x9f, 0xff, 0xb1, 0xac, 0x92, 0xff, 0xa3, 0x9c, 0x83, 0xff, 0x93, 0x8a, 0x73, 0xff, 0x88, 0x79, 0x6d, 0xff, 0x83, 0x7a, 0x7a, 0xff, 0x7c, 0x84, 0x8b, 0xff, 0xa4, 0xb4, 0xbd, 0xff, 0x5b, 0x68, 0x6a, 0xff, 0x1a, 0x21, 0x17, 0xff, 0x35, 0x34, 0x2e, 0xff, 0x17, 0x20, 0x23, 0xff, 0x00, 0x01, 0x05, 0xff, 0x08, 0x04, 0x04, 0xff, 0x1b, 0x1c, 0x17, 0xff, 0x62, 0x7e, 0x7c, 0xff, 0x4d, 0x70, 0x6c, 0xff, 0x15, 0x25, 0x1f, 0xff, 0x11, 0x10, 0x0d, 0xff, 0x06, 0x08, 0x04, 0xff, 0x0d, 0x14, 0x10, 0xff, 0x00, 0x00, 0x00, 0xff, 0x37, 0x39, 0x43, 0xff, 0xfa, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xeb, 0xed, 0xf6, 0xff, 0xbc, 0xcf, 0xef, 0xff, 0xaf, 0xc6, 0xf8, 0xff, 0xb0, 0xc0, 0xf6, 0xff, 0xa2, 0xb3, 0xe7, 0xff, 0x88, 0x9b, 0xd2, 0xff, 0x6b, 0x7e, 0xb6, 0xff, 0x51, 0x67, 0x9c, 0xff, 0x4e, 0x63, 0x96, 0xff, 0x1a, 0x39, 0x74, 0xff, 0x11, 0x31, 0x62, 0xff, 0x4e, 0x63, 0x79, 0xff, 0x47, 0x53, 0x60, 0xff, 0x2d, 0x3e, 0x47, 0xff, 0x23, 0x2c, 0x30, 0xff, 0x18, 0x24, 0x24, 0xff, 0x0f, 0x1d, 0x20, 0xff, 0x16, 0x21, 0x24, 0xff, 0x1d, 0x2e, 0x31, 0xff, 0x2f, 0x3f, 0x43, 0xff, 0x37, 0x43, 0x45, 0xff, 0x34, 0x4b, 0x57, 0xff, 0x51, 0x62, 0x73, 0xff, 0x6e, 0x7f, 0x8f, 0xff, 0x88, 0x9c, 0xb0, 0xff, 0x9b, 0xad, 0xcb, 0xff, 0x99, 0xa7, 0xcf, 0xff, 0x95, 0xa2, 0xd0, 0xff, 0x87, 0x97, 0xc6, 0xff, 0x82, 0x93, 0xc1, 0xff, 0x71, 0x84, 0xb4, 0xff, 0x70, 0x83, 0xb6, 0xff, 0x70, 0x84, 0xb8, 0xff, 0x5f, 0x76, 0xab, 0xff, 0x53, 0x6d, 0xa3, 0xff, 0x50, 0x6b, 0xa2, 0xff, 0x51, 0x6d, 0xa4, 0xff, 0x4b, 0x68, 0xa0, 0xff, 0x4e, 0x6d, 0xa3, 0xff, 0x42, 0x64, 0x9e, 0xff, 0x40, 0x60, 0x9b, 0xff, 0x49, 0x67, 0x9d, 0xff, 0x41, 0x60, 0x98, 0xff, 0x38, 0x57, 0x8c, 0xff, 0x24, 0x37, 0x5f, 0xff, 0x16, 0x25, 0x3f, 0xff, 0x0d, 0x16, 0x25, 0xff, 0x12, 0x11, 0x16, 0xff, 0x0c, 0x0b, 0x0f, 0xff, 0x04, 0x08, 0x0f, 0xff, 0x06, 0x06, 0x0d, 0xff, 0x00, 0x00, 0x00, 0xff, 0x04, 0x09, 0x05, 0xff, 0x07, 0x0e, 0x18, 0xff, 0x0c, 0x14, 0x2d, 0xff, 0x14, 0x1d, 0x42, 0xff, 0x22, 0x25, 0x50, 0xff, 0x28, 0x35, 0x68, 0xff, 0x31, 0x4e, 0x87, 0xff, 0x42, 0x62, 0x9e, 0xff, 0x3c, 0x5d, 0xa0, 0xff, 0x32, 0x50, 0x98, 0xff, 0x3b, 0x56, 0xa3, 0xff, 0x72, 0x88, 0xcf, 0xff, 0x7e, 0x88, 0xad, 0xff, 0x20, 0x29, 0x39, 0xff, 0x0c, 0x1c, 0x26, 0xff, 0x98, 0x90, 0x76, 0xff, 0xe1, 0xc8, 0x9f, 0xff, 0xd1, 0xb7, 0x9b, 0xff, 0xce, 0xb5, 0x9c, 0xff, 0xc2, 0xb8, 0x9c, 0xff, 0xc8, 0xbb, 0xa0, 0xff, 0xcf, 0xb9, 0xa1, 0xff, 0xc6, 0xb5, 0x9c, 0xff, 0xc1, 0xb5, 0x9c, 0xff, 0xbf, 0xb5, 0x9d, 0xff, 0xb8, 0xb3, 0x9c, 0xff, 0xb4, 0xb3, 0x9e, 0xff, 0xb2, 0xb2, 0xa0, 0xff, 0xaf, 0xaf, 0x9f, 0xff, 0xa6, 0xa7, 0x97, 0xff, 0x9e, 0xa1, 0x91, 0xff, 0x9e, 0xa1, 0x92, 0xff, 0x9f, 0xa2, 0x93, 0xbe,
    0xc7, 0xba, 0x98, 0xd3, 0xc8, 0xbc, 0xa1, 0xff, 0xce, 0xc4, 0xaa, 0xff, 0xd1, 0xc8, 0xaf, 0xff, 0xd2, 0xca, 0xb2, 0xff, 0xd2, 0xcb, 0xb2, 0xff, 0xcc, 0xc5, 0xa8, 0xff, 0xca, 0xbf, 0xa2, 0xff, 0xc7, 0xbe, 0x9d, 0xff, 0xc4, 0xba, 0x99, 0xff, 0xc7, 0xbd, 0x9a, 0xff, 0xcb, 0xc1, 0x9f, 0xff, 0xcb, 0xc1, 0xa6, 0xff, 0xcc, 0xc6, 0xae, 0xff, 0xcd, 0xc9, 0xaf, 0xff, 0xcd, 0xc9, 0xb0, 0xff, 0xcc, 0xc8, 0xb0, 0xff, 0xc5, 0xc1, 0xa9, 0xff, 0xb9, 0xb6, 0x9b, 0xff, 0xaf, 0xaa, 0x8f, 0xff, 0xa2, 0x9a, 0x82, 0xff, 0x93, 0x88, 0x70, 0xff, 0x86, 0x79, 0x6b, 0xff, 0x81, 0x79, 0x75, 0xff, 0x83, 0x89, 0x89, 0xff, 0xab, 0xb7, 0xb9, 0xff, 0x59, 0x62, 0x5e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x13, 0x11, 0x10, 0xff, 0x14, 0x1f, 0x28, 0xff, 0x10, 0x1f, 0x27, 0xff, 0x0f, 0x0d, 0x0d, 0xff, 0x01, 0x01, 0x00, 0xff, 0x61, 0x89, 0x86, 0xff, 0x78, 0xa0, 0x97, 0xff, 0x07, 0x15, 0x0d, 0xff, 0x02, 0x00, 0x00, 0xff, 0x02, 0x00, 0x04, 0xff, 0x1d, 0x20, 0x27, 0xff, 0x0c, 0x0a, 0x17, 0xff, 0xd6, 0xd8, 0xdc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xfc, 0xff, 0xc4, 0xe1, 0xf3, 0xff, 0xb8, 0xc4, 0xf8, 0xff, 0x98, 0xae, 0xec, 0xff, 0x7b, 0x94, 0xd5, 0xff, 0x75, 0x85, 0xc6, 0xff, 0x6d, 0x7c, 0xb9, 0xff, 0x6b, 0x7b, 0xb3, 0xff, 0x72, 0x85, 0xb9, 0xff, 0x79, 0x8d, 0xbe, 0xff, 0x62, 0x77, 0xaf, 0xff, 0x21, 0x30, 0x65, 0xff, 0x05, 0x11, 0x40, 0xff, 0x2e, 0x45, 0x79, 0xff, 0x41, 0x5f, 0x92, 0xff, 0x58, 0x78, 0x9e, 0xff, 0x5c, 0x7c, 0x9e, 0xff, 0x5e, 0x7d, 0xa1, 0xff, 0x6f, 0x8c, 0xae, 0xff, 0x76, 0x92, 0xb3, 0xff, 0x85, 0xa0, 0xc0, 0xff, 0x9f, 0xb9, 0xd8, 0xff, 0x9e, 0xb7, 0xd4, 0xff, 0xa8, 0xc0, 0xe0, 0xff, 0xa7, 0xbd, 0xe6, 0xff, 0x95, 0xa8, 0xd9, 0xff, 0x8a, 0x9e, 0xd2, 0xff, 0x7e, 0x95, 0xc6, 0xff, 0x7b, 0x8e, 0xbd, 0xff, 0x7c, 0x8d, 0xbb, 0xff, 0x77, 0x8b, 0xb9, 0xff, 0x6f, 0x83, 0xb5, 0xff, 0x61, 0x76, 0xac, 0xff, 0x5e, 0x70, 0xae, 0xff, 0x58, 0x6e, 0xa5, 0xff, 0x4f, 0x6a, 0x9b, 0xff, 0x4c, 0x67, 0x9c, 0xff, 0x50, 0x6b, 0xa3, 0xff, 0x4f, 0x6c, 0xa3, 0xff, 0x4d, 0x6c, 0xa2, 0xff, 0x44, 0x67, 0xa2, 0xff, 0x3f, 0x63, 0x9b, 0xff, 0x47, 0x65, 0x97, 0xff, 0x41, 0x61, 0x97, 0xff, 0x3c, 0x5c, 0x8e, 0xff, 0x2e, 0x45, 0x6c, 0xff, 0x1d, 0x2e, 0x4d, 0xff, 0x0e, 0x18, 0x2a, 0xff, 0x08, 0x07, 0x0f, 0xff, 0x0a, 0x08, 0x10, 0xff, 0x00, 0x02, 0x0e, 0xff, 0x01, 0x03, 0x0a, 0xff, 0x02, 0x05, 0x18, 0xff, 0x10, 0x1d, 0x40, 0xff, 0x1a, 0x2b, 0x54, 0xff, 0x1a, 0x33, 0x65, 0xff, 0x23, 0x3d, 0x75, 0xff, 0x25, 0x3d, 0x75, 0xff, 0x23, 0x3e, 0x7c, 0xff, 0x2b, 0x49, 0x87, 0xff, 0x3b, 0x54, 0x89, 0xff, 0x40, 0x55, 0x8a, 0xff, 0x34, 0x48, 0x87, 0xff, 0x31, 0x43, 0x8b, 0xff, 0x26, 0x3e, 0xa2, 0xff, 0x5d, 0x6d, 0xc6, 0xff, 0x6c, 0x7a, 0x9c, 0xff, 0x27, 0x3d, 0x45, 0xff, 0xa5, 0x99, 0x7d, 0xff, 0xea, 0xc6, 0xa7, 0xff, 0xd7, 0xb5, 0xa0, 0xff, 0xcf, 0xb8, 0x9b, 0xff, 0xc3, 0xb9, 0x9c, 0xff, 0xc6, 0xbb, 0x9f, 0xff, 0xcb, 0xb8, 0x9e, 0xff, 0xc5, 0xb3, 0x9c, 0xff, 0xbe, 0xb1, 0x9b, 0xff, 0xbb, 0xb2, 0x9d, 0xff, 0xb6, 0xb0, 0x9c, 0xff, 0xae, 0xae, 0x9c, 0xff, 0xac, 0xae, 0x9e, 0xff, 0xa9, 0xab, 0x9d, 0xff, 0xa0, 0xa2, 0x94, 0xff, 0x9a, 0x9d, 0x8c, 0xff, 0x9e, 0xa1, 0x91, 0xff, 0x9f, 0xa3, 0x93, 0xd3,
    0xc6, 0xb9, 0x98, 0xe7, 0xc7, 0xbd, 0xa1, 0xff, 0xd0, 0xc5, 0xa9, 0xff, 0xd4, 0xc8, 0xb0, 0xff, 0xd1, 0xc8, 0xb2, 0xff, 0xd0, 0xca, 0xb1, 0xff, 0xcb, 0xc6, 0xa9, 0xff, 0xc9, 0xc2, 0xa1, 0xff, 0xc8, 0xbd, 0x99, 0xff, 0xc7, 0xba, 0x94, 0xff, 0xc6, 0xbb, 0x98, 0xff, 0xc7, 0xbd, 0x9e, 0xff, 0xcb, 0xc0, 0xa4, 0xff, 0xcd, 0xc4, 0xaa, 0xff, 0xcf, 0xc7, 0xaf, 0xff, 0xce, 0xc8, 0xb0, 0xff, 0xcc, 0xc7, 0xaf, 0xff, 0xc6, 0xc2, 0xa6, 0xff, 0xbb, 0xb6, 0x98, 0xff, 0xb0, 0xaa, 0x8a, 0xff, 0xa3, 0x99, 0x7e, 0xff, 0x94, 0x87, 0x6f, 0xff, 0x8a, 0x79, 0x6d, 0xff, 0x83, 0x79, 0x77, 0xff, 0x86, 0x89, 0x8d, 0xff, 0xaa, 0xb4, 0xba, 0xff, 0x64, 0x6e, 0x6c, 0xff, 0x00, 0x00, 0x00, 0xff, 0x04, 0x05, 0x00, 0xff, 0x01, 0x04, 0x01, 0xff, 0x13, 0x1d, 0x1c, 0xff, 0x1d, 0x29, 0x28, 0xff, 0x01, 0x13, 0x15, 0xff, 0x53, 0x7e, 0x81, 0xff, 0x61, 0x87, 0x89, 0xff, 0x03, 0x06, 0x08, 0xff, 0x1a, 0x1c, 0x15, 0xff, 0xa7, 0xb2, 0x92, 0xff, 0x0e, 0x0e, 0x0e, 0xff, 0x4d, 0x47, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf8, 0xf7, 0xff, 0xd5, 0xe3, 0xf7, 0xff, 0xb5, 0xd6, 0xfc, 0xff, 0x92, 0xac, 0xea, 0xff, 0x60, 0x79, 0xcc, 0xff, 0x51, 0x65, 0xba, 0xff, 0x51, 0x60, 0xad, 0xff, 0x53, 0x67, 0xa7, 0xff, 0x68, 0x7c, 0xb4, 0xff, 0x82, 0x95, 0xca, 0xff, 0x93, 0xa3, 0xd7, 0xff, 0x9e, 0xb0, 0xe4, 0xff, 0x8f, 0xa1, 0xd4, 0xff, 0x49, 0x5a, 0x90, 0xff, 0x0a, 0x1d, 0x56, 0xff, 0x0b, 0x21, 0x5a, 0xff, 0x2b, 0x42, 0x78, 0xff, 0x4f, 0x6c, 0xa0, 0xff, 0x63, 0x82, 0xb7, 0xff, 0x6b, 0x89, 0xbd, 0xff, 0x7a, 0x97, 0xc9, 0xff, 0x86, 0xa3, 0xd0, 0xff, 0x88, 0xa2, 0xc9, 0xff, 0x85, 0x9b, 0xca, 0xff, 0x7f, 0x93, 0xcb, 0xff, 0x76, 0x8c, 0xc7, 0xff, 0x6c, 0x84, 0xc1, 0xff, 0x65, 0x7a, 0xb6, 0xff, 0x67, 0x7a, 0xb3, 0xff, 0x62, 0x74, 0xab, 0xff, 0x5e, 0x6f, 0xa5, 0xff, 0x5d, 0x6e, 0xa5, 0xff, 0x5d, 0x6f, 0xa6, 0xff, 0x59, 0x6a, 0xa3, 0xff, 0x60, 0x6f, 0xaa, 0xff, 0x55, 0x6b, 0xa4, 0xff, 0x4b, 0x66, 0x9b, 0xff, 0x4d, 0x67, 0x9d, 0xff, 0x43, 0x61, 0x97, 0xff, 0x45, 0x64, 0x9a, 0xff, 0x47, 0x65, 0x9f, 0xff, 0x44, 0x62, 0x9f, 0xff, 0x42, 0x61, 0x9b, 0xff, 0x3f, 0x5c, 0x93, 0xff, 0x46, 0x63, 0x96, 0xff, 0x41, 0x5b, 0x86, 0xff, 0x32, 0x4b, 0x6e, 0xff, 0x2d, 0x3c, 0x58, 0xff, 0x19, 0x1c, 0x29, 0xff, 0x02, 0x05, 0x02, 0xff, 0x03, 0x08, 0x10, 0xff, 0x04, 0x0f, 0x2d, 0xff, 0x0f, 0x20, 0x40, 0xff, 0x18, 0x2b, 0x53, 0xff, 0x1d, 0x33, 0x69, 0xff, 0x26, 0x3d, 0x73, 0xff, 0x2f, 0x48, 0x84, 0xff, 0x29, 0x45, 0x7f, 0xff, 0x21, 0x40, 0x79, 0xff, 0x2f, 0x48, 0x7a, 0xff, 0x42, 0x53, 0x82, 0xff, 0x43, 0x56, 0x8b, 0xff, 0x36, 0x48, 0x81, 0xff, 0x24, 0x34, 0x72, 0xff, 0x33, 0x3a, 0x79, 0xff, 0x2e, 0x3c, 0x89, 0xff, 0x26, 0x35, 0x97, 0xff, 0x5e, 0x6c, 0xbd, 0xff, 0x60, 0x7a, 0x8d, 0xff, 0xb3, 0xa6, 0x87, 0xff, 0xe4, 0xc1, 0xa2, 0xff, 0xce, 0xb7, 0x9d, 0xff, 0xcb, 0xb7, 0x9b, 0xff, 0xc6, 0xb6, 0x9b, 0xff, 0xc8, 0xb9, 0x9f, 0xff, 0xc5, 0xb7, 0x9d, 0xff, 0xbf, 0xb1, 0x98, 0xff, 0xbb, 0xaf, 0x9a, 0xff, 0xb7, 0xaf, 0x9d, 0xff, 0xb1, 0xae, 0x9b, 0xff, 0xab, 0xac, 0x9c, 0xff, 0xa7, 0xaa, 0x9c, 0xff, 0xa3, 0xa5, 0x9a, 0xff, 0x99, 0x9c, 0x90, 0xff, 0x95, 0x9a, 0x8a, 0xff, 0x99, 0x9e, 0x8d, 0xff, 0x9c, 0x9e, 0x8d, 0xe6,
    0xc5, 0xb8, 0x96, 0xf0, 0xc6, 0xbd, 0x9f, 0xff, 0xcf, 0xc5, 0xa9, 0xff, 0xd4, 0xc8, 0xaf, 0xff, 0xd1, 0xc8, 0xb2, 0xff, 0xd1, 0xca, 0xb1, 0xff, 0xcc, 0xc7, 0xa9, 0xff, 0xc7, 0xc2, 0x9f, 0xff, 0xc7, 0xbb, 0x95, 0xff, 0xc8, 0xb8, 0x90, 0xff, 0xc6, 0xb9, 0x96, 0xff, 0xc5, 0xbb, 0x9b, 0xff, 0xca, 0xbf, 0xa0, 0xff, 0xcd, 0xc4, 0xa6, 0xff, 0xcf, 0xc6, 0xac, 0xff, 0xcf, 0xc6, 0xae, 0xff, 0xca, 0xc5, 0xac, 0xff, 0xc5, 0xc1, 0xa4, 0xff, 0xbc, 0xb7, 0x94, 0xff, 0xb1, 0xa9, 0x85, 0xff, 0xa2, 0x96, 0x79, 0xff, 0x94, 0x85, 0x6e, 0xff, 0x8c, 0x79, 0x6d, 0xff, 0x85, 0x79, 0x79, 0xff, 0x89, 0x88, 0x91, 0xff, 0xa1, 0xaa, 0xb4, 0xff, 0x7d, 0x88, 0x88, 0xff, 0x0a, 0x16, 0x07, 0xff, 0x26, 0x29, 0x1d, 0xff, 0x35, 0x2f, 0x2a, 0xff, 0x2b, 0x25, 0x23, 0xff, 0x08, 0x0e, 0x11, 0xff, 0x10, 0x2a, 0x31, 0xff, 0x6b, 0x98, 0x9e, 0xff, 0x2e, 0x46, 0x58, 0xff, 0x00, 0x00, 0x00, 0xff, 0x83, 0x8e, 0x6f, 0xff, 0xcf, 0xeb, 0xac, 0xff, 0x16, 0x25, 0x1d, 0xff, 0xcb, 0xd0, 0xe7, 0xff, 0xef, 0xfa, 0xff, 0xff, 0xb7, 0xcf, 0xef, 0xff, 0xaa, 0xc3, 0xfc, 0xff, 0x91, 0xac, 0xed, 0xff, 0x61, 0x7c, 0xc2, 0xff, 0x56, 0x65, 0xbb, 0xff, 0x57, 0x6c, 0xb3, 0xff, 0x4e, 0x6a, 0xa8, 0xff, 0x42, 0x5b, 0xa3, 0xff, 0x4a, 0x65, 0xaa, 0xff, 0x61, 0x7b, 0xb6, 0xff, 0x71, 0x8d, 0xb9, 0xff, 0x6d, 0x87, 0xbc, 0xff, 0x70, 0x8a, 0xc7, 0xff, 0x6f, 0x86, 0xc1, 0xff, 0x3d, 0x51, 0x88, 0xff, 0x1c, 0x2d, 0x61, 0xff, 0x29, 0x34, 0x6a, 0xff, 0x3a, 0x4f, 0x84, 0xff, 0x51, 0x6d, 0xa1, 0xff, 0x67, 0x83, 0xb8, 0xff, 0x78, 0x95, 0xc9, 0xff, 0x8b, 0xa5, 0xd4, 0xff, 0x90, 0xa6, 0xd0, 0xff, 0x8d, 0x9f, 0xd2, 0xff, 0x80, 0x93, 0xce, 0xff, 0x6f, 0x88, 0xc0, 0xff, 0x68, 0x82, 0xba, 0xff, 0x69, 0x80, 0xba, 0xff, 0x64, 0x77, 0xb1, 0xff, 0x57, 0x6a, 0xa2, 0xff, 0x53, 0x67, 0x9f, 0xff, 0x47, 0x5b, 0x93, 0xff, 0x48, 0x5b, 0x93, 0xff, 0x43, 0x56, 0x8d, 0xff, 0x43, 0x56, 0x8b, 0xff, 0x48, 0x5d, 0x95, 0xff, 0x45, 0x5c, 0x95, 0xff, 0x47, 0x62, 0x97, 0xff, 0x44, 0x61, 0x95, 0xff, 0x43, 0x60, 0x97, 0xff, 0x3f, 0x5c, 0x96, 0xff, 0x39, 0x57, 0x92, 0xff, 0x3a, 0x59, 0x94, 0xff, 0x3c, 0x5c, 0x94, 0xff, 0x45, 0x60, 0x92, 0xff, 0x38, 0x4e, 0x76, 0xff, 0x26, 0x3b, 0x5b, 0xff, 0x26, 0x31, 0x50, 0xff, 0x13, 0x17, 0x29, 0xff, 0x07, 0x0d, 0x10, 0xff, 0x0c, 0x17, 0x2d, 0xff, 0x1e, 0x30, 0x63, 0xff, 0x28, 0x40, 0x79, 0xff, 0x2e, 0x47, 0x7a, 0xff, 0x2b, 0x42, 0x76, 0xff, 0x32, 0x47, 0x7f, 0xff, 0x2d, 0x41, 0x7c, 0xff, 0x28, 0x3e, 0x78, 0xff, 0x33, 0x4e, 0x87, 0xff, 0x31, 0x45, 0x73, 0xff, 0x42, 0x4d, 0x76, 0xff, 0x34, 0x48, 0x7c, 0xff, 0x22, 0x38, 0x74, 0xff, 0x17, 0x2a, 0x69, 0xff, 0x19, 0x25, 0x60, 0xff, 0x24, 0x31, 0x70, 0xff, 0x22, 0x2e, 0x81, 0xff, 0x3a, 0x47, 0xa7, 0xff, 0x66, 0x7b, 0xad, 0xff, 0xad, 0xa0, 0x88, 0xff, 0xde, 0xc1, 0x99, 0xff, 0xc9, 0xb8, 0x9d, 0xff, 0xc7, 0xb5, 0x9b, 0xff, 0xc6, 0xb5, 0x9a, 0xff, 0xc7, 0xb8, 0x9e, 0xff, 0xc3, 0xb6, 0x9e, 0xff, 0xba, 0xaf, 0x96, 0xff, 0xb6, 0xac, 0x96, 0xff, 0xb2, 0xab, 0x9b, 0xff, 0xad, 0xab, 0x9a, 0xff, 0xa8, 0xaa, 0x9b, 0xff, 0xa3, 0xa8, 0x9b, 0xff, 0x9d, 0xa0, 0x97, 0xff, 0x96, 0x99, 0x8e, 0xff, 0x94, 0x99, 0x8a, 0xff, 0x95, 0x9a, 0x8a, 0xff, 0x95, 0x9a, 0x89, 0xf0,
    0xc3, 0xb7, 0x93, 0xf3, 0xc4, 0xbc, 0x9c, 0xff, 0xcb, 0xc4, 0xa8, 0xff, 0xd0, 0xc9, 0xae, 0xff, 0xd2, 0xc8, 0xb0, 0xff, 0xd1, 0xc9, 0xaf, 0xff, 0xcc, 0xc7, 0xa9, 0xff, 0xc7, 0xbf, 0x9e, 0xff, 0xc5, 0xba, 0x93, 0xff, 0xc5, 0xb7, 0x8e, 0xff, 0xc5, 0xb8, 0x92, 0xff, 0xc6, 0xba, 0x98, 0xff, 0xc8, 0xbe, 0x9c, 0xff, 0xcc, 0xc2, 0xa3, 0xff, 0xce, 0xc5, 0xa9, 0xff, 0xce, 0xc5, 0xab, 0xff, 0xcb, 0xc3, 0xa9, 0xff, 0xc6, 0xbf, 0xa0, 0xff, 0xbb, 0xb4, 0x91, 0xff, 0xae, 0xa5, 0x82, 0xff, 0xa0, 0x94, 0x76, 0xff, 0x92, 0x84, 0x6c, 0xff, 0x88, 0x79, 0x6c, 0xff, 0x84, 0x77, 0x77, 0xff, 0x89, 0x87, 0x8e, 0xff, 0x98, 0x9f, 0xa8, 0xff, 0xa0, 0xaa, 0xad, 0xff, 0x28, 0x34, 0x2d, 0xff, 0x1f, 0x24, 0x17, 0xff, 0x41, 0x39, 0x2f, 0xff, 0x39, 0x2a, 0x27, 0xff, 0x0a, 0x02, 0x03, 0xff, 0x27, 0x38, 0x3b, 0xff, 0x53, 0x80, 0x83, 0xff, 0x12, 0x32, 0x29, 0xff, 0x27, 0x2e, 0x2d, 0xff, 0x7d, 0x89, 0x7f, 0xff, 0x8e, 0xa9, 0x95, 0xff, 0x99, 0xb6, 0xbe, 0xff, 0xcc, 0xe5, 0xe7, 0xff, 0xc7, 0xec, 0xfe, 0xff, 0x92, 0xb6, 0xf2, 0xff, 0x84, 0x9f, 0xec, 0xff, 0x77, 0x8e, 0xdf, 0xff, 0x6f, 0x7f, 0xc7, 0xff, 0x6b, 0x74, 0xb8, 0xff, 0x63, 0x75, 0xb8, 0xff, 0x6a, 0x80, 0xc7, 0xff, 0x71, 0x84, 0xd5, 0xff, 0x67, 0x7c, 0xce, 0xff, 0x4f, 0x66, 0xb3, 0xff, 0x48, 0x64, 0xad, 0xff, 0x49, 0x66, 0xac, 0xff, 0x48, 0x64, 0xa9, 0xff, 0x4f, 0x67, 0xaa, 0xff, 0x4b, 0x60, 0x9e, 0xff, 0x25, 0x35, 0x70, 0xff, 0x27, 0x32, 0x6c, 0xff, 0x37, 0x4a, 0x80, 0xff, 0x33, 0x4d, 0x80, 0xff, 0x4a, 0x64, 0x9c, 0xff, 0x6f, 0x8b, 0xc1, 0xff, 0x85, 0x9e, 0xd2, 0xff, 0x91, 0xa9, 0xdb, 0xff, 0x93, 0xa6, 0xd5, 0xff, 0x91, 0xa2, 0xd2, 0xff, 0x85, 0x9a, 0xcb, 0xff, 0x7c, 0x93, 0xc5, 0xff, 0x6c, 0x83, 0xbb, 0xff, 0x5d, 0x73, 0xb0, 0xff, 0x51, 0x67, 0x9e, 0xff, 0x49, 0x60, 0x91, 0xff, 0x3f, 0x56, 0x89, 0xff, 0x3e, 0x55, 0x88, 0xff, 0x3e, 0x55, 0x88, 0xff, 0x34, 0x4b, 0x7e, 0xff, 0x38, 0x4d, 0x82, 0xff, 0x39, 0x4e, 0x84, 0xff, 0x3b, 0x53, 0x86, 0xff, 0x42, 0x5c, 0x8e, 0xff, 0x42, 0x5d, 0x92, 0xff, 0x41, 0x5a, 0x92, 0xff, 0x36, 0x56, 0x8e, 0xff, 0x34, 0x57, 0x8e, 0xff, 0x3f, 0x5f, 0x95, 0xff, 0x3c, 0x55, 0x87, 0xff, 0x2f, 0x43, 0x6c, 0xff, 0x22, 0x2f, 0x50, 0xff, 0x27, 0x32, 0x55, 0xff, 0x16, 0x22, 0x3c, 0xff, 0x0c, 0x17, 0x27, 0xff, 0x20, 0x2f, 0x54, 0xff, 0x2c, 0x41, 0x7f, 0xff, 0x27, 0x41, 0x83, 0xff, 0x30, 0x47, 0x7f, 0xff, 0x32, 0x47, 0x7b, 0xff, 0x31, 0x44, 0x7e, 0xff, 0x28, 0x3d, 0x78, 0xff, 0x39, 0x4e, 0x88, 0xff, 0x33, 0x4a, 0x84, 0xff, 0x0c, 0x1b, 0x4c, 0xff, 0x1b, 0x29, 0x53, 0xff, 0x27, 0x3b, 0x6c, 0xff, 0x24, 0x3c, 0x71, 0xff, 0x24, 0x3c, 0x75, 0xff, 0x14, 0x2b, 0x61, 0xff, 0x0f, 0x23, 0x68, 0xff, 0x1c, 0x2a, 0x6f, 0xff, 0x1e, 0x29, 0x80, 0xff, 0x4d, 0x58, 0xb6, 0xff, 0xaf, 0xa8, 0xa2, 0xff, 0xd7, 0xbf, 0x91, 0xff, 0xcd, 0xb6, 0xa0, 0xff, 0xc8, 0xb5, 0x99, 0xff, 0xc5, 0xb4, 0x9a, 0xff, 0xc3, 0xb5, 0x9d, 0xff, 0xc0, 0xb3, 0x9e, 0xff, 0xb9, 0xad, 0x98, 0xff, 0xb2, 0xa9, 0x96, 0xff, 0xaf, 0xa8, 0x99, 0xff, 0xab, 0xa8, 0x9a, 0xff, 0xa7, 0xa9, 0x9d, 0xff, 0xa0, 0xa5, 0x9a, 0xff, 0x9a, 0x9d, 0x95, 0xff, 0x91, 0x95, 0x8c, 0xff, 0x8e, 0x94, 0x86, 0xff, 0x8f, 0x94, 0x86, 0xff, 0x8e, 0x93, 0x85, 0xf3,
    0xc1, 0xb5, 0x92, 0xff, 0xc3, 0xb8, 0x99, 0xff, 0xc9, 0xc1, 0xa4, 0xff, 0xcc, 0xc6, 0xab, 0xff, 0xcf, 0xc6, 0xad, 0xff, 0xd0, 0xc7, 0xac, 0xff, 0xcc, 0xc4, 0xa6, 0xff, 0xc5, 0xbd, 0x99, 0xff, 0xc2, 0xb7, 0x90, 0xff, 0xc5, 0xb6, 0x8c, 0xff, 0xc5, 0xb8, 0x8f, 0xff, 0xc7, 0xb9, 0x95, 0xff, 0xc8, 0xbc, 0x99, 0xff, 0xca, 0xbf, 0x9f, 0xff, 0xcd, 0xc3, 0xa4, 0xff, 0xcd, 0xc3, 0xa7, 0xff, 0xcc, 0xc1, 0xa5, 0xff, 0xc7, 0xbe, 0x9c, 0xff, 0xbc, 0xb3, 0x8d, 0xff, 0xae, 0xa4, 0x81, 0xff, 0xa0, 0x94, 0x76, 0xff, 0x93, 0x84, 0x6d, 0xff, 0x89, 0x79, 0x6d, 0xff, 0x84, 0x78, 0x75, 0xff, 0x88, 0x84, 0x88, 0xff, 0x93, 0x98, 0xa0, 0xff, 0xbc, 0xc6, 0xcb, 0xff, 0x5f, 0x6a, 0x6b, 0xff, 0x00, 0x00, 0x00, 0xff, 0x31, 0x34, 0x1b, 0xff, 0x28, 0x24, 0x16, 0xff, 0x07, 0x07, 0x00, 0xff, 0x25, 0x38, 0x32, 0xff, 0x29, 0x52, 0x4e, 0xff, 0x32, 0x61, 0x46, 0xff, 0x98, 0xb6, 0xad, 0xff, 0xa9, 0xbc, 0xc7, 0xff, 0xa8, 0xbd, 0xd4, 0xff, 0x9e, 0xb0, 0xd6, 0xff, 0xcf, 0xe2, 0xe0, 0xff, 0xe0, 0xf8, 0xfb, 0xff, 0x8c, 0xa5, 0xd4, 0xff, 0x79, 0x8b, 0xcd, 0xff, 0x81, 0x91, 0xd8, 0xff, 0x82, 0x93, 0xd2, 0xff, 0x73, 0x85, 0xbe, 0xff, 0x80, 0x88, 0xd5, 0xff, 0x75, 0x79, 0xc2, 0xff, 0x6c, 0x77, 0xa1, 0xff, 0x7a, 0x88, 0xb0, 0xff, 0x81, 0x94, 0xd4, 0xff, 0x76, 0x81, 0xe0, 0xff, 0x56, 0x6a, 0xbf, 0xff, 0x4f, 0x6c, 0xb0, 0xff, 0x51, 0x69, 0xad, 0xff, 0x49, 0x5c, 0x9c, 0xff, 0x24, 0x31, 0x6d, 0xff, 0x21, 0x2a, 0x64, 0xff, 0x3c, 0x4d, 0x82, 0xff, 0x39, 0x4d, 0x80, 0xff, 0x2b, 0x42, 0x79, 0xff, 0x40, 0x57, 0x90, 0xff, 0x73, 0x8c, 0xc4, 0xff, 0x85, 0x9e, 0xd4, 0xff, 0x92, 0xa6, 0xd6, 0xff, 0xa2, 0xb3, 0xdf, 0xff, 0xa7, 0xbb, 0xe7, 0xff, 0x97, 0xad, 0xda, 0xff, 0x77, 0x8c, 0xc1, 0xff, 0x58, 0x6c, 0xa9, 0xff, 0x4d, 0x63, 0x98, 0xff, 0x48, 0x60, 0x8f, 0xff, 0x41, 0x58, 0x88, 0xff, 0x44, 0x5c, 0x8c, 0xff, 0x3d, 0x55, 0x85, 0xff, 0x32, 0x4b, 0x7a, 0xff, 0x38, 0x4d, 0x7f, 0xff, 0x34, 0x46, 0x79, 0xff, 0x28, 0x3d, 0x6e, 0xff, 0x39, 0x4f, 0x80, 0xff, 0x3d, 0x56, 0x87, 0xff, 0x3d, 0x54, 0x89, 0xff, 0x38, 0x55, 0x8b, 0xff, 0x39, 0x5a, 0x8f, 0xff, 0x38, 0x56, 0x89, 0xff, 0x33, 0x4d, 0x7b, 0xff, 0x34, 0x47, 0x6f, 0xff, 0x24, 0x2e, 0x51, 0xff, 0x26, 0x34, 0x53, 0xff, 0x20, 0x33, 0x47, 0xff, 0x0d, 0x1c, 0x2c, 0xff, 0x28, 0x3d, 0x64, 0xff, 0x2f, 0x4c, 0x88, 0xff, 0x23, 0x45, 0x7e, 0xff, 0x2a, 0x45, 0x77, 0xff, 0x30, 0x43, 0x7a, 0xff, 0x2f, 0x42, 0x7d, 0xff, 0x32, 0x45, 0x7f, 0xff, 0x43, 0x58, 0x90, 0xff, 0x1b, 0x30, 0x65, 0xff, 0x00, 0x00, 0x30, 0xff, 0x00, 0x0e, 0x3b, 0xff, 0x19, 0x2e, 0x5e, 0xff, 0x2f, 0x49, 0x7b, 0xff, 0x3a, 0x56, 0x8b, 0xff, 0x29, 0x46, 0x7b, 0xff, 0x0a, 0x22, 0x67, 0xff, 0x15, 0x21, 0x5e, 0xff, 0x17, 0x1f, 0x76, 0xff, 0x30, 0x3b, 0xb1, 0xff, 0xa7, 0xa7, 0xb4, 0xff, 0xd6, 0xc1, 0x95, 0xff, 0xcc, 0xb2, 0x9d, 0xff, 0xc7, 0xb3, 0x97, 0xff, 0xc3, 0xb2, 0x99, 0xff, 0xc2, 0xb2, 0x9c, 0xff, 0xbd, 0xb2, 0x9c, 0xff, 0xb6, 0xac, 0x9a, 0xff, 0xb1, 0xaa, 0x99, 0xff, 0xaf, 0xaa, 0x9c, 0xff, 0xaa, 0xaa, 0x9d, 0xff, 0xa5, 0xa9, 0x9e, 0xff, 0x9d, 0xa5, 0x9b, 0xff, 0x97, 0x9d, 0x97, 0xff, 0x8e, 0x93, 0x8b, 0xff, 0x88, 0x8f, 0x82, 0xff, 0x89, 0x90, 0x84, 0xff, 0x8a, 0x8e, 0x81, 0xff,
    0xc1, 0xb2, 0x8d, 0xff, 0xc2, 0xb4, 0x94, 0xff, 0xc6, 0xbb, 0x9e, 0xff, 0xc9, 0xc2, 0xa5, 0xff, 0xcd, 0xc3, 0xa8, 0xff, 0xcc, 0xc3, 0xa8, 0xff, 0xc7, 0xc0, 0xa0, 0xff, 0xc1, 0xb9, 0x95, 0xff, 0xc1, 0xb5, 0x8d, 0xff, 0xc6, 0xb5, 0x8b, 0xff, 0xc5, 0xb7, 0x8c, 0xff, 0xc8, 0xba, 0x92, 0xff, 0xc9, 0xbb, 0x95, 0xff, 0xc9, 0xbd, 0x9b, 0xff, 0xcb, 0xc0, 0xa0, 0xff, 0xcb, 0xc0, 0xa2, 0xff, 0xcb, 0xbf, 0xa0, 0xff, 0xc7, 0xbb, 0x98, 0xff, 0xbc, 0xb2, 0x8a, 0xff, 0xb0, 0xa5, 0x80, 0xff, 0xa2, 0x95, 0x77, 0xff, 0x95, 0x84, 0x6d, 0xff, 0x89, 0x7a, 0x6d, 0xff, 0x84, 0x79, 0x75, 0xff, 0x8a, 0x85, 0x87, 0xff, 0x95, 0x98, 0xa0, 0xff, 0xa4, 0xae, 0xb5, 0xff, 0xb6, 0xc3, 0xc9, 0xff, 0x52, 0x5e, 0x4f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x43, 0x5d, 0x59, 0xff, 0x7e, 0x9d, 0x99, 0xff, 0xa0, 0xc2, 0xcc, 0xff, 0xbc, 0xd9, 0xeb, 0xff, 0xb3, 0xd3, 0xd1, 0xff, 0xa2, 0xc4, 0xce, 0xff, 0x97, 0xa7, 0xd9, 0xff, 0xda, 0xde, 0xf8, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xc3, 0xcf, 0xed, 0xff, 0x82, 0x8b, 0xc3, 0xff, 0x85, 0x93, 0xca, 0xff, 0x88, 0x9c, 0xd1, 0xff, 0x82, 0x99, 0xd4, 0xff, 0x48, 0x4f, 0x8c, 0xff, 0x0e, 0x11, 0x35, 0xff, 0x00, 0x09, 0x11, 0xff, 0x00, 0x04, 0x05, 0xff, 0x23, 0x2a, 0x3a, 0xff, 0x61, 0x6b, 0xa6, 0xff, 0x7b, 0x8f, 0xd3, 0xff, 0x70, 0x86, 0xc6, 0xff, 0x5f, 0x71, 0xaf, 0xff, 0x2f, 0x3d, 0x77, 0xff, 0x14, 0x1e, 0x56, 0xff, 0x29, 0x30, 0x66, 0xff, 0x2d, 0x37, 0x69, 0xff, 0x33, 0x40, 0x72, 0xff, 0x2b, 0x3c, 0x72, 0xff, 0x14, 0x27, 0x60, 0xff, 0x2e, 0x42, 0x7b, 0xff, 0x78, 0x8e, 0xc6, 0xff, 0x91, 0xa8, 0xda, 0xff, 0x9d, 0xb3, 0xde, 0xff, 0xa6, 0xb9, 0xe2, 0xff, 0x99, 0xac, 0xd3, 0xff, 0x85, 0x97, 0xc5, 0xff, 0x64, 0x75, 0xae, 0xff, 0x4e, 0x64, 0x97, 0xff, 0x4b, 0x62, 0x8f, 0xff, 0x46, 0x5d, 0x8b, 0xff, 0x41, 0x58, 0x86, 0xff, 0x3b, 0x51, 0x80, 0xff, 0x39, 0x51, 0x7e, 0xff, 0x37, 0x49, 0x7a, 0xff, 0x2e, 0x3c, 0x6d, 0xff, 0x24, 0x37, 0x65, 0xff, 0x2a, 0x3e, 0x6c, 0xff, 0x30, 0x45, 0x74, 0xff, 0x37, 0x4d, 0x80, 0xff, 0x37, 0x50, 0x82, 0xff, 0x3a, 0x56, 0x88, 0xff, 0x38, 0x53, 0x84, 0xff, 0x2c, 0x44, 0x71, 0xff, 0x2f, 0x43, 0x6a, 0xff, 0x28, 0x37, 0x58, 0xff, 0x23, 0x31, 0x4e, 0xff, 0x1d, 0x2e, 0x40, 0xff, 0x0d, 0x1e, 0x2f, 0xff, 0x2b, 0x43, 0x69, 0xff, 0x35, 0x53, 0x8d, 0xff, 0x2b, 0x4e, 0x7c, 0xff, 0x2e, 0x4a, 0x74, 0xff, 0x29, 0x3d, 0x73, 0xff, 0x22, 0x38, 0x6e, 0xff, 0x31, 0x46, 0x7d, 0xff, 0x33, 0x49, 0x7d, 0xff, 0x2a, 0x41, 0x70, 0xff, 0x1c, 0x31, 0x63, 0xff, 0x0f, 0x25, 0x57, 0xff, 0x25, 0x3d, 0x6d, 0xff, 0x43, 0x5d, 0x8f, 0xff, 0x53, 0x70, 0xa4, 0xff, 0x37, 0x55, 0x8e, 0xff, 0x13, 0x2f, 0x6e, 0xff, 0x13, 0x1c, 0x5b, 0xff, 0x19, 0x1f, 0x78, 0xff, 0x1a, 0x2f, 0x9a, 0xff, 0x8b, 0x93, 0xb2, 0xff, 0xd7, 0xbf, 0xa4, 0xff, 0xcc, 0xb1, 0x96, 0xff, 0xc4, 0xb1, 0x96, 0xff, 0xc2, 0xaf, 0x98, 0xff, 0xbf, 0xaf, 0x9c, 0xff, 0xb9, 0xae, 0x9c, 0xff, 0xb2, 0xaa, 0x9a, 0xff, 0xb0, 0xa9, 0x9b, 0xff, 0xaf, 0xab, 0x9e, 0xff, 0xaa, 0xab, 0x9f, 0xff, 0xa3, 0xaa, 0x9e, 0xff, 0x9c, 0xa5, 0x9d, 0xff, 0x97, 0x9d, 0x99, 0xff, 0x8f, 0x95, 0x8e, 0xff, 0x89, 0x8e, 0x85, 0xff, 0x87, 0x8d, 0x83, 0xff, 0x85, 0x8b, 0x7e, 0xff,
    0xbe, 0xad, 0x89, 0xf3, 0xc3, 0xb0, 0x8e, 0xff, 0xc6, 0xb7, 0x9b, 0xff, 0xc7, 0xbd, 0xa2, 0xff, 0xc9, 0xbe, 0xa3, 0xff, 0xc8, 0xbe, 0xa2, 0xff, 0xc1, 0xb9, 0x99, 0xff, 0xbb, 0xb3, 0x8e, 0xff, 0xbe, 0xb4, 0x8a, 0xff, 0xc5, 0xb4, 0x88, 0xff, 0xc4, 0xb4, 0x89, 0xff, 0xc5, 0xb7, 0x8d, 0xff, 0xc5, 0xb8, 0x8f, 0xff, 0xc5, 0xb9, 0x94, 0xff, 0xc8, 0xbc, 0x9a, 0xff, 0xc6, 0xbc, 0x9c, 0xff, 0xc8, 0xbb, 0x99, 0xff, 0xc6, 0xb8, 0x92, 0xff, 0xba, 0xb0, 0x87, 0xff, 0xb0, 0xa5, 0x7e, 0xff, 0xa3, 0x95, 0x77, 0xff, 0x95, 0x85, 0x6d, 0xff, 0x89, 0x7a, 0x6d, 0xff, 0x84, 0x79, 0x75, 0xff, 0x8c, 0x85, 0x87, 0xff, 0x95, 0x96, 0x9e, 0xff, 0x99, 0xa2, 0xab, 0xff, 0xab, 0xb9, 0xc2, 0xff, 0xd6, 0xe2, 0xe1, 0xff, 0x88, 0x97, 0x95, 0xff, 0x5e, 0x70, 0x72, 0xff, 0x76, 0x96, 0x9b, 0xff, 0xb3, 0xd2, 0xdc, 0xff, 0xd1, 0xe6, 0xf4, 0xff, 0xc8, 0xd4, 0xeb, 0xff, 0xb8, 0xc1, 0xde, 0xff, 0xae, 0xc4, 0xd4, 0xff, 0x90, 0xaf, 0xd0, 0xff, 0x72, 0x86, 0xc8, 0xff, 0xc1, 0xd6, 0xe7, 0xff, 0xb6, 0xc7, 0xec, 0xff, 0xa3, 0xae, 0xf0, 0xff, 0xa2, 0xaf, 0xec, 0xff, 0x83, 0x93, 0xcb, 0xff, 0x93, 0xa5, 0xdc, 0xff, 0x6d, 0x7d, 0xbf, 0xff, 0x00, 0x00, 0x28, 0xff, 0x14, 0x1a, 0x4c, 0xff, 0x41, 0x42, 0x81, 0xff, 0x2b, 0x28, 0x66, 0xff, 0x09, 0x0a, 0x3c, 0xff, 0x10, 0x1f, 0x4f, 0xff, 0x4a, 0x5d, 0x90, 0xff, 0x42, 0x50, 0x85, 0xff, 0x16, 0x22, 0x56, 0xff, 0x00, 0x09, 0x3c, 0xff, 0x22, 0x29, 0x5b, 0xff, 0x29, 0x30, 0x62, 0xff, 0x1e, 0x24, 0x52, 0xff, 0x2a, 0x32, 0x61, 0xff, 0x45, 0x51, 0x86, 0xff, 0x46, 0x56, 0x8f, 0xff, 0x0a, 0x1c, 0x56, 0xff, 0x2a, 0x3b, 0x75, 0xff, 0x7b, 0x96, 0xc9, 0xff, 0x8e, 0xab, 0xd7, 0xff, 0x97, 0xac, 0xd1, 0xff, 0x8e, 0x9f, 0xc1, 0xff, 0x82, 0x90, 0xb9, 0xff, 0x70, 0x7d, 0xb0, 0xff, 0x5f, 0x71, 0xa1, 0xff, 0x4e, 0x61, 0x8e, 0xff, 0x42, 0x55, 0x83, 0xff, 0x3f, 0x52, 0x80, 0xff, 0x35, 0x49, 0x77, 0xff, 0x32, 0x47, 0x75, 0xff, 0x33, 0x43, 0x73, 0xff, 0x2d, 0x3a, 0x6a, 0xff, 0x2f, 0x3e, 0x6c, 0xff, 0x2b, 0x3e, 0x6a, 0xff, 0x2d, 0x41, 0x6f, 0xff, 0x36, 0x4a, 0x7d, 0xff, 0x35, 0x49, 0x7b, 0xff, 0x36, 0x4b, 0x7b, 0xff, 0x34, 0x4c, 0x7b, 0xff, 0x2f, 0x46, 0x73, 0xff, 0x2a, 0x3f, 0x65, 0xff, 0x18, 0x2e, 0x4c, 0xff, 0x25, 0x33, 0x54, 0xff, 0x25, 0x2d, 0x49, 0xff, 0x11, 0x1d, 0x36, 0xff, 0x32, 0x45, 0x74, 0xff, 0x38, 0x54, 0x92, 0xff, 0x38, 0x55, 0x81, 0xff, 0x42, 0x5c, 0x84, 0xff, 0x3e, 0x55, 0x88, 0xff, 0x18, 0x2e, 0x61, 0xff, 0x28, 0x3e, 0x70, 0xff, 0x24, 0x38, 0x68, 0xff, 0x1d, 0x2f, 0x5a, 0xff, 0x4b, 0x66, 0x99, 0xff, 0x4d, 0x6b, 0xa4, 0xff, 0x4e, 0x69, 0x9b, 0xff, 0x52, 0x6e, 0x9f, 0xff, 0x5e, 0x79, 0xaf, 0xff, 0x43, 0x5e, 0x9c, 0xff, 0x1d, 0x3a, 0x74, 0xff, 0x1a, 0x21, 0x65, 0xff, 0x1a, 0x1d, 0x7b, 0xff, 0x13, 0x35, 0x85, 0xff, 0x66, 0x75, 0x9f, 0xff, 0xd0, 0xb4, 0xab, 0xff, 0xd0, 0xb4, 0x92, 0xff, 0xc5, 0xb1, 0x96, 0xff, 0xc1, 0xaf, 0x98, 0xff, 0xbd, 0xaf, 0x9b, 0xff, 0xb7, 0xac, 0x9d, 0xff, 0xb1, 0xa9, 0x9c, 0xff, 0xae, 0xa9, 0x9c, 0xff, 0xad, 0xab, 0x9f, 0xff, 0xaa, 0xab, 0xa1, 0xff, 0xa2, 0xaa, 0xa0, 0xff, 0x9d, 0xa4, 0x9e, 0xff, 0x98, 0x9f, 0x9c, 0xff, 0x90, 0x97, 0x93, 0xff, 0x8a, 0x90, 0x89, 0xff, 0x87, 0x8b, 0x83, 0xff, 0x82, 0x89, 0x7b, 0xf3,
    0xb9, 0xa6, 0x82, 0xf0, 0xbf, 0xad, 0x8a, 0xff, 0xc2, 0xb4, 0x95, 0xff, 0xc3, 0xba, 0x9b, 0xff, 0xc3, 0xb9, 0x99, 0xff, 0xc1, 0xb7, 0x97, 0xff, 0xbe, 0xb2, 0x90, 0xff, 0xba, 0xae, 0x88, 0xff, 0xbc, 0xae, 0x87, 0xff, 0xbf, 0xaf, 0x87, 0xff, 0xc1, 0xb0, 0x87, 0xff, 0xc2, 0xb2, 0x8a, 0xff, 0xc2, 0xb3, 0x8b, 0xff, 0xc3, 0xb4, 0x8e, 0xff, 0xc6, 0xb8, 0x93, 0xff, 0xc6, 0xb9, 0x94, 0xff, 0xc2, 0xb7, 0x95, 0xff, 0xbe, 0xb4, 0x90, 0xff, 0xb8, 0xad, 0x85, 0xff, 0xb0, 0xa4, 0x7f, 0xff, 0xa3, 0x95, 0x76, 0xff, 0x92, 0x84, 0x6a, 0xff, 0x88, 0x79, 0x6c, 0xff, 0x86, 0x7a, 0x74, 0xff, 0x8d, 0x87, 0x86, 0xff, 0x95, 0x96, 0x9a, 0xff, 0x9f, 0xa5, 0xac, 0xff, 0xa1, 0xa9, 0xb0, 0xff, 0xa8, 0xb4, 0xb6, 0xff, 0xcd, 0xde, 0xdf, 0xff, 0xc0, 0xd5, 0xde, 0xff, 0xcf, 0xe2, 0xf3, 0xff, 0xd8, 0xe4, 0xfb, 0xff, 0xb9, 0xc1, 0xe0, 0xff, 0xaf, 0xba, 0xdf, 0xff, 0x9b, 0xa8, 0xd5, 0xff, 0x8f, 0x9d, 0xd2, 0xff, 0x88, 0x9a, 0xd7, 0xff, 0x79, 0x8d, 0xcc, 0xff, 0x99, 0xb4, 0xd4, 0xff, 0xad, 0xc3, 0xf5, 0xff, 0x56, 0x62, 0xc0, 0xff, 0x82, 0x8b, 0xde, 0xff, 0xa1, 0xaf, 0xec, 0xff, 0x92, 0xa5, 0xd8, 0xff, 0x7a, 0x89, 0xce, 0xff, 0x49, 0x55, 0xa4, 0xff, 0x56, 0x60, 0xaf, 0xff, 0x65, 0x66, 0xb3, 0xff, 0x51, 0x50, 0x96, 0xff, 0x2f, 0x34, 0x6e, 0xff, 0x15, 0x1e, 0x4e, 0xff, 0x0a, 0x14, 0x3f, 0xff, 0x04, 0x0b, 0x36, 0xff, 0x02, 0x07, 0x34, 0xff, 0x1a, 0x1f, 0x4d, 0xff, 0x2a, 0x2f, 0x5f, 0xff, 0x24, 0x28, 0x5b, 0xff, 0x23, 0x28, 0x54, 0xff, 0x29, 0x33, 0x5a, 0xff, 0x47, 0x56, 0x81, 0xff, 0x8e, 0xa0, 0xcf, 0xff, 0x62, 0x76, 0xa6, 0xff, 0x00, 0x05, 0x38, 0xff, 0x14, 0x22, 0x53, 0xff, 0x63, 0x7a, 0xaa, 0xff, 0x82, 0x9a, 0xc7, 0xff, 0x86, 0x9e, 0xc9, 0xff, 0x79, 0x8c, 0xb9, 0xff, 0x6e, 0x7c, 0xab, 0xff, 0x63, 0x75, 0xa4, 0xff, 0x57, 0x6c, 0x9b, 0xff, 0x3f, 0x52, 0x80, 0xff, 0x3d, 0x4e, 0x7c, 0xff, 0x38, 0x48, 0x73, 0xff, 0x2b, 0x39, 0x65, 0xff, 0x32, 0x41, 0x6d, 0xff, 0x2c, 0x3b, 0x68, 0xff, 0x2b, 0x3c, 0x68, 0xff, 0x32, 0x43, 0x72, 0xff, 0x2c, 0x3f, 0x6f, 0xff, 0x34, 0x49, 0x78, 0xff, 0x34, 0x4b, 0x7d, 0xff, 0x33, 0x4b, 0x7d, 0xff, 0x31, 0x48, 0x78, 0xff, 0x33, 0x48, 0x74, 0xff, 0x2b, 0x3d, 0x64, 0xff, 0x24, 0x35, 0x5a, 0xff, 0x22, 0x32, 0x55, 0xff, 0x29, 0x35, 0x52, 0xff, 0x15, 0x1e, 0x3e, 0xff, 0x33, 0x44, 0x73, 0xff, 0x43, 0x5e, 0x96, 0xff, 0x3f, 0x5a, 0x89, 0xff, 0x4f, 0x67, 0x93, 0xff, 0x60, 0x79, 0xa9, 0xff, 0x40, 0x59, 0x8a, 0xff, 0x2c, 0x42, 0x71, 0xff, 0x2a, 0x3b, 0x65, 0xff, 0x00, 0x00, 0x24, 0xff, 0x16, 0x23, 0x48, 0xff, 0x47, 0x5b, 0x89, 0xff, 0x52, 0x6c, 0xa1, 0xff, 0x46, 0x60, 0x97, 0xff, 0x57, 0x6e, 0xa3, 0xff, 0x50, 0x69, 0xa5, 0xff, 0x25, 0x41, 0x7e, 0xff, 0x21, 0x24, 0x6a, 0xff, 0x22, 0x28, 0x72, 0xff, 0x0e, 0x2a, 0x84, 0xff, 0x4e, 0x5a, 0x93, 0xff, 0xcb, 0xb9, 0xa4, 0xff, 0xd0, 0xb5, 0x9a, 0xff, 0xc3, 0xb0, 0x98, 0xff, 0xbf, 0xaf, 0x99, 0xff, 0xbd, 0xaf, 0x9c, 0xff, 0xb9, 0xaf, 0x9f, 0xff, 0xb4, 0xad, 0x9e, 0xff, 0xb0, 0xac, 0x9f, 0xff, 0xad, 0xac, 0xa2, 0xff, 0xa9, 0xab, 0xa1, 0xff, 0xa3, 0xa9, 0xa1, 0xff, 0x9e, 0xa6, 0xa0, 0xff, 0x9a, 0xa0, 0x9c, 0xff, 0x94, 0x9b, 0x95, 0xff, 0x8c, 0x94, 0x8a, 0xff, 0x88, 0x8d, 0x84, 0xff, 0x82, 0x89, 0x7d, 0xf0,
    0xb2, 0x9e, 0x79, 0xe7, 0xb7, 0xa5, 0x81, 0xff, 0xbc, 0xad, 0x8b, 0xff, 0xbd, 0xb2, 0x91, 0xff, 0xbd, 0xb1, 0x8f, 0xff, 0xbc, 0xb0, 0x8d, 0xff, 0xbc, 0xae, 0x8a, 0xff, 0xba, 0xaa, 0x84, 0xff, 0xba, 0xa8, 0x83, 0xff, 0xbc, 0xaa, 0x83, 0xff, 0xbd, 0xab, 0x85, 0xff, 0xc1, 0xaf, 0x88, 0xff, 0xc2, 0xb0, 0x8a, 0xff, 0xc2, 0xb1, 0x8d, 0xff, 0xc7, 0xb6, 0x90, 0xff, 0xc7, 0xb6, 0x90, 0xff, 0xbd, 0xb3, 0x90, 0xff, 0xb8, 0xb0, 0x8b, 0xff, 0xb5, 0xa8, 0x81, 0xff, 0xaf, 0xa0, 0x7c, 0xff, 0xa2, 0x93, 0x74, 0xff, 0x91, 0x83, 0x69, 0xff, 0x87, 0x79, 0x6c, 0xff, 0x86, 0x7a, 0x75, 0xff, 0x8d, 0x89, 0x87, 0xff, 0x95, 0x96, 0x9a, 0xff, 0xa0, 0xa4, 0xa9, 0xff, 0xa8, 0xad, 0xb2, 0xff, 0xa5, 0xb0, 0xb1, 0xff, 0x99, 0xa4, 0xa6, 0xff, 0x9b, 0xa1, 0xaa, 0xff, 0xdd, 0xe0, 0xf1, 0xff, 0xe0, 0xe2, 0xf6, 0xff, 0xd0, 0xd4, 0xe9, 0xff, 0xd4, 0xd9, 0xf0, 0xff, 0xbd, 0xc7, 0xe7, 0xff, 0x98, 0xab, 0xdf, 0xff, 0x79, 0x92, 0xd3, 0xff, 0x6a, 0x89, 0xc6, 0xff, 0x60, 0x7f, 0xc2, 0xff, 0xaf, 0xca, 0xee, 0xff, 0xb0, 0xbf, 0xed, 0xff, 0x6d, 0x75, 0xbd, 0xff, 0xa4, 0xb2, 0xe3, 0xff, 0xa8, 0xbc, 0xe0, 0xff, 0x94, 0xa3, 0xdc, 0xff, 0x82, 0x90, 0xdc, 0xff, 0x60, 0x6f, 0xb2, 0xff, 0x3b, 0x45, 0x7a, 0xff, 0x1a, 0x21, 0x47, 0xff, 0x0e, 0x16, 0x35, 0xff, 0x06, 0x0f, 0x2c, 0xff, 0x08, 0x0e, 0x33, 0xff, 0x11, 0x15, 0x3e, 0xff, 0x20, 0x26, 0x4d, 0xff, 0x24, 0x2a, 0x53, 0xff, 0x29, 0x2e, 0x5a, 0xff, 0x2f, 0x33, 0x60, 0xff, 0x2f, 0x34, 0x60, 0xff, 0x1b, 0x23, 0x4d, 0xff, 0x1a, 0x27, 0x52, 0xff, 0x62, 0x73, 0x9f, 0xff, 0xac, 0xbc, 0xeb, 0xff, 0x67, 0x72, 0xa4, 0xff, 0x0f, 0x17, 0x47, 0xff, 0x25, 0x39, 0x67, 0xff, 0x50, 0x69, 0x98, 0xff, 0x63, 0x7d, 0xac, 0xff, 0x69, 0x82, 0xb1, 0xff, 0x70, 0x85, 0xb3, 0xff, 0x64, 0x79, 0xa8, 0xff, 0x53, 0x68, 0x98, 0xff, 0x46, 0x5a, 0x89, 0xff, 0x36, 0x48, 0x75, 0xff, 0x36, 0x45, 0x70, 0xff, 0x32, 0x3e, 0x69, 0xff, 0x2a, 0x39, 0x63, 0xff, 0x2e, 0x3f, 0x69, 0xff, 0x28, 0x3a, 0x64, 0xff, 0x2a, 0x3c, 0x6a, 0xff, 0x2b, 0x3f, 0x6d, 0xff, 0x31, 0x46, 0x74, 0xff, 0x2f, 0x48, 0x7a, 0xff, 0x33, 0x4e, 0x82, 0xff, 0x34, 0x4a, 0x7b, 0xff, 0x32, 0x46, 0x72, 0xff, 0x2e, 0x3d, 0x67, 0xff, 0x2f, 0x3b, 0x65, 0xff, 0x29, 0x3b, 0x5d, 0xff, 0x20, 0x2f, 0x4a, 0xff, 0x16, 0x20, 0x41, 0xff, 0x2a, 0x3c, 0x68, 0xff, 0x43, 0x5e, 0x93, 0xff, 0x40, 0x5b, 0x8d, 0xff, 0x48, 0x5e, 0x8d, 0xff, 0x54, 0x6e, 0x9f, 0xff, 0x5f, 0x7d, 0xb1, 0xff, 0x3c, 0x57, 0x8b, 0xff, 0x20, 0x32, 0x5c, 0xff, 0x00, 0x02, 0x20, 0xff, 0x00, 0x01, 0x1c, 0xff, 0x01, 0x0c, 0x33, 0xff, 0x29, 0x40, 0x74, 0xff, 0x3d, 0x54, 0x8b, 0xff, 0x4a, 0x5f, 0x92, 0xff, 0x53, 0x6e, 0xa7, 0xff, 0x32, 0x4d, 0x8e, 0xff, 0x23, 0x25, 0x6d, 0xff, 0x1d, 0x26, 0x67, 0xff, 0x14, 0x2b, 0x8c, 0xff, 0x62, 0x67, 0xa1, 0xff, 0xcd, 0xc0, 0xa3, 0xff, 0xcf, 0xb7, 0xa1, 0xff, 0xc6, 0xb3, 0x9c, 0xff, 0xc2, 0xb2, 0x9d, 0xff, 0xc0, 0xb2, 0xa1, 0xff, 0xbd, 0xb4, 0xa3, 0xff, 0xb9, 0xb3, 0xa3, 0xff, 0xb5, 0xb2, 0xa5, 0xff, 0xb1, 0xb0, 0xa7, 0xff, 0xaa, 0xae, 0xa2, 0xff, 0xa4, 0xab, 0xa3, 0xff, 0xa0, 0xa9, 0xa2, 0xff, 0x9d, 0xa3, 0x9e, 0xff, 0x99, 0x9f, 0x99, 0xff, 0x90, 0x97, 0x8e, 0xff, 0x88, 0x90, 0x85, 0xff, 0x81, 0x88, 0x7c, 0xe6,
    0xa7, 0x93, 0x71, 0xd3, 0xaf, 0x9a, 0x78, 0xff, 0xb4, 0xa2, 0x82, 0xff, 0xb6, 0xa8, 0x87, 0xff, 0xb7, 0xa9, 0x89, 0xff, 0xb9, 0xaa, 0x89, 0xff, 0xb8, 0xa8, 0x85, 0xff, 0xb5, 0xa3, 0x7f, 0xff, 0xb4, 0xa2, 0x7c, 0xff, 0xb6, 0xa3, 0x7c, 0xff, 0xb8, 0xa4, 0x7e, 0xff, 0xbc, 0xa9, 0x83, 0xff, 0xbf, 0xab, 0x86, 0xff, 0xc0, 0xad, 0x88, 0xff, 0xc3, 0xb1, 0x8c, 0xff, 0xc3, 0xb1, 0x8c, 0xff, 0xbd, 0xaf, 0x8d, 0xff, 0xb9, 0xab, 0x88, 0xff, 0xb3, 0xa4, 0x7d, 0xff, 0xab, 0x9a, 0x76, 0xff, 0x9f, 0x8e, 0x70, 0xff, 0x90, 0x80, 0x69, 0xff, 0x85, 0x76, 0x6d, 0xff, 0x83, 0x78, 0x75, 0xff, 0x8b, 0x86, 0x86, 0xff, 0x93, 0x94, 0x98, 0xff, 0x9e, 0xa2, 0xa7, 0xff, 0xa7, 0xab, 0xb0, 0xff, 0xa9, 0xb0, 0xb3, 0xff, 0x99, 0xa2, 0xa4, 0xff, 0xc0, 0xc9, 0xcc, 0xff, 0xf7, 0xfb, 0xff, 0xff, 0xf3, 0xf9, 0xfd, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf4, 0xff, 0xff, 0xad, 0xc4, 0xf9, 0xff, 0x61, 0x86, 0xd3, 0xff, 0x4b, 0x72, 0xbe, 0xff, 0x5b, 0x7f, 0xc2, 0xff, 0xb4, 0xce, 0xe8, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xa5, 0xb0, 0xd5, 0xff, 0x8b, 0x98, 0xb6, 0xff, 0xaa, 0xbb, 0xd5, 0xff, 0x9f, 0xac, 0xd8, 0xff, 0x81, 0x8e, 0xc4, 0xff, 0x5a, 0x68, 0x9a, 0xff, 0x30, 0x3b, 0x65, 0xff, 0x18, 0x1f, 0x42, 0xff, 0x12, 0x18, 0x37, 0xff, 0x12, 0x19, 0x33, 0xff, 0x16, 0x1e, 0x3e, 0xff, 0x14, 0x1d, 0x41, 0xff, 0x29, 0x31, 0x54, 0xff, 0x36, 0x3e, 0x62, 0xff, 0x3a, 0x41, 0x66, 0xff, 0x34, 0x3d, 0x5f, 0xff, 0x39, 0x3f, 0x67, 0xff, 0x24, 0x29, 0x54, 0xff, 0x21, 0x29, 0x55, 0xff, 0x27, 0x31, 0x5e, 0xff, 0x4e, 0x57, 0x85, 0xff, 0x79, 0x7f, 0xb1, 0xff, 0x55, 0x62, 0x8d, 0xff, 0x47, 0x5a, 0x7f, 0xff, 0x34, 0x4a, 0x73, 0xff, 0x3d, 0x56, 0x81, 0xff, 0x4d, 0x67, 0x94, 0xff, 0x54, 0x6f, 0x9e, 0xff, 0x55, 0x6d, 0x9c, 0xff, 0x54, 0x68, 0x97, 0xff, 0x56, 0x6a, 0x97, 0xff, 0x43, 0x55, 0x81, 0xff, 0x2e, 0x3e, 0x68, 0xff, 0x35, 0x43, 0x6b, 0xff, 0x2d, 0x3c, 0x65, 0xff, 0x29, 0x3a, 0x63, 0xff, 0x2c, 0x3c, 0x66, 0xff, 0x26, 0x39, 0x64, 0xff, 0x29, 0x3d, 0x6a, 0xff, 0x2a, 0x3d, 0x6c, 0xff, 0x30, 0x46, 0x77, 0xff, 0x38, 0x50, 0x82, 0xff, 0x38, 0x50, 0x7f, 0xff, 0x2e, 0x42, 0x6e, 0xff, 0x29, 0x39, 0x62, 0xff, 0x28, 0x37, 0x5e, 0xff, 0x2a, 0x3b, 0x5e, 0xff, 0x23, 0x30, 0x4f, 0xff, 0x18, 0x24, 0x45, 0xff, 0x27, 0x3a, 0x67, 0xff, 0x3c, 0x57, 0x8f, 0xff, 0x3e, 0x58, 0x8b, 0xff, 0x3d, 0x54, 0x82, 0xff, 0x44, 0x5d, 0x91, 0xff, 0x5b, 0x79, 0xb4, 0xff, 0x3e, 0x5d, 0x97, 0xff, 0x15, 0x29, 0x59, 0xff, 0x00, 0x00, 0x1d, 0xff, 0x0e, 0x0e, 0x2a, 0xff, 0x01, 0x08, 0x31, 0xff, 0x0e, 0x1e, 0x51, 0xff, 0x2e, 0x42, 0x75, 0xff, 0x40, 0x54, 0x85, 0xff, 0x58, 0x73, 0xaa, 0xff, 0x33, 0x4e, 0x91, 0xff, 0x28, 0x2c, 0x75, 0xff, 0x2a, 0x32, 0x7b, 0xff, 0x34, 0x49, 0xa1, 0xff, 0x7f, 0x7d, 0xa8, 0xff, 0xd2, 0xbe, 0xa9, 0xff, 0xcf, 0xb9, 0xa1, 0xff, 0xcb, 0xb7, 0xa0, 0xff, 0xc7, 0xb7, 0xa2, 0xff, 0xc4, 0xb7, 0xa5, 0xff, 0xc0, 0xb6, 0xa5, 0xff, 0xbc, 0xb5, 0xa5, 0xff, 0xb8, 0xb4, 0xa7, 0xff, 0xb4, 0xb3, 0xaa, 0xff, 0xaf, 0xb2, 0xa7, 0xff, 0xa8, 0xaf, 0xa6, 0xff, 0xa4, 0xac, 0xa5, 0xff, 0xa0, 0xa6, 0xa1, 0xff, 0x9b, 0xa1, 0x9b, 0xff, 0x92, 0x99, 0x92, 0xff, 0x8a, 0x92, 0x88, 0xff, 0x82, 0x89, 0x7e, 0xd3,
    0xa1, 0x8a, 0x6e, 0xbe, 0xa8, 0x92, 0x76, 0xff, 0xac, 0x99, 0x7e, 0xff, 0xaf, 0xa0, 0x82, 0xff, 0xb1, 0xa2, 0x85, 0xff, 0xb1, 0xa1, 0x84, 0xff, 0xaf, 0x9e, 0x80, 0xff, 0xae, 0x9b, 0x7b, 0xff, 0xae, 0x9a, 0x78, 0xff, 0xae, 0x99, 0x78, 0xff, 0xb1, 0x9d, 0x79, 0xff, 0xb5, 0xa1, 0x7d, 0xff, 0xb8, 0xa4, 0x80, 0xff, 0xbb, 0xa8, 0x85, 0xff, 0xbe, 0xaa, 0x88, 0xff, 0xbe, 0xab, 0x89, 0xff, 0xb8, 0xa8, 0x8a, 0xff, 0xb5, 0xa5, 0x85, 0xff, 0xaf, 0x9e, 0x7b, 0xff, 0xa6, 0x94, 0x74, 0xff, 0x98, 0x86, 0x6e, 0xff, 0x8c, 0x7b, 0x69, 0xff, 0x82, 0x74, 0x6d, 0xff, 0x80, 0x75, 0x74, 0xff, 0x87, 0x82, 0x84, 0xff, 0x8f, 0x90, 0x96, 0xff, 0x9a, 0x9e, 0xa2, 0xff, 0xa4, 0xa7, 0xac, 0xff, 0xa7, 0xaa, 0xae, 0xff, 0x9b, 0xa1, 0xa4, 0xff, 0xd3, 0xdd, 0xdf, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xf0, 0xfb, 0xfe, 0xff, 0xee, 0xf7, 0xfb, 0xff, 0xe9, 0xf0, 0xfe, 0xff, 0xba, 0xca, 0xf5, 0xff, 0x80, 0x9d, 0xe9, 0xff, 0x56, 0x7b, 0xd4, 0xff, 0x48, 0x65, 0xb2, 0xff, 0x84, 0x97, 0xcd, 0xff, 0xdb, 0xec, 0xfb, 0xff, 0xe3, 0xf1, 0xfb, 0xff, 0xd6, 0xe1, 0xf8, 0xff, 0xb7, 0xc0, 0xdd, 0xff, 0x8e, 0x97, 0xb9, 0xff, 0x95, 0x9f, 0xca, 0xff, 0x8f, 0x9c, 0xcb, 0xff, 0x5a, 0x67, 0x97, 0xff, 0x3a, 0x44, 0x70, 0xff, 0x1f, 0x26, 0x50, 0xff, 0x24, 0x2a, 0x50, 0xff, 0x25, 0x27, 0x4c, 0xff, 0x19, 0x21, 0x42, 0xff, 0x28, 0x33, 0x54, 0xff, 0x32, 0x3c, 0x5e, 0xff, 0x3d, 0x47, 0x67, 0xff, 0x30, 0x3b, 0x59, 0xff, 0x2b, 0x36, 0x55, 0xff, 0x39, 0x41, 0x61, 0xff, 0x33, 0x39, 0x58, 0xff, 0x22, 0x29, 0x49, 0xff, 0x19, 0x1f, 0x41, 0xff, 0x23, 0x2a, 0x4c, 0xff, 0x38, 0x3f, 0x61, 0xff, 0x46, 0x52, 0x74, 0xff, 0x58, 0x67, 0x8a, 0xff, 0x42, 0x53, 0x78, 0xff, 0x40, 0x54, 0x7d, 0xff, 0x4a, 0x64, 0x8e, 0xff, 0x42, 0x61, 0x8c, 0xff, 0x3a, 0x54, 0x81, 0xff, 0x4b, 0x5f, 0x8c, 0xff, 0x55, 0x6b, 0x96, 0xff, 0x42, 0x54, 0x7e, 0xff, 0x33, 0x43, 0x6b, 0xff, 0x37, 0x46, 0x6c, 0xff, 0x24, 0x34, 0x59, 0xff, 0x2a, 0x3b, 0x61, 0xff, 0x2c, 0x3d, 0x65, 0xff, 0x28, 0x3a, 0x64, 0xff, 0x27, 0x3a, 0x66, 0xff, 0x26, 0x3b, 0x68, 0xff, 0x2e, 0x44, 0x71, 0xff, 0x36, 0x4e, 0x7c, 0xff, 0x38, 0x4e, 0x7b, 0xff, 0x35, 0x47, 0x72, 0xff, 0x26, 0x37, 0x60, 0xff, 0x21, 0x31, 0x58, 0xff, 0x1e, 0x30, 0x54, 0xff, 0x20, 0x2f, 0x4f, 0xff, 0x27, 0x31, 0x55, 0xff, 0x2e, 0x41, 0x72, 0xff, 0x3b, 0x57, 0x90, 0xff, 0x35, 0x50, 0x86, 0xff, 0x3d, 0x53, 0x85, 0xff, 0x52, 0x69, 0x9f, 0xff, 0x59, 0x74, 0xad, 0xff, 0x1a, 0x34, 0x6c, 0xff, 0x1d, 0x2d, 0x5d, 0xff, 0x0f, 0x15, 0x38, 0xff, 0x03, 0x04, 0x26, 0xff, 0x0c, 0x11, 0x3d, 0xff, 0x05, 0x12, 0x45, 0xff, 0x15, 0x23, 0x55, 0xff, 0x39, 0x48, 0x79, 0xff, 0x5a, 0x73, 0xac, 0xff, 0x2a, 0x44, 0x89, 0xff, 0x32, 0x3a, 0x7e, 0xff, 0x43, 0x4c, 0x97, 0xff, 0x40, 0x51, 0xa0, 0xff, 0x97, 0x8e, 0xa5, 0xff, 0xdb, 0xc3, 0xad, 0xff, 0xcd, 0xba, 0xa1, 0xff, 0xcd, 0xb9, 0xa2, 0xff, 0xc7, 0xb7, 0xa2, 0xff, 0xc5, 0xb7, 0xa5, 0xff, 0xc0, 0xb7, 0xa6, 0xff, 0xbe, 0xb7, 0xa7, 0xff, 0xba, 0xb6, 0xa9, 0xff, 0xb4, 0xb3, 0xaa, 0xff, 0xb0, 0xb3, 0xa8, 0xff, 0xac, 0xb1, 0xaa, 0xff, 0xa5, 0xad, 0xa7, 0xff, 0xa2, 0xa8, 0xa3, 0xff, 0x9c, 0xa3, 0x9d, 0xff, 0x95, 0x9c, 0x96, 0xff, 0x8d, 0x96, 0x8d, 0xff, 0x86, 0x8f, 0x83, 0xbe,
    0x94, 0x81, 0x6a, 0xaa, 0x9e, 0x89, 0x73, 0xff, 0xa1, 0x90, 0x7a, 0xff, 0xa4, 0x95, 0x7f, 0xff, 0xa7, 0x99, 0x83, 0xff, 0xa5, 0x96, 0x80, 0xff, 0xa3, 0x92, 0x7a, 0xff, 0xa2, 0x90, 0x76, 0xff, 0xa3, 0x90, 0x75, 0xff, 0xa5, 0x90, 0x74, 0xff, 0xa8, 0x94, 0x76, 0xff, 0xac, 0x98, 0x79, 0xff, 0xaf, 0x9b, 0x7d, 0xff, 0xb3, 0xa1, 0x83, 0xff, 0xb5, 0xa3, 0x86, 0xff, 0xb5, 0xa5, 0x88, 0xff, 0xb1, 0xa1, 0x88, 0xff, 0xad, 0x9c, 0x81, 0xff, 0xa6, 0x96, 0x79, 0xff, 0x9c, 0x8b, 0x71, 0xff, 0x8e, 0x7e, 0x6d, 0xff, 0x85, 0x76, 0x6d, 0xff, 0x7f, 0x71, 0x6e, 0xff, 0x7c, 0x73, 0x73, 0xff, 0x81, 0x7c, 0x80, 0xff, 0x8b, 0x8a, 0x90, 0xff, 0x96, 0x99, 0x9e, 0xff, 0x9f, 0xa3, 0xa8, 0xff, 0xa5, 0xa5, 0xa9, 0xff, 0xa0, 0xa4, 0xa9, 0xff, 0xd9, 0xe7, 0xec, 0xff, 0xe8, 0xfd, 0xff, 0xff, 0xd8, 0xec, 0xfc, 0xff, 0xd9, 0xe9, 0xf9, 0xff, 0xbd, 0xd3, 0xfb, 0xff, 0x87, 0xa3, 0xea, 0xff, 0x66, 0x83, 0xd9, 0xff, 0x51, 0x6c, 0xc2, 0xff, 0x5a, 0x6b, 0xaa, 0xff, 0xc9, 0xd0, 0xf2, 0xff, 0xeb, 0xf8, 0xff, 0xff, 0xda, 0xea, 0xfa, 0xff, 0xd7, 0xe2, 0xf9, 0xff, 0xd2, 0xdb, 0xfc, 0xff, 0xa7, 0xb0, 0xdb, 0xff, 0x77, 0x83, 0xb1, 0xff, 0x7d, 0x8a, 0xb8, 0xff, 0x6e, 0x79, 0xaa, 0xff, 0x47, 0x53, 0x84, 0xff, 0x40, 0x4b, 0x7c, 0xff, 0x38, 0x3d, 0x6e, 0xff, 0x27, 0x27, 0x57, 0xff, 0x21, 0x27, 0x4e, 0xff, 0x31, 0x3c, 0x5f, 0xff, 0x2c, 0x35, 0x5a, 0xff, 0x28, 0x32, 0x55, 0xff, 0x28, 0x32, 0x54, 0xff, 0x37, 0x41, 0x64, 0xff, 0x38, 0x43, 0x61, 0xff, 0x28, 0x33, 0x4c, 0xff, 0x27, 0x2f, 0x4b, 0xff, 0x11, 0x16, 0x33, 0xff, 0x17, 0x1f, 0x3b, 0xff, 0x13, 0x1f, 0x38, 0xff, 0x41, 0x4c, 0x6b, 0xff, 0x5a, 0x65, 0x8a, 0xff, 0x46, 0x51, 0x76, 0xff, 0x41, 0x4f, 0x75, 0xff, 0x44, 0x59, 0x7d, 0xff, 0x53, 0x6e, 0x93, 0xff, 0x31, 0x4a, 0x74, 0xff, 0x2d, 0x43, 0x6e, 0xff, 0x44, 0x59, 0x83, 0xff, 0x3e, 0x52, 0x79, 0xff, 0x36, 0x47, 0x6d, 0xff, 0x25, 0x35, 0x5a, 0xff, 0x20, 0x2f, 0x54, 0xff, 0x22, 0x32, 0x57, 0xff, 0x23, 0x34, 0x5a, 0xff, 0x2c, 0x3e, 0x66, 0xff, 0x22, 0x35, 0x60, 0xff, 0x28, 0x3c, 0x66, 0xff, 0x2c, 0x41, 0x6d, 0xff, 0x23, 0x39, 0x66, 0xff, 0x3b, 0x4e, 0x7b, 0xff, 0x33, 0x45, 0x6f, 0xff, 0x32, 0x43, 0x6c, 0xff, 0x2c, 0x3c, 0x64, 0xff, 0x24, 0x36, 0x5c, 0xff, 0x22, 0x31, 0x54, 0xff, 0x26, 0x32, 0x57, 0xff, 0x33, 0x47, 0x78, 0xff, 0x34, 0x50, 0x8a, 0xff, 0x2c, 0x46, 0x7d, 0xff, 0x40, 0x58, 0x8e, 0xff, 0x5d, 0x73, 0xa8, 0xff, 0x2a, 0x39, 0x69, 0xff, 0x00, 0x00, 0x27, 0xff, 0x0f, 0x18, 0x43, 0xff, 0x1c, 0x29, 0x58, 0xff, 0x00, 0x0b, 0x36, 0xff, 0x04, 0x0e, 0x3a, 0xff, 0x03, 0x12, 0x47, 0xff, 0x03, 0x11, 0x42, 0xff, 0x43, 0x4f, 0x82, 0xff, 0x59, 0x6f, 0xac, 0xff, 0x1f, 0x35, 0x81, 0xff, 0x39, 0x46, 0x82, 0xff, 0x4a, 0x58, 0xa0, 0xff, 0x48, 0x57, 0x9a, 0xff, 0xb9, 0xa9, 0xab, 0xff, 0xdc, 0xc1, 0xab, 0xff, 0xc9, 0xba, 0x9f, 0xff, 0xcc, 0xb7, 0xa1, 0xff, 0xc6, 0xb5, 0xa0, 0xff, 0xc3, 0xb6, 0xa3, 0xff, 0xbf, 0xb6, 0xa5, 0xff, 0xbe, 0xb7, 0xa7, 0xff, 0xb9, 0xb5, 0xa9, 0xff, 0xb4, 0xb3, 0xaa, 0xff, 0xaf, 0xb3, 0xa8, 0xff, 0xab, 0xb1, 0xa9, 0xff, 0xa5, 0xad, 0xa7, 0xff, 0xa2, 0xa8, 0xa3, 0xff, 0x9e, 0xa3, 0x9f, 0xff, 0x98, 0x9f, 0x9a, 0xff, 0x90, 0x98, 0x92, 0xff, 0x87, 0x90, 0x86, 0xab,
    0x87, 0x75, 0x64, 0x8b, 0x90, 0x7d, 0x6b, 0xff, 0x95, 0x85, 0x72, 0xff, 0x99, 0x8b, 0x78, 0xff, 0x9a, 0x8c, 0x7a, 0xff, 0x97, 0x88, 0x76, 0xff, 0x96, 0x86, 0x73, 0xff, 0x96, 0x84, 0x70, 0xff, 0x96, 0x84, 0x6d, 0xff, 0x97, 0x84, 0x6b, 0xff, 0x9a, 0x88, 0x6c, 0xff, 0x9f, 0x8d, 0x71, 0xff, 0xa3, 0x92, 0x76, 0xff, 0xa7, 0x96, 0x7c, 0xff, 0xaa, 0x99, 0x80, 0xff, 0xab, 0x9c, 0x83, 0xff, 0xa8, 0x99, 0x82, 0xff, 0xa0, 0x90, 0x78, 0xff, 0x98, 0x89, 0x6f, 0xff, 0x90, 0x81, 0x6c, 0xff, 0x86, 0x78, 0x6c, 0xff, 0x7f, 0x73, 0x71, 0xff, 0x7e, 0x71, 0x6f, 0xff, 0x7c, 0x73, 0x72, 0xff, 0x7d, 0x78, 0x7d, 0xff, 0x84, 0x84, 0x8a, 0xff, 0x91, 0x92, 0x98, 0xff, 0x9c, 0x9e, 0xa2, 0xff, 0x9f, 0x9e, 0xa0, 0xff, 0xa0, 0xa4, 0xab, 0xff, 0xda, 0xea, 0xf5, 0xff, 0xd7, 0xf2, 0xff, 0xff, 0xc6, 0xe1, 0xfe, 0xff, 0xc0, 0xd8, 0xfa, 0xff, 0x9b, 0xb8, 0xec, 0xff, 0x74, 0x90, 0xd3, 0xff, 0x5f, 0x75, 0xbf, 0xff, 0x45, 0x58, 0x9f, 0xff, 0x85, 0x97, 0xc8, 0xff, 0xde, 0xef, 0xff, 0xff, 0xd7, 0xe8, 0xfa, 0xff, 0xdd, 0xef, 0xfb, 0xff, 0xdf, 0xee, 0xfe, 0xff, 0xc9, 0xd6, 0xf7, 0xff, 0xa3, 0xb1, 0xdf, 0xff, 0x76, 0x8b, 0xb5, 0xff, 0x71, 0x85, 0xac, 0xff, 0x6c, 0x7e, 0xa8, 0xff, 0x5d, 0x6e, 0x9d, 0xff, 0x5a, 0x68, 0x98, 0xff, 0x44, 0x4c, 0x7b, 0xff, 0x36, 0x38, 0x65, 0xff, 0x34, 0x38, 0x62, 0xff, 0x33, 0x3a, 0x63, 0xff, 0x2a, 0x30, 0x59, 0xff, 0x2f, 0x37, 0x5e, 0xff, 0x33, 0x3b, 0x61, 0xff, 0x33, 0x3b, 0x61, 0xff, 0x32, 0x3d, 0x60, 0xff, 0x2f, 0x39, 0x5c, 0xff, 0x28, 0x2b, 0x50, 0xff, 0x25, 0x26, 0x4a, 0xff, 0x23, 0x28, 0x4b, 0xff, 0x14, 0x1d, 0x3f, 0xff, 0x2b, 0x35, 0x59, 0xff, 0x46, 0x4d, 0x72, 0xff, 0x49, 0x4f, 0x74, 0xff, 0x2a, 0x32, 0x55, 0xff, 0x1e, 0x2b, 0x4d, 0xff, 0x50, 0x63, 0x84, 0xff, 0x49, 0x61, 0x88, 0xff, 0x2f, 0x46, 0x71, 0xff, 0x36, 0x4c, 0x73, 0xff, 0x38, 0x4c, 0x71, 0xff, 0x3c, 0x4e, 0x74, 0xff, 0x26, 0x37, 0x5c, 0xff, 0x1d, 0x2d, 0x51, 0xff, 0x22, 0x31, 0x55, 0xff, 0x24, 0x35, 0x5a, 0xff, 0x25, 0x37, 0x5d, 0xff, 0x23, 0x35, 0x5e, 0xff, 0x2b, 0x3f, 0x67, 0xff, 0x34, 0x48, 0x73, 0xff, 0x2e, 0x42, 0x6e, 0xff, 0x39, 0x4b, 0x76, 0xff, 0x2b, 0x3c, 0x66, 0xff, 0x37, 0x47, 0x71, 0xff, 0x2e, 0x3e, 0x66, 0xff, 0x22, 0x35, 0x5b, 0xff, 0x24, 0x33, 0x56, 0xff, 0x23, 0x2f, 0x56, 0xff, 0x2d, 0x41, 0x74, 0xff, 0x30, 0x4d, 0x8b, 0xff, 0x2c, 0x47, 0x7d, 0xff, 0x46, 0x62, 0x9a, 0xff, 0x31, 0x45, 0x7b, 0xff, 0x00, 0x00, 0x19, 0xff, 0x00, 0x00, 0x10, 0xff, 0x00, 0x00, 0x1c, 0xff, 0x0b, 0x1f, 0x5b, 0xff, 0x1e, 0x33, 0x6b, 0xff, 0x14, 0x25, 0x51, 0xff, 0x04, 0x18, 0x4c, 0xff, 0x25, 0x33, 0x65, 0xff, 0x69, 0x72, 0xa6, 0xff, 0x46, 0x58, 0x9a, 0xff, 0x16, 0x2a, 0x7a, 0xff, 0x48, 0x59, 0x8e, 0xff, 0x4e, 0x5e, 0xa6, 0xff, 0x64, 0x72, 0xb0, 0xff, 0xcb, 0xba, 0xac, 0xff, 0xd7, 0xbc, 0xa2, 0xff, 0xc9, 0xbc, 0x9f, 0xff, 0xcb, 0xb6, 0xa1, 0xff, 0xc5, 0xb5, 0xa0, 0xff, 0xc3, 0xb6, 0xa3, 0xff, 0xbe, 0xb5, 0xa5, 0xff, 0xbc, 0xb5, 0xa5, 0xff, 0xb9, 0xb5, 0xa9, 0xff, 0xb4, 0xb3, 0xab, 0xff, 0xaf, 0xb3, 0xa8, 0xff, 0xab, 0xb1, 0xa9, 0xff, 0xa5, 0xad, 0xa7, 0xff, 0xa2, 0xa8, 0xa3, 0xff, 0x9c, 0xa3, 0x9e, 0xff, 0x96, 0x9e, 0x99, 0xff, 0x91, 0x98, 0x92, 0xff, 0x87, 0x90, 0x89, 0x8b,
    0x77, 0x6b, 0x61, 0x6b, 0x7d, 0x70, 0x67, 0xff, 0x82, 0x77, 0x6d, 0xff, 0x87, 0x7d, 0x73, 0xff, 0x89, 0x7b, 0x72, 0xff, 0x87, 0x78, 0x6f, 0xff, 0x87, 0x7a, 0x6f, 0xff, 0x87, 0x7a, 0x6c, 0xff, 0x87, 0x78, 0x68, 0xff, 0x86, 0x77, 0x66, 0xff, 0x89, 0x7a, 0x67, 0xff, 0x8e, 0x7e, 0x6a, 0xff, 0x95, 0x85, 0x71, 0xff, 0x9a, 0x8a, 0x78, 0xff, 0x9e, 0x8e, 0x7c, 0xff, 0x9f, 0x90, 0x7e, 0xff, 0x98, 0x8b, 0x7a, 0xff, 0x90, 0x83, 0x72, 0xff, 0x8a, 0x7b, 0x6d, 0xff, 0x84, 0x76, 0x6b, 0xff, 0x80, 0x72, 0x6b, 0xff, 0x7d, 0x70, 0x6d, 0xff, 0x79, 0x6f, 0x70, 0xff, 0x79, 0x72, 0x76, 0xff, 0x7f, 0x78, 0x7d, 0xff, 0x82, 0x7e, 0x86, 0xff, 0x8b, 0x88, 0x92, 0xff, 0x97, 0x95, 0x9f, 0xff, 0x97, 0x95, 0x99, 0xff, 0x9d, 0xa2, 0xa0, 0xff, 0xdc, 0xef, 0xf3, 0xff, 0xd6, 0xf0, 0xff, 0xff, 0xc1, 0xdb, 0xff, 0xff, 0xb3, 0xcd, 0xf7, 0xff, 0x90, 0xac, 0xda, 0xff, 0x73, 0x8d, 0xc4, 0xff, 0x5f, 0x76, 0xb4, 0xff, 0x47, 0x5d, 0x98, 0xff, 0xb0, 0xc3, 0xec, 0xff, 0xdd, 0xf3, 0xff, 0xff, 0xd5, 0xe5, 0xf8, 0xff, 0xd1, 0xe3, 0xf8, 0xff, 0xcc, 0xe1, 0xfd, 0xff, 0xba, 0xcc, 0xf6, 0xff, 0x8e, 0x9c, 0xca, 0xff, 0x73, 0x7e, 0xa9, 0xff, 0xa8, 0xad, 0xc9, 0xff, 0xb0, 0xb9, 0xd1, 0xff, 0x82, 0x92, 0xb7, 0xff, 0x6d, 0x80, 0xa9, 0xff, 0x61, 0x71, 0x9b, 0xff, 0x4d, 0x56, 0x82, 0xff, 0x45, 0x4e, 0x7d, 0xff, 0x43, 0x4d, 0x7d, 0xff, 0x41, 0x49, 0x78, 0xff, 0x40, 0x47, 0x74, 0xff, 0x3a, 0x40, 0x6b, 0xff, 0x2d, 0x32, 0x5c, 0xff, 0x32, 0x3b, 0x63, 0xff, 0x37, 0x40, 0x67, 0xff, 0x29, 0x2f, 0x57, 0xff, 0x23, 0x29, 0x51, 0xff, 0x2d, 0x35, 0x5c, 0xff, 0x2a, 0x33, 0x5b, 0xff, 0x22, 0x2b, 0x53, 0xff, 0x2e, 0x37, 0x60, 0xff, 0x42, 0x4b, 0x73, 0xff, 0x32, 0x3b, 0x60, 0xff, 0x10, 0x1d, 0x3e, 0xff, 0x23, 0x33, 0x4d, 0xff, 0x5f, 0x78, 0x99, 0xff, 0x4e, 0x67, 0x95, 0xff, 0x32, 0x47, 0x76, 0xff, 0x29, 0x3c, 0x62, 0xff, 0x33, 0x43, 0x62, 0xff, 0x33, 0x3a, 0x5a, 0xff, 0x27, 0x35, 0x5b, 0xff, 0x23, 0x37, 0x5e, 0xff, 0x2d, 0x3f, 0x63, 0xff, 0x2c, 0x3b, 0x60, 0xff, 0x25, 0x37, 0x5e, 0xff, 0x2b, 0x41, 0x6d, 0xff, 0x26, 0x3a, 0x68, 0xff, 0x30, 0x3f, 0x6d, 0xff, 0x2f, 0x41, 0x6a, 0xff, 0x29, 0x3a, 0x61, 0xff, 0x2e, 0x40, 0x67, 0xff, 0x23, 0x35, 0x5c, 0xff, 0x1b, 0x29, 0x51, 0xff, 0x2b, 0x37, 0x5a, 0xff, 0x22, 0x30, 0x54, 0xff, 0x26, 0x3d, 0x6d, 0xff, 0x2e, 0x4d, 0x88, 0xff, 0x29, 0x46, 0x7e, 0xff, 0x48, 0x60, 0x93, 0xff, 0x15, 0x20, 0x50, 0xff, 0x00, 0x00, 0x09, 0xff, 0x06, 0x07, 0x2b, 0xff, 0x48, 0x51, 0x7a, 0xff, 0x5f, 0x76, 0xac, 0xff, 0x59, 0x6f, 0x9d, 0xff, 0x4c, 0x5c, 0x87, 0xff, 0x3d, 0x52, 0x8f, 0xff, 0x5a, 0x71, 0xb0, 0xff, 0x5a, 0x70, 0xae, 0xff, 0x24, 0x34, 0x81, 0xff, 0x24, 0x36, 0x7c, 0xff, 0x57, 0x68, 0xad, 0xff, 0x55, 0x5c, 0xab, 0xff, 0x83, 0x88, 0x99, 0xff, 0xce, 0xbe, 0xa7, 0xff, 0xd2, 0xba, 0xa3, 0xff, 0xcd, 0xbc, 0x9e, 0xff, 0xca, 0xb7, 0xa1, 0xff, 0xc6, 0xb5, 0xa0, 0xff, 0xc4, 0xb6, 0xa2, 0xff, 0xc0, 0xb6, 0xa4, 0xff, 0xbd, 0xb5, 0xa4, 0xff, 0xbb, 0xb4, 0xa6, 0xff, 0xb7, 0xb4, 0xa7, 0xff, 0xb2, 0xb3, 0xa7, 0xff, 0xab, 0xb0, 0xa9, 0xff, 0xa5, 0xad, 0xa7, 0xff, 0xa2, 0xa9, 0xa3, 0xff, 0x9b, 0xa3, 0x9d, 0xff, 0x95, 0x9d, 0x96, 0xff, 0x90, 0x97, 0x8f, 0xff, 0x8a, 0x91, 0x87, 0x6b,
    0x6c, 0x62, 0x62, 0x4b, 0x6f, 0x68, 0x65, 0xff, 0x72, 0x6a, 0x67, 0xff, 0x74, 0x6c, 0x69, 0xff, 0x77, 0x6d, 0x6b, 0xff, 0x79, 0x6d, 0x6b, 0xff, 0x7a, 0x6e, 0x69, 0xff, 0x7a, 0x6f, 0x68, 0xff, 0x7b, 0x6f, 0x65, 0xff, 0x7b, 0x70, 0x64, 0xff, 0x7e, 0x71, 0x66, 0xff, 0x80, 0x73, 0x68, 0xff, 0x85, 0x78, 0x6d, 0xff, 0x89, 0x7c, 0x71, 0xff, 0x8c, 0x7f, 0x74, 0xff, 0x8d, 0x7f, 0x74, 0xff, 0x88, 0x7c, 0x6f, 0xff, 0x82, 0x76, 0x6d, 0xff, 0x7d, 0x72, 0x6b, 0xff, 0x78, 0x6d, 0x69, 0xff, 0x7a, 0x6c, 0x6b, 0xff, 0x7b, 0x6d, 0x6c, 0xff, 0x76, 0x6e, 0x73, 0xff, 0x77, 0x71, 0x77, 0xff, 0x7f, 0x77, 0x7d, 0xff, 0x81, 0x7b, 0x85, 0xff, 0x86, 0x81, 0x8c, 0xff, 0x8d, 0x8a, 0x98, 0xff, 0x8d, 0x8a, 0x92, 0xff, 0xa2, 0xa7, 0xa4, 0xff, 0xe6, 0xf9, 0xfa, 0xff, 0xd5, 0xec, 0xff, 0xff, 0xc2, 0xd9, 0xff, 0xff, 0xa5, 0xbc, 0xe7, 0xff, 0x86, 0x9e, 0xcc, 0xff, 0x7a, 0x92, 0xc7, 0xff, 0x52, 0x68, 0xa2, 0xff, 0x63, 0x78, 0xae, 0xff, 0xc6, 0xd9, 0xfa, 0xff, 0xe0, 0xf3, 0xff, 0xff, 0xd5, 0xea, 0xf9, 0xff, 0xcf, 0xe4, 0xfb, 0xff, 0xc2, 0xd4, 0xec, 0xff, 0xc5, 0xd3, 0xe8, 0xff, 0xcc, 0xd1, 0xe0, 0xff, 0xd6, 0xd5, 0xe1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xfd, 0xff, 0xff, 0xb5, 0xca, 0xf1, 0xff, 0x7f, 0x9a, 0xd3, 0xff, 0x69, 0x86, 0xc3, 0xff, 0x5a, 0x77, 0xaf, 0xff, 0x54, 0x6e, 0xa5, 0xff, 0x4b, 0x64, 0x9a, 0xff, 0x47, 0x5d, 0x92, 0xff, 0x3d, 0x51, 0x85, 0xff, 0x37, 0x4b, 0x7e, 0xff, 0x32, 0x41, 0x72, 0xff, 0x34, 0x40, 0x70, 0xff, 0x33, 0x41, 0x71, 0xff, 0x24, 0x32, 0x61, 0xff, 0x23, 0x30, 0x5e, 0xff, 0x31, 0x40, 0x6b, 0xff, 0x2a, 0x36, 0x65, 0xff, 0x2e, 0x38, 0x6a, 0xff, 0x4e, 0x5a, 0x87, 0xff, 0x4a, 0x57, 0x7f, 0xff, 0x25, 0x35, 0x57, 0xff, 0x03, 0x11, 0x27, 0xff, 0x4a, 0x60, 0x7e, 0xff, 0x5d, 0x75, 0xa5, 0xff, 0x34, 0x45, 0x77, 0xff, 0x22, 0x33, 0x5a, 0xff, 0x20, 0x30, 0x4a, 0xff, 0x22, 0x29, 0x44, 0xff, 0x32, 0x41, 0x66, 0xff, 0x30, 0x47, 0x6d, 0xff, 0x30, 0x42, 0x64, 0xff, 0x29, 0x38, 0x5c, 0xff, 0x26, 0x38, 0x5d, 0xff, 0x28, 0x3f, 0x69, 0xff, 0x2d, 0x40, 0x6f, 0xff, 0x31, 0x3f, 0x6f, 0xff, 0x2c, 0x3d, 0x66, 0xff, 0x2f, 0x40, 0x65, 0xff, 0x2b, 0x3d, 0x62, 0xff, 0x28, 0x37, 0x5f, 0xff, 0x1c, 0x29, 0x51, 0xff, 0x22, 0x2e, 0x51, 0xff, 0x25, 0x33, 0x56, 0xff, 0x26, 0x3d, 0x6f, 0xff, 0x2d, 0x4d, 0x87, 0xff, 0x29, 0x44, 0x7b, 0xff, 0x35, 0x4a, 0x7b, 0xff, 0x2a, 0x3e, 0x6d, 0xff, 0x36, 0x46, 0x73, 0xff, 0x55, 0x67, 0x97, 0xff, 0x76, 0x8c, 0xbc, 0xff, 0x70, 0x8d, 0xbb, 0xff, 0x63, 0x80, 0xaf, 0xff, 0x57, 0x6d, 0xa6, 0xff, 0x4f, 0x64, 0xa7, 0xff, 0x47, 0x5e, 0xa2, 0xff, 0x28, 0x3c, 0x7e, 0xff, 0x18, 0x23, 0x6e, 0xff, 0x51, 0x5c, 0xa4, 0xff, 0x64, 0x73, 0xbe, 0xff, 0x43, 0x4d, 0x80, 0xff, 0x5f, 0x5e, 0x58, 0xff, 0xc8, 0xb5, 0xa2, 0xff, 0xd3, 0xbe, 0xa4, 0xff, 0xce, 0xbb, 0x9e, 0xff, 0xc9, 0xb6, 0x9f, 0xff, 0xc5, 0xb6, 0x9f, 0xff, 0xc4, 0xb6, 0xa1, 0xff, 0xc2, 0xb5, 0xa3, 0xff, 0xbf, 0xb4, 0xa3, 0xff, 0xbb, 0xb4, 0xa2, 0xff, 0xb7, 0xb3, 0xa3, 0xff, 0xb1, 0xb1, 0xa5, 0xff, 0xaa, 0xae, 0xa7, 0xff, 0xa4, 0xab, 0xa5, 0xff, 0xa0, 0xa8, 0xa0, 0xff, 0x9c, 0xa2, 0x9b, 0xff, 0x95, 0x9c, 0x95, 0xff, 0x8e, 0x96, 0x8c, 0xff, 0x8b, 0x8e, 0x84, 0x4b,
    0x64, 0x5c, 0x64, 0x21, 0x66, 0x62, 0x64, 0xff, 0x67, 0x63, 0x65, 0xff, 0x68, 0x63, 0x65, 0xff, 0x6a, 0x64, 0x65, 0xff, 0x6c, 0x66, 0x66, 0xff, 0x6e, 0x67, 0x66, 0xff, 0x70, 0x68, 0x67, 0xff, 0x71, 0x69, 0x66, 0xff, 0x73, 0x6a, 0x66, 0xff, 0x73, 0x6b, 0x66, 0xff, 0x75, 0x6d, 0x67, 0xff, 0x77, 0x6f, 0x68, 0xff, 0x79, 0x71, 0x6a, 0xff, 0x7a, 0x72, 0x6c, 0xff, 0x7a, 0x72, 0x6b, 0xff, 0x78, 0x70, 0x6a, 0xff, 0x77, 0x6e, 0x6b, 0xff, 0x74, 0x6b, 0x6a, 0xff, 0x71, 0x69, 0x6a, 0xff, 0x73, 0x6a, 0x6c, 0xff, 0x74, 0x6c, 0x70, 0xff, 0x75, 0x6e, 0x73, 0xff, 0x78, 0x71, 0x78, 0xff, 0x7a, 0x75, 0x7e, 0xff, 0x7f, 0x7a, 0x83, 0xff, 0x82, 0x7e, 0x88, 0xff, 0x86, 0x82, 0x8e, 0xff, 0x86, 0x80, 0x8b, 0xff, 0x9a, 0x9c, 0xa1, 0xff, 0xe3, 0xf4, 0xf9, 0xff, 0xd7, 0xec, 0xff, 0xff, 0xb3, 0xc9, 0xf2, 0xff, 0xa0, 0xb6, 0xe0, 0xff, 0x97, 0xae, 0xdc, 0xff, 0x6b, 0x82, 0xb5, 0xff, 0x36, 0x4b, 0x7d, 0xff, 0x83, 0x95, 0xc1, 0xff, 0xd9, 0xed, 0xff, 0xff, 0xe2, 0xf4, 0xfc, 0xff, 0xd1, 0xed, 0xf6, 0xff, 0xd4, 0xed, 0xf8, 0xff, 0xe5, 0xef, 0xf3, 0xff, 0xfe, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xfd, 0xfa, 0xff, 0xdc, 0xe9, 0xf5, 0xff, 0xc2, 0xcd, 0xf2, 0xff, 0x96, 0xa0, 0xe7, 0xff, 0x6c, 0x76, 0xcc, 0xff, 0x64, 0x6f, 0xc5, 0xff, 0x5d, 0x6a, 0xb8, 0xff, 0x56, 0x63, 0xad, 0xff, 0x50, 0x5b, 0xa6, 0xff, 0x4d, 0x57, 0xa1, 0xff, 0x4a, 0x53, 0x9c, 0xff, 0x4a, 0x52, 0x9b, 0xff, 0x44, 0x4d, 0x94, 0xff, 0x3c, 0x49, 0x8b, 0xff, 0x39, 0x47, 0x86, 0xff, 0x36, 0x44, 0x7f, 0xff, 0x38, 0x46, 0x7d, 0xff, 0x35, 0x43, 0x75, 0xff, 0x34, 0x40, 0x73, 0xff, 0x3a, 0x44, 0x78, 0xff, 0x38, 0x43, 0x75, 0xff, 0x2c, 0x37, 0x63, 0xff, 0x21, 0x30, 0x52, 0xff, 0x08, 0x19, 0x2e, 0xff, 0x55, 0x6a, 0x87, 0xff, 0x83, 0x96, 0xc5, 0xff, 0x40, 0x4f, 0x80, 0xff, 0x1b, 0x2b, 0x51, 0xff, 0x21, 0x34, 0x4e, 0xff, 0x1a, 0x2b, 0x45, 0xff, 0x2f, 0x41, 0x60, 0xff, 0x37, 0x4a, 0x6a, 0xff, 0x2c, 0x3c, 0x5b, 0xff, 0x20, 0x33, 0x53, 0xff, 0x34, 0x47, 0x6a, 0xff, 0x34, 0x48, 0x6d, 0xff, 0x36, 0x47, 0x73, 0xff, 0x37, 0x46, 0x74, 0xff, 0x2e, 0x3e, 0x68, 0xff, 0x26, 0x37, 0x5d, 0xff, 0x29, 0x39, 0x5e, 0xff, 0x2e, 0x3d, 0x64, 0xff, 0x1e, 0x2e, 0x54, 0xff, 0x1a, 0x29, 0x4b, 0xff, 0x2a, 0x38, 0x5d, 0xff, 0x2c, 0x42, 0x74, 0xff, 0x2c, 0x4a, 0x86, 0xff, 0x29, 0x40, 0x78, 0xff, 0x1a, 0x34, 0x68, 0xff, 0x29, 0x49, 0x7e, 0xff, 0x65, 0x7d, 0xb3, 0xff, 0x60, 0x77, 0xa8, 0xff, 0x55, 0x6e, 0xa1, 0xff, 0x4b, 0x6a, 0x9b, 0xff, 0x36, 0x56, 0x92, 0xff, 0x26, 0x41, 0x87, 0xff, 0x24, 0x36, 0x75, 0xff, 0x1d, 0x28, 0x67, 0xff, 0x17, 0x1f, 0x65, 0xff, 0x4b, 0x55, 0xa0, 0xff, 0x67, 0x6a, 0xc4, 0xff, 0x57, 0x63, 0x99, 0xff, 0x20, 0x38, 0x35, 0xff, 0x5d, 0x5b, 0x5c, 0xff, 0xc8, 0xb2, 0xa3, 0xff, 0xd4, 0xbe, 0xa0, 0xff, 0xcd, 0xb8, 0x9f, 0xff, 0xcb, 0xb6, 0x9d, 0xff, 0xc7, 0xb5, 0x9e, 0xff, 0xc3, 0xb4, 0xa0, 0xff, 0xc0, 0xb3, 0xa2, 0xff, 0xbe, 0xb3, 0xa2, 0xff, 0xbb, 0xb3, 0xa0, 0xff, 0xb6, 0xb2, 0xa0, 0xff, 0xb0, 0xb0, 0xa3, 0xff, 0xaa, 0xad, 0xa4, 0xff, 0xa4, 0xaa, 0xa2, 0xff, 0x9f, 0xa6, 0x9d, 0xff, 0x9b, 0xa0, 0x98, 0xff, 0x91, 0x99, 0x90, 0xff, 0x8b, 0x92, 0x88, 0xff, 0x8b, 0x92, 0x83, 0x21,
    0x00, 0x00, 0x00, 0x01, 0x60, 0x5d, 0x65, 0xf2, 0x62, 0x60, 0x67, 0xff, 0x63, 0x61, 0x68, 0xff, 0x63, 0x61, 0x67, 0xff, 0x65, 0x63, 0x68, 0xff, 0x68, 0x64, 0x68, 0xff, 0x69, 0x65, 0x69, 0xff, 0x6b, 0x66, 0x6a, 0xff, 0x6c, 0x66, 0x6b, 0xff, 0x6d, 0x68, 0x69, 0xff, 0x6e, 0x69, 0x69, 0xff, 0x6e, 0x69, 0x6a, 0xff, 0x70, 0x6b, 0x6c, 0xff, 0x70, 0x6b, 0x6c, 0xff, 0x6e, 0x69, 0x6a, 0xff, 0x6e, 0x68, 0x6a, 0xff, 0x6d, 0x67, 0x69, 0xff, 0x6c, 0x66, 0x6a, 0xff, 0x6c, 0x66, 0x6b, 0xff, 0x6c, 0x68, 0x6e, 0xff, 0x6f, 0x6b, 0x71, 0xff, 0x72, 0x6d, 0x74, 0xff, 0x76, 0x71, 0x7a, 0xff, 0x79, 0x75, 0x7d, 0xff, 0x7a, 0x76, 0x81, 0xff, 0x7d, 0x79, 0x84, 0xff, 0x81, 0x7d, 0x88, 0xff, 0x82, 0x79, 0x87, 0xff, 0x81, 0x81, 0x8a, 0xff, 0xd1, 0xe0, 0xe7, 0xff, 0xe1, 0xf7, 0xff, 0xff, 0xad, 0xc4, 0xea, 0xff, 0xa3, 0xba, 0xe1, 0xff, 0x91, 0xa9, 0xd5, 0xff, 0x58, 0x6f, 0x9e, 0xff, 0x4e, 0x63, 0x8d, 0xff, 0xa8, 0xba, 0xda, 0xff, 0xdf, 0xf2, 0xff, 0xff, 0xde, 0xf1, 0xfa, 0xff, 0xdd, 0xf5, 0xf9, 0xff, 0xe8, 0xfa, 0xff, 0xff, 0xea, 0xf1, 0xff, 0xff, 0xd6, 0xd6, 0xfe, 0xff, 0xbd, 0xc0, 0xf5, 0xff, 0xad, 0xb6, 0xf0, 0xff, 0x94, 0xa6, 0xec, 0xff, 0x78, 0x87, 0xdd, 0xff, 0x60, 0x65, 0xc9, 0xff, 0x5a, 0x58, 0xc2, 0xff, 0x65, 0x61, 0xc4, 0xff, 0x65, 0x64, 0xbb, 0xff, 0x65, 0x64, 0xb9, 0xff, 0x5e, 0x5b, 0xb3, 0xff, 0x51, 0x4e, 0xa5, 0xff, 0x47, 0x43, 0x9a, 0xff, 0x46, 0x41, 0x99, 0xff, 0x43, 0x3a, 0x96, 0xff, 0x3c, 0x3d, 0x8f, 0xff, 0x38, 0x40, 0x88, 0xff, 0x32, 0x3a, 0x7f, 0xff, 0x2f, 0x37, 0x76, 0xff, 0x3a, 0x40, 0x79, 0xff, 0x34, 0x35, 0x6b, 0xff, 0x2c, 0x30, 0x62, 0xff, 0x32, 0x3b, 0x6a, 0xff, 0x2b, 0x34, 0x64, 0xff, 0x19, 0x25, 0x4f, 0xff, 0x1d, 0x2d, 0x4e, 0xff, 0x18, 0x2a, 0x42, 0xff, 0x60, 0x76, 0x94, 0xff, 0x80, 0x94, 0xc2, 0xff, 0x3e, 0x4b, 0x7d, 0xff, 0x29, 0x37, 0x5d, 0xff, 0x1a, 0x2d, 0x46, 0xff, 0x1e, 0x33, 0x4e, 0xff, 0x2a, 0x3d, 0x58, 0xff, 0x17, 0x28, 0x42, 0xff, 0x1f, 0x30, 0x4d, 0xff, 0x24, 0x37, 0x57, 0xff, 0x2f, 0x42, 0x65, 0xff, 0x2f, 0x42, 0x65, 0xff, 0x38, 0x48, 0x73, 0xff, 0x34, 0x43, 0x71, 0xff, 0x2a, 0x3a, 0x62, 0xff, 0x21, 0x31, 0x56, 0xff, 0x2b, 0x3b, 0x5f, 0xff, 0x2c, 0x3a, 0x5f, 0xff, 0x22, 0x31, 0x58, 0xff, 0x21, 0x31, 0x55, 0xff, 0x23, 0x33, 0x57, 0xff, 0x2e, 0x44, 0x77, 0xff, 0x26, 0x41, 0x7f, 0xff, 0x29, 0x40, 0x78, 0xff, 0x40, 0x58, 0x90, 0xff, 0x35, 0x4d, 0x89, 0xff, 0x45, 0x59, 0x92, 0xff, 0x4d, 0x61, 0x96, 0xff, 0x5f, 0x71, 0xa8, 0xff, 0x48, 0x5d, 0x95, 0xff, 0x3d, 0x50, 0x8c, 0xff, 0x34, 0x43, 0x7c, 0xff, 0x24, 0x32, 0x64, 0xff, 0x2d, 0x35, 0x75, 0xff, 0x53, 0x5a, 0xab, 0xff, 0x55, 0x6c, 0xc1, 0xff, 0x61, 0x6a, 0xc2, 0xff, 0x25, 0x2d, 0x4b, 0xff, 0x1f, 0x37, 0x1a, 0xff, 0x96, 0x8f, 0x8d, 0xff, 0xce, 0xb7, 0xa7, 0xff, 0xcf, 0xbc, 0x9b, 0xff, 0xcd, 0xb7, 0xa0, 0xff, 0xcb, 0xb7, 0x9c, 0xff, 0xc7, 0xb5, 0x9d, 0xff, 0xc3, 0xb3, 0x9d, 0xff, 0xbf, 0xb3, 0xa0, 0xff, 0xbd, 0xb3, 0xa2, 0xff, 0xb9, 0xb0, 0x9e, 0xff, 0xb5, 0xaf, 0x9c, 0xff, 0xb1, 0xad, 0x9f, 0xff, 0xa9, 0xaa, 0xa0, 0xff, 0xa3, 0xa8, 0x9e, 0xff, 0xa1, 0xa5, 0x9a, 0xff, 0x99, 0x9e, 0x95, 0xff, 0x8f, 0x95, 0x8c, 0xff, 0x88, 0x8f, 0x84, 0xf2, 0xff, 0xff, 0xff, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x5b, 0x5b, 0x64, 0xc6, 0x5d, 0x5d, 0x66, 0xff, 0x5f, 0x5f, 0x67, 0xff, 0x60, 0x61, 0x68, 0xff, 0x62, 0x62, 0x69, 0xff, 0x65, 0x63, 0x6a, 0xff, 0x65, 0x63, 0x6a, 0xff, 0x68, 0x64, 0x6b, 0xff, 0x69, 0x65, 0x6c, 0xff, 0x6a, 0x66, 0x6b, 0xff, 0x6b, 0x67, 0x6c, 0xff, 0x6b, 0x68, 0x6d, 0xff, 0x6c, 0x68, 0x6d, 0xff, 0x6b, 0x68, 0x6d, 0xff, 0x6b, 0x67, 0x6c, 0xff, 0x6c, 0x66, 0x6c, 0xff, 0x6b, 0x64, 0x69, 0xff, 0x68, 0x63, 0x6a, 0xff, 0x69, 0x66, 0x6c, 0xff, 0x6c, 0x69, 0x70, 0xff, 0x6e, 0x6b, 0x75, 0xff, 0x71, 0x6d, 0x78, 0xff, 0x76, 0x71, 0x7c, 0xff, 0x78, 0x73, 0x7e, 0xff, 0x78, 0x74, 0x7f, 0xff, 0x7b, 0x77, 0x82, 0xff, 0x7e, 0x7a, 0x85, 0xff, 0x80, 0x76, 0x85, 0xff, 0x74, 0x73, 0x7c, 0xff, 0xc5, 0xd5, 0xd9, 0xff, 0xe3, 0xf8, 0xff, 0xff, 0xb7, 0xcd, 0xf0, 0xff, 0xaf, 0xc6, 0xe7, 0xff, 0x7f, 0x9a, 0xc0, 0xff, 0x66, 0x81, 0xaa, 0xff, 0x94, 0xaa, 0xcc, 0xff, 0xcf, 0xe1, 0xfc, 0xff, 0xd5, 0xe8, 0xfa, 0xff, 0xd9, 0xf0, 0xfb, 0xff, 0xe6, 0xf7, 0xff, 0xff, 0xce, 0xda, 0xf8, 0xff, 0xa3, 0xaf, 0xf0, 0xff, 0x8a, 0x96, 0xf0, 0xff, 0x67, 0x76, 0xd7, 0xff, 0x59, 0x71, 0xc9, 0xff, 0x67, 0x6c, 0xd2, 0xff, 0x66, 0x5e, 0xcd, 0xff, 0x6d, 0x6b, 0xcd, 0xff, 0x70, 0x77, 0xcc, 0xff, 0x65, 0x7b, 0xc4, 0xff, 0x49, 0x6e, 0xad, 0xff, 0x6a, 0x86, 0xc2, 0xff, 0x74, 0x88, 0xc5, 0xff, 0x63, 0x78, 0xb8, 0xff, 0x59, 0x6e, 0xae, 0xff, 0x61, 0x74, 0xb6, 0xff, 0x53, 0x67, 0xaa, 0xff, 0x35, 0x41, 0x82, 0xff, 0x26, 0x2e, 0x6b, 0xff, 0x3e, 0x4b, 0x85, 0xff, 0x28, 0x33, 0x67, 0xff, 0x0d, 0x13, 0x42, 0xff, 0x17, 0x13, 0x42, 0xff, 0x09, 0x07, 0x2a, 0xff, 0x03, 0x05, 0x1f, 0xff, 0x01, 0x05, 0x24, 0xff, 0x17, 0x22, 0x44, 0xff, 0x23, 0x34, 0x55, 0xff, 0x1c, 0x31, 0x4e, 0xff, 0x82, 0x9e, 0xbc, 0xff, 0x86, 0x9f, 0xc9, 0xff, 0x53, 0x60, 0x90, 0xff, 0x30, 0x3a, 0x60, 0xff, 0x13, 0x24, 0x3d, 0xff, 0x22, 0x37, 0x50, 0xff, 0x23, 0x33, 0x4c, 0xff, 0x1c, 0x2a, 0x42, 0xff, 0x2f, 0x40, 0x5f, 0xff, 0x2f, 0x41, 0x65, 0xff, 0x21, 0x32, 0x57, 0xff, 0x2b, 0x39, 0x5d, 0xff, 0x37, 0x45, 0x6f, 0xff, 0x2d, 0x3d, 0x6a, 0xff, 0x27, 0x37, 0x5d, 0xff, 0x24, 0x35, 0x58, 0xff, 0x22, 0x31, 0x55, 0xff, 0x22, 0x2e, 0x54, 0xff, 0x24, 0x34, 0x5b, 0xff, 0x1f, 0x30, 0x55, 0xff, 0x23, 0x31, 0x59, 0xff, 0x2d, 0x42, 0x77, 0xff, 0x19, 0x34, 0x72, 0xff, 0x32, 0x47, 0x81, 0xff, 0x69, 0x7b, 0xb6, 0xff, 0x50, 0x64, 0xa3, 0xff, 0x44, 0x56, 0x90, 0xff, 0x53, 0x63, 0x9b, 0xff, 0x5c, 0x69, 0xa0, 0xff, 0x63, 0x6f, 0xa8, 0xff, 0x72, 0x79, 0xb4, 0xff, 0x7c, 0x84, 0xb4, 0xff, 0x6e, 0x7e, 0xab, 0xff, 0x6a, 0x79, 0xbc, 0xff, 0x54, 0x60, 0xb4, 0xff, 0x59, 0x71, 0xb9, 0xff, 0x3f, 0x4c, 0x76, 0xff, 0x00, 0x00, 0x00, 0xff, 0x39, 0x45, 0x3a, 0xff, 0xaa, 0xa2, 0x97, 0xff, 0xcc, 0xb4, 0xa0, 0xff, 0xcb, 0xb5, 0x99, 0xff, 0xca, 0xb6, 0x9c, 0xff, 0xcb, 0xb7, 0x9b, 0xff, 0xc7, 0xb4, 0x9a, 0xff, 0xc2, 0xb2, 0x9c, 0xff, 0xc1, 0xb4, 0xa0, 0xff, 0xbe, 0xb2, 0xa0, 0xff, 0xb6, 0xad, 0x9a, 0xff, 0xb2, 0xab, 0x97, 0xff, 0xaf, 0xab, 0x9b, 0xff, 0xa7, 0xa8, 0x9b, 0xff, 0xa1, 0xa5, 0x9a, 0xff, 0xa0, 0xa3, 0x96, 0xff, 0x9a, 0x9d, 0x92, 0xff, 0x8e, 0x94, 0x8a, 0xff, 0x88, 0x8f, 0x83, 0xc4, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x5a, 0x58, 0x61, 0x90, 0x5b, 0x59, 0x63, 0xff, 0x5d, 0x5b, 0x64, 0xff, 0x5e, 0x5d, 0x65, 0xff, 0x5e, 0x5e, 0x67, 0xff, 0x61, 0x5f, 0x68, 0xff, 0x62, 0x60, 0x69, 0xff, 0x64, 0x60, 0x6a, 0xff, 0x67, 0x60, 0x6b, 0xff, 0x67, 0x63, 0x6a, 0xff, 0x68, 0x64, 0x6b, 0xff, 0x69, 0x65, 0x6c, 0xff, 0x69, 0x65, 0x6c, 0xff, 0x69, 0x65, 0x6b, 0xff, 0x6a, 0x66, 0x6c, 0xff, 0x6d, 0x66, 0x6c, 0xff, 0x6c, 0x63, 0x6b, 0xff, 0x6a, 0x63, 0x6a, 0xff, 0x69, 0x66, 0x6b, 0xff, 0x6c, 0x67, 0x6e, 0xff, 0x6e, 0x6a, 0x72, 0xff, 0x70, 0x6d, 0x78, 0xff, 0x73, 0x70, 0x7d, 0xff, 0x75, 0x72, 0x7d, 0xff, 0x78, 0x74, 0x7f, 0xff, 0x7b, 0x77, 0x82, 0xff, 0x7d, 0x79, 0x83, 0xff, 0x81, 0x78, 0x85, 0xff, 0x6a, 0x69, 0x70, 0xff, 0xaa, 0xb9, 0xbb, 0xff, 0xe3, 0xf9, 0xff, 0xff, 0xb9, 0xd1, 0xf0, 0xff, 0xb2, 0xc9, 0xe4, 0xff, 0x6f, 0x8f, 0xb1, 0xff, 0x77, 0x98, 0xbc, 0xff, 0xb0, 0xc7, 0xe4, 0xff, 0xc6, 0xda, 0xf0, 0xff, 0xd1, 0xe5, 0xf8, 0xff, 0xd7, 0xef, 0xfb, 0xff, 0xd7, 0xe8, 0xff, 0xff, 0x9b, 0xa8, 0xe5, 0xff, 0x6f, 0x82, 0xdb, 0xff, 0x51, 0x6d, 0xd4, 0xff, 0x65, 0x83, 0xd0, 0xff, 0xa1, 0xb8, 0xdd, 0xff, 0x7a, 0x9f, 0xc9, 0xff, 0x7f, 0xa8, 0xd3, 0xff, 0xab, 0xcb, 0xe7, 0xff, 0xae, 0xca, 0xe3, 0xff, 0xa3, 0xc0, 0xe4, 0xff, 0x8c, 0xaf, 0xe8, 0xff, 0xb5, 0xd2, 0xf7, 0xff, 0xc0, 0xd8, 0xed, 0xff, 0x89, 0xa1, 0xbb, 0xff, 0x8e, 0xa5, 0xc4, 0xff, 0xb5, 0xcf, 0xee, 0xff, 0x8b, 0xa2, 0xbe, 0xff, 0x66, 0x7c, 0xa2, 0xff, 0x47, 0x61, 0x8d, 0xff, 0x58, 0x77, 0x9e, 0xff, 0x35, 0x50, 0x74, 0xff, 0x00, 0x12, 0x32, 0xff, 0x13, 0x19, 0x3e, 0xff, 0x04, 0x01, 0x12, 0xff, 0x00, 0x00, 0x00, 0xff, 0x08, 0x0a, 0x1a, 0xff, 0x1b, 0x24, 0x3e, 0xff, 0x24, 0x35, 0x55, 0xff, 0x23, 0x38, 0x5a, 0xff, 0x7b, 0x9c, 0xbd, 0xff, 0x88, 0xa7, 0xce, 0xff, 0x3d, 0x4a, 0x7a, 0xff, 0x27, 0x2e, 0x54, 0xff, 0x12, 0x1e, 0x38, 0xff, 0x1c, 0x2e, 0x48, 0xff, 0x15, 0x24, 0x3a, 0xff, 0x22, 0x2f, 0x45, 0xff, 0x38, 0x47, 0x68, 0xff, 0x36, 0x47, 0x6e, 0xff, 0x28, 0x37, 0x60, 0xff, 0x2f, 0x39, 0x60, 0xff, 0x37, 0x43, 0x6d, 0xff, 0x27, 0x37, 0x62, 0xff, 0x2a, 0x3a, 0x60, 0xff, 0x28, 0x37, 0x5b, 0xff, 0x21, 0x2f, 0x53, 0xff, 0x29, 0x35, 0x5a, 0xff, 0x25, 0x36, 0x5c, 0xff, 0x1a, 0x2e, 0x51, 0xff, 0x21, 0x2f, 0x57, 0xff, 0x2e, 0x42, 0x77, 0xff, 0x12, 0x2d, 0x6d, 0xff, 0x2b, 0x3e, 0x79, 0xff, 0x56, 0x6a, 0xa3, 0xff, 0x4c, 0x69, 0xa5, 0xff, 0x46, 0x63, 0x9a, 0xff, 0x53, 0x6c, 0x9f, 0xff, 0x5e, 0x71, 0xa2, 0xff, 0x67, 0x74, 0xa7, 0xff, 0x70, 0x7b, 0xb7, 0xff, 0x70, 0x81, 0xbb, 0xff, 0x5f, 0x7c, 0xb1, 0xff, 0x4b, 0x65, 0xb1, 0xff, 0x61, 0x6e, 0xc0, 0xff, 0x4e, 0x55, 0x7b, 0xff, 0x02, 0x04, 0x02, 0xff, 0x04, 0x08, 0x08, 0xff, 0x61, 0x5e, 0x78, 0xff, 0xb4, 0xad, 0x9c, 0xff, 0xc4, 0xac, 0x94, 0xff, 0xc7, 0xae, 0x9b, 0xff, 0xc3, 0xb0, 0x94, 0xff, 0xc5, 0xb0, 0x95, 0xff, 0xc5, 0xb1, 0x98, 0xff, 0xc2, 0xb1, 0x9b, 0xff, 0xc0, 0xb2, 0x9e, 0xff, 0xbb, 0xae, 0x9d, 0xff, 0xb1, 0xa7, 0x94, 0xff, 0xae, 0xa7, 0x91, 0xff, 0xab, 0xa8, 0x97, 0xff, 0xa6, 0xa5, 0x99, 0xff, 0xa1, 0xa2, 0x97, 0xff, 0x9f, 0xa0, 0x94, 0xff, 0x96, 0x9a, 0x8e, 0xff, 0x89, 0x8f, 0x85, 0xff, 0x84, 0x88, 0x7d, 0x90, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x59, 0x56, 0x5f, 0x58, 0x5a, 0x57, 0x60, 0xff, 0x5c, 0x59, 0x62, 0xff, 0x5c, 0x59, 0x61, 0xff, 0x5c, 0x5a, 0x64, 0xff, 0x5f, 0x5c, 0x66, 0xff, 0x60, 0x5d, 0x66, 0xff, 0x60, 0x5d, 0x66, 0xff, 0x62, 0x5e, 0x68, 0xff, 0x62, 0x5f, 0x66, 0xff, 0x62, 0x60, 0x66, 0xff, 0x63, 0x61, 0x67, 0xff, 0x64, 0x61, 0x67, 0xff, 0x65, 0x62, 0x68, 0xff, 0x66, 0x63, 0x69, 0xff, 0x66, 0x62, 0x6b, 0xff, 0x64, 0x61, 0x6a, 0xff, 0x63, 0x5f, 0x68, 0xff, 0x66, 0x62, 0x6a, 0xff, 0x69, 0x66, 0x6d, 0xff, 0x6d, 0x6a, 0x6f, 0xff, 0x6f, 0x6d, 0x75, 0xff, 0x70, 0x6d, 0x7a, 0xff, 0x73, 0x6f, 0x7a, 0xff, 0x76, 0x71, 0x7c, 0xff, 0x78, 0x74, 0x7f, 0xff, 0x7a, 0x76, 0x82, 0xff, 0x81, 0x78, 0x83, 0xff, 0x68, 0x66, 0x6c, 0xff, 0xa2, 0xac, 0xaf, 0xff, 0xee, 0xff, 0xff, 0xff, 0xc0, 0xd8, 0xef, 0xff, 0xaf, 0xc9, 0xe4, 0xff, 0x70, 0x90, 0xb0, 0xff, 0x71, 0x90, 0xb7, 0xff, 0xa2, 0xb8, 0xe1, 0xff, 0x92, 0xa6, 0xc7, 0xff, 0xb7, 0xcd, 0xe5, 0xff, 0xde, 0xf5, 0xff, 0xff, 0xc4, 0xc7, 0xf2, 0xff, 0x80, 0x8d, 0xee, 0xff, 0x47, 0x73, 0xdb, 0xff, 0x70, 0x96, 0xce, 0xff, 0xce, 0xdb, 0xed, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2, 0xff, 0xf6, 0xff, 0xe2, 0xff, 0xff, 0xff, 0xd2, 0xf2, 0xee, 0xff, 0xa2, 0xb9, 0xc5, 0xff, 0x9c, 0xaa, 0xc3, 0xff, 0x65, 0x6c, 0x8b, 0xff, 0x5d, 0x60, 0x6c, 0xff, 0x69, 0x6d, 0x73, 0xff, 0x20, 0x21, 0x25, 0xff, 0x2a, 0x2c, 0x32, 0xff, 0x8c, 0x96, 0xa8, 0xff, 0xa7, 0xb9, 0xd1, 0xff, 0x6a, 0x85, 0xad, 0xff, 0x51, 0x72, 0x9e, 0xff, 0x7e, 0x9e, 0xc1, 0xff, 0x3f, 0x59, 0x7c, 0xff, 0x04, 0x15, 0x38, 0xff, 0x0b, 0x19, 0x36, 0xff, 0x00, 0x02, 0x1a, 0xff, 0x0e, 0x10, 0x2c, 0xff, 0x1a, 0x1f, 0x42, 0xff, 0x25, 0x32, 0x51, 0xff, 0x1f, 0x34, 0x4f, 0xff, 0x31, 0x47, 0x66, 0xff, 0x58, 0x76, 0x99, 0xff, 0x53, 0x6f, 0x99, 0xff, 0x2f, 0x3f, 0x6d, 0xff, 0x2a, 0x38, 0x5a, 0xff, 0x15, 0x23, 0x3a, 0xff, 0x14, 0x20, 0x3b, 0xff, 0x16, 0x23, 0x3f, 0xff, 0x21, 0x30, 0x4b, 0xff, 0x21, 0x30, 0x50, 0xff, 0x31, 0x40, 0x63, 0xff, 0x3a, 0x49, 0x6d, 0xff, 0x2a, 0x37, 0x5b, 0xff, 0x28, 0x34, 0x5a, 0xff, 0x36, 0x42, 0x69, 0xff, 0x2c, 0x39, 0x5f, 0xff, 0x26, 0x33, 0x58, 0xff, 0x26, 0x34, 0x59, 0xff, 0x2a, 0x37, 0x5e, 0xff, 0x2b, 0x37, 0x5e, 0xff, 0x24, 0x32, 0x57, 0xff, 0x1a, 0x2b, 0x53, 0xff, 0x2e, 0x41, 0x72, 0xff, 0x17, 0x2c, 0x67, 0xff, 0x2d, 0x3d, 0x7b, 0xff, 0x4a, 0x60, 0xa1, 0xff, 0x48, 0x63, 0xa2, 0xff, 0x5b, 0x70, 0xa7, 0xff, 0x5a, 0x70, 0xaa, 0xff, 0x5d, 0x74, 0xb5, 0xff, 0x5d, 0x73, 0xb0, 0xff, 0x58, 0x65, 0xad, 0xff, 0x42, 0x56, 0xa5, 0xff, 0x4c, 0x6d, 0xb7, 0xff, 0x68, 0x77, 0xc2, 0xff, 0x49, 0x44, 0x77, 0xff, 0x01, 0x00, 0x02, 0xff, 0x00, 0x00, 0x00, 0xff, 0x28, 0x33, 0x41, 0xff, 0x97, 0x88, 0x93, 0xff, 0xc5, 0xb0, 0x9b, 0xff, 0xbe, 0xa7, 0x8b, 0xff, 0xc1, 0xaa, 0x91, 0xff, 0xc0, 0xa9, 0x8d, 0xff, 0xc1, 0xaa, 0x8e, 0xff, 0xc2, 0xae, 0x93, 0xff, 0xc0, 0xb0, 0x97, 0xff, 0xbe, 0xaf, 0x9a, 0xff, 0xba, 0xaa, 0x98, 0xff, 0xb1, 0xa3, 0x8e, 0xff, 0xab, 0xa3, 0x8c, 0xff, 0xaa, 0xa5, 0x92, 0xff, 0xa4, 0xa2, 0x95, 0xff, 0x9f, 0xa1, 0x94, 0xff, 0x9c, 0xa0, 0x91, 0xff, 0x93, 0x96, 0x89, 0xff, 0x8a, 0x8b, 0x7f, 0xff, 0x85, 0x85, 0x76, 0x58, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 0x5e, 0x1b, 0x57, 0x54, 0x5d, 0xff, 0x58, 0x55, 0x5e, 0xff, 0x59, 0x56, 0x5f, 0xff, 0x5a, 0x57, 0x61, 0xff, 0x5c, 0x59, 0x63, 0xff, 0x5d, 0x5a, 0x63, 0xff, 0x5d, 0x5b, 0x64, 0xff, 0x5e, 0x5c, 0x65, 0xff, 0x5e, 0x5c, 0x63, 0xff, 0x5e, 0x5c, 0x62, 0xff, 0x5f, 0x5d, 0x63, 0xff, 0x5f, 0x5d, 0x63, 0xff, 0x61, 0x5f, 0x65, 0xff, 0x61, 0x5f, 0x66, 0xff, 0x5d, 0x5d, 0x66, 0xff, 0x5b, 0x5c, 0x67, 0xff, 0x5d, 0x5c, 0x65, 0xff, 0x62, 0x5f, 0x69, 0xff, 0x67, 0x64, 0x6d, 0xff, 0x6d, 0x68, 0x70, 0xff, 0x6e, 0x6b, 0x73, 0xff, 0x70, 0x6d, 0x76, 0xff, 0x72, 0x6e, 0x79, 0xff, 0x73, 0x6f, 0x7b, 0xff, 0x75, 0x71, 0x7d, 0xff, 0x78, 0x74, 0x81, 0xff, 0x7f, 0x78, 0x82, 0xff, 0x6d, 0x6a, 0x72, 0xff, 0x8b, 0x91, 0x97, 0xff, 0xe3, 0xf1, 0xf9, 0xff, 0xce, 0xe5, 0xf8, 0xff, 0xb8, 0xd2, 0xee, 0xff, 0x6f, 0x8d, 0xb0, 0xff, 0x52, 0x6f, 0x9c, 0xff, 0x82, 0x98, 0xca, 0xff, 0x62, 0x7a, 0xa3, 0xff, 0x90, 0xa8, 0xca, 0xff, 0xb5, 0xc8, 0xef, 0xff, 0x96, 0xaa, 0xe5, 0xff, 0x66, 0x77, 0xe5, 0xff, 0x9b, 0xac, 0xf7, 0xff, 0xf7, 0xff, 0xef, 0xff, 0xff, 0xff, 0xeb, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xb7, 0xbb, 0xda, 0xff, 0x60, 0x64, 0x98, 0xff, 0x26, 0x24, 0x46, 0xff, 0x0b, 0x0e, 0x1e, 0xff, 0x02, 0x09, 0x0c, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x33, 0x3d, 0x4b, 0xff, 0x4c, 0x5d, 0x87, 0xff, 0x12, 0x22, 0x4f, 0xff, 0x39, 0x44, 0x61, 0xff, 0x27, 0x30, 0x55, 0xff, 0x07, 0x0d, 0x36, 0xff, 0x0f, 0x1a, 0x35, 0xff, 0x1c, 0x28, 0x48, 0xff, 0x27, 0x30, 0x61, 0xff, 0x1f, 0x27, 0x59, 0xff, 0x2a, 0x3c, 0x5f, 0xff, 0x20, 0x38, 0x50, 0xff, 0x3c, 0x54, 0x6f, 0xff, 0x5b, 0x75, 0x98, 0xff, 0x46, 0x5e, 0x8a, 0xff, 0x30, 0x42, 0x6e, 0xff, 0x29, 0x39, 0x5a, 0xff, 0x19, 0x27, 0x3d, 0xff, 0x13, 0x1d, 0x38, 0xff, 0x23, 0x30, 0x4f, 0xff, 0x2b, 0x3d, 0x5b, 0xff, 0x21, 0x30, 0x50, 0xff, 0x2e, 0x3d, 0x5c, 0xff, 0x38, 0x47, 0x68, 0xff, 0x31, 0x40, 0x64, 0xff, 0x30, 0x3d, 0x60, 0xff, 0x32, 0x3d, 0x60, 0xff, 0x23, 0x2e, 0x54, 0xff, 0x29, 0x34, 0x5b, 0xff, 0x30, 0x3d, 0x63, 0xff, 0x2b, 0x38, 0x60, 0xff, 0x2d, 0x37, 0x5e, 0xff, 0x20, 0x2b, 0x51, 0xff, 0x1d, 0x2e, 0x58, 0xff, 0x32, 0x45, 0x75, 0xff, 0x2f, 0x41, 0x78, 0xff, 0x3a, 0x4a, 0x88, 0xff, 0x54, 0x67, 0xb1, 0xff, 0x4e, 0x60, 0xa9, 0xff, 0x5f, 0x69, 0xa8, 0xff, 0x5a, 0x67, 0xae, 0xff, 0x4a, 0x5e, 0xae, 0xff, 0x4d, 0x61, 0xa8, 0xff, 0x5b, 0x5d, 0xba, 0xff, 0x61, 0x68, 0xc5, 0xff, 0x45, 0x5f, 0x81, 0xff, 0x23, 0x28, 0x34, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x05, 0x11, 0x1e, 0xff, 0x5d, 0x64, 0x6b, 0xff, 0xbb, 0xa6, 0x99, 0xff, 0xc3, 0xa6, 0x8e, 0xff, 0xbc, 0xa6, 0x88, 0xff, 0xbf, 0xa9, 0x8c, 0xff, 0xc3, 0xa8, 0x8e, 0xff, 0xc1, 0xa9, 0x8d, 0xff, 0xbe, 0xaa, 0x8e, 0xff, 0xba, 0xab, 0x92, 0xff, 0xbc, 0xab, 0x96, 0xff, 0xbb, 0xa8, 0x96, 0xff, 0xb2, 0xa1, 0x8d, 0xff, 0xac, 0xa0, 0x8a, 0xff, 0xaa, 0xa2, 0x8f, 0xff, 0xa2, 0x9f, 0x91, 0xff, 0x9d, 0x9e, 0x90, 0xff, 0x9b, 0x9c, 0x8e, 0xff, 0x95, 0x95, 0x87, 0xff, 0x8a, 0x89, 0x7a, 0xff, 0x84, 0x84, 0x71, 0x1b, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x51, 0x5b, 0xd7, 0x55, 0x53, 0x5b, 0xff, 0x57, 0x54, 0x5c, 0xff, 0x59, 0x56, 0x5e, 0xff, 0x5b, 0x58, 0x60, 0xff, 0x5c, 0x59, 0x61, 0xff, 0x5d, 0x5a, 0x62, 0xff, 0x5d, 0x5a, 0x62, 0xff, 0x5e, 0x5c, 0x62, 0xff, 0x5d, 0x5c, 0x61, 0xff, 0x5d, 0x5b, 0x61, 0xff, 0x5d, 0x5b, 0x61, 0xff, 0x5e, 0x5c, 0x62, 0xff, 0x5e, 0x5c, 0x62, 0xff, 0x5b, 0x59, 0x62, 0xff, 0x59, 0x56, 0x61, 0xff, 0x5b, 0x57, 0x61, 0xff, 0x5e, 0x5b, 0x64, 0xff, 0x65, 0x5f, 0x69, 0xff, 0x6a, 0x64, 0x6f, 0xff, 0x6c, 0x68, 0x71, 0xff, 0x6e, 0x6c, 0x75, 0xff, 0x72, 0x6f, 0x78, 0xff, 0x73, 0x6e, 0x7a, 0xff, 0x74, 0x70, 0x7b, 0xff, 0x77, 0x73, 0x7f, 0xff, 0x7b, 0x74, 0x80, 0xff, 0x6d, 0x69, 0x73, 0xff, 0x77, 0x7c, 0x85, 0xff, 0xdb, 0xe8, 0xf5, 0xff, 0xd5, 0xea, 0xfe, 0xff, 0xc4, 0xdb, 0xf7, 0xff, 0x8e, 0xa9, 0xd4, 0xff, 0x33, 0x4f, 0x80, 0xff, 0x39, 0x55, 0x82, 0xff, 0x53, 0x72, 0x99, 0xff, 0x5e, 0x79, 0xa6, 0xff, 0x78, 0x8c, 0xc9, 0xff, 0x56, 0x70, 0xe1, 0xff, 0x39, 0x3c, 0xaa, 0xff, 0xe2, 0xd7, 0xe4, 0xff, 0xfd, 0xff, 0xf7, 0xff, 0xa6, 0xd4, 0xf0, 0xff, 0x40, 0x7b, 0xcc, 0xff, 0x00, 0x1e, 0x8d, 0xff, 0x00, 0x00, 0x43, 0xff, 0x00, 0x00, 0x08, 0xff, 0x00, 0x00, 0x0c, 0xff, 0x00, 0x00, 0x0d, 0xff, 0x05, 0x01, 0x03, 0xff, 0x03, 0x03, 0x0a, 0xff, 0x00, 0x01, 0x0b, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x05, 0x12, 0xff, 0x00, 0x03, 0x09, 0xff, 0x00, 0x00, 0x0e, 0xff, 0x1e, 0x20, 0x49, 0xff, 0x29, 0x28, 0x66, 0xff, 0x2d, 0x2b, 0x6d, 0xff, 0x38, 0x41, 0x70, 0xff, 0x29, 0x36, 0x60, 0xff, 0x29, 0x35, 0x65, 0xff, 0x37, 0x49, 0x6d, 0xff, 0x21, 0x38, 0x52, 0xff, 0x38, 0x4f, 0x6f, 0xff, 0x62, 0x7c, 0x9f, 0xff, 0x3d, 0x54, 0x7c, 0xff, 0x2c, 0x3c, 0x67, 0xff, 0x25, 0x33, 0x53, 0xff, 0x13, 0x20, 0x37, 0xff, 0x15, 0x20, 0x3b, 0xff, 0x28, 0x36, 0x54, 0xff, 0x29, 0x3b, 0x58, 0xff, 0x31, 0x41, 0x61, 0xff, 0x34, 0x43, 0x63, 0xff, 0x26, 0x35, 0x56, 0xff, 0x34, 0x43, 0x67, 0xff, 0x3b, 0x48, 0x6c, 0xff, 0x28, 0x34, 0x58, 0xff, 0x26, 0x31, 0x57, 0xff, 0x31, 0x3e, 0x64, 0xff, 0x33, 0x40, 0x66, 0xff, 0x2b, 0x36, 0x5f, 0xff, 0x2c, 0x37, 0x5c, 0xff, 0x1a, 0x27, 0x4b, 0xff, 0x29, 0x3a, 0x64, 0xff, 0x3b, 0x4d, 0x80, 0xff, 0x44, 0x57, 0x90, 0xff, 0x36, 0x47, 0x82, 0xff, 0x42, 0x53, 0x9b, 0xff, 0x64, 0x72, 0xc0, 0xff, 0x66, 0x71, 0xb7, 0xff, 0x5d, 0x6c, 0xb6, 0xff, 0x57, 0x69, 0xb6, 0xff, 0x65, 0x73, 0xb9, 0xff, 0x65, 0x67, 0xab, 0xff, 0x30, 0x2f, 0x58, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x06, 0x04, 0x00, 0xff, 0x00, 0x00, 0x07, 0xff, 0x30, 0x38, 0x40, 0xff, 0xa5, 0x9b, 0x8e, 0xff, 0xc3, 0xaa, 0x98, 0xff, 0xc0, 0xa5, 0x89, 0xff, 0xb9, 0xa3, 0x86, 0xff, 0xbe, 0xa8, 0x8d, 0xff, 0xc3, 0xa9, 0x8e, 0xff, 0xc2, 0xa9, 0x8c, 0xff, 0xbe, 0xa9, 0x8e, 0xff, 0xba, 0xa9, 0x8f, 0xff, 0xbb, 0xab, 0x95, 0xff, 0xbc, 0xaa, 0x96, 0xff, 0xb6, 0xa5, 0x8e, 0xff, 0xb1, 0xa2, 0x8b, 0xff, 0xab, 0xa0, 0x8f, 0xff, 0xa4, 0x9d, 0x8e, 0xff, 0x9d, 0x9b, 0x8e, 0xff, 0x9c, 0x9a, 0x8c, 0xff, 0x98, 0x96, 0x87, 0xff, 0x89, 0x88, 0x7a, 0xd7, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x56, 0x54, 0x59, 0x8e, 0x56, 0x54, 0x59, 0xff, 0x58, 0x53, 0x58, 0xff, 0x59, 0x54, 0x59, 0xff, 0x5d, 0x57, 0x5d, 0xff, 0x5f, 0x5a, 0x5f, 0xff, 0x61, 0x5c, 0x61, 0xff, 0x64, 0x5e, 0x64, 0xff, 0x62, 0x60, 0x64, 0xff, 0x60, 0x60, 0x63, 0xff, 0x61, 0x60, 0x63, 0xff, 0x60, 0x5f, 0x62, 0xff, 0x5f, 0x5e, 0x61, 0xff, 0x5f, 0x5e, 0x61, 0xff, 0x5f, 0x5d, 0x62, 0xff, 0x5a, 0x59, 0x5f, 0xff, 0x5a, 0x57, 0x5e, 0xff, 0x5d, 0x59, 0x60, 0xff, 0x61, 0x5b, 0x65, 0xff, 0x68, 0x5f, 0x6b, 0xff, 0x6a, 0x65, 0x6d, 0xff, 0x6d, 0x6a, 0x71, 0xff, 0x71, 0x6c, 0x75, 0xff, 0x72, 0x6d, 0x77, 0xff, 0x73, 0x6e, 0x79, 0xff, 0x77, 0x72, 0x7c, 0xff, 0x79, 0x71, 0x7e, 0xff, 0x6d, 0x6a, 0x75, 0xff, 0x69, 0x6e, 0x77, 0xff, 0xc6, 0xd3, 0xdf, 0xff, 0xd5, 0xe8, 0xfc, 0xff, 0xc8, 0xde, 0xf7, 0xff, 0xba, 0xd3, 0xfc, 0xff, 0x61, 0x7b, 0xad, 0xff, 0x2c, 0x4c, 0x78, 0xff, 0x5d, 0x7f, 0xa7, 0xff, 0x5d, 0x7b, 0xaa, 0xff, 0x4f, 0x64, 0xad, 0xff, 0x00, 0x00, 0x5e, 0xff, 0x00, 0x00, 0x0c, 0xff, 0xd4, 0xd8, 0xc5, 0xff, 0xde, 0xff, 0xff, 0xff, 0x47, 0x88, 0xf5, 0xff, 0x24, 0x5c, 0xe4, 0xff, 0x24, 0x55, 0xb7, 0xff, 0x08, 0x18, 0x5d, 0xff, 0x00, 0x00, 0x0d, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x02, 0x03, 0x0e, 0xff, 0x22, 0x22, 0x34, 0xff, 0x2a, 0x2d, 0x49, 0xff, 0x31, 0x2c, 0x5b, 0xff, 0x41, 0x3b, 0x6f, 0xff, 0x45, 0x49, 0x78, 0xff, 0x39, 0x40, 0x74, 0xff, 0x39, 0x42, 0x79, 0xff, 0x39, 0x49, 0x76, 0xff, 0x2a, 0x3b, 0x66, 0xff, 0x2c, 0x3a, 0x6b, 0xff, 0x38, 0x48, 0x76, 0xff, 0x34, 0x48, 0x69, 0xff, 0x2c, 0x44, 0x5d, 0xff, 0x3f, 0x54, 0x76, 0xff, 0x4e, 0x64, 0x87, 0xff, 0x30, 0x45, 0x69, 0xff, 0x26, 0x33, 0x5d, 0xff, 0x26, 0x32, 0x50, 0xff, 0x16, 0x24, 0x39, 0xff, 0x1d, 0x29, 0x42, 0xff, 0x2f, 0x3c, 0x5a, 0xff, 0x2e, 0x3d, 0x5b, 0xff, 0x2a, 0x39, 0x58, 0xff, 0x2e, 0x3b, 0x5c, 0xff, 0x2c, 0x39, 0x5b, 0xff, 0x34, 0x41, 0x65, 0xff, 0x31, 0x3e, 0x62, 0xff, 0x2e, 0x39, 0x5e, 0xff, 0x33, 0x3d, 0x63, 0xff, 0x39, 0x45, 0x6b, 0xff, 0x28, 0x35, 0x5b, 0xff, 0x25, 0x31, 0x5a, 0xff, 0x2b, 0x37, 0x5b, 0xff, 0x25, 0x33, 0x54, 0xff, 0x27, 0x38, 0x62, 0xff, 0x3f, 0x51, 0x85, 0xff, 0x49, 0x5b, 0x95, 0xff, 0x30, 0x42, 0x7c, 0xff, 0x26, 0x38, 0x77, 0xff, 0x3d, 0x4d, 0x8f, 0xff, 0x51, 0x60, 0x9d, 0xff, 0x48, 0x5c, 0x9c, 0xff, 0x4a, 0x5f, 0x9d, 0xff, 0x41, 0x4c, 0x84, 0xff, 0x07, 0x0d, 0x1e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x00, 0x00, 0xff, 0x0f, 0x02, 0x0d, 0xff, 0x02, 0x00, 0x06, 0xff, 0x07, 0x13, 0x10, 0xff, 0x7d, 0x82, 0x6e, 0xff, 0xc6, 0xae, 0x95, 0xff, 0xc4, 0xa5, 0x8c, 0xff, 0xc1, 0xa5, 0x8a, 0xff, 0xb7, 0xa3, 0x86, 0xff, 0xbd, 0xa6, 0x8b, 0xff, 0xc4, 0xa9, 0x8c, 0xff, 0xc1, 0xa9, 0x8a, 0xff, 0xbe, 0xa9, 0x8d, 0xff, 0xbc, 0xaa, 0x8f, 0xff, 0xbc, 0xab, 0x93, 0xff, 0xbd, 0xaa, 0x96, 0xff, 0xb8, 0xa5, 0x8f, 0xff, 0xb3, 0xa1, 0x8a, 0xff, 0xac, 0x9f, 0x8d, 0xff, 0xa4, 0x9d, 0x8d, 0xff, 0xa0, 0x9a, 0x8c, 0xff, 0x9d, 0x99, 0x8a, 0xff, 0x97, 0x95, 0x86, 0xff, 0x88, 0x88, 0x7a, 0x8e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5e, 0x5e, 0x5e, 0x46, 0x5c, 0x5b, 0x5a, 0xff, 0x5d, 0x58, 0x59, 0xff, 0x5d, 0x57, 0x58, 0xff, 0x60, 0x5a, 0x5b, 0xff, 0x63, 0x5d, 0x5e, 0xff, 0x67, 0x61, 0x62, 0xff, 0x6c, 0x67, 0x67, 0xff, 0x6a, 0x69, 0x67, 0xff, 0x6a, 0x6c, 0x6a, 0xff, 0x6c, 0x6c, 0x6a, 0xff, 0x6a, 0x6b, 0x69, 0xff, 0x69, 0x69, 0x67, 0xff, 0x67, 0x68, 0x66, 0xff, 0x65, 0x66, 0x64, 0xff, 0x62, 0x61, 0x61, 0xff, 0x5f, 0x5c, 0x5e, 0xff, 0x5e, 0x5b, 0x5e, 0xff, 0x61, 0x5b, 0x61, 0xff, 0x64, 0x5d, 0x64, 0xff, 0x66, 0x61, 0x67, 0xff, 0x6a, 0x67, 0x6c, 0xff, 0x6e, 0x6a, 0x71, 0xff, 0x70, 0x6a, 0x73, 0xff, 0x72, 0x6c, 0x75, 0xff, 0x75, 0x6e, 0x79, 0xff, 0x76, 0x70, 0x7b, 0xff, 0x72, 0x70, 0x79, 0xff, 0x59, 0x5f, 0x64, 0xff, 0xab, 0xb8, 0xbf, 0xff, 0xdf, 0xf2, 0xff, 0xff, 0xc5, 0xdb, 0xef, 0xff, 0xcd, 0xe6, 0xff, 0xff, 0x9d, 0xb8, 0xe3, 0xff, 0x61, 0x7d, 0xab, 0xff, 0x6d, 0x8d, 0xb5, 0xff, 0x87, 0xa2, 0xc9, 0xff, 0x78, 0x8c, 0xbd, 0xff, 0x85, 0x84, 0xbc, 0xff, 0x47, 0x36, 0x7b, 0xff, 0x5a, 0x5d, 0x99, 0xff, 0xa3, 0xe5, 0xfd, 0xff, 0x43, 0x95, 0xd7, 0xff, 0x3c, 0x70, 0xc3, 0xff, 0x27, 0x42, 0x9b, 0xff, 0x00, 0x00, 0x37, 0xff, 0x08, 0x08, 0x1b, 0xff, 0x25, 0x27, 0x28, 0xff, 0x2e, 0x34, 0x34, 0xff, 0x4a, 0x50, 0x51, 0xff, 0x75, 0x72, 0x74, 0xff, 0x8d, 0x82, 0x9a, 0xff, 0xa1, 0x9c, 0xc1, 0xff, 0xa7, 0xa8, 0xe5, 0xff, 0x8a, 0x8e, 0xd7, 0xff, 0x5e, 0x67, 0xa9, 0xff, 0x54, 0x57, 0xa3, 0xff, 0x46, 0x49, 0x92, 0xff, 0x3d, 0x47, 0x82, 0xff, 0x42, 0x4d, 0x89, 0xff, 0x42, 0x51, 0x8a, 0xff, 0x2d, 0x43, 0x6b, 0xff, 0x29, 0x3c, 0x65, 0xff, 0x3e, 0x4c, 0x7e, 0xff, 0x3d, 0x4d, 0x79, 0xff, 0x1c, 0x31, 0x4e, 0xff, 0x28, 0x3d, 0x54, 0xff, 0x3e, 0x4e, 0x70, 0xff, 0x34, 0x47, 0x68, 0xff, 0x30, 0x43, 0x64, 0xff, 0x2b, 0x37, 0x5e, 0xff, 0x21, 0x2b, 0x49, 0xff, 0x18, 0x25, 0x39, 0xff, 0x29, 0x35, 0x4e, 0xff, 0x2a, 0x36, 0x54, 0xff, 0x20, 0x2e, 0x4c, 0xff, 0x2c, 0x39, 0x59, 0xff, 0x27, 0x33, 0x55, 0xff, 0x34, 0x3f, 0x62, 0xff, 0x35, 0x41, 0x65, 0xff, 0x29, 0x35, 0x59, 0xff, 0x28, 0x33, 0x58, 0xff, 0x40, 0x4a, 0x70, 0xff, 0x2f, 0x3b, 0x61, 0xff, 0x16, 0x24, 0x4a, 0xff, 0x2a, 0x36, 0x60, 0xff, 0x29, 0x36, 0x57, 0xff, 0x20, 0x2f, 0x4c, 0xff, 0x30, 0x41, 0x6b, 0xff, 0x41, 0x52, 0x88, 0xff, 0x44, 0x56, 0x90, 0xff, 0x32, 0x44, 0x7e, 0xff, 0x23, 0x36, 0x6e, 0xff, 0x16, 0x28, 0x5f, 0xff, 0x18, 0x2b, 0x60, 0xff, 0x19, 0x33, 0x6a, 0xff, 0x24, 0x3c, 0x6c, 0xff, 0x18, 0x22, 0x3f, 0xff, 0x00, 0x00, 0x02, 0xff, 0x04, 0x02, 0x02, 0xff, 0x0c, 0x03, 0x04, 0xff, 0x09, 0x03, 0x0b, 0xff, 0x00, 0x00, 0x00, 0xff, 0x32, 0x35, 0x3b, 0xff, 0xb3, 0xa7, 0x93, 0xff, 0xc6, 0xaa, 0x88, 0xff, 0xc3, 0xa2, 0x89, 0xff, 0xbe, 0xa2, 0x8a, 0xff, 0xb4, 0xa1, 0x86, 0xff, 0xbf, 0xa7, 0x89, 0xff, 0xc6, 0xa8, 0x8a, 0xff, 0xc3, 0xa8, 0x89, 0xff, 0xc0, 0xaa, 0x8d, 0xff, 0xbd, 0xac, 0x91, 0xff, 0xbe, 0xac, 0x94, 0xff, 0xbf, 0xac, 0x96, 0xff, 0xb7, 0xa4, 0x8d, 0xff, 0xaf, 0x9f, 0x85, 0xff, 0xa9, 0x9e, 0x89, 0xff, 0xa2, 0x9b, 0x8d, 0xff, 0x9d, 0x98, 0x8a, 0xff, 0x98, 0x96, 0x87, 0xff, 0x92, 0x90, 0x81, 0xff, 0x86, 0x83, 0x74, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x6d, 0x6d, 0x07, 0x61, 0x61, 0x5c, 0xef, 0x64, 0x5f, 0x5b, 0xff, 0x61, 0x5c, 0x59, 0xff, 0x61, 0x5d, 0x5a, 0xff, 0x64, 0x60, 0x5d, 0xff, 0x69, 0x64, 0x61, 0xff, 0x6e, 0x68, 0x65, 0xff, 0x6c, 0x6d, 0x66, 0xff, 0x6e, 0x71, 0x6a, 0xff, 0x73, 0x74, 0x6d, 0xff, 0x73, 0x74, 0x6d, 0xff, 0x71, 0x73, 0x6c, 0xff, 0x6f, 0x72, 0x6a, 0xff, 0x6e, 0x6e, 0x68, 0xff, 0x6c, 0x6a, 0x66, 0xff, 0x66, 0x65, 0x62, 0xff, 0x63, 0x5f, 0x5f, 0xff, 0x63, 0x5d, 0x5f, 0xff, 0x62, 0x5d, 0x60, 0xff, 0x62, 0x5e, 0x64, 0xff, 0x66, 0x62, 0x69, 0xff, 0x68, 0x64, 0x6a, 0xff, 0x6c, 0x67, 0x70, 0xff, 0x6f, 0x6a, 0x73, 0xff, 0x72, 0x6b, 0x76, 0xff, 0x74, 0x6d, 0x77, 0xff, 0x70, 0x70, 0x75, 0xff, 0x57, 0x5f, 0x60, 0xff, 0x98, 0xa7, 0xaa, 0xff, 0xe6, 0xfa, 0xff, 0xff, 0xc7, 0xdd, 0xec, 0xff, 0xce, 0xe8, 0xfa, 0xff, 0xb3, 0xcf, 0xf0, 0xff, 0x91, 0xab, 0xda, 0xff, 0x82, 0x9c, 0xc3, 0xff, 0x9b, 0xb0, 0xca, 0xff, 0xcb, 0xdb, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xee, 0xff, 0xff, 0x97, 0x9e, 0xd8, 0xff, 0x91, 0xb1, 0xe0, 0xff, 0x53, 0x6c, 0xa1, 0xff, 0x51, 0x55, 0x8f, 0xff, 0x84, 0x88, 0xb5, 0xff, 0xab, 0xa9, 0xb7, 0xff, 0xe1, 0xde, 0xd9, 0xff, 0xfc, 0xf9, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0xf4, 0xff, 0xff, 0xbd, 0xbe, 0xff, 0xff, 0x8e, 0x8d, 0xff, 0xff, 0x52, 0x54, 0xbd, 0xff, 0x33, 0x38, 0x7e, 0xff, 0x3c, 0x45, 0x84, 0xff, 0x45, 0x53, 0x8e, 0xff, 0x4c, 0x5a, 0x8e, 0xff, 0x50, 0x5b, 0x95, 0xff, 0x44, 0x4b, 0x88, 0xff, 0x2f, 0x37, 0x6a, 0xff, 0x3c, 0x49, 0x76, 0xff, 0x42, 0x51, 0x80, 0xff, 0x21, 0x30, 0x5b, 0xff, 0x15, 0x28, 0x42, 0xff, 0x30, 0x42, 0x55, 0xff, 0x39, 0x45, 0x64, 0xff, 0x32, 0x44, 0x62, 0xff, 0x2b, 0x3f, 0x5e, 0xff, 0x32, 0x3a, 0x62, 0xff, 0x1a, 0x24, 0x40, 0xff, 0x13, 0x20, 0x34, 0xff, 0x20, 0x2d, 0x45, 0xff, 0x24, 0x31, 0x4e, 0xff, 0x29, 0x37, 0x54, 0xff, 0x3c, 0x49, 0x69, 0xff, 0x31, 0x3d, 0x5e, 0xff, 0x32, 0x3e, 0x60, 0xff, 0x33, 0x3f, 0x64, 0xff, 0x27, 0x33, 0x57, 0xff, 0x25, 0x31, 0x54, 0xff, 0x33, 0x3c, 0x62, 0xff, 0x34, 0x40, 0x66, 0xff, 0x26, 0x34, 0x5a, 0xff, 0x2b, 0x36, 0x62, 0xff, 0x2c, 0x39, 0x5a, 0xff, 0x1a, 0x2a, 0x46, 0xff, 0x31, 0x42, 0x6d, 0xff, 0x41, 0x52, 0x88, 0xff, 0x41, 0x54, 0x8e, 0xff, 0x2c, 0x40, 0x78, 0xff, 0x1b, 0x2d, 0x65, 0xff, 0x1d, 0x2a, 0x63, 0xff, 0x1d, 0x31, 0x6b, 0xff, 0x24, 0x3f, 0x79, 0xff, 0x2c, 0x45, 0x72, 0xff, 0x19, 0x20, 0x35, 0xff, 0x00, 0x00, 0x05, 0xff, 0x05, 0x03, 0x04, 0xff, 0x09, 0x05, 0x05, 0xff, 0x00, 0x05, 0x08, 0xff, 0x00, 0x01, 0x01, 0xff, 0x67, 0x62, 0x5f, 0xff, 0xc3, 0xaf, 0x98, 0xff, 0xc0, 0xa5, 0x81, 0xff, 0xc4, 0xa3, 0x8b, 0xff, 0xba, 0xa1, 0x8b, 0xff, 0xb2, 0xa0, 0x85, 0xff, 0xc2, 0xa7, 0x87, 0xff, 0xca, 0xaa, 0x89, 0xff, 0xc4, 0xa9, 0x8b, 0xff, 0xc0, 0xac, 0x8d, 0xff, 0xbf, 0xad, 0x91, 0xff, 0xbf, 0xae, 0x94, 0xff, 0xc1, 0xab, 0x95, 0xff, 0xb7, 0xa4, 0x8b, 0xff, 0xad, 0xa1, 0x85, 0xff, 0xa8, 0x9f, 0x8b, 0xff, 0xa1, 0x9c, 0x8b, 0xff, 0x97, 0x96, 0x87, 0xff, 0x92, 0x93, 0x81, 0xff, 0x8d, 0x8c, 0x7b, 0xef, 0x91, 0x6d, 0x6d, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 0x67, 0x5a, 0xa0, 0x66, 0x64, 0x59, 0xff, 0x67, 0x63, 0x58, 0xff, 0x68, 0x62, 0x55, 0xff, 0x69, 0x63, 0x56, 0xff, 0x69, 0x66, 0x5a, 0xff, 0x6a, 0x6a, 0x5f, 0xff, 0x6c, 0x6f, 0x64, 0xff, 0x6e, 0x73, 0x68, 0xff, 0x70, 0x78, 0x6b, 0xff, 0x71, 0x79, 0x6b, 0xff, 0x71, 0x79, 0x6b, 0xff, 0x71, 0x78, 0x6b, 0xff, 0x6f, 0x75, 0x6a, 0xff, 0x6d, 0x70, 0x68, 0xff, 0x6a, 0x6c, 0x63, 0xff, 0x68, 0x66, 0x61, 0xff, 0x65, 0x60, 0x60, 0xff, 0x65, 0x5d, 0x60, 0xff, 0x63, 0x5d, 0x60, 0xff, 0x62, 0x5d, 0x61, 0xff, 0x63, 0x5f, 0x65, 0xff, 0x67, 0x63, 0x6a, 0xff, 0x69, 0x67, 0x6d, 0xff, 0x6c, 0x6c, 0x70, 0xff, 0x6f, 0x70, 0x72, 0xff, 0x70, 0x71, 0x74, 0xff, 0x66, 0x68, 0x6a, 0xff, 0x73, 0x7b, 0x7b, 0xff, 0xd1, 0xe1, 0xea, 0xff, 0xd1, 0xe5, 0xfd, 0xff, 0xce, 0xe4, 0xfc, 0xff, 0xc8, 0xde, 0xf8, 0xff, 0xa2, 0xba, 0xe1, 0xff, 0x8e, 0xaa, 0xd4, 0xff, 0xa7, 0xc0, 0xdb, 0xff, 0xe0, 0xec, 0xf3, 0xff, 0xf4, 0xff, 0xfd, 0xff, 0xe7, 0xf1, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xf0, 0xf8, 0xff, 0xf8, 0xe6, 0xed, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0xf0, 0xff, 0xff, 0xc6, 0xbe, 0xfe, 0xff, 0x92, 0x8e, 0xf7, 0xff, 0x59, 0x5c, 0xd2, 0xff, 0x36, 0x35, 0xad, 0xff, 0x25, 0x25, 0x96, 0xff, 0x34, 0x37, 0x91, 0xff, 0x57, 0x5c, 0x9d, 0xff, 0x57, 0x5f, 0x9c, 0xff, 0x56, 0x62, 0x9b, 0xff, 0x50, 0x59, 0x8d, 0xff, 0x3b, 0x44, 0x75, 0xff, 0x2b, 0x35, 0x64, 0xff, 0x2d, 0x3c, 0x64, 0xff, 0x39, 0x4a, 0x77, 0xff, 0x30, 0x40, 0x6e, 0xff, 0x12, 0x22, 0x41, 0xff, 0x23, 0x34, 0x49, 0xff, 0x33, 0x42, 0x59, 0xff, 0x40, 0x4b, 0x70, 0xff, 0x3c, 0x4a, 0x6d, 0xff, 0x27, 0x35, 0x53, 0xff, 0x23, 0x2d, 0x4b, 0xff, 0x20, 0x2b, 0x41, 0xff, 0x26, 0x31, 0x48, 0xff, 0x1b, 0x25, 0x42, 0xff, 0x1c, 0x28, 0x49, 0xff, 0x28, 0x34, 0x55, 0xff, 0x39, 0x45, 0x66, 0xff, 0x3f, 0x4b, 0x6c, 0xff, 0x31, 0x3c, 0x5e, 0xff, 0x36, 0x40, 0x62, 0xff, 0x31, 0x3e, 0x63, 0xff, 0x29, 0x38, 0x5e, 0xff, 0x27, 0x35, 0x5b, 0xff, 0x30, 0x3e, 0x63, 0xff, 0x31, 0x3f, 0x63, 0xff, 0x2e, 0x3c, 0x60, 0xff, 0x29, 0x35, 0x55, 0xff, 0x1c, 0x27, 0x48, 0xff, 0x2c, 0x3a, 0x67, 0xff, 0x41, 0x51, 0x89, 0xff, 0x3c, 0x4d, 0x8a, 0xff, 0x25, 0x39, 0x72, 0xff, 0x1c, 0x2c, 0x68, 0xff, 0x24, 0x32, 0x6c, 0xff, 0x29, 0x3c, 0x71, 0xff, 0x2a, 0x40, 0x7c, 0xff, 0x2c, 0x3e, 0x74, 0xff, 0x12, 0x15, 0x2e, 0xff, 0x00, 0x00, 0x03, 0xff, 0x05, 0x06, 0x01, 0xff, 0x08, 0x05, 0x03, 0xff, 0x00, 0x00, 0x0a, 0xff, 0x09, 0x19, 0x29, 0xff, 0x97, 0x8c, 0x7e, 0xff, 0xc7, 0xab, 0x89, 0xff, 0xba, 0xa2, 0x84, 0xff, 0xbf, 0xa4, 0x8c, 0xff, 0xb8, 0x9f, 0x88, 0xff, 0xb3, 0x9d, 0x83, 0xff, 0xbd, 0xa6, 0x88, 0xff, 0xc4, 0xaa, 0x8a, 0xff, 0xc4, 0xaa, 0x8c, 0xff, 0xbf, 0xaa, 0x8d, 0xff, 0xbe, 0xaa, 0x8f, 0xff, 0xbd, 0xac, 0x92, 0xff, 0xba, 0xa8, 0x92, 0xff, 0xb0, 0xa1, 0x8b, 0xff, 0xac, 0xa0, 0x87, 0xff, 0xa8, 0x9f, 0x8a, 0xff, 0x9f, 0x9a, 0x88, 0xff, 0x93, 0x94, 0x83, 0xff, 0x8f, 0x8f, 0x7f, 0xff, 0x8a, 0x89, 0x7a, 0xa0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x67, 0x6a, 0x59, 0x4a, 0x67, 0x6a, 0x59, 0xff, 0x6c, 0x68, 0x57, 0xff, 0x6c, 0x66, 0x52, 0xff, 0x6b, 0x64, 0x50, 0xff, 0x69, 0x68, 0x55, 0xff, 0x69, 0x6d, 0x5c, 0xff, 0x6c, 0x71, 0x61, 0xff, 0x6e, 0x76, 0x66, 0xff, 0x6e, 0x7a, 0x69, 0xff, 0x6f, 0x7e, 0x6b, 0xff, 0x71, 0x80, 0x6c, 0xff, 0x72, 0x7e, 0x6c, 0xff, 0x72, 0x7b, 0x6e, 0xff, 0x70, 0x77, 0x6b, 0xff, 0x70, 0x73, 0x66, 0xff, 0x6d, 0x6c, 0x62, 0xff, 0x69, 0x64, 0x60, 0xff, 0x67, 0x5f, 0x60, 0xff, 0x64, 0x5c, 0x5d, 0xff, 0x61, 0x5b, 0x5d, 0xff, 0x63, 0x5c, 0x63, 0xff, 0x64, 0x5f, 0x66, 0xff, 0x65, 0x65, 0x68, 0xff, 0x69, 0x6c, 0x6c, 0xff, 0x69, 0x70, 0x6e, 0xff, 0x6f, 0x72, 0x74, 0xff, 0x70, 0x6f, 0x72, 0xff, 0x68, 0x6f, 0x6a, 0xff, 0xba, 0xc9, 0xcf, 0xff, 0xcd, 0xdf, 0xfd, 0xff, 0xc7, 0xda, 0xf8, 0xff, 0xca, 0xdc, 0xf3, 0xff, 0xac, 0xc3, 0xe2, 0xff, 0x93, 0xb2, 0xdd, 0xff, 0xa6, 0xc0, 0xe1, 0xff, 0xe0, 0xea, 0xf3, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xeb, 0xee, 0xf8, 0xff, 0xe4, 0xe6, 0xf2, 0xff, 0xee, 0xef, 0xf7, 0xff, 0xf8, 0xf9, 0xff, 0xff, 0xfe, 0xfc, 0xff, 0xff, 0xfc, 0xfc, 0xfa, 0xff, 0xf9, 0xfa, 0xfb, 0xff, 0xe7, 0xec, 0xfa, 0xff, 0xc5, 0xcd, 0xf2, 0xff, 0x94, 0x99, 0xe5, 0xff, 0x69, 0x67, 0xe3, 0xff, 0x51, 0x4d, 0xcd, 0xff, 0x47, 0x46, 0xb2, 0xff, 0x4f, 0x52, 0xaa, 0xff, 0x53, 0x5b, 0x9d, 0xff, 0x5f, 0x69, 0x9e, 0xff, 0x57, 0x64, 0x94, 0xff, 0x52, 0x60, 0x8f, 0xff, 0x4d, 0x58, 0x88, 0xff, 0x3f, 0x46, 0x78, 0xff, 0x2d, 0x35, 0x65, 0xff, 0x24, 0x31, 0x60, 0xff, 0x2f, 0x42, 0x71, 0xff, 0x2b, 0x3d, 0x6e, 0xff, 0x1b, 0x2c, 0x52, 0xff, 0x1d, 0x2d, 0x44, 0xff, 0x39, 0x47, 0x5b, 0xff, 0x2a, 0x36, 0x52, 0xff, 0x2b, 0x35, 0x60, 0xff, 0x27, 0x31, 0x59, 0xff, 0x28, 0x33, 0x4e, 0xff, 0x22, 0x2d, 0x44, 0xff, 0x1a, 0x26, 0x38, 0xff, 0x26, 0x30, 0x49, 0xff, 0x28, 0x31, 0x51, 0xff, 0x2b, 0x38, 0x5a, 0xff, 0x2d, 0x3a, 0x5c, 0xff, 0x27, 0x34, 0x55, 0xff, 0x2a, 0x35, 0x57, 0xff, 0x18, 0x22, 0x44, 0xff, 0x2d, 0x36, 0x56, 0xff, 0x40, 0x4e, 0x72, 0xff, 0x36, 0x46, 0x6f, 0xff, 0x34, 0x43, 0x6a, 0xff, 0x27, 0x35, 0x5a, 0xff, 0x30, 0x3e, 0x61, 0xff, 0x28, 0x35, 0x57, 0xff, 0x21, 0x2d, 0x4b, 0xff, 0x22, 0x2c, 0x4e, 0xff, 0x35, 0x42, 0x70, 0xff, 0x3d, 0x4e, 0x84, 0xff, 0x37, 0x48, 0x83, 0xff, 0x23, 0x37, 0x6f, 0xff, 0x1b, 0x2c, 0x69, 0xff, 0x24, 0x35, 0x6e, 0xff, 0x2d, 0x41, 0x71, 0xff, 0x2f, 0x43, 0x7e, 0xff, 0x2a, 0x3b, 0x73, 0xff, 0x0b, 0x10, 0x26, 0xff, 0x00, 0x00, 0x02, 0xff, 0x05, 0x05, 0x04, 0xff, 0x08, 0x07, 0x00, 0xff, 0x00, 0x03, 0x0d, 0xff, 0x3b, 0x49, 0x5c, 0xff, 0xb2, 0xa2, 0x93, 0xff, 0xc4, 0xa5, 0x81, 0xff, 0xba, 0xa2, 0x85, 0xff, 0xbd, 0xa3, 0x8a, 0xff, 0xb9, 0x9e, 0x86, 0xff, 0xb6, 0x9c, 0x82, 0xff, 0xbc, 0xa6, 0x88, 0xff, 0xc1, 0xa9, 0x89, 0xff, 0xc2, 0xa9, 0x8b, 0xff, 0xbf, 0xa9, 0x8d, 0xff, 0xbd, 0xa8, 0x8e, 0xff, 0xbb, 0xaa, 0x90, 0xff, 0xb6, 0xa8, 0x8f, 0xff, 0xb0, 0xa0, 0x8b, 0xff, 0xac, 0x9c, 0x88, 0xff, 0xa6, 0x9c, 0x88, 0xff, 0x9c, 0x98, 0x85, 0xff, 0x93, 0x90, 0x81, 0xff, 0x8e, 0x8c, 0x7f, 0xff, 0x86, 0x86, 0x78, 0x4a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x7f, 0x3f, 0x04, 0x6b, 0x6e, 0x5a, 0xe7, 0x6e, 0x6c, 0x58, 0xff, 0x6e, 0x69, 0x51, 0xff, 0x6c, 0x67, 0x4f, 0xff, 0x6b, 0x6a, 0x54, 0xff, 0x6a, 0x6f, 0x5a, 0xff, 0x6c, 0x72, 0x5f, 0xff, 0x6f, 0x78, 0x64, 0xff, 0x71, 0x7e, 0x69, 0xff, 0x72, 0x82, 0x6c, 0xff, 0x73, 0x84, 0x6d, 0xff, 0x74, 0x83, 0x6e, 0xff, 0x77, 0x81, 0x6f, 0xff, 0x75, 0x7d, 0x6a, 0xff, 0x74, 0x79, 0x65, 0xff, 0x72, 0x73, 0x62, 0xff, 0x6f, 0x6c, 0x61, 0xff, 0x69, 0x63, 0x5b, 0xff, 0x65, 0x5c, 0x5b, 0xff, 0x62, 0x5a, 0x5c, 0xff, 0x62, 0x5c, 0x5e, 0xff, 0x61, 0x5d, 0x63, 0xff, 0x63, 0x63, 0x67, 0xff, 0x68, 0x6a, 0x6b, 0xff, 0x68, 0x6f, 0x6e, 0xff, 0x6d, 0x73, 0x74, 0xff, 0x72, 0x78, 0x78, 0xff, 0x60, 0x6c, 0x66, 0xff, 0x92, 0xa6, 0xa6, 0xff, 0xde, 0xf2, 0xff, 0xff, 0xce, 0xe1, 0xf9, 0xff, 0xd0, 0xe1, 0xf6, 0xff, 0xb8, 0xcd, 0xea, 0xff, 0x99, 0xb5, 0xdb, 0xff, 0xad, 0xc6, 0xe3, 0xff, 0xe7, 0xf3, 0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xec, 0xf0, 0xf8, 0xff, 0xcd, 0xd3, 0xe8, 0xff, 0xcd, 0xd2, 0xf0, 0xff, 0xd7, 0xdd, 0xfc, 0xff, 0xe2, 0xe6, 0xff, 0xff, 0xd9, 0xe2, 0xff, 0xff, 0xc1, 0xcc, 0xfb, 0xff, 0xb2, 0xb9, 0xf7, 0xff, 0x9b, 0xa5, 0xe8, 0xff, 0x8c, 0x98, 0xe2, 0xff, 0x78, 0x81, 0xdf, 0xff, 0x78, 0x7f, 0xcb, 0xff, 0x76, 0x7f, 0xb9, 0xff, 0x64, 0x6d, 0xa7, 0xff, 0x57, 0x61, 0x95, 0xff, 0x53, 0x5d, 0x8b, 0xff, 0x4c, 0x56, 0x80, 0xff, 0x36, 0x43, 0x71, 0xff, 0x2d, 0x38, 0x6b, 0xff, 0x32, 0x3c, 0x6d, 0xff, 0x2f, 0x3b, 0x69, 0xff, 0x28, 0x36, 0x63, 0xff, 0x27, 0x38, 0x63, 0xff, 0x25, 0x36, 0x5e, 0xff, 0x13, 0x24, 0x44, 0xff, 0x28, 0x37, 0x4e, 0xff, 0x2c, 0x39, 0x4e, 0xff, 0x2c, 0x38, 0x56, 0xff, 0x44, 0x4b, 0x77, 0xff, 0x1c, 0x25, 0x4c, 0xff, 0x1d, 0x28, 0x42, 0xff, 0x1b, 0x25, 0x3c, 0xff, 0x14, 0x1f, 0x34, 0xff, 0x1d, 0x28, 0x3f, 0xff, 0x28, 0x32, 0x50, 0xff, 0x2c, 0x39, 0x5a, 0xff, 0x32, 0x42, 0x65, 0xff, 0x27, 0x35, 0x57, 0xff, 0x27, 0x33, 0x56, 0xff, 0x2e, 0x3a, 0x5d, 0xff, 0x36, 0x3f, 0x62, 0xff, 0x2f, 0x3c, 0x62, 0xff, 0x2b, 0x3a, 0x62, 0xff, 0x32, 0x3f, 0x66, 0xff, 0x25, 0x32, 0x57, 0xff, 0x27, 0x32, 0x55, 0xff, 0x22, 0x2e, 0x50, 0xff, 0x1d, 0x2a, 0x49, 0xff, 0x1d, 0x2c, 0x4c, 0xff, 0x2b, 0x3d, 0x67, 0xff, 0x3b, 0x4c, 0x80, 0xff, 0x33, 0x47, 0x7e, 0xff, 0x20, 0x36, 0x6a, 0xff, 0x18, 0x2a, 0x64, 0xff, 0x20, 0x31, 0x69, 0xff, 0x2c, 0x40, 0x70, 0xff, 0x2e, 0x45, 0x7e, 0xff, 0x2d, 0x3f, 0x75, 0xff, 0x0e, 0x15, 0x2a, 0xff, 0x00, 0x00, 0x03, 0xff, 0x05, 0x06, 0x06, 0xff, 0x02, 0x04, 0x01, 0xff, 0x09, 0x16, 0x1d, 0xff, 0x6e, 0x75, 0x7e, 0xff, 0xb8, 0xa5, 0x93, 0xff, 0xbb, 0x9d, 0x7b, 0xff, 0xb8, 0x9e, 0x81, 0xff, 0xba, 0xa0, 0x86, 0xff, 0xb9, 0x9e, 0x83, 0xff, 0xb9, 0x9d, 0x81, 0xff, 0xbf, 0xa5, 0x85, 0xff, 0xc0, 0xa6, 0x86, 0xff, 0xbf, 0xa6, 0x8a, 0xff, 0xbd, 0xa4, 0x89, 0xff, 0xbb, 0xa4, 0x89, 0xff, 0xbb, 0xa7, 0x8d, 0xff, 0xb9, 0xa6, 0x8d, 0xff, 0xb3, 0xa0, 0x89, 0xff, 0xad, 0x9b, 0x87, 0xff, 0xa5, 0x98, 0x86, 0xff, 0x9c, 0x96, 0x84, 0xff, 0x95, 0x90, 0x81, 0xff, 0x8f, 0x8c, 0x7d, 0xe7, 0x99, 0x99, 0x66, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6f, 0x71, 0x56, 0x87, 0x71, 0x6f, 0x53, 0xff, 0x72, 0x6d, 0x4f, 0xff, 0x70, 0x6b, 0x4c, 0xff, 0x6d, 0x6d, 0x50, 0xff, 0x6c, 0x71, 0x55, 0xff, 0x70, 0x76, 0x5b, 0xff, 0x73, 0x7c, 0x60, 0xff, 0x73, 0x82, 0x66, 0xff, 0x73, 0x85, 0x6a, 0xff, 0x74, 0x86, 0x6c, 0xff, 0x74, 0x86, 0x6d, 0xff, 0x77, 0x84, 0x6b, 0xff, 0x79, 0x81, 0x67, 0xff, 0x77, 0x7e, 0x62, 0xff, 0x76, 0x79, 0x61, 0xff, 0x73, 0x72, 0x60, 0xff, 0x6d, 0x69, 0x5b, 0xff, 0x68, 0x5e, 0x58, 0xff, 0x64, 0x5b, 0x59, 0xff, 0x61, 0x5b, 0x5c, 0xff, 0x60, 0x5c, 0x5f, 0xff, 0x62, 0x62, 0x65, 0xff, 0x67, 0x6a, 0x6b, 0xff, 0x6a, 0x72, 0x6f, 0xff, 0x6d, 0x77, 0x75, 0xff, 0x70, 0x7b, 0x7a, 0xff, 0x67, 0x76, 0x6e, 0xff, 0x6f, 0x82, 0x7c, 0xff, 0xca, 0xdd, 0xe3, 0xff, 0xd0, 0xe2, 0xf4, 0xff, 0xc9, 0xd8, 0xec, 0xff, 0xbe, 0xd1, 0xe9, 0xff, 0xa7, 0xc0, 0xe1, 0xff, 0xb4, 0xcc, 0xe7, 0xff, 0xe5, 0xf4, 0xfd, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xf3, 0xf8, 0xfc, 0xff, 0xdd, 0xe6, 0xf9, 0xff, 0xcd, 0xd7, 0xfa, 0xff, 0xbe, 0xc9, 0xf2, 0xff, 0xb3, 0xbf, 0xe8, 0xff, 0xa3, 0xae, 0xe7, 0xff, 0x91, 0x98, 0xe4, 0xff, 0x8b, 0x90, 0xe2, 0xff, 0x8a, 0x92, 0xd8, 0xff, 0x82, 0x8f, 0xc3, 0xff, 0x71, 0x81, 0xaf, 0xff, 0x6b, 0x78, 0xa4, 0xff, 0x5a, 0x65, 0x90, 0xff, 0x53, 0x5d, 0x8a, 0xff, 0x49, 0x51, 0x7f, 0xff, 0x3d, 0x44, 0x73, 0xff, 0x3a, 0x40, 0x6f, 0xff, 0x2c, 0x36, 0x66, 0xff, 0x23, 0x2f, 0x60, 0xff, 0x2f, 0x3d, 0x6b, 0xff, 0x2b, 0x3a, 0x64, 0xff, 0x29, 0x38, 0x60, 0xff, 0x24, 0x34, 0x5b, 0xff, 0x26, 0x37, 0x57, 0xff, 0x1d, 0x2d, 0x47, 0xff, 0x26, 0x33, 0x4a, 0xff, 0x1c, 0x27, 0x3b, 0xff, 0x26, 0x2e, 0x4b, 0xff, 0x3b, 0x41, 0x6a, 0xff, 0x17, 0x1f, 0x43, 0xff, 0x1a, 0x24, 0x3f, 0xff, 0x19, 0x21, 0x38, 0xff, 0x15, 0x1d, 0x31, 0xff, 0x1b, 0x26, 0x3d, 0xff, 0x17, 0x22, 0x3e, 0xff, 0x20, 0x2d, 0x50, 0xff, 0x25, 0x34, 0x59, 0xff, 0x29, 0x36, 0x5b, 0xff, 0x32, 0x40, 0x64, 0xff, 0x46, 0x51, 0x76, 0xff, 0x3e, 0x49, 0x6e, 0xff, 0x26, 0x32, 0x58, 0xff, 0x2a, 0x37, 0x5e, 0xff, 0x28, 0x36, 0x5b, 0xff, 0x2e, 0x3a, 0x5d, 0xff, 0x2a, 0x36, 0x58, 0xff, 0x25, 0x31, 0x50, 0xff, 0x21, 0x30, 0x4e, 0xff, 0x1b, 0x2e, 0x4f, 0xff, 0x27, 0x39, 0x64, 0xff, 0x37, 0x4a, 0x7e, 0xff, 0x2e, 0x44, 0x77, 0xff, 0x1f, 0x34, 0x66, 0xff, 0x1b, 0x2d, 0x65, 0xff, 0x1e, 0x31, 0x69, 0xff, 0x29, 0x3f, 0x6e, 0xff, 0x30, 0x47, 0x81, 0xff, 0x30, 0x43, 0x79, 0xff, 0x0f, 0x16, 0x2a, 0xff, 0x00, 0x00, 0x02, 0xff, 0x03, 0x05, 0x07, 0xff, 0x00, 0x01, 0x06, 0xff, 0x27, 0x2f, 0x37, 0xff, 0x8a, 0x8b, 0x8d, 0xff, 0xb7, 0xa4, 0x94, 0xff, 0xbc, 0xa1, 0x89, 0xff, 0xb8, 0xa0, 0x8a, 0xff, 0xb3, 0x9c, 0x83, 0xff, 0xb3, 0x9a, 0x80, 0xff, 0xb5, 0x9c, 0x81, 0xff, 0xb9, 0x9f, 0x83, 0xff, 0xbb, 0xa2, 0x87, 0xff, 0xb9, 0xa1, 0x8a, 0xff, 0xb9, 0xa1, 0x89, 0xff, 0xb9, 0xa2, 0x8b, 0xff, 0xb7, 0xa2, 0x8b, 0xff, 0xb8, 0xa4, 0x8e, 0xff, 0xb2, 0x9e, 0x8a, 0xff, 0xab, 0x98, 0x85, 0xff, 0xa2, 0x96, 0x84, 0xff, 0x9a, 0x93, 0x83, 0xff, 0x94, 0x8f, 0x81, 0xff, 0x8f, 0x89, 0x7e, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x78, 0x78, 0x52, 0x22, 0x78, 0x71, 0x50, 0xfe, 0x77, 0x6e, 0x4c, 0xff, 0x76, 0x6d, 0x4b, 0xff, 0x73, 0x70, 0x4d, 0xff, 0x72, 0x73, 0x52, 0xff, 0x78, 0x7b, 0x59, 0xff, 0x7b, 0x81, 0x60, 0xff, 0x7a, 0x86, 0x65, 0xff, 0x78, 0x89, 0x69, 0xff, 0x77, 0x88, 0x6a, 0xff, 0x77, 0x87, 0x6a, 0xff, 0x7a, 0x86, 0x68, 0xff, 0x7b, 0x84, 0x65, 0xff, 0x79, 0x80, 0x61, 0xff, 0x78, 0x7c, 0x60, 0xff, 0x76, 0x75, 0x5f, 0xff, 0x72, 0x6d, 0x5c, 0xff, 0x6c, 0x62, 0x57, 0xff, 0x67, 0x5c, 0x57, 0xff, 0x63, 0x5b, 0x5b, 0xff, 0x62, 0x5d, 0x5f, 0xff, 0x62, 0x62, 0x63, 0xff, 0x67, 0x6b, 0x6b, 0xff, 0x6c, 0x75, 0x6e, 0xff, 0x6f, 0x79, 0x76, 0xff, 0x71, 0x7c, 0x7a, 0xff, 0x71, 0x80, 0x76, 0xff, 0x5f, 0x71, 0x63, 0xff, 0x99, 0xa8, 0xa4, 0xff, 0xd7, 0xe7, 0xf6, 0xff, 0xc5, 0xd5, 0xe8, 0xff, 0xca, 0xda, 0xee, 0xff, 0xb4, 0xca, 0xe7, 0xff, 0xc2, 0xd9, 0xf3, 0xff, 0xe1, 0xf2, 0xfd, 0xff, 0xf7, 0xfe, 0xfb, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xe7, 0xf1, 0xff, 0xff, 0xd1, 0xe0, 0xfb, 0xff, 0xad, 0xbd, 0xe2, 0xff, 0x9b, 0xaa, 0xd5, 0xff, 0x96, 0xab, 0xc3, 0xff, 0x94, 0xa6, 0xc0, 0xff, 0x84, 0x8f, 0xba, 0xff, 0x72, 0x7b, 0xa6, 0xff, 0x66, 0x6f, 0x96, 0xff, 0x64, 0x6a, 0x94, 0xff, 0x4e, 0x55, 0x80, 0xff, 0x42, 0x4c, 0x76, 0xff, 0x49, 0x53, 0x7f, 0xff, 0x39, 0x41, 0x71, 0xff, 0x32, 0x39, 0x6b, 0xff, 0x23, 0x28, 0x5d, 0xff, 0x2e, 0x38, 0x6a, 0xff, 0x2f, 0x3d, 0x6c, 0xff, 0x2c, 0x3d, 0x69, 0xff, 0x25, 0x37, 0x5f, 0xff, 0x2d, 0x3d, 0x61, 0xff, 0x2c, 0x3b, 0x5d, 0xff, 0x2f, 0x3d, 0x5c, 0xff, 0x2f, 0x3c, 0x56, 0xff, 0x22, 0x2c, 0x43, 0xff, 0x19, 0x21, 0x36, 0xff, 0x17, 0x1d, 0x37, 0xff, 0x1b, 0x21, 0x42, 0xff, 0x19, 0x20, 0x41, 0xff, 0x1f, 0x24, 0x40, 0xff, 0x17, 0x1d, 0x33, 0xff, 0x12, 0x1a, 0x2c, 0xff, 0x1b, 0x26, 0x3b, 0xff, 0x19, 0x26, 0x41, 0xff, 0x23, 0x33, 0x55, 0xff, 0x2e, 0x3e, 0x63, 0xff, 0x2e, 0x3c, 0x60, 0xff, 0x23, 0x31, 0x55, 0xff, 0x29, 0x36, 0x59, 0xff, 0x37, 0x43, 0x66, 0xff, 0x34, 0x41, 0x66, 0xff, 0x2a, 0x37, 0x5d, 0xff, 0x23, 0x2f, 0x54, 0xff, 0x2b, 0x38, 0x5a, 0xff, 0x1d, 0x29, 0x49, 0xff, 0x16, 0x1f, 0x3f, 0xff, 0x1d, 0x2b, 0x4b, 0xff, 0x2a, 0x3d, 0x64, 0xff, 0x37, 0x49, 0x78, 0xff, 0x34, 0x48, 0x7e, 0xff, 0x25, 0x39, 0x70, 0xff, 0x1e, 0x31, 0x65, 0xff, 0x1a, 0x2d, 0x64, 0xff, 0x1e, 0x33, 0x68, 0xff, 0x2b, 0x42, 0x70, 0xff, 0x2f, 0x49, 0x80, 0xff, 0x2d, 0x42, 0x76, 0xff, 0x0b, 0x16, 0x29, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x02, 0x07, 0xff, 0x00, 0x08, 0x13, 0xff, 0x47, 0x4e, 0x57, 0xff, 0xa3, 0xa1, 0xa1, 0xff, 0xba, 0xad, 0xa3, 0xff, 0xbc, 0xa9, 0x9f, 0xff, 0xb8, 0xa5, 0x9a, 0xff, 0xb3, 0xa0, 0x91, 0xff, 0xac, 0x99, 0x85, 0xff, 0xa8, 0x94, 0x7f, 0xff, 0xb3, 0x9d, 0x88, 0xff, 0xba, 0xa3, 0x90, 0xff, 0xb8, 0xa3, 0x91, 0xff, 0xb8, 0xa2, 0x91, 0xff, 0xb7, 0xa3, 0x91, 0xff, 0xb6, 0xa2, 0x90, 0xff, 0xb0, 0x9f, 0x8d, 0xff, 0xab, 0x9a, 0x87, 0xff, 0xa5, 0x95, 0x83, 0xff, 0x9e, 0x94, 0x82, 0xff, 0x97, 0x92, 0x81, 0xff, 0x8e, 0x8d, 0x7f, 0xfe, 0x8a, 0x8a, 0x7b, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7c, 0x72, 0x4d, 0xb4, 0x7b, 0x6f, 0x48, 0xff, 0x7b, 0x6f, 0x47, 0xff, 0x78, 0x70, 0x4a, 0xff, 0x77, 0x74, 0x50, 0xff, 0x7d, 0x7c, 0x57, 0xff, 0x80, 0x83, 0x5e, 0xff, 0x7f, 0x88, 0x63, 0xff, 0x7c, 0x8a, 0x66, 0xff, 0x7a, 0x89, 0x68, 0xff, 0x79, 0x88, 0x69, 0xff, 0x7b, 0x87, 0x68, 0xff, 0x7b, 0x84, 0x65, 0xff, 0x79, 0x80, 0x61, 0xff, 0x78, 0x7c, 0x61, 0xff, 0x77, 0x76, 0x60, 0xff, 0x74, 0x70, 0x5d, 0xff, 0x6e, 0x64, 0x59, 0xff, 0x68, 0x5d, 0x56, 0xff, 0x67, 0x5e, 0x5b, 0xff, 0x64, 0x5f, 0x61, 0xff, 0x63, 0x63, 0x66, 0xff, 0x68, 0x6d, 0x6d, 0xff, 0x6f, 0x77, 0x6f, 0xff, 0x73, 0x7a, 0x77, 0xff, 0x74, 0x7c, 0x7c, 0xff, 0x74, 0x82, 0x75, 0xff, 0x6f, 0x7d, 0x6c, 0xff, 0x80, 0x8c, 0x82, 0xff, 0xca, 0xd8, 0xe7, 0xff, 0xd2, 0xe1, 0xf6, 0xff, 0xc6, 0xd4, 0xe5, 0xff, 0xab, 0xc0, 0xd9, 0xff, 0xb5, 0xcd, 0xe5, 0xff, 0xdb, 0xec, 0xfa, 0xff, 0xf8, 0xff, 0xfb, 0xff, 0xf1, 0xf9, 0xf6, 0xff, 0xe0, 0xed, 0xfa, 0xff, 0xd9, 0xea, 0xfd, 0xff, 0xbb, 0xce, 0xe8, 0xff, 0x9d, 0xb0, 0xcb, 0xff, 0x93, 0xa5, 0xc1, 0xff, 0x85, 0x92, 0xb8, 0xff, 0x61, 0x6c, 0x96, 0xff, 0x66, 0x71, 0x94, 0xff, 0x62, 0x6c, 0x8e, 0xff, 0x53, 0x58, 0x85, 0xff, 0x47, 0x4e, 0x7a, 0xff, 0x42, 0x4d, 0x75, 0xff, 0x41, 0x4c, 0x75, 0xff, 0x30, 0x3c, 0x67, 0xff, 0x2b, 0x35, 0x63, 0xff, 0x29, 0x34, 0x60, 0xff, 0x29, 0x35, 0x64, 0xff, 0x2d, 0x3c, 0x6b, 0xff, 0x25, 0x38, 0x62, 0xff, 0x29, 0x3e, 0x64, 0xff, 0x32, 0x43, 0x66, 0xff, 0x34, 0x40, 0x60, 0xff, 0x2e, 0x38, 0x58, 0xff, 0x2c, 0x37, 0x54, 0xff, 0x1e, 0x27, 0x3e, 0xff, 0x1d, 0x24, 0x37, 0xff, 0x1b, 0x21, 0x35, 0xff, 0x11, 0x17, 0x30, 0xff, 0x17, 0x1c, 0x3a, 0xff, 0x1f, 0x23, 0x40, 0xff, 0x20, 0x25, 0x3b, 0xff, 0x0b, 0x13, 0x25, 0xff, 0x1a, 0x25, 0x39, 0xff, 0x24, 0x31, 0x4a, 0xff, 0x25, 0x35, 0x56, 0xff, 0x2f, 0x40, 0x67, 0xff, 0x2c, 0x3d, 0x61, 0xff, 0x2a, 0x38, 0x5d, 0xff, 0x22, 0x30, 0x55, 0xff, 0x2a, 0x37, 0x5b, 0xff, 0x31, 0x3d, 0x63, 0xff, 0x2e, 0x3a, 0x5e, 0xff, 0x28, 0x35, 0x58, 0xff, 0x22, 0x2e, 0x50, 0xff, 0x1f, 0x2a, 0x4b, 0xff, 0x1c, 0x26, 0x42, 0xff, 0x24, 0x32, 0x54, 0xff, 0x30, 0x42, 0x6f, 0xff, 0x3d, 0x50, 0x82, 0xff, 0x33, 0x46, 0x80, 0xff, 0x20, 0x32, 0x6d, 0xff, 0x1e, 0x2d, 0x66, 0xff, 0x19, 0x2a, 0x64, 0xff, 0x1d, 0x33, 0x65, 0xff, 0x2c, 0x45, 0x70, 0xff, 0x32, 0x4c, 0x85, 0xff, 0x30, 0x45, 0x78, 0xff, 0x0d, 0x19, 0x2c, 0xff, 0x06, 0x04, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x12, 0x1c, 0x2b, 0xff, 0x71, 0x7a, 0x85, 0xff, 0xa5, 0xa5, 0xa6, 0xff, 0xa7, 0xa2, 0xa1, 0xff, 0xa9, 0x9e, 0xa1, 0xff, 0xa9, 0x9a, 0x99, 0xff, 0xa7, 0x9b, 0x92, 0xff, 0xaa, 0x9c, 0x8e, 0xff, 0xab, 0x9c, 0x8c, 0xff, 0xb0, 0x9e, 0x91, 0xff, 0xb2, 0x9f, 0x94, 0xff, 0xb0, 0x9d, 0x91, 0xff, 0xaf, 0x9c, 0x8e, 0xff, 0xaf, 0x9d, 0x8f, 0xff, 0xb3, 0xa0, 0x92, 0xff, 0xae, 0x9d, 0x8e, 0xff, 0xa7, 0x96, 0x85, 0xff, 0xa1, 0x94, 0x81, 0xff, 0x9b, 0x93, 0x82, 0xff, 0x94, 0x91, 0x82, 0xff, 0x8e, 0x8f, 0x81, 0xb5, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x73, 0x4b, 0x40, 0x7c, 0x6f, 0x45, 0xff, 0x7b, 0x6e, 0x46, 0xff, 0x7a, 0x70, 0x4a, 0xff, 0x7a, 0x73, 0x4f, 0xff, 0x7e, 0x7b, 0x53, 0xff, 0x80, 0x81, 0x5a, 0xff, 0x7e, 0x86, 0x63, 0xff, 0x7b, 0x87, 0x65, 0xff, 0x7a, 0x86, 0x65, 0xff, 0x7c, 0x86, 0x65, 0xff, 0x7d, 0x85, 0x65, 0xff, 0x7c, 0x82, 0x63, 0xff, 0x7a, 0x7f, 0x60, 0xff, 0x78, 0x7b, 0x5f, 0xff, 0x77, 0x75, 0x5e, 0xff, 0x72, 0x6f, 0x5b, 0xff, 0x70, 0x65, 0x5a, 0xff, 0x6b, 0x5f, 0x57, 0xff, 0x68, 0x5f, 0x5b, 0xff, 0x66, 0x61, 0x62, 0xff, 0x66, 0x65, 0x65, 0xff, 0x6b, 0x6e, 0x6d, 0xff, 0x70, 0x77, 0x71, 0xff, 0x73, 0x7b, 0x76, 0xff, 0x75, 0x7f, 0x78, 0xff, 0x77, 0x84, 0x76, 0xff, 0x79, 0x85, 0x74, 0xff, 0x75, 0x7e, 0x6b, 0xff, 0x8e, 0x9e, 0x9a, 0xff, 0xc0, 0xd1, 0xda, 0xff, 0xc6, 0xd5, 0xe6, 0xff, 0xaf, 0xc5, 0xe2, 0xff, 0xb8, 0xcb, 0xe4, 0xff, 0xe3, 0xed, 0xf6, 0xff, 0xf3, 0xfe, 0xfc, 0xff, 0xe9, 0xf7, 0xf7, 0xff, 0xde, 0xeb, 0xf6, 0xff, 0xcc, 0xda, 0xef, 0xff, 0xad, 0xbe, 0xdb, 0xff, 0x8f, 0xa5, 0xc5, 0xff, 0x7d, 0x93, 0xbd, 0xff, 0x66, 0x78, 0xa7, 0xff, 0x56, 0x66, 0x8f, 0xff, 0x6d, 0x7e, 0xa0, 0xff, 0x41, 0x51, 0x72, 0xff, 0x33, 0x41, 0x67, 0xff, 0x3b, 0x49, 0x73, 0xff, 0x2f, 0x3e, 0x6b, 0xff, 0x3b, 0x48, 0x76, 0xff, 0x42, 0x50, 0x7e, 0xff, 0x31, 0x3e, 0x6f, 0xff, 0x2c, 0x3b, 0x6c, 0xff, 0x2f, 0x3a, 0x6c, 0xff, 0x1d, 0x28, 0x57, 0xff, 0x1e, 0x2d, 0x54, 0xff, 0x2b, 0x3d, 0x5e, 0xff, 0x42, 0x53, 0x71, 0xff, 0x4b, 0x5b, 0x77, 0xff, 0x3d, 0x49, 0x63, 0xff, 0x26, 0x31, 0x48, 0xff, 0x1e, 0x26, 0x3d, 0xff, 0x1e, 0x23, 0x3b, 0xff, 0x1c, 0x21, 0x38, 0xff, 0x15, 0x17, 0x31, 0xff, 0x19, 0x1b, 0x35, 0xff, 0x15, 0x19, 0x31, 0xff, 0x1a, 0x21, 0x35, 0xff, 0x1f, 0x28, 0x3c, 0xff, 0x1e, 0x2a, 0x42, 0xff, 0x24, 0x31, 0x4e, 0xff, 0x1e, 0x2d, 0x4f, 0xff, 0x2a, 0x3a, 0x60, 0xff, 0x2a, 0x3b, 0x5f, 0xff, 0x35, 0x45, 0x6a, 0xff, 0x2e, 0x3d, 0x61, 0xff, 0x23, 0x33, 0x57, 0xff, 0x1d, 0x2b, 0x50, 0xff, 0x2f, 0x3b, 0x60, 0xff, 0x33, 0x3e, 0x60, 0xff, 0x24, 0x2e, 0x4f, 0xff, 0x2b, 0x35, 0x56, 0xff, 0x1d, 0x28, 0x47, 0xff, 0x27, 0x37, 0x5b, 0xff, 0x2a, 0x3d, 0x6a, 0xff, 0x36, 0x4a, 0x7a, 0xff, 0x2d, 0x41, 0x77, 0xff, 0x1c, 0x30, 0x69, 0xff, 0x1c, 0x2d, 0x67, 0xff, 0x18, 0x2f, 0x63, 0xff, 0x1e, 0x35, 0x6b, 0xff, 0x2c, 0x41, 0x76, 0xff, 0x35, 0x50, 0x7e, 0xff, 0x38, 0x4d, 0x80, 0xff, 0x12, 0x1d, 0x36, 0xff, 0x04, 0x01, 0x00, 0xff, 0x00, 0x00, 0x03, 0xff, 0x24, 0x33, 0x3e, 0xff, 0x8d, 0x95, 0x9e, 0xff, 0xa7, 0x9f, 0xa4, 0xff, 0x9d, 0x99, 0x97, 0xff, 0x9d, 0x9b, 0x96, 0xff, 0x9e, 0x96, 0x92, 0xff, 0x93, 0x8f, 0x8b, 0xff, 0x9c, 0x99, 0x94, 0xff, 0xa5, 0x9e, 0x96, 0xff, 0x9e, 0x94, 0x89, 0xff, 0xa5, 0x97, 0x8a, 0xff, 0xa6, 0x97, 0x8a, 0xff, 0xa1, 0x95, 0x87, 0xff, 0xa2, 0x95, 0x88, 0xff, 0xa2, 0x96, 0x8b, 0xff, 0xa3, 0x96, 0x8d, 0xff, 0x9c, 0x91, 0x84, 0xff, 0x96, 0x90, 0x7d, 0xff, 0x92, 0x8f, 0x7f, 0xff, 0x8e, 0x8e, 0x80, 0xff, 0x8b, 0x8f, 0x83, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7d, 0x6d, 0x43, 0xcd, 0x7b, 0x6c, 0x44, 0xff, 0x7d, 0x70, 0x4b, 0xff, 0x7f, 0x74, 0x52, 0xff, 0x80, 0x7c, 0x56, 0xff, 0x81, 0x82, 0x5e, 0xff, 0x7f, 0x85, 0x67, 0xff, 0x7b, 0x86, 0x68, 0xff, 0x7b, 0x84, 0x65, 0xff, 0x7e, 0x86, 0x66, 0xff, 0x7f, 0x84, 0x65, 0xff, 0x7d, 0x82, 0x63, 0xff, 0x7c, 0x7f, 0x5f, 0xff, 0x7a, 0x7a, 0x5d, 0xff, 0x78, 0x74, 0x5d, 0xff, 0x74, 0x6e, 0x5b, 0xff, 0x71, 0x64, 0x5a, 0xff, 0x6c, 0x60, 0x59, 0xff, 0x68, 0x5f, 0x5b, 0xff, 0x66, 0x61, 0x60, 0xff, 0x66, 0x65, 0x64, 0xff, 0x6b, 0x6d, 0x6a, 0xff, 0x70, 0x75, 0x72, 0xff, 0x74, 0x7c, 0x76, 0xff, 0x76, 0x82, 0x78, 0xff, 0x78, 0x84, 0x77, 0xff, 0x7c, 0x86, 0x76, 0xff, 0x7a, 0x83, 0x6d, 0xff, 0x74, 0x81, 0x72, 0xff, 0xc5, 0xd3, 0xce, 0xff, 0xd7, 0xe4, 0xf0, 0xff, 0xb7, 0xca, 0xe9, 0xff, 0xbb, 0xcd, 0xe8, 0xff, 0xe1, 0xe8, 0xf2, 0xff, 0xec, 0xfb, 0xfc, 0xff, 0xe5, 0xf6, 0xfa, 0xff, 0xe1, 0xec, 0xfa, 0xff, 0xd0, 0xdb, 0xf2, 0xff, 0xac, 0xbc, 0xde, 0xff, 0x90, 0xa5, 0xce, 0xff, 0x65, 0x7b, 0xad, 0xff, 0x53, 0x67, 0x98, 0xff, 0x51, 0x5f, 0x8e, 0xff, 0x49, 0x55, 0x85, 0xff, 0x41, 0x4c, 0x7f, 0xff, 0x3c, 0x48, 0x7d, 0xff, 0x3e, 0x4b, 0x7f, 0xff, 0x3f, 0x4d, 0x81, 0xff, 0x35, 0x42, 0x78, 0xff, 0x32, 0x40, 0x76, 0xff, 0x31, 0x3f, 0x75, 0xff, 0x2a, 0x38, 0x72, 0xff, 0x23, 0x2f, 0x5f, 0xff, 0x35, 0x40, 0x66, 0xff, 0x65, 0x72, 0x96, 0xff, 0x85, 0x92, 0xb4, 0xff, 0x77, 0x85, 0xa3, 0xff, 0x7a, 0x8a, 0xa7, 0xff, 0x66, 0x72, 0x8b, 0xff, 0x27, 0x30, 0x46, 0xff, 0x16, 0x1d, 0x35, 0xff, 0x1e, 0x23, 0x3d, 0xff, 0x16, 0x1a, 0x33, 0xff, 0x14, 0x14, 0x30, 0xff, 0x1b, 0x1d, 0x33, 0xff, 0x10, 0x15, 0x28, 0xff, 0x0a, 0x12, 0x24, 0xff, 0x20, 0x2a, 0x3f, 0xff, 0x1d, 0x2a, 0x45, 0xff, 0x20, 0x2d, 0x4d, 0xff, 0x1e, 0x2c, 0x4f, 0xff, 0x23, 0x31, 0x56, 0xff, 0x26, 0x35, 0x59, 0xff, 0x27, 0x36, 0x5a, 0xff, 0x24, 0x33, 0x57, 0xff, 0x2d, 0x3c, 0x60, 0xff, 0x2e, 0x3c, 0x62, 0xff, 0x32, 0x3f, 0x64, 0xff, 0x2e, 0x39, 0x5a, 0xff, 0x24, 0x2e, 0x4e, 0xff, 0x1f, 0x29, 0x4b, 0xff, 0x1f, 0x2a, 0x4e, 0xff, 0x2d, 0x3e, 0x66, 0xff, 0x30, 0x45, 0x72, 0xff, 0x2e, 0x40, 0x71, 0xff, 0x23, 0x37, 0x6b, 0xff, 0x1a, 0x2f, 0x65, 0xff, 0x1b, 0x30, 0x69, 0xff, 0x18, 0x35, 0x63, 0xff, 0x20, 0x37, 0x6f, 0xff, 0x30, 0x41, 0x7c, 0xff, 0x37, 0x52, 0x7a, 0xff, 0x40, 0x55, 0x87, 0xff, 0x1a, 0x25, 0x41, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0b, 0x0d, 0x14, 0xff, 0x48, 0x5a, 0x5f, 0xff, 0x97, 0x9b, 0xa0, 0xff, 0xae, 0x9f, 0xa3, 0xff, 0xa7, 0x9f, 0x99, 0xff, 0xa6, 0xa1, 0x96, 0xff, 0xa1, 0x9a, 0x94, 0xff, 0x9a, 0x98, 0x96, 0xff, 0x9f, 0x9e, 0x9e, 0xff, 0xa2, 0x9d, 0x99, 0xff, 0xa4, 0x9a, 0x8f, 0xff, 0xa7, 0x9a, 0x8a, 0xff, 0xa9, 0x9b, 0x8b, 0xff, 0xa4, 0x98, 0x8a, 0xff, 0xa3, 0x96, 0x89, 0xff, 0x9f, 0x94, 0x89, 0xff, 0x9f, 0x94, 0x8c, 0xff, 0x9a, 0x91, 0x84, 0xff, 0x92, 0x8c, 0x79, 0xff, 0x91, 0x8c, 0x7c, 0xff, 0x8d, 0x8c, 0x7c, 0xcd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0x6b, 0x42, 0x51, 0x7e, 0x69, 0x40, 0xff, 0x7f, 0x6d, 0x49, 0xff, 0x81, 0x72, 0x4f, 0xff, 0x7e, 0x78, 0x57, 0xff, 0x7e, 0x7e, 0x60, 0xff, 0x7d, 0x83, 0x68, 0xff, 0x7b, 0x85, 0x6b, 0xff, 0x7a, 0x83, 0x6a, 0xff, 0x7d, 0x84, 0x69, 0xff, 0x7e, 0x84, 0x67, 0xff, 0x7e, 0x82, 0x63, 0xff, 0x7e, 0x7f, 0x60, 0xff, 0x7d, 0x79, 0x5d, 0xff, 0x7b, 0x72, 0x5d, 0xff, 0x7a, 0x6c, 0x5b, 0xff, 0x73, 0x65, 0x5a, 0xff, 0x6d, 0x61, 0x5a, 0xff, 0x69, 0x5e, 0x5c, 0xff, 0x66, 0x60, 0x61, 0xff, 0x69, 0x67, 0x67, 0xff, 0x6d, 0x6e, 0x6c, 0xff, 0x72, 0x75, 0x73, 0xff, 0x77, 0x7e, 0x78, 0xff, 0x7a, 0x84, 0x7b, 0xff, 0x7e, 0x89, 0x7c, 0xff, 0x83, 0x8c, 0x7b, 0xff, 0x87, 0x8d, 0x7c, 0xff, 0x7c, 0x83, 0x74, 0xff, 0x99, 0x9e, 0x92, 0xff, 0xc8, 0xcd, 0xcd, 0xff, 0xbc, 0xca, 0xde, 0xff, 0xb1, 0xc4, 0xdd, 0xff, 0xd7, 0xe5, 0xf4, 0xff, 0xea, 0xf7, 0xfe, 0xff, 0xe2, 0xf0, 0xfb, 0xff, 0xde, 0xe9, 0xfc, 0xff, 0xcd, 0xd9, 0xf2, 0xff, 0xb7, 0xc6, 0xe8, 0xff, 0xb0, 0xc1, 0xe4, 0xff, 0x8d, 0x9d, 0xca, 0xff, 0x7a, 0x86, 0xb9, 0xff, 0x7b, 0x85, 0xb4, 0xff, 0x6d, 0x75, 0xa0, 0xff, 0x90, 0x9b, 0xc6, 0xff, 0x85, 0x92, 0xbe, 0xff, 0x7d, 0x89, 0xbc, 0xff, 0x83, 0x8f, 0xc6, 0xff, 0x6b, 0x78, 0xac, 0xff, 0x5b, 0x68, 0x9a, 0xff, 0x50, 0x5e, 0x8e, 0xff, 0x4d, 0x5b, 0x8c, 0xff, 0x86, 0x96, 0xb8, 0xff, 0xbd, 0xce, 0xe8, 0xff, 0xcd, 0xdb, 0xfb, 0xff, 0xbc, 0xc8, 0xea, 0xff, 0x91, 0x9b, 0xbe, 0xff, 0x6c, 0x77, 0x99, 0xff, 0x50, 0x59, 0x77, 0xff, 0x2f, 0x35, 0x50, 0xff, 0x1b, 0x20, 0x3a, 0xff, 0x18, 0x1c, 0x35, 0xff, 0x15, 0x19, 0x2f, 0xff, 0x10, 0x13, 0x26, 0xff, 0x16, 0x19, 0x2d, 0xff, 0x18, 0x1c, 0x31, 0xff, 0x18, 0x20, 0x33, 0xff, 0x19, 0x23, 0x3a, 0xff, 0x15, 0x21, 0x3c, 0xff, 0x1f, 0x2b, 0x4c, 0xff, 0x21, 0x2d, 0x52, 0xff, 0x35, 0x41, 0x67, 0xff, 0x2d, 0x38, 0x5e, 0xff, 0x2a, 0x35, 0x5b, 0xff, 0x33, 0x3f, 0x65, 0xff, 0x2b, 0x37, 0x5d, 0xff, 0x32, 0x3e, 0x64, 0xff, 0x31, 0x3e, 0x62, 0xff, 0x29, 0x34, 0x56, 0xff, 0x1d, 0x26, 0x47, 0xff, 0x1e, 0x29, 0x4b, 0xff, 0x29, 0x35, 0x59, 0xff, 0x2f, 0x3f, 0x68, 0xff, 0x31, 0x45, 0x72, 0xff, 0x28, 0x3b, 0x6e, 0xff, 0x1d, 0x32, 0x68, 0xff, 0x1b, 0x2f, 0x67, 0xff, 0x1d, 0x32, 0x6a, 0xff, 0x1a, 0x36, 0x65, 0xff, 0x1f, 0x36, 0x6d, 0xff, 0x30, 0x42, 0x7c, 0xff, 0x36, 0x52, 0x7c, 0xff, 0x45, 0x59, 0x8c, 0xff, 0x1e, 0x28, 0x43, 0xff, 0x00, 0x00, 0x00, 0xff, 0x1d, 0x26, 0x29, 0xff, 0x77, 0x82, 0x86, 0xff, 0xaa, 0xa8, 0xab, 0xff, 0xb0, 0xa3, 0xa4, 0xff, 0xb3, 0xa7, 0xa3, 0xff, 0xb4, 0xa9, 0xa2, 0xff, 0xb0, 0xa4, 0x9e, 0xff, 0xb2, 0xa9, 0xa7, 0xff, 0xb2, 0xab, 0xaa, 0xff, 0xb4, 0xaa, 0xa5, 0xff, 0xb8, 0xa8, 0x9d, 0xff, 0xb9, 0xa7, 0x9a, 0xff, 0xba, 0xa8, 0x9b, 0xff, 0xb6, 0xa6, 0x99, 0xff, 0xb4, 0xa3, 0x97, 0xff, 0xb3, 0xa3, 0x97, 0xff, 0xb3, 0xa4, 0x99, 0xff, 0xae, 0xa0, 0x91, 0xff, 0xa5, 0x99, 0x86, 0xff, 0xa2, 0x99, 0x87, 0xff, 0xa0, 0x97, 0x87, 0x51, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x01, 0x83, 0x6a, 0x41, 0xce, 0x82, 0x6b, 0x48, 0xff, 0x82, 0x6d, 0x4c, 0xff, 0x7c, 0x70, 0x54, 0xff, 0x78, 0x74, 0x5a, 0xff, 0x76, 0x79, 0x61, 0xff, 0x74, 0x79, 0x64, 0xff, 0x75, 0x7a, 0x65, 0xff, 0x78, 0x7c, 0x65, 0xff, 0x7b, 0x7d, 0x61, 0xff, 0x7c, 0x7c, 0x5f, 0xff, 0x7d, 0x7a, 0x5d, 0xff, 0x7f, 0x76, 0x5d, 0xff, 0x80, 0x70, 0x5d, 0xff, 0x7c, 0x6a, 0x5a, 0xff, 0x74, 0x64, 0x5b, 0xff, 0x6e, 0x60, 0x5a, 0xff, 0x6b, 0x60, 0x5e, 0xff, 0x6a, 0x62, 0x64, 0xff, 0x6c, 0x67, 0x68, 0xff, 0x6f, 0x6e, 0x6d, 0xff, 0x72, 0x74, 0x73, 0xff, 0x76, 0x7b, 0x78, 0xff, 0x7a, 0x82, 0x7a, 0xff, 0x80, 0x89, 0x7d, 0xff, 0x85, 0x8c, 0x7f, 0xff, 0x8a, 0x8e, 0x7f, 0xff, 0x8b, 0x8d, 0x79, 0xff, 0x7e, 0x7d, 0x67, 0xff, 0xa9, 0xa8, 0x9b, 0xff, 0xc7, 0xd1, 0xd8, 0xff, 0xb3, 0xc4, 0xd8, 0xff, 0xbf, 0xd2, 0xe5, 0xff, 0xde, 0xeb, 0xf9, 0xff, 0xdb, 0xe7, 0xf7, 0xff, 0xd1, 0xdd, 0xf6, 0xff, 0xd5, 0xe0, 0xfc, 0xff, 0xd0, 0xdb, 0xf7, 0xff, 0xbd, 0xc9, 0xe8, 0xff, 0xa8, 0xb3, 0xd5, 0xff, 0xb6, 0xc1, 0xdd, 0xff, 0xcc, 0xd5, 0xed, 0xff, 0xce, 0xd8, 0xf4, 0xff, 0xd5, 0xe2, 0xfe, 0xff, 0xcb, 0xda, 0xf6, 0xff, 0xb5, 0xc6, 0xec, 0xff, 0xb2, 0xc1, 0xec, 0xff, 0xb7, 0xc6, 0xed, 0xff, 0xb7, 0xc5, 0xea, 0xff, 0xb1, 0xbf, 0xe3, 0xff, 0xc9, 0xd6, 0xf8, 0xff, 0xde, 0xef, 0xff, 0xff, 0xc8, 0xd9, 0xf5, 0xff, 0xb9, 0xc5, 0xe6, 0xff, 0x84, 0x8d, 0xb1, 0xff, 0x56, 0x5e, 0x83, 0xff, 0x39, 0x41, 0x65, 0xff, 0x46, 0x4c, 0x6b, 0xff, 0x2b, 0x2e, 0x4a, 0xff, 0x1b, 0x1f, 0x3a, 0xff, 0x10, 0x15, 0x2c, 0xff, 0x11, 0x16, 0x2a, 0xff, 0x0e, 0x14, 0x24, 0xff, 0x12, 0x15, 0x28, 0xff, 0x16, 0x1b, 0x2f, 0xff, 0x20, 0x27, 0x3b, 0xff, 0x1d, 0x26, 0x3e, 0xff, 0x17, 0x22, 0x3f, 0xff, 0x24, 0x30, 0x53, 0xff, 0x24, 0x2e, 0x56, 0xff, 0x30, 0x3b, 0x63, 0xff, 0x2b, 0x36, 0x5d, 0xff, 0x28, 0x33, 0x5a, 0xff, 0x33, 0x3e, 0x66, 0xff, 0x2d, 0x39, 0x60, 0xff, 0x2a, 0x36, 0x5d, 0xff, 0x2b, 0x38, 0x5c, 0xff, 0x2c, 0x37, 0x59, 0xff, 0x1f, 0x29, 0x4c, 0xff, 0x1a, 0x26, 0x4b, 0xff, 0x2a, 0x38, 0x5f, 0xff, 0x2f, 0x41, 0x6c, 0xff, 0x2a, 0x3f, 0x6f, 0xff, 0x24, 0x39, 0x6c, 0xff, 0x1e, 0x33, 0x6a, 0xff, 0x1d, 0x34, 0x6b, 0xff, 0x20, 0x37, 0x6c, 0xff, 0x1f, 0x3b, 0x6a, 0xff, 0x24, 0x3a, 0x72, 0xff, 0x30, 0x42, 0x7c, 0xff, 0x36, 0x52, 0x7b, 0xff, 0x49, 0x5d, 0x90, 0xff, 0x23, 0x2c, 0x47, 0xff, 0x00, 0x00, 0x00, 0xff, 0x35, 0x3f, 0x43, 0xff, 0x9b, 0x9c, 0x9e, 0xff, 0xb4, 0xad, 0xad, 0xff, 0xb5, 0xa9, 0xa8, 0xff, 0xb8, 0xab, 0xa8, 0xff, 0xb8, 0xa9, 0xa4, 0xff, 0xb6, 0xa8, 0xa1, 0xff, 0xba, 0xae, 0xac, 0xff, 0xb9, 0xaf, 0xad, 0xff, 0xbb, 0xae, 0xa9, 0xff, 0xbe, 0xac, 0x9f, 0xff, 0xbf, 0xab, 0x9d, 0xff, 0xc0, 0xad, 0xa0, 0xff, 0xbf, 0xac, 0x9f, 0xff, 0xbc, 0xaa, 0x9d, 0xff, 0xb9, 0xa8, 0x9b, 0xff, 0xb9, 0xa9, 0x9b, 0xff, 0xb7, 0xa8, 0x96, 0xff, 0xb2, 0xa3, 0x8e, 0xff, 0xae, 0x9f, 0x8e, 0xce, 0xff, 0xff, 0xff, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x86, 0x6d, 0x45, 0x46, 0x85, 0x6c, 0x4a, 0xff, 0x81, 0x6d, 0x4e, 0xff, 0x7e, 0x6e, 0x53, 0xff, 0x7a, 0x70, 0x56, 0xff, 0x75, 0x71, 0x5b, 0xff, 0x72, 0x71, 0x5d, 0xff, 0x73, 0x72, 0x5d, 0xff, 0x75, 0x75, 0x5e, 0xff, 0x7d, 0x77, 0x5d, 0xff, 0x7f, 0x77, 0x5d, 0xff, 0x7f, 0x76, 0x5a, 0xff, 0x80, 0x74, 0x5b, 0xff, 0x7e, 0x6f, 0x5d, 0xff, 0x7b, 0x69, 0x5b, 0xff, 0x75, 0x64, 0x5b, 0xff, 0x70, 0x60, 0x5d, 0xff, 0x6e, 0x61, 0x61, 0xff, 0x6d, 0x64, 0x65, 0xff, 0x6b, 0x67, 0x69, 0xff, 0x6f, 0x6d, 0x6d, 0xff, 0x74, 0x74, 0x74, 0xff, 0x79, 0x7c, 0x7a, 0xff, 0x7d, 0x82, 0x7d, 0xff, 0x83, 0x89, 0x7f, 0xff, 0x89, 0x8d, 0x80, 0xff, 0x8b, 0x8f, 0x81, 0xff, 0x93, 0x92, 0x7d, 0xff, 0x8f, 0x8a, 0x6d, 0xff, 0x96, 0x91, 0x79, 0xff, 0xc3, 0xc7, 0xc2, 0xff, 0xc0, 0xce, 0xdc, 0xff, 0xc2, 0xd3, 0xe6, 0xff, 0xd8, 0xe6, 0xf6, 0xff, 0xd9, 0xe7, 0xf7, 0xff, 0xda, 0xe8, 0xfe, 0xff, 0xd1, 0xde, 0xf8, 0xff, 0xd3, 0xdf, 0xf5, 0xff, 0xc9, 0xd5, 0xeb, 0xff, 0xd6, 0xe0, 0xec, 0xff, 0xf4, 0xfd, 0xff, 0xff, 0xee, 0xf7, 0xff, 0xff, 0xe7, 0xf2, 0xff, 0xff, 0xdd, 0xea, 0xfd, 0xff, 0xd3, 0xe3, 0xf7, 0xff, 0xcb, 0xdc, 0xf9, 0xff, 0xba, 0xcb, 0xec, 0xff, 0xb9, 0xcb, 0xea, 0xff, 0xc1, 0xcf, 0xee, 0xff, 0xcc, 0xd8, 0xf7, 0xff, 0xca, 0xd5, 0xf2, 0xff, 0xba, 0xc7, 0xe8, 0xff, 0xa5, 0xb2, 0xd8, 0xff, 0x8b, 0x94, 0xbd, 0xff, 0x66, 0x6c, 0x97, 0xff, 0x47, 0x4c, 0x71, 0xff, 0x34, 0x3a, 0x58, 0xff, 0x22, 0x27, 0x41, 0xff, 0x13, 0x16, 0x2f, 0xff, 0x1b, 0x1e, 0x36, 0xff, 0x15, 0x1a, 0x30, 0xff, 0x18, 0x1e, 0x31, 0xff, 0x1c, 0x22, 0x34, 0xff, 0x1b, 0x1f, 0x32, 0xff, 0x13, 0x19, 0x2b, 0xff, 0x12, 0x1a, 0x2d, 0xff, 0x1c, 0x25, 0x3d, 0xff, 0x25, 0x2f, 0x4f, 0xff, 0x2c, 0x36, 0x5c, 0xff, 0x29, 0x35, 0x60, 0xff, 0x2a, 0x39, 0x63, 0xff, 0x34, 0x42, 0x6c, 0xff, 0x28, 0x36, 0x60, 0xff, 0x2d, 0x3a, 0x65, 0xff, 0x2a, 0x39, 0x64, 0xff, 0x20, 0x2d, 0x54, 0xff, 0x30, 0x3c, 0x5e, 0xff, 0x32, 0x3e, 0x5f, 0xff, 0x1b, 0x27, 0x4a, 0xff, 0x16, 0x23, 0x4a, 0xff, 0x26, 0x36, 0x5f, 0xff, 0x2a, 0x3c, 0x6b, 0xff, 0x28, 0x3d, 0x6f, 0xff, 0x24, 0x3a, 0x6e, 0xff, 0x22, 0x36, 0x6e, 0xff, 0x22, 0x39, 0x6f, 0xff, 0x21, 0x3a, 0x6e, 0xff, 0x20, 0x3d, 0x6b, 0xff, 0x25, 0x3a, 0x73, 0xff, 0x2f, 0x42, 0x7b, 0xff, 0x35, 0x51, 0x7a, 0xff, 0x4e, 0x63, 0x95, 0xff, 0x2a, 0x32, 0x4e, 0xff, 0x00, 0x05, 0x05, 0xff, 0x5a, 0x63, 0x67, 0xff, 0xba, 0xac, 0xaf, 0xff, 0xb9, 0xaa, 0xa8, 0xff, 0xb4, 0xac, 0xa6, 0xff, 0xb8, 0xab, 0xa8, 0xff, 0xbb, 0xaa, 0xa6, 0xff, 0xb9, 0xaa, 0xa2, 0xff, 0xbb, 0xb0, 0xac, 0xff, 0xbb, 0xb0, 0xaf, 0xff, 0xbb, 0xb0, 0xa9, 0xff, 0xbf, 0xac, 0x9f, 0xff, 0xbf, 0xac, 0x9d, 0xff, 0xbf, 0xac, 0x9f, 0xff, 0xbc, 0xaa, 0x9d, 0xff, 0xb8, 0xa8, 0x99, 0xff, 0xb6, 0xa6, 0x96, 0xff, 0xb5, 0xa7, 0x97, 0xff, 0xb5, 0xa5, 0x92, 0xff, 0xb3, 0xa4, 0x8d, 0xff, 0xac, 0xa1, 0x8c, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x83, 0x6e, 0x4b, 0xb4, 0x80, 0x6f, 0x4f, 0xff, 0x84, 0x71, 0x54, 0xff, 0x81, 0x72, 0x57, 0xff, 0x7a, 0x71, 0x59, 0xff, 0x75, 0x70, 0x5a, 0xff, 0x73, 0x6d, 0x57, 0xff, 0x77, 0x71, 0x57, 0xff, 0x81, 0x72, 0x5c, 0xff, 0x83, 0x72, 0x5a, 0xff, 0x7f, 0x71, 0x58, 0xff, 0x7d, 0x71, 0x59, 0xff, 0x7c, 0x6e, 0x5b, 0xff, 0x79, 0x69, 0x5b, 0xff, 0x76, 0x65, 0x5d, 0xff, 0x73, 0x62, 0x5f, 0xff, 0x71, 0x62, 0x63, 0xff, 0x6e, 0x64, 0x67, 0xff, 0x6f, 0x68, 0x6b, 0xff, 0x71, 0x6e, 0x70, 0xff, 0x77, 0x76, 0x76, 0xff, 0x7d, 0x7f, 0x7c, 0xff, 0x82, 0x86, 0x81, 0xff, 0x87, 0x8c, 0x84, 0xff, 0x8e, 0x90, 0x84, 0xff, 0x90, 0x91, 0x85, 0xff, 0x96, 0x95, 0x81, 0xff, 0x9c, 0x96, 0x76, 0xff, 0x91, 0x89, 0x6b, 0xff, 0xa0, 0xa1, 0x95, 0xff, 0xc6, 0xd0, 0xda, 0xff, 0xcf, 0xdd, 0xf0, 0xff, 0xd4, 0xe4, 0xf2, 0xff, 0xd4, 0xe3, 0xf4, 0xff, 0xc9, 0xda, 0xf1, 0xff, 0xbf, 0xcf, 0xea, 0xff, 0xcf, 0xde, 0xf3, 0xff, 0xe2, 0xf0, 0xfa, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xef, 0xf3, 0xf8, 0xff, 0xe1, 0xe6, 0xf0, 0xff, 0xdf, 0xe6, 0xf5, 0xff, 0xd8, 0xe2, 0xf7, 0xff, 0xd0, 0xdd, 0xfa, 0xff, 0xc4, 0xd5, 0xf6, 0xff, 0xb2, 0xc4, 0xe5, 0xff, 0xbf, 0xcc, 0xee, 0xff, 0xc0, 0xca, 0xef, 0xff, 0xb9, 0xbf, 0xe7, 0xff, 0xc2, 0xc8, 0xee, 0xff, 0xb2, 0xbb, 0xe8, 0xff, 0x6d, 0x76, 0xa8, 0xff, 0x3f, 0x43, 0x78, 0xff, 0x3d, 0x40, 0x72, 0xff, 0x3c, 0x40, 0x64, 0xff, 0x23, 0x29, 0x3f, 0xff, 0x18, 0x1c, 0x2e, 0xff, 0x18, 0x1a, 0x2d, 0xff, 0x12, 0x14, 0x28, 0xff, 0x11, 0x14, 0x29, 0xff, 0x16, 0x1a, 0x30, 0xff, 0x26, 0x2b, 0x41, 0xff, 0x1a, 0x1e, 0x30, 0xff, 0x18, 0x1d, 0x2e, 0xff, 0x20, 0x28, 0x3a, 0xff, 0x19, 0x21, 0x38, 0xff, 0x23, 0x2d, 0x4d, 0xff, 0x27, 0x31, 0x5b, 0xff, 0x32, 0x40, 0x6c, 0xff, 0x36, 0x49, 0x73, 0xff, 0x38, 0x49, 0x74, 0xff, 0x32, 0x44, 0x6e, 0xff, 0x33, 0x45, 0x6f, 0xff, 0x25, 0x38, 0x63, 0xff, 0x21, 0x30, 0x57, 0xff, 0x32, 0x3d, 0x60, 0xff, 0x2b, 0x36, 0x59, 0xff, 0x1c, 0x29, 0x4c, 0xff, 0x28, 0x37, 0x5d, 0xff, 0x25, 0x36, 0x60, 0xff, 0x24, 0x37, 0x68, 0xff, 0x2a, 0x3f, 0x74, 0xff, 0x28, 0x3d, 0x73, 0xff, 0x23, 0x38, 0x70, 0xff, 0x24, 0x3d, 0x73, 0xff, 0x21, 0x3d, 0x70, 0xff, 0x20, 0x3e, 0x6d, 0xff, 0x28, 0x3e, 0x76, 0xff, 0x30, 0x42, 0x7b, 0xff, 0x36, 0x50, 0x79, 0xff, 0x55, 0x69, 0x9b, 0xff, 0x31, 0x39, 0x53, 0xff, 0x0b, 0x1d, 0x1e, 0xff, 0x82, 0x89, 0x90, 0xff, 0xc9, 0xb1, 0xb3, 0xff, 0xbf, 0xaa, 0xa6, 0xff, 0xb2, 0xae, 0xa5, 0xff, 0xb4, 0xad, 0xa8, 0xff, 0xbd, 0xaf, 0xa8, 0xff, 0xb9, 0xae, 0xa4, 0xff, 0xbb, 0xb1, 0xae, 0xff, 0xbc, 0xb3, 0xb2, 0xff, 0xbc, 0xb0, 0xaa, 0xff, 0xbf, 0xae, 0x9f, 0xff, 0xc1, 0xae, 0x9f, 0xff, 0xbd, 0xad, 0xa0, 0xff, 0xba, 0xaa, 0x9b, 0xff, 0xb6, 0xa7, 0x97, 0xff, 0xb3, 0xa6, 0x95, 0xff, 0xb2, 0xa6, 0x95, 0xff, 0xb0, 0xa3, 0x8f, 0xff, 0xb1, 0xa1, 0x89, 0xb4, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x82, 0x6f, 0x4e, 0x27, 0x85, 0x72, 0x54, 0xfa, 0x89, 0x73, 0x57, 0xff, 0x87, 0x73, 0x5a, 0xff, 0x7d, 0x71, 0x5e, 0xff, 0x75, 0x6e, 0x5d, 0xff, 0x74, 0x6c, 0x58, 0xff, 0x76, 0x6f, 0x59, 0xff, 0x7d, 0x71, 0x57, 0xff, 0x82, 0x72, 0x58, 0xff, 0x7f, 0x70, 0x5a, 0xff, 0x7d, 0x6d, 0x5b, 0xff, 0x7a, 0x6d, 0x5b, 0xff, 0x73, 0x6a, 0x59, 0xff, 0x75, 0x66, 0x5b, 0xff, 0x74, 0x63, 0x5e, 0xff, 0x70, 0x62, 0x62, 0xff, 0x6d, 0x64, 0x67, 0xff, 0x6e, 0x69, 0x6b, 0xff, 0x71, 0x6f, 0x70, 0xff, 0x79, 0x78, 0x76, 0xff, 0x81, 0x80, 0x7b, 0xff, 0x88, 0x88, 0x83, 0xff, 0x8f, 0x8f, 0x87, 0xff, 0x94, 0x94, 0x88, 0xff, 0x94, 0x95, 0x86, 0xff, 0x96, 0x98, 0x86, 0xff, 0xa5, 0x9a, 0x7c, 0xff, 0xa3, 0x8e, 0x69, 0xff, 0x94, 0x8c, 0x75, 0xff, 0xb4, 0xbd, 0xc0, 0xff, 0xce, 0xd7, 0xeb, 0xff, 0xd8, 0xe1, 0xf4, 0xff, 0xd3, 0xdd, 0xef, 0xff, 0xd0, 0xdc, 0xee, 0xff, 0xd2, 0xde, 0xef, 0xff, 0xe7, 0xed, 0xf9, 0xff, 0xf4, 0xf6, 0xfc, 0xff, 0xee, 0xf2, 0xfa, 0xff, 0xe1, 0xe7, 0xf3, 0xff, 0xdc, 0xe2, 0xf0, 0xff, 0xd7, 0xde, 0xf2, 0xff, 0xd2, 0xdb, 0xf4, 0xff, 0xce, 0xd8, 0xf4, 0xff, 0xbb, 0xc9, 0xee, 0xff, 0xb1, 0xc0, 0xe9, 0xff, 0xbf, 0xcc, 0xf5, 0xff, 0xbb, 0xc6, 0xf4, 0xff, 0xa7, 0xb0, 0xe2, 0xff, 0x94, 0x9c, 0xd1, 0xff, 0x62, 0x6a, 0x9a, 0xff, 0x2e, 0x34, 0x61, 0xff, 0x25, 0x2a, 0x53, 0xff, 0x2d, 0x31, 0x54, 0xff, 0x26, 0x29, 0x48, 0xff, 0x1b, 0x1f, 0x38, 0xff, 0x19, 0x1d, 0x34, 0xff, 0x25, 0x2a, 0x40, 0xff, 0x1e, 0x21, 0x37, 0xff, 0x12, 0x14, 0x2b, 0xff, 0x1d, 0x20, 0x3a, 0xff, 0x19, 0x1d, 0x3a, 0xff, 0x20, 0x26, 0x3e, 0xff, 0x23, 0x2b, 0x41, 0xff, 0x2b, 0x35, 0x4f, 0xff, 0x25, 0x30, 0x4f, 0xff, 0x1f, 0x2a, 0x4a, 0xff, 0x2a, 0x32, 0x52, 0xff, 0x2b, 0x38, 0x5c, 0xff, 0x30, 0x43, 0x6a, 0xff, 0x26, 0x38, 0x5e, 0xff, 0x33, 0x43, 0x69, 0xff, 0x2e, 0x3d, 0x64, 0xff, 0x2c, 0x3c, 0x63, 0xff, 0x29, 0x34, 0x58, 0xff, 0x2a, 0x34, 0x55, 0xff, 0x29, 0x35, 0x56, 0xff, 0x27, 0x35, 0x5b, 0xff, 0x27, 0x36, 0x5f, 0xff, 0x2d, 0x3f, 0x6a, 0xff, 0x2a, 0x3f, 0x6f, 0xff, 0x2c, 0x42, 0x76, 0xff, 0x27, 0x3b, 0x71, 0xff, 0x22, 0x37, 0x6f, 0xff, 0x27, 0x3e, 0x75, 0xff, 0x26, 0x3e, 0x73, 0xff, 0x25, 0x40, 0x6e, 0xff, 0x2a, 0x3e, 0x76, 0xff, 0x2d, 0x41, 0x7c, 0xff, 0x2f, 0x50, 0x7b, 0xff, 0x4e, 0x65, 0x9a, 0xff, 0x3a, 0x44, 0x61, 0xff, 0x4e, 0x50, 0x51, 0xff, 0xb6, 0xac, 0xb0, 0xff, 0xc2, 0xb0, 0xb2, 0xff, 0xb8, 0xa8, 0xa7, 0xff, 0xb7, 0xac, 0xa8, 0xff, 0xb9, 0xad, 0xa9, 0xff, 0xbc, 0xae, 0xa8, 0xff, 0xb9, 0xac, 0xa5, 0xff, 0xbc, 0xaf, 0xab, 0xff, 0xc2, 0xb6, 0xb0, 0xff, 0xbb, 0xaf, 0xa7, 0xff, 0xbb, 0xae, 0xa2, 0xff, 0xbd, 0xae, 0xa2, 0xff, 0xbd, 0xac, 0xa2, 0xff, 0xbb, 0xab, 0x9f, 0xff, 0xb9, 0xa8, 0x9a, 0xff, 0xb5, 0xa7, 0x96, 0xff, 0xb2, 0xa3, 0x91, 0xff, 0xac, 0x9e, 0x8a, 0xfa, 0xac, 0x9f, 0x85, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x87, 0x70, 0x56, 0x88, 0x88, 0x72, 0x56, 0xff, 0x84, 0x73, 0x5b, 0xff, 0x7c, 0x71, 0x62, 0xff, 0x74, 0x71, 0x64, 0xff, 0x74, 0x6f, 0x60, 0xff, 0x75, 0x6f, 0x5e, 0xff, 0x77, 0x6f, 0x53, 0xff, 0x7c, 0x6f, 0x54, 0xff, 0x7c, 0x6e, 0x59, 0xff, 0x7d, 0x6c, 0x5c, 0xff, 0x7a, 0x6d, 0x5c, 0xff, 0x73, 0x6c, 0x59, 0xff, 0x75, 0x67, 0x5a, 0xff, 0x74, 0x64, 0x5e, 0xff, 0x71, 0x63, 0x61, 0xff, 0x6d, 0x64, 0x66, 0xff, 0x6c, 0x69, 0x69, 0xff, 0x71, 0x71, 0x71, 0xff, 0x7b, 0x7a, 0x77, 0xff, 0x83, 0x83, 0x7e, 0xff, 0x8c, 0x8a, 0x86, 0xff, 0x94, 0x91, 0x8a, 0xff, 0x97, 0x97, 0x8b, 0xff, 0x99, 0x9b, 0x89, 0xff, 0x94, 0x9a, 0x88, 0xff, 0xa5, 0x99, 0x7e, 0xff, 0xb0, 0x96, 0x6f, 0xff, 0x99, 0x8d, 0x6e, 0xff, 0xa1, 0xa8, 0xa6, 0xff, 0xd5, 0xdc, 0xf1, 0xff, 0xe8, 0xed, 0xff, 0xff, 0xda, 0xe1, 0xf3, 0xff, 0xde, 0xe6, 0xf5, 0xff, 0xe9, 0xf1, 0xfc, 0xff, 0xed, 0xf0, 0xfb, 0xff, 0xf4, 0xf1, 0xfc, 0xff, 0xe2, 0xe6, 0xf8, 0xff, 0xcd, 0xd7, 0xed, 0xff, 0xd6, 0xe0, 0xf6, 0xff, 0xce, 0xd9, 0xf2, 0xff, 0xc0, 0xcc, 0xe8, 0xff, 0xc3, 0xcf, 0xea, 0xff, 0xa5, 0xb2, 0xda, 0xff, 0x99, 0xa6, 0xd5, 0xff, 0x81, 0x8e, 0xbc, 0xff, 0x7c, 0x87, 0xb7, 0xff, 0x79, 0x84, 0xb5, 0xff, 0x30, 0x3a, 0x6f, 0xff, 0x28, 0x2e, 0x5b, 0xff, 0x2c, 0x31, 0x56, 0xff, 0x26, 0x2d, 0x4b, 0xff, 0x28, 0x2c, 0x46, 0xff, 0x20, 0x23, 0x3d, 0xff, 0x20, 0x22, 0x3f, 0xff, 0x18, 0x1c, 0x3a, 0xff, 0x23, 0x28, 0x45, 0xff, 0x31, 0x34, 0x4e, 0xff, 0x21, 0x25, 0x3d, 0xff, 0x20, 0x24, 0x3e, 0xff, 0x1c, 0x23, 0x40, 0xff, 0x2d, 0x34, 0x4f, 0xff, 0x19, 0x23, 0x3d, 0xff, 0x15, 0x21, 0x42, 0xff, 0x2a, 0x37, 0x5c, 0xff, 0x20, 0x2c, 0x4f, 0xff, 0x22, 0x2a, 0x49, 0xff, 0x20, 0x2c, 0x4e, 0xff, 0x26, 0x36, 0x5e, 0xff, 0x2f, 0x3d, 0x63, 0xff, 0x2f, 0x3b, 0x62, 0xff, 0x27, 0x32, 0x5a, 0xff, 0x33, 0x3c, 0x65, 0xff, 0x31, 0x39, 0x5c, 0xff, 0x29, 0x34, 0x51, 0xff, 0x20, 0x2d, 0x4f, 0xff, 0x2a, 0x38, 0x60, 0xff, 0x24, 0x35, 0x5f, 0xff, 0x26, 0x39, 0x64, 0xff, 0x2c, 0x40, 0x6f, 0xff, 0x2b, 0x40, 0x73, 0xff, 0x24, 0x3a, 0x6e, 0xff, 0x26, 0x3a, 0x71, 0xff, 0x29, 0x3d, 0x75, 0xff, 0x2a, 0x41, 0x76, 0xff, 0x2a, 0x43, 0x72, 0xff, 0x29, 0x3d, 0x77, 0xff, 0x29, 0x40, 0x7d, 0xff, 0x2a, 0x4f, 0x7a, 0xff, 0x47, 0x5f, 0x93, 0xff, 0x53, 0x5b, 0x79, 0xff, 0x7d, 0x73, 0x6d, 0xff, 0xc2, 0xaf, 0xa7, 0xff, 0xbf, 0xb5, 0xb0, 0xff, 0xb6, 0xac, 0xa7, 0xff, 0xb5, 0xa8, 0xa3, 0xff, 0xb5, 0xa8, 0xa3, 0xff, 0xb8, 0xac, 0xa5, 0xff, 0xbb, 0xae, 0xa6, 0xff, 0xba, 0xad, 0xa4, 0xff, 0xbd, 0xb1, 0xa6, 0xff, 0xbc, 0xb1, 0xa5, 0xff, 0xba, 0xb0, 0xa4, 0xff, 0xb8, 0xad, 0xa2, 0xff, 0xb9, 0xad, 0xa2, 0xff, 0xb9, 0xad, 0xa0, 0xff, 0xb8, 0xac, 0x9c, 0xff, 0xb3, 0xa7, 0x93, 0xff, 0xad, 0xa0, 0x8a, 0xff, 0xa9, 0x9c, 0x87, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8d, 0x71, 0x55, 0x09, 0x83, 0x72, 0x58, 0xdb, 0x81, 0x74, 0x5e, 0xff, 0x7b, 0x75, 0x66, 0xff, 0x74, 0x75, 0x69, 0xff, 0x73, 0x73, 0x66, 0xff, 0x76, 0x74, 0x64, 0xff, 0x7d, 0x73, 0x5a, 0xff, 0x7a, 0x6c, 0x54, 0xff, 0x7a, 0x6c, 0x56, 0xff, 0x7d, 0x6e, 0x5b, 0xff, 0x7c, 0x6e, 0x5d, 0xff, 0x79, 0x6d, 0x5d, 0xff, 0x78, 0x69, 0x5d, 0xff, 0x76, 0x65, 0x5d, 0xff, 0x72, 0x65, 0x62, 0xff, 0x6f, 0x66, 0x68, 0xff, 0x6c, 0x68, 0x6a, 0xff, 0x71, 0x71, 0x72, 0xff, 0x7a, 0x7c, 0x7a, 0xff, 0x84, 0x85, 0x84, 0xff, 0x8d, 0x8d, 0x8b, 0xff, 0x91, 0x90, 0x8b, 0xff, 0x94, 0x94, 0x8a, 0xff, 0x99, 0x9a, 0x8c, 0xff, 0x96, 0x9c, 0x8a, 0xff, 0xa1, 0x9b, 0x83, 0xff, 0xab, 0x99, 0x79, 0xff, 0x95, 0x8d, 0x6e, 0xff, 0xb5, 0xbb, 0xb3, 0xff, 0xde, 0xe9, 0xfa, 0xff, 0xe9, 0xee, 0xff, 0xff, 0xe6, 0xeb, 0xf9, 0xff, 0xe0, 0xe9, 0xf8, 0xff, 0xe5, 0xf1, 0xff, 0xff, 0xdd, 0xe6, 0xfd, 0xff, 0xd6, 0xdd, 0xf4, 0xff, 0xcf, 0xd9, 0xf6, 0xff, 0xbf, 0xca, 0xed, 0xff, 0xbb, 0xc8, 0xe8, 0xff, 0xb7, 0xc5, 0xe9, 0xff, 0xaf, 0xbc, 0xe4, 0xff, 0x9d, 0xac, 0xd3, 0xff, 0x71, 0x82, 0xae, 0xff, 0x66, 0x75, 0xa5, 0xff, 0x43, 0x50, 0x7f, 0xff, 0x39, 0x42, 0x6f, 0xff, 0x36, 0x3b, 0x66, 0xff, 0x28, 0x2d, 0x58, 0xff, 0x32, 0x36, 0x5f, 0xff, 0x2f, 0x34, 0x59, 0xff, 0x22, 0x28, 0x48, 0xff, 0x20, 0x24, 0x41, 0xff, 0x1d, 0x20, 0x3b, 0xff, 0x18, 0x1b, 0x36, 0xff, 0x1d, 0x20, 0x3f, 0xff, 0x1b, 0x1e, 0x3d, 0xff, 0x20, 0x24, 0x3e, 0xff, 0x24, 0x28, 0x40, 0xff, 0x26, 0x2c, 0x45, 0xff, 0x25, 0x2e, 0x49, 0xff, 0x22, 0x2b, 0x45, 0xff, 0x1d, 0x26, 0x41, 0xff, 0x2e, 0x38, 0x5b, 0xff, 0x2f, 0x3d, 0x63, 0xff, 0x21, 0x30, 0x59, 0xff, 0x2a, 0x38, 0x61, 0xff, 0x2d, 0x3b, 0x65, 0xff, 0x32, 0x3f, 0x6a, 0xff, 0x32, 0x3f, 0x69, 0xff, 0x2a, 0x35, 0x60, 0xff, 0x2b, 0x34, 0x5f, 0xff, 0x2e, 0x36, 0x64, 0xff, 0x31, 0x3b, 0x60, 0xff, 0x22, 0x2b, 0x4c, 0xff, 0x12, 0x1e, 0x42, 0xff, 0x24, 0x33, 0x5a, 0xff, 0x29, 0x3a, 0x65, 0xff, 0x20, 0x33, 0x60, 0xff, 0x29, 0x3e, 0x6d, 0xff, 0x27, 0x3d, 0x70, 0xff, 0x23, 0x38, 0x6d, 0xff, 0x28, 0x3d, 0x74, 0xff, 0x28, 0x40, 0x76, 0xff, 0x2a, 0x42, 0x77, 0xff, 0x2b, 0x45, 0x74, 0xff, 0x29, 0x3f, 0x79, 0xff, 0x29, 0x40, 0x7b, 0xff, 0x2d, 0x4e, 0x77, 0xff, 0x43, 0x56, 0x87, 0xff, 0x73, 0x76, 0x92, 0xff, 0xa8, 0x9c, 0x8c, 0xff, 0xba, 0xa4, 0x8d, 0xff, 0xba, 0xa9, 0x98, 0xff, 0xba, 0xac, 0x9d, 0xff, 0xb4, 0xaa, 0x9c, 0xff, 0xb1, 0xa9, 0x9d, 0xff, 0xb5, 0xaa, 0x9e, 0xff, 0xb9, 0xac, 0x9f, 0xff, 0xb4, 0xa7, 0x9a, 0xff, 0xb3, 0xa7, 0x99, 0xff, 0xbc, 0xaf, 0xa1, 0xff, 0xbe, 0xb1, 0xa2, 0xff, 0xba, 0xb0, 0xa1, 0xff, 0xb9, 0xaf, 0xa0, 0xff, 0xb8, 0xad, 0x9c, 0xff, 0xb4, 0xaa, 0x96, 0xff, 0xad, 0xa3, 0x8c, 0xff, 0xac, 0xa1, 0x88, 0xdb, 0xaa, 0xaa, 0x8d, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0x71, 0x5a, 0x41, 0x7d, 0x72, 0x61, 0xfe, 0x76, 0x73, 0x68, 0xff, 0x73, 0x73, 0x6b, 0xff, 0x73, 0x75, 0x6a, 0xff, 0x76, 0x78, 0x6b, 0xff, 0x80, 0x77, 0x65, 0xff, 0x7f, 0x71, 0x5b, 0xff, 0x7c, 0x6e, 0x57, 0xff, 0x7e, 0x70, 0x5b, 0xff, 0x7d, 0x6e, 0x5d, 0xff, 0x7b, 0x6d, 0x5f, 0xff, 0x7b, 0x6a, 0x5e, 0xff, 0x79, 0x67, 0x5e, 0xff, 0x75, 0x67, 0x63, 0xff, 0x6f, 0x67, 0x68, 0xff, 0x6b, 0x68, 0x6b, 0xff, 0x6f, 0x6f, 0x73, 0xff, 0x78, 0x7c, 0x7c, 0xff, 0x83, 0x84, 0x85, 0xff, 0x88, 0x89, 0x8a, 0xff, 0x8d, 0x8d, 0x8b, 0xff, 0x91, 0x93, 0x8a, 0xff, 0x94, 0x98, 0x8b, 0xff, 0x98, 0x9d, 0x8b, 0xff, 0xa0, 0x9e, 0x89, 0xff, 0xa8, 0x9b, 0x80, 0xff, 0x98, 0x8d, 0x6c, 0xff, 0xaf, 0xaf, 0x9d, 0xff, 0xd8, 0xe4, 0xf2, 0xff, 0xdc, 0xe2, 0xf4, 0xff, 0xe8, 0xeb, 0xf8, 0xff, 0xda, 0xe5, 0xf5, 0xff, 0xca, 0xd7, 0xf0, 0xff, 0xcb, 0xd7, 0xf6, 0xff, 0xb8, 0xc4, 0xe7, 0xff, 0xae, 0xbb, 0xe4, 0xff, 0xaa, 0xb9, 0xe4, 0xff, 0x95, 0xa4, 0xcf, 0xff, 0xa5, 0xb4, 0xe1, 0xff, 0x87, 0x97, 0xc5, 0xff, 0x6e, 0x7f, 0xae, 0xff, 0x59, 0x69, 0x98, 0xff, 0x43, 0x54, 0x82, 0xff, 0x43, 0x50, 0x7c, 0xff, 0x39, 0x41, 0x6b, 0xff, 0x2d, 0x33, 0x59, 0xff, 0x37, 0x3c, 0x60, 0xff, 0x38, 0x3b, 0x5f, 0xff, 0x2d, 0x30, 0x52, 0xff, 0x1f, 0x24, 0x41, 0xff, 0x24, 0x28, 0x43, 0xff, 0x24, 0x28, 0x41, 0xff, 0x17, 0x1b, 0x34, 0xff, 0x2d, 0x30, 0x4e, 0xff, 0x26, 0x29, 0x49, 0xff, 0x1d, 0x20, 0x3b, 0xff, 0x1f, 0x22, 0x3c, 0xff, 0x19, 0x21, 0x3b, 0xff, 0x31, 0x3c, 0x57, 0xff, 0x27, 0x33, 0x50, 0xff, 0x21, 0x2b, 0x4c, 0xff, 0x30, 0x3c, 0x5e, 0xff, 0x2d, 0x3b, 0x62, 0xff, 0x33, 0x43, 0x6e, 0xff, 0x35, 0x47, 0x74, 0xff, 0x35, 0x43, 0x6f, 0xff, 0x3c, 0x48, 0x71, 0xff, 0x36, 0x41, 0x6c, 0xff, 0x28, 0x31, 0x5c, 0xff, 0x2e, 0x37, 0x62, 0xff, 0x3d, 0x45, 0x72, 0xff, 0x34, 0x3d, 0x64, 0xff, 0x22, 0x2d, 0x50, 0xff, 0x1b, 0x28, 0x4e, 0xff, 0x27, 0x36, 0x60, 0xff, 0x2c, 0x3d, 0x6a, 0xff, 0x2f, 0x41, 0x70, 0xff, 0x26, 0x3c, 0x6c, 0xff, 0x20, 0x37, 0x69, 0xff, 0x26, 0x3d, 0x72, 0xff, 0x2b, 0x42, 0x79, 0xff, 0x28, 0x41, 0x76, 0xff, 0x28, 0x42, 0x76, 0xff, 0x2a, 0x46, 0x75, 0xff, 0x28, 0x41, 0x7a, 0xff, 0x29, 0x42, 0x7d, 0xff, 0x2c, 0x4d, 0x76, 0xff, 0x3d, 0x51, 0x81, 0xff, 0x73, 0x77, 0x94, 0xff, 0xb9, 0xa4, 0x91, 0xff, 0xce, 0xa8, 0x89, 0xff, 0xbc, 0x9e, 0x86, 0xff, 0xb6, 0x9c, 0x89, 0xff, 0xb2, 0x9c, 0x8c, 0xff, 0xb0, 0x9e, 0x90, 0xff, 0xb2, 0x9f, 0x91, 0xff, 0xb6, 0x9f, 0x92, 0xff, 0xb7, 0xa1, 0x94, 0xff, 0xb9, 0xa4, 0x97, 0xff, 0xbb, 0xa6, 0x99, 0xff, 0xbf, 0xa9, 0x9c, 0xff, 0xbe, 0xab, 0x9e, 0xff, 0xb7, 0xa7, 0x99, 0xff, 0xb5, 0xa3, 0x93, 0xff, 0xb3, 0xa1, 0x90, 0xff, 0xb0, 0x9f, 0x8b, 0xfe, 0xb0, 0x9c, 0x85, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x79, 0x6e, 0x62, 0x8f, 0x72, 0x70, 0x68, 0xff, 0x6f, 0x71, 0x6b, 0xff, 0x73, 0x76, 0x6f, 0xff, 0x76, 0x78, 0x6f, 0xff, 0x7f, 0x76, 0x68, 0xff, 0x81, 0x74, 0x60, 0xff, 0x7e, 0x71, 0x5a, 0xff, 0x81, 0x73, 0x5c, 0xff, 0x7f, 0x70, 0x5e, 0xff, 0x7e, 0x6e, 0x62, 0xff, 0x7e, 0x6d, 0x5f, 0xff, 0x7c, 0x69, 0x5f, 0xff, 0x76, 0x68, 0x65, 0xff, 0x6f, 0x66, 0x68, 0xff, 0x6a, 0x68, 0x6d, 0xff, 0x6d, 0x70, 0x74, 0xff, 0x76, 0x79, 0x7c, 0xff, 0x7f, 0x83, 0x83, 0xff, 0x86, 0x89, 0x89, 0xff, 0x8d, 0x90, 0x8e, 0xff, 0x94, 0x97, 0x90, 0xff, 0x96, 0x9b, 0x8f, 0xff, 0x9d, 0xa0, 0x8e, 0xff, 0xa2, 0x9e, 0x8f, 0xff, 0xa8, 0x9e, 0x89, 0xff, 0x9f, 0x90, 0x6b, 0xff, 0xb0, 0xa9, 0x8e, 0xff, 0xd6, 0xdf, 0xea, 0xff, 0xca, 0xd1, 0xe5, 0xff, 0xd0, 0xd6, 0xe8, 0xff, 0xd3, 0xdf, 0xf7, 0xff, 0xbd, 0xcc, 0xea, 0xff, 0xb9, 0xc9, 0xee, 0xff, 0x9c, 0xaa, 0xd4, 0xff, 0x89, 0x96, 0xc2, 0xff, 0x81, 0x8f, 0xba, 0xff, 0x70, 0x7e, 0xa9, 0xff, 0x89, 0x97, 0xc2, 0xff, 0x6f, 0x7d, 0xa9, 0xff, 0x5b, 0x69, 0x96, 0xff, 0x5e, 0x6a, 0x97, 0xff, 0x59, 0x65, 0x8f, 0xff, 0x5c, 0x65, 0x8e, 0xff, 0x46, 0x4d, 0x75, 0xff, 0x35, 0x3b, 0x61, 0xff, 0x3c, 0x42, 0x65, 0xff, 0x3a, 0x3b, 0x5d, 0xff, 0x24, 0x26, 0x46, 0xff, 0x29, 0x2d, 0x48, 0xff, 0x21, 0x26, 0x3e, 0xff, 0x27, 0x2b, 0x44, 0xff, 0x2f, 0x33, 0x4c, 0xff, 0x37, 0x3c, 0x55, 0xff, 0x29, 0x2c, 0x46, 0xff, 0x26, 0x29, 0x42, 0xff, 0x2b, 0x2f, 0x4a, 0xff, 0x1d, 0x24, 0x43, 0xff, 0x2f, 0x3a, 0x5d, 0xff, 0x33, 0x3f, 0x65, 0xff, 0x31, 0x3e, 0x64, 0xff, 0x2a, 0x36, 0x59, 0xff, 0x2b, 0x35, 0x5a, 0xff, 0x39, 0x45, 0x6d, 0xff, 0x32, 0x41, 0x6d, 0xff, 0x2f, 0x3d, 0x68, 0xff, 0x32, 0x3e, 0x65, 0xff, 0x32, 0x3c, 0x64, 0xff, 0x30, 0x3a, 0x62, 0xff, 0x35, 0x3e, 0x67, 0xff, 0x37, 0x3e, 0x67, 0xff, 0x33, 0x3b, 0x62, 0xff, 0x2e, 0x3b, 0x5e, 0xff, 0x2a, 0x37, 0x5e, 0xff, 0x2b, 0x3a, 0x67, 0xff, 0x23, 0x35, 0x64, 0xff, 0x27, 0x3a, 0x6a, 0xff, 0x29, 0x3e, 0x6e, 0xff, 0x27, 0x3e, 0x70, 0xff, 0x2b, 0x43, 0x78, 0xff, 0x2d, 0x45, 0x7c, 0xff, 0x26, 0x40, 0x75, 0xff, 0x27, 0x42, 0x77, 0xff, 0x2a, 0x46, 0x74, 0xff, 0x29, 0x42, 0x7a, 0xff, 0x29, 0x43, 0x7e, 0xff, 0x27, 0x4c, 0x76, 0xff, 0x35, 0x4b, 0x80, 0xff, 0x69, 0x72, 0x95, 0xff, 0xb2, 0x9f, 0x91, 0xff, 0xcb, 0xa1, 0x86, 0xff, 0xba, 0x9a, 0x86, 0xff, 0xb2, 0x96, 0x85, 0xff, 0xad, 0x93, 0x85, 0xff, 0xab, 0x94, 0x89, 0xff, 0xac, 0x98, 0x8d, 0xff, 0xae, 0x99, 0x91, 0xff, 0xb1, 0x9c, 0x94, 0xff, 0xb4, 0x9f, 0x97, 0xff, 0xb5, 0xa1, 0x99, 0xff, 0xb7, 0xa2, 0x9b, 0xff, 0xb5, 0xa0, 0x9a, 0xff, 0xb2, 0x9f, 0x98, 0xff, 0xb0, 0x9d, 0x94, 0xff, 0xaf, 0x9c, 0x91, 0xff, 0xad, 0x9b, 0x8d, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x6d, 0x6d, 0x07, 0x71, 0x6c, 0x67, 0xd1, 0x6e, 0x6c, 0x6c, 0xff, 0x6f, 0x70, 0x6d, 0xff, 0x75, 0x75, 0x6f, 0xff, 0x80, 0x77, 0x6b, 0xff, 0x83, 0x77, 0x66, 0xff, 0x81, 0x72, 0x5c, 0xff, 0x81, 0x73, 0x5a, 0xff, 0x80, 0x70, 0x5e, 0xff, 0x80, 0x6e, 0x64, 0xff, 0x80, 0x6d, 0x5f, 0xff, 0x7c, 0x69, 0x5d, 0xff, 0x74, 0x68, 0x63, 0xff, 0x6e, 0x66, 0x68, 0xff, 0x6a, 0x67, 0x6d, 0xff, 0x69, 0x6d, 0x72, 0xff, 0x73, 0x78, 0x7a, 0xff, 0x7f, 0x83, 0x84, 0xff, 0x86, 0x89, 0x8b, 0xff, 0x8d, 0x90, 0x90, 0xff, 0x93, 0x97, 0x91, 0xff, 0x94, 0x9c, 0x90, 0xff, 0xa1, 0xa0, 0x8c, 0xff, 0xa0, 0x9b, 0x91, 0xff, 0xa5, 0x9b, 0x8e, 0xff, 0xad, 0x9a, 0x71, 0xff, 0xb1, 0xa4, 0x82, 0xff, 0xca, 0xd1, 0xdd, 0xff, 0xc1, 0xcb, 0xe8, 0xff, 0xbc, 0xc5, 0xdc, 0xff, 0xb7, 0xc5, 0xe1, 0xff, 0xa9, 0xbb, 0xdd, 0xff, 0x98, 0xa8, 0xd2, 0xff, 0x7d, 0x8b, 0xbc, 0xff, 0x73, 0x80, 0xa8, 0xff, 0x7a, 0x86, 0xaa, 0xff, 0x76, 0x81, 0xa9, 0xff, 0x72, 0x7b, 0xa3, 0xff, 0x64, 0x6d, 0x95, 0xff, 0x5d, 0x67, 0x8f, 0xff, 0x5f, 0x66, 0x8f, 0xff, 0x5e, 0x65, 0x8c, 0xff, 0x4a, 0x52, 0x78, 0xff, 0x3a, 0x42, 0x68, 0xff, 0x3d, 0x45, 0x6b, 0xff, 0x3c, 0x45, 0x6a, 0xff, 0x3f, 0x41, 0x62, 0xff, 0x2f, 0x30, 0x4d, 0xff, 0x31, 0x35, 0x4e, 0xff, 0x25, 0x29, 0x40, 0xff, 0x2d, 0x30, 0x49, 0xff, 0x34, 0x37, 0x52, 0xff, 0x27, 0x2c, 0x40, 0xff, 0x1c, 0x21, 0x32, 0xff, 0x2f, 0x33, 0x48, 0xff, 0x31, 0x36, 0x50, 0xff, 0x2c, 0x33, 0x54, 0xff, 0x28, 0x2f, 0x5a, 0xff, 0x2d, 0x3b, 0x69, 0xff, 0x30, 0x41, 0x6a, 0xff, 0x31, 0x3b, 0x5f, 0xff, 0x39, 0x3f, 0x60, 0xff, 0x3a, 0x41, 0x64, 0xff, 0x2c, 0x35, 0x5c, 0xff, 0x32, 0x3f, 0x64, 0xff, 0x2e, 0x3c, 0x5e, 0xff, 0x2a, 0x37, 0x5b, 0xff, 0x34, 0x40, 0x65, 0xff, 0x36, 0x3f, 0x64, 0xff, 0x34, 0x3d, 0x60, 0xff, 0x2e, 0x37, 0x5c, 0xff, 0x28, 0x32, 0x59, 0xff, 0x27, 0x32, 0x5c, 0xff, 0x2a, 0x38, 0x66, 0xff, 0x2c, 0x3e, 0x6e, 0xff, 0x2b, 0x3e, 0x72, 0xff, 0x2a, 0x41, 0x73, 0xff, 0x2d, 0x46, 0x76, 0xff, 0x2a, 0x42, 0x77, 0xff, 0x2a, 0x43, 0x7a, 0xff, 0x25, 0x3f, 0x75, 0xff, 0x27, 0x43, 0x77, 0xff, 0x28, 0x45, 0x71, 0xff, 0x28, 0x40, 0x77, 0xff, 0x26, 0x41, 0x7d, 0xff, 0x23, 0x49, 0x76, 0xff, 0x2f, 0x48, 0x85, 0xff, 0x5e, 0x69, 0x93, 0xff, 0xa6, 0x9b, 0x91, 0xff, 0xb1, 0x96, 0x82, 0xff, 0xa1, 0x8d, 0x80, 0xff, 0x9a, 0x89, 0x7d, 0xff, 0x96, 0x86, 0x7b, 0xff, 0x99, 0x8a, 0x7e, 0xff, 0x9a, 0x90, 0x87, 0xff, 0x9a, 0x94, 0x8d, 0xff, 0x9e, 0x95, 0x8f, 0xff, 0xa1, 0x98, 0x94, 0xff, 0xa3, 0x9a, 0x98, 0xff, 0xa4, 0x9b, 0x9a, 0xff, 0xa4, 0x9b, 0x9a, 0xff, 0xa6, 0x9c, 0x9a, 0xff, 0xa3, 0x9a, 0x95, 0xff, 0x9f, 0x98, 0x8f, 0xd1, 0x9f, 0x9f, 0x7f, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x6d, 0x61, 0x2a, 0x68, 0x6b, 0x65, 0xf3, 0x69, 0x6b, 0x64, 0xff, 0x6f, 0x72, 0x69, 0xff, 0x7d, 0x77, 0x69, 0xff, 0x82, 0x78, 0x65, 0xff, 0x7f, 0x74, 0x5f, 0xff, 0x7f, 0x71, 0x5b, 0xff, 0x7e, 0x6f, 0x5c, 0xff, 0x7c, 0x6f, 0x60, 0xff, 0x7f, 0x6e, 0x5f, 0xff, 0x7b, 0x69, 0x5e, 0xff, 0x74, 0x66, 0x61, 0xff, 0x6d, 0x62, 0x63, 0xff, 0x67, 0x64, 0x69, 0xff, 0x67, 0x69, 0x71, 0xff, 0x71, 0x74, 0x79, 0xff, 0x7a, 0x7d, 0x82, 0xff, 0x7f, 0x83, 0x86, 0xff, 0x85, 0x8b, 0x8a, 0xff, 0x8d, 0x90, 0x8d, 0xff, 0x91, 0x94, 0x8e, 0xff, 0x9b, 0x99, 0x8c, 0xff, 0xa0, 0x9a, 0x8a, 0xff, 0xa7, 0x99, 0x85, 0xff, 0xae, 0x9b, 0x79, 0xff, 0x95, 0x86, 0x67, 0xff, 0xc3, 0xbf, 0xb1, 0xff, 0xd6, 0xdf, 0xf0, 0xff, 0xa2, 0xb1, 0xca, 0xff, 0xa4, 0xb4, 0xcd, 0xff, 0x9b, 0xaa, 0xc9, 0xff, 0x84, 0x91, 0xb4, 0xff, 0x7e, 0x89, 0xb1, 0xff, 0x69, 0x76, 0xa0, 0xff, 0x6d, 0x7a, 0xa2, 0xff, 0x6b, 0x73, 0x98, 0xff, 0x58, 0x5e, 0x82, 0xff, 0x60, 0x67, 0x8c, 0xff, 0x6a, 0x71, 0x98, 0xff, 0x5a, 0x62, 0x89, 0xff, 0x4e, 0x56, 0x7c, 0xff, 0x3f, 0x47, 0x6d, 0xff, 0x44, 0x4d, 0x73, 0xff, 0x52, 0x5a, 0x80, 0xff, 0x49, 0x52, 0x7a, 0xff, 0x41, 0x45, 0x68, 0xff, 0x3f, 0x40, 0x5c, 0xff, 0x3c, 0x40, 0x58, 0xff, 0x2f, 0x35, 0x4a, 0xff, 0x2e, 0x33, 0x49, 0xff, 0x35, 0x3b, 0x52, 0xff, 0x2b, 0x30, 0x48, 0xff, 0x29, 0x2f, 0x48, 0xff, 0x34, 0x3b, 0x57, 0xff, 0x3d, 0x45, 0x65, 0xff, 0x34, 0x3e, 0x62, 0xff, 0x26, 0x33, 0x5c, 0xff, 0x2b, 0x3a, 0x62, 0xff, 0x27, 0x35, 0x59, 0xff, 0x3c, 0x47, 0x6b, 0xff, 0x3e, 0x47, 0x6a, 0xff, 0x42, 0x4b, 0x6e, 0xff, 0x3b, 0x44, 0x6a, 0xff, 0x39, 0x43, 0x69, 0xff, 0x38, 0x43, 0x68, 0xff, 0x37, 0x41, 0x64, 0xff, 0x35, 0x3e, 0x5f, 0xff, 0x2c, 0x36, 0x59, 0xff, 0x2f, 0x39, 0x5f, 0xff, 0x32, 0x3f, 0x66, 0xff, 0x24, 0x32, 0x5b, 0xff, 0x26, 0x37, 0x61, 0xff, 0x31, 0x44, 0x6f, 0xff, 0x31, 0x43, 0x72, 0xff, 0x28, 0x3d, 0x6d, 0xff, 0x2a, 0x43, 0x74, 0xff, 0x2d, 0x46, 0x79, 0xff, 0x2a, 0x41, 0x76, 0xff, 0x27, 0x3f, 0x75, 0xff, 0x2a, 0x42, 0x79, 0xff, 0x29, 0x42, 0x78, 0xff, 0x24, 0x40, 0x6f, 0xff, 0x23, 0x3f, 0x73, 0xff, 0x26, 0x42, 0x7c, 0xff, 0x28, 0x47, 0x7d, 0xff, 0x30, 0x49, 0x7f, 0xff, 0x4c, 0x62, 0x91, 0xff, 0x92, 0x93, 0x9e, 0xff, 0xa0, 0x8b, 0x7a, 0xff, 0x88, 0x77, 0x64, 0xff, 0x80, 0x76, 0x64, 0xff, 0x76, 0x70, 0x69, 0xff, 0x7a, 0x70, 0x6f, 0xff, 0x75, 0x75, 0x73, 0xff, 0x6e, 0x78, 0x77, 0xff, 0x71, 0x79, 0x7a, 0xff, 0x76, 0x7d, 0x80, 0xff, 0x7b, 0x80, 0x83, 0xff, 0x7d, 0x82, 0x82, 0xff, 0x81, 0x83, 0x85, 0xff, 0x86, 0x86, 0x89, 0xff, 0x83, 0x85, 0x85, 0xf4, 0x7f, 0x7f, 0x7f, 0x2a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x69, 0x60, 0x55, 0x64, 0x6a, 0x61, 0xfe, 0x6a, 0x6f, 0x65, 0xff, 0x77, 0x73, 0x63, 0xff, 0x7c, 0x74, 0x61, 0xff, 0x7d, 0x72, 0x5f, 0xff, 0x7f, 0x70, 0x5e, 0xff, 0x7d, 0x6f, 0x5d, 0xff, 0x7b, 0x6f, 0x5e, 0xff, 0x7c, 0x6d, 0x5f, 0xff, 0x79, 0x69, 0x5e, 0xff, 0x75, 0x64, 0x5e, 0xff, 0x6b, 0x5f, 0x5e, 0xff, 0x65, 0x5f, 0x64, 0xff, 0x64, 0x65, 0x6d, 0xff, 0x6b, 0x6c, 0x74, 0xff, 0x72, 0x73, 0x79, 0xff, 0x76, 0x7a, 0x7c, 0xff, 0x7c, 0x82, 0x81, 0xff, 0x84, 0x87, 0x85, 0xff, 0x8b, 0x8a, 0x88, 0xff, 0x91, 0x8f, 0x88, 0xff, 0x9c, 0x94, 0x82, 0xff, 0xa7, 0x97, 0x7b, 0xff, 0xa8, 0x95, 0x78, 0xff, 0x97, 0x87, 0x6a, 0xff, 0x9e, 0x91, 0x6c, 0xff, 0xda, 0xdb, 0xd5, 0xff, 0xb2, 0xbe, 0xcd, 0xff, 0x98, 0xa6, 0xb8, 0xff, 0x91, 0xa2, 0xbc, 0xff, 0x86, 0x95, 0xb5, 0xff, 0x62, 0x6b, 0x8d, 0xff, 0x59, 0x68, 0x8b, 0xff, 0x64, 0x73, 0x95, 0xff, 0x50, 0x58, 0x74, 0xff, 0x4f, 0x56, 0x70, 0xff, 0x58, 0x5e, 0x7b, 0xff, 0x5e, 0x67, 0x87, 0xff, 0x63, 0x6c, 0x8a, 0xff, 0x4d, 0x56, 0x70, 0xff, 0x53, 0x5c, 0x77, 0xff, 0x59, 0x61, 0x7d, 0xff, 0x4d, 0x56, 0x71, 0xff, 0x42, 0x4b, 0x67, 0xff, 0x46, 0x4c, 0x69, 0xff, 0x34, 0x38, 0x54, 0xff, 0x3e, 0x43, 0x5b, 0xff, 0x39, 0x40, 0x56, 0xff, 0x1d, 0x23, 0x39, 0xff, 0x2d, 0x33, 0x4a, 0xff, 0x51, 0x57, 0x76, 0xff, 0x43, 0x49, 0x6e, 0xff, 0x33, 0x3b, 0x5f, 0xff, 0x39, 0x44, 0x66, 0xff, 0x2d, 0x3a, 0x5d, 0xff, 0x30, 0x3e, 0x61, 0xff, 0x32, 0x3e, 0x5f, 0xff, 0x39, 0x44, 0x66, 0xff, 0x3f, 0x4b, 0x6f, 0xff, 0x30, 0x3d, 0x61, 0xff, 0x34, 0x40, 0x66, 0xff, 0x3a, 0x47, 0x6c, 0xff, 0x43, 0x4d, 0x75, 0xff, 0x46, 0x4e, 0x75, 0xff, 0x36, 0x3d, 0x60, 0xff, 0x3a, 0x42, 0x62, 0xff, 0x32, 0x3d, 0x5f, 0xff, 0x2b, 0x35, 0x5d, 0xff, 0x2b, 0x3b, 0x63, 0xff, 0x21, 0x33, 0x5c, 0xff, 0x23, 0x36, 0x60, 0xff, 0x29, 0x3d, 0x68, 0xff, 0x29, 0x3e, 0x6a, 0xff, 0x2c, 0x41, 0x6e, 0xff, 0x31, 0x4a, 0x7a, 0xff, 0x2f, 0x48, 0x7d, 0xff, 0x2d, 0x45, 0x78, 0xff, 0x2c, 0x44, 0x78, 0xff, 0x2b, 0x43, 0x79, 0xff, 0x27, 0x3e, 0x75, 0xff, 0x25, 0x41, 0x74, 0xff, 0x20, 0x41, 0x72, 0xff, 0x26, 0x42, 0x7a, 0xff, 0x2c, 0x45, 0x80, 0xff, 0x31, 0x49, 0x79, 0xff, 0x3a, 0x59, 0x89, 0xff, 0x7e, 0x85, 0x9e, 0xff, 0x9a, 0x84, 0x75, 0xff, 0x83, 0x71, 0x57, 0xff, 0x7a, 0x6e, 0x56, 0xff, 0x6a, 0x67, 0x5f, 0xff, 0x69, 0x62, 0x66, 0xff, 0x63, 0x63, 0x66, 0xff, 0x59, 0x65, 0x68, 0xff, 0x5a, 0x65, 0x6b, 0xff, 0x5d, 0x69, 0x6f, 0xff, 0x64, 0x6c, 0x71, 0xff, 0x6b, 0x6e, 0x6f, 0xff, 0x6f, 0x73, 0x74, 0xff, 0x70, 0x76, 0x78, 0xff, 0x6d, 0x73, 0x73, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5f, 0x65, 0x5f, 0x8d, 0x66, 0x6b, 0x66, 0xff, 0x75, 0x70, 0x64, 0xff, 0x7a, 0x70, 0x5f, 0xff, 0x7e, 0x72, 0x60, 0xff, 0x7f, 0x6f, 0x5d, 0xff, 0x7e, 0x6f, 0x5f, 0xff, 0x7d, 0x6d, 0x60, 0xff, 0x7b, 0x6a, 0x5d, 0xff, 0x78, 0x66, 0x5b, 0xff, 0x73, 0x62, 0x5d, 0xff, 0x6b, 0x5e, 0x5e, 0xff, 0x60, 0x5a, 0x5f, 0xff, 0x5f, 0x5e, 0x67, 0xff, 0x66, 0x66, 0x6e, 0xff, 0x6b, 0x6d, 0x72, 0xff, 0x6f, 0x72, 0x76, 0xff, 0x75, 0x79, 0x79, 0xff, 0x7b, 0x7f, 0x7d, 0xff, 0x82, 0x83, 0x81, 0xff, 0x89, 0x87, 0x80, 0xff, 0x93, 0x8b, 0x7e, 0xff, 0xa0, 0x8f, 0x79, 0xff, 0xa7, 0x90, 0x71, 0xff, 0xa3, 0x8d, 0x6a, 0xff, 0x89, 0x76, 0x51, 0xff, 0x9f, 0x90, 0x79, 0xff, 0xc9, 0xc5, 0xc0, 0xff, 0x89, 0x92, 0x9d, 0xff, 0x7f, 0x92, 0xac, 0xff, 0x7e, 0x8f, 0xaf, 0xff, 0x61, 0x69, 0x89, 0xff, 0x5b, 0x67, 0x7c, 0xff, 0x54, 0x61, 0x72, 0xff, 0x4b, 0x55, 0x66, 0xff, 0x47, 0x50, 0x60, 0xff, 0x2c, 0x35, 0x47, 0xff, 0x3e, 0x48, 0x5e, 0xff, 0x3f, 0x48, 0x59, 0xff, 0x33, 0x3a, 0x4a, 0xff, 0x42, 0x49, 0x5a, 0xff, 0x47, 0x4e, 0x5f, 0xff, 0x43, 0x4a, 0x5b, 0xff, 0x2e, 0x36, 0x44, 0xff, 0x2d, 0x33, 0x4c, 0xff, 0x38, 0x3f, 0x5b, 0xff, 0x33, 0x3b, 0x54, 0xff, 0x39, 0x40, 0x5a, 0xff, 0x34, 0x3a, 0x57, 0xff, 0x40, 0x44, 0x64, 0xff, 0x3f, 0x47, 0x6c, 0xff, 0x32, 0x3d, 0x63, 0xff, 0x3d, 0x46, 0x6b, 0xff, 0x36, 0x40, 0x61, 0xff, 0x35, 0x3f, 0x60, 0xff, 0x3c, 0x45, 0x66, 0xff, 0x3d, 0x49, 0x6c, 0xff, 0x3c, 0x49, 0x6e, 0xff, 0x36, 0x41, 0x65, 0xff, 0x33, 0x3e, 0x64, 0xff, 0x31, 0x3d, 0x62, 0xff, 0x3b, 0x46, 0x6c, 0xff, 0x40, 0x4b, 0x72, 0xff, 0x3f, 0x49, 0x6f, 0xff, 0x3b, 0x43, 0x68, 0xff, 0x43, 0x4b, 0x70, 0xff, 0x35, 0x3f, 0x64, 0xff, 0x2c, 0x39, 0x5f, 0xff, 0x1d, 0x2d, 0x54, 0xff, 0x21, 0x32, 0x5b, 0xff, 0x25, 0x37, 0x61, 0xff, 0x2e, 0x41, 0x6c, 0xff, 0x2a, 0x3e, 0x6b, 0xff, 0x2d, 0x42, 0x70, 0xff, 0x32, 0x4b, 0x7b, 0xff, 0x31, 0x4b, 0x7d, 0xff, 0x2e, 0x47, 0x79, 0xff, 0x2a, 0x42, 0x76, 0xff, 0x28, 0x40, 0x75, 0xff, 0x24, 0x3c, 0x73, 0xff, 0x27, 0x42, 0x75, 0xff, 0x27, 0x45, 0x76, 0xff, 0x27, 0x42, 0x7a, 0xff, 0x29, 0x44, 0x7c, 0xff, 0x32, 0x4a, 0x7b, 0xff, 0x35, 0x51, 0x82, 0xff, 0x72, 0x7a, 0x94, 0xff, 0x9c, 0x8b, 0x83, 0xff, 0x85, 0x75, 0x5f, 0xff, 0x7a, 0x6d, 0x55, 0xff, 0x6a, 0x67, 0x5a, 0xff, 0x66, 0x65, 0x61, 0xff, 0x62, 0x65, 0x64, 0xff, 0x5e, 0x64, 0x66, 0xff, 0x60, 0x67, 0x6a, 0xff, 0x65, 0x6c, 0x6f, 0xff, 0x6d, 0x71, 0x73, 0xff, 0x77, 0x74, 0x74, 0xff, 0x7d, 0x7c, 0x79, 0xff, 0x79, 0x7e, 0x79, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 0x55, 0x03, 0x61, 0x66, 0x63, 0xb1, 0x72, 0x6c, 0x61, 0xff, 0x79, 0x6f, 0x5f, 0xff, 0x7d, 0x71, 0x60, 0xff, 0x7f, 0x6e, 0x5d, 0xff, 0x80, 0x6d, 0x5e, 0xff, 0x7e, 0x6d, 0x60, 0xff, 0x79, 0x69, 0x5c, 0xff, 0x77, 0x64, 0x5a, 0xff, 0x73, 0x61, 0x5c, 0xff, 0x69, 0x5b, 0x5b, 0xff, 0x5c, 0x56, 0x5b, 0xff, 0x5a, 0x59, 0x61, 0xff, 0x62, 0x60, 0x6a, 0xff, 0x67, 0x66, 0x6e, 0xff, 0x69, 0x6b, 0x70, 0xff, 0x6d, 0x70, 0x72, 0xff, 0x73, 0x75, 0x74, 0xff, 0x79, 0x78, 0x77, 0xff, 0x80, 0x7e, 0x76, 0xff, 0x89, 0x82, 0x76, 0xff, 0x94, 0x82, 0x6f, 0xff, 0x9d, 0x84, 0x66, 0xff, 0x9c, 0x82, 0x5f, 0xff, 0x95, 0x7c, 0x55, 0xff, 0x85, 0x6a, 0x44, 0xff, 0x94, 0x80, 0x67, 0xff, 0x89, 0x87, 0x82, 0xff, 0x58, 0x62, 0x6f, 0xff, 0x3c, 0x46, 0x5b, 0xff, 0x47, 0x4b, 0x61, 0xff, 0x5a, 0x60, 0x72, 0xff, 0x56, 0x5e, 0x70, 0xff, 0x40, 0x47, 0x5b, 0xff, 0x55, 0x5c, 0x72, 0xff, 0x47, 0x4e, 0x66, 0xff, 0x2e, 0x36, 0x50, 0xff, 0x31, 0x37, 0x4e, 0xff, 0x31, 0x36, 0x4b, 0xff, 0x2a, 0x2f, 0x45, 0xff, 0x33, 0x39, 0x4e, 0xff, 0x3e, 0x44, 0x59, 0xff, 0x2c, 0x31, 0x45, 0xff, 0x37, 0x3f, 0x5a, 0xff, 0x50, 0x5b, 0x79, 0xff, 0x2e, 0x39, 0x54, 0xff, 0x3c, 0x46, 0x63, 0xff, 0x49, 0x52, 0x72, 0xff, 0x50, 0x57, 0x7e, 0xff, 0x30, 0x3c, 0x64, 0xff, 0x31, 0x40, 0x66, 0xff, 0x41, 0x4d, 0x73, 0xff, 0x3a, 0x44, 0x68, 0xff, 0x46, 0x4f, 0x71, 0xff, 0x49, 0x50, 0x70, 0xff, 0x3b, 0x45, 0x67, 0xff, 0x2e, 0x3b, 0x5f, 0xff, 0x32, 0x3d, 0x63, 0xff, 0x4d, 0x58, 0x7e, 0xff, 0x43, 0x4e, 0x75, 0xff, 0x39, 0x42, 0x6b, 0xff, 0x38, 0x43, 0x6b, 0xff, 0x37, 0x43, 0x6a, 0xff, 0x3f, 0x49, 0x6f, 0xff, 0x3e, 0x47, 0x6e, 0xff, 0x3a, 0x44, 0x6b, 0xff, 0x29, 0x38, 0x5f, 0xff, 0x26, 0x37, 0x5e, 0xff, 0x2d, 0x3e, 0x67, 0xff, 0x2b, 0x3d, 0x67, 0xff, 0x32, 0x45, 0x70, 0xff, 0x2d, 0x41, 0x6e, 0xff, 0x32, 0x47, 0x76, 0xff, 0x34, 0x4b, 0x7b, 0xff, 0x34, 0x4c, 0x7e, 0xff, 0x30, 0x48, 0x79, 0xff, 0x2b, 0x42, 0x75, 0xff, 0x28, 0x3f, 0x73, 0xff, 0x29, 0x3f, 0x74, 0xff, 0x26, 0x3f, 0x73, 0xff, 0x24, 0x3f, 0x72, 0xff, 0x24, 0x41, 0x76, 0xff, 0x26, 0x43, 0x76, 0xff, 0x2c, 0x46, 0x76, 0xff, 0x31, 0x4c, 0x7e, 0xff, 0x62, 0x71, 0x93, 0xff, 0x98, 0x91, 0x95, 0xff, 0x86, 0x76, 0x68, 0xff, 0x80, 0x71, 0x5a, 0xff, 0x6f, 0x6b, 0x5d, 0xff, 0x65, 0x67, 0x61, 0xff, 0x64, 0x67, 0x64, 0xff, 0x64, 0x66, 0x67, 0xff, 0x6a, 0x6c, 0x6c, 0xff, 0x74, 0x75, 0x76, 0xff, 0x7e, 0x7d, 0x7c, 0xff, 0x88, 0x82, 0x7d, 0xff, 0x91, 0x87, 0x80, 0xb1, 0xaa, 0xaa, 0xaa, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x66, 0x66, 0x66, 0x0a, 0x6c, 0x68, 0x5a, 0xc8, 0x76, 0x6f, 0x5b, 0xff, 0x7d, 0x72, 0x5e, 0xff, 0x80, 0x6f, 0x5c, 0xff, 0x7e, 0x6b, 0x59, 0xff, 0x7b, 0x68, 0x59, 0xff, 0x79, 0x67, 0x59, 0xff, 0x76, 0x63, 0x5a, 0xff, 0x73, 0x5f, 0x5b, 0xff, 0x69, 0x59, 0x59, 0xff, 0x5b, 0x53, 0x59, 0xff, 0x58, 0x55, 0x5e, 0xff, 0x5d, 0x5a, 0x65, 0xff, 0x62, 0x60, 0x69, 0xff, 0x64, 0x65, 0x6a, 0xff, 0x67, 0x69, 0x6b, 0xff, 0x6e, 0x6d, 0x6d, 0xff, 0x71, 0x6f, 0x6f, 0xff, 0x76, 0x71, 0x6a, 0xff, 0x7f, 0x74, 0x6a, 0xff, 0x87, 0x75, 0x66, 0xff, 0x8d, 0x76, 0x5d, 0xff, 0x91, 0x76, 0x57, 0xff, 0x8f, 0x74, 0x50, 0xff, 0x8f, 0x71, 0x49, 0xff, 0x82, 0x67, 0x42, 0xff, 0x74, 0x63, 0x48, 0xff, 0x6a, 0x62, 0x54, 0xff, 0x5a, 0x57, 0x56, 0xff, 0x54, 0x53, 0x5a, 0xff, 0x70, 0x71, 0x82, 0xff, 0x58, 0x5b, 0x72, 0xff, 0x52, 0x58, 0x73, 0xff, 0x68, 0x6f, 0x8e, 0xff, 0x53, 0x5b, 0x7c, 0xff, 0x4e, 0x56, 0x77, 0xff, 0x54, 0x5b, 0x7b, 0xff, 0x53, 0x5a, 0x7a, 0xff, 0x46, 0x4c, 0x6d, 0xff, 0x3f, 0x46, 0x67, 0xff, 0x3c, 0x43, 0x63, 0xff, 0x43, 0x49, 0x69, 0xff, 0x55, 0x60, 0x82, 0xff, 0x46, 0x54, 0x75, 0xff, 0x35, 0x43, 0x62, 0xff, 0x4d, 0x5a, 0x79, 0xff, 0x42, 0x4e, 0x70, 0xff, 0x3f, 0x4b, 0x70, 0xff, 0x41, 0x4d, 0x74, 0xff, 0x42, 0x50, 0x77, 0xff, 0x43, 0x50, 0x75, 0xff, 0x3e, 0x48, 0x6c, 0xff, 0x3c, 0x44, 0x6a, 0xff, 0x46, 0x4f, 0x71, 0xff, 0x38, 0x42, 0x65, 0xff, 0x38, 0x44, 0x6a, 0xff, 0x3d, 0x48, 0x6f, 0xff, 0x48, 0x52, 0x79, 0xff, 0x3d, 0x47, 0x6f, 0xff, 0x3c, 0x45, 0x6e, 0xff, 0x38, 0x44, 0x6c, 0xff, 0x38, 0x46, 0x6d, 0xff, 0x3d, 0x47, 0x73, 0xff, 0x3e, 0x47, 0x74, 0xff, 0x34, 0x40, 0x6b, 0xff, 0x1d, 0x2e, 0x55, 0xff, 0x26, 0x38, 0x5e, 0xff, 0x36, 0x46, 0x6f, 0xff, 0x29, 0x3c, 0x66, 0xff, 0x28, 0x3b, 0x66, 0xff, 0x32, 0x46, 0x73, 0xff, 0x38, 0x4d, 0x7c, 0xff, 0x30, 0x47, 0x76, 0xff, 0x33, 0x4a, 0x7a, 0xff, 0x31, 0x47, 0x79, 0xff, 0x2e, 0x45, 0x77, 0xff, 0x2d, 0x43, 0x76, 0xff, 0x2d, 0x41, 0x75, 0xff, 0x26, 0x3d, 0x71, 0xff, 0x21, 0x3c, 0x6f, 0xff, 0x24, 0x41, 0x72, 0xff, 0x24, 0x41, 0x73, 0xff, 0x2b, 0x44, 0x74, 0xff, 0x2f, 0x48, 0x7b, 0xff, 0x44, 0x5a, 0x86, 0xff, 0x8e, 0x96, 0xa9, 0xff, 0x96, 0x8a, 0x84, 0xff, 0x81, 0x70, 0x5d, 0xff, 0x75, 0x6d, 0x5f, 0xff, 0x6a, 0x69, 0x63, 0xff, 0x69, 0x67, 0x65, 0xff, 0x6a, 0x67, 0x68, 0xff, 0x70, 0x6d, 0x6d, 0xff, 0x7b, 0x77, 0x77, 0xff, 0x88, 0x7f, 0x7c, 0xff, 0x93, 0x88, 0x7f, 0xc8, 0x99, 0x99, 0x7f, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x6d, 0x55, 0x15, 0x70, 0x6e, 0x55, 0xd7, 0x7a, 0x6f, 0x59, 0xff, 0x7e, 0x6d, 0x57, 0xff, 0x7d, 0x69, 0x55, 0xff, 0x7a, 0x66, 0x53, 0xff, 0x77, 0x62, 0x55, 0xff, 0x75, 0x61, 0x58, 0xff, 0x72, 0x5e, 0x59, 0xff, 0x68, 0x58, 0x58, 0xff, 0x5a, 0x53, 0x59, 0xff, 0x54, 0x53, 0x5c, 0xff, 0x59, 0x55, 0x62, 0xff, 0x5c, 0x59, 0x63, 0xff, 0x5b, 0x5b, 0x62, 0xff, 0x5f, 0x61, 0x64, 0xff, 0x67, 0x67, 0x68, 0xff, 0x68, 0x67, 0x67, 0xff, 0x6f, 0x67, 0x60, 0xff, 0x77, 0x69, 0x5e, 0xff, 0x78, 0x69, 0x5c, 0xff, 0x7d, 0x67, 0x56, 0xff, 0x82, 0x6a, 0x52, 0xff, 0x81, 0x6b, 0x4d, 0xff, 0x82, 0x6a, 0x4a, 0xff, 0x86, 0x6a, 0x43, 0xff, 0x80, 0x61, 0x32, 0xff, 0x7a, 0x5d, 0x33, 0xff, 0x7a, 0x69, 0x54, 0xff, 0x8a, 0x84, 0x82, 0xff, 0x8e, 0x90, 0xa0, 0xff, 0x7b, 0x82, 0x99, 0xff, 0x6b, 0x74, 0x8d, 0xff, 0x63, 0x70, 0x90, 0xff, 0x59, 0x65, 0x87, 0xff, 0x63, 0x6e, 0x8b, 0xff, 0x56, 0x62, 0x82, 0xff, 0x5b, 0x69, 0x8d, 0xff, 0x57, 0x64, 0x87, 0xff, 0x5b, 0x69, 0x8b, 0xff, 0x37, 0x44, 0x66, 0xff, 0x41, 0x4e, 0x6f, 0xff, 0x56, 0x62, 0x8a, 0xff, 0x36, 0x42, 0x6c, 0xff, 0x51, 0x5f, 0x83, 0xff, 0x45, 0x54, 0x75, 0xff, 0x42, 0x51, 0x72, 0xff, 0x3f, 0x4d, 0x72, 0xff, 0x4c, 0x57, 0x7c, 0xff, 0x56, 0x61, 0x84, 0xff, 0x46, 0x52, 0x75, 0xff, 0x44, 0x50, 0x74, 0xff, 0x44, 0x4f, 0x76, 0xff, 0x39, 0x43, 0x6c, 0xff, 0x40, 0x4a, 0x71, 0xff, 0x39, 0x44, 0x6a, 0xff, 0x3b, 0x45, 0x6c, 0xff, 0x3a, 0x43, 0x6c, 0xff, 0x3a, 0x44, 0x6c, 0xff, 0x3e, 0x46, 0x71, 0xff, 0x34, 0x41, 0x69, 0xff, 0x40, 0x50, 0x76, 0xff, 0x48, 0x51, 0x80, 0xff, 0x35, 0x3e, 0x70, 0xff, 0x26, 0x32, 0x60, 0xff, 0x22, 0x35, 0x5c, 0xff, 0x2b, 0x3e, 0x64, 0xff, 0x33, 0x43, 0x6c, 0xff, 0x31, 0x44, 0x6d, 0xff, 0x32, 0x46, 0x70, 0xff, 0x2f, 0x44, 0x70, 0xff, 0x31, 0x46, 0x74, 0xff, 0x32, 0x4a, 0x78, 0xff, 0x32, 0x49, 0x79, 0xff, 0x30, 0x47, 0x77, 0xff, 0x32, 0x49, 0x7b, 0xff, 0x2f, 0x46, 0x79, 0xff, 0x29, 0x3f, 0x72, 0xff, 0x29, 0x3f, 0x73, 0xff, 0x27, 0x40, 0x72, 0xff, 0x27, 0x42, 0x74, 0xff, 0x24, 0x41, 0x70, 0xff, 0x28, 0x44, 0x72, 0xff, 0x2d, 0x43, 0x77, 0xff, 0x2a, 0x46, 0x79, 0xff, 0x75, 0x8a, 0xa9, 0xff, 0xb7, 0xae, 0xaa, 0xff, 0x8c, 0x75, 0x64, 0xff, 0x74, 0x64, 0x59, 0xff, 0x6f, 0x68, 0x65, 0xff, 0x6b, 0x65, 0x65, 0xff, 0x6b, 0x67, 0x65, 0xff, 0x71, 0x6d, 0x6d, 0xff, 0x7a, 0x73, 0x72, 0xff, 0x86, 0x7a, 0x74, 0xd8, 0x9d, 0x85, 0x79, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x76, 0x6d, 0x51, 0x1c, 0x79, 0x6c, 0x53, 0xdc, 0x7e, 0x6d, 0x53, 0xff, 0x7c, 0x69, 0x51, 0xff, 0x79, 0x66, 0x50, 0xff, 0x75, 0x62, 0x52, 0xff, 0x74, 0x60, 0x56, 0xff, 0x71, 0x5e, 0x59, 0xff, 0x67, 0x58, 0x58, 0xff, 0x5d, 0x54, 0x58, 0xff, 0x59, 0x53, 0x58, 0xff, 0x59, 0x53, 0x5a, 0xff, 0x56, 0x54, 0x5b, 0xff, 0x52, 0x52, 0x58, 0xff, 0x56, 0x54, 0x5a, 0xff, 0x5d, 0x5a, 0x5c, 0xff, 0x5f, 0x5b, 0x5c, 0xff, 0x62, 0x5b, 0x58, 0xff, 0x6d, 0x5f, 0x58, 0xff, 0x79, 0x63, 0x56, 0xff, 0x80, 0x69, 0x55, 0xff, 0x86, 0x6f, 0x54, 0xff, 0x8b, 0x6f, 0x50, 0xff, 0x89, 0x6d, 0x4b, 0xff, 0x86, 0x69, 0x3f, 0xff, 0x83, 0x62, 0x34, 0xff, 0x7e, 0x5e, 0x33, 0xff, 0x6e, 0x56, 0x37, 0xff, 0x63, 0x53, 0x3d, 0xff, 0x73, 0x6b, 0x64, 0xff, 0x99, 0x9b, 0xa4, 0xff, 0x91, 0x9d, 0xb1, 0xff, 0x7a, 0x88, 0xa1, 0xff, 0x6f, 0x7b, 0x94, 0xff, 0x78, 0x84, 0x9f, 0xff, 0x6e, 0x7c, 0x9c, 0xff, 0x5d, 0x6c, 0x8d, 0xff, 0x61, 0x71, 0x91, 0xff, 0x5f, 0x6f, 0x92, 0xff, 0x51, 0x62, 0x85, 0xff, 0x4a, 0x5d, 0x7f, 0xff, 0x5a, 0x67, 0x90, 0xff, 0x4c, 0x57, 0x81, 0xff, 0x53, 0x60, 0x86, 0xff, 0x51, 0x5f, 0x83, 0xff, 0x5b, 0x6a, 0x8c, 0xff, 0x51, 0x5f, 0x84, 0xff, 0x48, 0x56, 0x77, 0xff, 0x4c, 0x58, 0x79, 0xff, 0x41, 0x4c, 0x71, 0xff, 0x44, 0x4e, 0x75, 0xff, 0x47, 0x50, 0x79, 0xff, 0x3c, 0x47, 0x6c, 0xff, 0x44, 0x4f, 0x75, 0xff, 0x45, 0x51, 0x78, 0xff, 0x3d, 0x48, 0x70, 0xff, 0x30, 0x3c, 0x64, 0xff, 0x35, 0x40, 0x69, 0xff, 0x3b, 0x44, 0x6e, 0xff, 0x3d, 0x4c, 0x75, 0xff, 0x38, 0x48, 0x72, 0xff, 0x2b, 0x37, 0x63, 0xff, 0x21, 0x2d, 0x59, 0xff, 0x23, 0x30, 0x5b, 0xff, 0x28, 0x38, 0x5f, 0xff, 0x33, 0x44, 0x6b, 0xff, 0x2f, 0x40, 0x6a, 0xff, 0x2e, 0x40, 0x6a, 0xff, 0x31, 0x44, 0x70, 0xff, 0x36, 0x4a, 0x78, 0xff, 0x31, 0x47, 0x74, 0xff, 0x31, 0x48, 0x77, 0xff, 0x36, 0x4c, 0x7c, 0xff, 0x33, 0x4a, 0x7a, 0xff, 0x31, 0x48, 0x79, 0xff, 0x2b, 0x42, 0x75, 0xff, 0x28, 0x40, 0x71, 0xff, 0x28, 0x3f, 0x72, 0xff, 0x29, 0x41, 0x74, 0xff, 0x28, 0x42, 0x73, 0xff, 0x25, 0x3f, 0x70, 0xff, 0x26, 0x41, 0x72, 0xff, 0x2b, 0x44, 0x77, 0xff, 0x2c, 0x48, 0x79, 0xff, 0x53, 0x68, 0x8d, 0xff, 0xaf, 0xb1, 0xbf, 0xff, 0xad, 0xa3, 0xa2, 0xff, 0x79, 0x6d, 0x69, 0xff, 0x6f, 0x64, 0x61, 0xff, 0x6b, 0x65, 0x64, 0xff, 0x67, 0x67, 0x66, 0xff, 0x72, 0x6b, 0x69, 0xff, 0x7b, 0x72, 0x6d, 0xdd, 0x88, 0x7f, 0x76, 0x1c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0x6c, 0x4d, 0x21, 0x80, 0x6f, 0x50, 0xde, 0x7e, 0x6a, 0x4f, 0xff, 0x78, 0x64, 0x4e, 0xff, 0x73, 0x61, 0x50, 0xff, 0x73, 0x5f, 0x53, 0xff, 0x6e, 0x5c, 0x57, 0xff, 0x65, 0x57, 0x57, 0xff, 0x5e, 0x51, 0x54, 0xff, 0x5a, 0x50, 0x52, 0xff, 0x57, 0x51, 0x52, 0xff, 0x52, 0x4f, 0x53, 0xff, 0x4f, 0x4b, 0x51, 0xff, 0x4e, 0x4b, 0x50, 0xff, 0x53, 0x4d, 0x50, 0xff, 0x59, 0x51, 0x50, 0xff, 0x5e, 0x56, 0x54, 0xff, 0x71, 0x60, 0x5b, 0xff, 0x89, 0x6c, 0x5d, 0xff, 0x8e, 0x75, 0x5b, 0xff, 0x8f, 0x76, 0x55, 0xff, 0x98, 0x73, 0x52, 0xff, 0x97, 0x74, 0x4f, 0xff, 0x90, 0x72, 0x4c, 0xff, 0x86, 0x67, 0x43, 0xff, 0x7a, 0x5c, 0x3a, 0xff, 0x78, 0x5c, 0x3c, 0xff, 0x71, 0x56, 0x37, 0xff, 0x5c, 0x46, 0x2c, 0xff, 0x7b, 0x70, 0x67, 0xff, 0xac, 0xae, 0xb8, 0xff, 0x91, 0x98, 0xa8, 0xff, 0x7b, 0x84, 0x98, 0xff, 0x84, 0x91, 0xad, 0xff, 0x81, 0x8e, 0xad, 0xff, 0x6b, 0x79, 0x97, 0xff, 0x61, 0x70, 0x90, 0xff, 0x5d, 0x6e, 0x91, 0xff, 0x64, 0x76, 0x9c, 0xff, 0x66, 0x79, 0xa0, 0xff, 0x63, 0x73, 0x99, 0xff, 0x56, 0x65, 0x89, 0xff, 0x59, 0x67, 0x8d, 0xff, 0x5b, 0x6a, 0x8f, 0xff, 0x56, 0x66, 0x89, 0xff, 0x51, 0x62, 0x84, 0xff, 0x46, 0x55, 0x74, 0xff, 0x4c, 0x5a, 0x7b, 0xff, 0x47, 0x53, 0x7b, 0xff, 0x44, 0x4f, 0x78, 0xff, 0x3e, 0x4a, 0x71, 0xff, 0x38, 0x46, 0x68, 0xff, 0x33, 0x3f, 0x65, 0xff, 0x47, 0x53, 0x7d, 0xff, 0x3e, 0x49, 0x72, 0xff, 0x32, 0x3d, 0x66, 0xff, 0x38, 0x44, 0x6c, 0xff, 0x38, 0x43, 0x6a, 0xff, 0x36, 0x44, 0x6d, 0xff, 0x25, 0x36, 0x61, 0xff, 0x29, 0x37, 0x60, 0xff, 0x2c, 0x3a, 0x63, 0xff, 0x27, 0x36, 0x5f, 0xff, 0x26, 0x33, 0x5c, 0xff, 0x2d, 0x3b, 0x67, 0xff, 0x35, 0x47, 0x72, 0xff, 0x31, 0x42, 0x6d, 0xff, 0x33, 0x47, 0x74, 0xff, 0x35, 0x4b, 0x78, 0xff, 0x2f, 0x47, 0x73, 0xff, 0x37, 0x4e, 0x7c, 0xff, 0x39, 0x4f, 0x7f, 0xff, 0x31, 0x47, 0x77, 0xff, 0x2a, 0x41, 0x72, 0xff, 0x2b, 0x42, 0x75, 0xff, 0x29, 0x42, 0x74, 0xff, 0x24, 0x3e, 0x70, 0xff, 0x26, 0x3f, 0x71, 0xff, 0x25, 0x3e, 0x70, 0xff, 0x23, 0x3b, 0x6e, 0xff, 0x25, 0x3d, 0x70, 0xff, 0x2b, 0x44, 0x77, 0xff, 0x2c, 0x45, 0x75, 0xff, 0x42, 0x56, 0x7f, 0xff, 0x81, 0x8d, 0xab, 0xff, 0xb7, 0xb9, 0xc7, 0xff, 0xae, 0xa7, 0xaa, 0xff, 0x79, 0x6e, 0x6c, 0xff, 0x5e, 0x5a, 0x5b, 0xff, 0x65, 0x63, 0x67, 0xff, 0x74, 0x68, 0x69, 0xde, 0x7b, 0x6c, 0x6c, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x6b, 0x4e, 0x1a, 0x80, 0x69, 0x4e, 0xd7, 0x7b, 0x65, 0x4f, 0xff, 0x73, 0x60, 0x4f, 0xff, 0x70, 0x5c, 0x50, 0xff, 0x6a, 0x58, 0x52, 0xff, 0x62, 0x54, 0x54, 0xff, 0x5b, 0x4f, 0x52, 0xff, 0x56, 0x4d, 0x50, 0xff, 0x54, 0x4f, 0x50, 0xff, 0x54, 0x4f, 0x52, 0xff, 0x53, 0x4c, 0x52, 0xff, 0x53, 0x4e, 0x51, 0xff, 0x56, 0x4e, 0x4e, 0xff, 0x59, 0x4e, 0x4c, 0xff, 0x69, 0x5c, 0x53, 0xff, 0x82, 0x6b, 0x5d, 0xff, 0x93, 0x74, 0x60, 0xff, 0x93, 0x78, 0x5b, 0xff, 0x94, 0x7a, 0x59, 0xff, 0x99, 0x75, 0x53, 0xff, 0x95, 0x74, 0x51, 0xff, 0x92, 0x74, 0x51, 0xff, 0x91, 0x70, 0x4b, 0xff, 0x89, 0x67, 0x42, 0xff, 0x7e, 0x5f, 0x3d, 0xff, 0x78, 0x59, 0x3c, 0xff, 0x72, 0x54, 0x37, 0xff, 0x7c, 0x62, 0x49, 0xff, 0x8f, 0x7e, 0x72, 0xff, 0x92, 0x8e, 0x91, 0xff, 0x80, 0x87, 0x98, 0xff, 0x6d, 0x7d, 0x94, 0xff, 0x74, 0x83, 0x9e, 0xff, 0x75, 0x84, 0xa1, 0xff, 0x72, 0x82, 0xa0, 0xff, 0x6f, 0x7f, 0xa0, 0xff, 0x6e, 0x81, 0xa4, 0xff, 0x71, 0x83, 0xa9, 0xff, 0x63, 0x74, 0x99, 0xff, 0x54, 0x65, 0x89, 0xff, 0x66, 0x78, 0x9b, 0xff, 0x61, 0x72, 0x93, 0xff, 0x49, 0x5b, 0x7b, 0xff, 0x46, 0x58, 0x78, 0xff, 0x39, 0x4c, 0x6b, 0xff, 0x46, 0x58, 0x78, 0xff, 0x49, 0x5a, 0x7f, 0xff, 0x46, 0x56, 0x7d, 0xff, 0x3f, 0x50, 0x75, 0xff, 0x39, 0x4b, 0x6f, 0xff, 0x3b, 0x4b, 0x71, 0xff, 0x3a, 0x46, 0x6f, 0xff, 0x37, 0x44, 0x6c, 0xff, 0x38, 0x45, 0x6d, 0xff, 0x40, 0x4d, 0x75, 0xff, 0x3b, 0x48, 0x6f, 0xff, 0x25, 0x33, 0x5b, 0xff, 0x1c, 0x2b, 0x54, 0xff, 0x27, 0x35, 0x5f, 0xff, 0x2d, 0x3c, 0x66, 0xff, 0x30, 0x40, 0x6a, 0xff, 0x30, 0x40, 0x6a, 0xff, 0x2e, 0x3e, 0x68, 0xff, 0x38, 0x4a, 0x75, 0xff, 0x3a, 0x4d, 0x79, 0xff, 0x3a, 0x4f, 0x7c, 0xff, 0x36, 0x4d, 0x7a, 0xff, 0x35, 0x4d, 0x7b, 0xff, 0x38, 0x4f, 0x7d, 0xff, 0x36, 0x4d, 0x7b, 0xff, 0x31, 0x48, 0x78, 0xff, 0x30, 0x47, 0x77, 0xff, 0x29, 0x40, 0x71, 0xff, 0x23, 0x3c, 0x6f, 0xff, 0x25, 0x3e, 0x70, 0xff, 0x24, 0x3e, 0x6e, 0xff, 0x24, 0x3d, 0x6e, 0xff, 0x24, 0x3d, 0x6e, 0xff, 0x24, 0x3d, 0x6e, 0xff, 0x25, 0x3e, 0x6f, 0xff, 0x29, 0x43, 0x75, 0xff, 0x38, 0x51, 0x80, 0xff, 0x5c, 0x6a, 0x8c, 0xff, 0x93, 0x95, 0xa9, 0xff, 0xbe, 0xb8, 0xc4, 0xff, 0xb5, 0xac, 0xb6, 0xff, 0x74, 0x6b, 0x73, 0xff, 0x58, 0x4d, 0x56, 0xd7, 0x71, 0x5e, 0x67, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x68, 0x51, 0x16, 0x7c, 0x66, 0x4f, 0xca, 0x74, 0x61, 0x4f, 0xff, 0x6f, 0x5d, 0x50, 0xff, 0x6a, 0x58, 0x52, 0xff, 0x61, 0x53, 0x52, 0xff, 0x5c, 0x50, 0x52, 0xff, 0x59, 0x52, 0x53, 0xff, 0x5a, 0x53, 0x54, 0xff, 0x5c, 0x54, 0x56, 0xff, 0x5b, 0x54, 0x56, 0xff, 0x59, 0x51, 0x51, 0xff, 0x5c, 0x4f, 0x4c, 0xff, 0x64, 0x55, 0x4f, 0xff, 0x74, 0x62, 0x53, 0xff, 0x8b, 0x6f, 0x5b, 0xff, 0x9c, 0x7b, 0x62, 0xff, 0xa6, 0x88, 0x69, 0xff, 0xac, 0x91, 0x6f, 0xff, 0xb0, 0x8d, 0x6e, 0xff, 0xa7, 0x89, 0x62, 0xff, 0x9e, 0x86, 0x59, 0xff, 0x9b, 0x7d, 0x50, 0xff, 0x93, 0x73, 0x47, 0xff, 0x86, 0x68, 0x40, 0xff, 0x7a, 0x5e, 0x38, 0xff, 0x7a, 0x5b, 0x39, 0xff, 0x82, 0x5e, 0x3a, 0xff, 0x77, 0x57, 0x36, 0xff, 0x9a, 0x8e, 0x82, 0xff, 0xcc, 0xd5, 0xde, 0xff, 0x89, 0x9a, 0xb1, 0xff, 0x6b, 0x7b, 0x93, 0xff, 0x75, 0x85, 0x9e, 0xff, 0x7b, 0x8c, 0xa6, 0xff, 0x7a, 0x8b, 0xab, 0xff, 0x68, 0x7b, 0x9e, 0xff, 0x63, 0x74, 0x9b, 0xff, 0x65, 0x78, 0x9c, 0xff, 0x5d, 0x71, 0x93, 0xff, 0x5e, 0x71, 0x94, 0xff, 0x4d, 0x60, 0x81, 0xff, 0x3e, 0x52, 0x72, 0xff, 0x4a, 0x5e, 0x7d, 0xff, 0x45, 0x59, 0x7a, 0xff, 0x4d, 0x60, 0x83, 0xff, 0x47, 0x5b, 0x7e, 0xff, 0x41, 0x54, 0x78, 0xff, 0x3c, 0x4f, 0x73, 0xff, 0x3b, 0x4e, 0x73, 0xff, 0x3b, 0x4c, 0x72, 0xff, 0x3d, 0x4c, 0x73, 0xff, 0x3c, 0x4c, 0x72, 0xff, 0x2d, 0x3c, 0x63, 0xff, 0x33, 0x42, 0x69, 0xff, 0x36, 0x45, 0x6c, 0xff, 0x31, 0x40, 0x67, 0xff, 0x24, 0x32, 0x59, 0xff, 0x1e, 0x2e, 0x57, 0xff, 0x25, 0x35, 0x5f, 0xff, 0x31, 0x41, 0x6b, 0xff, 0x32, 0x42, 0x6d, 0xff, 0x32, 0x44, 0x6f, 0xff, 0x35, 0x47, 0x74, 0xff, 0x37, 0x4b, 0x78, 0xff, 0x38, 0x4d, 0x7a, 0xff, 0x38, 0x4f, 0x7c, 0xff, 0x35, 0x4d, 0x7b, 0xff, 0x33, 0x4a, 0x78, 0xff, 0x32, 0x49, 0x78, 0xff, 0x33, 0x4b, 0x79, 0xff, 0x33, 0x4a, 0x79, 0xff, 0x29, 0x40, 0x71, 0xff, 0x26, 0x3f, 0x6f, 0xff, 0x26, 0x40, 0x70, 0xff, 0x24, 0x3f, 0x6d, 0xff, 0x24, 0x3e, 0x6d, 0xff, 0x26, 0x41, 0x6f, 0xff, 0x23, 0x3d, 0x6c, 0xff, 0x21, 0x3b, 0x69, 0xff, 0x27, 0x42, 0x74, 0xff, 0x2d, 0x47, 0x79, 0xff, 0x47, 0x59, 0x82, 0xff, 0x73, 0x7b, 0x9b, 0xff, 0x8e, 0x8f, 0xa7, 0xff, 0xab, 0xa7, 0xc0, 0xff, 0xb4, 0xac, 0xba, 0xca, 0x7f, 0x73, 0x7f, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x73, 0x5c, 0x45, 0x0b, 0x73, 0x61, 0x4d, 0xaf, 0x6f, 0x5d, 0x4f, 0xff, 0x6a, 0x58, 0x51, 0xff, 0x63, 0x55, 0x54, 0xff, 0x5e, 0x54, 0x54, 0xff, 0x5c, 0x55, 0x56, 0xff, 0x5f, 0x55, 0x57, 0xff, 0x61, 0x57, 0x59, 0xff, 0x5f, 0x55, 0x56, 0xff, 0x5d, 0x51, 0x50, 0xff, 0x63, 0x53, 0x4d, 0xff, 0x71, 0x5f, 0x55, 0xff, 0x82, 0x6d, 0x5a, 0xff, 0x9b, 0x7c, 0x64, 0xff, 0xb1, 0x8d, 0x74, 0xff, 0xb5, 0x98, 0x79, 0xff, 0xb3, 0x9a, 0x7c, 0xff, 0xb9, 0x99, 0x7f, 0xff, 0xb3, 0x99, 0x70, 0xff, 0xac, 0x97, 0x66, 0xff, 0xa6, 0x8e, 0x60, 0xff, 0x98, 0x7f, 0x55, 0xff, 0x85, 0x6d, 0x47, 0xff, 0x74, 0x5e, 0x3d, 0xff, 0x6b, 0x57, 0x40, 0xff, 0x7d, 0x5f, 0x3e, 0xff, 0x84, 0x60, 0x36, 0xff, 0x92, 0x87, 0x73, 0xff, 0xe9, 0xf9, 0xff, 0xff, 0xde, 0xf5, 0xfe, 0xff, 0x9b, 0xae, 0xc2, 0xff, 0x62, 0x73, 0x88, 0xff, 0x66, 0x77, 0x90, 0xff, 0x7f, 0x90, 0xae, 0xff, 0x6d, 0x80, 0xa3, 0xff, 0x63, 0x75, 0x9b, 0xff, 0x5b, 0x6e, 0x92, 0xff, 0x6d, 0x80, 0xa4, 0xff, 0x61, 0x74, 0x98, 0xff, 0x46, 0x59, 0x7d, 0xff, 0x52, 0x65, 0x88, 0xff, 0x58, 0x6c, 0x8e, 0xff, 0x4d, 0x5f, 0x83, 0xff, 0x42, 0x56, 0x79, 0xff, 0x3a, 0x4d, 0x6e, 0xff, 0x3b, 0x4e, 0x70, 0xff, 0x39, 0x4c, 0x6f, 0xff, 0x39, 0x4a, 0x71, 0xff, 0x30, 0x3f, 0x67, 0xff, 0x40, 0x4e, 0x75, 0xff, 0x37, 0x45, 0x6c, 0xff, 0x1f, 0x2d, 0x54, 0xff, 0x23, 0x32, 0x59, 0xff, 0x2b, 0x3a, 0x61, 0xff, 0x28, 0x37, 0x5d, 0xff, 0x29, 0x3a, 0x5e, 0xff, 0x25, 0x36, 0x5d, 0xff, 0x22, 0x33, 0x5d, 0xff, 0x29, 0x3b, 0x65, 0xff, 0x2f, 0x42, 0x6e, 0xff, 0x2f, 0x42, 0x6f, 0xff, 0x33, 0x46, 0x73, 0xff, 0x39, 0x4d, 0x7a, 0xff, 0x3a, 0x4f, 0x7e, 0xff, 0x36, 0x4e, 0x7c, 0xff, 0x30, 0x48, 0x78, 0xff, 0x34, 0x4c, 0x7a, 0xff, 0x36, 0x4d, 0x7a, 0xff, 0x32, 0x4b, 0x78, 0xff, 0x2d, 0x45, 0x73, 0xff, 0x2b, 0x43, 0x72, 0xff, 0x25, 0x40, 0x6e, 0xff, 0x23, 0x3d, 0x6b, 0xff, 0x24, 0x3e, 0x6d, 0xff, 0x22, 0x3c, 0x6b, 0xff, 0x23, 0x3d, 0x6c, 0xff, 0x22, 0x3c, 0x6b, 0xff, 0x23, 0x3d, 0x6c, 0xff, 0x24, 0x3f, 0x70, 0xff, 0x26, 0x41, 0x72, 0xff, 0x38, 0x4d, 0x7b, 0xff, 0x56, 0x65, 0x8d, 0xff, 0x6e, 0x78, 0x9b, 0xff, 0x6c, 0x73, 0x96, 0xb0, 0x8b, 0xa2, 0xa2, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 0x55, 0x03, 0x71, 0x5d, 0x50, 0x8b, 0x6b, 0x59, 0x52, 0xff, 0x64, 0x56, 0x54, 0xff, 0x5d, 0x53, 0x54, 0xff, 0x5a, 0x52, 0x52, 0xff, 0x60, 0x56, 0x56, 0xff, 0x64, 0x5a, 0x5a, 0xff, 0x64, 0x59, 0x58, 0xff, 0x63, 0x55, 0x52, 0xff, 0x6a, 0x59, 0x51, 0xff, 0x7a, 0x66, 0x5a, 0xff, 0x90, 0x7a, 0x64, 0xff, 0xaf, 0x8e, 0x76, 0xff, 0xbc, 0x96, 0x7e, 0xff, 0xb7, 0x9b, 0x7e, 0xff, 0xb5, 0xa1, 0x85, 0xff, 0xbc, 0x9f, 0x8a, 0xff, 0xb9, 0xa0, 0x82, 0xff, 0xae, 0x99, 0x74, 0xff, 0xa4, 0x8b, 0x66, 0xff, 0xa0, 0x87, 0x67, 0xff, 0x94, 0x7e, 0x64, 0xff, 0x8c, 0x77, 0x66, 0xff, 0x7a, 0x78, 0x6e, 0xff, 0x91, 0x81, 0x62, 0xff, 0xa4, 0x81, 0x51, 0xff, 0x8b, 0x85, 0x6e, 0xff, 0xbd, 0xd8, 0xe3, 0xff, 0xe2, 0xff, 0xff, 0xff, 0xdf, 0xf2, 0xfe, 0xff, 0xa5, 0xb3, 0xc7, 0xff, 0x75, 0x86, 0x9d, 0xff, 0x6b, 0x7e, 0x9b, 0xff, 0x67, 0x7b, 0x9e, 0xff, 0x67, 0x78, 0x9d, 0xff, 0x60, 0x72, 0x99, 0xff, 0x6e, 0x82, 0xaa, 0xff, 0x63, 0x77, 0x9f, 0xff, 0x58, 0x6c, 0x92, 0xff, 0x56, 0x6a, 0x8f, 0xff, 0x54, 0x69, 0x8f, 0xff, 0x44, 0x54, 0x7b, 0xff, 0x2e, 0x3c, 0x5f, 0xff, 0x39, 0x49, 0x68, 0xff, 0x38, 0x49, 0x68, 0xff, 0x36, 0x45, 0x68, 0xff, 0x3f, 0x4b, 0x76, 0xff, 0x2f, 0x3e, 0x67, 0xff, 0x2e, 0x3e, 0x64, 0xff, 0x2a, 0x3a, 0x60, 0xff, 0x26, 0x35, 0x5c, 0xff, 0x22, 0x32, 0x58, 0xff, 0x20, 0x30, 0x56, 0xff, 0x1d, 0x2d, 0x52, 0xff, 0x27, 0x37, 0x5b, 0xff, 0x2b, 0x3b, 0x62, 0xff, 0x2b, 0x3c, 0x64, 0xff, 0x2f, 0x43, 0x6d, 0xff, 0x2c, 0x42, 0x70, 0xff, 0x31, 0x45, 0x72, 0xff, 0x39, 0x4b, 0x7a, 0xff, 0x37, 0x4c, 0x7b, 0xff, 0x32, 0x49, 0x77, 0xff, 0x31, 0x48, 0x78, 0xff, 0x30, 0x48, 0x79, 0xff, 0x32, 0x4c, 0x7a, 0xff, 0x36, 0x4d, 0x78, 0xff, 0x32, 0x4b, 0x77, 0xff, 0x2d, 0x45, 0x73, 0xff, 0x2a, 0x42, 0x70, 0xff, 0x23, 0x3e, 0x6c, 0xff, 0x22, 0x3c, 0x6b, 0xff, 0x22, 0x3c, 0x6b, 0xff, 0x22, 0x3d, 0x6b, 0xff, 0x22, 0x3c, 0x6b, 0xff, 0x22, 0x3c, 0x6b, 0xff, 0x20, 0x3b, 0x69, 0xff, 0x23, 0x3b, 0x6c, 0xff, 0x26, 0x3b, 0x6d, 0xff, 0x2a, 0x41, 0x70, 0xff, 0x3d, 0x53, 0x80, 0xff, 0x59, 0x6a, 0x90, 0x8b, 0x55, 0x55, 0xaa, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6c, 0x5a, 0x52, 0x57, 0x64, 0x56, 0x53, 0xf3, 0x5d, 0x52, 0x53, 0xff, 0x58, 0x53, 0x53, 0xff, 0x62, 0x55, 0x57, 0xff, 0x68, 0x58, 0x5b, 0xff, 0x64, 0x58, 0x58, 0xff, 0x66, 0x58, 0x54, 0xff, 0x71, 0x5e, 0x53, 0xff, 0x84, 0x6c, 0x5d, 0xff, 0xa2, 0x86, 0x6f, 0xff, 0xb5, 0x97, 0x7a, 0xff, 0xbe, 0x9f, 0x82, 0xff, 0xc1, 0xa6, 0x8e, 0xff, 0xc3, 0xaa, 0x95, 0xff, 0xc3, 0xac, 0x90, 0xff, 0xc1, 0xab, 0x8b, 0xff, 0xb6, 0xa1, 0x89, 0xff, 0xa7, 0x94, 0x8a, 0xff, 0xb3, 0xa2, 0xa7, 0xff, 0xc5, 0xb4, 0xc3, 0xff, 0xc6, 0xb5, 0xc8, 0xff, 0xc2, 0xb4, 0xcb, 0xff, 0xcd, 0xbf, 0xc0, 0xff, 0xd4, 0xc5, 0xab, 0xff, 0xc7, 0xc3, 0xb2, 0xff, 0xd0, 0xe1, 0xe7, 0xff, 0xd0, 0xf0, 0xfd, 0xff, 0xcf, 0xed, 0xfe, 0xff, 0xd4, 0xeb, 0xff, 0xff, 0x9b, 0xad, 0xc1, 0xff, 0x7b, 0x8a, 0xa2, 0xff, 0x77, 0x87, 0xa6, 0xff, 0x71, 0x84, 0xa7, 0xff, 0x6a, 0x7e, 0xa5, 0xff, 0x66, 0x7a, 0xa2, 0xff, 0x5a, 0x6e, 0x96, 0xff, 0x57, 0x6a, 0x91, 0xff, 0x4e, 0x62, 0x88, 0xff, 0x4f, 0x63, 0x89, 0xff, 0x4c, 0x5d, 0x83, 0xff, 0x43, 0x52, 0x77, 0xff, 0x3b, 0x4b, 0x6e, 0xff, 0x3c, 0x4b, 0x6e, 0xff, 0x42, 0x50, 0x75, 0xff, 0x3a, 0x48, 0x6e, 0xff, 0x2e, 0x3d, 0x66, 0xff, 0x27, 0x38, 0x62, 0xff, 0x2b, 0x3b, 0x65, 0xff, 0x23, 0x34, 0x5d, 0xff, 0x1e, 0x2f, 0x57, 0xff, 0x21, 0x31, 0x5a, 0xff, 0x21, 0x32, 0x58, 0xff, 0x25, 0x36, 0x5b, 0xff, 0x25, 0x37, 0x5d, 0xff, 0x2c, 0x3e, 0x65, 0xff, 0x36, 0x49, 0x74, 0xff, 0x3b, 0x4f, 0x7b, 0xff, 0x3d, 0x50, 0x7c, 0xff, 0x3b, 0x4e, 0x7c, 0xff, 0x32, 0x47, 0x75, 0xff, 0x30, 0x46, 0x73, 0xff, 0x32, 0x48, 0x77, 0xff, 0x33, 0x4b, 0x7b, 0xff, 0x32, 0x4a, 0x78, 0xff, 0x34, 0x4b, 0x77, 0xff, 0x31, 0x49, 0x76, 0xff, 0x2e, 0x45, 0x73, 0xff, 0x2a, 0x41, 0x6f, 0xff, 0x26, 0x3e, 0x6c, 0xff, 0x25, 0x3e, 0x6c, 0xff, 0x21, 0x3b, 0x68, 0xff, 0x20, 0x38, 0x66, 0xff, 0x20, 0x39, 0x67, 0xff, 0x21, 0x3a, 0x68, 0xff, 0x1f, 0x38, 0x65, 0xff, 0x1f, 0x38, 0x67, 0xff, 0x21, 0x39, 0x6a, 0xff, 0x25, 0x3c, 0x6c, 0xf3, 0x31, 0x49, 0x75, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x64, 0x53, 0x53, 0x2b, 0x5d, 0x52, 0x52, 0xd2, 0x57, 0x53, 0x54, 0xff, 0x61, 0x53, 0x57, 0xff, 0x66, 0x53, 0x57, 0xff, 0x61, 0x54, 0x54, 0xff, 0x67, 0x57, 0x52, 0xff, 0x77, 0x60, 0x52, 0xff, 0x8f, 0x71, 0x5e, 0xff, 0xaa, 0x89, 0x72, 0xff, 0xba, 0x9e, 0x7d, 0xff, 0xbe, 0xa4, 0x85, 0xff, 0xc1, 0xa5, 0x8f, 0xff, 0xc2, 0xa7, 0x91, 0xff, 0xc0, 0xab, 0x88, 0xff, 0xbd, 0xa8, 0x8b, 0xff, 0xb9, 0xa6, 0x9f, 0xff, 0xc3, 0xb8, 0xc5, 0xff, 0xcd, 0xc4, 0xe6, 0xff, 0xc6, 0xba, 0xe4, 0xff, 0xbe, 0xb0, 0xda, 0xff, 0xc9, 0xb0, 0xdd, 0xff, 0xd2, 0xc2, 0xdf, 0xff, 0xdb, 0xdc, 0xda, 0xff, 0xfd, 0xf8, 0xf0, 0xff, 0xee, 0xef, 0xf4, 0xff, 0xcc, 0xe7, 0xf3, 0xff, 0xbf, 0xe5, 0xfb, 0xff, 0xc8, 0xe7, 0xfd, 0xff, 0xcd, 0xe1, 0xf5, 0xff, 0x96, 0xa3, 0xb8, 0xff, 0x77, 0x85, 0xa0, 0xff, 0x73, 0x88, 0xa9, 0xff, 0x6f, 0x84, 0xa9, 0xff, 0x66, 0x7a, 0xa0, 0xff, 0x54, 0x69, 0x8e, 0xff, 0x58, 0x6c, 0x91, 0xff, 0x61, 0x75, 0x9a, 0xff, 0x5b, 0x6f, 0x93, 0xff, 0x4f, 0x63, 0x87, 0xff, 0x54, 0x67, 0x8c, 0xff, 0x4d, 0x60, 0x86, 0xff, 0x3a, 0x4c, 0x72, 0xff, 0x3c, 0x4d, 0x72, 0xff, 0x41, 0x52, 0x75, 0xff, 0x3a, 0x4b, 0x72, 0xff, 0x30, 0x40, 0x6c, 0xff, 0x32, 0x42, 0x6d, 0xff, 0x28, 0x38, 0x62, 0xff, 0x1c, 0x2c, 0x56, 0xff, 0x25, 0x35, 0x5e, 0xff, 0x26, 0x38, 0x5d, 0xff, 0x21, 0x34, 0x59, 0xff, 0x29, 0x3c, 0x62, 0xff, 0x3a, 0x4c, 0x74, 0xff, 0x3b, 0x4e, 0x79, 0xff, 0x3b, 0x4e, 0x78, 0xff, 0x36, 0x49, 0x75, 0xff, 0x33, 0x47, 0x74, 0xff, 0x34, 0x49, 0x75, 0xff, 0x36, 0x4b, 0x78, 0xff, 0x34, 0x4a, 0x78, 0xff, 0x31, 0x48, 0x76, 0xff, 0x30, 0x47, 0x74, 0xff, 0x31, 0x47, 0x75, 0xff, 0x31, 0x47, 0x74, 0xff, 0x2d, 0x44, 0x72, 0xff, 0x2c, 0x43, 0x71, 0xff, 0x28, 0x3e, 0x6d, 0xff, 0x26, 0x3d, 0x6b, 0xff, 0x21, 0x39, 0x67, 0xff, 0x1f, 0x36, 0x64, 0xff, 0x1e, 0x36, 0x64, 0xff, 0x1e, 0x35, 0x63, 0xff, 0x1e, 0x35, 0x64, 0xff, 0x1c, 0x35, 0x63, 0xff, 0x1e, 0x3a, 0x68, 0xd2, 0x22, 0x3f, 0x6e, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5f, 0x3f, 0x5f, 0x08, 0x57, 0x50, 0x50, 0x8f, 0x5d, 0x51, 0x51, 0xfe, 0x61, 0x51, 0x50, 0xff, 0x60, 0x51, 0x4d, 0xff, 0x6b, 0x57, 0x4f, 0xff, 0x7e, 0x61, 0x51, 0xff, 0x92, 0x6c, 0x58, 0xff, 0xaa, 0x86, 0x6e, 0xff, 0xbc, 0x9c, 0x82, 0xff, 0xc0, 0xa1, 0x87, 0xff, 0xc2, 0xa6, 0x8b, 0xff, 0xc3, 0xaa, 0x8e, 0xff, 0xc2, 0xab, 0x8c, 0xff, 0xb9, 0xa4, 0xa1, 0xff, 0xc6, 0xb3, 0xc7, 0xff, 0xd2, 0xc4, 0xe1, 0xff, 0xc2, 0xb7, 0xe1, 0xff, 0xbd, 0xb2, 0xdf, 0xff, 0xbe, 0xb2, 0xdd, 0xff, 0xc8, 0xb7, 0xe7, 0xff, 0xd1, 0xc8, 0xe7, 0xff, 0xe1, 0xe2, 0xe3, 0xff, 0xff, 0xfd, 0xfc, 0xff, 0xec, 0xef, 0xf7, 0xff, 0xce, 0xe6, 0xf2, 0xff, 0xbf, 0xe2, 0xf4, 0xff, 0xc0, 0xdf, 0xf4, 0xff, 0xd0, 0xe8, 0xfd, 0xff, 0xbc, 0xd0, 0xe7, 0xff, 0x88, 0x9c, 0xb6, 0xff, 0x79, 0x8d, 0xac, 0xff, 0x6f, 0x85, 0xa8, 0xff, 0x68, 0x80, 0xa4, 0xff, 0x64, 0x7b, 0x9e, 0xff, 0x55, 0x6d, 0x90, 0xff, 0x58, 0x6f, 0x93, 0xff, 0x4e, 0x66, 0x89, 0xff, 0x53, 0x68, 0x8b, 0xff, 0x62, 0x77, 0x9a, 0xff, 0x55, 0x68, 0x8b, 0xff, 0x3d, 0x50, 0x72, 0xff, 0x39, 0x4d, 0x70, 0xff, 0x3b, 0x4e, 0x70, 0xff, 0x35, 0x46, 0x6b, 0xff, 0x36, 0x47, 0x6f, 0xff, 0x2a, 0x3b, 0x62, 0xff, 0x27, 0x38, 0x5f, 0xff, 0x2c, 0x3d, 0x64, 0xff, 0x28, 0x39, 0x5f, 0xff, 0x29, 0x3a, 0x60, 0xff, 0x25, 0x38, 0x5d, 0xff, 0x2d, 0x40, 0x67, 0xff, 0x38, 0x4a, 0x73, 0xff, 0x35, 0x48, 0x72, 0xff, 0x32, 0x45, 0x71, 0xff, 0x34, 0x47, 0x74, 0xff, 0x35, 0x48, 0x76, 0xff, 0x35, 0x4a, 0x77, 0xff, 0x34, 0x49, 0x75, 0xff, 0x33, 0x49, 0x76, 0xff, 0x31, 0x48, 0x76, 0xff, 0x33, 0x4a, 0x76, 0xff, 0x30, 0x47, 0x73, 0xff, 0x2e, 0x45, 0x71, 0xff, 0x2c, 0x43, 0x70, 0xff, 0x2d, 0x44, 0x72, 0xff, 0x29, 0x3f, 0x6e, 0xff, 0x23, 0x3c, 0x6a, 0xff, 0x21, 0x39, 0x67, 0xff, 0x1f, 0x37, 0x65, 0xff, 0x1e, 0x37, 0x64, 0xff, 0x1c, 0x34, 0x62, 0xff, 0x1b, 0x33, 0x61, 0xfe, 0x1c, 0x33, 0x62, 0x8f, 0x1f, 0x3f, 0x5f, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x4d, 0x49, 0x42, 0x5d, 0x4c, 0x49, 0xdb, 0x5d, 0x4d, 0x48, 0xff, 0x6b, 0x54, 0x4a, 0xff, 0x7f, 0x5f, 0x4d, 0xff, 0x8f, 0x64, 0x4e, 0xff, 0xa6, 0x7f, 0x66, 0xff, 0xbb, 0x98, 0x83, 0xff, 0xc2, 0xa3, 0x8c, 0xff, 0xc5, 0xad, 0x8d, 0xff, 0xc3, 0xaf, 0x92, 0xff, 0xc4, 0xad, 0x9a, 0xff, 0xb8, 0xa4, 0xbd, 0xff, 0xc7, 0xb7, 0xe5, 0xff, 0xc8, 0xba, 0xea, 0xff, 0xc0, 0xb4, 0xe9, 0xff, 0xbe, 0xb4, 0xe8, 0xff, 0xbf, 0xb6, 0xe8, 0xff, 0xc4, 0xb9, 0xef, 0xff, 0xca, 0xc8, 0xea, 0xff, 0xe6, 0xea, 0xef, 0xff, 0xff, 0xfd, 0xfe, 0xff, 0xe5, 0xe7, 0xf0, 0xff, 0xd1, 0xe7, 0xf1, 0xff, 0xc2, 0xe4, 0xf3, 0xff, 0xc0, 0xe2, 0xf5, 0xff, 0xbf, 0xde, 0xf1, 0xff, 0xc8, 0xe3, 0xfb, 0xff, 0xb1, 0xc8, 0xe3, 0xff, 0x7a, 0x8f, 0xaa, 0xff, 0x65, 0x7d, 0x9d, 0xff, 0x6b, 0x85, 0xa7, 0xff, 0x6d, 0x86, 0xa8, 0xff, 0x67, 0x7f, 0xa1, 0xff, 0x5e, 0x77, 0x9a, 0xff, 0x52, 0x6a, 0x8f, 0xff, 0x4e, 0x65, 0x89, 0xff, 0x48, 0x5e, 0x81, 0xff, 0x43, 0x59, 0x7b, 0xff, 0x47, 0x5a, 0x7e, 0xff, 0x48, 0x5b, 0x7e, 0xff, 0x42, 0x55, 0x78, 0xff, 0x3b, 0x4e, 0x72, 0xff, 0x3e, 0x51, 0x74, 0xff, 0x2d, 0x40, 0x63, 0xff, 0x2c, 0x3f, 0x63, 0xff, 0x2e, 0x3f, 0x65, 0xff, 0x29, 0x3a, 0x61, 0xff, 0x2b, 0x3c, 0x62, 0xff, 0x29, 0x3c, 0x62, 0xff, 0x2f, 0x42, 0x69, 0xff, 0x33, 0x44, 0x6e, 0xff, 0x2e, 0x40, 0x6c, 0xff, 0x31, 0x44, 0x71, 0xff, 0x37, 0x49, 0x78, 0xff, 0x37, 0x49, 0x79, 0xff, 0x37, 0x4c, 0x7a, 0xff, 0x36, 0x4b, 0x79, 0xff, 0x33, 0x4a, 0x77, 0xff, 0x35, 0x4c, 0x78, 0xff, 0x36, 0x4d, 0x78, 0xff, 0x2f, 0x47, 0x70, 0xff, 0x2e, 0x46, 0x70, 0xff, 0x2d, 0x44, 0x71, 0xff, 0x2b, 0x42, 0x70, 0xff, 0x2a, 0x41, 0x6f, 0xff, 0x23, 0x3b, 0x67, 0xff, 0x21, 0x3a, 0x64, 0xff, 0x1e, 0x37, 0x62, 0xff, 0x1b, 0x33, 0x5e, 0xff, 0x1a, 0x33, 0x5e, 0xdb, 0x1b, 0x32, 0x60, 0x42, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x55, 0x38, 0x09, 0x58, 0x47, 0x42, 0x87, 0x62, 0x4a, 0x40, 0xf9, 0x76, 0x57, 0x46, 0xff, 0x88, 0x5f, 0x49, 0xff, 0x9e, 0x7a, 0x5e, 0xff, 0xb9, 0x97, 0x83, 0xff, 0xc1, 0xa3, 0x92, 0xff, 0xc0, 0xae, 0x8e, 0xff, 0xbd, 0xae, 0x93, 0xff, 0xbe, 0xa8, 0xa9, 0xff, 0xb6, 0xa3, 0xcf, 0xff, 0xbf, 0xb2, 0xec, 0xff, 0xc5, 0xb8, 0xf0, 0xff, 0xbe, 0xb3, 0xec, 0xff, 0xbb, 0xb3, 0xea, 0xff, 0xbc, 0xb4, 0xec, 0xff, 0xc0, 0xb9, 0xeb, 0xff, 0xc7, 0xc7, 0xe3, 0xff, 0xe7, 0xeb, 0xf0, 0xff, 0xff, 0xfd, 0xfe, 0xff, 0xee, 0xf0, 0xf8, 0xff, 0xd6, 0xeb, 0xf1, 0xff, 0xc5, 0xe7, 0xf2, 0xff, 0xba, 0xdd, 0xed, 0xff, 0xbc, 0xe0, 0xf3, 0xff, 0xc4, 0xe6, 0xfe, 0xff, 0xc2, 0xdf, 0xf8, 0xff, 0x99, 0xaf, 0xc9, 0xff, 0x75, 0x8d, 0xac, 0xff, 0x66, 0x81, 0xa4, 0xff, 0x67, 0x81, 0xa4, 0xff, 0x6c, 0x86, 0xa9, 0xff, 0x63, 0x7c, 0xa2, 0xff, 0x57, 0x70, 0x99, 0xff, 0x58, 0x70, 0x96, 0xff, 0x56, 0x6c, 0x92, 0xff, 0x52, 0x6a, 0x8f, 0xff, 0x52, 0x67, 0x8d, 0xff, 0x4e, 0x62, 0x88, 0xff, 0x48, 0x5b, 0x82, 0xff, 0x49, 0x5c, 0x80, 0xff, 0x38, 0x4b, 0x6e, 0xff, 0x36, 0x48, 0x6c, 0xff, 0x42, 0x54, 0x7a, 0xff, 0x2e, 0x3e, 0x67, 0xff, 0x26, 0x36, 0x60, 0xff, 0x34, 0x44, 0x6c, 0xff, 0x2a, 0x3c, 0x64, 0xff, 0x29, 0x3b, 0x64, 0xff, 0x31, 0x42, 0x6d, 0xff, 0x33, 0x45, 0x73, 0xff, 0x31, 0x45, 0x72, 0xff, 0x34, 0x45, 0x75, 0xff, 0x37, 0x48, 0x79, 0xff, 0x35, 0x49, 0x79, 0xff, 0x35, 0x49, 0x77, 0xff, 0x2f, 0x46, 0x71, 0xff, 0x2f, 0x47, 0x71, 0xff, 0x30, 0x48, 0x71, 0xff, 0x2f, 0x47, 0x70, 0xff, 0x2c, 0x44, 0x6e, 0xff, 0x2b, 0x43, 0x6d, 0xff, 0x28, 0x3f, 0x6c, 0xff, 0x26, 0x3d, 0x6a, 0xff, 0x25, 0x3d, 0x68, 0xff, 0x22, 0x3a, 0x63, 0xff, 0x1c, 0x35, 0x5f, 0xf9, 0x1a, 0x32, 0x5b, 0x88, 0x1c, 0x38, 0x55, 0x09, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x44, 0x3e, 0x25, 0x6b, 0x4f, 0x3e, 0xb7, 0x7d, 0x59, 0x44, 0xff, 0x97, 0x79, 0x57, 0xff, 0xb6, 0x97, 0x81, 0xff, 0xbe, 0xa1, 0x95, 0xff, 0xba, 0xac, 0x8e, 0xff, 0xb4, 0xaa, 0x94, 0xff, 0xb8, 0xa1, 0xb8, 0xff, 0xb1, 0xa0, 0xd7, 0xff, 0xb6, 0xac, 0xe5, 0xff, 0xc0, 0xb2, 0xeb, 0xff, 0xbb, 0xad, 0xe9, 0xff, 0xb8, 0xad, 0xeb, 0xff, 0xb4, 0xab, 0xec, 0xff, 0xbe, 0xb5, 0xe6, 0xff, 0xda, 0xd9, 0xea, 0xff, 0xf5, 0xf7, 0xf6, 0xff, 0xfd, 0xfa, 0xf7, 0xff, 0xf6, 0xf9, 0xfe, 0xff, 0xd8, 0xf1, 0xf3, 0xff, 0xc7, 0xea, 0xf1, 0xff, 0xb8, 0xde, 0xee, 0xff, 0xb2, 0xdc, 0xf0, 0xff, 0xb9, 0xe1, 0xfa, 0xff, 0xc0, 0xe0, 0xf8, 0xff, 0xb8, 0xcc, 0xe2, 0xff, 0x88, 0xa1, 0xbf, 0xff, 0x67, 0x84, 0xaa, 0xff, 0x69, 0x84, 0xaa, 0xff, 0x6d, 0x87, 0xb0, 0xff, 0x6a, 0x83, 0xaf, 0xff, 0x65, 0x7e, 0xab, 0xff, 0x5e, 0x77, 0xa2, 0xff, 0x5b, 0x72, 0x9c, 0xff, 0x5f, 0x74, 0x9f, 0xff, 0x59, 0x6e, 0x9a, 0xff, 0x52, 0x65, 0x91, 0xff, 0x48, 0x5b, 0x89, 0xff, 0x3b, 0x4e, 0x75, 0xff, 0x34, 0x46, 0x68, 0xff, 0x3b, 0x4c, 0x72, 0xff, 0x3a, 0x4b, 0x74, 0xff, 0x2f, 0x3f, 0x6b, 0xff, 0x30, 0x3f, 0x6e, 0xff, 0x34, 0x43, 0x6f, 0xff, 0x35, 0x47, 0x6f, 0xff, 0x2c, 0x3e, 0x67, 0xff, 0x2b, 0x3b, 0x68, 0xff, 0x32, 0x45, 0x73, 0xff, 0x33, 0x46, 0x74, 0xff, 0x33, 0x43, 0x74, 0xff, 0x35, 0x45, 0x78, 0xff, 0x31, 0x44, 0x75, 0xff, 0x2f, 0x43, 0x72, 0xff, 0x2e, 0x46, 0x70, 0xff, 0x2c, 0x45, 0x6d, 0xff, 0x2c, 0x45, 0x6d, 0xff, 0x2b, 0x44, 0x6c, 0xff, 0x28, 0x41, 0x6b, 0xff, 0x27, 0x40, 0x69, 0xff, 0x25, 0x3c, 0x68, 0xff, 0x23, 0x3a, 0x68, 0xff, 0x22, 0x3a, 0x65, 0xff, 0x1e, 0x39, 0x5e, 0xb7, 0x1a, 0x35, 0x5d, 0x26, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0x4c, 0x36, 0x46, 0x8f, 0x6d, 0x59, 0xca, 0xb1, 0x8f, 0x81, 0xff, 0xb8, 0x9e, 0x8c, 0xff, 0xb6, 0xab, 0x8a, 0xff, 0xaf, 0xa7, 0x96, 0xff, 0xaf, 0x9f, 0xb5, 0xff, 0xa8, 0x9e, 0xce, 0xff, 0xb1, 0xac, 0xdf, 0xff, 0xc2, 0xb8, 0xea, 0xff, 0xc5, 0xba, 0xeb, 0xff, 0xc5, 0xbc, 0xe8, 0xff, 0xcf, 0xc8, 0xef, 0xff, 0xe8, 0xe2, 0xf7, 0xff, 0xf7, 0xf8, 0xfb, 0xff, 0xf9, 0xfa, 0xf7, 0xff, 0xfb, 0xf5, 0xf4, 0xff, 0xf9, 0xf7, 0xfb, 0xff, 0xdf, 0xf0, 0xf3, 0xff, 0xcd, 0xe9, 0xf2, 0xff, 0xc4, 0xe5, 0xf4, 0xff, 0xb5, 0xdd, 0xec, 0xff, 0xb2, 0xdb, 0xf0, 0xff, 0xb8, 0xdc, 0xf6, 0xff, 0xb8, 0xd5, 0xf3, 0xff, 0x9b, 0xb5, 0xd7, 0xff, 0x76, 0x91, 0xb5, 0xff, 0x6b, 0x86, 0xab, 0xff, 0x6d, 0x87, 0xaf, 0xff, 0x71, 0x8b, 0xb7, 0xff, 0x62, 0x7c, 0xa9, 0xff, 0x4d, 0x64, 0x92, 0xff, 0x4f, 0x64, 0x92, 0xff, 0x5c, 0x73, 0xa0, 0xff, 0x60, 0x77, 0xa3, 0xff, 0x5b, 0x70, 0x9c, 0xff, 0x43, 0x56, 0x82, 0xff, 0x37, 0x48, 0x72, 0xff, 0x46, 0x57, 0x7f, 0xff, 0x3e, 0x4f, 0x79, 0xff, 0x3c, 0x4d, 0x78, 0xff, 0x3b, 0x4a, 0x77, 0xff, 0x32, 0x41, 0x6f, 0xff, 0x2c, 0x3b, 0x67, 0xff, 0x2e, 0x3f, 0x68, 0xff, 0x30, 0x41, 0x6b, 0xff, 0x33, 0x43, 0x6f, 0xff, 0x36, 0x47, 0x74, 0xff, 0x35, 0x47, 0x76, 0xff, 0x34, 0x47, 0x77, 0xff, 0x33, 0x46, 0x76, 0xff, 0x2f, 0x43, 0x72, 0xff, 0x2f, 0x44, 0x71, 0xff, 0x2e, 0x45, 0x6f, 0xff, 0x2c, 0x44, 0x6c, 0xff, 0x2e, 0x45, 0x6e, 0xff, 0x2c, 0x44, 0x6d, 0xff, 0x29, 0x41, 0x6a, 0xff, 0x28, 0x3f, 0x69, 0xff, 0x26, 0x3d, 0x67, 0xff, 0x23, 0x3a, 0x64, 0xca, 0x20, 0x36, 0x62, 0x46, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa8, 0x8e, 0x7a, 0x4d, 0xb4, 0x9d, 0x85, 0xcc, 0xb0, 0xa3, 0x8a, 0xff, 0xa9, 0x9f, 0x98, 0xff, 0xa9, 0x9f, 0xaf, 0xff, 0xac, 0xa4, 0xce, 0xff, 0xb6, 0xb0, 0xe1, 0xff, 0xc3, 0xbb, 0xe7, 0xff, 0xc7, 0xc0, 0xe0, 0xff, 0xe0, 0xdc, 0xeb, 0xff, 0xfa, 0xf8, 0xf7, 0xff, 0xfe, 0xff, 0xf9, 0xff, 0xf4, 0xf7, 0xf5, 0xff, 0xf3, 0xf4, 0xf5, 0xff, 0xfc, 0xf5, 0xf5, 0xff, 0xfa, 0xf6, 0xfa, 0xff, 0xe4, 0xef, 0xf3, 0xff, 0xcf, 0xe4, 0xf0, 0xff, 0xc8, 0xe2, 0xf1, 0xff, 0xc1, 0xe4, 0xf0, 0xff, 0xb7, 0xe1, 0xf2, 0xff, 0xb0, 0xd9, 0xf4, 0xff, 0xad, 0xd2, 0xf7, 0xff, 0xac, 0xc7, 0xec, 0xff, 0x8d, 0xa6, 0xc8, 0xff, 0x6b, 0x85, 0xa9, 0xff, 0x6d, 0x8a, 0xb0, 0xff, 0x72, 0x8e, 0xb6, 0xff, 0x5f, 0x7c, 0xa8, 0xff, 0x55, 0x6d, 0x9a, 0xff, 0x61, 0x76, 0xa4, 0xff, 0x58, 0x73, 0x9d, 0xff, 0x57, 0x72, 0x9b, 0xff, 0x56, 0x6f, 0x98, 0xff, 0x51, 0x64, 0x8c, 0xff, 0x4c, 0x5c, 0x87, 0xff, 0x45, 0x55, 0x82, 0xff, 0x3e, 0x4f, 0x7b, 0xff, 0x3d, 0x4e, 0x79, 0xff, 0x41, 0x51, 0x7b, 0xff, 0x2f, 0x3f, 0x69, 0xff, 0x2d, 0x3d, 0x66, 0xff, 0x34, 0x44, 0x6e, 0xff, 0x34, 0x45, 0x70, 0xff, 0x38, 0x49, 0x74, 0xff, 0x35, 0x47, 0x73, 0xff, 0x35, 0x47, 0x76, 0xff, 0x35, 0x49, 0x76, 0xff, 0x32, 0x48, 0x74, 0xff, 0x32, 0x47, 0x73, 0xff, 0x30, 0x46, 0x70, 0xff, 0x2c, 0x41, 0x6b, 0xff, 0x2a, 0x3f, 0x69, 0xff, 0x2c, 0x41, 0x6b, 0xff, 0x2d, 0x42, 0x6d, 0xff, 0x2c, 0x41, 0x6b, 0xff, 0x29, 0x3e, 0x69, 0xcc, 0x24, 0x38, 0x63, 0x4d, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa8, 0x99, 0x91, 0x41, 0xa4, 0x9a, 0x97, 0xb4, 0xac, 0xa1, 0xb1, 0xfe, 0xaf, 0xa7, 0xc8, 0xff, 0xa9, 0xa2, 0xc6, 0xff, 0xb8, 0xb0, 0xcf, 0xff, 0xe3, 0xdd, 0xef, 0xff, 0xff, 0xfd, 0xfe, 0xff, 0xfc, 0xfd, 0xf0, 0xff, 0xf4, 0xf5, 0xf1, 0xff, 0xf1, 0xf3, 0xf6, 0xff, 0xf4, 0xf5, 0xf5, 0xff, 0xfb, 0xf3, 0xf4, 0xff, 0xf5, 0xf4, 0xf7, 0xff, 0xe1, 0xf0, 0xf4, 0xff, 0xd0, 0xe8, 0xf1, 0xff, 0xc6, 0xe3, 0xee, 0xff, 0xc0, 0xe4, 0xf1, 0xff, 0xb8, 0xe3, 0xf4, 0xff, 0xaf, 0xd8, 0xf2, 0xff, 0xab, 0xcf, 0xf1, 0xff, 0xaa, 0xc8, 0xeb, 0xff, 0xa2, 0xbc, 0xe1, 0xff, 0x76, 0x91, 0xb7, 0xff, 0x62, 0x80, 0xa7, 0xff, 0x6b, 0x89, 0xb2, 0xff, 0x6a, 0x88, 0xb2, 0xff, 0x63, 0x7c, 0xa9, 0xff, 0x63, 0x7a, 0xa8, 0xff, 0x5a, 0x74, 0x9f, 0xff, 0x56, 0x6f, 0x99, 0xff, 0x54, 0x6c, 0x96, 0xff, 0x4e, 0x63, 0x8c, 0xff, 0x52, 0x65, 0x90, 0xff, 0x47, 0x59, 0x85, 0xff, 0x42, 0x54, 0x7f, 0xff, 0x3d, 0x4e, 0x79, 0xff, 0x3a, 0x4a, 0x75, 0xff, 0x37, 0x46, 0x72, 0xff, 0x33, 0x45, 0x6f, 0xff, 0x37, 0x49, 0x72, 0xff, 0x36, 0x47, 0x72, 0xff, 0x32, 0x45, 0x70, 0xff, 0x34, 0x48, 0x74, 0xff, 0x37, 0x4a, 0x77, 0xff, 0x33, 0x47, 0x73, 0xff, 0x33, 0x46, 0x72, 0xff, 0x31, 0x44, 0x6f, 0xff, 0x2f, 0x42, 0x6d, 0xff, 0x2e, 0x41, 0x6c, 0xff, 0x2a, 0x3d, 0x66, 0xff, 0x28, 0x3d, 0x66, 0xfe, 0x2a, 0x41, 0x6a, 0xb4, 0x27, 0x3e, 0x69, 0x41, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x99, 0x99, 0x91, 0x23, 0xa6, 0xa6, 0xa6, 0x88, 0xc7, 0xc7, 0xcc, 0xe6, 0xeb, 0xe9, 0xed, 0xff, 0xff, 0xfa, 0xfe, 0xff, 0xfa, 0xf6, 0xf7, 0xff, 0xf7, 0xf5, 0xf1, 0xff, 0xf5, 0xf3, 0xf3, 0xff, 0xf1, 0xf3, 0xf4, 0xff, 0xf2, 0xf3, 0xf3, 0xff, 0xf8, 0xf3, 0xf3, 0xff, 0xf4, 0xf4, 0xf8, 0xff, 0xdd, 0xf0, 0xf3, 0xff, 0xca, 0xe5, 0xef, 0xff, 0xc1, 0xe0, 0xed, 0xff, 0xb8, 0xe0, 0xec, 0xff, 0xb3, 0xe0, 0xf2, 0xff, 0xaf, 0xd9, 0xf3, 0xff, 0xab, 0xd0, 0xf2, 0xff, 0xa8, 0xc9, 0xef, 0xff, 0xb2, 0xcf, 0xf5, 0xff, 0x96, 0xb4, 0xdb, 0xff, 0x64, 0x82, 0xaa, 0xff, 0x61, 0x7f, 0xa9, 0xff, 0x71, 0x91, 0xb9, 0xff, 0x6d, 0x88, 0xb3, 0xff, 0x5b, 0x74, 0xa1, 0xff, 0x5b, 0x74, 0x9f, 0xff, 0x5d, 0x75, 0x9f, 0xff, 0x58, 0x71, 0x9a, 0xff, 0x50, 0x68, 0x90, 0xff, 0x53, 0x68, 0x91, 0xff, 0x51, 0x64, 0x90, 0xff, 0x4b, 0x5e, 0x89, 0xff, 0x40, 0x52, 0x7d, 0xff, 0x3e, 0x4f, 0x7a, 0xff, 0x3f, 0x4f, 0x7a, 0xff, 0x3c, 0x4d, 0x78, 0xff, 0x34, 0x45, 0x70, 0xff, 0x34, 0x46, 0x71, 0xff, 0x32, 0x45, 0x70, 0xff, 0x34, 0x47, 0x72, 0xff, 0x34, 0x4a, 0x74, 0xff, 0x33, 0x48, 0x73, 0xff, 0x33, 0x45, 0x70, 0xff, 0x2e, 0x41, 0x6d, 0xff, 0x30, 0x43, 0x6e, 0xff, 0x2f, 0x43, 0x6c, 0xe7, 0x2b, 0x3f, 0x67, 0x88, 0x24, 0x3a, 0x66, 0x23, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xff, 0xff, 0xff, 0x05, 0xfb, 0xff, 0xf4, 0x4a, 0xf2, 0xf2, 0xf0, 0xa0, 0xf3, 0xf1, 0xf6, 0xef, 0xf3, 0xf0, 0xfa, 0xff, 0xf4, 0xf1, 0xf6, 0xff, 0xf1, 0xf3, 0xf2, 0xff, 0xf1, 0xf3, 0xf3, 0xff, 0xf6, 0xf3, 0xf3, 0xff, 0xf3, 0xf5, 0xf8, 0xff, 0xd8, 0xed, 0xef, 0xff, 0xc7, 0xe3, 0xef, 0xff, 0xc1, 0xe0, 0xf1, 0xff, 0xb5, 0xde, 0xed, 0xff, 0xac, 0xd8, 0xed, 0xff, 0xa9, 0xd4, 0xf1, 0xff, 0xa7, 0xcd, 0xf2, 0xff, 0xa3, 0xc6, 0xee, 0xff, 0xa5, 0xc7, 0xef, 0xff, 0x9d, 0xbd, 0xe5, 0xff, 0x75, 0x94, 0xbe, 0xff, 0x5c, 0x7a, 0xa3, 0xff, 0x67, 0x85, 0xad, 0xff, 0x6d, 0x8a, 0xb5, 0xff, 0x5e, 0x78, 0xa5, 0xff, 0x55, 0x6d, 0x99, 0xff, 0x57, 0x6f, 0x99, 0xff, 0x58, 0x70, 0x9a, 0xff, 0x5e, 0x79, 0xa0, 0xff, 0x56, 0x6e, 0x97, 0xff, 0x4d, 0x5f, 0x8a, 0xff, 0x4a, 0x5d, 0x88, 0xff, 0x3f, 0x51, 0x7c, 0xff, 0x42, 0x53, 0x7e, 0xff, 0x3d, 0x4e, 0x79, 0xff, 0x38, 0x49, 0x74, 0xff, 0x36, 0x49, 0x76, 0xff, 0x35, 0x48, 0x74, 0xff, 0x37, 0x4b, 0x76, 0xff, 0x34, 0x4b, 0x74, 0xff, 0x32, 0x49, 0x72, 0xff, 0x34, 0x47, 0x71, 0xff, 0x32, 0x43, 0x6e, 0xef, 0x34, 0x44, 0x6f, 0xa0, 0x30, 0x44, 0x6e, 0x4a, 0x33, 0x33, 0x66, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfe, 0xfe, 0xfe, 0x07, 0xec, 0xf0, 0xf0, 0x46, 0xf2, 0xf2, 0xf2, 0x8e, 0xf1, 0xf1, 0xf4, 0xd7, 0xf2, 0xf2, 0xf2, 0xff, 0xf5, 0xf5, 0xf3, 0xff, 0xf3, 0xf3, 0xf6, 0xff, 0xd6, 0xea, 0xe7, 0xff, 0xc4, 0xe1, 0xf0, 0xff, 0xbe, 0xdc, 0xf2, 0xff, 0xb2, 0xda, 0xec, 0xff, 0xa7, 0xd4, 0xea, 0xff, 0x9f, 0xcb, 0xea, 0xff, 0xa0, 0xc7, 0xef, 0xff, 0xa1, 0xc5, 0xf1, 0xff, 0xa0, 0xc3, 0xee, 0xff, 0x9f, 0xc3, 0xeb, 0xff, 0x80, 0xa1, 0xc9, 0xff, 0x67, 0x86, 0xaf, 0xff, 0x67, 0x85, 0xad, 0xff, 0x63, 0x84, 0xae, 0xff, 0x5d, 0x7a, 0xa7, 0xff, 0x53, 0x6a, 0x96, 0xff, 0x62, 0x77, 0xa1, 0xff, 0x5e, 0x75, 0x9e, 0xff, 0x55, 0x72, 0x98, 0xff, 0x53, 0x6e, 0x96, 0xff, 0x53, 0x67, 0x92, 0xff, 0x46, 0x5a, 0x84, 0xff, 0x3a, 0x4d, 0x78, 0xff, 0x3a, 0x4b, 0x76, 0xff, 0x38, 0x48, 0x72, 0xff, 0x38, 0x49, 0x77, 0xff, 0x3b, 0x4d, 0x7d, 0xff, 0x3b, 0x4d, 0x7b, 0xff, 0x38, 0x4e, 0x78, 0xff, 0x36, 0x4e, 0x77, 0xd7, 0x35, 0x4d, 0x76, 0x8e, 0x36, 0x48, 0x74, 0x46, 0x48, 0x48, 0x6d, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xec, 0xf5, 0xf5, 0x1b, 0xfc, 0xf3, 0xf6, 0x55, 0xd9, 0xe7, 0xeb, 0x8f, 0xbb, 0xdb, 0xe7, 0xc2, 0xc8, 0xe5, 0xef, 0xea, 0xb7, 0xd9, 0xed, 0xff, 0xad, 0xd6, 0xec, 0xff, 0xa3, 0xcf, 0xee, 0xff, 0x9d, 0xc6, 0xec, 0xff, 0x9b, 0xbd, 0xe9, 0xff, 0x9e, 0xc1, 0xec, 0xff, 0xa4, 0xc7, 0xf2, 0xff, 0xa8, 0xcb, 0xf5, 0xff, 0x93, 0xb5, 0xdf, 0xff, 0x6f, 0x8e, 0xb8, 0xff, 0x6a, 0x87, 0xb1, 0xff, 0x68, 0x86, 0xb0, 0xff, 0x69, 0x86, 0xb0, 0xff, 0x5b, 0x75, 0xa0, 0xff, 0x58, 0x6f, 0x9a, 0xff, 0x5d, 0x75, 0x9f, 0xff, 0x5a, 0x75, 0xa0, 0xff, 0x57, 0x70, 0x9a, 0xff, 0x53, 0x69, 0x92, 0xff, 0x50, 0x66, 0x90, 0xff, 0x48, 0x5c, 0x87, 0xff, 0x3a, 0x4d, 0x78, 0xff, 0x3d, 0x4e, 0x7a, 0xea, 0x3c, 0x4e, 0x7a, 0xc2, 0x3a, 0x50, 0x79, 0x8f, 0x3c, 0x4e, 0x78, 0x55, 0x38, 0x55, 0x7a, 0x1b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb4, 0xe1, 0xf0, 0x11, 0xa6, 0xd0, 0xec, 0x37, 0xa1, 0xcc, 0xee, 0x5f, 0x9d, 0xc2, 0xeb, 0x86, 0x9c, 0xba, 0xe8, 0xa8, 0x9b, 0xbb, 0xe7, 0xc0, 0x9d, 0xc1, 0xea, 0xd3, 0xa5, 0xc8, 0xf3, 0xe6, 0x9f, 0xc5, 0xf0, 0xee, 0x80, 0xa0, 0xcc, 0xf5, 0x70, 0x8b, 0xb8, 0xff, 0x6e, 0x88, 0xb2, 0xff, 0x6d, 0x89, 0xb0, 0xf5, 0x61, 0x7e, 0xa7, 0xee, 0x54, 0x6d, 0x9b, 0xe6, 0x59, 0x72, 0x9e, 0xd3, 0x5c, 0x74, 0xa3, 0xc0, 0x58, 0x6e, 0x9a, 0xa8, 0x53, 0x6a, 0x92, 0x86, 0x50, 0x66, 0x90, 0x5f, 0x4a, 0x61, 0x8b, 0x37, 0x4b, 0x5a, 0x87, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

const lv_image_dsc_t img_multilang_avatar_15 = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 128,
    .header.h = 128,
    .header.stride = 512,
    .data = img_multilang_avatar_15_map,
    .data_size = sizeof(img_multilang_avatar_15_map),
};

#endif
