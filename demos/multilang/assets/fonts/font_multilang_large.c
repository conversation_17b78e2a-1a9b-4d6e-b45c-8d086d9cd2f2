/*******************************************************************************
 * Size: 22 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 22 --format lvgl --output font_multilang_large.c --font Montserrat-Bold.ttf -r 0x20-0x7F
 ******************************************************************************/

#include "../../../../lvgl.h"

#ifndef FONT_MULTILANG_LARGE
    #define FONT_MULTILANG_LARGE 1
#endif

#if FONT_MULTILANG_LARGE

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xdf, 0xff, 0x3c, 0xff, 0xf2, 0xbf, 0xff, 0x9,
    0xff, 0xf0, 0x8f, 0xfe, 0x7, 0xff, 0xd0, 0x6f,
    0xfc, 0x5, 0xff, 0xb0, 0x4f, 0xfa, 0x3, 0xff,
    0x90, 0x0, 0x0, 0x3, 0xce, 0x70, 0xcf, 0xff,
    0x2c, 0xff, 0xf2, 0x3d, 0xf8, 0x0,

    /* U+0022 "\"" */
    0xef, 0xf0, 0x6f, 0xf8, 0xdf, 0xf0, 0x5f, 0xf7,
    0xdf, 0xe0, 0x4f, 0xf6, 0xcf, 0xd0, 0x4f, 0xf6,
    0xcf, 0xd0, 0x3f, 0xf5, 0xbf, 0xc0, 0x3f, 0xf5,
    0x23, 0x20, 0x3, 0x31,

    /* U+0023 "#" */
    0x0, 0x0, 0x4f, 0xf3, 0x0, 0x9f, 0xe0, 0x0,
    0x0, 0x0, 0x6f, 0xf1, 0x0, 0xbf, 0xc0, 0x0,
    0x0, 0x0, 0x8f, 0xf0, 0x0, 0xdf, 0xa0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x5, 0x66, 0xff, 0xf6, 0x69, 0xff, 0xa6, 0x62,
    0x0, 0x0, 0xff, 0x60, 0x5, 0xff, 0x20, 0x0,
    0x0, 0x2, 0xff, 0x40, 0x7, 0xff, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x20, 0x9, 0xfe, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x36, 0x6f, 0xff, 0x66, 0x6f, 0xfe, 0x66, 0x40,
    0x0, 0xc, 0xfa, 0x0, 0x1f, 0xf6, 0x0, 0x0,
    0x0, 0xe, 0xf8, 0x0, 0x3f, 0xf4, 0x0, 0x0,
    0x0, 0x1f, 0xf6, 0x0, 0x5f, 0xf2, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x78, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x3, 0x9d, 0xff,
    0xfc, 0x94, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0xe, 0xff, 0xd3, 0xef, 0x42, 0x7b, 0x0,
    0xf, 0xff, 0x80, 0xef, 0x40, 0x0, 0x0, 0xe,
    0xff, 0xe5, 0xff, 0x40, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xd4, 0x0, 0x0, 0x0, 0x5d, 0xff,
    0xff, 0xff, 0xe8, 0x0, 0x0, 0x0, 0x48, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xef, 0x9c,
    0xff, 0xf3, 0x1, 0x0, 0x0, 0xef, 0x42, 0xff,
    0xf6, 0x9, 0xe8, 0x30, 0xef, 0x59, 0xff, 0xf4,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x1b,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x38,
    0xce, 0xff, 0xfa, 0x50, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x78, 0x20,
    0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x7d, 0xfd, 0x60, 0x0, 0x0, 0xb, 0xfd,
    0x0, 0x0, 0xaf, 0xea, 0xff, 0x70, 0x0, 0x7,
    0xff, 0x30, 0x0, 0x2f, 0xf2, 0x4, 0xff, 0x0,
    0x2, 0xff, 0x70, 0x0, 0x5, 0xfd, 0x0, 0xf,
    0xf2, 0x0, 0xdf, 0xc0, 0x0, 0x0, 0x5f, 0xe0,
    0x0, 0xff, 0x20, 0x9f, 0xf1, 0x0, 0x0, 0x2,
    0xff, 0x40, 0x6f, 0xf0, 0x4f, 0xf5, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xdf, 0xf7, 0x1e, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xef, 0xd6, 0xa, 0xfe,
    0x13, 0xbf, 0xea, 0x10, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x43, 0xff, 0xef, 0xfd, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x80, 0xaf, 0xb0, 0x1e, 0xf7, 0x0,
    0x0, 0x0, 0xcf, 0xd0, 0xe, 0xf5, 0x0, 0x9f,
    0xa0, 0x0, 0x0, 0x7f, 0xf2, 0x0, 0xef, 0x40,
    0x9, 0xfa, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0xa,
    0xf9, 0x0, 0xdf, 0x70, 0x0, 0xd, 0xfb, 0x0,
    0x0, 0x3f, 0xfb, 0xdf, 0xe1, 0x0, 0x9, 0xfe,
    0x10, 0x0, 0x0, 0x3b, 0xfe, 0xa1, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x5b, 0xef, 0xea, 0x20, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x85, 0xbf, 0xfa, 0x0, 0x0,
    0x0, 0x5f, 0xfc, 0x0, 0x2f, 0xfc, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0x10, 0x9f, 0xf8, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x3d, 0xff, 0xff, 0xf5, 0x0, 0x77, 0x20,
    0x3, 0xff, 0xf9, 0xcf, 0xff, 0x70, 0xff, 0xf0,
    0xd, 0xff, 0x80, 0x9, 0xff, 0xff, 0xff, 0xa0,
    0x2f, 0xff, 0x30, 0x0, 0x7f, 0xff, 0xff, 0x30,
    0x1f, 0xff, 0x90, 0x0, 0xb, 0xff, 0xff, 0x20,
    0xc, 0xff, 0xfe, 0xbc, 0xef, 0xff, 0xff, 0xe3,
    0x2, 0xdf, 0xff, 0xff, 0xff, 0xfb, 0xef, 0xd1,
    0x0, 0x6, 0xbe, 0xfe, 0xc8, 0x20, 0x1b, 0x20,

    /* U+0027 "'" */
    0xef, 0xfd, 0xff, 0xdf, 0xec, 0xfd, 0xcf, 0xdb,
    0xfc, 0x23, 0x20,

    /* U+0028 "(" */
    0x0, 0x7, 0x99, 0x40, 0x3, 0xff, 0xf2, 0x0,
    0xcf, 0xfa, 0x0, 0x2f, 0xff, 0x30, 0x7, 0xff,
    0xe0, 0x0, 0xcf, 0xf9, 0x0, 0xf, 0xff, 0x60,
    0x2, 0xff, 0xf3, 0x0, 0x4f, 0xff, 0x10, 0x5,
    0xff, 0xf0, 0x0, 0x5f, 0xff, 0x0, 0x5, 0xff,
    0xf0, 0x0, 0x4f, 0xff, 0x10, 0x3, 0xff, 0xf2,
    0x0, 0xf, 0xff, 0x50, 0x0, 0xdf, 0xf8, 0x0,
    0xa, 0xff, 0xc0, 0x0, 0x4f, 0xff, 0x10, 0x0,
    0xef, 0xf7, 0x0, 0x7, 0xff, 0xe0, 0x0, 0xe,
    0xff, 0x60,

    /* U+0029 ")" */
    0x59, 0x95, 0x0, 0x4, 0xff, 0xf1, 0x0, 0xc,
    0xff, 0xa0, 0x0, 0x5f, 0xff, 0x0, 0x0, 0xff,
    0xf5, 0x0, 0xb, 0xff, 0xa0, 0x0, 0x8f, 0xfd,
    0x0, 0x5, 0xff, 0xf0, 0x0, 0x3f, 0xff, 0x20,
    0x2, 0xff, 0xf3, 0x0, 0x1f, 0xff, 0x30, 0x2,
    0xff, 0xf3, 0x0, 0x3f, 0xff, 0x20, 0x4, 0xff,
    0xf1, 0x0, 0x7f, 0xfe, 0x0, 0xa, 0xff, 0xb0,
    0x0, 0xef, 0xf8, 0x0, 0x3f, 0xff, 0x20, 0x9,
    0xff, 0xc0, 0x1, 0xff, 0xf5, 0x0, 0x8f, 0xfc,
    0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x2, 0xa8, 0x0, 0x0, 0x3, 0x4, 0xfc,
    0x1, 0x20, 0x3f, 0xb6, 0xfc, 0x6e, 0xb0, 0x5f,
    0xff, 0xff, 0xff, 0xc1, 0x1, 0xcf, 0xff, 0xf6,
    0x0, 0x2a, 0xff, 0xff, 0xfe, 0x70, 0x6f, 0xfb,
    0xfe, 0xcf, 0xe0, 0x8, 0x14, 0xfc, 0x5, 0x40,
    0x0, 0x4, 0xfc, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x68, 0x80, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x7b, 0xbb,
    0xff, 0xfb, 0xbb, 0x9a, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0xd, 0xff, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x68, 0x80, 0x0, 0x0,

    /* U+002C "," */
    0x0, 0x11, 0x0, 0xa, 0xff, 0x60, 0x2f, 0xff,
    0xe0, 0x2f, 0xff, 0xf0, 0x7, 0xff, 0xb0, 0x3,
    0xff, 0x50, 0x7, 0xfe, 0x0, 0xc, 0xf8, 0x0,
    0x7, 0x82, 0x0,

    /* U+002D "-" */
    0xac, 0xcc, 0xcc, 0x4d, 0xff, 0xff, 0xf6, 0xdf,
    0xff, 0xff, 0x60,

    /* U+002E "." */
    0x0, 0x32, 0x0, 0xb, 0xff, 0x70, 0x2f, 0xff,
    0xf0, 0x1f, 0xff, 0xe0, 0x6, 0xed, 0x40,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x4, 0x88, 0x40, 0x0, 0x0,
    0x0, 0xdf, 0xf3, 0x0, 0x0, 0x0, 0x2f, 0xfe,
    0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0, 0x0,
    0x0, 0xef, 0xf2, 0x0, 0x0, 0x0, 0x3f, 0xfd,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x70, 0x0, 0x0,
    0x0, 0xef, 0xf1, 0x0, 0x0, 0x0, 0x4f, 0xfc,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x5f, 0xfb,
    0x0, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0, 0x0,
    0x1, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x6f, 0xfa,
    0x0, 0x0, 0x0, 0xc, 0xff, 0x40, 0x0, 0x0,
    0x2, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x7f, 0xf9,
    0x0, 0x0, 0x0, 0xd, 0xff, 0x30, 0x0, 0x0,
    0x3, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x4b, 0xef, 0xda, 0x40, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x4f,
    0xff, 0xc3, 0x3, 0xdf, 0xff, 0x30, 0xa, 0xff,
    0xf1, 0x0, 0x2, 0xff, 0xf9, 0x0, 0xef, 0xfb,
    0x0, 0x0, 0xc, 0xff, 0xd0, 0xf, 0xff, 0x80,
    0x0, 0x0, 0x9f, 0xff, 0x1, 0xff, 0xf7, 0x0,
    0x0, 0x8, 0xff, 0xf0, 0xf, 0xff, 0x80, 0x0,
    0x0, 0x9f, 0xff, 0x0, 0xef, 0xfb, 0x0, 0x0,
    0xc, 0xff, 0xd0, 0xa, 0xff, 0xf1, 0x0, 0x2,
    0xff, 0xf9, 0x0, 0x4f, 0xff, 0xc3, 0x3, 0xdf,
    0xff, 0x30, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x4b, 0xef, 0xea, 0x40, 0x0,
    0x0,

    /* U+0031 "1" */
    0xdf, 0xff, 0xff, 0xcd, 0xff, 0xff, 0xfc, 0xbd,
    0xdf, 0xff, 0xc0, 0x0, 0xcf, 0xfc, 0x0, 0xc,
    0xff, 0xc0, 0x0, 0xcf, 0xfc, 0x0, 0xc, 0xff,
    0xc0, 0x0, 0xcf, 0xfc, 0x0, 0xc, 0xff, 0xc0,
    0x0, 0xcf, 0xfc, 0x0, 0xc, 0xff, 0xc0, 0x0,
    0xcf, 0xfc, 0x0, 0xc, 0xff, 0xc0, 0x0, 0xcf,
    0xfc, 0x0, 0xc, 0xff, 0xc0,

    /* U+0032 "2" */
    0x0, 0x39, 0xde, 0xfe, 0xa3, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x2, 0xbf, 0xa3, 0x1, 0x7f,
    0xff, 0xb0, 0x0, 0x30, 0x0, 0x0, 0xcf, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0xa, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0xee, 0xee, 0xee, 0x54, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50,

    /* U+0033 "3" */
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x3, 0xdd,
    0xdd, 0xdd, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x40, 0x0, 0x0, 0x0, 0xa, 0xff, 0xfa,
    0x20, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x1, 0x5e, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf3, 0x0, 0x20,
    0x0, 0x0, 0x7, 0xff, 0xf2, 0x1, 0xfc, 0x51,
    0x1, 0x5f, 0xff, 0xe0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x28, 0xbe, 0xff, 0xd9, 0x30,
    0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0xc0, 0xb, 0xdd, 0x50, 0x0, 0xc, 0xff, 0xf2,
    0x0, 0xef, 0xf7, 0x0, 0x8, 0xff, 0xf5, 0x0,
    0xe, 0xff, 0x70, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xee, 0xee, 0xee, 0xee, 0xff,
    0xff, 0xed, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x70,
    0x0,

    /* U+0035 "5" */
    0x0, 0xef, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x1, 0xff, 0xff,
    0xdd, 0xdd, 0xd7, 0x0, 0x3f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xed, 0xca, 0x50, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xd2, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x14,
    0xdf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xf7, 0x2, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x60,
    0xdd, 0x62, 0x0, 0x4d, 0xff, 0xf3, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x10, 0x1, 0x6b, 0xef, 0xfe, 0xa5,
    0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x6, 0xbd, 0xff, 0xd9, 0x30, 0x0,
    0x4, 0xef, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x5f,
    0xff, 0xff, 0xef, 0xff, 0x30, 0x1, 0xff, 0xfe,
    0x60, 0x0, 0x14, 0x0, 0x8, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xa5, 0xcf, 0xfd,
    0x81, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x30, 0x1f, 0xff, 0xff, 0xdb, 0xdf, 0xff, 0xe0,
    0x1f, 0xff, 0xf8, 0x0, 0x7, 0xff, 0xf5, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0xff, 0xf8, 0xc, 0xff,
    0xe0, 0x0, 0x0, 0xff, 0xf7, 0x6, 0xff, 0xf7,
    0x0, 0x8, 0xff, 0xf4, 0x0, 0xcf, 0xff, 0xda,
    0xdf, 0xff, 0xc0, 0x0, 0x1b, 0xff, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x5b, 0xdf, 0xec, 0x60,
    0x0,

    /* U+0037 "7" */
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x6f, 0xff, 0xee,
    0xee, 0xff, 0xff, 0xc6, 0xff, 0xb0, 0x0, 0x7,
    0xff, 0xf5, 0x6f, 0xfb, 0x0, 0x0, 0xef, 0xfe,
    0x2, 0x55, 0x30, 0x0, 0x5f, 0xff, 0x70, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0x40, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xf6, 0x0,
    0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x2, 0x9d, 0xff, 0xeb, 0x60, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xfd, 0x20, 0x4, 0xff,
    0xff, 0xba, 0xdf, 0xff, 0xc0, 0x9, 0xff, 0xf2,
    0x0, 0xa, 0xff, 0xf1, 0x9, 0xff, 0xe0, 0x0,
    0x7, 0xff, 0xf1, 0x4, 0xff, 0xfb, 0x54, 0x7f,
    0xff, 0xc0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe,
    0x10, 0x1, 0xcf, 0xff, 0xff, 0xff, 0xff, 0x60,
    0xb, 0xff, 0xf8, 0x33, 0x5d, 0xff, 0xf3, 0x1f,
    0xff, 0x90, 0x0, 0x1, 0xff, 0xf9, 0x3f, 0xff,
    0x70, 0x0, 0x0, 0xef, 0xfb, 0xf, 0xff, 0xd1,
    0x0, 0x6, 0xff, 0xf8, 0x9, 0xff, 0xff, 0xba,
    0xcf, 0xff, 0xf2, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xfe, 0x40, 0x0, 0x4, 0x9d, 0xff, 0xeb, 0x70,
    0x0,

    /* U+0039 "9" */
    0x0, 0x7, 0xce, 0xfd, 0xb5, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xb1, 0x0, 0xe, 0xff,
    0xfd, 0xac, 0xff, 0xfc, 0x0, 0x6f, 0xff, 0x50,
    0x0, 0x4f, 0xff, 0x60, 0x8f, 0xff, 0x0, 0x0,
    0xd, 0xff, 0xc0, 0x6f, 0xff, 0x50, 0x0, 0x4f,
    0xff, 0xf0, 0x1f, 0xff, 0xfc, 0xbd, 0xff, 0xff,
    0xf1, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x29, 0xdf, 0xfc, 0x58, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x80, 0x0, 0x62, 0x0,
    0x17, 0xff, 0xff, 0x10, 0x3, 0xff, 0xfe, 0xff,
    0xff, 0xf5, 0x0, 0xb, 0xff, 0xff, 0xff, 0xfe,
    0x40, 0x0, 0x3, 0x9d, 0xff, 0xeb, 0x61, 0x0,
    0x0,

    /* U+003A ":" */
    0x7, 0xed, 0x40, 0x2f, 0xff, 0xe0, 0x2f, 0xff,
    0xf0, 0xa, 0xff, 0x70, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x32, 0x0,
    0xb, 0xff, 0x70, 0x2f, 0xff, 0xf0, 0x1f, 0xff,
    0xe0, 0x6, 0xed, 0x40,

    /* U+003B ";" */
    0x7, 0xed, 0x40, 0x2f, 0xff, 0xe0, 0x2f, 0xff,
    0xf0, 0xa, 0xff, 0x70, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x0,
    0xa, 0xff, 0x60, 0x2f, 0xff, 0xe0, 0x2f, 0xff,
    0xf0, 0x7, 0xff, 0xb0, 0x3, 0xff, 0x50, 0x7,
    0xfe, 0x0, 0xc, 0xf8, 0x0, 0x7, 0x82, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x9c, 0x0, 0x0, 0x1, 0x7d, 0xff,
    0xd0, 0x0, 0x5b, 0xff, 0xff, 0xfa, 0x28, 0xef,
    0xff, 0xfd, 0x82, 0xa, 0xff, 0xfe, 0x93, 0x0,
    0x0, 0xaf, 0xff, 0x50, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xe9, 0x30, 0x0, 0x2, 0x8e, 0xff, 0xff,
    0xd8, 0x20, 0x0, 0x4, 0xbf, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x17, 0xdf, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x37,

    /* U+003D "=" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xda, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x6a, 0xaa, 0xaa, 0xaa, 0xaa,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xbb, 0xbb, 0xbb, 0xbb,
    0xb9, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xda, 0xff,
    0xff, 0xff, 0xff, 0xfd,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xa4, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfe, 0x82, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xfc, 0x60, 0x0, 0x1, 0x6c,
    0xff, 0xff, 0xfa, 0x30, 0x0, 0x2, 0x8e, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xd0, 0x0,
    0x28, 0xef, 0xff, 0xfc, 0x17, 0xcf, 0xff, 0xff,
    0xa3, 0xa, 0xff, 0xff, 0xc6, 0x0, 0x0, 0xaf,
    0xe8, 0x20, 0x0, 0x0, 0x6, 0x40, 0x0, 0x0,
    0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x3, 0x9d, 0xef, 0xeb, 0x50, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0xa, 0xff, 0xff,
    0xef, 0xff, 0xff, 0x70, 0x3c, 0xf9, 0x10, 0x5,
    0xff, 0xfb, 0x0, 0x2, 0x0, 0x0, 0xd, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xaa, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xea, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xb0,
    0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x6, 0xad, 0xef, 0xed, 0x95,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe7, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xfe, 0x84, 0x21, 0x24, 0x8d, 0xff, 0xc1,
    0x0, 0x0, 0x1d, 0xff, 0x70, 0x3, 0x54, 0x0,
    0x66, 0xcf, 0xfc, 0x0, 0x0, 0xbf, 0xf4, 0x4,
    0xdf, 0xff, 0xf5, 0xff, 0xf3, 0xef, 0x90, 0x4,
    0xff, 0x70, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x5f, 0xf2, 0xa, 0xfe, 0x0, 0xdf, 0xfb, 0x20,
    0x4e, 0xff, 0xf0, 0xd, 0xf7, 0xe, 0xf9, 0x3,
    0xff, 0xe0, 0x0, 0x5, 0xff, 0xf0, 0x8, 0xfb,
    0xf, 0xf6, 0x5, 0xff, 0xa0, 0x0, 0x0, 0xff,
    0xf0, 0x6, 0xfd, 0x1f, 0xf5, 0x6, 0xff, 0x90,
    0x0, 0x0, 0xff, 0xf0, 0x5, 0xfd, 0xf, 0xf6,
    0x5, 0xff, 0xc0, 0x0, 0x1, 0xff, 0xf0, 0x6,
    0xfc, 0xe, 0xf9, 0x1, 0xff, 0xf4, 0x0, 0x9,
    0xff, 0xf0, 0xa, 0xf9, 0x9, 0xfe, 0x0, 0x9f,
    0xff, 0x97, 0xbf, 0xff, 0xfa, 0x9f, 0xf3, 0x3,
    0xff, 0x70, 0xc, 0xff, 0xff, 0xfe, 0x6f, 0xff,
    0xff, 0x90, 0x0, 0xaf, 0xf4, 0x0, 0x7d, 0xfe,
    0x91, 0x6, 0xdf, 0xd6, 0x0, 0x0, 0x1d, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xfe, 0x84, 0x22, 0x34, 0x8c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xad, 0xef, 0xfd, 0x95, 0x0, 0x0,
    0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x8, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xef, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xe2, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf7, 0xb, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x10, 0x4f, 0xff,
    0x20, 0x0, 0x0, 0x0, 0xc, 0xff, 0xa0, 0x0,
    0xdf, 0xfa, 0x0, 0x0, 0x0, 0x3, 0xff, 0xf3,
    0x0, 0x6, 0xff, 0xf1, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x9, 0xff, 0xfb, 0xbb, 0xbb, 0xbd, 0xff,
    0xf7, 0x0, 0x1, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xe0, 0x0, 0x8f, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x60, 0xe, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0,

    /* U+0042 "B" */
    0x2f, 0xff, 0xff, 0xff, 0xfe, 0xc8, 0x10, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x30, 0x2f,
    0xff, 0xca, 0xaa, 0xbe, 0xff, 0xfd, 0x2, 0xff,
    0xf6, 0x0, 0x0, 0xb, 0xff, 0xf2, 0x2f, 0xff,
    0x60, 0x0, 0x0, 0x7f, 0xff, 0x12, 0xff, 0xf6,
    0x0, 0x0, 0x4e, 0xff, 0xd0, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x2f, 0xff, 0xe9, 0x99,
    0x9b, 0xff, 0xfe, 0x12, 0xff, 0xf6, 0x0, 0x0,
    0x1, 0xef, 0xfa, 0x2f, 0xff, 0x60, 0x0, 0x0,
    0xb, 0xff, 0xe2, 0xff, 0xf6, 0x0, 0x0, 0x2,
    0xef, 0xfe, 0x2f, 0xff, 0xcb, 0xbb, 0xbc, 0xff,
    0xff, 0x92, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc1, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xeb, 0x60,
    0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x4, 0x9d, 0xff, 0xeb, 0x60, 0x0,
    0x0, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0xef, 0xff, 0xc5, 0x10, 0x27, 0xef, 0x90,
    0x7, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x16, 0x0,
    0xd, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x16, 0x0,
    0x0, 0xef, 0xff, 0xc5, 0x10, 0x27, 0xef, 0xa0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xfd, 0x40,
    0x0, 0x0, 0x4, 0x9d, 0xff, 0xeb, 0x60, 0x0,

    /* U+0044 "D" */
    0x2f, 0xff, 0xff, 0xff, 0xed, 0xa5, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x30,
    0x0, 0x2f, 0xff, 0xfe, 0xee, 0xff, 0xff, 0xff,
    0x50, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x3a, 0xff,
    0xff, 0x30, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x6,
    0xff, 0xfb, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf1, 0x2f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x32, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf4, 0x2f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x32, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xf1, 0x2f, 0xff, 0x60,
    0x0, 0x0, 0x6, 0xff, 0xfb, 0x2, 0xff, 0xf6,
    0x0, 0x0, 0x3a, 0xff, 0xff, 0x30, 0x2f, 0xff,
    0xfe, 0xee, 0xff, 0xff, 0xff, 0x50, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xed, 0xa5, 0x0, 0x0, 0x0,

    /* U+0045 "E" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x72, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x2f, 0xff, 0xed,
    0xdd, 0xdd, 0xdd, 0x62, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x2f, 0xff, 0xfc, 0xcc,
    0xcc, 0xc6, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xed, 0xdd, 0xdd, 0xdd, 0xa2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0,

    /* U+0046 "F" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x72, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x2f, 0xff, 0xed,
    0xdd, 0xdd, 0xdd, 0x62, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xfd, 0xdd, 0xdd, 0xd6, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x4, 0x9d, 0xef, 0xec, 0x71, 0x0,
    0x0, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0xef, 0xff, 0xc5, 0x10, 0x26, 0xdf, 0xb0,
    0x7, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x6, 0x0,
    0xd, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x8b, 0xb5,
    0xf, 0xff, 0x90, 0x0, 0x0, 0x0, 0xcf, 0xf7,
    0xd, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xcf, 0xf7,
    0x7, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xcf, 0xf7,
    0x0, 0xef, 0xff, 0xc5, 0x10, 0x25, 0xff, 0xf7,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0x4, 0x9d, 0xff, 0xeb, 0x72, 0x0,

    /* U+0048 "H" */
    0x2f, 0xff, 0x60, 0x0, 0x0, 0x9, 0xff, 0xf2,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x2f,
    0xff, 0x60, 0x0, 0x0, 0x9, 0xff, 0xf2, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x2f, 0xff,
    0x60, 0x0, 0x0, 0x9, 0xff, 0xf2, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0x2f, 0xff, 0x60, 0x0, 0x0,
    0x9, 0xff, 0xf2, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x9,
    0xff, 0xf2, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x9, 0xff,
    0xf0,

    /* U+0049 "I" */
    0x2f, 0xff, 0x62, 0xff, 0xf6, 0x2f, 0xff, 0x62,
    0xff, 0xf6, 0x2f, 0xff, 0x62, 0xff, 0xf6, 0x2f,
    0xff, 0x62, 0xff, 0xf6, 0x2f, 0xff, 0x62, 0xff,
    0xf6, 0x2f, 0xff, 0x62, 0xff, 0xf6, 0x2f, 0xff,
    0x62, 0xff, 0xf6, 0x2f, 0xff, 0x60,

    /* U+004A "J" */
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0xad, 0xdd, 0xde,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf3, 0x0, 0x30, 0x0, 0x7,
    0xff, 0xf2, 0x4, 0xfa, 0x10, 0x2d, 0xff, 0xf0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0x90, 0x9, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x3a, 0xef, 0xfc,
    0x70, 0x0,

    /* U+004B "K" */
    0x2f, 0xff, 0x60, 0x0, 0x0, 0x5f, 0xff, 0xa0,
    0x2f, 0xff, 0x60, 0x0, 0x4, 0xff, 0xfb, 0x0,
    0x2f, 0xff, 0x60, 0x0, 0x4f, 0xff, 0xb0, 0x0,
    0x2f, 0xff, 0x60, 0x4, 0xff, 0xfc, 0x0, 0x0,
    0x2f, 0xff, 0x60, 0x3f, 0xff, 0xc0, 0x0, 0x0,
    0x2f, 0xff, 0x63, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x2f, 0xff, 0x9f, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x2f, 0xff, 0xfe, 0x6f, 0xff, 0xe2, 0x0, 0x0,
    0x2f, 0xff, 0xf2, 0x6, 0xff, 0xfd, 0x0, 0x0,
    0x2f, 0xff, 0x80, 0x0, 0x9f, 0xff, 0xa0, 0x0,
    0x2f, 0xff, 0x60, 0x0, 0xb, 0xff, 0xf7, 0x0,
    0x2f, 0xff, 0x60, 0x0, 0x0, 0xdf, 0xff, 0x40,
    0x2f, 0xff, 0x60, 0x0, 0x0, 0x2e, 0xff, 0xf2,

    /* U+004C "L" */
    0x2f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x2, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xfe, 0xee, 0xee, 0xee, 0x12, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10,

    /* U+004D "M" */
    0x2f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0x22, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf2, 0x2f, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0x22, 0xff, 0xff, 0xe1,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xf2, 0x2f, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x22,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x3f, 0xff, 0xff,
    0xf2, 0x2f, 0xff, 0xaf, 0xfd, 0x0, 0xc, 0xff,
    0x9f, 0xff, 0x22, 0xff, 0xf3, 0xdf, 0xf7, 0x6,
    0xff, 0xc2, 0xff, 0xf2, 0x2f, 0xff, 0x24, 0xff,
    0xf3, 0xef, 0xf3, 0x2f, 0xff, 0x22, 0xff, 0xf2,
    0xa, 0xff, 0xff, 0xf9, 0x2, 0xff, 0xf2, 0x2f,
    0xff, 0x20, 0x1f, 0xff, 0xfe, 0x10, 0x2f, 0xff,
    0x22, 0xff, 0xf2, 0x0, 0x7f, 0xff, 0x60, 0x2,
    0xff, 0xf2, 0x2f, 0xff, 0x20, 0x0, 0xdf, 0xc0,
    0x0, 0x2f, 0xff, 0x22, 0xff, 0xf2, 0x0, 0x2,
    0x62, 0x0, 0x2, 0xff, 0xf2, 0x2f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x20,

    /* U+004E "N" */
    0x2f, 0xff, 0x30, 0x0, 0x0, 0x9, 0xff, 0xf2,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x9f, 0xff, 0x2f,
    0xff, 0xfc, 0x0, 0x0, 0x9, 0xff, 0xf2, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x9f, 0xff, 0x2f, 0xff,
    0xff, 0xf7, 0x0, 0x9, 0xff, 0xf2, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x9f, 0xff, 0x2f, 0xff, 0x6e,
    0xff, 0xf2, 0x9, 0xff, 0xf2, 0xff, 0xf5, 0x2f,
    0xff, 0xd1, 0x9f, 0xff, 0x2f, 0xff, 0x50, 0x5f,
    0xff, 0xb9, 0xff, 0xf2, 0xff, 0xf5, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0x2f, 0xff, 0x50, 0x0, 0xaf,
    0xff, 0xff, 0xf2, 0xff, 0xf5, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0x2f, 0xff, 0x50, 0x0, 0x1, 0xef,
    0xff, 0xf2, 0xff, 0xf5, 0x0, 0x0, 0x3, 0xff,
    0xff, 0x2f, 0xff, 0x50, 0x0, 0x0, 0x6, 0xff,
    0xf0,

    /* U+004F "O" */
    0x0, 0x0, 0x3, 0x9d, 0xef, 0xeb, 0x71, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0xdf, 0xff, 0xc5, 0x10,
    0x28, 0xff, 0xff, 0x70, 0x7, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xf1, 0xd, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf6, 0xf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf9, 0x1f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfa,
    0xf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf9, 0xd, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xf6, 0x7, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xf1, 0x0, 0xdf, 0xff, 0xc5, 0x10,
    0x27, 0xff, 0xff, 0x70, 0x0, 0x2e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x1, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x3, 0x9d, 0xff, 0xeb, 0x71, 0x0, 0x0,

    /* U+0050 "P" */
    0x2f, 0xff, 0xff, 0xff, 0xec, 0x81, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x2f,
    0xff, 0xfe, 0xee, 0xff, 0xff, 0xf4, 0x2, 0xff,
    0xf6, 0x0, 0x0, 0x7f, 0xff, 0xc0, 0x2f, 0xff,
    0x60, 0x0, 0x0, 0xaf, 0xff, 0x2, 0xff, 0xf6,
    0x0, 0x0, 0x7, 0xff, 0xf2, 0x2f, 0xff, 0x60,
    0x0, 0x0, 0xbf, 0xff, 0x2, 0xff, 0xf6, 0x0,
    0x2, 0x9f, 0xff, 0xc0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x2f, 0xff, 0xfe, 0xee, 0xdb,
    0x61, 0x0, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x3, 0x9d, 0xef, 0xeb, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0xd, 0xff, 0xfc,
    0x51, 0x2, 0x8f, 0xff, 0xf7, 0x0, 0x7, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf1, 0x0,
    0xdf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x60, 0xf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf9, 0x1, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xa0, 0xf, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf9, 0x0, 0xdf, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x60, 0x7,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf1,
    0x0, 0xd, 0xff, 0xfc, 0x51, 0x2, 0x7f, 0xff,
    0xf7, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x9f, 0xff, 0xff, 0xd1, 0x0, 0x41, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xca, 0xdf, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6c,
    0xef, 0xd9, 0x10,

    /* U+0052 "R" */
    0x2f, 0xff, 0xff, 0xff, 0xec, 0x81, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x2f,
    0xff, 0xfe, 0xee, 0xff, 0xff, 0xf4, 0x2, 0xff,
    0xf6, 0x0, 0x0, 0x7f, 0xff, 0xc0, 0x2f, 0xff,
    0x60, 0x0, 0x0, 0xaf, 0xff, 0x2, 0xff, 0xf6,
    0x0, 0x0, 0x7, 0xff, 0xf2, 0x2f, 0xff, 0x60,
    0x0, 0x0, 0xbf, 0xff, 0x2, 0xff, 0xf6, 0x0,
    0x2, 0x9f, 0xff, 0xc0, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x2f, 0xff, 0xfd, 0xdf, 0xff,
    0xf4, 0x0, 0x2, 0xff, 0xf6, 0x0, 0xd, 0xff,
    0xe1, 0x0, 0x2f, 0xff, 0x60, 0x0, 0x2f, 0xff,
    0xa0, 0x2, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff,
    0x60, 0x2f, 0xff, 0x60, 0x0, 0x0, 0xbf, 0xff,
    0x20,

    /* U+0053 "S" */
    0x0, 0x3, 0x9d, 0xef, 0xec, 0x94, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x7, 0xff,
    0xff, 0xee, 0xff, 0xff, 0x40, 0xe, 0xff, 0xd3,
    0x0, 0x2, 0x7b, 0x0, 0xf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xe5, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfc, 0x84, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0xff, 0xff, 0xe8, 0x0,
    0x0, 0x0, 0x48, 0xcf, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x4c, 0xff, 0xf3, 0x1, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf6, 0x9, 0xe8, 0x30,
    0x0, 0x9, 0xff, 0xf4, 0x1f, 0xff, 0xff, 0xee,
    0xff, 0xff, 0xd0, 0x1b, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x38, 0xce, 0xff, 0xea, 0x50,
    0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xce, 0xee,
    0xef, 0xff, 0xfe, 0xee, 0xe7, 0x0, 0x0, 0xf,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x90, 0x0,
    0x0,

    /* U+0055 "U" */
    0x5f, 0xff, 0x40, 0x0, 0x0, 0xe, 0xff, 0xa5,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0xef, 0xfa, 0x5f,
    0xff, 0x40, 0x0, 0x0, 0xe, 0xff, 0xa5, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0xef, 0xfa, 0x5f, 0xff,
    0x40, 0x0, 0x0, 0xe, 0xff, 0xa5, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0xef, 0xfa, 0x5f, 0xff, 0x40,
    0x0, 0x0, 0xe, 0xff, 0xa5, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0xef, 0xfa, 0x4f, 0xff, 0x40, 0x0,
    0x0, 0xe, 0xff, 0x93, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0xff, 0xf8, 0xf, 0xff, 0xc0, 0x0, 0x0,
    0x6f, 0xff, 0x50, 0xaf, 0xff, 0xb3, 0x12, 0x7f,
    0xff, 0xe0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x7b, 0xef, 0xfd, 0x92, 0x0,
    0x0,

    /* U+0056 "V" */
    0xe, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf6, 0x8, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xe0, 0x1, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x80, 0x0, 0xaf, 0xff, 0x20, 0x0,
    0x0, 0x7f, 0xff, 0x10, 0x0, 0x3f, 0xff, 0x90,
    0x0, 0x0, 0xef, 0xfa, 0x0, 0x0, 0xc, 0xff,
    0xf1, 0x0, 0x5, 0xff, 0xf2, 0x0, 0x0, 0x5,
    0xff, 0xf7, 0x0, 0xc, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0xdf, 0xfe, 0x0, 0x3f, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x50, 0xaf, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xc2, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xfc, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf3, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x5f, 0xff, 0x50, 0x0, 0x0, 0xe, 0xff, 0xc0,
    0x0, 0x0, 0x7, 0xff, 0xe0, 0xf, 0xff, 0xa0,
    0x0, 0x0, 0x3f, 0xff, 0xf1, 0x0, 0x0, 0xd,
    0xff, 0x90, 0xa, 0xff, 0xf0, 0x0, 0x0, 0x9f,
    0xff, 0xf6, 0x0, 0x0, 0x2f, 0xff, 0x40, 0x5,
    0xff, 0xf5, 0x0, 0x0, 0xef, 0xff, 0xfc, 0x0,
    0x0, 0x7f, 0xfe, 0x0, 0x0, 0xff, 0xfa, 0x0,
    0x4, 0xff, 0xff, 0xff, 0x10, 0x0, 0xdf, 0xf9,
    0x0, 0x0, 0xaf, 0xff, 0x0, 0x9, 0xff, 0x8d,
    0xff, 0x60, 0x2, 0xff, 0xf4, 0x0, 0x0, 0x5f,
    0xff, 0x50, 0xf, 0xff, 0x38, 0xff, 0xb0, 0x8,
    0xff, 0xe0, 0x0, 0x0, 0xf, 0xff, 0xa0, 0x5f,
    0xfd, 0x3, 0xff, 0xf1, 0xd, 0xff, 0x90, 0x0,
    0x0, 0xa, 0xff, 0xf0, 0xaf, 0xf8, 0x0, 0xdf,
    0xf6, 0x3f, 0xff, 0x30, 0x0, 0x0, 0x5, 0xff,
    0xf5, 0xff, 0xf2, 0x0, 0x8f, 0xfb, 0x8f, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x3f, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0x80, 0x0, 0xe, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0x20, 0x0, 0x9, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xfd, 0x0, 0x0,
    0x4, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xef, 0xff,
    0x30, 0x0, 0x0,

    /* U+0058 "X" */
    0x4f, 0xff, 0xc0, 0x0, 0x0, 0x1e, 0xff, 0xd0,
    0x8, 0xff, 0xf7, 0x0, 0x0, 0xaf, 0xff, 0x20,
    0x0, 0xcf, 0xff, 0x30, 0x6, 0xff, 0xf6, 0x0,
    0x0, 0x2f, 0xff, 0xe1, 0x2f, 0xff, 0xa0, 0x0,
    0x0, 0x5, 0xff, 0xfb, 0xcf, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xf4, 0xaf, 0xff, 0x40, 0x0,
    0x0, 0x5f, 0xff, 0x90, 0x1e, 0xff, 0xe1, 0x0,
    0x2, 0xff, 0xfd, 0x0, 0x4, 0xff, 0xfc, 0x0,
    0xc, 0xff, 0xf3, 0x0, 0x0, 0x8f, 0xff, 0x80,
    0x9f, 0xff, 0x80, 0x0, 0x0, 0xc, 0xff, 0xf4,

    /* U+0059 "Y" */
    0x1e, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x9f, 0xfd,
    0x0, 0x6f, 0xff, 0x60, 0x0, 0x0, 0x3f, 0xff,
    0x40, 0x0, 0xcf, 0xfe, 0x10, 0x0, 0xd, 0xff,
    0xa0, 0x0, 0x2, 0xff, 0xfa, 0x0, 0x7, 0xff,
    0xf1, 0x0, 0x0, 0x8, 0xff, 0xf3, 0x2, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0xd, 0xff, 0xd0, 0xbf,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xcf,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x30, 0x0, 0x0, 0x0,

    /* U+005A "Z" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe,
    0xee, 0xee, 0xee, 0xef, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xee, 0xee, 0xee,
    0xee, 0x42, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40,

    /* U+005B "[" */
    0x1a, 0xaa, 0xaa, 0x82, 0xff, 0xff, 0xfc, 0x2f,
    0xff, 0xff, 0xc2, 0xff, 0xf4, 0x0, 0x2f, 0xff,
    0x40, 0x2, 0xff, 0xf4, 0x0, 0x2f, 0xff, 0x40,
    0x2, 0xff, 0xf4, 0x0, 0x2f, 0xff, 0x40, 0x2,
    0xff, 0xf4, 0x0, 0x2f, 0xff, 0x40, 0x2, 0xff,
    0xf4, 0x0, 0x2f, 0xff, 0x40, 0x2, 0xff, 0xf4,
    0x0, 0x2f, 0xff, 0x40, 0x2, 0xff, 0xf4, 0x0,
    0x2f, 0xff, 0x40, 0x2, 0xff, 0xf4, 0x0, 0x2f,
    0xff, 0xcb, 0x82, 0xff, 0xff, 0xfc, 0x2f, 0xff,
    0xff, 0xc0,

    /* U+005C "\\" */
    0x68, 0x81, 0x0, 0x0, 0x0, 0x9, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x3f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf2, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x2f, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xf3, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x1f, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf4, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf5, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x10, 0x0, 0x0, 0x0, 0xaf, 0xf6, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x20, 0x0, 0x0, 0x0, 0x9f, 0xf7,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x30,

    /* U+005D "]" */
    0x7a, 0xaa, 0xaa, 0x2a, 0xff, 0xff, 0xf4, 0xaf,
    0xff, 0xff, 0x40, 0x2, 0xff, 0xf4, 0x0, 0x2f,
    0xff, 0x40, 0x2, 0xff, 0xf4, 0x0, 0x2f, 0xff,
    0x40, 0x2, 0xff, 0xf4, 0x0, 0x2f, 0xff, 0x40,
    0x2, 0xff, 0xf4, 0x0, 0x2f, 0xff, 0x40, 0x2,
    0xff, 0xf4, 0x0, 0x2f, 0xff, 0x40, 0x2, 0xff,
    0xf4, 0x0, 0x2f, 0xff, 0x40, 0x2, 0xff, 0xf4,
    0x0, 0x2f, 0xff, 0x40, 0x2, 0xff, 0xf4, 0x7b,
    0xbf, 0xff, 0x4a, 0xff, 0xff, 0xf4, 0xaf, 0xff,
    0xff, 0x40,

    /* U+005E "^" */
    0x0, 0x0, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xa0, 0x0, 0x0, 0xd, 0xfd, 0xff, 0x10,
    0x0, 0x5, 0xff, 0x2e, 0xf8, 0x0, 0x0, 0xcf,
    0xb0, 0x8f, 0xe0, 0x0, 0x3f, 0xf5, 0x1, 0xff,
    0x60, 0xa, 0xfe, 0x0, 0xb, 0xfd, 0x1, 0xff,
    0x80, 0x0, 0x4f, 0xf4, 0x8f, 0xf1, 0x0, 0x0,
    0xef, 0xb0,

    /* U+005F "_" */
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff,

    /* U+0060 "`" */
    0x6, 0xff, 0xe3, 0x0, 0x0, 0x3d, 0xfe, 0x30,
    0x0, 0x1, 0xaf, 0xe2,

    /* U+0061 "a" */
    0x0, 0x39, 0xcf, 0xfe, 0xb5, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x3, 0xff, 0xec,
    0xdf, 0xff, 0xf7, 0x0, 0x7, 0x30, 0x0, 0x2e,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0x0, 0x4, 0xbe, 0xff, 0xff, 0xff, 0xf0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0xff, 0xf9,
    0x10, 0x6, 0xff, 0xf1, 0x2f, 0xff, 0x30, 0x0,
    0xaf, 0xff, 0x10, 0xff, 0xfb, 0x32, 0x8f, 0xff,
    0xf1, 0x7, 0xff, 0xff, 0xff, 0xef, 0xff, 0x10,
    0x5, 0xbf, 0xfd, 0x83, 0xff, 0xf1,

    /* U+0062 "b" */
    0x37, 0x77, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x19, 0xdf, 0xea,
    0x40, 0x0, 0x6f, 0xff, 0xef, 0xff, 0xff, 0xf8,
    0x0, 0x6f, 0xff, 0xff, 0xde, 0xff, 0xff, 0x60,
    0x6f, 0xff, 0xe3, 0x0, 0x3e, 0xff, 0xe0, 0x6f,
    0xff, 0x40, 0x0, 0x5, 0xff, 0xf4, 0x6f, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf6, 0x6f, 0xff, 0x0,
    0x0, 0x0, 0xff, 0xf6, 0x6f, 0xff, 0x40, 0x0,
    0x5, 0xff, 0xf4, 0x6f, 0xff, 0xe3, 0x0, 0x3e,
    0xff, 0xe0, 0x6f, 0xff, 0xff, 0xde, 0xff, 0xff,
    0x60, 0x6f, 0xff, 0xef, 0xff, 0xff, 0xf8, 0x0,
    0x6f, 0xfd, 0x19, 0xef, 0xea, 0x40, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x7c, 0xef, 0xda, 0x30, 0x0, 0x3,
    0xef, 0xff, 0xff, 0xff, 0x70, 0x2, 0xff, 0xff,
    0xed, 0xff, 0xff, 0x40, 0xbf, 0xff, 0x50, 0x1,
    0xcf, 0x91, 0x1f, 0xff, 0x80, 0x0, 0x1, 0x10,
    0x3, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf8,
    0x0, 0x0, 0x11, 0x0, 0xb, 0xff, 0xf5, 0x0,
    0x1c, 0xf9, 0x10, 0x2f, 0xff, 0xfe, 0xdf, 0xff,
    0xf4, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x17, 0xce, 0xfd, 0xa3, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x57, 0x74, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xfa, 0x0, 0x2, 0x9d, 0xfe, 0xa2,
    0xcf, 0xfa, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x3, 0xff, 0xff, 0xed, 0xff, 0xff, 0xfa,
    0xb, 0xff, 0xf6, 0x0, 0x1c, 0xff, 0xfa, 0x1f,
    0xff, 0x80, 0x0, 0x1, 0xff, 0xfa, 0x3f, 0xff,
    0x40, 0x0, 0x0, 0xff, 0xfa, 0x3f, 0xff, 0x40,
    0x0, 0x0, 0xff, 0xfa, 0x1f, 0xff, 0x80, 0x0,
    0x1, 0xff, 0xfa, 0xb, 0xff, 0xf5, 0x0, 0x1c,
    0xff, 0xfa, 0x3, 0xff, 0xff, 0xed, 0xff, 0xff,
    0xfa, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x2, 0x9d, 0xfe, 0xb3, 0xaf, 0xfa,

    /* U+0065 "e" */
    0x0, 0x1, 0x7d, 0xff, 0xd8, 0x10, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x2, 0xff,
    0xfe, 0xaa, 0xef, 0xff, 0x20, 0xb, 0xff, 0xc1,
    0x0, 0xc, 0xff, 0xa0, 0x1f, 0xff, 0x40, 0x0,
    0x3, 0xff, 0xf0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x1f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xe4, 0x0, 0x2, 0x90, 0x0, 0x2,
    0xff, 0xff, 0xec, 0xdf, 0xfb, 0x0, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x6c,
    0xef, 0xeb, 0x60, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x59, 0xa9, 0x50, 0x0, 0xb, 0xff,
    0xff, 0xf0, 0x0, 0x7f, 0xff, 0xff, 0x90, 0x0,
    0xdf, 0xfc, 0x11, 0x10, 0x0, 0xff, 0xf6, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0x60, 0xdf, 0xff,
    0xff, 0xff, 0x60, 0x8a, 0xff, 0xff, 0xaa, 0x30,
    0x0, 0xff, 0xf7, 0x0, 0x0, 0x0, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0xff, 0xf7, 0x0, 0x0, 0x0, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0xff, 0xf7, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x3, 0xae, 0xfe, 0xa3, 0x7f, 0xfc, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xdf, 0xfc, 0x6, 0xff,
    0xff, 0xca, 0xdf, 0xff, 0xfc, 0xe, 0xff, 0xe2,
    0x0, 0x7, 0xff, 0xfc, 0x2f, 0xff, 0x50, 0x0,
    0x0, 0xff, 0xfc, 0x4f, 0xff, 0x30, 0x0, 0x0,
    0xff, 0xfc, 0x2f, 0xff, 0x60, 0x0, 0x0, 0xff,
    0xfc, 0xe, 0xff, 0xf4, 0x0, 0x9, 0xff, 0xfc,
    0x6, 0xff, 0xff, 0xed, 0xff, 0xff, 0xfc, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x3,
    0xae, 0xfe, 0xb3, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf9, 0x0, 0x99, 0x20, 0x0,
    0x9, 0xff, 0xf5, 0x2, 0xff, 0xfe, 0xcc, 0xff,
    0xff, 0xd0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x20, 0x0, 0x17, 0xbe, 0xff, 0xeb, 0x60, 0x0,

    /* U+0068 "h" */
    0x24, 0x44, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xf1, 0x8d, 0xfe, 0xb4, 0x0, 0x6f,
    0xff, 0xef, 0xff, 0xff, 0xf6, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x6f, 0xff, 0xe4, 0x1,
    0x9f, 0xff, 0x76, 0xff, 0xf5, 0x0, 0x0, 0xef,
    0xf9, 0x6f, 0xff, 0x10, 0x0, 0xc, 0xff, 0xa6,
    0xff, 0xf0, 0x0, 0x0, 0xcf, 0xfb, 0x6f, 0xff,
    0x0, 0x0, 0xc, 0xff, 0xb6, 0xff, 0xf0, 0x0,
    0x0, 0xcf, 0xfb, 0x6f, 0xff, 0x0, 0x0, 0xc,
    0xff, 0xb6, 0xff, 0xf0, 0x0, 0x0, 0xcf, 0xfb,
    0x6f, 0xff, 0x0, 0x0, 0xc, 0xff, 0xb0,

    /* U+0069 "i" */
    0x3d, 0xfa, 0xb, 0xff, 0xf5, 0xaf, 0xff, 0x41,
    0xac, 0x70, 0x0, 0x0, 0x6, 0xff, 0xf0, 0x6f,
    0xff, 0x6, 0xff, 0xf0, 0x6f, 0xff, 0x6, 0xff,
    0xf0, 0x6f, 0xff, 0x6, 0xff, 0xf0, 0x6f, 0xff,
    0x6, 0xff, 0xf0, 0x6f, 0xff, 0x6, 0xff, 0xf0,
    0x6f, 0xff, 0x0,

    /* U+006A "j" */
    0x0, 0x0, 0x9, 0xc9, 0x0, 0x0, 0x8, 0xff,
    0xf7, 0x0, 0x0, 0x9f, 0xff, 0x70, 0x0, 0x1,
    0xcf, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x20,
    0x0, 0x4, 0xff, 0xf2, 0x0, 0x0, 0x4f, 0xff,
    0x20, 0x0, 0x4, 0xff, 0xf2, 0x0, 0x0, 0x4f,
    0xff, 0x20, 0x0, 0x4, 0xff, 0xf2, 0x0, 0x0,
    0x4f, 0xff, 0x20, 0x0, 0x4, 0xff, 0xf2, 0x0,
    0x0, 0x4f, 0xff, 0x20, 0x0, 0x4, 0xff, 0xf2,
    0x0, 0x0, 0x4f, 0xff, 0x20, 0x0, 0x4, 0xff,
    0xf2, 0x0, 0x0, 0x7f, 0xff, 0x10, 0x5d, 0xcf,
    0xff, 0xd0, 0xb, 0xff, 0xff, 0xf4, 0x0, 0x8d,
    0xfe, 0xb3, 0x0,

    /* U+006B "k" */
    0x24, 0x44, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x0, 0x0, 0x7f,
    0xff, 0xa0, 0x6f, 0xff, 0x0, 0x8, 0xff, 0xfa,
    0x0, 0x6f, 0xff, 0x0, 0x9f, 0xff, 0xa0, 0x0,
    0x6f, 0xff, 0x9, 0xff, 0xfb, 0x0, 0x0, 0x6f,
    0xff, 0xaf, 0xff, 0xb0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x6f, 0xff, 0xfb, 0xff,
    0xfe, 0x20, 0x0, 0x6f, 0xff, 0x70, 0x5f, 0xff,
    0xc0, 0x0, 0x6f, 0xff, 0x0, 0x9, 0xff, 0xf9,
    0x0, 0x6f, 0xff, 0x0, 0x0, 0xcf, 0xff, 0x60,
    0x6f, 0xff, 0x0, 0x0, 0x1e, 0xff, 0xf3,

    /* U+006C "l" */
    0x24, 0x44, 0x6, 0xff, 0xf0, 0x6f, 0xff, 0x6,
    0xff, 0xf0, 0x6f, 0xff, 0x6, 0xff, 0xf0, 0x6f,
    0xff, 0x6, 0xff, 0xf0, 0x6f, 0xff, 0x6, 0xff,
    0xf0, 0x6f, 0xff, 0x6, 0xff, 0xf0, 0x6f, 0xff,
    0x6, 0xff, 0xf0, 0x6f, 0xff, 0x6, 0xff, 0xf0,
    0x6f, 0xff, 0x0,

    /* U+006D "m" */
    0x6f, 0xfd, 0x19, 0xdf, 0xd9, 0x20, 0x18, 0xdf,
    0xeb, 0x30, 0x6, 0xff, 0xfe, 0xff, 0xff, 0xfe,
    0x5e, 0xff, 0xff, 0xff, 0x50, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6,
    0xff, 0xfe, 0x30, 0x2d, 0xff, 0xff, 0x60, 0x1a,
    0xff, 0xf5, 0x6f, 0xff, 0x50, 0x0, 0x5f, 0xff,
    0x90, 0x0, 0x1f, 0xff, 0x86, 0xff, 0xf1, 0x0,
    0x2, 0xff, 0xf5, 0x0, 0x0, 0xef, 0xf9, 0x6f,
    0xff, 0x0, 0x0, 0x2f, 0xff, 0x40, 0x0, 0xd,
    0xff, 0x96, 0xff, 0xf0, 0x0, 0x2, 0xff, 0xf4,
    0x0, 0x0, 0xdf, 0xf9, 0x6f, 0xff, 0x0, 0x0,
    0x2f, 0xff, 0x40, 0x0, 0xd, 0xff, 0x96, 0xff,
    0xf0, 0x0, 0x2, 0xff, 0xf4, 0x0, 0x0, 0xdf,
    0xf9, 0x6f, 0xff, 0x0, 0x0, 0x2f, 0xff, 0x40,
    0x0, 0xd, 0xff, 0x96, 0xff, 0xf0, 0x0, 0x2,
    0xff, 0xf4, 0x0, 0x0, 0xdf, 0xf9,

    /* U+006E "n" */
    0x6f, 0xfd, 0x8, 0xdf, 0xeb, 0x40, 0x6, 0xff,
    0xfd, 0xff, 0xff, 0xff, 0x60, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x26, 0xff, 0xfe, 0x40, 0x19,
    0xff, 0xf7, 0x6f, 0xff, 0x50, 0x0, 0xe, 0xff,
    0x96, 0xff, 0xf1, 0x0, 0x0, 0xcf, 0xfa, 0x6f,
    0xff, 0x0, 0x0, 0xc, 0xff, 0xb6, 0xff, 0xf0,
    0x0, 0x0, 0xcf, 0xfb, 0x6f, 0xff, 0x0, 0x0,
    0xc, 0xff, 0xb6, 0xff, 0xf0, 0x0, 0x0, 0xcf,
    0xfb, 0x6f, 0xff, 0x0, 0x0, 0xc, 0xff, 0xb6,
    0xff, 0xf0, 0x0, 0x0, 0xcf, 0xfb,

    /* U+006F "o" */
    0x0, 0x1, 0x7c, 0xef, 0xd9, 0x30, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x2, 0xff,
    0xff, 0xed, 0xff, 0xff, 0x80, 0xb, 0xff, 0xf5,
    0x0, 0x2c, 0xff, 0xf2, 0x1f, 0xff, 0x80, 0x0,
    0x1, 0xff, 0xf7, 0x3f, 0xff, 0x30, 0x0, 0x0,
    0xdf, 0xfa, 0x3f, 0xff, 0x30, 0x0, 0x0, 0xdf,
    0xfa, 0x1f, 0xff, 0x80, 0x0, 0x1, 0xff, 0xf7,
    0xb, 0xff, 0xf5, 0x0, 0x1c, 0xff, 0xf2, 0x2,
    0xff, 0xff, 0xed, 0xff, 0xff, 0x80, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x1, 0x7c,
    0xef, 0xd9, 0x30, 0x0,

    /* U+0070 "p" */
    0x6f, 0xfd, 0x19, 0xef, 0xea, 0x40, 0x0, 0x6f,
    0xff, 0xef, 0xff, 0xff, 0xf8, 0x0, 0x6f, 0xff,
    0xff, 0xde, 0xff, 0xff, 0x60, 0x6f, 0xff, 0xe3,
    0x0, 0x3e, 0xff, 0xe0, 0x6f, 0xff, 0x40, 0x0,
    0x5, 0xff, 0xf4, 0x6f, 0xff, 0x0, 0x0, 0x0,
    0xff, 0xf6, 0x6f, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xf6, 0x6f, 0xff, 0x40, 0x0, 0x5, 0xff, 0xf4,
    0x6f, 0xff, 0xe3, 0x0, 0x3e, 0xff, 0xe0, 0x6f,
    0xff, 0xff, 0xde, 0xff, 0xff, 0x70, 0x6f, 0xff,
    0xef, 0xff, 0xff, 0xf9, 0x0, 0x6f, 0xff, 0x19,
    0xdf, 0xea, 0x40, 0x0, 0x6f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x2, 0x9d, 0xfe, 0xa3, 0xbf, 0xf9, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x4, 0xff,
    0xff, 0xed, 0xff, 0xff, 0xf9, 0xc, 0xff, 0xf5,
    0x0, 0x2c, 0xff, 0xf9, 0x1f, 0xff, 0x80, 0x0,
    0x1, 0xff, 0xf9, 0x3f, 0xff, 0x30, 0x0, 0x0,
    0xff, 0xf9, 0x3f, 0xff, 0x30, 0x0, 0x0, 0xff,
    0xf9, 0x1f, 0xff, 0x80, 0x0, 0x1, 0xff, 0xf9,
    0xc, 0xff, 0xf5, 0x0, 0x1c, 0xff, 0xf9, 0x4,
    0xff, 0xff, 0xed, 0xff, 0xff, 0xf9, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x2, 0x9d,
    0xfe, 0xa2, 0xdf, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf9,

    /* U+0072 "r" */
    0x6f, 0xfd, 0x18, 0xde, 0x6f, 0xff, 0xef, 0xff,
    0x6f, 0xff, 0xff, 0xfe, 0x6f, 0xff, 0xf6, 0x10,
    0x6f, 0xff, 0x70, 0x0, 0x6f, 0xff, 0x20, 0x0,
    0x6f, 0xff, 0x0, 0x0, 0x6f, 0xff, 0x0, 0x0,
    0x6f, 0xff, 0x0, 0x0, 0x6f, 0xff, 0x0, 0x0,
    0x6f, 0xff, 0x0, 0x0, 0x6f, 0xff, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x28, 0xdf, 0xfe, 0xc7, 0x10, 0x4, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xe, 0xff, 0xea, 0xab,
    0xff, 0x10, 0x3f, 0xff, 0x10, 0x0, 0x3, 0x0,
    0x2f, 0xff, 0xa5, 0x20, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0xb5, 0x0, 0x0, 0x8e, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x14, 0x7c, 0xff, 0xf0,
    0x5, 0x40, 0x0, 0x3, 0xff, 0xf1, 0xe, 0xfe,
    0xb9, 0xae, 0xff, 0xd0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x3, 0x9c, 0xef, 0xec, 0x81, 0x0,

    /* U+0074 "t" */
    0x0, 0x88, 0x83, 0x0, 0x0, 0x0, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xf7, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x60, 0xdf, 0xff, 0xff, 0xff,
    0x60, 0x8a, 0xff, 0xff, 0xaa, 0x30, 0x0, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0xff, 0xf7, 0x0, 0x0, 0x0, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xcd,
    0x80, 0x0, 0x3f, 0xff, 0xff, 0xe0, 0x0, 0x3,
    0xbe, 0xfd, 0x90,

    /* U+0075 "u" */
    0x7f, 0xff, 0x0, 0x0, 0xe, 0xff, 0x87, 0xff,
    0xf0, 0x0, 0x0, 0xef, 0xf8, 0x7f, 0xff, 0x0,
    0x0, 0xe, 0xff, 0x87, 0xff, 0xf0, 0x0, 0x0,
    0xef, 0xf8, 0x7f, 0xff, 0x0, 0x0, 0xe, 0xff,
    0x87, 0xff, 0xf0, 0x0, 0x0, 0xef, 0xf8, 0x7f,
    0xff, 0x0, 0x0, 0xf, 0xff, 0x86, 0xff, 0xf2,
    0x0, 0x4, 0xff, 0xf8, 0x4f, 0xff, 0xc2, 0x3,
    0xdf, 0xff, 0x80, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x3, 0xff, 0xff, 0xff, 0xef, 0xff, 0x80,
    0x2, 0xae, 0xfe, 0x92, 0xcf, 0xf8,

    /* U+0076 "v" */
    0xe, 0xff, 0x80, 0x0, 0x0, 0x2f, 0xff, 0x20,
    0x8f, 0xfe, 0x0, 0x0, 0x9, 0xff, 0xb0, 0x2,
    0xff, 0xf5, 0x0, 0x0, 0xff, 0xf4, 0x0, 0xb,
    0xff, 0xc0, 0x0, 0x6f, 0xfd, 0x0, 0x0, 0x4f,
    0xff, 0x20, 0xd, 0xff, 0x70, 0x0, 0x0, 0xef,
    0xf9, 0x4, 0xff, 0xf1, 0x0, 0x0, 0x7, 0xff,
    0xe0, 0xaf, 0xfa, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0x7f, 0xff, 0x30, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x80,
    0x0, 0x0,

    /* U+0077 "w" */
    0xcf, 0xf6, 0x0, 0x0, 0x3f, 0xff, 0x0, 0x0,
    0x9, 0xff, 0x67, 0xff, 0xc0, 0x0, 0x9, 0xff,
    0xf6, 0x0, 0x0, 0xff, 0xf1, 0x1f, 0xff, 0x10,
    0x0, 0xef, 0xff, 0xb0, 0x0, 0x5f, 0xfb, 0x0,
    0xbf, 0xf7, 0x0, 0x4f, 0xff, 0xff, 0x10, 0xa,
    0xff, 0x50, 0x6, 0xff, 0xc0, 0xa, 0xff, 0xff,
    0xf6, 0x0, 0xff, 0xf0, 0x0, 0xf, 0xff, 0x20,
    0xff, 0xf5, 0xff, 0xc0, 0x6f, 0xf9, 0x0, 0x0,
    0xaf, 0xf7, 0x5f, 0xf9, 0xf, 0xff, 0x1b, 0xff,
    0x40, 0x0, 0x4, 0xff, 0xdb, 0xff, 0x30, 0x9f,
    0xf8, 0xff, 0xe0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xd0, 0x4, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf8, 0x0, 0xe, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x3, 0xff, 0xff, 0x20, 0x0, 0x8f,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xc0,
    0x0, 0x3, 0xff, 0xf7, 0x0, 0x0,

    /* U+0078 "x" */
    0x6f, 0xff, 0x70, 0x0, 0x3f, 0xff, 0x70, 0xaf,
    0xff, 0x20, 0xd, 0xff, 0xb0, 0x0, 0xdf, 0xfd,
    0xa, 0xff, 0xe1, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x6, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x4f, 0xff, 0xcf,
    0xff, 0x60, 0x0, 0x1e, 0xff, 0xa0, 0xaf, 0xff,
    0x20, 0xc, 0xff, 0xd0, 0x1, 0xef, 0xfd, 0x8,
    0xff, 0xf3, 0x0, 0x4, 0xff, 0xfa,

    /* U+0079 "y" */
    0xe, 0xff, 0x90, 0x0, 0x0, 0x2f, 0xff, 0x10,
    0x8f, 0xff, 0x0, 0x0, 0x9, 0xff, 0xa0, 0x1,
    0xff, 0xf6, 0x0, 0x1, 0xff, 0xf3, 0x0, 0xa,
    0xff, 0xd0, 0x0, 0x7f, 0xfc, 0x0, 0x0, 0x3f,
    0xff, 0x40, 0xd, 0xff, 0x60, 0x0, 0x0, 0xcf,
    0xfa, 0x4, 0xff, 0xe0, 0x0, 0x0, 0x5, 0xff,
    0xf1, 0xbf, 0xf8, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xaf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x30, 0x8, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x7f, 0xdd, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x5c, 0xff, 0xd7, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xa, 0xaa, 0xaa, 0xcf,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0xdf, 0xfd, 0x10,
    0x0, 0x0, 0xb, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0x40, 0x0, 0x0, 0x6, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x90, 0x0, 0x0,
    0x2, 0xef, 0xfc, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xfb, 0xaa, 0xaa, 0xa3, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xf5,

    /* U+007B "{" */
    0x0, 0x1, 0x7a, 0xa2, 0x0, 0x4f, 0xff, 0xf4,
    0x0, 0xdf, 0xff, 0xf4, 0x1, 0xff, 0xfa, 0x10,
    0x2, 0xff, 0xf4, 0x0, 0x2, 0xff, 0xf4, 0x0,
    0x2, 0xff, 0xf4, 0x0, 0x2, 0xff, 0xf4, 0x0,
    0x3, 0xff, 0xf3, 0x0, 0x9d, 0xff, 0xf1, 0x0,
    0xdf, 0xff, 0x50, 0x0, 0xdf, 0xff, 0xd0, 0x0,
    0x5, 0xff, 0xf3, 0x0, 0x2, 0xff, 0xf4, 0x0,
    0x2, 0xff, 0xf4, 0x0, 0x2, 0xff, 0xf4, 0x0,
    0x2, 0xff, 0xf4, 0x0, 0x1, 0xff, 0xf7, 0x0,
    0x0, 0xef, 0xff, 0xc2, 0x0, 0x7f, 0xff, 0xf4,
    0x0, 0x6, 0xcf, 0xf4,

    /* U+007C "|" */
    0x19, 0x98, 0x2f, 0xff, 0x2f, 0xff, 0x2f, 0xff,
    0x2f, 0xff, 0x2f, 0xff, 0x2f, 0xff, 0x2f, 0xff,
    0x2f, 0xff, 0x2f, 0xff, 0x2f, 0xff, 0x2f, 0xff,
    0x2f, 0xff, 0x2f, 0xff, 0x2f, 0xff, 0x2f, 0xff,
    0x2f, 0xff, 0x2f, 0xff, 0x2f, 0xff, 0x2f, 0xff,
    0x2f, 0xff,

    /* U+007D "}" */
    0x7a, 0x95, 0x0, 0x0, 0xaf, 0xff, 0xc0, 0x0,
    0xaf, 0xff, 0xf7, 0x0, 0x3, 0xff, 0xfb, 0x0,
    0x0, 0xbf, 0xfc, 0x0, 0x0, 0xaf, 0xfc, 0x0,
    0x0, 0xaf, 0xfc, 0x0, 0x0, 0xaf, 0xfc, 0x0,
    0x0, 0xaf, 0xfd, 0x0, 0x0, 0x7f, 0xff, 0xb4,
    0x0, 0xb, 0xff, 0xf7, 0x0, 0x4f, 0xff, 0xf7,
    0x0, 0x9f, 0xfe, 0x10, 0x0, 0xaf, 0xfc, 0x0,
    0x0, 0xaf, 0xfc, 0x0, 0x0, 0xaf, 0xfc, 0x0,
    0x0, 0xaf, 0xfc, 0x0, 0x0, 0xdf, 0xfb, 0x0,
    0x7d, 0xff, 0xf8, 0x0, 0xaf, 0xff, 0xf2, 0x0,
    0xaf, 0xea, 0x20, 0x0,

    /* U+007E "~" */
    0x0, 0x57, 0x40, 0x0, 0x7, 0x70, 0xb, 0xff,
    0xfb, 0x10, 0x3f, 0xf0, 0x6f, 0xff, 0xff, 0xe8,
    0xdf, 0xc0, 0xbf, 0xb0, 0x5f, 0xff, 0xff, 0x50,
    0xdf, 0x50, 0x2, 0xbf, 0xe7, 0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 100, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 102, .box_w = 5, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 38, .adv_w = 154, .box_w = 8, .box_h = 7, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 66, .adv_w = 253, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 186, .adv_w = 225, .box_w = 14, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 340, .adv_w = 309, .box_w = 19, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 483, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 603, .adv_w = 81, .box_w = 3, .box_h = 7, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 614, .adv_w = 126, .box_w = 7, .box_h = 21, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 688, .adv_w = 126, .box_w = 7, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 762, .adv_w = 153, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 807, .adv_w = 211, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 868, .adv_w = 92, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 895, .adv_w = 136, .box_w = 7, .box_h = 3, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 906, .adv_w = 92, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 921, .adv_w = 138, .box_w = 11, .box_h = 21, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 1037, .adv_w = 239, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1150, .adv_w = 138, .box_w = 7, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1203, .adv_w = 208, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1301, .adv_w = 208, .box_w = 14, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1406, .adv_w = 243, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1519, .adv_w = 209, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1617, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1722, .adv_w = 218, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1820, .adv_w = 232, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1925, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2030, .adv_w = 92, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2066, .adv_w = 92, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2114, .adv_w = 211, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 2180, .adv_w = 211, .box_w = 11, .box_h = 8, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 2224, .adv_w = 211, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 2290, .adv_w = 207, .box_w = 13, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2388, .adv_w = 364, .box_w = 22, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2597, .adv_w = 270, .box_w = 19, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2740, .adv_w = 269, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2853, .adv_w = 258, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2973, .adv_w = 291, .box_w = 17, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3101, .adv_w = 236, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3199, .adv_w = 225, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3297, .adv_w = 271, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3417, .adv_w = 284, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3530, .adv_w = 115, .box_w = 5, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3568, .adv_w = 190, .box_w = 12, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3658, .adv_w = 260, .box_w = 16, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3778, .adv_w = 213, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3876, .adv_w = 336, .box_w = 19, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4019, .adv_w = 284, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4132, .adv_w = 297, .box_w = 18, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4267, .adv_w = 258, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4380, .adv_w = 297, .box_w = 19, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4551, .adv_w = 259, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4664, .adv_w = 225, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4769, .adv_w = 218, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4874, .adv_w = 277, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4987, .adv_w = 263, .box_w = 18, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5122, .adv_w = 409, .box_w = 26, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5317, .adv_w = 251, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5437, .adv_w = 238, .box_w = 17, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5565, .adv_w = 236, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5678, .adv_w = 130, .box_w = 7, .box_h = 21, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 5752, .adv_w = 138, .box_w = 11, .box_h = 21, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5868, .adv_w = 130, .box_w = 7, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5942, .adv_w = 211, .box_w = 11, .box_h = 9, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 5992, .adv_w = 176, .box_w = 11, .box_h = 2, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6003, .adv_w = 211, .box_w = 8, .box_h = 3, .ofs_x = 1, .ofs_y = 13},
    {.bitmap_index = 6015, .adv_w = 217, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6093, .adv_w = 243, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6212, .adv_w = 208, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6290, .adv_w = 244, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6409, .adv_w = 222, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6493, .adv_w = 136, .box_w = 10, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6578, .adv_w = 246, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6690, .adv_w = 243, .box_w = 13, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6801, .adv_w = 106, .box_w = 5, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6844, .adv_w = 108, .box_w = 9, .box_h = 22, .ofs_x = -3, .ofs_y = -4},
    {.bitmap_index = 6943, .adv_w = 232, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7062, .adv_w = 106, .box_w = 5, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7105, .adv_w = 369, .box_w = 21, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7231, .adv_w = 243, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7309, .adv_w = 231, .box_w = 14, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7393, .adv_w = 243, .box_w = 14, .box_h = 16, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 7505, .adv_w = 243, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 7617, .adv_w = 152, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7665, .adv_w = 187, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7737, .adv_w = 153, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7812, .adv_w = 242, .box_w = 13, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7890, .adv_w = 210, .box_w = 15, .box_h = 12, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 7980, .adv_w = 330, .box_w = 21, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8106, .adv_w = 209, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8184, .adv_w = 210, .box_w = 15, .box_h = 16, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 8304, .adv_w = 191, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8376, .adv_w = 138, .box_w = 8, .box_h = 21, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 8460, .adv_w = 109, .box_w = 4, .box_h = 21, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 8502, .adv_w = 138, .box_w = 8, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 8586, .adv_w = 211, .box_w = 12, .box_h = 5, .ofs_x = 1, .ofs_y = 6}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 40,
    39, 38, 41, 42, 38, 38, 43, 43,
    39, 43, 39, 43, 44, 45, 46, 47,
    47, 48, 49, 50, 0, 0, 35, 9
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 1, 0, 0, 0, 0, 0, 3,
    0, 1, 0, 0, 4, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 8, 7, 0, 4, 0, 7, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 16, 0, 8, -6, 0,
    0, 7, 0, -19, -21, 1, 15, 6,
    5, -14, 1, 14, 0, 13, 4, 10,
    0, -15, 0, 0, 5, 0, 0, 0,
    0, 0, 0, 21, 5, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -11, 0, 0,
    0, 0, 0, -7, 5, 7, 0, 0,
    -4, 0, -1, 4, 0, -4, 0, -4,
    -2, -7, 0, 0, 0, 0, -4, 0,
    0, -4, -5, 0, 0, -4, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -4, -4, -4, 0, 0, -8,
    0, -44, 0, 0, -7, -18, 7, 11,
    0, 0, -7, 4, 4, 11, 7, -5,
    7, 0, 0, -19, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    0, 0, 2, 0, 9, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, -14, 0, -14, -1, 0, 0, -11,
    0, -1, 11, 0, -11, -5, -1, 1,
    0, -5, 0, 0, -3, -23, 0, 2,
    0, 10, -8, 0, -7, 0, -14, 2,
    0, -28, -5, 7, 4, 0, 0, 0,
    0, 0, 0, 0, 4, 0, -6, -1,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 13, 0, 4, 0,
    0, -7, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 15, 5, 1, 0, 0,
    0, 0, 0, 36, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 3, 7,
    4, 11, -4, 0, 0, 7, -4, -13,
    -46, 1, 8, 7, -1, -4, 0, 8,
    0, 10, 0, 10, 0, -34, 0, -5,
    11, 0, 11, -4, 7, 4, 0, 0,
    1, -4, 0, 0, -5, 0, 0, 0,
    28, 0, 11, 0, 13, 6, 13, 5,
    0, 0, -5, -12, 0, 0, 0, -4,
    1, -3, 0, 1, -7, -6, -7, 1,
    0, -4, 0, 0, 0, -14, 1, -8,
    0, -7, -13, 0, -9, -7, -12, 0,
    0, -23, 0, 0, 0, 0, 4, 0,
    0, 0, 0, 0, 4, 0, -6, -9,
    -6, -4, 1, -19, 4, -23, 0, 0,
    0, -13, -3, 0, 32, -5, -6, 4,
    4, 0, 0, -6, 4, 0, 0, -20,
    -7, 11, 0, 19, -12, -2, -13, 0,
    -13, 5, 0, -33, 0, 4, 4, 0,
    -2, 0, 0, 4, 0, 0, -1, -2,
    -11, 0, -11, 0, 0, 21, -7, 0,
    -12, 0, 13, 0, -25, -33, -26, -7,
    11, 0, 0, -23, 0, 2, -9, 0,
    -5, 0, -7, -14, 0, -4, 11, 0,
    11, 0, 11, 0, 0, 5, 11, -41,
    -23, 0, -23, 0, 5, 1, -23, -23,
    -8, -23, -11, -18, -11, -23, 0, 1,
    0, 0, 0, 0, 0, 1, 1, -5,
    -7, 0, -1, -1, -4, 0, 0, -1,
    0, 0, 0, -7, 0, -4, 0, -9,
    -7, 0, -9, -13, -13, -5, 0, -7,
    0, -7, 0, 0, 0, 0, 0, -3,
    0, 0, 4, 0, 1, -4, 1, 0,
    0, 0, 0, 4, -1, 0, 0, 0,
    -1, 4, 4, 0, 0, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 1,
    0, 6, -1, 0, -5, 0, -6, 0,
    0, -1, 0, 11, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, -1, 0, -4, 0, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    -2, 0, -4, -3, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, -4, -4,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    -3, -6, -3, 0, 0, -11, -1, -11,
    7, 0, 0, -7, 4, 7, 8, 0,
    -9, 0, -2, 0, 0, -15, 4, -1,
    3, -20, 4, 0, 0, 1, -19, 0,
    -20, -4, -31, -1, 0, -18, 0, 7,
    10, 0, 6, 0, 0, 0, 0, 0,
    1, 0, -8, -6, -8, 0, 0, 0,
    0, -4, 0, 0, 0, -4, 0, 0,
    0, 0, 0, -2, -2, 0, -2, -4,
    0, 0, 0, 0, 0, 0, 0, -4,
    -4, 0, -3, -5, -2, 0, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, -4, 0,
    0, -1, 0, -7, 4, 0, 0, -2,
    2, 4, 4, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 0, -4, 0, -4, -1, -2, 0,
    0, 0, 0, 0, 0, 0, 2, 0,
    0, -3, 0, 0, 0, 0, -5, -5,
    -5, 0, 0, 11, -1, 1, -10, 0,
    0, 8, -18, -17, -14, -7, 4, 0,
    -2, -23, -6, 0, -6, 0, -7, 5,
    -6, -21, 0, -8, 0, 0, 2, 0,
    4, -1, 0, 4, 3, -11, -13, 0,
    -18, 0, 0, -8, -7, -11, -2, -8,
    1, -4, 1, -8, 0, 0, 0, -4,
    0, 0, 0, 1, 0, 4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -2, 0, 0, -4, 0,
    -6, -8, -8, 0, 0, -11, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, -1, -4, -1, 0, 0, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    19, 0, 0, 0, 0, 0, 0, 1,
    0, 0, 0, -4, 0, 4, 0, 12,
    -4, 0, -9, -3, -13, 0, 0, -7,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, 5, 0, 1, -4, 1, 0,
    -1, 0, 0, 0, -4, 0, 0, 2,
    0, -18, -11, 0, 0, 0, -5, -18,
    0, 0, -4, 4, 0, -6, -1, -16,
    0, -11, 0, 0, -6, -7, -6, -4,
    -3, 0, 0, -5, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 4, 0,
    4, 0, 0, -6, 0, 0, 0, 0,
    2, 0, 1, -7, -7, 0, -4, -4,
    -5, 0, 0, 0, 0, 0, 0, -11,
    0, -4, 0, -5, -4, 0, -8, -9,
    -11, -2, 0, -7, 3, -11, 0, 0,
    0, 0, 0, 28, 0, 0, 2, 0,
    0, -6, 0, 0, 0, -15, 0, 0,
    0, 0, 0, -34, -8, 11, 11, -4,
    -15, 0, 4, -5, 0, -18, -2, -4,
    4, -25, -4, 8, 0, 5, -12, -5,
    -13, -13, -15, 0, 0, -21, 0, 19,
    0, 0, -2, 0, 0, 0, 0, -2,
    -2, -4, -10, -13, -10, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, 0, -2, -4, -5, 0, 0, -7,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -7, 0, 0, 7,
    -1, 4, 0, -7, 4, -1, 0, -5,
    -4, 0, -4, -4, -3, 0, -5, -5,
    0, 0, -2, -1, -1, -5, -2, 0,
    0, -4, 0, 4, -1, 0, -7, 0,
    0, 0, 0, -7, 0, -5, 0, -5,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, -7, 4, 0, -4, 0, -1,
    -2, -6, -1, -1, -1, 0, -1, -2,
    0, 0, 0, 0, 0, 0, -4, -2,
    -2, 0, 0, 0, 0, 2, -1, 0,
    -1, 0, 0, 0, 0, -1, -2, -1,
    -2, -2, -2, -2, 5, 14, 0, 0,
    -8, 0, -1, 7, 0, -4, -14, -4,
    5, 1, 0, -15, -5, 4, -5, 3,
    0, 1, -2, -10, 0, -5, 2, 0,
    0, -5, 0, 0, 0, 4, 4, -7,
    -5, 0, -5, 0, 0, -5, -4, -4,
    0, -5, 2, -5, 2, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, -4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, -4, 0, 0, -4,
    -4, 0, 0, 0, 0, -4, 0, 0,
    0, 0, 0, -2, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, -5, 0,
    -7, 0, 0, 0, -13, 0, 1, -8,
    7, 1, -1, -15, 0, 0, -8, -4,
    0, -14, -9, -11, 0, 0, -17, -4,
    -14, -14, -18, 0, -5, 0, 5, 23,
    -4, 0, -8, 0, 0, 0, -4, -5,
    -8, -6, -13, -16, -13, -9, 0, 0,
    -1, 0, 1, 0, 0, -25, 0, 11,
    6, -6, -12, 0, 1, -6, 0, -18,
    -1, -4, 7, -31, -4, 1, 0, 0,
    -23, -3, -17, -4, -25, 0, 0, -25,
    0, 18, 1, 0, -1, 0, 0, 0,
    0, 0, -2, -1, -13, -1, -13, 0,
    0, 0, 0, 0, -10, 0, -2, 0,
    -1, -10, -15, 0, 0, -2, -5, -11,
    -4, 0, -3, 0, 0, 0, 0, -16,
    -4, -11, -10, -5, -6, -9, -4, -5,
    0, -7, -2, -13, -5, 0, -4, 0,
    0, -4, -4, 0, 2, 0, -1, -11,
    -1, 0, 0, -6, 0, 0, 0, 0,
    2, 0, 1, -7, 18, 0, -4, -4,
    -5, 0, 0, 0, 0, 0, 0, -11,
    0, -4, 0, -5, -4, 0, -8, -9,
    -11, -2, 0, -7, 5, 14, 0, 0,
    0, 0, 0, 28, 0, 0, 2, 0,
    0, -6, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    0, 0, -1, -7, 0, 0, 0, 0,
    0, -2, 0, 0, 0, -4, -4, 0,
    0, -7, -4, 0, 0, -7, 0, 5,
    -2, 0, 0, 0, 0, 0, 0, 0,
    2, 0, 0, 0, 0, 0, 7, 5,
    -2, 0, -10, -3, 0, 11, -13, -12,
    -7, -7, 14, 8, 4, -30, -3, 7,
    -4, 0, -4, 7, -4, -12, 0, -4,
    4, -4, -5, -11, -5, 0, 0, 11,
    7, 0, -10, 0, -19, 0, 0, 15,
    -4, -13, 1, -4, -11, -11, -11, -4,
    4, 0, -5, 0, -8, 0, 5, 11,
    -9, -13, -14, -9, 11, 0, 1, -27,
    -4, 4, -6, -3, -9, 0, -8, -13,
    -5, -5, -5, 0, 0, -9, -9, -4,
    0, 11, 9, -4, -19, 0, -19, 0,
    -2, -7, -12, -21, -1, -12, -6, -11,
    -6, -11, 0, 0, -4, 0, -7, -2,
    0, -4, -7, 0, 5, -13, 4, 0,
    0, -20, 0, -4, -8, -6, -3, -11,
    -9, -13, -8, 0, -11, -4, -9, -4,
    -11, -4, 0, 0, 1, 15, -6, 0,
    -11, 0, 0, 0, -4, -7, -9, -10,
    -12, -15, -12, -6, 7, 0, -5, 0,
    -18, -2, 4, 7, -12, -13, -7, -13,
    13, -4, 2, -34, -7, 7, -8, -6,
    -13, 0, -11, -15, -2, -4, -5, -4,
    -9, -11, -1, 0, 0, 11, 12, -1,
    -23, 0, -21, 0, -4, 13, -13, -25,
    -7, -12, -15, -18, -15, -13, 0, 0,
    0, 0, -2, 0, 0, 4, -2, 7,
    1, -5, 7, 0, 0, -6, 0, 0,
    0, 0, 1, 1, -2, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    5, 11, 1, 0, -2, 0, 0, 0,
    0, 0, -1, -1, -2, 0, -2, 0,
    1, 5, 0, 0, 0, 0, 5, 1,
    -1, 0, 15, 0, 8, 1, 1, -6,
    0, 7, 0, 0, 0, 5, 0, 0,
    0, 0, 7, 0, 9, 1, 12, 0,
    0, 11, 0, 12, -2, 0, 0, 0,
    0, 35, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -21, 0, -4, 5, 0,
    11, -46, 0, 32, 2, -7, -7, 4,
    4, -1, 1, -18, 0, 0, 19, -21,
    -7, 11, 0, 11, -7, -4, -14, 7,
    -7, 0, 0, -26, 15, 49, 0, 0,
    0, 0, 0, 42, 0, 0, 0, 0,
    7, 0, 7, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, -4, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    7, -8, 0, 0, 1, -4, 0, 4,
    42, -7, -2, 10, 8, -8, 4, 0,
    0, 4, 4, -6, -11, 19, 11, 27,
    0, -4, -4, 15, -1, 7, 0, -46,
    12, 0, -4, 0, -8, 0, 0, 40,
    0, 4, -7, -8, -6, 12, 7, 5,
    0, 0, 0, -10, 0, 0, 0, -8,
    0, 0, 0, 0, -7, -2, 0, 0,
    0, -7, 0, -2, 0, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -23, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    -4, 0, 0, -5, 0, -8, 0, 0,
    0, -5, 4, -2, 0, 0, -8, -4,
    -8, 0, 0, -8, 0, -4, 0, -15,
    0, -7, 0, 0, -23, -4, -14, -7,
    -14, 0, 0, -23, 0, -8, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, -6, -5, -2, 0, 0, 0, 0,
    -6, 0, -8, 6, -7, 7, 0, -1,
    -8, -1, -5, -4, 0, -2, -2, -1,
    3, -8, -1, 0, 0, 0, -25, -5,
    -9, 0, -14, 0, -1, -15, -2, 0,
    0, -1, -2, 0, -1, 0, 0, 0,
    3, 0, -3, -5, -3, -1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 14, 0, 0, 0, 0, 0,
    0, -6, 0, -1, 0, 0, 0, -7,
    4, 0, 0, 0, -8, -4, -7, 0,
    0, -10, 0, -4, 0, -15, 0, 0,
    0, 0, -33, 0, -7, -13, -18, 0,
    0, -23, 0, -3, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -5,
    -4, -2, 1, 0, 0, 5, -6, 0,
    14, 14, -4, -4, -11, 2, 14, 5,
    6, -8, 2, 13, 2, 9, 6, 8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 22, 15, -8, -4, 4,
    -2, 3, 4, 16, 1, 0, 0, 0,
    4, 0, 4, 0, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 9, 0, 0, 0, 0, -23, -2,
    -5, -12, -14, 0, 0, -23, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 9, 0, 0, 0, 0,
    -23, -2, -5, -12, -14, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    -9, 4, 0, -4, 5, 8, 4, -11,
    0, 1, -4, 4, 0, 5, 0, 0,
    0, 0, -3, 0, -2, -1, -7, 0,
    -2, -14, 0, 20, -4, 0, -8, 0,
    0, 0, -1, -6, 0, -4, -11, -7,
    -11, -5, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 9,
    0, 0, 0, 0, -23, -2, -5, -12,
    -14, 0, 0, -23, 0, 0, 0, 0,
    0, 0, 0, 18, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    -10, -2, -2, 11, -3, -4, -14, 1,
    3, 1, -1, -8, 1, 8, 1, 5,
    1, 5, -7, -16, -2, 0, -7, -4,
    -8, -14, -12, 0, -3, -7, -2, -6,
    -14, -1, -3, 0, -1, 0, -1, 0,
    5, 0, 5, -1, 5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, -4, -4, 0, 0, -8,
    0, -2, 0, -5, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -21,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, -4, 0,
    0, 0, 0, 0, -2, 0, 0, -5,
    -4, 4, 0, -5, -5, -1, 0, -7,
    -1, -6, -3, -2, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -23, 0, 10, 0, 0, -6, 0,
    0, 0, 0, 0, -4, 0, -4, 0,
    -4, 0, 0, 0, -1, 0, -9, 0,
    0, 13, -6, -11, -12, 1, 7, 7,
    1, -12, 1, 5, 1, 11, 1, 13,
    -1, -10, 0, 0, -6, 0, 0, -11,
    -8, 0, 0, -7, 0, -6, -6, 0,
    -5, 0, 0, -5, 0, -3, 5, 0,
    -5, -11, -5, -4, 0, 0, -2, 0,
    -7, 0, 0, 6, -9, 0, 4, -4,
    5, 3, 0, -13, 0, -1, -1, 0,
    -4, 7, -5, 0, 0, 0, -8, -2,
    -8, 0, -11, 0, 0, -15, 0, 12,
    -4, 0, -6, 0, 0, 9, 0, -4,
    0, -4, -11, 0, -11, -4, 0, 0,
    0, 0, -1, 0, 0, 4, -4, 1,
    0, 0, -2, -1, 0, -2, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -20,
    0, 6, 0, 0, -2, 0, 0, 0,
    0, 0, 1, 0, -4, -4, -4, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 50,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t font_multilang_large = {
#else
lv_font_t font_multilang_large = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 23,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if FONT_MULTILANG_LARGE*/
