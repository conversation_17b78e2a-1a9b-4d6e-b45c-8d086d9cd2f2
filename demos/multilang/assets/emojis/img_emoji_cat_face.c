#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_EMOJI_CAT_FACE
    #define LV_ATTRIBUTE_IMAGE_IMG_EMOJI_CAT_FACE
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_EMOJI_CAT_FACE uint8_t
img_emoji_cat_face_map[] = {
    0xd9, 0xf9, 0xf4, 0xff, 0xff, 0xf6, 0xff, 0xff, 0xe4, 0xff, 0xff, 0xff, 0xf9, 0xf2, 0xf5, 0xff, 0xee, 0xff, 0xf8, 0xff, 0xfb, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfd, 0xfc, 0xff, 0xff, 0xe4, 0xfc, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xf7, 0xfe, 0xfb, 0xff, 0xf8, 0xff, 0xfb, 0xff, 0xff, 0xfc, 0xfc, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xf5, 0xfc, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xcf, 0xff, 0xfc, 0xff, 0xff, 0xf0, 0xff, 0xff, 0x96, 0xe4, 0xfb, 0xff, 0xa7, 0xe3, 0xff, 0xff, 0xeb, 0xf3, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xf3, 0xff, 0xf7, 0xff, 0xed, 0xfc, 0xe7, 0xff, 0xff, 0xff, 0xf6, 0xff, 0xf2, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xdb, 0xf7, 0xff, 0xff, 0x85, 0xe5, 0xfc, 0xff, 0x25, 0xdc, 0xec, 0xff, 0xec, 0xf9, 0xff, 0xff, 0xfa, 0xfd, 0xff, 0xff,
    0xff, 0xfb, 0xff, 0xff, 0xce, 0xe2, 0xfb, 0xff, 0x35, 0xc8, 0xee, 0xff, 0x5a, 0xc7, 0xff, 0xff, 0x68, 0xe0, 0xff, 0xff, 0xdb, 0xec, 0xff, 0xff, 0xff, 0xfb, 0xf9, 0xff, 0xff, 0xff, 0xee, 0xff, 0xff, 0xff, 0xee, 0xff, 0xef, 0xff, 0xef, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfd, 0xff, 0xff, 0x91, 0xe7, 0xf3, 0xff, 0x66, 0xd3, 0xf3, 0xff, 0x55, 0xc8, 0xfb, 0xff, 0x05, 0xbf, 0xf5, 0xff, 0xe0, 0xf6, 0xfc, 0xff, 0xf1, 0xfc, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0x94, 0xe3, 0xf8, 0xff, 0x2d, 0xce, 0xf4, 0xff, 0x83, 0xc2, 0xff, 0xff, 0x3f, 0xce, 0xfb, 0xff, 0x82, 0xd3, 0xf6, 0xff, 0xfd, 0xec, 0xff, 0xff, 0xe1, 0xf7, 0xff, 0xff, 0xe3, 0xfe, 0xff, 0xff, 0xcb, 0xf8, 0xff, 0xff, 0xfa, 0xf7, 0xff, 0xff, 0xd0, 0xf4, 0xff, 0xff, 0x4f, 0xdc, 0xf7, 0xff, 0x7b, 0xc9, 0xfe, 0xff, 0xc8, 0xc9, 0xff, 0xff, 0x7c, 0xca, 0xff, 0xff, 0xd6, 0xf3, 0xf8, 0xff, 0xed, 0xfc, 0xff, 0xff,
    0xd8, 0xf8, 0xff, 0xff, 0x99, 0xe6, 0xf9, 0xff, 0x62, 0xe0, 0xfd, 0xff, 0xef, 0xd2, 0xff, 0xff, 0xb0, 0xc6, 0xff, 0xff, 0x47, 0xc0, 0xf1, 0xff, 0x43, 0xc8, 0xff, 0xff, 0x67, 0xb0, 0xff, 0xff, 0x5d, 0xb9, 0xff, 0xff, 0x41, 0xa3, 0xe9, 0xff, 0x6b, 0xac, 0xf1, 0xff, 0x5d, 0xc3, 0xfe, 0xff, 0x41, 0xc6, 0xf8, 0xff, 0xc6, 0xbe, 0xff, 0xff, 0xfb, 0xbd, 0xff, 0xff, 0x59, 0xbe, 0xf2, 0xff, 0xd6, 0xf3, 0xf8, 0xff, 0xed, 0xfc, 0xff, 0xff,
    0xe6, 0xfc, 0xff, 0xff, 0xa2, 0xea, 0xfb, 0xff, 0x63, 0xd7, 0xee, 0xff, 0xe9, 0xc6, 0xff, 0xff, 0x9f, 0xca, 0xff, 0xff, 0x1b, 0xc3, 0xf9, 0xff, 0x04, 0xb5, 0xff, 0xff, 0x27, 0x8f, 0xff, 0xff, 0x24, 0xa4, 0xff, 0xff, 0x23, 0x99, 0xff, 0xff, 0x39, 0xa5, 0xff, 0xff, 0x13, 0xc3, 0xff, 0xff, 0x00, 0xd1, 0xff, 0xff, 0x81, 0xcd, 0xff, 0xff, 0xc5, 0xcd, 0xff, 0xff, 0x63, 0xd2, 0xf8, 0xff, 0xe0, 0xf6, 0xfc, 0xff, 0xf1, 0xfc, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xbf, 0xed, 0xff, 0xff, 0x36, 0xc9, 0xe9, 0xff, 0x6c, 0xb3, 0xf3, 0xff, 0x32, 0xc8, 0xff, 0xff, 0x12, 0xc5, 0xff, 0xff, 0x2a, 0xba, 0xff, 0xff, 0x2f, 0xad, 0xff, 0xff, 0x1e, 0xac, 0xff, 0xff, 0x2c, 0xae, 0xff, 0xff, 0x31, 0xb5, 0xff, 0xff, 0x07, 0xbc, 0xff, 0xff, 0x00, 0xbe, 0xf4, 0xff, 0x08, 0xbf, 0xf1, 0xff, 0x4f, 0xc4, 0xfb, 0xff, 0x69, 0xcd, 0xff, 0xff, 0xec, 0xf9, 0xff, 0xff, 0xfa, 0xfd, 0xff, 0xff,
    0xf6, 0xff, 0xf9, 0xff, 0xfd, 0xeb, 0xff, 0xff, 0x22, 0xd0, 0xfe, 0xff, 0x08, 0xbc, 0xff, 0xff, 0x37, 0xbe, 0xff, 0xff, 0x35, 0xbb, 0xff, 0xff, 0x0e, 0xc6, 0xfa, 0xff, 0x27, 0xc7, 0xf7, 0xff, 0x28, 0xc1, 0xf4, 0xff, 0x12, 0xc9, 0xfb, 0xff, 0x11, 0xc3, 0xfa, 0xff, 0x43, 0xb8, 0xfd, 0xff, 0x62, 0xba, 0xff, 0xff, 0x35, 0xc2, 0xff, 0xff, 0x0d, 0xc5, 0xff, 0xff, 0x20, 0xc5, 0xff, 0xff, 0xf5, 0xfc, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xe9, 0xff, 0xe8, 0xff, 0xf2, 0xf7, 0xff, 0xff, 0x3f, 0xc2, 0xff, 0xff, 0x19, 0xc4, 0xff, 0xff, 0x20, 0xa7, 0xdd, 0xff, 0x1d, 0xaf, 0xcb, 0xff, 0x37, 0xc7, 0xea, 0xff, 0x07, 0xc7, 0xf6, 0xff, 0x4e, 0xc3, 0xff, 0xff, 0x07, 0xc0, 0xfa, 0xff, 0x00, 0xc1, 0xfb, 0xff, 0x25, 0xc8, 0xf9, 0xff, 0x0a, 0x8d, 0xae, 0xff, 0x2e, 0xb5, 0xd1, 0xff, 0x25, 0xb3, 0xe2, 0xff, 0x3a, 0xca, 0xff, 0xff, 0xe8, 0xfe, 0xff, 0xff, 0xf8, 0xfe, 0xff, 0xff,
    0xff, 0xfe, 0xfc, 0xff, 0xb5, 0xe1, 0xff, 0xff, 0x28, 0xba, 0xee, 0xff, 0x16, 0xc8, 0xf9, 0xff, 0x21, 0x5e, 0x8a, 0xff, 0x02, 0x03, 0x2f, 0xff, 0x21, 0x7b, 0xb1, 0xff, 0x26, 0xc6, 0xff, 0xff, 0x00, 0xb3, 0xed, 0xff, 0x3a, 0xd2, 0xff, 0xff, 0x49, 0xcd, 0xfc, 0xff, 0x2a, 0x67, 0x89, 0xff, 0x00, 0x00, 0x15, 0xff, 0x00, 0x37, 0x5a, 0xff, 0x16, 0xc8, 0xff, 0xff, 0x05, 0xbe, 0xff, 0xff, 0xd2, 0xec, 0xf2, 0xff, 0xed, 0xf8, 0xfc, 0xff,
    0xf4, 0xd7, 0xee, 0xff, 0x8f, 0xcb, 0xef, 0xff, 0x3c, 0xbf, 0xeb, 0xff, 0x07, 0xc1, 0xdf, 0xff, 0x0d, 0x47, 0x64, 0xff, 0x12, 0x00, 0x1c, 0xff, 0x21, 0x82, 0xbc, 0xff, 0x2c, 0xbd, 0xff, 0xff, 0x06, 0xb6, 0xfd, 0xff, 0x2c, 0xab, 0xf6, 0xff, 0x12, 0xb2, 0xe8, 0xff, 0x07, 0x87, 0xaa, 0xff, 0x00, 0x05, 0x25, 0xff, 0x16, 0x6c, 0x7e, 0xff, 0x25, 0xd2, 0xe6, 0xff, 0x40, 0xaf, 0xdd, 0xff, 0xb2, 0xd0, 0xd5, 0xff, 0xd5, 0xe6, 0xe9, 0xff,
    0xf1, 0xf5, 0xff, 0xff, 0xb8, 0xd4, 0xf6, 0xff, 0x5c, 0xb8, 0xeb, 0xff, 0x16, 0xcf, 0xf5, 0xff, 0x2a, 0xbe, 0xdc, 0xff, 0x37, 0x9b, 0xbe, 0xff, 0x2a, 0xc5, 0xf2, 0xff, 0x05, 0x97, 0xd9, 0xff, 0x1d, 0x05, 0x00, 0xff, 0x06, 0x05, 0x15, 0xff, 0x30, 0x9a, 0xbf, 0xff, 0x31, 0xc7, 0xff, 0xff, 0x18, 0x82, 0xcf, 0xff, 0x3e, 0xc3, 0xff, 0xff, 0x25, 0xb5, 0xec, 0xff, 0x7c, 0xa7, 0xe6, 0xff, 0xa5, 0xc3, 0xc8, 0xff, 0xcd, 0xde, 0xe1, 0xff,
    0xc1, 0xe7, 0xd1, 0xff, 0xcb, 0xbd, 0xd5, 0xff, 0x57, 0xaf, 0xe5, 0xff, 0x0c, 0xbf, 0xfd, 0xff, 0x00, 0xc3, 0xfb, 0xff, 0x00, 0xc5, 0xec, 0xff, 0x17, 0xa6, 0xcc, 0xff, 0x48, 0xb6, 0xe0, 0xff, 0x18, 0x49, 0x53, 0xff, 0x00, 0x3a, 0x52, 0xff, 0x3c, 0xba, 0xea, 0xff, 0x00, 0x8a, 0xd2, 0xff, 0x1a, 0xb4, 0xff, 0xff, 0x09, 0xc4, 0xf7, 0xff, 0x20, 0xcc, 0xe2, 0xff, 0x46, 0xc2, 0xc8, 0xff, 0xcc, 0xe4, 0xea, 0xff, 0xef, 0xfb, 0xff, 0xff,
    0xe0, 0xfe, 0xdb, 0xff, 0xea, 0xcd, 0xdc, 0xff, 0x5a, 0xbe, 0xee, 0xff, 0x17, 0xbc, 0xff, 0xff, 0x21, 0xc4, 0xff, 0xff, 0x1a, 0xc0, 0xfd, 0xff, 0x00, 0x44, 0x76, 0xff, 0x00, 0x3c, 0x62, 0xff, 0x03, 0x30, 0x3d, 0xff, 0x00, 0x31, 0x39, 0xff, 0x0a, 0x4c, 0x5f, 0xff, 0x15, 0x53, 0x7c, 0xff, 0x44, 0xbb, 0xf9, 0xff, 0x11, 0xc0, 0xff, 0xff, 0x1e, 0xce, 0xff, 0xff, 0x14, 0xb0, 0xfd, 0xff, 0xd3, 0xe0, 0xe8, 0xff, 0xeb, 0xef, 0xf4, 0xff,
    0xf8, 0xff, 0xf2, 0xff, 0xff, 0xf5, 0xff, 0xff, 0xa5, 0xe4, 0xff, 0xff, 0x25, 0xb6, 0xed, 0xff, 0x1e, 0xad, 0xf1, 0xff, 0x45, 0xc7, 0xff, 0xff, 0x3b, 0xb0, 0xed, 0xff, 0x55, 0xb9, 0xf0, 0xff, 0x2e, 0xc0, 0xff, 0xff, 0x37, 0xc3, 0xfe, 0xff, 0x06, 0xa1, 0xc8, 0xff, 0x25, 0xc2, 0xe9, 0xff, 0x27, 0xc2, 0xf3, 0xff, 0x0a, 0xbf, 0xed, 0xff, 0x33, 0xca, 0xef, 0xff, 0xa3, 0xdf, 0xff, 0xff, 0xfa, 0xf9, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xf7, 0xee, 0xfb, 0xff, 0xf1, 0xf4, 0xff, 0xff, 0x5a, 0xdf, 0xff, 0xff, 0x29, 0xd0, 0xfc, 0xff, 0x20, 0xbe, 0xfa, 0xff, 0x00, 0xd0, 0xff, 0xff, 0x15, 0xb7, 0xff, 0xff, 0x12, 0xbe, 0xf8, 0xff, 0x37, 0xb4, 0xf1, 0xff, 0x1e, 0xcd, 0xff, 0xff, 0x07, 0xc8, 0xff, 0xff, 0x30, 0xb7, 0xff, 0xff, 0x2c, 0xc5, 0xff, 0xff, 0x60, 0xdb, 0xf5, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff,
    0xff, 0xfe, 0xfc, 0xff, 0xff, 0xfe, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0xf9, 0xff, 0xff, 0xb1, 0xec, 0xff, 0xff, 0x73, 0xdc, 0xfd, 0xff, 0x38, 0xcc, 0xfa, 0xff, 0x13, 0xc3, 0xf8, 0xff, 0x10, 0xc2, 0xfb, 0xff, 0x19, 0xc4, 0xf8, 0xff, 0x2d, 0xca, 0xf2, 0xff, 0x51, 0xd4, 0xef, 0xff, 0x81, 0xe1, 0xee, 0xff, 0xb7, 0xf0, 0xf1, 0xff, 0xe7, 0xfe, 0xf6, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0xff, 0xff, 0xff, 0xec, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xf5, 0xf4, 0xff, 0xff, 0xe7, 0xec, 0xff, 0xff, 0xdc, 0xe7, 0xff, 0xff, 0xde, 0xef, 0xff, 0xff, 0xdd, 0xf1, 0xff, 0xff, 0xdc, 0xf2, 0xff, 0xff, 0xe2, 0xf5, 0xff, 0xff, 0xee, 0xf9, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xfb, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xfe, 0xf8, 0xff, 0xff, 0xfd, 0xfb, 0xff, 0xff, 0xfc, 0xfc, 0xff, 0xff, 0xfb, 0xfb, 0xff, 0xff, 0xfb, 0xfc, 0xff, 0xff, 0xfd, 0xfc, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
};

const lv_image_dsc_t img_emoji_cat_face = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 18,
    .header.h = 19,
    .header.stride = 72,
    .data = img_emoji_cat_face_map,
    .data_size = sizeof(img_emoji_cat_face_map),
};

#endif
