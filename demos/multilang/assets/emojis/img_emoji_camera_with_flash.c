#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_MULTILANG

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_EMOJI_CAMERA_WITH_FLASH
    #define LV_ATTRIBUTE_IMAGE_IMG_EMOJI_CAMERA_WITH_FLASH
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_EMOJI_CAMERA_WITH_FLASH uint8_t
img_emoji_camera_with_flash_map[] = {
    0xf1, 0xff, 0xf8, 0xff, 0xfe, 0xfa, 0xff, 0xff, 0xff, 0xf9, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xf4, 0xff, 0xfc, 0xff, 0xf3, 0xff, 0xf8, 0xff, 0xfc, 0xfd, 0xf4, 0xff, 0xff, 0xf6, 0xf5, 0xff, 0xed, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xd1, 0xff, 0xf7, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xbe, 0xf3, 0xf6, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xf7, 0xf7, 0xff,
    0xf8, 0xff, 0xfc, 0xff, 0xfc, 0xfe, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xf5, 0xfb, 0xff, 0xff, 0xf9, 0xf7, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xd3, 0xf1, 0xff, 0xff, 0xeb, 0xf3, 0xff, 0xff, 0xc5, 0xe1, 0xec, 0xff, 0xff, 0xee, 0xff, 0xff, 0xff, 0xee, 0xff, 0xff, 0xc6, 0xe4, 0xf7, 0xff, 0xfd, 0xf9, 0xff, 0xff, 0xfa, 0xfc, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfb, 0xfb, 0xff,
    0xff, 0xfa, 0xfa, 0xff, 0xfa, 0xfc, 0xfd, 0xff, 0xf1, 0xff, 0xff, 0xff, 0xf3, 0xff, 0xfc, 0xff, 0xf4, 0xff, 0xf9, 0xff, 0xdc, 0xfb, 0xf8, 0xff, 0x97, 0xdc, 0xe6, 0xff, 0x6c, 0xcd, 0xe7, 0xff, 0x9a, 0xd7, 0xff, 0xff, 0x8a, 0xda, 0xf9, 0xff, 0x52, 0xd2, 0xe5, 0xff, 0x82, 0xe3, 0xff, 0xff, 0x83, 0xe0, 0xff, 0xff, 0x63, 0xd1, 0xe9, 0xff, 0xc2, 0xea, 0xfd, 0xff, 0xf8, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xf1, 0xfe, 0xfc, 0xff, 0xee, 0xff, 0xfb, 0xff, 0xfe, 0xfc, 0xf2, 0xff, 0xff, 0xfc, 0xf8, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xe0, 0xec, 0xff, 0xff, 0xad, 0xe6, 0xff, 0xff, 0x40, 0xc5, 0xf7, 0xff, 0x22, 0xdb, 0xfd, 0xff, 0x00, 0xdf, 0xf4, 0xff, 0x00, 0xe5, 0xfd, 0xff, 0x03, 0xd8, 0xf7, 0xff, 0x14, 0xcc, 0xea, 0xff, 0x8e, 0xe7, 0xff, 0xff, 0xee, 0xf5, 0xff, 0xff, 0xf6, 0xf6, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xf6, 0xff, 0xe2, 0xe8, 0xe7, 0xff, 0xd4, 0xf1, 0xe8, 0xff, 0xda, 0xf3, 0xe5, 0xff, 0xe9, 0xf7, 0xec, 0xff, 0xc8, 0xed, 0xf1, 0xff, 0x63, 0xc7, 0xdf, 0xff, 0x19, 0xb4, 0xdb, 0xff, 0x00, 0xcd, 0xf9, 0xff, 0x00, 0xea, 0xff, 0xff, 0x00, 0xf1, 0xff, 0xff, 0x07, 0xea, 0xff, 0xff, 0x13, 0xdb, 0xff, 0xff, 0x2d, 0xd2, 0xf9, 0xff, 0x65, 0xd3, 0xf1, 0xff, 0xa1, 0xc1, 0xde, 0xff, 0xca, 0xca, 0xca, 0xff, 0xe4, 0xe4, 0xe4, 0xff,
    0xcd, 0xd1, 0xcc, 0xff, 0xa8, 0xb0, 0xaf, 0xff, 0x9c, 0xa1, 0xa2, 0xff, 0xa3, 0x99, 0x99, 0xff, 0xc0, 0xa8, 0xa8, 0xff, 0xcc, 0xbc, 0xc7, 0xff, 0xa8, 0xb8, 0xd5, 0xff, 0x8f, 0xbf, 0xe9, 0xff, 0x10, 0xd5, 0xfb, 0xff, 0x00, 0xf0, 0xff, 0xff, 0x01, 0xf6, 0xff, 0xff, 0x00, 0xed, 0xff, 0xff, 0x09, 0xe1, 0xff, 0xff, 0x25, 0xcc, 0xf1, 0xff, 0x1f, 0x9c, 0xb8, 0xff, 0x40, 0x5d, 0x78, 0xff, 0x94, 0x94, 0x94, 0xff, 0xc1, 0xc1, 0xc1, 0xff,
    0xd1, 0xd9, 0xcf, 0xff, 0xc0, 0xc0, 0xc0, 0xff, 0xc2, 0xba, 0xc4, 0xff, 0xb9, 0xb6, 0xbf, 0xff, 0xaf, 0xbf, 0xbe, 0xff, 0x9e, 0xc6, 0xc4, 0xff, 0x7e, 0xb9, 0xc2, 0xff, 0x77, 0xbc, 0xcf, 0xff, 0x5d, 0xc8, 0xed, 0xff, 0x1c, 0xda, 0xed, 0xff, 0x24, 0xdc, 0xee, 0xff, 0x04, 0xd8, 0xe9, 0xff, 0x0a, 0xd3, 0xee, 0xff, 0x2b, 0xbe, 0xde, 0xff, 0x17, 0x8c, 0xa1, 0xff, 0x48, 0x52, 0x6a, 0xff, 0x8b, 0x8b, 0x8b, 0xff, 0xbd, 0xbd, 0xbd, 0xff,
    0xb9, 0xd0, 0xc2, 0xff, 0xbc, 0xb9, 0xbb, 0xff, 0xd0, 0xb6, 0xc8, 0xff, 0xc5, 0xaf, 0xc1, 0xff, 0xb1, 0xb4, 0xb9, 0xff, 0xa6, 0xbb, 0xb9, 0xff, 0xa3, 0xb4, 0xb7, 0xff, 0xb8, 0xbe, 0xcb, 0xff, 0x91, 0xb3, 0xd7, 0xff, 0x59, 0xb9, 0xd0, 0xff, 0x83, 0xb8, 0xd3, 0xff, 0x72, 0xb5, 0xd6, 0xff, 0x70, 0xb6, 0xde, 0xff, 0x83, 0xb7, 0xe0, 0xff, 0x5f, 0xb2, 0xc8, 0xff, 0x9d, 0xa2, 0xb7, 0xff, 0xa8, 0xa8, 0xa8, 0xff, 0xd4, 0xd4, 0xd4, 0xff,
    0x83, 0x83, 0x83, 0xff, 0x82, 0x82, 0x82, 0xff, 0x68, 0x68, 0x68, 0xff, 0x84, 0x84, 0x84, 0xff, 0x97, 0x97, 0x97, 0xff, 0x6e, 0x6e, 0x6e, 0xff, 0x8b, 0x8b, 0x8b, 0xff, 0x71, 0x71, 0x71, 0xff, 0x7d, 0x8f, 0x90, 0xff, 0x87, 0x96, 0x98, 0xff, 0x73, 0x80, 0x82, 0xff, 0x7a, 0x83, 0x86, 0xff, 0x8a, 0x91, 0x94, 0xff, 0x7e, 0x81, 0x85, 0xff, 0x74, 0x75, 0x79, 0xff, 0x6e, 0x6d, 0x71, 0xff, 0x6e, 0x6e, 0x6e, 0xff, 0x91, 0x91, 0x91, 0xff,
    0x70, 0x70, 0x70, 0xff, 0x59, 0x59, 0x59, 0xff, 0x60, 0x60, 0x60, 0xff, 0x78, 0x78, 0x78, 0xff, 0x85, 0x85, 0x85, 0xff, 0x6a, 0x6a, 0x6a, 0xff, 0x7a, 0x7a, 0x7a, 0xff, 0x73, 0x73, 0x73, 0xff, 0x67, 0x75, 0x74, 0xff, 0x56, 0x62, 0x62, 0xff, 0x3c, 0x46, 0x46, 0xff, 0x46, 0x4e, 0x4e, 0xff, 0x5e, 0x62, 0x63, 0xff, 0x70, 0x72, 0x73, 0xff, 0x79, 0x78, 0x7a, 0xff, 0x5f, 0x5c, 0x5e, 0xff, 0x5a, 0x5a, 0x5a, 0xff, 0x7c, 0x7c, 0x7c, 0xff,
    0x73, 0x73, 0x73, 0xff, 0x3c, 0x3c, 0x3c, 0xff, 0x51, 0x51, 0x51, 0xff, 0x56, 0x56, 0x56, 0xff, 0x58, 0x58, 0x58, 0xff, 0x57, 0x57, 0x57, 0xff, 0x6b, 0x6b, 0x6b, 0xff, 0x77, 0x77, 0x77, 0xff, 0x3f, 0x47, 0x47, 0xff, 0x38, 0x40, 0x40, 0xff, 0x30, 0x35, 0x36, 0xff, 0x34, 0x38, 0x39, 0xff, 0x32, 0x34, 0x35, 0xff, 0x4d, 0x4a, 0x4c, 0xff, 0x71, 0x6e, 0x70, 0xff, 0x66, 0x61, 0x63, 0xff, 0x4d, 0x4d, 0x4d, 0xff, 0x70, 0x70, 0x70, 0xff,
    0x77, 0x77, 0x77, 0xff, 0x3c, 0x3c, 0x3c, 0xff, 0x49, 0x49, 0x49, 0xff, 0x43, 0x43, 0x43, 0xff, 0x3d, 0x3d, 0x3d, 0xff, 0x46, 0x46, 0x46, 0xff, 0x66, 0x66, 0x66, 0xff, 0x64, 0x64, 0x64, 0xff, 0x2b, 0x30, 0x2f, 0xff, 0x43, 0x45, 0x45, 0xff, 0x4a, 0x4c, 0x4c, 0xff, 0x4c, 0x4c, 0x4c, 0xff, 0x3a, 0x38, 0x38, 0xff, 0x37, 0x32, 0x33, 0xff, 0x5e, 0x59, 0x5a, 0xff, 0x76, 0x70, 0x71, 0xff, 0x4a, 0x4a, 0x4a, 0xff, 0x6d, 0x6d, 0x6d, 0xff,
    0x61, 0x61, 0x61, 0xff, 0x41, 0x41, 0x41, 0xff, 0x46, 0x46, 0x46, 0xff, 0x48, 0x48, 0x48, 0xff, 0x44, 0x44, 0x44, 0xff, 0x45, 0x45, 0x45, 0xff, 0x68, 0x68, 0x68, 0xff, 0x47, 0x47, 0x47, 0xff, 0x2d, 0x2d, 0x2d, 0xff, 0x4b, 0x4b, 0x4b, 0xff, 0x4f, 0x4d, 0x4d, 0xff, 0x56, 0x54, 0x54, 0xff, 0x4f, 0x4d, 0x4d, 0xff, 0x35, 0x30, 0x31, 0xff, 0x4b, 0x46, 0x47, 0xff, 0x77, 0x72, 0x73, 0xff, 0x44, 0x44, 0x44, 0xff, 0x68, 0x68, 0x68, 0xff,
    0x56, 0x56, 0x56, 0xff, 0x3f, 0x3f, 0x3f, 0xff, 0x3c, 0x3c, 0x3c, 0xff, 0x3d, 0x3d, 0x3d, 0xff, 0x40, 0x40, 0x40, 0xff, 0x42, 0x42, 0x42, 0xff, 0x6a, 0x6a, 0x6a, 0xff, 0x52, 0x52, 0x52, 0xff, 0x26, 0x24, 0x23, 0xff, 0x40, 0x3e, 0x3d, 0xff, 0x42, 0x40, 0x3f, 0xff, 0x4a, 0x48, 0x47, 0xff, 0x41, 0x3f, 0x3e, 0xff, 0x2a, 0x28, 0x27, 0xff, 0x47, 0x45, 0x44, 0xff, 0x74, 0x72, 0x71, 0xff, 0x3d, 0x3d, 0x3d, 0xff, 0x62, 0x62, 0x62, 0xff,
    0x5b, 0x5b, 0x5b, 0xff, 0x32, 0x32, 0x32, 0xff, 0x37, 0x37, 0x37, 0xff, 0x2b, 0x2b, 0x2b, 0xff, 0x31, 0x31, 0x31, 0xff, 0x34, 0x34, 0x34, 0xff, 0x53, 0x53, 0x53, 0xff, 0x6c, 0x6c, 0x6c, 0xff, 0x30, 0x31, 0x2f, 0xff, 0x2c, 0x2d, 0x2b, 0xff, 0x32, 0x33, 0x31, 0xff, 0x39, 0x3a, 0x38, 0xff, 0x23, 0x26, 0x24, 0xff, 0x2f, 0x32, 0x30, 0xff, 0x5b, 0x5e, 0x5c, 0xff, 0x57, 0x5a, 0x58, 0xff, 0x38, 0x38, 0x38, 0xff, 0x5d, 0x5d, 0x5d, 0xff,
    0x58, 0x58, 0x58, 0xff, 0x20, 0x20, 0x20, 0xff, 0x3f, 0x3f, 0x3f, 0xff, 0x2d, 0x2d, 0x2d, 0xff, 0x31, 0x31, 0x31, 0xff, 0x2c, 0x2c, 0x2c, 0xff, 0x2e, 0x2e, 0x2e, 0xff, 0x6e, 0x6e, 0x6e, 0xff, 0x57, 0x58, 0x54, 0xff, 0x20, 0x24, 0x1f, 0xff, 0x1b, 0x1f, 0x1a, 0xff, 0x2c, 0x30, 0x2b, 0xff, 0x1f, 0x25, 0x20, 0xff, 0x52, 0x58, 0x53, 0xff, 0x78, 0x7e, 0x79, 0xff, 0x28, 0x2f, 0x2a, 0xff, 0x32, 0x32, 0x32, 0xff, 0x57, 0x57, 0x57, 0xff,
    0x42, 0x42, 0x42, 0xff, 0x33, 0x33, 0x33, 0xff, 0x27, 0x27, 0x27, 0xff, 0x24, 0x24, 0x24, 0xff, 0x27, 0x27, 0x27, 0xff, 0x28, 0x28, 0x28, 0xff, 0x28, 0x28, 0x28, 0xff, 0x29, 0x29, 0x29, 0xff, 0x64, 0x64, 0x64, 0xff, 0x7b, 0x7b, 0x7b, 0xff, 0x65, 0x65, 0x65, 0xff, 0x5d, 0x5d, 0x5d, 0xff, 0x7a, 0x7a, 0x7a, 0xff, 0x5d, 0x5d, 0x5d, 0xff, 0x2a, 0x2a, 0x2a, 0xff, 0x2c, 0x2c, 0x2c, 0xff, 0x30, 0x30, 0x30, 0xff, 0x4d, 0x4d, 0x4d, 0xff,
    0xb6, 0xb6, 0xb6, 0xff, 0xab, 0xab, 0xab, 0xff, 0xa2, 0xa2, 0xa2, 0xff, 0xa3, 0xa3, 0xa3, 0xff, 0xa7, 0xa7, 0xa7, 0xff, 0xa7, 0xa7, 0xa7, 0xff, 0xa5, 0xa5, 0xa5, 0xff, 0xa5, 0xa5, 0xa5, 0xff, 0xa6, 0xa6, 0xa6, 0xff, 0xb8, 0xb8, 0xb8, 0xff, 0xa9, 0xa9, 0xa9, 0xff, 0xa4, 0xa4, 0xa4, 0xff, 0xbc, 0xbc, 0xbc, 0xff, 0xb1, 0xb1, 0xb1, 0xff, 0x9a, 0x9a, 0x9a, 0xff, 0xa6, 0xa6, 0xa6, 0xff, 0x9c, 0x9c, 0x9c, 0xff, 0xb4, 0xb4, 0xb4, 0xff,
    0xec, 0xec, 0xec, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xe1, 0xe1, 0xe1, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xe9, 0xe9, 0xe9, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xdf, 0xdf, 0xdf, 0xff, 0xe8, 0xe8, 0xe8, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xdf, 0xdf, 0xdf, 0xff, 0xe6, 0xe6, 0xe6, 0xff, 0xe5, 0xe5, 0xe5, 0xff, 0xe4, 0xe4, 0xe4, 0xff, 0xf0, 0xf0, 0xf0, 0xff, 0xe3, 0xe3, 0xe3, 0xff, 0xf3, 0xf3, 0xf3, 0xff,
};

const lv_image_dsc_t img_emoji_camera_with_flash = {
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.w = 18,
    .header.h = 19,
    .header.stride = 72,
    .data = img_emoji_camera_with_flash_map,
    .data_size = sizeof(img_emoji_camera_with_flash_map),
};

#endif
