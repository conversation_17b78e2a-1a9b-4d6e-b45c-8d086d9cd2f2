/**
 * @file lv_demo_flex_layout.h
 *
 */

#ifndef LV_DEMO_FLEX_LAYOUT_H
#define LV_DEMO_FLEX_LAYOUT_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_demo_flex_layout(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_DEMO_FLEX_LAYOUT_H*/
