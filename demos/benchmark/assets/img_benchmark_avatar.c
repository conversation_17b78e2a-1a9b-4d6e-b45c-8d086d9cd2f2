#ifdef __has_include
    #if __has_include("lvgl.h")
        #ifndef LV_LVGL_H_INCLUDE_SIMPLE
            #define LV_LVGL_H_INCLUDE_SIMPLE
        #endif
    #endif
#endif

#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
    #include "lvgl.h"
#else
    #include "lvgl/lvgl.h"
#endif

#if LV_USE_DEMO_BENCHMARK

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMG_BENCHMARK_AVATAR
    #define LV_ATTRIBUTE_IMAGE_IMG_BENCHMARK_AVATAR
#endif

static const
LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMG_BENCHMARK_AVATAR
uint8_t img_benchmark_avatar_map[] = {

    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0x65, 0x72, 0x0a, 0x6b, 0x63, 0x71, 0x28, 0x6e, 0x65, 0x6f, 0x40, 0x72, 0x66, 0x6f, 0x5d, 0x6c, 0x64, 0x6b, 0x87, 0x6b, 0x64, 0x70, 0xb2, 0x63, 0x60, 0x72, 0xd1, 0x62, 0x61, 0x75, 0xe6, 0x80, 0x7a, 0x8a, 0xf3, 0x9b, 0x8d, 0x95, 0xfc, 0xae, 0x9e, 0x9a, 0xfc, 0xb9, 0xa9, 0x9c, 0xf3, 0xbe, 0xae, 0x9c, 0xe6, 0xc0, 0xb3, 0xa1, 0xd1, 0xbf, 0xb4, 0x9e, 0xb2, 0xba, 0xb0, 0x9a, 0x87, 0xb6, 0xb0, 0x99, 0x5d, 0xb6, 0xb2, 0x9c, 0x40, 0xbf, 0xb6, 0xa1, 0x28, 0xc2, 0xbb, 0xa3, 0x0a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x62, 0x83, 0x03, 0x5b, 0x61, 0x84, 0x0a, 0x5c, 0x61, 0x84, 0x4a, 0x60, 0x61, 0x84, 0xa8, 0x60, 0x61, 0x7f, 0xe1, 0x61, 0x60, 0x79, 0xee, 0x64, 0x61, 0x75, 0xfa, 0x66, 0x61, 0x72, 0xff, 0x67, 0x65, 0x75, 0xff, 0x5f, 0x5e, 0x72, 0xff, 0x5f, 0x5f, 0x76, 0xff, 0x60, 0x61, 0x78, 0xff, 0x7b, 0x78, 0x88, 0xff, 0x9c, 0x91, 0x98, 0xff, 0xb0, 0x9f, 0x9d, 0xff, 0xbd, 0xaa, 0xa0, 0xff, 0xc6, 0xb3, 0xa1, 0xff, 0xc6, 0xb5, 0xa2, 0xff, 0xc0, 0xb5, 0xa2, 0xff, 0xbd, 0xb3, 0xa0, 0xff, 0xba, 0xb2, 0x9f, 0xff, 0xb7, 0xb1, 0x9d, 0xfa, 0xba, 0xb4, 0xa1, 0xee, 0xbd, 0xb7, 0xa4, 0xe1, 0xbc, 0xb6, 0xa3, 0xa8, 0xbc, 0xb8, 0xa3, 0x4a, 0xbb, 0xb6, 0xa3, 0x0a, 0xbe, 0xb9, 0xa4, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4d, 0x54, 0x6b, 0x07, 0x57, 0x5b, 0x79, 0x50, 0x5b, 0x60, 0x80, 0xa0, 0x5d, 0x63, 0x86, 0xe8, 0x5c, 0x62, 0x88, 0xff, 0x5d, 0x63, 0x88, 0xff, 0x5b, 0x61, 0x84, 0xff, 0x5a, 0x5e, 0x7e, 0xff, 0x58, 0x5c, 0x7b, 0xff, 0x5f, 0x61, 0x7c, 0xff, 0x65, 0x68, 0x80, 0xff, 0x59, 0x5c, 0x76, 0xff, 0x5c, 0x5f, 0x7d, 0xff, 0x65, 0x69, 0x84, 0xff, 0x84, 0x84, 0x97, 0xff, 0x93, 0x8c, 0x94, 0xff, 0xad, 0xa1, 0x9f, 0xff, 0xba, 0xa9, 0xa0, 0xff, 0xc5, 0xb2, 0xa0, 0xff, 0xc6, 0xb6, 0xa0, 0xff, 0xc3, 0xb8, 0xa4, 0xff, 0xc2, 0xb7, 0xa4, 0xff, 0xc3, 0xb8, 0xa5, 0xff, 0xc2, 0xb6, 0xa3, 0xff, 0xbf, 0xb7, 0xa5, 0xff, 0xbc, 0xb6, 0xa4, 0xff, 0xba, 0xb6, 0xa5, 0xff, 0xb7, 0xb5, 0xa4, 0xff, 0xb9, 0xb5, 0xa3, 0xe8, 0xbe, 0xb9, 0xa5, 0xa0, 0xc2, 0xbc, 0xa5, 0x50, 0xc3, 0xbf, 0xa5, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0x71, 0x6c, 0x13, 0x6a, 0x67, 0x71, 0x64, 0x62, 0x62, 0x73, 0xe4, 0x5d, 0x5f, 0x79, 0xfb, 0x5e, 0x61, 0x7e, 0xff, 0x5f, 0x64, 0x84, 0xff, 0x5f, 0x65, 0x88, 0xff, 0x60, 0x65, 0x89, 0xff, 0x5d, 0x63, 0x85, 0xff, 0x58, 0x5f, 0x7f, 0xff, 0x57, 0x5c, 0x7c, 0xff, 0x5c, 0x5f, 0x7f, 0xff, 0x67, 0x6b, 0x88, 0xff, 0x5d, 0x60, 0x7d, 0xff, 0x59, 0x5e, 0x7d, 0xff, 0x5d, 0x62, 0x80, 0xff, 0x79, 0x7c, 0x8f, 0xff, 0x9d, 0x99, 0xa0, 0xff, 0xab, 0xa5, 0xa4, 0xff, 0xb6, 0xac, 0xa3, 0xff, 0xc0, 0xb3, 0xa0, 0xff, 0xc6, 0xb9, 0xa3, 0xff, 0xc6, 0xbc, 0xa7, 0xff, 0xc8, 0xbb, 0xa4, 0xff, 0xc7, 0xbb, 0xa4, 0xff, 0xc9, 0xbe, 0xa9, 0xff, 0xc8, 0xbd, 0xab, 0xff, 0xc6, 0xbb, 0xaa, 0xff, 0xc3, 0xbb, 0xaa, 0xff, 0xbc, 0xb9, 0xa7, 0xff, 0xb6, 0xb5, 0xa4, 0xff, 0xbc, 0xb7, 0xa2, 0xff, 0xc1, 0xbb, 0xa3, 0xfb, 0xc5, 0xbd, 0xa6, 0xe4, 0xc5, 0xbd, 0xa7, 0x64, 0xc8, 0xc0, 0xa8, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa9, 0x97, 0x79, 0x0c, 0x9f, 0x8f, 0x79, 0x81, 0x89, 0x7c, 0x70, 0xdf, 0x7f, 0x77, 0x75, 0xff, 0x79, 0x72, 0x7a, 0xff, 0x6a, 0x69, 0x78, 0xff, 0x62, 0x66, 0x7c, 0xff, 0x5f, 0x65, 0x83, 0xff, 0x63, 0x66, 0x85, 0xff, 0x64, 0x67, 0x86, 0xff, 0x62, 0x66, 0x85, 0xff, 0x5d, 0x61, 0x7f, 0xff, 0x5e, 0x62, 0x80, 0xff, 0x5d, 0x5e, 0x81, 0xff, 0x61, 0x68, 0x87, 0xff, 0x69, 0x6e, 0x8d, 0xff, 0x70, 0x73, 0x92, 0xff, 0x68, 0x6d, 0x8b, 0xff, 0x73, 0x75, 0x8a, 0xff, 0x8f, 0x89, 0x95, 0xff, 0xaa, 0xa4, 0xa4, 0xff, 0xb6, 0xae, 0xa9, 0xff, 0xbf, 0xb3, 0xa7, 0xff, 0xca, 0xbc, 0xa9, 0xff, 0xca, 0xbd, 0xa9, 0xff, 0xcd, 0xbd, 0xaa, 0xff, 0xcb, 0xbc, 0xaa, 0xff, 0xcb, 0xc3, 0xae, 0xff, 0xc9, 0xbf, 0xa9, 0xff, 0xce, 0xc1, 0xae, 0xff, 0xcd, 0xc2, 0xaf, 0xff, 0xc8, 0xc0, 0xad, 0xff, 0xc5, 0xbd, 0xaa, 0xff, 0xc2, 0xbb, 0xa8, 0xff, 0xc3, 0xbb, 0xa8, 0xff, 0xc3, 0xba, 0xa9, 0xff, 0xc4, 0xbb, 0xa6, 0xff, 0xc6, 0xbe, 0xa8, 0xdf, 0xca, 0xc0, 0xae, 0x81, 0xc7, 0xc0, 0xb0, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc5, 0xa8, 0x8a, 0x05, 0xca, 0xae, 0x8a, 0x52, 0xc1, 0xa8, 0x85, 0xdd, 0xb6, 0xa1, 0x7f, 0xff, 0xa5, 0x90, 0x75, 0xff, 0x9c, 0x89, 0x7a, 0xff, 0x95, 0x85, 0x7f, 0xff, 0x7e, 0x78, 0x81, 0xff, 0x6a, 0x6c, 0x7e, 0xff, 0x66, 0x69, 0x84, 0xff, 0x6a, 0x6e, 0x89, 0xff, 0x6a, 0x6d, 0x88, 0xff, 0x68, 0x6c, 0x83, 0xff, 0x64, 0x66, 0x7c, 0xff, 0x6e, 0x6f, 0x85, 0xff, 0x66, 0x67, 0x82, 0xff, 0x74, 0x7a, 0x96, 0xff, 0x5d, 0x62, 0x81, 0xff, 0x74, 0x78, 0x96, 0xff, 0x6d, 0x71, 0x8e, 0xff, 0x83, 0x85, 0x9b, 0xff, 0x90, 0x8d, 0x99, 0xff, 0xa7, 0xa1, 0xa7, 0xff, 0xb2, 0xab, 0xab, 0xff, 0xbe, 0xb3, 0xa8, 0xff, 0xc2, 0xb7, 0xa4, 0xff, 0xc9, 0xbc, 0xa8, 0xff, 0xc3, 0xbe, 0xb7, 0xff, 0xc8, 0xc3, 0xb7, 0xff, 0xd1, 0xc1, 0xa9, 0xff, 0xd7, 0xc2, 0xa4, 0xff, 0xcc, 0xc7, 0xa9, 0xff, 0xd0, 0xc5, 0xb0, 0xff, 0xd4, 0xc4, 0xb2, 0xff, 0xcf, 0xc4, 0xad, 0xff, 0xcd, 0xc1, 0xab, 0xff, 0xcc, 0xc1, 0xab, 0xff, 0xcb, 0xc0, 0xab, 0xff, 0xca, 0xbf, 0xa9, 0xff, 0xc9, 0xbe, 0xac, 0xff, 0xc8, 0xbd, 0xae, 0xff, 0xbf, 0xb6, 0xb5, 0xde, 0xb7, 0xae, 0xb6, 0x52, 0xb8, 0xb0, 0xb8, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc0, 0xae, 0x9d, 0x20, 0xc9, 0xb2, 0x97, 0xbf, 0xd2, 0xb6, 0x90, 0xfa, 0xcd, 0xb2, 0x8b, 0xff, 0xc3, 0xac, 0x83, 0xff, 0xb5, 0x9f, 0x7a, 0xff, 0xae, 0x99, 0x85, 0xff, 0xa8, 0x95, 0x8b, 0xff, 0x90, 0x88, 0x88, 0xff, 0x76, 0x72, 0x7e, 0xff, 0x6c, 0x6e, 0x83, 0xff, 0x70, 0x71, 0x8b, 0xff, 0x72, 0x76, 0x89, 0xff, 0x72, 0x74, 0x85, 0xff, 0x6f, 0x70, 0x7c, 0xff, 0x6c, 0x6c, 0x77, 0xff, 0x84, 0x86, 0x95, 0xff, 0x7c, 0x84, 0x97, 0xff, 0x86, 0x8e, 0xa7, 0xff, 0x72, 0x7a, 0x95, 0xff, 0x86, 0x8f, 0xab, 0xff, 0x99, 0xa3, 0xb8, 0xff, 0xc1, 0xc7, 0xd3, 0xff, 0xc6, 0xca, 0xd5, 0xff, 0xcd, 0xd1, 0xd9, 0xff, 0xc6, 0xc4, 0xc7, 0xff, 0xbf, 0xbb, 0xbb, 0xff, 0xbf, 0xb6, 0xac, 0xff, 0xbf, 0xbe, 0xb4, 0xff, 0xc2, 0xc6, 0xc4, 0xff, 0xb1, 0xb4, 0xb6, 0xff, 0xcf, 0xc2, 0xb4, 0xff, 0xd3, 0xc1, 0xa9, 0xff, 0xd9, 0xc4, 0xab, 0xff, 0xd5, 0xc5, 0xae, 0xff, 0xd3, 0xc5, 0xae, 0xff, 0xd2, 0xc4, 0xac, 0xff, 0xd1, 0xc2, 0xab, 0xff, 0xd0, 0xc1, 0xaa, 0xff, 0xcf, 0xc2, 0xab, 0xff, 0xcc, 0xc1, 0xb0, 0xff, 0xc9, 0xbb, 0xb4, 0xff, 0xbb, 0xb2, 0xb6, 0xff, 0xaf, 0xa8, 0xb8, 0xfa, 0xa0, 0x9c, 0xbd, 0xbf, 0x98, 0x95, 0xc1, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb3, 0xa7, 0xaf, 0x02, 0xbe, 0xad, 0xa5, 0x62, 0xc6, 0xb0, 0x9e, 0xe8, 0xcd, 0xb7, 0x9a, 0xff, 0xd8, 0xbd, 0x96, 0xff, 0xd3, 0xb8, 0x91, 0xff, 0xc8, 0xb1, 0x8a, 0xff, 0xbb, 0xa6, 0x82, 0xff, 0xb5, 0xa1, 0x8b, 0xff, 0xad, 0x9c, 0x91, 0xff, 0x99, 0x91, 0x90, 0xff, 0x7e, 0x7b, 0x87, 0xff, 0x71, 0x75, 0x8a, 0xff, 0x74, 0x79, 0x91, 0xff, 0x79, 0x7d, 0x8e, 0xff, 0x7e, 0x7d, 0x88, 0xff, 0x7c, 0x78, 0x7d, 0xff, 0x76, 0x72, 0x77, 0xff, 0x78, 0x7d, 0x84, 0xff, 0xa1, 0xac, 0xbb, 0xff, 0xc1, 0xcb, 0xdf, 0xff, 0xdc, 0xe4, 0xef, 0xff, 0xdd, 0xe7, 0xf6, 0xff, 0xd8, 0xe5, 0xf8, 0xff, 0xe7, 0xef, 0xf9, 0xff, 0xda, 0xea, 0xf8, 0xff, 0xc6, 0xd5, 0xe3, 0xff, 0xca, 0xcf, 0xda, 0xff, 0xcd, 0xcf, 0xd7, 0xff, 0xbd, 0xc1, 0xc6, 0xff, 0xb9, 0xbc, 0xb8, 0xff, 0xb7, 0xbd, 0xbd, 0xff, 0x9b, 0xa8, 0xb5, 0xff, 0x87, 0x9b, 0xa8, 0xff, 0xc2, 0xba, 0xb2, 0xff, 0xd9, 0xc7, 0xac, 0xff, 0xd5, 0xc2, 0xa3, 0xff, 0xd4, 0xc3, 0xa9, 0xff, 0xcf, 0xc3, 0xa9, 0xff, 0xd0, 0xc3, 0xa9, 0xff, 0xd2, 0xc2, 0xaa, 0xff, 0xd2, 0xc3, 0xac, 0xff, 0xcf, 0xc2, 0xb3, 0xff, 0xc9, 0xbc, 0xb7, 0xff, 0xbe, 0xb6, 0xbb, 0xff, 0xb1, 0xa9, 0xbe, 0xff, 0x9e, 0x9a, 0xc1, 0xff, 0x90, 0x8c, 0xc6, 0xe8, 0x7a, 0x80, 0xc8, 0x62, 0x73, 0x78, 0xc2, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8a, 0x89, 0x9d, 0x0d, 0xa4, 0x9d, 0xa8, 0x93, 0xba, 0xab, 0xa7, 0xfb, 0xc2, 0xae, 0x9d, 0xff, 0xc8, 0xb2, 0x94, 0xff, 0xd2, 0xb9, 0x91, 0xff, 0xd4, 0xb9, 0x93, 0xff, 0xcd, 0xb3, 0x90, 0xff, 0xc1, 0xab, 0x88, 0xff, 0xba, 0xa5, 0x8d, 0xff, 0xac, 0x9e, 0x92, 0xff, 0x99, 0x93, 0x95, 0xff, 0x88, 0x86, 0x96, 0xff, 0x7c, 0x7f, 0x98, 0xff, 0x7e, 0x83, 0x9a, 0xff, 0x84, 0x85, 0x96, 0xff, 0x8a, 0x88, 0x91, 0xff, 0x8b, 0x8b, 0x90, 0xff, 0x96, 0x9c, 0xa3, 0xff, 0xc1, 0xcf, 0xdf, 0xff, 0xc2, 0xd5, 0xea, 0xff, 0xc6, 0xda, 0xf0, 0xff, 0xb3, 0xc6, 0xd7, 0xff, 0xb1, 0xca, 0xdc, 0xff, 0xa9, 0xc4, 0xda, 0xff, 0xae, 0xc7, 0xdc, 0xff, 0xa5, 0xc0, 0xd9, 0xff, 0xad, 0xc4, 0xda, 0xff, 0xb7, 0xc6, 0xd7, 0xff, 0xbf, 0xc9, 0xd7, 0xff, 0xc0, 0xca, 0xdd, 0xff, 0xb6, 0xbe, 0xcb, 0xff, 0xbb, 0xbc, 0xc3, 0xff, 0xb3, 0xb0, 0xae, 0xff, 0xb3, 0xb9, 0xb6, 0xff, 0x5e, 0x75, 0x8d, 0xff, 0x9d, 0xa4, 0xa8, 0xff, 0xd0, 0xc3, 0xb1, 0xff, 0xd1, 0xc0, 0xa5, 0xff, 0xd2, 0xc2, 0xa0, 0xff, 0xd0, 0xc0, 0xa3, 0xff, 0xd2, 0xc3, 0xa9, 0xff, 0xd4, 0xc4, 0xad, 0xff, 0xd1, 0xc1, 0xb2, 0xff, 0xc8, 0xbc, 0xb7, 0xff, 0xbd, 0xb5, 0xba, 0xff, 0xaf, 0xa6, 0xbd, 0xff, 0x9c, 0x9b, 0xbc, 0xff, 0x8d, 0x89, 0xc5, 0xff, 0x7b, 0x80, 0xc6, 0xfb, 0x7c, 0x80, 0xcb, 0x93, 0x7f, 0x83, 0xd2, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x7e, 0x93, 0x16, 0x88, 0x87, 0x9c, 0xc0, 0x9c, 0x98, 0xa6, 0xff, 0xb2, 0xa7, 0xa5, 0xff, 0xbb, 0xa9, 0x9c, 0xff, 0xc2, 0xad, 0x91, 0xff, 0xc8, 0xb1, 0x8a, 0xff, 0xcb, 0xb3, 0x8b, 0xff, 0xc6, 0xaf, 0x88, 0xff, 0xbf, 0xa7, 0x85, 0xff, 0xb4, 0xa2, 0x8b, 0xff, 0xa4, 0x99, 0x90, 0xff, 0x92, 0x8d, 0x93, 0xff, 0x85, 0x84, 0x97, 0xff, 0x80, 0x81, 0x9c, 0xff, 0x84, 0x88, 0x9f, 0xff, 0x94, 0x97, 0xa9, 0xff, 0xa1, 0xa6, 0xb3, 0xff, 0xd6, 0xe1, 0xed, 0xff, 0xd9, 0xe9, 0xf6, 0xff, 0xdf, 0xee, 0xf8, 0xff, 0xd0, 0xe1, 0xf0, 0xff, 0xb7, 0xcb, 0xdd, 0xff, 0x9f, 0xb9, 0xd2, 0xff, 0x87, 0xa4, 0xc3, 0xff, 0x5d, 0x7b, 0x99, 0xff, 0x5e, 0x7e, 0x9c, 0xff, 0x7e, 0x9c, 0xbb, 0xff, 0x5e, 0x7a, 0x9a, 0xff, 0x69, 0x81, 0x9d, 0xff, 0x88, 0x9d, 0xb7, 0xff, 0xa6, 0xbc, 0xd2, 0xff, 0xac, 0xc1, 0xd6, 0xff, 0xae, 0xc2, 0xd4, 0xff, 0xa7, 0xb6, 0xc6, 0xff, 0xb6, 0xb4, 0xbb, 0xff, 0xae, 0xb3, 0xbe, 0xff, 0x5f, 0x6d, 0x82, 0xff, 0x6b, 0x77, 0x82, 0xff, 0xc1, 0xbc, 0xb0, 0xff, 0xd5, 0xc0, 0xa3, 0xff, 0xcd, 0xba, 0x9d, 0xff, 0xce, 0xc0, 0xa6, 0xff, 0xd1, 0xc1, 0xa8, 0xff, 0xd0, 0xc0, 0xae, 0xff, 0xc9, 0xbc, 0xb1, 0xff, 0xc0, 0xb3, 0xb6, 0xff, 0xae, 0xa5, 0xb8, 0xff, 0x98, 0x99, 0xb8, 0xff, 0x87, 0x87, 0xc0, 0xff, 0x7f, 0x82, 0xc3, 0xff, 0x80, 0x83, 0xcc, 0xff, 0x82, 0x84, 0xd2, 0xc0, 0x82, 0x85, 0xd3, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7d, 0x76, 0x85, 0x28, 0x7b, 0x7a, 0x8d, 0xdb, 0x85, 0x84, 0x9a, 0xff, 0x96, 0x94, 0xa3, 0xff, 0xaa, 0xa0, 0xa2, 0xff, 0xb2, 0xa3, 0x9a, 0xff, 0xb7, 0xa4, 0x8c, 0xff, 0xbc, 0xa7, 0x81, 0xff, 0xbf, 0xab, 0x81, 0xff, 0xbe, 0xa6, 0x80, 0xff, 0xb8, 0x9f, 0x7d, 0xff, 0xab, 0x9a, 0x84, 0xff, 0x9c, 0x92, 0x8c, 0xff, 0x8f, 0x87, 0x92, 0xff, 0x83, 0x83, 0x98, 0xff, 0x84, 0x8f, 0xa7, 0xff, 0xa8, 0xb8, 0xcc, 0xff, 0xce, 0xda, 0xeb, 0xff, 0xd2, 0xdf, 0xee, 0xff, 0xbc, 0xce, 0xdf, 0xff, 0x84, 0x9a, 0xad, 0xff, 0x97, 0xa4, 0xb2, 0xff, 0xa4, 0xb4, 0xc2, 0xff, 0x92, 0xa8, 0xbe, 0xff, 0x7a, 0x91, 0xaa, 0xff, 0x4b, 0x5e, 0x76, 0xff, 0x56, 0x69, 0x7f, 0xff, 0x3a, 0x52, 0x6f, 0xff, 0x22, 0x33, 0x48, 0xff, 0x5a, 0x73, 0x8e, 0xff, 0x3b, 0x56, 0x73, 0xff, 0x2e, 0x42, 0x58, 0xff, 0x46, 0x63, 0x7b, 0xff, 0x70, 0x8d, 0xa5, 0xff, 0x92, 0xb0, 0xc9, 0xff, 0x8f, 0xae, 0xca, 0xff, 0x94, 0xab, 0xc8, 0xff, 0xc1, 0xc7, 0xcf, 0xff, 0x9b, 0xa6, 0xb5, 0xff, 0x4f, 0x68, 0x83, 0xff, 0x48, 0x5a, 0x6e, 0xff, 0xa8, 0xa6, 0xa7, 0xff, 0xca, 0xbb, 0xa6, 0xff, 0xc6, 0xb8, 0x9c, 0xff, 0xcb, 0xbc, 0xa2, 0xff, 0xcc, 0xbc, 0xa5, 0xff, 0xc7, 0xb8, 0xa9, 0xff, 0xc0, 0xb0, 0xac, 0xff, 0xaf, 0xa2, 0xaf, 0xff, 0x95, 0x94, 0xac, 0xff, 0x86, 0x86, 0xb6, 0xff, 0x7f, 0x81, 0xbe, 0xff, 0x83, 0x83, 0xca, 0xff, 0x85, 0x86, 0xd2, 0xff, 0x84, 0x89, 0xd5, 0xdb, 0x86, 0x8c, 0xda, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x90, 0x83, 0x81, 0x25, 0x80, 0x7b, 0x83, 0xe2, 0x7c, 0x7b, 0x8b, 0xff, 0x80, 0x81, 0x98, 0xff, 0x8d, 0x90, 0xa1, 0xff, 0x9f, 0x9a, 0xa0, 0xff, 0xaa, 0x9d, 0x97, 0xff, 0xb0, 0x9f, 0x87, 0xff, 0xb2, 0x9f, 0x78, 0xff, 0xb2, 0x9e, 0x79, 0xff, 0xb0, 0x9b, 0x75, 0xff, 0xa9, 0x98, 0x76, 0xff, 0x9e, 0x91, 0x83, 0xff, 0x94, 0x8b, 0x85, 0xff, 0x88, 0x83, 0x89, 0xff, 0x86, 0x8d, 0xa1, 0xff, 0xae, 0xc1, 0xd8, 0xff, 0xbb, 0xcf, 0xe3, 0xff, 0xc1, 0xcf, 0xe4, 0xff, 0x9e, 0xb0, 0xc6, 0xff, 0x95, 0xab, 0xc4, 0xff, 0x66, 0x7f, 0x9a, 0xff, 0x1c, 0x2d, 0x43, 0xff, 0x21, 0x32, 0x45, 0xff, 0x3b, 0x4d, 0x63, 0xff, 0x41, 0x58, 0x72, 0xff, 0x2d, 0x3e, 0x52, 0xff, 0x0e, 0x18, 0x24, 0xff, 0x21, 0x2d, 0x3e, 0xff, 0x27, 0x3c, 0x4c, 0xff, 0x0b, 0x17, 0x27, 0xff, 0x31, 0x40, 0x54, 0xff, 0x42, 0x5b, 0x74, 0xff, 0x24, 0x33, 0x46, 0xff, 0x15, 0x26, 0x38, 0xff, 0x62, 0x76, 0x8d, 0xff, 0x70, 0x89, 0xa4, 0xff, 0x89, 0xa5, 0xb8, 0xff, 0x7d, 0x9b, 0xb5, 0xff, 0x8d, 0xa9, 0xc1, 0xff, 0x75, 0x90, 0xa8, 0xff, 0x2b, 0x49, 0x6b, 0xff, 0x2a, 0x38, 0x4f, 0xff, 0x8b, 0x8f, 0x91, 0xff, 0xcf, 0xc6, 0xad, 0xff, 0xca, 0xb7, 0x9c, 0xff, 0xca, 0xb4, 0x9f, 0xff, 0xc4, 0xb1, 0xa1, 0xff, 0xbe, 0xac, 0xa0, 0xff, 0xad, 0x9f, 0xa0, 0xff, 0x9b, 0x8f, 0x9d, 0xff, 0x86, 0x84, 0xae, 0xff, 0x83, 0x82, 0xb8, 0xff, 0x85, 0x84, 0xc6, 0xff, 0x87, 0x86, 0xd0, 0xff, 0x87, 0x8a, 0xd4, 0xff, 0x8a, 0x8d, 0xda, 0xe2, 0x8a, 0x8c, 0xda, 0x25, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb0, 0x96, 0x7c, 0x28, 0x9f, 0x8d, 0x7e, 0xe1, 0x89, 0x80, 0x83, 0xff, 0x80, 0x7c, 0x8e, 0xff, 0x7a, 0x7f, 0x95, 0xff, 0x83, 0x8b, 0x9c, 0xff, 0x96, 0x98, 0x9d, 0xff, 0xa2, 0x99, 0x97, 0xff, 0xa9, 0x9a, 0x87, 0xff, 0xab, 0x9a, 0x75, 0xff, 0xa8, 0x96, 0x6f, 0xff, 0xa7, 0x93, 0x70, 0xff, 0xab, 0x90, 0x71, 0xff, 0xa1, 0x8a, 0x79, 0xff, 0x90, 0x89, 0x8c, 0xff, 0x9a, 0x9d, 0xab, 0xff, 0xb8, 0xc4, 0xd7, 0xff, 0xc9, 0xda, 0xee, 0xff, 0xd0, 0xdf, 0xed, 0xff, 0xa9, 0xbd, 0xd0, 0xff, 0xa6, 0xbc, 0xd2, 0xff, 0x74, 0x8b, 0xa6, 0xff, 0x30, 0x47, 0x62, 0xff, 0x28, 0x34, 0x46, 0xff, 0x17, 0x24, 0x35, 0xff, 0x16, 0x22, 0x34, 0xff, 0x31, 0x44, 0x5b, 0xff, 0x27, 0x34, 0x49, 0xff, 0x12, 0x18, 0x27, 0xff, 0x0a, 0x12, 0x1d, 0xff, 0x1b, 0x26, 0x36, 0xff, 0x23, 0x32, 0x3e, 0xff, 0x2f, 0x3a, 0x48, 0xff, 0x1b, 0x27, 0x3a, 0xff, 0x20, 0x29, 0x38, 0xff, 0x13, 0x1e, 0x2b, 0xff, 0x21, 0x31, 0x42, 0xff, 0x3d, 0x52, 0x69, 0xff, 0x63, 0x78, 0x8a, 0xff, 0x67, 0x80, 0x99, 0xff, 0x68, 0x82, 0x9b, 0xff, 0x6c, 0x87, 0xa0, 0xff, 0x3d, 0x56, 0x73, 0xff, 0x1c, 0x31, 0x4c, 0xff, 0x19, 0x29, 0x39, 0xff, 0x78, 0x7e, 0x7f, 0xff, 0xc2, 0xb7, 0xab, 0xff, 0xd8, 0xc0, 0xa5, 0xff, 0xc9, 0xb3, 0x99, 0xff, 0xc1, 0xad, 0x94, 0xff, 0xb0, 0x9d, 0x8c, 0xff, 0xa2, 0x8d, 0x88, 0xff, 0x89, 0x80, 0x9a, 0xff, 0x86, 0x81, 0xae, 0xff, 0x8a, 0x85, 0xbf, 0xff, 0x8b, 0x88, 0xcd, 0xff, 0x8b, 0x8b, 0xd5, 0xff, 0x8b, 0x8e, 0xdc, 0xff, 0x8b, 0x8c, 0xdc, 0xe1, 0x8b, 0x8c, 0xdc, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x9f, 0x96, 0x16, 0xa9, 0x96, 0x7e, 0xda, 0xa1, 0x8e, 0x7d, 0xff, 0x8f, 0x81, 0x82, 0xff, 0x80, 0x7b, 0x8d, 0xff, 0x7a, 0x80, 0x94, 0xff, 0x81, 0x8b, 0x99, 0xff, 0x8c, 0x92, 0x9a, 0xff, 0x98, 0x94, 0x94, 0xff, 0xa0, 0x95, 0x88, 0xff, 0xa6, 0x97, 0x79, 0xff, 0xa7, 0x97, 0x6e, 0xff, 0xa8, 0x97, 0x6f, 0xff, 0xab, 0x95, 0x70, 0xff, 0xa1, 0x95, 0x81, 0xff, 0x9f, 0xa9, 0xae, 0xff, 0xc4, 0xd5, 0xe7, 0xff, 0xc5, 0xd8, 0xea, 0xff, 0xbd, 0xd2, 0xe2, 0xff, 0xad, 0xc2, 0xd3, 0xff, 0x6e, 0x8a, 0x9f, 0xff, 0x2e, 0x46, 0x5a, 0xff, 0x2f, 0x40, 0x56, 0xff, 0x31, 0x40, 0x55, 0xff, 0x0f, 0x1c, 0x2c, 0xff, 0x1e, 0x29, 0x3a, 0xff, 0x14, 0x23, 0x37, 0xff, 0x25, 0x37, 0x4d, 0xff, 0x18, 0x27, 0x3d, 0xff, 0x1a, 0x26, 0x3a, 0xff, 0x12, 0x1d, 0x2d, 0xff, 0x19, 0x21, 0x30, 0xff, 0x17, 0x25, 0x34, 0xff, 0x30, 0x3d, 0x4a, 0xff, 0x0f, 0x10, 0x18, 0xff, 0x22, 0x27, 0x34, 0xff, 0x09, 0x0e, 0x18, 0xff, 0x1e, 0x25, 0x2f, 0xff, 0x43, 0x52, 0x61, 0xff, 0x28, 0x35, 0x43, 0xff, 0x37, 0x4d, 0x61, 0xff, 0x60, 0x79, 0x8e, 0xff, 0x44, 0x5d, 0x73, 0xff, 0x36, 0x4e, 0x69, 0xff, 0x17, 0x27, 0x3f, 0xff, 0x0f, 0x1b, 0x2c, 0xff, 0x2b, 0x3c, 0x55, 0xff, 0x5e, 0x64, 0x6e, 0xff, 0xb4, 0xa9, 0x97, 0xff, 0xd1, 0xbd, 0xa1, 0xff, 0xc5, 0xaf, 0x8f, 0xff, 0xb4, 0xa2, 0x83, 0xff, 0xa4, 0x8a, 0x75, 0xff, 0x90, 0x80, 0x8c, 0xff, 0x88, 0x81, 0xa5, 0xff, 0x8f, 0x87, 0xbc, 0xff, 0x90, 0x8a, 0xce, 0xff, 0x8e, 0x8d, 0xd7, 0xff, 0x8b, 0x8e, 0xdd, 0xff, 0x8c, 0x8d, 0xdd, 0xff, 0x8b, 0x8c, 0xdd, 0xda, 0x89, 0x8c, 0xdc, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa1, 0xa7, 0xb8, 0x0d, 0xa4, 0xa3, 0xae, 0xc1, 0xa3, 0x9b, 0x9a, 0xff, 0x99, 0x8c, 0x87, 0xff, 0x8a, 0x7e, 0x7f, 0xff, 0x80, 0x7d, 0x8a, 0xff, 0x7e, 0x80, 0x92, 0xff, 0x82, 0x89, 0x99, 0xff, 0x92, 0x94, 0x9d, 0xff, 0x99, 0x97, 0x97, 0xff, 0x9c, 0x98, 0x87, 0xff, 0xa5, 0x98, 0x7e, 0xff, 0xa9, 0x9a, 0x76, 0xff, 0xaf, 0x99, 0x74, 0xff, 0xaa, 0x9e, 0x90, 0xff, 0xb3, 0xc0, 0xd0, 0xff, 0xd0, 0xe9, 0xf5, 0xff, 0xc5, 0xde, 0xef, 0xff, 0xc3, 0xdb, 0xf1, 0xff, 0xae, 0xcb, 0xe4, 0xff, 0x7d, 0x9f, 0xbd, 0xff, 0x64, 0x81, 0xa0, 0xff, 0x36, 0x4a, 0x62, 0xff, 0x05, 0x11, 0x1f, 0xff, 0x11, 0x19, 0x28, 0xff, 0x1e, 0x2a, 0x40, 0xff, 0x13, 0x1f, 0x32, 0xff, 0x16, 0x27, 0x41, 0xff, 0x12, 0x25, 0x3f, 0xff, 0x16, 0x29, 0x43, 0xff, 0x03, 0x13, 0x30, 0xff, 0x04, 0x15, 0x2a, 0xff, 0x0d, 0x1e, 0x30, 0xff, 0x0a, 0x15, 0x29, 0xff, 0x0b, 0x15, 0x24, 0xff, 0x27, 0x33, 0x38, 0xff, 0x18, 0x1d, 0x23, 0xff, 0x1e, 0x20, 0x26, 0xff, 0x30, 0x36, 0x3d, 0xff, 0x19, 0x1e, 0x28, 0xff, 0x15, 0x1c, 0x25, 0xff, 0x3f, 0x50, 0x62, 0xff, 0x38, 0x50, 0x64, 0xff, 0x20, 0x3b, 0x53, 0xff, 0x1d, 0x33, 0x4c, 0xff, 0x1b, 0x24, 0x35, 0xff, 0x0e, 0x12, 0x1d, 0xff, 0x1d, 0x24, 0x2e, 0xff, 0x2c, 0x3c, 0x4d, 0xff, 0x65, 0x71, 0x7f, 0xff, 0x9e, 0x8f, 0x82, 0xff, 0xc3, 0xaf, 0x96, 0xff, 0xb1, 0xa2, 0x85, 0xff, 0xaa, 0x92, 0x7c, 0xff, 0x9b, 0x89, 0x98, 0xff, 0x90, 0x8b, 0xac, 0xff, 0x92, 0x8c, 0xc0, 0xff, 0x90, 0x8d, 0xd3, 0xff, 0x8e, 0x8e, 0xdb, 0xff, 0x8d, 0x8e, 0xdf, 0xff, 0x8e, 0x8f, 0xe0, 0xff, 0x8d, 0x8e, 0xe0, 0xff, 0x89, 0x8c, 0xde, 0xc1, 0x87, 0x8a, 0xdb, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa1, 0xbb, 0xfe, 0x03, 0x94, 0xb1, 0xda, 0x92, 0x94, 0xa3, 0xbd, 0xff, 0x99, 0x9e, 0xac, 0xff, 0x93, 0x8e, 0x9d, 0xff, 0x7f, 0x78, 0x7a, 0xff, 0x7b, 0x76, 0x80, 0xff, 0x7d, 0x7d, 0x8e, 0xff, 0x86, 0x88, 0x93, 0xff, 0x92, 0x93, 0x97, 0xff, 0x99, 0x96, 0x90, 0xff, 0xa1, 0x97, 0x85, 0xff, 0xa8, 0x98, 0x7e, 0xff, 0xad, 0x9a, 0x7b, 0xff, 0xaa, 0x97, 0x85, 0xff, 0xc1, 0xce, 0xd8, 0xff, 0xcf, 0xde, 0xe7, 0xff, 0x9a, 0xba, 0xd2, 0xff, 0x7a, 0x96, 0xb5, 0xff, 0x50, 0x70, 0x90, 0xff, 0x45, 0x62, 0x83, 0xff, 0x54, 0x70, 0x92, 0xff, 0x38, 0x55, 0x77, 0xff, 0x20, 0x2e, 0x4a, 0xff, 0x0c, 0x18, 0x31, 0xff, 0x23, 0x39, 0x54, 0xff, 0x48, 0x5f, 0x7c, 0xff, 0x61, 0x7b, 0xa7, 0xff, 0x5a, 0x79, 0xa5, 0xff, 0x61, 0x84, 0xb2, 0xff, 0x53, 0x78, 0xa9, 0xff, 0x5b, 0x7d, 0xae, 0xff, 0x4b, 0x69, 0x98, 0xff, 0x32, 0x52, 0x7b, 0xff, 0x4d, 0x6c, 0x93, 0xff, 0x29, 0x40, 0x5f, 0xff, 0x2c, 0x3e, 0x4f, 0xff, 0x15, 0x1d, 0x29, 0xff, 0x27, 0x28, 0x31, 0xff, 0x09, 0x0b, 0x10, 0xff, 0x20, 0x24, 0x2b, 0xff, 0x19, 0x1d, 0x21, 0xff, 0x4a, 0x55, 0x5c, 0xff, 0x09, 0x16, 0x25, 0xff, 0x18, 0x2a, 0x40, 0xff, 0x11, 0x21, 0x34, 0xff, 0x0b, 0x10, 0x1a, 0xff, 0x11, 0x12, 0x1b, 0xff, 0x05, 0x07, 0x10, 0xff, 0x06, 0x0b, 0x16, 0xff, 0x53, 0x5d, 0x6a, 0xff, 0x62, 0x6c, 0x7b, 0xff, 0x9f, 0x95, 0x93, 0xff, 0xc0, 0xab, 0x93, 0xff, 0xb0, 0x9b, 0x7d, 0xff, 0xa0, 0x8e, 0x9b, 0xff, 0x98, 0x90, 0xb1, 0xff, 0x98, 0x8f, 0xc5, 0xff, 0x94, 0x92, 0xd1, 0xff, 0x8f, 0x8e, 0xdd, 0xff, 0x8f, 0x90, 0xe0, 0xff, 0x8d, 0x8f, 0xe0, 0xff, 0x8c, 0x8f, 0xe0, 0xff, 0x8a, 0x8c, 0xde, 0xff, 0x88, 0x8c, 0xdd, 0x92, 0x87, 0x8a, 0xdb, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77, 0xbc, 0xf7, 0x62, 0x7d, 0xb6, 0xea, 0xfb, 0x85, 0xa8, 0xd0, 0xff, 0x85, 0x9a, 0xb8, 0xff, 0x86, 0x8e, 0xa8, 0xff, 0x73, 0x73, 0x7b, 0xff, 0x70, 0x6e, 0x79, 0xff, 0x78, 0x78, 0x84, 0xff, 0x81, 0x83, 0x8d, 0xff, 0x8c, 0x8d, 0x8f, 0xff, 0x94, 0x90, 0x8a, 0xff, 0x9b, 0x92, 0x83, 0xff, 0xa5, 0x97, 0x80, 0xff, 0xa8, 0x97, 0x7a, 0xff, 0xb9, 0xb8, 0xb8, 0xff, 0x57, 0x76, 0x8f, 0xff, 0x37, 0x41, 0x52, 0xff, 0x27, 0x36, 0x41, 0xff, 0x36, 0x48, 0x62, 0xff, 0x1f, 0x35, 0x4f, 0xff, 0x1d, 0x30, 0x4d, 0xff, 0x14, 0x27, 0x45, 0xff, 0x0f, 0x21, 0x40, 0xff, 0x11, 0x2d, 0x5a, 0xff, 0x40, 0x5b, 0x88, 0xff, 0x96, 0xaf, 0xce, 0xff, 0xc4, 0xe1, 0xf6, 0xff, 0xac, 0xd0, 0xfa, 0xff, 0xa3, 0xca, 0xf4, 0xff, 0x9d, 0xc1, 0xee, 0xff, 0x95, 0xb8, 0xee, 0xff, 0x99, 0xbe, 0xf4, 0xff, 0x87, 0xaf, 0xe8, 0xff, 0x85, 0xa8, 0xe0, 0xff, 0x54, 0x78, 0xb1, 0xff, 0x41, 0x63, 0x9b, 0xff, 0x33, 0x50, 0x83, 0xff, 0x22, 0x39, 0x59, 0xff, 0x36, 0x45, 0x61, 0xff, 0x30, 0x3a, 0x4f, 0xff, 0x0a, 0x13, 0x24, 0xff, 0x27, 0x30, 0x42, 0xff, 0x10, 0x16, 0x20, 0xff, 0x13, 0x1a, 0x22, 0xff, 0x09, 0x13, 0x21, 0xff, 0x14, 0x1e, 0x2f, 0xff, 0x0a, 0x10, 0x18, 0xff, 0x08, 0x0e, 0x15, 0xff, 0x0a, 0x0c, 0x10, 0xff, 0x09, 0x0b, 0x10, 0xff, 0x13, 0x18, 0x21, 0xff, 0x40, 0x4b, 0x5b, 0xff, 0x56, 0x65, 0x70, 0xff, 0xb2, 0xa1, 0x97, 0xff, 0xb7, 0x9d, 0x88, 0xff, 0xa2, 0x95, 0x99, 0xff, 0x9b, 0x92, 0xb5, 0xff, 0x9c, 0x92, 0xc7, 0xff, 0x96, 0x94, 0xd1, 0xff, 0x91, 0x90, 0xde, 0xff, 0x8f, 0x90, 0xe1, 0xff, 0x8f, 0x91, 0xe1, 0xff, 0x8d, 0x90, 0xe1, 0xff, 0x8c, 0x8f, 0xe0, 0xff, 0x8a, 0x8d, 0xde, 0xfb, 0x8a, 0x8c, 0xdd, 0x62, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0xc3, 0xff, 0x20, 0x4d, 0xbd, 0xff, 0xe8, 0x59, 0xba, 0xf9, 0xff, 0x68, 0xaa, 0xe1, 0xff, 0x6a, 0x95, 0xc4, 0xff, 0x74, 0x8c, 0xb1, 0xff, 0x6e, 0x75, 0x7f, 0xff, 0x69, 0x68, 0x6f, 0xff, 0x71, 0x71, 0x78, 0xff, 0x7a, 0x7c, 0x82, 0xff, 0x85, 0x83, 0x84, 0xff, 0x8a, 0x86, 0x81, 0xff, 0x92, 0x8b, 0x7d, 0xff, 0x9a, 0x8e, 0x7a, 0xff, 0xa1, 0x95, 0x7f, 0xff, 0xae, 0xa4, 0x99, 0xff, 0x79, 0x8b, 0x96, 0xff, 0x50, 0x5f, 0x75, 0xff, 0x49, 0x64, 0x80, 0xff, 0x33, 0x46, 0x63, 0xff, 0x1b, 0x2b, 0x42, 0xff, 0x1f, 0x2d, 0x44, 0xff, 0x03, 0x11, 0x30, 0xff, 0x53, 0x68, 0x8d, 0xff, 0x89, 0xa5, 0xce, 0xff, 0xb8, 0xcf, 0xef, 0xff, 0xe9, 0xf4, 0xfe, 0xff, 0xeb, 0xf7, 0xfb, 0xff, 0xc7, 0xe1, 0xf4, 0xff, 0xcc, 0xe6, 0xfa, 0xff, 0xd3, 0xed, 0xfb, 0xff, 0xc3, 0xe3, 0xf7, 0xff, 0xc2, 0xe2, 0xfc, 0xff, 0xb0, 0xd0, 0xf5, 0xff, 0xa0, 0xc4, 0xf2, 0xff, 0x88, 0xaf, 0xe0, 0xff, 0x6d, 0x95, 0xc5, 0xff, 0x72, 0x97, 0xc5, 0xff, 0x5e, 0x81, 0xae, 0xff, 0x59, 0x74, 0x9f, 0xff, 0x29, 0x40, 0x68, 0xff, 0x1f, 0x37, 0x5c, 0xff, 0x24, 0x39, 0x5a, 0xff, 0x14, 0x24, 0x42, 0xff, 0x13, 0x1d, 0x2f, 0xff, 0x15, 0x20, 0x2f, 0xff, 0x15, 0x1e, 0x2b, 0xff, 0x07, 0x09, 0x10, 0xff, 0x05, 0x09, 0x0d, 0xff, 0x08, 0x08, 0x0a, 0xff, 0x06, 0x07, 0x0a, 0xff, 0x03, 0x04, 0x09, 0xff, 0x0c, 0x12, 0x1b, 0xff, 0x03, 0x0e, 0x16, 0xff, 0x6f, 0x69, 0x67, 0xff, 0xb7, 0xaa, 0x9f, 0xff, 0xa6, 0x96, 0x9a, 0xff, 0x9c, 0x93, 0xb3, 0xff, 0x9c, 0x93, 0xc6, 0xff, 0x95, 0x94, 0xd0, 0xff, 0x91, 0x91, 0xde, 0xff, 0x91, 0x91, 0xe2, 0xff, 0x90, 0x92, 0xe2, 0xff, 0x8e, 0x91, 0xe2, 0xff, 0x8c, 0x8f, 0xe0, 0xff, 0x8c, 0x8e, 0xdf, 0xff, 0x8d, 0x8f, 0xe0, 0xe8, 0x8c, 0x8d, 0xde, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x12, 0xc4, 0xff, 0x05, 0x30, 0xc6, 0xff, 0xc0, 0x3a, 0xbe, 0xfe, 0xff, 0x43, 0xbd, 0xfe, 0xff, 0x53, 0xaf, 0xea, 0xff, 0x63, 0x9c, 0xd1, 0xff, 0x70, 0x8e, 0xb8, 0xff, 0x6e, 0x75, 0x7d, 0xff, 0x77, 0x72, 0x72, 0xff, 0x76, 0x76, 0x7a, 0xff, 0x76, 0x76, 0x7b, 0xff, 0x81, 0x7e, 0x7f, 0xff, 0x85, 0x80, 0x7b, 0xff, 0x89, 0x81, 0x76, 0xff, 0x8f, 0x87, 0x77, 0xff, 0x90, 0x89, 0x76, 0xff, 0x95, 0x85, 0x7a, 0xff, 0xa9, 0xb5, 0xbf, 0xff, 0xb8, 0xc4, 0xd7, 0xff, 0x5e, 0x79, 0x8d, 0xff, 0x18, 0x33, 0x53, 0xff, 0x1b, 0x2d, 0x44, 0xff, 0x10, 0x1c, 0x31, 0xff, 0x68, 0x81, 0xa7, 0xff, 0xa5, 0xc7, 0xf7, 0xff, 0xcc, 0xe0, 0xf5, 0xff, 0xfb, 0xfd, 0xfd, 0xff, 0xff, 0xfe, 0xf8, 0xff, 0xf0, 0xf5, 0xf7, 0xff, 0xd8, 0xed, 0xfa, 0xff, 0xd8, 0xee, 0xfc, 0xff, 0xcc, 0xe7, 0xf5, 0xff, 0xbc, 0xdf, 0xf5, 0xff, 0xb6, 0xd3, 0xf6, 0xff, 0xb1, 0xcb, 0xfb, 0xff, 0xa4, 0xc2, 0xf0, 0xff, 0x9e, 0xc0, 0xee, 0xff, 0x94, 0xb4, 0xe2, 0xff, 0x98, 0xb3, 0xdb, 0xff, 0x75, 0x98, 0xc4, 0xff, 0x77, 0x96, 0xc5, 0xff, 0x4d, 0x6a, 0x9c, 0xff, 0x3f, 0x5e, 0x90, 0xff, 0x32, 0x4f, 0x7c, 0xff, 0x2e, 0x46, 0x75, 0xff, 0x1d, 0x31, 0x56, 0xff, 0x10, 0x23, 0x38, 0xff, 0x0b, 0x15, 0x20, 0xff, 0x09, 0x0b, 0x10, 0xff, 0x08, 0x04, 0x06, 0xff, 0x04, 0x04, 0x0a, 0xff, 0x01, 0x03, 0x06, 0xff, 0x03, 0x04, 0x04, 0xff, 0x09, 0x09, 0x08, 0xff, 0x08, 0x08, 0x06, 0xff, 0x02, 0x05, 0x05, 0xff, 0x81, 0x81, 0x7e, 0xff, 0xb5, 0xa2, 0xad, 0xff, 0x9c, 0x95, 0xb4, 0xff, 0x9b, 0x93, 0xc5, 0xff, 0x94, 0x94, 0xd0, 0xff, 0x92, 0x92, 0xdd, 0xff, 0x92, 0x92, 0xe3, 0xff, 0x91, 0x93, 0xe3, 0xff, 0x8f, 0x92, 0xe4, 0xff, 0x8e, 0x91, 0xe2, 0xff, 0x8d, 0x90, 0xe1, 0xff, 0x8f, 0x90, 0xe1, 0xff, 0x8f, 0x90, 0xe1, 0xc0, 0x91, 0x91, 0xdf, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0d, 0xc0, 0xfd, 0x53, 0x1e, 0xc5, 0xff, 0xfa, 0x35, 0xc5, 0xfc, 0xff, 0x3a, 0xc0, 0xff, 0xff, 0x52, 0xb6, 0xf5, 0xff, 0x64, 0xa3, 0xd6, 0xff, 0x6f, 0x91, 0xba, 0xff, 0x76, 0x7d, 0x81, 0xff, 0x89, 0x81, 0x7f, 0xff, 0x8b, 0x85, 0x83, 0xff, 0x83, 0x80, 0x7d, 0xff, 0x82, 0x80, 0x7c, 0xff, 0x8b, 0x85, 0x7f, 0xff, 0x88, 0x80, 0x76, 0xff, 0x8b, 0x82, 0x76, 0xff, 0x85, 0x7e, 0x72, 0xff, 0xa2, 0xa6, 0xa8, 0xff, 0xbe, 0xce, 0xdf, 0xff, 0x9b, 0xb7, 0xd3, 0xff, 0x59, 0x77, 0x98, 0xff, 0x1b, 0x32, 0x54, 0xff, 0x0e, 0x24, 0x41, 0xff, 0x86, 0xa4, 0xc5, 0xff, 0xcc, 0xe0, 0xfe, 0xff, 0xeb, 0xf4, 0xfb, 0xff, 0xfd, 0xfe, 0xfd, 0xff, 0xfe, 0xfd, 0xf9, 0xff, 0xfa, 0xfd, 0xfc, 0xff, 0xee, 0xf7, 0xf9, 0xff, 0xda, 0xea, 0xf6, 0xff, 0xd4, 0xe8, 0xf6, 0xff, 0xcb, 0xe2, 0xf5, 0xff, 0xb8, 0xd5, 0xf3, 0xff, 0xb2, 0xcf, 0xf7, 0xff, 0xad, 0xc7, 0xf4, 0xff, 0xaa, 0xc7, 0xf0, 0xff, 0x9d, 0xbc, 0xe8, 0xff, 0x97, 0xb5, 0xe2, 0xff, 0x9f, 0xb5, 0xdc, 0xff, 0xa0, 0xba, 0xe0, 0xff, 0x92, 0xae, 0xd5, 0xff, 0x7e, 0x9a, 0xc5, 0xff, 0x66, 0x83, 0xaf, 0xff, 0x51, 0x6e, 0x9c, 0xff, 0x41, 0x5d, 0x8f, 0xff, 0x2d, 0x45, 0x6f, 0xff, 0x14, 0x27, 0x42, 0xff, 0x09, 0x12, 0x20, 0xff, 0x12, 0x12, 0x19, 0xff, 0x07, 0x09, 0x0a, 0xff, 0x05, 0x08, 0x09, 0xff, 0x09, 0x0b, 0x0c, 0xff, 0x05, 0x07, 0x08, 0xff, 0x02, 0x04, 0x05, 0xff, 0x09, 0x08, 0x0d, 0xff, 0x0d, 0x0e, 0x13, 0xff, 0x24, 0x27, 0x2c, 0xff, 0x77, 0x77, 0x80, 0xff, 0xa9, 0xa3, 0xc0, 0xff, 0x9f, 0x9c, 0xc5, 0xff, 0x9c, 0x99, 0xd4, 0xff, 0x99, 0x96, 0xdc, 0xff, 0x95, 0x94, 0xe1, 0xff, 0x92, 0x92, 0xe6, 0xff, 0x8f, 0x91, 0xe5, 0xff, 0x8d, 0x91, 0xe4, 0xff, 0x8e, 0x92, 0xe4, 0xff, 0x90, 0x91, 0xe1, 0xff, 0x91, 0x91, 0xe0, 0xfa, 0x91, 0x90, 0xdf, 0x53, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb5, 0xfc, 0x0c, 0x00, 0xc1, 0xfe, 0xdd, 0x05, 0xc5, 0xfe, 0xff, 0x1d, 0xc6, 0xfc, 0xff, 0x2d, 0xc4, 0xff, 0xff, 0x4d, 0xb5, 0xf1, 0xff, 0x62, 0x9c, 0xcf, 0xff, 0x76, 0x94, 0xba, 0xff, 0x90, 0x95, 0x96, 0xff, 0x9a, 0x8f, 0x87, 0xff, 0x9d, 0x95, 0x8c, 0xff, 0x95, 0x91, 0x87, 0xff, 0x8d, 0x87, 0x7f, 0xff, 0x94, 0x8e, 0x87, 0xff, 0x8d, 0x8a, 0x7c, 0xff, 0x8a, 0x82, 0x71, 0xff, 0x9e, 0xa3, 0xac, 0xff, 0xd6, 0xe2, 0xf2, 0xff, 0xd9, 0xe9, 0xf9, 0xff, 0xad, 0xc8, 0xe2, 0xff, 0x6c, 0x8c, 0xad, 0xff, 0x36, 0x58, 0x7e, 0xff, 0xa8, 0xbb, 0xcd, 0xff, 0xec, 0xfd, 0xff, 0xff, 0xfd, 0xff, 0xfb, 0xff, 0xfc, 0xfd, 0xfd, 0xff, 0xff, 0xfc, 0xf9, 0xff, 0xfe, 0xfc, 0xfb, 0xff, 0xf7, 0xfc, 0xff, 0xff, 0xe0, 0xec, 0xf5, 0xff, 0xdc, 0xeb, 0xf6, 0xff, 0xd9, 0xea, 0xf6, 0xff, 0xcc, 0xe2, 0xf2, 0xff, 0xbd, 0xd7, 0xf4, 0xff, 0xb6, 0xd1, 0xf7, 0xff, 0xb3, 0xcb, 0xf5, 0xff, 0xac, 0xc8, 0xf5, 0xff, 0xa4, 0xc1, 0xee, 0xff, 0xa5, 0xbf, 0xea, 0xff, 0xae, 0xc6, 0xef, 0xff, 0xaf, 0xc8, 0xed, 0xff, 0x96, 0xb1, 0xd6, 0xff, 0x9b, 0xb4, 0xd9, 0xff, 0x8f, 0xa9, 0xce, 0xff, 0x75, 0x91, 0xb8, 0xff, 0x56, 0x72, 0x9a, 0xff, 0x3b, 0x53, 0x78, 0xff, 0x3d, 0x53, 0x6d, 0xff, 0x1e, 0x2c, 0x3a, 0xff, 0x06, 0x07, 0x0e, 0xff, 0x06, 0x03, 0x07, 0xff, 0x04, 0x06, 0x07, 0xff, 0x04, 0x06, 0x07, 0xff, 0x09, 0x0b, 0x0c, 0xff, 0x06, 0x08, 0x09, 0xff, 0x06, 0x07, 0x08, 0xff, 0x15, 0x16, 0x1a, 0xff, 0x19, 0x1b, 0x1f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x86, 0x83, 0x96, 0xff, 0xb2, 0xab, 0xca, 0xff, 0xab, 0xa2, 0xd2, 0xff, 0xa5, 0xa0, 0xdb, 0xff, 0x9f, 0x9e, 0xe3, 0xff, 0x9b, 0x9a, 0xe3, 0xff, 0x96, 0x99, 0xe2, 0xff, 0x94, 0x96, 0xe2, 0xff, 0x92, 0x94, 0xe2, 0xff, 0x91, 0x92, 0xe0, 0xff, 0x91, 0x91, 0xdf, 0xff, 0x92, 0x92, 0xdc, 0xdd, 0x93, 0x93, 0xd9, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0xb9, 0xfe, 0x80, 0x00, 0xc1, 0xfe, 0xff, 0x01, 0xc6, 0xff, 0xff, 0x12, 0xc6, 0xfc, 0xff, 0x27, 0xc4, 0xff, 0xff, 0x52, 0xb7, 0xf2, 0xff, 0x6f, 0xa2, 0xd4, 0xff, 0x88, 0xa1, 0xc4, 0xff, 0xa1, 0xa1, 0x9c, 0xff, 0xaa, 0x9c, 0x8e, 0xff, 0xab, 0xa0, 0x8e, 0xff, 0xa6, 0xa0, 0x8f, 0xff, 0x9e, 0x94, 0x87, 0xff, 0xad, 0x9f, 0x93, 0xff, 0x9b, 0x97, 0x87, 0xff, 0x9d, 0xa3, 0xa5, 0xff, 0xc3, 0xd6, 0xea, 0xff, 0xc7, 0xdc, 0xf1, 0xff, 0xcf, 0xe2, 0xf3, 0xff, 0xba, 0xd3, 0xeb, 0xff, 0x82, 0xa5, 0xc4, 0xff, 0x83, 0xa6, 0xc7, 0xff, 0xf6, 0xfa, 0xfd, 0xff, 0xff, 0xfe, 0xf8, 0xff, 0xf6, 0xfc, 0xfe, 0xff, 0xfe, 0xfd, 0xf9, 0xff, 0xfe, 0xfc, 0xfb, 0xff, 0xfc, 0xfc, 0xfd, 0xff, 0xf8, 0xfb, 0xfd, 0xff, 0xef, 0xf8, 0xfb, 0xff, 0xeb, 0xf5, 0xf8, 0xff, 0xe0, 0xef, 0xf8, 0xff, 0xcf, 0xe8, 0xf7, 0xff, 0xbf, 0xde, 0xf6, 0xff, 0xb3, 0xd3, 0xf5, 0xff, 0xaf, 0xcd, 0xf2, 0xff, 0xa9, 0xc7, 0xf4, 0xff, 0xae, 0xc9, 0xf1, 0xff, 0xba, 0xd4, 0xf6, 0xff, 0xbf, 0xda, 0xfc, 0xff, 0xad, 0xc6, 0xf1, 0xff, 0x9b, 0xb4, 0xdb, 0xff, 0xa0, 0xba, 0xdd, 0xff, 0x9a, 0xb4, 0xd4, 0xff, 0x94, 0xaf, 0xd0, 0xff, 0x8c, 0xa9, 0xca, 0xff, 0x4f, 0x6a, 0x88, 0xff, 0x22, 0x35, 0x4f, 0xff, 0x15, 0x1e, 0x2d, 0xff, 0x09, 0x0b, 0x14, 0xff, 0x04, 0x06, 0x0a, 0xff, 0x02, 0x05, 0x07, 0xff, 0x01, 0x02, 0x03, 0xff, 0x01, 0x03, 0x04, 0xff, 0x06, 0x09, 0x09, 0xff, 0x08, 0x09, 0x0a, 0xff, 0x03, 0x05, 0x06, 0xff, 0x16, 0x18, 0x1b, 0xff, 0x05, 0x0a, 0x0e, 0xff, 0x8c, 0x8c, 0x91, 0xff, 0xc2, 0xb6, 0xca, 0xff, 0xb8, 0xac, 0xcf, 0xff, 0xb6, 0xaf, 0xda, 0xff, 0xb0, 0xab, 0xde, 0xff, 0xac, 0xa6, 0xe0, 0xff, 0xa8, 0xa5, 0xdf, 0xff, 0xa5, 0xa4, 0xde, 0xff, 0xa2, 0xa0, 0xdd, 0xff, 0x9e, 0x9d, 0xdb, 0xff, 0x99, 0x99, 0xd8, 0xff, 0x94, 0x97, 0xd6, 0xff, 0x93, 0x95, 0xd4, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x16, 0xb0, 0xfa, 0x13, 0x0d, 0xb6, 0xfc, 0xdf, 0x05, 0xc1, 0xfc, 0xff, 0x11, 0xc8, 0xff, 0xff, 0x23, 0xc7, 0xfe, 0xff, 0x37, 0xc2, 0xff, 0xff, 0x65, 0xbb, 0xf5, 0xff, 0x7d, 0xac, 0xdd, 0xff, 0x94, 0xab, 0xce, 0xff, 0xa5, 0xa5, 0x9e, 0xff, 0xb4, 0xa5, 0x92, 0xff, 0xb5, 0xa8, 0x94, 0xff, 0xb1, 0xa6, 0x94, 0xff, 0xaa, 0x9e, 0x8e, 0xff, 0xb7, 0xaa, 0x98, 0xff, 0xaf, 0xa3, 0x98, 0xff, 0xaf, 0xc6, 0xdb, 0xff, 0xad, 0xc7, 0xda, 0xff, 0xae, 0xc3, 0xe1, 0xff, 0xba, 0xd6, 0xe3, 0xff, 0xbc, 0xd2, 0xec, 0xff, 0x82, 0xaa, 0xcf, 0xff, 0xd6, 0xe5, 0xec, 0xff, 0xfc, 0xfd, 0xfe, 0xff, 0xfa, 0xfa, 0xfe, 0xff, 0xfe, 0xfc, 0xfa, 0xff, 0xfc, 0xfb, 0xfd, 0xff, 0xfd, 0xfa, 0xff, 0xff, 0xfd, 0xfb, 0xfd, 0xff, 0xfd, 0xfb, 0xfb, 0xff, 0xf8, 0xfc, 0xfa, 0xff, 0xed, 0xf7, 0xf7, 0xff, 0xd9, 0xeb, 0xf6, 0xff, 0xc6, 0xe3, 0xf5, 0xff, 0xb6, 0xdb, 0xf0, 0xff, 0xb0, 0xd4, 0xf2, 0xff, 0xab, 0xce, 0xf1, 0xff, 0xa8, 0xca, 0xf2, 0xff, 0xb4, 0xcf, 0xf5, 0xff, 0xbd, 0xd7, 0xf9, 0xff, 0xb5, 0xd2, 0xf4, 0xff, 0x9d, 0xb9, 0xe8, 0xff, 0x82, 0x9b, 0xc6, 0xff, 0x92, 0xac, 0xd0, 0xff, 0x9f, 0xbb, 0xdc, 0xff, 0x9e, 0xb8, 0xd7, 0xff, 0xa4, 0xbf, 0xd8, 0xff, 0x8c, 0xa6, 0xc1, 0xff, 0x38, 0x4c, 0x68, 0xff, 0x18, 0x21, 0x36, 0xff, 0x06, 0x08, 0x10, 0xff, 0x06, 0x0a, 0x0f, 0xff, 0x01, 0x03, 0x04, 0xff, 0x02, 0x04, 0x05, 0xff, 0x02, 0x03, 0x04, 0xff, 0x09, 0x0a, 0x0c, 0xff, 0x08, 0x08, 0x08, 0xff, 0x0e, 0x0f, 0x0f, 0xff, 0x08, 0x0a, 0x0c, 0xff, 0x12, 0x15, 0x18, 0xff, 0x58, 0x59, 0x5a, 0xff, 0xb9, 0xb1, 0xbb, 0xff, 0xc2, 0xb6, 0xcb, 0xff, 0xc1, 0xb6, 0xd4, 0xff, 0xbe, 0xb5, 0xdb, 0xff, 0xba, 0xb2, 0xde, 0xff, 0xba, 0xb1, 0xdc, 0xff, 0xb7, 0xb1, 0xda, 0xff, 0xb3, 0xae, 0xda, 0xff, 0xac, 0xaa, 0xd7, 0xff, 0xa5, 0xa4, 0xd3, 0xff, 0x9d, 0x9f, 0xd1, 0xff, 0x99, 0x9a, 0xd0, 0xdf, 0x97, 0x99, 0xd0, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x31, 0x9d, 0xe2, 0x64, 0x19, 0xb2, 0xfd, 0xff, 0x07, 0xc0, 0xfd, 0xff, 0x11, 0xc8, 0xff, 0xff, 0x1d, 0xc8, 0xfd, 0xff, 0x31, 0xc4, 0xff, 0xff, 0x57, 0xbe, 0xf6, 0xff, 0x7a, 0xad, 0xdc, 0xff, 0x8d, 0xa5, 0xc9, 0xff, 0xa7, 0xa8, 0xa1, 0xff, 0xb6, 0xa7, 0x92, 0xff, 0xb8, 0xa9, 0x97, 0xff, 0xb5, 0xa8, 0x96, 0xff, 0xb3, 0xa7, 0x96, 0xff, 0xba, 0xae, 0x9f, 0xff, 0xb7, 0xb4, 0xad, 0xff, 0x99, 0xb6, 0xd4, 0xff, 0xa2, 0xb3, 0xc1, 0xff, 0xa5, 0xc0, 0xe3, 0xff, 0xc6, 0xe0, 0xf0, 0xff, 0x9e, 0xbf, 0xe2, 0xff, 0x96, 0xbb, 0xd8, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xfc, 0xfc, 0xfb, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfe, 0xfc, 0xfc, 0xff, 0xfa, 0xfc, 0xfd, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfa, 0xfc, 0xfb, 0xff, 0xfa, 0xfe, 0xfd, 0xff, 0xf1, 0xf4, 0xf7, 0xff, 0xe0, 0xee, 0xf1, 0xff, 0xd7, 0xea, 0xf4, 0xff, 0xc6, 0xe0, 0xf5, 0xff, 0xbd, 0xdb, 0xf5, 0xff, 0xbf, 0xdc, 0xf4, 0xff, 0xbe, 0xdc, 0xf5, 0xff, 0xc2, 0xdd, 0xf7, 0xff, 0xbc, 0xd8, 0xf5, 0xff, 0xb5, 0xd0, 0xf6, 0xff, 0xb2, 0xc8, 0xf5, 0xff, 0x94, 0xb1, 0xe0, 0xff, 0x6d, 0x84, 0xb2, 0xff, 0x75, 0x8d, 0xb8, 0xff, 0x6e, 0x8b, 0xb4, 0xff, 0x75, 0x8f, 0xb5, 0xff, 0x8a, 0x9f, 0xbd, 0xff, 0xa7, 0xc1, 0xdd, 0xff, 0x51, 0x6a, 0x88, 0xff, 0x21, 0x2e, 0x48, 0xff, 0x08, 0x0b, 0x15, 0xff, 0x04, 0x05, 0x0c, 0xff, 0x02, 0x04, 0x07, 0xff, 0x01, 0x03, 0x04, 0xff, 0x03, 0x03, 0x04, 0xff, 0x06, 0x05, 0x07, 0xff, 0x0a, 0x0a, 0x0a, 0xff, 0x08, 0x08, 0x08, 0xff, 0x17, 0x17, 0x17, 0xff, 0x36, 0x37, 0x37, 0xff, 0x33, 0x37, 0x37, 0xff, 0x57, 0x59, 0x59, 0xff, 0xc9, 0xbf, 0xc6, 0xff, 0xd1, 0xc1, 0xce, 0xff, 0xca, 0xbf, 0xd4, 0xff, 0xc3, 0xbc, 0xd7, 0xff, 0xc4, 0xbc, 0xd7, 0xff, 0xc4, 0xbb, 0xd5, 0xff, 0xbf, 0xb8, 0xd4, 0xff, 0xb7, 0xb3, 0xd0, 0xff, 0xae, 0xae, 0xcb, 0xff, 0xa9, 0xa8, 0xc9, 0xff, 0xa2, 0xa2, 0xc9, 0xff, 0x9b, 0x9d, 0xcb, 0x64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5c, 0x7b, 0x96, 0x07, 0x44, 0x9c, 0xd6, 0xe3, 0x27, 0xb1, 0xf8, 0xff, 0x11, 0xbc, 0xfc, 0xff, 0x11, 0xc1, 0xff, 0xff, 0x1e, 0xc2, 0xfe, 0xff, 0x2d, 0xbf, 0xff, 0xff, 0x4c, 0xb5, 0xee, 0xff, 0x69, 0xa0, 0xca, 0xff, 0x83, 0x9d, 0xbe, 0xff, 0xa3, 0xa2, 0x9f, 0xff, 0xaf, 0x9b, 0x8c, 0xff, 0xaf, 0xa0, 0x90, 0xff, 0xb1, 0xa3, 0x94, 0xff, 0xb2, 0xa6, 0x97, 0xff, 0xbd, 0xad, 0xa2, 0xff, 0x9e, 0xb6, 0xbd, 0xff, 0x96, 0xac, 0xc0, 0xff, 0xb0, 0xb6, 0xcf, 0xff, 0xa8, 0xd0, 0xe4, 0xff, 0xb7, 0xca, 0xeb, 0xff, 0x6b, 0xa0, 0xc9, 0xff, 0xd7, 0xe3, 0xec, 0xff, 0xfe, 0xfe, 0xfd, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xfc, 0xfc, 0xfc, 0xff, 0xff, 0xfe, 0xf9, 0xff, 0xff, 0xfd, 0xfc, 0xff, 0xfa, 0xfd, 0xfe, 0xff, 0xe5, 0xf0, 0xf6, 0xff, 0xdb, 0xeb, 0xf8, 0xff, 0xe1, 0xf1, 0xfb, 0xff, 0xe2, 0xf6, 0xfe, 0xff, 0xdd, 0xf7, 0xfe, 0xff, 0xc5, 0xe0, 0xf5, 0xff, 0xb7, 0xd3, 0xe3, 0xff, 0xae, 0xce, 0xd3, 0xff, 0xae, 0xc9, 0xce, 0xff, 0xb0, 0xc9, 0xc9, 0xff, 0xb8, 0xd2, 0xdc, 0xff, 0xb6, 0xcc, 0xf0, 0xff, 0xa4, 0xbd, 0xe2, 0xff, 0x70, 0x89, 0xb6, 0xff, 0x56, 0x70, 0xa4, 0xff, 0x59, 0x74, 0xa9, 0xff, 0x54, 0x71, 0xa2, 0xff, 0x38, 0x55, 0x82, 0xff, 0x55, 0x70, 0x94, 0xff, 0x6b, 0x86, 0xaa, 0xff, 0x33, 0x43, 0x5f, 0xff, 0x10, 0x19, 0x28, 0xff, 0x06, 0x09, 0x15, 0xff, 0x07, 0x09, 0x10, 0xff, 0x01, 0x05, 0x09, 0xff, 0x02, 0x03, 0x06, 0xff, 0x01, 0x02, 0x03, 0xff, 0x05, 0x04, 0x05, 0xff, 0x0d, 0x0d, 0x0d, 0xff, 0x20, 0x20, 0x20, 0xff, 0x44, 0x45, 0x45, 0xff, 0x2b, 0x30, 0x2e, 0xff, 0x66, 0x69, 0x63, 0xff, 0xb5, 0xae, 0xac, 0xff, 0xd8, 0xca, 0xca, 0xff, 0xd7, 0xca, 0xc9, 0xff, 0xd3, 0xc8, 0xcd, 0xff, 0xce, 0xc7, 0xd2, 0xff, 0xcf, 0xc6, 0xcf, 0xff, 0xcc, 0xc3, 0xca, 0xff, 0xc3, 0xbd, 0xc5, 0xff, 0xba, 0xb9, 0xc1, 0xff, 0xb2, 0xb0, 0xbf, 0xff, 0xa9, 0xab, 0xbe, 0xff, 0x9e, 0xa3, 0xbe, 0xe3, 0x99, 0xa2, 0xbd, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x73, 0x97, 0xb0, 0x50, 0x61, 0xa3, 0xce, 0xfb, 0x48, 0xaf, 0xea, 0xff, 0x3a, 0xb6, 0xf6, 0xff, 0x30, 0xba, 0xfe, 0xff, 0x33, 0xba, 0xf9, 0xff, 0x40, 0xb7, 0xfb, 0xff, 0x5d, 0xac, 0xe6, 0xff, 0x6e, 0x9d, 0xc6, 0xff, 0x80, 0x9a, 0xb9, 0xff, 0x89, 0x8b, 0x89, 0xff, 0x9b, 0x8c, 0x80, 0xff, 0x9b, 0x8f, 0x86, 0xff, 0x9b, 0x90, 0x88, 0xff, 0xaa, 0xa0, 0x98, 0xff, 0xa8, 0xa1, 0x9b, 0xff, 0x8e, 0xa7, 0xbf, 0xff, 0x9d, 0xa8, 0xb3, 0xff, 0xa9, 0xc3, 0xe1, 0xff, 0xb7, 0xd4, 0xed, 0xff, 0x88, 0xae, 0xd7, 0xff, 0x77, 0xa0, 0xbd, 0xff, 0xf1, 0xf8, 0xfb, 0xff, 0xfe, 0xfd, 0xfb, 0xff, 0xfd, 0xfc, 0xfc, 0xff, 0xfb, 0xfc, 0xfd, 0xff, 0xfa, 0xfd, 0xfc, 0xff, 0xee, 0xf5, 0xfb, 0xff, 0xf2, 0xf6, 0xfe, 0xff, 0xe6, 0xef, 0xf8, 0xff, 0xd3, 0xe4, 0xf2, 0xff, 0xdc, 0xeb, 0xf8, 0xff, 0xda, 0xe2, 0xe5, 0xff, 0xa3, 0xb0, 0xb9, 0xff, 0x69, 0x7a, 0x80, 0xff, 0x40, 0x4d, 0x50, 0xff, 0x1b, 0x2d, 0x2d, 0xff, 0x19, 0x2b, 0x29, 0xff, 0x00, 0x1a, 0x1a, 0xff, 0x7c, 0x6d, 0x56, 0xff, 0x96, 0x87, 0x70, 0xff, 0x2a, 0x3a, 0x43, 0xff, 0x96, 0xab, 0xbb, 0xff, 0x9b, 0xb3, 0xd5, 0xff, 0x69, 0x81, 0xb2, 0xff, 0x5e, 0x76, 0xa9, 0xff, 0x49, 0x66, 0x98, 0xff, 0x30, 0x4f, 0x85, 0xff, 0x1d, 0x38, 0x6c, 0xff, 0x27, 0x3d, 0x65, 0xff, 0x4e, 0x64, 0x83, 0xff, 0x13, 0x21, 0x39, 0xff, 0x0e, 0x15, 0x24, 0xff, 0x09, 0x0c, 0x15, 0xff, 0x06, 0x09, 0x10, 0xff, 0x07, 0x0a, 0x0c, 0xff, 0x05, 0x07, 0x07, 0xff, 0x05, 0x05, 0x05, 0xff, 0x0f, 0x0f, 0x0f, 0xff, 0x13, 0x13, 0x13, 0xff, 0x11, 0x10, 0x11, 0xff, 0x20, 0x1f, 0x1f, 0xff, 0x68, 0x6a, 0x64, 0xff, 0x9b, 0x9b, 0x94, 0xff, 0xbc, 0xb4, 0xaf, 0xff, 0xda, 0xcd, 0xc2, 0xff, 0xdd, 0xcc, 0xc1, 0xff, 0xdc, 0xce, 0xc7, 0xff, 0xda, 0xce, 0xc7, 0xff, 0xd5, 0xcb, 0xc3, 0xff, 0xcc, 0xc4, 0xbd, 0xff, 0xc2, 0xbe, 0xb8, 0xff, 0xb7, 0xb8, 0xb2, 0xff, 0xae, 0xb1, 0xb1, 0xff, 0xa4, 0xaa, 0xb3, 0xfb, 0x9c, 0xa5, 0xb3, 0x51, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x8f, 0x9a, 0x94, 0x03, 0x87, 0x9b, 0xa2, 0xa1, 0x79, 0xa3, 0xbc, 0xff, 0x63, 0xaa, 0xd7, 0xff, 0x57, 0xae, 0xea, 0xff, 0x52, 0xb5, 0xf3, 0xff, 0x4e, 0xb4, 0xf3, 0xff, 0x57, 0xb1, 0xf2, 0xff, 0x68, 0xab, 0xdc, 0xff, 0x6e, 0x9f, 0xc2, 0xff, 0x71, 0x85, 0x99, 0xff, 0x6d, 0x6b, 0x67, 0xff, 0x79, 0x73, 0x6d, 0xff, 0x7b, 0x73, 0x70, 0xff, 0x83, 0x7d, 0x7c, 0xff, 0x91, 0x8a, 0x8a, 0xff, 0x95, 0x97, 0x9e, 0xff, 0x7e, 0x98, 0xb4, 0xff, 0x90, 0xad, 0xbd, 0xff, 0xa5, 0xc8, 0xe7, 0xff, 0xae, 0xca, 0xea, 0xff, 0x63, 0x93, 0xc0, 0xff, 0xb7, 0xcc, 0xdf, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xfd, 0xfc, 0xf9, 0xff, 0xf6, 0xfa, 0xfe, 0xff, 0xfe, 0xfe, 0xfa, 0xff, 0xfa, 0xfd, 0xfa, 0xff, 0xe0, 0xe9, 0xf0, 0xff, 0xcb, 0xdc, 0xe6, 0xff, 0xa8, 0xc9, 0xe0, 0xff, 0xb3, 0xc8, 0xd6, 0xff, 0x8c, 0x93, 0x8a, 0xff, 0x62, 0x65, 0x5d, 0xff, 0x37, 0x3b, 0x34, 0xff, 0x2b, 0x2e, 0x2b, 0xff, 0x15, 0x12, 0x0f, 0xff, 0x07, 0x06, 0x04, 0xff, 0x03, 0x03, 0x02, 0xff, 0x0f, 0x0d, 0x07, 0xff, 0x9c, 0x7e, 0x62, 0xff, 0x75, 0x5e, 0x49, 0xff, 0x02, 0x00, 0x00, 0xff, 0x10, 0x17, 0x1b, 0xff, 0x7f, 0x9e, 0xb6, 0xff, 0x66, 0x84, 0xb3, 0xff, 0x62, 0x76, 0xa4, 0xff, 0x52, 0x6d, 0x9c, 0xff, 0x3e, 0x58, 0x89, 0xff, 0x2a, 0x48, 0x7d, 0xff, 0x19, 0x34, 0x66, 0xff, 0x23, 0x3a, 0x5f, 0xff, 0x1b, 0x2c, 0x46, 0xff, 0x11, 0x1f, 0x2f, 0xff, 0x0c, 0x10, 0x1b, 0xff, 0x08, 0x0a, 0x12, 0xff, 0x04, 0x07, 0x0b, 0xff, 0x06, 0x08, 0x0a, 0xff, 0x07, 0x08, 0x09, 0xff, 0x07, 0x06, 0x08, 0xff, 0x0b, 0x0a, 0x0a, 0xff, 0x16, 0x14, 0x15, 0xff, 0x2d, 0x2a, 0x29, 0xff, 0x42, 0x43, 0x3e, 0xff, 0x78, 0x77, 0x71, 0xff, 0xa7, 0xa3, 0x9e, 0xff, 0xc6, 0xb9, 0xb3, 0xff, 0xe0, 0xcf, 0xba, 0xff, 0xe1, 0xd0, 0xba, 0xff, 0xdd, 0xd0, 0xbd, 0xff, 0xdb, 0xcd, 0xbc, 0xff, 0xd1, 0xc8, 0xb8, 0xff, 0xc6, 0xc3, 0xb5, 0xff, 0xbd, 0xbb, 0xb2, 0xff, 0xb1, 0xb4, 0xad, 0xff, 0xa6, 0xaf, 0xab, 0xff, 0xa0, 0xaa, 0xaa, 0xa1, 0xa1, 0xaa, 0xab, 0x03, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x9b, 0xa2, 0x95, 0x0a, 0x9c, 0xa4, 0x9a, 0xe8, 0x96, 0xa8, 0xab, 0xff, 0x81, 0xaa, 0xc3, 0xff, 0x77, 0xab, 0xd5, 0xff, 0x6c, 0xaa, 0xdb, 0xff, 0x69, 0xad, 0xe1, 0xff, 0x73, 0xae, 0xda, 0xff, 0x76, 0xa2, 0xbc, 0xff, 0x75, 0x93, 0xa1, 0xff, 0x6d, 0x75, 0x74, 0xff, 0x65, 0x61, 0x5a, 0xff, 0x5b, 0x5a, 0x5a, 0xff, 0x65, 0x63, 0x64, 0xff, 0x75, 0x74, 0x77, 0xff, 0x78, 0x79, 0x7e, 0xff, 0x81, 0x8a, 0x9a, 0xff, 0x7a, 0x99, 0xbb, 0xff, 0x8b, 0xaf, 0xda, 0xff, 0x99, 0xbe, 0xe4, 0xff, 0x85, 0xb0, 0xd9, 0xff, 0x7b, 0xa5, 0xca, 0xff, 0xee, 0xf7, 0xfc, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xf6, 0xfc, 0xfc, 0xff, 0xe0, 0xeb, 0xf3, 0xff, 0xde, 0xe7, 0xef, 0xff, 0xda, 0xec, 0xf7, 0xff, 0xd0, 0xe5, 0xf0, 0xff, 0x98, 0xbf, 0xef, 0xff, 0xb0, 0xc9, 0xdb, 0xff, 0xe6, 0xe2, 0xd5, 0xff, 0x8b, 0x8a, 0x72, 0xff, 0x58, 0x57, 0x43, 0xff, 0x26, 0x28, 0x1d, 0xff, 0x0c, 0x10, 0x0d, 0xff, 0x07, 0x09, 0x0a, 0xff, 0x09, 0x0c, 0x0c, 0xff, 0x07, 0x09, 0x08, 0xff, 0x03, 0x06, 0x0a, 0xff, 0x5c, 0x50, 0x42, 0xff, 0x22, 0x1e, 0x18, 0xff, 0x0a, 0x09, 0x09, 0xff, 0x02, 0x08, 0x07, 0xff, 0x97, 0x90, 0xa3, 0xff, 0x8f, 0x91, 0xbd, 0xff, 0x4e, 0x74, 0x9d, 0xff, 0x53, 0x6c, 0x9d, 0xff, 0x44, 0x5f, 0x90, 0xff, 0x36, 0x50, 0x85, 0xff, 0x27, 0x43, 0x7b, 0xff, 0x19, 0x33, 0x62, 0xff, 0x09, 0x1a, 0x38, 0xff, 0x0e, 0x16, 0x23, 0xff, 0x0f, 0x11, 0x1b, 0xff, 0x0a, 0x0c, 0x13, 0xff, 0x05, 0x08, 0x0c, 0xff, 0x06, 0x07, 0x0a, 0xff, 0x0a, 0x0c, 0x0d, 0xff, 0x07, 0x07, 0x09, 0xff, 0x09, 0x08, 0x09, 0xff, 0x21, 0x1f, 0x1f, 0xff, 0x21, 0x1f, 0x1d, 0xff, 0x3f, 0x3f, 0x3b, 0xff, 0x95, 0x90, 0x8a, 0xff, 0xbb, 0xb4, 0xad, 0xff, 0xbc, 0xb3, 0xad, 0xff, 0xd4, 0xc8, 0xb2, 0xff, 0xd7, 0xca, 0xb0, 0xff, 0xd8, 0xca, 0xb5, 0xff, 0xd8, 0xcb, 0xb5, 0xff, 0xd3, 0xc9, 0xb7, 0xff, 0xc5, 0xc4, 0xb4, 0xff, 0xbe, 0xbc, 0xb4, 0xff, 0xb1, 0xb7, 0xb0, 0xff, 0xa8, 0xb2, 0xac, 0xff, 0xa6, 0xaf, 0xab, 0xe8, 0xa8, 0xb1, 0xa9, 0x0a, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xae, 0xae, 0x99, 0x4a, 0xae, 0xb1, 0xa2, 0xff, 0xa9, 0xaf, 0xa9, 0xff, 0x9b, 0xac, 0xaf, 0xff, 0x95, 0xaa, 0xbb, 0xff, 0x90, 0xa7, 0xbc, 0xff, 0x8e, 0xa7, 0xba, 0xff, 0x90, 0xa4, 0xaf, 0xff, 0x94, 0xa0, 0x9d, 0xff, 0x98, 0x9d, 0x91, 0xff, 0x89, 0x88, 0x79, 0xff, 0x73, 0x71, 0x65, 0xff, 0x5d, 0x5b, 0x59, 0xff, 0x60, 0x5f, 0x63, 0xff, 0x6b, 0x6f, 0x75, 0xff, 0x6f, 0x77, 0x81, 0xff, 0x7c, 0x92, 0xa2, 0xff, 0x76, 0xa2, 0xc3, 0xff, 0x8c, 0xb0, 0xd5, 0xff, 0xa4, 0xcc, 0xe4, 0xff, 0xaf, 0xce, 0xde, 0xff, 0xd9, 0xe5, 0xe4, 0xff, 0xdd, 0xdf, 0xce, 0xff, 0xbd, 0xc1, 0xbc, 0xff, 0x8b, 0xb0, 0xae, 0xff, 0x96, 0xb3, 0xea, 0xff, 0xbe, 0xe0, 0xf5, 0xff, 0xad, 0xbd, 0xd1, 0xff, 0x61, 0x85, 0xb9, 0xff, 0x9c, 0xbb, 0xdf, 0xff, 0xf4, 0xf0, 0xd3, 0xff, 0xf9, 0xf1, 0xdb, 0xff, 0xd3, 0xcd, 0xac, 0xff, 0xa5, 0xa0, 0x86, 0xff, 0x6c, 0x6c, 0x5d, 0xff, 0x07, 0x0a, 0x06, 0xff, 0x04, 0x07, 0x07, 0xff, 0x0a, 0x10, 0x0e, 0xff, 0x0a, 0x0e, 0x0f, 0xff, 0x2f, 0x23, 0x16, 0xff, 0x4e, 0x37, 0x24, 0xff, 0x00, 0x04, 0x05, 0xff, 0x08, 0x0d, 0x15, 0xff, 0x01, 0x01, 0x00, 0xff, 0x59, 0x65, 0x7c, 0xff, 0x3f, 0x54, 0x81, 0xff, 0x34, 0x53, 0x7e, 0xff, 0x57, 0x72, 0xa6, 0xff, 0x3f, 0x5d, 0x97, 0xff, 0x3f, 0x56, 0x91, 0xff, 0x34, 0x4d, 0x87, 0xff, 0x20, 0x3c, 0x74, 0xff, 0x0e, 0x20, 0x46, 0xff, 0x0d, 0x10, 0x1e, 0xff, 0x0b, 0x0b, 0x14, 0xff, 0x05, 0x07, 0x0f, 0xff, 0x07, 0x09, 0x0e, 0xff, 0x07, 0x08, 0x0b, 0xff, 0x05, 0x07, 0x0c, 0xff, 0x08, 0x09, 0x0d, 0xff, 0x15, 0x14, 0x16, 0xff, 0x13, 0x12, 0x13, 0xff, 0x38, 0x36, 0x34, 0xff, 0x35, 0x33, 0x33, 0xff, 0x64, 0x5d, 0x56, 0xff, 0xc2, 0xba, 0xac, 0xff, 0xb9, 0xb0, 0xa7, 0xff, 0xcd, 0xc2, 0xad, 0xff, 0xd5, 0xc6, 0xac, 0xff, 0xd3, 0xc3, 0xae, 0xff, 0xd1, 0xc4, 0xaf, 0xff, 0xcc, 0xc2, 0xb1, 0xff, 0xc1, 0xbf, 0xaf, 0xff, 0xb7, 0xb9, 0xb0, 0xff, 0xad, 0xb5, 0xae, 0xff, 0xa8, 0xb2, 0xac, 0xff, 0xa9, 0xb2, 0xaf, 0xff, 0xaa, 0xb2, 0xaf, 0x4a, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0xbc, 0xb7, 0xa2, 0xa8, 0xbb, 0xbb, 0xa8, 0xff, 0xb9, 0xb8, 0xa1, 0xff, 0xad, 0xae, 0x91, 0xff, 0xa9, 0xab, 0x94, 0xff, 0xa8, 0xaa, 0x95, 0xff, 0xa5, 0xaa, 0x96, 0xff, 0xa7, 0xac, 0x9e, 0xff, 0xab, 0xaf, 0xa0, 0xff, 0xa9, 0xad, 0x9d, 0xff, 0x94, 0x9a, 0x87, 0xff, 0x7b, 0x7e, 0x6d, 0xff, 0x69, 0x66, 0x59, 0xff, 0x69, 0x66, 0x67, 0xff, 0x73, 0x78, 0x7b, 0xff, 0x7c, 0x89, 0x93, 0xff, 0x83, 0x9b, 0xaf, 0xff, 0x85, 0xa5, 0xbb, 0xff, 0xdc, 0xe8, 0xe5, 0xff, 0xf5, 0xf8, 0xef, 0xff, 0xfe, 0xfe, 0xfa, 0xff, 0xfb, 0xf9, 0xe5, 0xff, 0x5c, 0x63, 0x4d, 0xff, 0x00, 0x00, 0x00, 0xff, 0x3d, 0x34, 0x32, 0xff, 0x75, 0x89, 0x7d, 0xff, 0x92, 0xbb, 0xd5, 0xff, 0xb9, 0xc7, 0xeb, 0xff, 0xbe, 0xc8, 0xed, 0xff, 0x84, 0xa4, 0xc0, 0xff, 0xf7, 0xed, 0xda, 0xff, 0xc1, 0xbd, 0xab, 0xff, 0x96, 0x96, 0x78, 0xff, 0x86, 0x84, 0x6f, 0xff, 0x4c, 0x4c, 0x46, 0xff, 0x0e, 0x0e, 0x0f, 0xff, 0x07, 0x0c, 0x0b, 0xff, 0x1c, 0x26, 0x24, 0xff, 0x0e, 0x11, 0x12, 0xff, 0x2f, 0x26, 0x1d, 0xff, 0x32, 0x22, 0x18, 0xff, 0x01, 0x0a, 0x0e, 0xff, 0x05, 0x04, 0x04, 0xff, 0x00, 0x00, 0x00, 0xff, 0x3e, 0x51, 0x69, 0xff, 0x32, 0x4e, 0x84, 0xff, 0x50, 0x6a, 0x9c, 0xff, 0x4d, 0x67, 0x9c, 0xff, 0x49, 0x6c, 0xad, 0xff, 0x47, 0x69, 0xa6, 0xff, 0x38, 0x56, 0x8e, 0xff, 0x2d, 0x4b, 0x82, 0xff, 0x1d, 0x37, 0x61, 0xff, 0x0a, 0x15, 0x28, 0xff, 0x0a, 0x0d, 0x16, 0xff, 0x05, 0x06, 0x0e, 0xff, 0x07, 0x09, 0x0e, 0xff, 0x08, 0x08, 0x0c, 0xff, 0x03, 0x06, 0x0b, 0xff, 0x05, 0x08, 0x0d, 0xff, 0x06, 0x08, 0x0a, 0xff, 0x0b, 0x0b, 0x0c, 0xff, 0x16, 0x15, 0x15, 0xff, 0x53, 0x4f, 0x52, 0xff, 0x66, 0x5e, 0x56, 0xff, 0xaf, 0xa2, 0x90, 0xff, 0xc6, 0xb5, 0xa9, 0xff, 0xcd, 0xbf, 0xad, 0xff, 0xdd, 0xc5, 0xac, 0xff, 0xd2, 0xc0, 0xab, 0xff, 0xca, 0xbe, 0xaa, 0xff, 0xc6, 0xbd, 0xab, 0xff, 0xbe, 0xbb, 0xac, 0xff, 0xb0, 0xb5, 0xac, 0xff, 0xa8, 0xb2, 0xaa, 0xff, 0xa6, 0xb1, 0xab, 0xff, 0xa9, 0xb1, 0xae, 0xff, 0xa6, 0xb4, 0xac, 0xa8, 0x00, 0x00, 0x00, 0x00,
    0xbb, 0xb3, 0x98, 0x0b, 0xc0, 0xbc, 0xa5, 0xe1, 0xc3, 0xc0, 0xa9, 0xff, 0xc2, 0xbe, 0xa3, 0xff, 0xb8, 0xb6, 0x93, 0xff, 0xb6, 0xb2, 0x8e, 0xff, 0xb5, 0xb2, 0x94, 0xff, 0xb6, 0xb5, 0x9e, 0xff, 0xb8, 0xb9, 0xa9, 0xff, 0xb9, 0xbd, 0xad, 0xff, 0xb5, 0xba, 0xa7, 0xff, 0xa3, 0xa8, 0x93, 0xff, 0x8b, 0x8c, 0x79, 0xff, 0x74, 0x70, 0x63, 0xff, 0x72, 0x6e, 0x69, 0xff, 0x81, 0x83, 0x89, 0xff, 0x7f, 0x95, 0x9a, 0xff, 0xaf, 0xbb, 0xc4, 0xff, 0xec, 0xf4, 0xed, 0xff, 0xf6, 0xf2, 0xd6, 0xff, 0xfe, 0xf9, 0xea, 0xff, 0xfc, 0xf6, 0xeb, 0xff, 0xfa, 0xf6, 0xe1, 0xff, 0xae, 0xb2, 0x9c, 0xff, 0x4b, 0x4a, 0x39, 0xff, 0x0a, 0x0e, 0x06, 0xff, 0x2c, 0x36, 0x29, 0xff, 0x72, 0x91, 0xba, 0xff, 0xdb, 0xed, 0xf4, 0xff, 0xd0, 0xe2, 0xf6, 0xff, 0x85, 0xb0, 0xe1, 0xff, 0xef, 0xe9, 0xcd, 0xff, 0xcb, 0xcf, 0xb5, 0xff, 0x84, 0x88, 0x6f, 0xff, 0x17, 0x1b, 0x13, 0xff, 0x07, 0x0c, 0x0a, 0xff, 0x10, 0x10, 0x0d, 0xff, 0x0e, 0x0c, 0x0c, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x44, 0x38, 0x28, 0xff, 0x1b, 0x17, 0x10, 0xff, 0x03, 0x06, 0x07, 0xff, 0x03, 0x04, 0x04, 0xff, 0x00, 0x00, 0x00, 0xff, 0x42, 0x58, 0x6e, 0xff, 0x31, 0x49, 0x82, 0xff, 0x44, 0x64, 0x9a, 0xff, 0x55, 0x72, 0xa7, 0xff, 0x48, 0x66, 0x9c, 0xff, 0x4b, 0x6b, 0xa6, 0xff, 0x4b, 0x6a, 0xad, 0xff, 0x34, 0x55, 0x93, 0xff, 0x23, 0x3d, 0x71, 0xff, 0x11, 0x1b, 0x35, 0xff, 0x0c, 0x0f, 0x1a, 0xff, 0x03, 0x09, 0x0e, 0xff, 0x04, 0x0a, 0x0a, 0xff, 0x10, 0x07, 0x0e, 0xff, 0x07, 0x08, 0x0c, 0xff, 0x09, 0x0b, 0x0e, 0xff, 0x09, 0x09, 0x0e, 0xff, 0x0a, 0x0b, 0x0e, 0xff, 0x0e, 0x0e, 0x0e, 0xff, 0x16, 0x14, 0x13, 0xff, 0x59, 0x56, 0x55, 0xff, 0x3e, 0x3a, 0x34, 0xff, 0xb0, 0xa9, 0x9f, 0xff, 0xcd, 0xbb, 0xa6, 0xff, 0xdb, 0xc7, 0xaa, 0xff, 0xd1, 0xc1, 0xaa, 0xff, 0xcc, 0xbe, 0xa7, 0xff, 0xc4, 0xbb, 0xa9, 0xff, 0xbe, 0xb9, 0xaa, 0xff, 0xb1, 0xb5, 0xaa, 0xff, 0xaa, 0xb2, 0xa7, 0xff, 0xa8, 0xb2, 0xab, 0xff, 0xa5, 0xb1, 0xab, 0xff, 0xaa, 0xb3, 0xaa, 0xe1, 0xa9, 0xb1, 0xaa, 0x0b,
    0xc0, 0xb7, 0x9c, 0x29, 0xc4, 0xbf, 0xa6, 0xef, 0xc7, 0xc3, 0xac, 0xff, 0xc5, 0xc0, 0xa8, 0xff, 0xbc, 0xb9, 0x99, 0xff, 0xb9, 0xb4, 0x93, 0xff, 0xbb, 0xb7, 0x9a, 0xff, 0xbf, 0xbd, 0xa7, 0xff, 0xc2, 0xc3, 0xb2, 0xff, 0xc1, 0xc4, 0xb3, 0xff, 0xbc, 0xc0, 0xac, 0xff, 0xad, 0xaf, 0x9a, 0xff, 0x97, 0x96, 0x83, 0xff, 0x7c, 0x79, 0x6a, 0xff, 0x74, 0x72, 0x76, 0xff, 0x84, 0x8d, 0x8d, 0xff, 0x90, 0x9b, 0xa8, 0xff, 0xef, 0xf2, 0xe0, 0xff, 0xf8, 0xf0, 0xd9, 0xff, 0xf9, 0xed, 0xdc, 0xff, 0xf7, 0xf2, 0xe1, 0xff, 0xf5, 0xee, 0xe0, 0xff, 0xf0, 0xe9, 0xd5, 0xff, 0xd6, 0xd2, 0xb3, 0xff, 0xbb, 0xb5, 0x99, 0xff, 0x86, 0x85, 0x72, 0xff, 0x20, 0x31, 0x30, 0xff, 0x6a, 0x8c, 0xaf, 0xff, 0xe8, 0xed, 0xef, 0xff, 0xd6, 0xed, 0xfb, 0xff, 0x84, 0xc0, 0xe3, 0xff, 0x6e, 0x76, 0x65, 0xff, 0xb8, 0xb6, 0x9f, 0xff, 0x50, 0x52, 0x45, 0xff, 0x0a, 0x0d, 0x0a, 0xff, 0x0f, 0x12, 0x10, 0xff, 0x0b, 0x0e, 0x09, 0xff, 0x0c, 0x0f, 0x0d, 0xff, 0x12, 0x16, 0x12, 0xff, 0x22, 0x24, 0x23, 0xff, 0x63, 0x5f, 0x4f, 0xff, 0x1a, 0x18, 0x12, 0xff, 0x0c, 0x0c, 0x10, 0xff, 0x03, 0x05, 0x09, 0xff, 0x0f, 0x0d, 0x0a, 0xff, 0x54, 0x6b, 0x85, 0xff, 0x35, 0x4b, 0x84, 0xff, 0x42, 0x5d, 0x94, 0xff, 0x52, 0x72, 0xa7, 0xff, 0x55, 0x73, 0xa8, 0xff, 0x49, 0x6b, 0xa4, 0xff, 0x3e, 0x5f, 0x9e, 0xff, 0x37, 0x5c, 0x96, 0xff, 0x36, 0x57, 0x92, 0xff, 0x17, 0x29, 0x57, 0xff, 0x06, 0x0c, 0x12, 0xff, 0x0e, 0x0a, 0x17, 0xff, 0x08, 0x05, 0x0f, 0xff, 0x02, 0x0b, 0x0b, 0xff, 0x09, 0x0a, 0x0e, 0xff, 0x0a, 0x0b, 0x0f, 0xff, 0x0a, 0x0b, 0x0f, 0xff, 0x08, 0x09, 0x0d, 0xff, 0x09, 0x0a, 0x0a, 0xff, 0x18, 0x18, 0x17, 0xff, 0x41, 0x40, 0x40, 0xff, 0x5d, 0x59, 0x55, 0xff, 0x70, 0x6e, 0x68, 0xff, 0xcf, 0xc0, 0xae, 0xff, 0xcf, 0xbb, 0xa0, 0xff, 0xcb, 0xbb, 0xa3, 0xff, 0xca, 0xbb, 0xa3, 0xff, 0xc5, 0xbc, 0xa7, 0xff, 0xc0, 0xb8, 0xa6, 0xff, 0xb5, 0xb5, 0xa7, 0xff, 0xb0, 0xb4, 0xa8, 0xff, 0xaa, 0xb3, 0xa9, 0xff, 0xa8, 0xb1, 0xa9, 0xff, 0xa8, 0xaf, 0xa8, 0xef, 0xa9, 0xb1, 0xa8, 0x29,
    0xc3, 0xb8, 0x9c, 0x43, 0xc8, 0xc0, 0xa6, 0xfb, 0xca, 0xc5, 0xae, 0xff, 0xca, 0xc3, 0xab, 0xff, 0xbf, 0xbb, 0x9a, 0xff, 0xbb, 0xb6, 0x95, 0xff, 0xbf, 0xba, 0x9d, 0xff, 0xc4, 0xc0, 0xa9, 0xff, 0xc6, 0xc5, 0xb3, 0xff, 0xc6, 0xc8, 0xb5, 0xff, 0xc3, 0xc5, 0xb1, 0xff, 0xb3, 0xb5, 0xa0, 0xff, 0xa1, 0x9f, 0x8c, 0xff, 0x85, 0x7f, 0x70, 0xff, 0x7c, 0x77, 0x7b, 0xff, 0x83, 0x8e, 0x93, 0xff, 0xd5, 0xdc, 0xde, 0xff, 0xa0, 0x9f, 0x80, 0xff, 0x74, 0x76, 0x5d, 0xff, 0xf1, 0xeb, 0xdc, 0xff, 0xfe, 0xf5, 0xe6, 0xff, 0xf1, 0xea, 0xd2, 0xff, 0xc5, 0xc6, 0xad, 0xff, 0x7a, 0x8a, 0x78, 0xff, 0x79, 0x90, 0x8c, 0xff, 0x2c, 0x32, 0x2d, 0xff, 0x3f, 0x56, 0x58, 0xff, 0xa0, 0xbc, 0xde, 0xff, 0xfb, 0xfc, 0xfd, 0xff, 0xce, 0xe3, 0xfb, 0xff, 0x8a, 0xbc, 0xee, 0xff, 0x46, 0x68, 0x80, 0xff, 0x00, 0x00, 0x00, 0xff, 0x08, 0x07, 0x07, 0xff, 0x08, 0x0a, 0x09, 0xff, 0x06, 0x07, 0x06, 0xff, 0x1d, 0x24, 0x23, 0xff, 0x44, 0x4e, 0x4c, 0xff, 0x32, 0x37, 0x33, 0xff, 0x39, 0x3d, 0x37, 0xff, 0x65, 0x68, 0x5e, 0xff, 0x08, 0x0b, 0x0d, 0xff, 0x0a, 0x09, 0x0b, 0xff, 0x04, 0x05, 0x0b, 0xff, 0x26, 0x2a, 0x34, 0xff, 0x7a, 0x93, 0xbe, 0xff, 0x65, 0x7b, 0xb5, 0xff, 0x5b, 0x75, 0xab, 0xff, 0x4f, 0x6b, 0xa2, 0xff, 0x4e, 0x6b, 0x9e, 0xff, 0x51, 0x6f, 0xa8, 0xff, 0x4f, 0x6d, 0xa8, 0xff, 0x3b, 0x5c, 0x94, 0xff, 0x25, 0x47, 0x82, 0xff, 0x2b, 0x3f, 0x6b, 0xff, 0x2f, 0x35, 0x61, 0xff, 0x15, 0x1c, 0x37, 0xff, 0x03, 0x03, 0x00, 0xff, 0x0d, 0x08, 0x0b, 0xff, 0x0a, 0x0a, 0x10, 0xff, 0x09, 0x09, 0x10, 0xff, 0x0c, 0x0c, 0x12, 0xff, 0x0b, 0x0c, 0x11, 0xff, 0x0b, 0x0e, 0x0e, 0xff, 0x0a, 0x0a, 0x0b, 0xff, 0x2d, 0x2d, 0x2d, 0xff, 0x70, 0x6d, 0x6a, 0xff, 0x55, 0x54, 0x55, 0xff, 0xb5, 0xab, 0x9f, 0xff, 0xd7, 0xc0, 0xa8, 0xff, 0xca, 0xb8, 0x9f, 0xff, 0xc8, 0xb9, 0x9f, 0xff, 0xc9, 0xbc, 0xa4, 0xff, 0xc3, 0xb9, 0xa1, 0xff, 0xb9, 0xb5, 0xa4, 0xff, 0xb4, 0xb3, 0xa3, 0xff, 0xb1, 0xb3, 0xa6, 0xff, 0xac, 0xb1, 0xa5, 0xff, 0xa7, 0xae, 0xa5, 0xfa, 0xa9, 0xaf, 0xa6, 0x43,
    0xc5, 0xb8, 0x9a, 0x68, 0xcc, 0xc2, 0xa7, 0xff, 0xce, 0xc7, 0xaf, 0xff, 0xcd, 0xc5, 0xad, 0xff, 0xc4, 0xbd, 0x9d, 0xff, 0xc1, 0xba, 0x99, 0xff, 0xc2, 0xbd, 0x9f, 0xff, 0xc7, 0xc2, 0xab, 0xff, 0xcb, 0xca, 0xb6, 0xff, 0xcb, 0xcc, 0xb9, 0xff, 0xc6, 0xc8, 0xb5, 0xff, 0xba, 0xba, 0xa5, 0xff, 0xa6, 0xa4, 0x90, 0xff, 0x8c, 0x84, 0x78, 0xff, 0x86, 0x80, 0x77, 0xff, 0x8d, 0x98, 0xa7, 0xff, 0xc7, 0xd6, 0xc3, 0xff, 0x47, 0x40, 0x05, 0xff, 0x26, 0x25, 0x11, 0xff, 0xbd, 0xc8, 0xad, 0xff, 0x84, 0x9b, 0x81, 0xff, 0xe2, 0xe4, 0xd3, 0xff, 0xea, 0xe2, 0xce, 0xff, 0xae, 0xb4, 0x9f, 0xff, 0x1e, 0x27, 0x21, 0xff, 0x00, 0x00, 0x04, 0xff, 0x68, 0x80, 0x84, 0xff, 0xe7, 0xf7, 0xff, 0xff, 0xf7, 0xfb, 0xf6, 0xff, 0xb1, 0xc8, 0xf4, 0xff, 0x64, 0x7b, 0xcc, 0xff, 0x64, 0x83, 0xb3, 0xff, 0x11, 0x10, 0x10, 0xff, 0x0b, 0x09, 0x0d, 0xff, 0x04, 0x05, 0x07, 0xff, 0x07, 0x05, 0x05, 0xff, 0x1c, 0x23, 0x24, 0xff, 0x12, 0x1a, 0x1b, 0xff, 0x27, 0x25, 0x23, 0xff, 0x1f, 0x20, 0x1c, 0xff, 0x15, 0x14, 0x11, 0xff, 0x0d, 0x0e, 0x13, 0xff, 0x06, 0x04, 0x04, 0xff, 0x06, 0x0d, 0x0c, 0xff, 0x51, 0x5b, 0x70, 0xff, 0x8f, 0xab, 0xe1, 0xff, 0x5c, 0x76, 0xb0, 0xff, 0x60, 0x7a, 0xb1, 0xff, 0x64, 0x79, 0xb0, 0xff, 0x5c, 0x70, 0xa5, 0xff, 0x4a, 0x64, 0x9b, 0xff, 0x4a, 0x65, 0xa0, 0xff, 0x44, 0x62, 0x99, 0xff, 0x33, 0x51, 0x86, 0xff, 0x17, 0x28, 0x4b, 0xff, 0x0e, 0x11, 0x1f, 0xff, 0x30, 0x34, 0x63, 0xff, 0x26, 0x2e, 0x57, 0xff, 0x09, 0x0b, 0x16, 0xff, 0x08, 0x08, 0x0d, 0xff, 0x0a, 0x0a, 0x10, 0xff, 0x0f, 0x0f, 0x14, 0xff, 0x0b, 0x0b, 0x12, 0xff, 0x06, 0x09, 0x0d, 0xff, 0x08, 0x0a, 0x0c, 0xff, 0x03, 0x04, 0x04, 0xff, 0x4d, 0x4b, 0x4a, 0xff, 0x3e, 0x3f, 0x42, 0xff, 0x9c, 0x94, 0x8c, 0xff, 0xd9, 0xc2, 0xaa, 0xff, 0xd0, 0xbe, 0xa3, 0xff, 0xcb, 0xbb, 0xa2, 0xff, 0xce, 0xc0, 0xa5, 0xff, 0xc6, 0xb8, 0x9e, 0xff, 0xbd, 0xb6, 0xa2, 0xff, 0xb8, 0xb4, 0xa1, 0xff, 0xb5, 0xb4, 0xa5, 0xff, 0xad, 0xaf, 0xa1, 0xff, 0xa5, 0xaa, 0xa1, 0xff, 0xa7, 0xac, 0xa2, 0x68,
    0xc5, 0xb9, 0x9b, 0x92, 0xcd, 0xc3, 0xa8, 0xff, 0xd0, 0xc8, 0xaf, 0xff, 0xcd, 0xc7, 0xad, 0xff, 0xc9, 0xc0, 0xa2, 0xff, 0xc6, 0xbc, 0x9a, 0xff, 0xc6, 0xbf, 0xa0, 0xff, 0xc9, 0xc3, 0xab, 0xff, 0xcc, 0xc8, 0xb5, 0xff, 0xcb, 0xcc, 0xb8, 0xff, 0xc8, 0xc9, 0xb7, 0xff, 0xbc, 0xbc, 0xa8, 0xff, 0xaa, 0xa7, 0x92, 0xff, 0x8f, 0x88, 0x76, 0xff, 0x7a, 0x74, 0x71, 0xff, 0xb7, 0xd1, 0xd5, 0xff, 0x58, 0x67, 0x60, 0xff, 0x41, 0x3c, 0x1b, 0xff, 0x19, 0x17, 0x0a, 0xff, 0x19, 0x2f, 0x20, 0xff, 0x03, 0x37, 0x2c, 0xff, 0x38, 0x4b, 0x3b, 0xff, 0x8c, 0x8c, 0x77, 0xff, 0x3f, 0x3d, 0x32, 0xff, 0x08, 0x08, 0x0a, 0xff, 0x00, 0x04, 0x02, 0xff, 0xac, 0xc7, 0xda, 0xff, 0xf5, 0xfe, 0xfe, 0xff, 0xe4, 0xef, 0xf7, 0xff, 0xb4, 0xc8, 0xf6, 0xff, 0x85, 0x9a, 0xd6, 0xff, 0x6d, 0x85, 0xb8, 0xff, 0x37, 0x49, 0x64, 0xff, 0x14, 0x13, 0x11, 0xff, 0x1c, 0x1a, 0x1a, 0xff, 0x04, 0x07, 0x0d, 0xff, 0x06, 0x05, 0x08, 0xff, 0x10, 0x10, 0x15, 0xff, 0x00, 0x01, 0x04, 0xff, 0x05, 0x03, 0x05, 0xff, 0x03, 0x02, 0x03, 0xff, 0x03, 0x02, 0x06, 0xff, 0x17, 0x1d, 0x26, 0xff, 0x13, 0x1e, 0x22, 0xff, 0x89, 0x9e, 0xbd, 0xff, 0x8e, 0xa2, 0xd1, 0xff, 0x7c, 0x91, 0xcd, 0xff, 0x54, 0x6f, 0xa8, 0xff, 0x51, 0x68, 0xa5, 0xff, 0x54, 0x6f, 0xa7, 0xff, 0x4f, 0x6d, 0xa4, 0xff, 0x48, 0x67, 0x9e, 0xff, 0x42, 0x5f, 0x94, 0xff, 0x2d, 0x4f, 0x84, 0xff, 0x13, 0x25, 0x46, 0xff, 0x08, 0x0a, 0x13, 0xff, 0x08, 0x08, 0x08, 0xff, 0x16, 0x19, 0x33, 0xff, 0x13, 0x17, 0x27, 0xff, 0x06, 0x09, 0x15, 0xff, 0x06, 0x07, 0x0b, 0xff, 0x0c, 0x10, 0x11, 0xff, 0x05, 0x0a, 0x0f, 0xff, 0x09, 0x06, 0x05, 0xff, 0x05, 0x03, 0x04, 0xff, 0x01, 0x01, 0x00, 0xff, 0x12, 0x14, 0x16, 0xff, 0x37, 0x33, 0x31, 0xff, 0x6a, 0x71, 0x78, 0xff, 0xcd, 0xba, 0xa4, 0xff, 0xcd, 0xbc, 0xa6, 0xff, 0xcc, 0xbf, 0xa5, 0xff, 0xdc, 0xc4, 0xa9, 0xff, 0xca, 0xba, 0x9d, 0xff, 0xb9, 0xb5, 0x9d, 0xff, 0xb3, 0xb2, 0x9d, 0xff, 0xb5, 0xb5, 0xa4, 0xff, 0xae, 0xab, 0x9e, 0xff, 0xa2, 0xa7, 0x9b, 0xff, 0xa6, 0xaa, 0x9e, 0x92,
    0xc7, 0xbb, 0x9d, 0xb5, 0xd0, 0xc6, 0xab, 0xff, 0xd3, 0xcb, 0xb2, 0xff, 0xd0, 0xc9, 0xaf, 0xff, 0xc8, 0xbf, 0xa1, 0xff, 0xc7, 0xbe, 0x9c, 0xff, 0xc8, 0xc0, 0x9f, 0xff, 0xca, 0xc4, 0xa9, 0xff, 0xcd, 0xc8, 0xb3, 0xff, 0xcc, 0xcb, 0xb6, 0xff, 0xc9, 0xc8, 0xb4, 0xff, 0xbd, 0xbb, 0xa5, 0xff, 0xab, 0xa7, 0x90, 0xff, 0x92, 0x8a, 0x77, 0xff, 0x81, 0x76, 0x75, 0xff, 0x99, 0xae, 0xb7, 0xff, 0x64, 0x76, 0x79, 0xff, 0x24, 0x25, 0x13, 0xff, 0x15, 0x15, 0x10, 0xff, 0x15, 0x18, 0x13, 0xff, 0x19, 0x2c, 0x25, 0xff, 0x08, 0x1f, 0x22, 0xff, 0x06, 0x09, 0x0e, 0xff, 0x11, 0x13, 0x12, 0xff, 0x0c, 0x11, 0x14, 0xff, 0x33, 0x43, 0x4c, 0xff, 0xeb, 0xf2, 0xf3, 0xff, 0xf6, 0xfd, 0xfc, 0xff, 0xcf, 0xe2, 0xf8, 0xff, 0xb4, 0xc5, 0xeb, 0xff, 0x87, 0x9d, 0xd2, 0xff, 0x64, 0x7c, 0xb2, 0xff, 0x57, 0x6e, 0x99, 0xff, 0x2b, 0x36, 0x49, 0xff, 0x05, 0x0e, 0x04, 0xff, 0x3c, 0x41, 0x46, 0xff, 0x4f, 0x50, 0x53, 0xff, 0x0c, 0x11, 0x10, 0xff, 0x06, 0x0b, 0x0b, 0xff, 0x08, 0x0e, 0x10, 0xff, 0x09, 0x12, 0x1c, 0xff, 0x0b, 0x17, 0x18, 0xff, 0x05, 0x14, 0x1a, 0xff, 0x7d, 0x7d, 0x9b, 0xff, 0x8d, 0x9d, 0xc3, 0xff, 0x8e, 0x9f, 0xcd, 0xff, 0x7a, 0x8f, 0xc1, 0xff, 0x72, 0x89, 0xbb, 0xff, 0x56, 0x6e, 0xa8, 0xff, 0x53, 0x6d, 0xa5, 0xff, 0x4e, 0x6d, 0xa3, 0xff, 0x46, 0x65, 0x9f, 0xff, 0x46, 0x63, 0x9c, 0xff, 0x3b, 0x59, 0x93, 0xff, 0x21, 0x33, 0x5d, 0xff, 0x09, 0x12, 0x21, 0xff, 0x10, 0x11, 0x14, 0xff, 0x07, 0x0c, 0x13, 0xff, 0x03, 0x04, 0x06, 0xff, 0x04, 0x03, 0x0b, 0xff, 0x01, 0x00, 0x05, 0xff, 0x01, 0x02, 0x05, 0xff, 0x02, 0x03, 0x03, 0xff, 0x0e, 0x12, 0x22, 0xff, 0x2d, 0x3d, 0x5c, 0xff, 0x4b, 0x56, 0x78, 0xff, 0x48, 0x4f, 0x62, 0xff, 0x18, 0x14, 0x13, 0xff, 0x55, 0x56, 0x65, 0xff, 0xc3, 0xb2, 0x9d, 0xff, 0xc8, 0xb6, 0x9e, 0xff, 0xc6, 0xb9, 0x9e, 0xff, 0xd4, 0xbf, 0xa5, 0xff, 0xc9, 0xba, 0x9e, 0xff, 0xbe, 0xb5, 0x9a, 0xff, 0xb7, 0xb2, 0x9a, 0xff, 0xb8, 0xb6, 0xa2, 0xff, 0xae, 0xab, 0x99, 0xff, 0x9c, 0xa2, 0x94, 0xff, 0xa2, 0xa6, 0x99, 0xb5,
    0xc8, 0xbb, 0x9c, 0xd1, 0xce, 0xc4, 0xa9, 0xff, 0xd2, 0xca, 0xb1, 0xff, 0xd1, 0xca, 0xb1, 0xff, 0xc8, 0xbf, 0xa2, 0xff, 0xc7, 0xbe, 0x9c, 0xff, 0xc9, 0xbf, 0x9d, 0xff, 0xcb, 0xc1, 0xa5, 0xff, 0xcd, 0xc7, 0xaf, 0xff, 0xce, 0xcb, 0xb2, 0xff, 0xc9, 0xc7, 0xb0, 0xff, 0xbd, 0xba, 0xa1, 0xff, 0xaa, 0xa4, 0x8b, 0xff, 0x92, 0x89, 0x73, 0xff, 0x84, 0x79, 0x76, 0xff, 0x8c, 0x98, 0xa0, 0xff, 0x6a, 0x78, 0x7b, 0xff, 0x27, 0x28, 0x1f, 0xff, 0x15, 0x1d, 0x1f, 0xff, 0x09, 0x07, 0x08, 0xff, 0x39, 0x49, 0x46, 0xff, 0x4c, 0x6c, 0x69, 0xff, 0x10, 0x17, 0x13, 0xff, 0x0b, 0x0e, 0x0a, 0xff, 0x04, 0x07, 0x06, 0xff, 0x96, 0x98, 0x9c, 0xff, 0xfc, 0xfd, 0xfe, 0xff, 0xd8, 0xe2, 0xf3, 0xff, 0xb1, 0xc7, 0xf7, 0xff, 0xa6, 0xb7, 0xea, 0xff, 0x81, 0x94, 0xcb, 0xff, 0x57, 0x6c, 0xa2, 0xff, 0x41, 0x58, 0x8d, 0xff, 0x29, 0x41, 0x69, 0xff, 0x44, 0x52, 0x62, 0xff, 0x29, 0x38, 0x43, 0xff, 0x22, 0x2e, 0x32, 0xff, 0x1a, 0x27, 0x2f, 0xff, 0x26, 0x36, 0x3d, 0xff, 0x3a, 0x47, 0x4d, 0xff, 0x42, 0x55, 0x63, 0xff, 0x68, 0x78, 0x89, 0xff, 0x89, 0x9b, 0xb4, 0xff, 0x9a, 0xa7, 0xce, 0xff, 0x8c, 0x9b, 0xc9, 0xff, 0x7c, 0x8d, 0xbc, 0xff, 0x71, 0x85, 0xb7, 0xff, 0x68, 0x7e, 0xb2, 0xff, 0x54, 0x6e, 0xa4, 0xff, 0x51, 0x6c, 0xa4, 0xff, 0x4d, 0x6a, 0xa1, 0xff, 0x45, 0x66, 0xa0, 0xff, 0x45, 0x64, 0x9c, 0xff, 0x40, 0x5f, 0x97, 0xff, 0x2b, 0x42, 0x6f, 0xff, 0x13, 0x20, 0x37, 0xff, 0x11, 0x12, 0x18, 0xff, 0x08, 0x0a, 0x0f, 0xff, 0x05, 0x06, 0x0b, 0xff, 0x03, 0x06, 0x08, 0xff, 0x08, 0x10, 0x22, 0xff, 0x12, 0x1c, 0x3f, 0xff, 0x22, 0x2a, 0x57, 0xff, 0x31, 0x4c, 0x82, 0xff, 0x41, 0x60, 0x9e, 0xff, 0x41, 0x5b, 0xa0, 0xff, 0x6d, 0x81, 0xc2, 0xff, 0x58, 0x60, 0x7c, 0xff, 0x40, 0x42, 0x40, 0xff, 0xce, 0xb9, 0x95, 0xff, 0xcf, 0xb6, 0x9b, 0xff, 0xc4, 0xb7, 0x9c, 0xff, 0xcc, 0xba, 0xa1, 0xff, 0xc6, 0xb6, 0x9c, 0xff, 0xc0, 0xb5, 0x9c, 0xff, 0xb7, 0xb3, 0x9c, 0xff, 0xb3, 0xb3, 0xa0, 0xff, 0xab, 0xab, 0x9b, 0xff, 0x9e, 0xa1, 0x91, 0xff, 0x9f, 0xa2, 0x93, 0xd1,
    0xc7, 0xba, 0x9b, 0xe8, 0xce, 0xc4, 0xa9, 0xff, 0xd2, 0xc9, 0xb1, 0xff, 0xd0, 0xca, 0xb0, 0xff, 0xca, 0xc2, 0xa4, 0xff, 0xc7, 0xbc, 0x9a, 0xff, 0xc6, 0xbc, 0x99, 0xff, 0xca, 0xc0, 0xa2, 0xff, 0xcd, 0xc5, 0xac, 0xff, 0xce, 0xc8, 0xb0, 0xff, 0xca, 0xc6, 0xad, 0xff, 0xbb, 0xb7, 0x9b, 0xff, 0xa9, 0xa2, 0x87, 0xff, 0x93, 0x86, 0x70, 0xff, 0x84, 0x79, 0x73, 0xff, 0x93, 0x9a, 0x9c, 0xff, 0x6c, 0x75, 0x74, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0b, 0x15, 0x1b, 0xff, 0x16, 0x1f, 0x20, 0xff, 0x31, 0x4b, 0x4b, 0xff, 0x6a, 0x91, 0x8d, 0xff, 0x00, 0x00, 0x00, 0xff, 0x6e, 0x75, 0x60, 0xff, 0x1e, 0x16, 0x2f, 0xff, 0xf3, 0xf4, 0xf4, 0xff, 0xec, 0xf3, 0xfa, 0xff, 0xb2, 0xcc, 0xf5, 0xff, 0x7e, 0x95, 0xdb, 0xff, 0x64, 0x76, 0xbe, 0xff, 0x63, 0x74, 0xb1, 0xff, 0x7a, 0x8c, 0xc1, 0xff, 0x88, 0x9a, 0xce, 0xff, 0x67, 0x75, 0xa4, 0xff, 0x23, 0x35, 0x69, 0xff, 0x37, 0x52, 0x81, 0xff, 0x55, 0x74, 0x9f, 0xff, 0x68, 0x87, 0xb3, 0xff, 0x7b, 0x97, 0xc1, 0xff, 0x91, 0xac, 0xd0, 0xff, 0x95, 0xac, 0xd3, 0xff, 0x92, 0xa8, 0xd8, 0xff, 0x7e, 0x92, 0xca, 0xff, 0x72, 0x87, 0xbc, 0xff, 0x6e, 0x80, 0xb1, 0xff, 0x6a, 0x7d, 0xaf, 0xff, 0x5f, 0x72, 0xa9, 0xff, 0x5b, 0x6e, 0xa9, 0xff, 0x4d, 0x68, 0x9c, 0xff, 0x4b, 0x66, 0x9d, 0xff, 0x4a, 0x68, 0x9f, 0xff, 0x45, 0x65, 0xa1, 0xff, 0x42, 0x61, 0x98, 0xff, 0x43, 0x61, 0x95, 0xff, 0x35, 0x4f, 0x77, 0xff, 0x22, 0x2f, 0x49, 0xff, 0x07, 0x0a, 0x10, 0xff, 0x04, 0x07, 0x17, 0xff, 0x08, 0x15, 0x2d, 0xff, 0x15, 0x26, 0x50, 0xff, 0x22, 0x38, 0x6b, 0xff, 0x26, 0x41, 0x7a, 0xff, 0x26, 0x41, 0x79, 0xff, 0x38, 0x4f, 0x85, 0xff, 0x3d, 0x51, 0x87, 0xff, 0x2f, 0x3f, 0x7e, 0xff, 0x2b, 0x3c, 0x94, 0xff, 0x5a, 0x68, 0xb2, 0xff, 0x61, 0x6e, 0x76, 0xff, 0xd8, 0xba, 0x9c, 0xff, 0xd1, 0xb6, 0x9d, 0xff, 0xc6, 0xb8, 0x9c, 0xff, 0xc8, 0xb9, 0x9e, 0xff, 0xc2, 0xb2, 0x9a, 0xff, 0xba, 0xb0, 0x9c, 0xff, 0xb1, 0xae, 0x9c, 0xff, 0xaa, 0xac, 0x9d, 0xff, 0xa1, 0xa4, 0x97, 0xff, 0x98, 0x9c, 0x8c, 0xff, 0x9d, 0xa1, 0x90, 0xe7,
    0xc6, 0xba, 0x99, 0xf5, 0xcd, 0xc4, 0xa8, 0xff, 0xd2, 0xc8, 0xb0, 0xff, 0xd0, 0xc9, 0xb0, 0xff, 0xc9, 0xc3, 0xa2, 0xff, 0xc7, 0xba, 0x93, 0xff, 0xc6, 0xb9, 0x95, 0xff, 0xc7, 0xbd, 0x9d, 0xff, 0xcd, 0xc4, 0xa6, 0xff, 0xcf, 0xc6, 0xad, 0xff, 0xc9, 0xc4, 0xa9, 0xff, 0xbd, 0xb7, 0x96, 0xff, 0xa9, 0x9f, 0x7f, 0xff, 0x93, 0x84, 0x6e, 0xff, 0x87, 0x78, 0x75, 0xff, 0x91, 0x94, 0x9d, 0xff, 0x84, 0x8f, 0x91, 0xff, 0x13, 0x1a, 0x08, 0xff, 0x34, 0x2e, 0x29, 0xff, 0x19, 0x17, 0x17, 0xff, 0x3e, 0x5e, 0x62, 0xff, 0x3a, 0x57, 0x62, 0xff, 0x59, 0x60, 0x4d, 0xff, 0xb7, 0xd1, 0x9d, 0xff, 0xab, 0xb3, 0xc5, 0xff, 0xdc, 0xed, 0xfa, 0xff, 0xa8, 0xc1, 0xf8, 0xff, 0x7c, 0x96, 0xd9, 0xff, 0x5a, 0x6a, 0xbb, 0xff, 0x55, 0x6d, 0xaf, 0xff, 0x4d, 0x65, 0xad, 0xff, 0x5f, 0x78, 0xb6, 0xff, 0x6d, 0x87, 0xba, 0xff, 0x6c, 0x86, 0xc2, 0xff, 0x4f, 0x63, 0x9c, 0xff, 0x20, 0x2f, 0x65, 0xff, 0x39, 0x4e, 0x83, 0xff, 0x5a, 0x75, 0xaa, 0xff, 0x79, 0x95, 0xc9, 0xff, 0x8e, 0xa6, 0xd2, 0xff, 0x8a, 0x9d, 0xd1, 0xff, 0x75, 0x8c, 0xc3, 0xff, 0x6a, 0x82, 0xbb, 0xff, 0x62, 0x75, 0xaf, 0xff, 0x53, 0x67, 0x9f, 0xff, 0x47, 0x5b, 0x92, 0xff, 0x44, 0x57, 0x8e, 0xff, 0x45, 0x59, 0x8f, 0xff, 0x44, 0x5b, 0x93, 0xff, 0x44, 0x60, 0x94, 0xff, 0x42, 0x5f, 0x96, 0xff, 0x3a, 0x58, 0x93, 0xff, 0x3b, 0x5b, 0x94, 0xff, 0x42, 0x5c, 0x8d, 0xff, 0x2c, 0x40, 0x63, 0xff, 0x22, 0x2c, 0x48, 0xff, 0x0a, 0x11, 0x1b, 0xff, 0x19, 0x28, 0x51, 0xff, 0x28, 0x40, 0x78, 0xff, 0x2c, 0x43, 0x77, 0xff, 0x30, 0x45, 0x7e, 0xff, 0x2b, 0x41, 0x7b, 0xff, 0x30, 0x47, 0x7b, 0xff, 0x3c, 0x49, 0x73, 0xff, 0x29, 0x3e, 0x76, 0xff, 0x1a, 0x2b, 0x68, 0xff, 0x21, 0x2f, 0x6f, 0xff, 0x2d, 0x39, 0x93, 0xff, 0x70, 0x7c, 0xa9, 0xff, 0xd1, 0xb8, 0x94, 0xff, 0xc9, 0xb7, 0x9d, 0xff, 0xc6, 0xb5, 0x9a, 0xff, 0xc5, 0xb7, 0x9e, 0xff, 0xba, 0xaf, 0x97, 0xff, 0xb3, 0xab, 0x99, 0xff, 0xab, 0xaa, 0x9a, 0xff, 0xa3, 0xa8, 0x9b, 0xff, 0x99, 0x9c, 0x92, 0xff, 0x93, 0x99, 0x8a, 0xff, 0x95, 0x99, 0x89, 0xf5,
    0xc2, 0xb7, 0x95, 0xfc, 0xc9, 0xc1, 0xa4, 0xff, 0xcf, 0xc7, 0xad, 0xff, 0xd0, 0xc7, 0xac, 0xff, 0xc8, 0xc0, 0x9e, 0xff, 0xc4, 0xb7, 0x8f, 0xff, 0xc5, 0xb8, 0x90, 0xff, 0xc7, 0xbb, 0x98, 0xff, 0xcb, 0xc0, 0xa1, 0xff, 0xcd, 0xc4, 0xa8, 0xff, 0xca, 0xc1, 0xa4, 0xff, 0xbd, 0xb4, 0x90, 0xff, 0xa7, 0x9c, 0x7c, 0xff, 0x92, 0x83, 0x6d, 0xff, 0x85, 0x78, 0x73, 0xff, 0x8c, 0x8b, 0x91, 0xff, 0xae, 0xb7, 0xbc, 0xff, 0x34, 0x3d, 0x3c, 0xff, 0x35, 0x33, 0x21, 0xff, 0x19, 0x13, 0x0b, 0xff, 0x2c, 0x48, 0x46, 0xff, 0x32, 0x5b, 0x48, 0xff, 0x91, 0xa6, 0xa6, 0xff, 0x9f, 0xb5, 0xc5, 0xff, 0xc1, 0xd6, 0xdd, 0xff, 0xc5, 0xe1, 0xf3, 0xff, 0x7e, 0x94, 0xd8, 0xff, 0x7d, 0x8f, 0xd5, 0xff, 0x72, 0x81, 0xbf, 0xff, 0x75, 0x7e, 0xc8, 0xff, 0x71, 0x7f, 0xb6, 0xff, 0x75, 0x87, 0xc9, 0xff, 0x5f, 0x70, 0xc7, 0xff, 0x4d, 0x69, 0xae, 0xff, 0x4c, 0x61, 0xa2, 0xff, 0x23, 0x30, 0x6b, 0xff, 0x38, 0x49, 0x7e, 0xff, 0x36, 0x4c, 0x81, 0xff, 0x58, 0x70, 0xa7, 0xff, 0x85, 0x9d, 0xd3, 0xff, 0x96, 0xa9, 0xd7, 0xff, 0x9d, 0xb1, 0xde, 0xff, 0x83, 0x99, 0xca, 0xff, 0x59, 0x6e, 0xaa, 0xff, 0x4a, 0x61, 0x92, 0xff, 0x41, 0x58, 0x89, 0xff, 0x3e, 0x55, 0x86, 0xff, 0x35, 0x4c, 0x7d, 0xff, 0x35, 0x48, 0x7c, 0xff, 0x38, 0x4f, 0x80, 0xff, 0x3f, 0x58, 0x8b, 0xff, 0x38, 0x56, 0x8c, 0xff, 0x39, 0x59, 0x8e, 0xff, 0x36, 0x4f, 0x7d, 0xff, 0x28, 0x36, 0x5a, 0xff, 0x24, 0x32, 0x50, 0xff, 0x11, 0x20, 0x32, 0xff, 0x2b, 0x42, 0x75, 0xff, 0x26, 0x44, 0x7f, 0xff, 0x2f, 0x45, 0x7a, 0xff, 0x2f, 0x42, 0x7d, 0xff, 0x3d, 0x52, 0x8b, 0xff, 0x17, 0x27, 0x58, 0xff, 0x0d, 0x1c, 0x48, 0xff, 0x28, 0x40, 0x72, 0xff, 0x30, 0x4b, 0x80, 0xff, 0x11, 0x28, 0x69, 0xff, 0x17, 0x22, 0x6d, 0xff, 0x55, 0x5b, 0xb0, 0xff, 0xcb, 0xba, 0x9c, 0xff, 0xcb, 0xb3, 0x9c, 0xff, 0xc4, 0xb3, 0x99, 0xff, 0xc0, 0xb3, 0x9d, 0xff, 0xb6, 0xac, 0x99, 0xff, 0xb0, 0xa9, 0x9a, 0xff, 0xa9, 0xa9, 0x9d, 0xff, 0x9f, 0xa5, 0x9b, 0xff, 0x93, 0x98, 0x91, 0xff, 0x8a, 0x91, 0x84, 0xff, 0x8b, 0x90, 0x83, 0xfc,
    0xc1, 0xb1, 0x8e, 0xfc, 0xc6, 0xb9, 0x9c, 0xff, 0xca, 0xc1, 0xa6, 0xff, 0xca, 0xc1, 0xa5, 0xff, 0xc1, 0xb9, 0x96, 0xff, 0xc2, 0xb5, 0x8b, 0xff, 0xc5, 0xb6, 0x8b, 0xff, 0xc8, 0xba, 0x92, 0xff, 0xc8, 0xbc, 0x99, 0xff, 0xca, 0xbf, 0xa0, 0xff, 0xc9, 0xbd, 0x9c, 0xff, 0xbd, 0xb2, 0x8b, 0xff, 0xa9, 0x9d, 0x7b, 0xff, 0x94, 0x84, 0x6d, 0xff, 0x85, 0x79, 0x72, 0xff, 0x8e, 0x8b, 0x8f, 0xff, 0xa0, 0xa9, 0xb0, 0xff, 0xa3, 0xaf, 0xb1, 0xff, 0x4c, 0x55, 0x54, 0xff, 0x39, 0x4b, 0x4e, 0xff, 0x83, 0x9d, 0xa0, 0xff, 0xad, 0xc7, 0xd4, 0xff, 0xb7, 0xd1, 0xde, 0xff, 0x9b, 0xbb, 0xcf, 0xff, 0xc1, 0xcb, 0xea, 0xff, 0xde, 0xe5, 0xf7, 0xff, 0x94, 0x9f, 0xd5, 0xff, 0x88, 0x99, 0xcf, 0xff, 0x76, 0x8a, 0xc5, 0xff, 0x1e, 0x21, 0x4e, 0xff, 0x1b, 0x1d, 0x43, 0xff, 0x1e, 0x23, 0x3d, 0xff, 0x63, 0x72, 0xad, 0xff, 0x63, 0x77, 0xb3, 0xff, 0x37, 0x44, 0x7a, 0xff, 0x1f, 0x26, 0x5c, 0xff, 0x29, 0x32, 0x63, 0xff, 0x33, 0x40, 0x73, 0xff, 0x27, 0x37, 0x6f, 0xff, 0x59, 0x6c, 0xa1, 0xff, 0x90, 0xa8, 0xd8, 0xff, 0xa0, 0xb4, 0xdc, 0xff, 0x8e, 0x9f, 0xc8, 0xff, 0x67, 0x77, 0xad, 0xff, 0x4e, 0x63, 0x92, 0xff, 0x44, 0x59, 0x87, 0xff, 0x3a, 0x50, 0x7e, 0xff, 0x37, 0x4b, 0x7a, 0xff, 0x2d, 0x3c, 0x6c, 0xff, 0x29, 0x3c, 0x6a, 0xff, 0x32, 0x46, 0x76, 0xff, 0x37, 0x4e, 0x80, 0xff, 0x38, 0x52, 0x83, 0xff, 0x2d, 0x45, 0x71, 0xff, 0x27, 0x39, 0x5b, 0xff, 0x23, 0x31, 0x4c, 0xff, 0x13, 0x23, 0x38, 0xff, 0x32, 0x4c, 0x80, 0xff, 0x30, 0x50, 0x7e, 0xff, 0x31, 0x48, 0x79, 0xff, 0x24, 0x39, 0x6e, 0xff, 0x2e, 0x44, 0x77, 0xff, 0x2c, 0x42, 0x71, 0xff, 0x2e, 0x46, 0x77, 0xff, 0x43, 0x5d, 0x8e, 0xff, 0x50, 0x6c, 0xa2, 0xff, 0x1e, 0x39, 0x75, 0xff, 0x17, 0x1d, 0x6c, 0xff, 0x37, 0x46, 0x97, 0xff, 0xc1, 0xaf, 0xa9, 0xff, 0xcb, 0xb2, 0x95, 0xff, 0xc2, 0xaf, 0x98, 0xff, 0xbb, 0xae, 0x9c, 0xff, 0xb2, 0xaa, 0x9b, 0xff, 0xaf, 0xaa, 0x9d, 0xff, 0xa8, 0xab, 0x9f, 0xff, 0x9d, 0xa5, 0x9d, 0xff, 0x93, 0x9a, 0x95, 0xff, 0x89, 0x8f, 0x86, 0xff, 0x85, 0x8b, 0x7f, 0xfc,
    0xbc, 0xa9, 0x86, 0xf5, 0xc2, 0xb3, 0x94, 0xff, 0xc4, 0xba, 0x9b, 0xff, 0xc1, 0xb7, 0x97, 0xff, 0xbb, 0xb0, 0x8b, 0xff, 0xbd, 0xaf, 0x87, 0xff, 0xc1, 0xb0, 0x87, 0xff, 0xc2, 0xb3, 0x8b, 0xff, 0xc4, 0xb5, 0x8f, 0xff, 0xc6, 0xb9, 0x95, 0xff, 0xc2, 0xb7, 0x94, 0xff, 0xb9, 0xae, 0x86, 0xff, 0xaa, 0x9d, 0x7b, 0xff, 0x92, 0x83, 0x6b, 0xff, 0x86, 0x79, 0x72, 0xff, 0x8f, 0x8c, 0x8d, 0xff, 0x9d, 0xa3, 0xaa, 0xff, 0xa9, 0xb3, 0xb8, 0xff, 0xc0, 0xd1, 0xd3, 0xff, 0xc1, 0xd4, 0xe2, 0xff, 0xce, 0xda, 0xf1, 0xff, 0xb5, 0xc0, 0xe1, 0xff, 0x9b, 0xa8, 0xd5, 0xff, 0x87, 0x9b, 0xd4, 0xff, 0x93, 0xab, 0xd2, 0xff, 0x9d, 0xb0, 0xe8, 0xff, 0x84, 0x8e, 0xdc, 0xff, 0x99, 0xaa, 0xe1, 0xff, 0x75, 0x84, 0xc7, 0xff, 0x4d, 0x57, 0xa2, 0xff, 0x5a, 0x5b, 0xa3, 0xff, 0x30, 0x34, 0x6d, 0xff, 0x18, 0x23, 0x4e, 0xff, 0x16, 0x1e, 0x47, 0xff, 0x13, 0x19, 0x46, 0xff, 0x28, 0x2d, 0x5e, 0xff, 0x23, 0x29, 0x56, 0xff, 0x37, 0x43, 0x6d, 0xff, 0x7f, 0x90, 0xbf, 0xff, 0x38, 0x45, 0x6e, 0xff, 0x48, 0x5b, 0x86, 0xff, 0x80, 0x98, 0xc4, 0xff, 0x80, 0x95, 0xc0, 0xff, 0x6d, 0x7c, 0xab, 0xff, 0x5a, 0x6e, 0x9d, 0xff, 0x3f, 0x51, 0x7f, 0xff, 0x38, 0x48, 0x74, 0xff, 0x2f, 0x3e, 0x6a, 0xff, 0x2c, 0x3b, 0x69, 0xff, 0x2f, 0x40, 0x6e, 0xff, 0x2f, 0x42, 0x72, 0xff, 0x34, 0x4a, 0x7c, 0xff, 0x32, 0x4a, 0x7b, 0xff, 0x31, 0x46, 0x72, 0xff, 0x26, 0x37, 0x5c, 0xff, 0x25, 0x33, 0x54, 0xff, 0x1a, 0x24, 0x43, 0xff, 0x3b, 0x52, 0x86, 0xff, 0x41, 0x5b, 0x8a, 0xff, 0x58, 0x70, 0x9f, 0xff, 0x39, 0x51, 0x81, 0xff, 0x28, 0x3a, 0x65, 0xff, 0x16, 0x22, 0x45, 0xff, 0x47, 0x5d, 0x8d, 0xff, 0x4a, 0x65, 0x9a, 0xff, 0x55, 0x6d, 0xa5, 0xff, 0x2d, 0x47, 0x83, 0xff, 0x21, 0x24, 0x6d, 0xff, 0x22, 0x36, 0x86, 0xff, 0xb2, 0xa4, 0xa1, 0xff, 0xcc, 0xb4, 0x99, 0xff, 0xc0, 0xaf, 0x99, 0xff, 0xbb, 0xaf, 0x9e, 0xff, 0xb3, 0xad, 0x9e, 0xff, 0xae, 0xac, 0xa1, 0xff, 0xa7, 0xab, 0xa1, 0xff, 0x9e, 0xa6, 0xa0, 0xff, 0x97, 0x9d, 0x99, 0xff, 0x8c, 0x93, 0x8a, 0xff, 0x84, 0x8a, 0x7f, 0xf5,
    0xaf, 0x9b, 0x77, 0xe8, 0xb7, 0xa7, 0x85, 0xff, 0xba, 0xad, 0x8c, 0xff, 0xba, 0xad, 0x8a, 0xff, 0xb8, 0xa8, 0x83, 0xff, 0xb8, 0xa6, 0x80, 0xff, 0xbb, 0xa8, 0x81, 0xff, 0xc0, 0xad, 0x87, 0xff, 0xc2, 0xb0, 0x8b, 0xff, 0xc5, 0xb4, 0x8e, 0xff, 0xbc, 0xb0, 0x8d, 0xff, 0xb4, 0xa7, 0x80, 0xff, 0xa7, 0x97, 0x76, 0xff, 0x90, 0x81, 0x6a, 0xff, 0x85, 0x78, 0x72, 0xff, 0x8e, 0x8c, 0x8c, 0xff, 0x9e, 0xa2, 0xa6, 0xff, 0xa8, 0xaf, 0xb2, 0xff, 0x9b, 0xa5, 0xa7, 0xff, 0xdb, 0xdf, 0xe8, 0xff, 0xe9, 0xed, 0xf8, 0xff, 0xeb, 0xed, 0xf7, 0xff, 0xc2, 0xce, 0xf1, 0xff, 0x6e, 0x8d, 0xd2, 0xff, 0x5a, 0x7c, 0xc1, 0xff, 0xbf, 0xd5, 0xef, 0xff, 0x99, 0xa4, 0xd0, 0xff, 0xa1, 0xb2, 0xd4, 0xff, 0x98, 0xa6, 0xd9, 0xff, 0x6a, 0x78, 0xb5, 0xff, 0x2d, 0x37, 0x62, 0xff, 0x0f, 0x17, 0x35, 0xff, 0x0e, 0x15, 0x33, 0xff, 0x16, 0x1c, 0x42, 0xff, 0x2b, 0x33, 0x58, 0xff, 0x32, 0x39, 0x60, 0xff, 0x33, 0x39, 0x63, 0xff, 0x1b, 0x23, 0x4f, 0xff, 0x56, 0x63, 0x8d, 0xff, 0x7b, 0x85, 0xb5, 0xff, 0x3b, 0x48, 0x70, 0xff, 0x41, 0x58, 0x84, 0xff, 0x57, 0x70, 0x9e, 0xff, 0x62, 0x7a, 0xa8, 0xff, 0x57, 0x6c, 0x9b, 0xff, 0x4a, 0x5d, 0x8a, 0xff, 0x34, 0x43, 0x6e, 0xff, 0x30, 0x3e, 0x67, 0xff, 0x2b, 0x3c, 0x66, 0xff, 0x28, 0x3a, 0x66, 0xff, 0x2b, 0x3f, 0x6d, 0xff, 0x2f, 0x47, 0x78, 0xff, 0x36, 0x4e, 0x80, 0xff, 0x30, 0x43, 0x6f, 0xff, 0x2c, 0x3a, 0x63, 0xff, 0x28, 0x38, 0x59, 0xff, 0x19, 0x25, 0x46, 0xff, 0x36, 0x4d, 0x7f, 0xff, 0x40, 0x5a, 0x8c, 0xff, 0x49, 0x62, 0x93, 0xff, 0x56, 0x73, 0xab, 0xff, 0x21, 0x36, 0x63, 0xff, 0x03, 0x02, 0x1d, 0xff, 0x03, 0x10, 0x39, 0xff, 0x30, 0x45, 0x79, 0xff, 0x4b, 0x62, 0x96, 0xff, 0x39, 0x53, 0x93, 0xff, 0x24, 0x28, 0x6f, 0xff, 0x38, 0x46, 0x98, 0xff, 0xb9, 0xad, 0xa6, 0xff, 0xcd, 0xb7, 0xa0, 0xff, 0xc5, 0xb5, 0xa0, 0xff, 0xc0, 0xb5, 0xa4, 0xff, 0xba, 0xb4, 0xa4, 0xff, 0xb4, 0xb2, 0xa8, 0xff, 0xab, 0xaf, 0xa4, 0xff, 0xa2, 0xab, 0xa3, 0xff, 0x9c, 0xa2, 0x9d, 0xff, 0x90, 0x97, 0x8f, 0xff, 0x84, 0x8c, 0x80, 0xe7,
    0xa2, 0x8c, 0x71, 0xd1, 0xaa, 0x97, 0x7d, 0xff, 0xaf, 0xa0, 0x83, 0xff, 0xaf, 0xa0, 0x83, 0xff, 0xad, 0x9b, 0x7c, 0xff, 0xad, 0x99, 0x78, 0xff, 0xb0, 0x9c, 0x79, 0xff, 0xb6, 0xa2, 0x7e, 0xff, 0xba, 0xa7, 0x85, 0xff, 0xbd, 0xaa, 0x89, 0xff, 0xb6, 0xa6, 0x88, 0xff, 0xae, 0x9e, 0x7c, 0xff, 0x9e, 0x8c, 0x71, 0xff, 0x8a, 0x7a, 0x6a, 0xff, 0x80, 0x74, 0x72, 0xff, 0x89, 0x86, 0x89, 0xff, 0x99, 0x9c, 0xa0, 0xff, 0xa6, 0xa8, 0xad, 0xff, 0xa5, 0xac, 0xaf, 0xff, 0xf0, 0xf6, 0xf7, 0xff, 0xed, 0xf8, 0xfd, 0xff, 0xe4, 0xed, 0xfd, 0xff, 0x9d, 0xb3, 0xee, 0xff, 0x56, 0x79, 0xce, 0xff, 0x7e, 0x90, 0xc8, 0xff, 0xdf, 0xef, 0xfb, 0xff, 0xd6, 0xe1, 0xf7, 0xff, 0xa7, 0xb0, 0xd0, 0xff, 0x91, 0x9c, 0xc7, 0xff, 0x6e, 0x7b, 0xaa, 0xff, 0x35, 0x3f, 0x6b, 0xff, 0x27, 0x2c, 0x54, 0xff, 0x1f, 0x24, 0x48, 0xff, 0x2a, 0x34, 0x56, 0xff, 0x38, 0x42, 0x62, 0xff, 0x2e, 0x39, 0x58, 0xff, 0x37, 0x40, 0x60, 0xff, 0x2a, 0x31, 0x50, 0xff, 0x1b, 0x21, 0x43, 0xff, 0x34, 0x3a, 0x5c, 0xff, 0x4c, 0x59, 0x7b, 0xff, 0x46, 0x56, 0x7b, 0xff, 0x45, 0x5b, 0x84, 0xff, 0x44, 0x61, 0x8c, 0xff, 0x44, 0x59, 0x86, 0xff, 0x4e, 0x63, 0x8e, 0xff, 0x36, 0x46, 0x6e, 0xff, 0x2d, 0x3c, 0x62, 0xff, 0x29, 0x3a, 0x60, 0xff, 0x29, 0x3b, 0x64, 0xff, 0x26, 0x3a, 0x66, 0xff, 0x2d, 0x43, 0x70, 0xff, 0x36, 0x4d, 0x7b, 0xff, 0x33, 0x45, 0x70, 0xff, 0x24, 0x35, 0x5c, 0xff, 0x20, 0x31, 0x54, 0xff, 0x26, 0x31, 0x55, 0xff, 0x35, 0x4d, 0x82, 0xff, 0x36, 0x50, 0x86, 0xff, 0x4d, 0x64, 0x99, 0xff, 0x48, 0x61, 0x96, 0xff, 0x1a, 0x2b, 0x5b, 0xff, 0x0a, 0x10, 0x33, 0xff, 0x09, 0x10, 0x3d, 0xff, 0x10, 0x1e, 0x50, 0xff, 0x47, 0x59, 0x8d, 0xff, 0x34, 0x4c, 0x8e, 0xff, 0x3c, 0x45, 0x8b, 0xff, 0x55, 0x5e, 0xa0, 0xff, 0xcb, 0xb6, 0xab, 0xff, 0xcd, 0xba, 0xa1, 0xff, 0xc8, 0xb7, 0xa2, 0xff, 0xc2, 0xb7, 0xa5, 0xff, 0xbd, 0xb7, 0xa7, 0xff, 0xb6, 0xb4, 0xaa, 0xff, 0xaf, 0xb2, 0xa9, 0xff, 0xa6, 0xad, 0xa7, 0xff, 0x9f, 0xa5, 0xa0, 0xff, 0x95, 0x9c, 0x95, 0xff, 0x89, 0x92, 0x88, 0xd1,
    0x8f, 0x7d, 0x69, 0xb5, 0x98, 0x88, 0x74, 0xff, 0x9d, 0x8f, 0x7c, 0xff, 0x9b, 0x8c, 0x79, 0xff, 0x9a, 0x88, 0x73, 0xff, 0x9a, 0x88, 0x6f, 0xff, 0x9e, 0x8b, 0x6f, 0xff, 0xa5, 0x93, 0x76, 0xff, 0xab, 0x9a, 0x7e, 0xff, 0xae, 0x9e, 0x84, 0xff, 0xa9, 0x99, 0x81, 0xff, 0x9d, 0x8e, 0x73, 0xff, 0x8e, 0x7f, 0x6d, 0xff, 0x81, 0x74, 0x70, 0xff, 0x7d, 0x72, 0x71, 0xff, 0x80, 0x7d, 0x82, 0xff, 0x91, 0x93, 0x98, 0xff, 0x9f, 0xa0, 0xa3, 0xff, 0xaa, 0xb0, 0xb7, 0xff, 0xdc, 0xf3, 0xfc, 0xff, 0xcb, 0xe2, 0xfc, 0xff, 0xaa, 0xc3, 0xf1, 0xff, 0x6d, 0x87, 0xd1, 0xff, 0x52, 0x67, 0xaf, 0xff, 0xc2, 0xd0, 0xeb, 0xff, 0xdd, 0xed, 0xfb, 0xff, 0xdc, 0xea, 0xfc, 0xff, 0xba, 0xc6, 0xed, 0xff, 0x78, 0x8a, 0xb5, 0xff, 0x6f, 0x7f, 0xab, 0xff, 0x56, 0x65, 0x94, 0xff, 0x43, 0x4b, 0x7a, 0xff, 0x30, 0x32, 0x5e, 0xff, 0x31, 0x39, 0x60, 0xff, 0x2c, 0x34, 0x5a, 0xff, 0x31, 0x3a, 0x5f, 0xff, 0x34, 0x3e, 0x60, 0xff, 0x2a, 0x32, 0x53, 0xff, 0x20, 0x23, 0x45, 0xff, 0x17, 0x1f, 0x3f, 0xff, 0x3c, 0x46, 0x68, 0xff, 0x48, 0x50, 0x75, 0xff, 0x2e, 0x3b, 0x5e, 0xff, 0x4e, 0x64, 0x87, 0xff, 0x34, 0x4c, 0x75, 0xff, 0x3a, 0x4f, 0x77, 0xff, 0x3a, 0x4c, 0x71, 0xff, 0x21, 0x31, 0x56, 0xff, 0x22, 0x31, 0x56, 0xff, 0x26, 0x38, 0x5e, 0xff, 0x25, 0x38, 0x61, 0xff, 0x31, 0x45, 0x70, 0xff, 0x33, 0x46, 0x72, 0xff, 0x2f, 0x41, 0x6b, 0xff, 0x30, 0x40, 0x69, 0xff, 0x23, 0x35, 0x5a, 0xff, 0x24, 0x31, 0x58, 0xff, 0x30, 0x49, 0x81, 0xff, 0x30, 0x4b, 0x82, 0xff, 0x44, 0x5a, 0x90, 0xff, 0x0c, 0x13, 0x33, 0xff, 0x03, 0x07, 0x2b, 0xff, 0x14, 0x26, 0x5d, 0xff, 0x0e, 0x1e, 0x4b, 0xff, 0x16, 0x24, 0x55, 0xff, 0x5b, 0x67, 0x9e, 0xff, 0x27, 0x3a, 0x83, 0xff, 0x49, 0x58, 0x98, 0xff, 0x73, 0x78, 0xaa, 0xff, 0xd4, 0xbc, 0xa7, 0xff, 0xca, 0xba, 0xa0, 0xff, 0xc6, 0xb5, 0xa0, 0xff, 0xc1, 0xb6, 0xa4, 0xff, 0xbc, 0xb6, 0xa6, 0xff, 0xb6, 0xb4, 0xaa, 0xff, 0xae, 0xb2, 0xa8, 0xff, 0xa6, 0xad, 0xa7, 0xff, 0x9f, 0xa6, 0xa1, 0xff, 0x96, 0x9e, 0x99, 0xff, 0x8c, 0x94, 0x8c, 0xb5,
    0x77, 0x6b, 0x64, 0x92, 0x7d, 0x73, 0x6b, 0xff, 0x83, 0x78, 0x70, 0xff, 0x83, 0x75, 0x6e, 0xff, 0x83, 0x77, 0x6c, 0xff, 0x83, 0x75, 0x66, 0xff, 0x85, 0x77, 0x67, 0xff, 0x8d, 0x7e, 0x6d, 0xff, 0x95, 0x86, 0x76, 0xff, 0x9a, 0x8b, 0x7b, 0xff, 0x91, 0x84, 0x75, 0xff, 0x87, 0x79, 0x6d, 0xff, 0x7f, 0x72, 0x6b, 0xff, 0x7c, 0x6f, 0x6d, 0xff, 0x78, 0x71, 0x75, 0xff, 0x80, 0x79, 0x80, 0xff, 0x89, 0x85, 0x8f, 0xff, 0x94, 0x92, 0x9a, 0xff, 0xaa, 0xb1, 0xb0, 0xff, 0xda, 0xf1, 0xfd, 0xff, 0xbc, 0xd5, 0xfc, 0xff, 0x91, 0xac, 0xd9, 0xff, 0x69, 0x81, 0xba, 0xff, 0x68, 0x7b, 0xae, 0xff, 0xd4, 0xe8, 0xfc, 0xff, 0xd4, 0xe5, 0xf8, 0xff, 0xc9, 0xdd, 0xf8, 0xff, 0xb0, 0xbe, 0xe2, 0xff, 0xa2, 0xa6, 0xc2, 0xff, 0xcd, 0xd1, 0xe0, 0xff, 0xa6, 0xb4, 0xcd, 0xff, 0x70, 0x84, 0xb1, 0xff, 0x51, 0x62, 0x94, 0xff, 0x48, 0x58, 0x8a, 0xff, 0x43, 0x50, 0x80, 0xff, 0x38, 0x42, 0x70, 0xff, 0x32, 0x3d, 0x67, 0xff, 0x32, 0x3b, 0x65, 0xff, 0x24, 0x2d, 0x57, 0xff, 0x2c, 0x36, 0x5f, 0xff, 0x27, 0x31, 0x5b, 0xff, 0x43, 0x4d, 0x76, 0xff, 0x2d, 0x38, 0x5b, 0xff, 0x2b, 0x3b, 0x54, 0xff, 0x55, 0x6e, 0x98, 0xff, 0x2f, 0x42, 0x70, 0xff, 0x2d, 0x3d, 0x5c, 0xff, 0x2d, 0x37, 0x59, 0xff, 0x29, 0x3d, 0x63, 0xff, 0x2c, 0x3c, 0x60, 0xff, 0x27, 0x3a, 0x62, 0xff, 0x29, 0x3d, 0x6a, 0xff, 0x2f, 0x3f, 0x6b, 0xff, 0x2b, 0x3d, 0x63, 0xff, 0x27, 0x39, 0x60, 0xff, 0x20, 0x2d, 0x53, 0xff, 0x24, 0x32, 0x56, 0xff, 0x2a, 0x46, 0x7c, 0xff, 0x2e, 0x49, 0x80, 0xff, 0x2d, 0x3e, 0x6d, 0xff, 0x20, 0x2a, 0x49, 0xff, 0x56, 0x64, 0x8e, 0xff, 0x62, 0x7b, 0xac, 0xff, 0x4f, 0x62, 0x94, 0xff, 0x50, 0x67, 0xa7, 0xff, 0x44, 0x57, 0x97, 0xff, 0x35, 0x44, 0x89, 0xff, 0x56, 0x62, 0xab, 0xff, 0x88, 0x87, 0x8e, 0xff, 0xd1, 0xbc, 0xa4, 0xff, 0xcc, 0xba, 0x9f, 0xff, 0xc6, 0xb6, 0xa0, 0xff, 0xc2, 0xb6, 0xa3, 0xff, 0xbd, 0xb5, 0xa4, 0xff, 0xb8, 0xb4, 0xa6, 0xff, 0xb0, 0xb2, 0xa7, 0xff, 0xa5, 0xad, 0xa7, 0xff, 0x9e, 0xa6, 0x9f, 0xff, 0x95, 0x9c, 0x95, 0xff, 0x8d, 0x94, 0x8a, 0x92,
    0x67, 0x62, 0x64, 0x68, 0x68, 0x64, 0x65, 0xff, 0x6b, 0x65, 0x66, 0xff, 0x6e, 0x67, 0x67, 0xff, 0x71, 0x69, 0x67, 0xff, 0x73, 0x6a, 0x66, 0xff, 0x74, 0x6c, 0x66, 0xff, 0x78, 0x6f, 0x68, 0xff, 0x7b, 0x72, 0x6b, 0xff, 0x7c, 0x74, 0x6d, 0xff, 0x7a, 0x71, 0x6b, 0xff, 0x75, 0x6c, 0x6a, 0xff, 0x73, 0x6a, 0x6b, 0xff, 0x75, 0x6c, 0x70, 0xff, 0x77, 0x70, 0x76, 0xff, 0x7c, 0x77, 0x7f, 0xff, 0x82, 0x7e, 0x88, 0xff, 0x86, 0x82, 0x8d, 0xff, 0xa7, 0xac, 0xb0, 0xff, 0xdc, 0xf0, 0xfe, 0xff, 0xaf, 0xc5, 0xee, 0xff, 0x95, 0xac, 0xd9, 0xff, 0x55, 0x6b, 0x9e, 0xff, 0x90, 0xa2, 0xc8, 0xff, 0xdf, 0xf2, 0xfe, 0xff, 0xd2, 0xed, 0xf7, 0xff, 0xe0, 0xec, 0xf4, 0xff, 0xf6, 0xf8, 0xfa, 0xff, 0xf6, 0xf8, 0xfa, 0xff, 0xe5, 0xef, 0xf7, 0xff, 0xbb, 0xc6, 0xef, 0xff, 0x76, 0x82, 0xd1, 0xff, 0x61, 0x6f, 0xbe, 0xff, 0x55, 0x64, 0xac, 0xff, 0x4d, 0x59, 0xa0, 0xff, 0x48, 0x52, 0x98, 0xff, 0x42, 0x4c, 0x90, 0xff, 0x39, 0x47, 0x85, 0xff, 0x34, 0x42, 0x7b, 0xff, 0x35, 0x42, 0x75, 0xff, 0x34, 0x3f, 0x72, 0xff, 0x3b, 0x45, 0x76, 0xff, 0x2a, 0x37, 0x5e, 0xff, 0x21, 0x2f, 0x45, 0xff, 0x74, 0x88, 0xb2, 0xff, 0x35, 0x44, 0x73, 0xff, 0x20, 0x32, 0x4d, 0xff, 0x26, 0x37, 0x54, 0xff, 0x34, 0x47, 0x67, 0xff, 0x25, 0x36, 0x56, 0xff, 0x32, 0x46, 0x69, 0xff, 0x35, 0x46, 0x72, 0xff, 0x32, 0x42, 0x6e, 0xff, 0x28, 0x39, 0x5e, 0xff, 0x2c, 0x3b, 0x62, 0xff, 0x1d, 0x2c, 0x51, 0xff, 0x28, 0x36, 0x5b, 0xff, 0x2c, 0x46, 0x7e, 0xff, 0x28, 0x40, 0x77, 0xff, 0x26, 0x43, 0x77, 0xff, 0x5f, 0x76, 0xa9, 0xff, 0x5b, 0x73, 0xa5, 0xff, 0x49, 0x67, 0x9b, 0xff, 0x31, 0x49, 0x8a, 0xff, 0x29, 0x38, 0x76, 0xff, 0x2e, 0x38, 0x7d, 0xff, 0x60, 0x65, 0xba, 0xff, 0x43, 0x52, 0x7a, 0xff, 0x76, 0x6e, 0x6b, 0xff, 0xd1, 0xbb, 0xa2, 0xff, 0xcc, 0xb8, 0x9e, 0xff, 0xc7, 0xb5, 0x9e, 0xff, 0xc2, 0xb4, 0xa1, 0xff, 0xbe, 0xb3, 0xa2, 0xff, 0xb8, 0xb2, 0xa0, 0xff, 0xae, 0xaf, 0xa4, 0xff, 0xa5, 0xaa, 0xa2, 0xff, 0x9d, 0xa3, 0x9b, 0xff, 0x91, 0x99, 0x90, 0xff, 0x8b, 0x91, 0x87, 0x68,
    0x5f, 0x5c, 0x65, 0x43, 0x5f, 0x5e, 0x66, 0xfb, 0x61, 0x61, 0x68, 0xff, 0x64, 0x63, 0x69, 0xff, 0x67, 0x64, 0x69, 0xff, 0x6a, 0x65, 0x6b, 0xff, 0x6b, 0x67, 0x6a, 0xff, 0x6c, 0x68, 0x6b, 0xff, 0x6e, 0x69, 0x6d, 0xff, 0x6d, 0x68, 0x6b, 0xff, 0x6c, 0x66, 0x6a, 0xff, 0x6a, 0x65, 0x6a, 0xff, 0x6b, 0x67, 0x6d, 0xff, 0x6f, 0x6b, 0x73, 0xff, 0x75, 0x70, 0x7a, 0xff, 0x79, 0x74, 0x7e, 0xff, 0x7c, 0x78, 0x83, 0xff, 0x80, 0x79, 0x86, 0xff, 0x8a, 0x8d, 0x95, 0xff, 0xdd, 0xf1, 0xf7, 0xff, 0xaf, 0xc6, 0xea, 0xff, 0x8c, 0xa5, 0xcd, 0xff, 0x68, 0x7f, 0xa7, 0xff, 0xc0, 0xd2, 0xed, 0xff, 0xdb, 0xf0, 0xfb, 0xff, 0xe1, 0xf4, 0xfc, 0xff, 0xcd, 0xd7, 0xf9, 0xff, 0xa7, 0xac, 0xef, 0xff, 0x86, 0x93, 0xde, 0xff, 0x71, 0x78, 0xd8, 0xff, 0x62, 0x62, 0xc8, 0xff, 0x62, 0x6b, 0xc3, 0xff, 0x5e, 0x6e, 0xb7, 0xff, 0x65, 0x70, 0xba, 0xff, 0x52, 0x5d, 0xa7, 0xff, 0x4f, 0x58, 0xa5, 0xff, 0x3a, 0x40, 0x8b, 0xff, 0x34, 0x3c, 0x7e, 0xff, 0x2c, 0x34, 0x6e, 0xff, 0x29, 0x2a, 0x5c, 0xff, 0x20, 0x24, 0x4d, 0xff, 0x1d, 0x25, 0x4c, 0xff, 0x1c, 0x2a, 0x4e, 0xff, 0x33, 0x46, 0x5f, 0xff, 0x7f, 0x97, 0xbf, 0xff, 0x42, 0x4e, 0x7c, 0xff, 0x1b, 0x2c, 0x47, 0xff, 0x24, 0x37, 0x51, 0xff, 0x1c, 0x2b, 0x45, 0xff, 0x29, 0x3b, 0x5c, 0xff, 0x2a, 0x3b, 0x5f, 0xff, 0x36, 0x45, 0x6f, 0xff, 0x2d, 0x3c, 0x67, 0xff, 0x23, 0x34, 0x58, 0xff, 0x27, 0x35, 0x5a, 0xff, 0x22, 0x32, 0x58, 0xff, 0x23, 0x33, 0x59, 0xff, 0x27, 0x3f, 0x78, 0xff, 0x36, 0x4b, 0x83, 0xff, 0x4c, 0x60, 0x9c, 0xff, 0x47, 0x5a, 0x92, 0xff, 0x5c, 0x6b, 0xa2, 0xff, 0x59, 0x66, 0xa0, 0xff, 0x5f, 0x69, 0x99, 0xff, 0x53, 0x60, 0x99, 0xff, 0x55, 0x64, 0xb5, 0xff, 0x52, 0x5e, 0xa4, 0xff, 0x16, 0x28, 0x1f, 0xff, 0xa6, 0x9c, 0x93, 0xff, 0xce, 0xb8, 0x9d, 0xff, 0xcc, 0xb7, 0x9d, 0xff, 0xc7, 0xb5, 0x9c, 0xff, 0xc1, 0xb3, 0x9e, 0xff, 0xbd, 0xb2, 0xa0, 0xff, 0xb5, 0xad, 0x9a, 0xff, 0xae, 0xab, 0x9d, 0xff, 0xa3, 0xa7, 0x9c, 0xff, 0x9d, 0xa1, 0x96, 0xff, 0x8e, 0x94, 0x8b, 0xfa, 0x89, 0x8f, 0x84, 0x43,
    0x5a, 0x58, 0x61, 0x29, 0x5b, 0x59, 0x63, 0xef, 0x5d, 0x5c, 0x64, 0xff, 0x5e, 0x5e, 0x67, 0xff, 0x62, 0x5f, 0x68, 0xff, 0x64, 0x60, 0x6a, 0xff, 0x66, 0x62, 0x6a, 0xff, 0x68, 0x64, 0x6b, 0xff, 0x68, 0x65, 0x6b, 0xff, 0x69, 0x65, 0x6b, 0xff, 0x6c, 0x65, 0x6c, 0xff, 0x69, 0x63, 0x6a, 0xff, 0x6a, 0x66, 0x6c, 0xff, 0x6e, 0x6a, 0x72, 0xff, 0x72, 0x6f, 0x7b, 0xff, 0x76, 0x72, 0x7d, 0xff, 0x7a, 0x76, 0x81, 0xff, 0x7f, 0x79, 0x84, 0xff, 0x77, 0x79, 0x7f, 0xff, 0xd6, 0xe9, 0xee, 0xff, 0xb8, 0xcf, 0xed, 0xff, 0x7c, 0x9a, 0xba, 0xff, 0x94, 0xaf, 0xd0, 0xff, 0xc1, 0xd5, 0xec, 0xff, 0xd5, 0xec, 0xfa, 0xff, 0xc6, 0xd4, 0xf7, 0xff, 0x74, 0x87, 0xdd, 0xff, 0x6a, 0x84, 0xd4, 0xff, 0xab, 0xbe, 0xdf, 0xff, 0x90, 0xb4, 0xd7, 0xff, 0xae, 0xcc, 0xe5, 0xff, 0xa1, 0xbc, 0xde, 0xff, 0x98, 0xb5, 0xe2, 0xff, 0xad, 0xc4, 0xd9, 0xff, 0x81, 0x96, 0xb3, 0xff, 0xa5, 0xbd, 0xda, 0xff, 0x6b, 0x81, 0xa6, 0xff, 0x54, 0x71, 0x9a, 0xff, 0x34, 0x4e, 0x71, 0xff, 0x0d, 0x15, 0x39, 0xff, 0x03, 0x01, 0x11, 0xff, 0x0b, 0x0e, 0x23, 0xff, 0x20, 0x2e, 0x4c, 0xff, 0x3a, 0x50, 0x6f, 0xff, 0x80, 0x9f, 0xc4, 0xff, 0x36, 0x42, 0x6f, 0xff, 0x17, 0x23, 0x3e, 0xff, 0x19, 0x29, 0x41, 0xff, 0x25, 0x32, 0x4b, 0xff, 0x35, 0x46, 0x6a, 0xff, 0x2c, 0x39, 0x61, 0xff, 0x34, 0x40, 0x69, 0xff, 0x2a, 0x39, 0x62, 0xff, 0x27, 0x36, 0x5a, 0xff, 0x27, 0x33, 0x58, 0xff, 0x23, 0x34, 0x59, 0xff, 0x20, 0x2f, 0x57, 0xff, 0x23, 0x39, 0x72, 0xff, 0x33, 0x46, 0x80, 0xff, 0x4f, 0x69, 0xa5, 0xff, 0x4d, 0x67, 0x9d, 0xff, 0x5d, 0x71, 0xa4, 0xff, 0x6a, 0x76, 0xaf, 0xff, 0x6a, 0x7c, 0xb7, 0xff, 0x55, 0x6e, 0xb3, 0xff, 0x58, 0x62, 0xa7, 0xff, 0x1c, 0x21, 0x32, 0xff, 0x49, 0x46, 0x59, 0xff, 0xb6, 0xab, 0x99, 0xff, 0xc6, 0xad, 0x98, 0xff, 0xc3, 0xaf, 0x94, 0xff, 0xc5, 0xb1, 0x97, 0xff, 0xc1, 0xb1, 0x9c, 0xff, 0xba, 0xad, 0x9b, 0xff, 0xaf, 0xa7, 0x91, 0xff, 0xa9, 0xa7, 0x97, 0xff, 0xa1, 0xa2, 0x97, 0xff, 0x9a, 0x9d, 0x91, 0xff, 0x8a, 0x8f, 0x84, 0xef, 0x84, 0x89, 0x7d, 0x29,
    0x58, 0x55, 0x5e, 0x0b, 0x58, 0x55, 0x5e, 0xe1, 0x5a, 0x57, 0x5f, 0xff, 0x5b, 0x58, 0x62, 0xff, 0x5e, 0x5b, 0x64, 0xff, 0x5e, 0x5c, 0x65, 0xff, 0x5f, 0x5d, 0x64, 0xff, 0x60, 0x5e, 0x64, 0xff, 0x61, 0x5e, 0x64, 0xff, 0x62, 0x60, 0x67, 0xff, 0x5f, 0x5e, 0x68, 0xff, 0x5f, 0x5d, 0x66, 0xff, 0x65, 0x62, 0x6b, 0xff, 0x6d, 0x69, 0x70, 0xff, 0x6f, 0x6d, 0x76, 0xff, 0x73, 0x6f, 0x7a, 0xff, 0x76, 0x72, 0x7d, 0xff, 0x7d, 0x77, 0x82, 0xff, 0x73, 0x72, 0x79, 0xff, 0xd2, 0xdf, 0xe4, 0xff, 0xc4, 0xdc, 0xf3, 0xff, 0x7c, 0x99, 0xb9, 0xff, 0x78, 0x91, 0xbe, 0xff, 0x7b, 0x91, 0xb6, 0xff, 0xb9, 0xce, 0xeb, 0xff, 0x97, 0xa5, 0xe9, 0xff, 0x88, 0x9d, 0xee, 0xff, 0xe7, 0xef, 0xe8, 0xff, 0xf9, 0xfb, 0xfa, 0xff, 0xa8, 0xb8, 0xcc, 0xff, 0x76, 0x88, 0x8c, 0xff, 0x59, 0x62, 0x70, 0xff, 0x32, 0x33, 0x40, 0xff, 0x32, 0x33, 0x36, 0xff, 0x06, 0x03, 0x00, 0xff, 0x58, 0x61, 0x6d, 0xff, 0x59, 0x6c, 0x92, 0xff, 0x46, 0x5b, 0x7d, 0xff, 0x2f, 0x3e, 0x60, 0xff, 0x0a, 0x16, 0x34, 0xff, 0x1a, 0x23, 0x46, 0xff, 0x1f, 0x26, 0x54, 0xff, 0x24, 0x38, 0x55, 0xff, 0x3e, 0x56, 0x73, 0xff, 0x4f, 0x69, 0x92, 0xff, 0x2e, 0x3e, 0x68, 0xff, 0x1b, 0x29, 0x41, 0xff, 0x1a, 0x25, 0x42, 0xff, 0x27, 0x38, 0x55, 0xff, 0x2b, 0x3a, 0x5a, 0xff, 0x36, 0x45, 0x67, 0xff, 0x2e, 0x3b, 0x5f, 0xff, 0x2d, 0x39, 0x5e, 0xff, 0x29, 0x35, 0x5b, 0xff, 0x2c, 0x39, 0x60, 0xff, 0x2a, 0x34, 0x5b, 0xff, 0x1e, 0x2e, 0x57, 0xff, 0x2d, 0x40, 0x74, 0xff, 0x3b, 0x4b, 0x8a, 0xff, 0x4e, 0x62, 0xa9, 0xff, 0x5d, 0x6b, 0xa9, 0xff, 0x52, 0x66, 0xb0, 0xff, 0x56, 0x63, 0xb0, 0xff, 0x56, 0x63, 0xb7, 0xff, 0x44, 0x54, 0x82, 0xff, 0x1e, 0x1a, 0x34, 0xff, 0x0c, 0x13, 0x1c, 0xff, 0x8c, 0x81, 0x80, 0xff, 0xc2, 0xa8, 0x91, 0xff, 0xbf, 0xa8, 0x8c, 0xff, 0xc2, 0xa8, 0x8d, 0xff, 0xbf, 0xab, 0x8f, 0xff, 0xbc, 0xac, 0x95, 0xff, 0xba, 0xa8, 0x95, 0xff, 0xad, 0xa1, 0x8b, 0xff, 0xa8, 0xa2, 0x91, 0xff, 0x9e, 0x9f, 0x91, 0xff, 0x98, 0x9a, 0x8b, 0xff, 0x8a, 0x8a, 0x7c, 0xe1, 0x81, 0x82, 0x73, 0x0b,
    0x00, 0x00, 0x00, 0x00, 0x55, 0x53, 0x5b, 0xa8, 0x56, 0x53, 0x5b, 0xff, 0x59, 0x56, 0x5d, 0xff, 0x5c, 0x59, 0x60, 0xff, 0x5e, 0x5b, 0x62, 0xff, 0x5f, 0x5d, 0x63, 0xff, 0x5e, 0x5d, 0x61, 0xff, 0x5e, 0x5c, 0x61, 0xff, 0x5e, 0x5c, 0x62, 0xff, 0x5b, 0x59, 0x61, 0xff, 0x5b, 0x57, 0x60, 0xff, 0x61, 0x5c, 0x65, 0xff, 0x69, 0x63, 0x6e, 0xff, 0x6d, 0x6a, 0x73, 0xff, 0x72, 0x6e, 0x78, 0xff, 0x74, 0x6f, 0x7a, 0xff, 0x79, 0x73, 0x7f, 0xff, 0x6e, 0x6b, 0x76, 0xff, 0xbe, 0xc9, 0xd5, 0xff, 0xd1, 0xe6, 0xfc, 0xff, 0xa1, 0xba, 0xe2, 0xff, 0x37, 0x53, 0x83, 0xff, 0x56, 0x75, 0x9d, 0xff, 0x68, 0x7e, 0xb9, 0xff, 0x38, 0x4d, 0xb5, 0xff, 0xd2, 0xcd, 0xd4, 0xff, 0xcc, 0xe5, 0xf7, 0xff, 0x37, 0x6f, 0xce, 0xff, 0x00, 0x19, 0x6b, 0xff, 0x00, 0x00, 0x06, 0xff, 0x00, 0x00, 0x07, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x09, 0x0a, 0x11, 0xff, 0x17, 0x14, 0x30, 0xff, 0x23, 0x23, 0x41, 0xff, 0x29, 0x2d, 0x5b, 0xff, 0x31, 0x35, 0x6f, 0xff, 0x31, 0x3d, 0x6a, 0xff, 0x2e, 0x3c, 0x6a, 0xff, 0x2e, 0x43, 0x61, 0xff, 0x40, 0x57, 0x77, 0xff, 0x47, 0x5e, 0x83, 0xff, 0x29, 0x37, 0x5f, 0xff, 0x17, 0x24, 0x3c, 0xff, 0x22, 0x2f, 0x4b, 0xff, 0x2b, 0x3c, 0x5a, 0xff, 0x32, 0x40, 0x60, 0xff, 0x2c, 0x3a, 0x5c, 0xff, 0x37, 0x44, 0x68, 0xff, 0x29, 0x35, 0x5a, 0xff, 0x33, 0x40, 0x65, 0xff, 0x2b, 0x37, 0x5f, 0xff, 0x28, 0x34, 0x57, 0xff, 0x28, 0x39, 0x62, 0xff, 0x42, 0x54, 0x8b, 0xff, 0x36, 0x47, 0x83, 0xff, 0x52, 0x61, 0xaa, 0xff, 0x5e, 0x6b, 0xb0, 0xff, 0x55, 0x67, 0xaf, 0xff, 0x5a, 0x61, 0xa0, 0xff, 0x26, 0x26, 0x47, 0xff, 0x02, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x51, 0x55, 0x4f, 0xff, 0xbb, 0xa6, 0x94, 0xff, 0xbf, 0xa5, 0x89, 0xff, 0xbc, 0xa6, 0x8a, 0xff, 0xc3, 0xa9, 0x8d, 0xff, 0xbe, 0xa9, 0x8d, 0xff, 0xbb, 0xaa, 0x92, 0xff, 0xbb, 0xa9, 0x95, 0xff, 0xb3, 0xa3, 0x8c, 0xff, 0xa9, 0x9f, 0x8e, 0xff, 0x9f, 0x9b, 0x8d, 0xff, 0x9a, 0x98, 0x89, 0xff, 0x8a, 0x89, 0x7b, 0xa8, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x5b, 0x5b, 0x5c, 0x4a, 0x5c, 0x59, 0x59, 0xff, 0x5d, 0x57, 0x59, 0xff, 0x62, 0x5c, 0x5d, 0xff, 0x68, 0x62, 0x63, 0xff, 0x69, 0x68, 0x67, 0xff, 0x6a, 0x6b, 0x69, 0xff, 0x69, 0x6a, 0x68, 0xff, 0x67, 0x67, 0x66, 0xff, 0x64, 0x64, 0x63, 0xff, 0x5f, 0x5c, 0x5f, 0xff, 0x5f, 0x5b, 0x60, 0xff, 0x65, 0x5e, 0x65, 0xff, 0x69, 0x66, 0x6b, 0xff, 0x6f, 0x6a, 0x72, 0xff, 0x72, 0x6c, 0x75, 0xff, 0x76, 0x6f, 0x7a, 0xff, 0x6e, 0x6d, 0x76, 0xff, 0x9a, 0xa6, 0xad, 0xff, 0xd7, 0xeb, 0xfa, 0xff, 0xc8, 0xe1, 0xfb, 0xff, 0x7d, 0x97, 0xc3, 0xff, 0x6f, 0x8e, 0xb6, 0xff, 0x7c, 0x92, 0xc0, 0xff, 0x78, 0x75, 0xa6, 0xff, 0x77, 0x7d, 0xa3, 0xff, 0x87, 0xc5, 0xee, 0xff, 0x39, 0x6a, 0xc3, 0xff, 0x27, 0x32, 0x6d, 0xff, 0x38, 0x37, 0x3a, 0xff, 0x47, 0x4a, 0x4a, 0xff, 0x69, 0x69, 0x6a, 0xff, 0x8b, 0x83, 0x9a, 0xff, 0x9a, 0x9a, 0xcf, 0xff, 0x75, 0x79, 0xbd, 0xff, 0x50, 0x53, 0x9a, 0xff, 0x42, 0x48, 0x87, 0xff, 0x41, 0x4c, 0x87, 0xff, 0x35, 0x47, 0x76, 0xff, 0x30, 0x41, 0x6d, 0xff, 0x3b, 0x4a, 0x77, 0xff, 0x24, 0x39, 0x52, 0xff, 0x3d, 0x4e, 0x6f, 0xff, 0x33, 0x46, 0x67, 0xff, 0x28, 0x33, 0x58, 0xff, 0x1a, 0x26, 0x3c, 0xff, 0x29, 0x35, 0x51, 0xff, 0x25, 0x32, 0x51, 0xff, 0x2a, 0x36, 0x57, 0xff, 0x34, 0x3f, 0x62, 0xff, 0x2c, 0x38, 0x5c, 0xff, 0x35, 0x3f, 0x64, 0xff, 0x2f, 0x3b, 0x60, 0xff, 0x25, 0x31, 0x5a, 0xff, 0x27, 0x34, 0x54, 0xff, 0x2e, 0x3f, 0x68, 0xff, 0x43, 0x55, 0x8d, 0xff, 0x31, 0x43, 0x7c, 0xff, 0x20, 0x31, 0x6a, 0xff, 0x26, 0x38, 0x6e, 0xff, 0x2b, 0x41, 0x73, 0xff, 0x15, 0x1c, 0x35, 0xff, 0x04, 0x02, 0x02, 0xff, 0x0a, 0x03, 0x09, 0xff, 0x0e, 0x12, 0x19, 0xff, 0xa4, 0x99, 0x86, 0xff, 0xc5, 0xa7, 0x89, 0xff, 0xbd, 0xa2, 0x89, 0xff, 0xbc, 0xa5, 0x88, 0xff, 0xc5, 0xa8, 0x8a, 0xff, 0xc0, 0xaa, 0x8d, 0xff, 0xbd, 0xac, 0x92, 0xff, 0xbe, 0xab, 0x95, 0xff, 0xb2, 0xa1, 0x88, 0xff, 0xa7, 0x9d, 0x8b, 0xff, 0x9e, 0x99, 0x8a, 0xff, 0x96, 0x93, 0x84, 0xff, 0x88, 0x87, 0x78, 0x4a, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x63, 0x65, 0x5d, 0x0a, 0x64, 0x63, 0x5b, 0xe8, 0x64, 0x60, 0x58, 0xff, 0x66, 0x61, 0x59, 0xff, 0x6a, 0x66, 0x5f, 0xff, 0x6c, 0x6e, 0x65, 0xff, 0x70, 0x74, 0x6b, 0xff, 0x72, 0x77, 0x6c, 0xff, 0x70, 0x75, 0x6b, 0xff, 0x6e, 0x70, 0x68, 0xff, 0x69, 0x69, 0x63, 0xff, 0x65, 0x60, 0x60, 0xff, 0x63, 0x5d, 0x60, 0xff, 0x63, 0x5f, 0x64, 0xff, 0x67, 0x63, 0x69, 0xff, 0x6c, 0x68, 0x70, 0xff, 0x70, 0x6d, 0x74, 0xff, 0x6e, 0x6f, 0x72, 0xff, 0x7b, 0x85, 0x87, 0xff, 0xd8, 0xeb, 0xf6, 0xff, 0xcd, 0xe5, 0xfa, 0xff, 0xad, 0xc6, 0xea, 0xff, 0x8d, 0xa7, 0xcd, 0xff, 0xc9, 0xd9, 0xe9, 0xff, 0xf8, 0xfd, 0xff, 0xff, 0xd8, 0xdb, 0xf0, 0xff, 0xc5, 0xc4, 0xdc, 0xff, 0xc4, 0xc4, 0xce, 0xff, 0xd8, 0xd7, 0xe0, 0xff, 0xf8, 0xf6, 0xf3, 0xff, 0xfa, 0xfb, 0xff, 0xff, 0xdf, 0xdd, 0xff, 0xff, 0xb5, 0xba, 0xeb, 0xff, 0x76, 0x75, 0xd7, 0xff, 0x43, 0x46, 0xa0, 0xff, 0x4a, 0x52, 0x90, 0xff, 0x4f, 0x5b, 0x92, 0xff, 0x46, 0x4f, 0x85, 0xff, 0x31, 0x3b, 0x6c, 0xff, 0x3b, 0x4a, 0x78, 0xff, 0x20, 0x2f, 0x55, 0xff, 0x28, 0x39, 0x4e, 0xff, 0x3c, 0x48, 0x69, 0xff, 0x2e, 0x3f, 0x5e, 0xff, 0x28, 0x31, 0x51, 0xff, 0x1e, 0x29, 0x3f, 0xff, 0x1e, 0x2a, 0x47, 0xff, 0x2b, 0x38, 0x57, 0xff, 0x3a, 0x46, 0x67, 0xff, 0x33, 0x3e, 0x61, 0xff, 0x2d, 0x39, 0x5e, 0xff, 0x2a, 0x36, 0x5b, 0xff, 0x31, 0x3e, 0x64, 0xff, 0x2c, 0x39, 0x61, 0xff, 0x26, 0x33, 0x52, 0xff, 0x2d, 0x3c, 0x67, 0xff, 0x41, 0x52, 0x8b, 0xff, 0x28, 0x3c, 0x74, 0xff, 0x1f, 0x2d, 0x67, 0xff, 0x25, 0x3a, 0x72, 0xff, 0x2b, 0x40, 0x73, 0xff, 0x0c, 0x0e, 0x1f, 0xff, 0x05, 0x04, 0x03, 0xff, 0x02, 0x03, 0x07, 0xff, 0x47, 0x44, 0x42, 0xff, 0xbd, 0xa7, 0x8c, 0xff, 0xbf, 0xa3, 0x87, 0xff, 0xb8, 0xa0, 0x89, 0xff, 0xbc, 0xa4, 0x86, 0xff, 0xc6, 0xaa, 0x8a, 0xff, 0xc0, 0xab, 0x8d, 0xff, 0xbe, 0xac, 0x92, 0xff, 0xbc, 0xa9, 0x92, 0xff, 0xaf, 0xa1, 0x87, 0xff, 0xa6, 0x9e, 0x8a, 0xff, 0x96, 0x96, 0x85, 0xff, 0x8e, 0x8e, 0x7e, 0xe8, 0x8a, 0x89, 0x79, 0x0a, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x68, 0x6c, 0x5a, 0x03, 0x67, 0x6b, 0x59, 0xa1, 0x6c, 0x68, 0x57, 0xff, 0x6b, 0x65, 0x51, 0xff, 0x69, 0x6a, 0x57, 0xff, 0x6c, 0x71, 0x60, 0xff, 0x6e, 0x78, 0x68, 0xff, 0x70, 0x7f, 0x6b, 0xff, 0x72, 0x7f, 0x6c, 0xff, 0x72, 0x7a, 0x6d, 0xff, 0x70, 0x74, 0x66, 0xff, 0x6c, 0x69, 0x61, 0xff, 0x67, 0x5f, 0x5f, 0xff, 0x62, 0x5b, 0x5d, 0xff, 0x63, 0x5d, 0x63, 0xff, 0x65, 0x64, 0x68, 0xff, 0x69, 0x6e, 0x6d, 0xff, 0x6f, 0x72, 0x74, 0xff, 0x69, 0x6e, 0x6c, 0xff, 0xbf, 0xcf, 0xdd, 0xff, 0xca, 0xdd, 0xf9, 0xff, 0xbd, 0xd1, 0xec, 0xff, 0x98, 0xb5, 0xdd, 0xff, 0xd1, 0xe0, 0xee, 0xff, 0xf9, 0xfa, 0xfd, 0xff, 0xe4, 0xe6, 0xf2, 0xff, 0xef, 0xf0, 0xfa, 0xff, 0xfa, 0xf9, 0xfe, 0xff, 0xf4, 0xf6, 0xfb, 0xff, 0xd9, 0xdf, 0xf7, 0xff, 0x9e, 0xa4, 0xe7, 0xff, 0x66, 0x65, 0xda, 0xff, 0x51, 0x53, 0xb4, 0xff, 0x52, 0x59, 0xa0, 0xff, 0x5a, 0x65, 0x98, 0xff, 0x50, 0x5d, 0x8c, 0xff, 0x44, 0x4e, 0x7f, 0xff, 0x2e, 0x37, 0x66, 0xff, 0x2b, 0x3c, 0x6b, 0xff, 0x27, 0x38, 0x65, 0xff, 0x1f, 0x2f, 0x48, 0xff, 0x31, 0x3f, 0x56, 0xff, 0x2f, 0x38, 0x62, 0xff, 0x27, 0x31, 0x50, 0xff, 0x1f, 0x2a, 0x40, 0xff, 0x24, 0x2e, 0x46, 0xff, 0x29, 0x34, 0x55, 0xff, 0x2d, 0x3a, 0x5c, 0xff, 0x2a, 0x36, 0x57, 0xff, 0x23, 0x2d, 0x4e, 0xff, 0x3b, 0x48, 0x6c, 0xff, 0x34, 0x43, 0x6b, 0xff, 0x29, 0x37, 0x5b, 0xff, 0x2a, 0x37, 0x59, 0xff, 0x21, 0x2c, 0x4b, 0xff, 0x32, 0x3f, 0x6b, 0xff, 0x3a, 0x4c, 0x84, 0xff, 0x23, 0x36, 0x6e, 0xff, 0x21, 0x32, 0x6c, 0xff, 0x2d, 0x42, 0x75, 0xff, 0x2a, 0x3c, 0x73, 0xff, 0x04, 0x06, 0x15, 0xff, 0x05, 0x05, 0x03, 0xff, 0x01, 0x04, 0x09, 0xff, 0x74, 0x71, 0x73, 0xff, 0xc0, 0xa4, 0x83, 0xff, 0xbb, 0xa2, 0x87, 0xff, 0xb9, 0x9e, 0x85, 0xff, 0xbb, 0xa3, 0x86, 0xff, 0xc1, 0xa9, 0x89, 0xff, 0xbf, 0xa8, 0x8c, 0xff, 0xbc, 0xa9, 0x8f, 0xff, 0xb6, 0xa7, 0x8e, 0xff, 0xad, 0x9d, 0x89, 0xff, 0xa3, 0x9b, 0x87, 0xff, 0x94, 0x91, 0x82, 0xff, 0x8d, 0x8b, 0x7d, 0xa1, 0x87, 0x86, 0x78, 0x03, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6d, 0x70, 0x58, 0x51, 0x70, 0x6e, 0x54, 0xfb, 0x6f, 0x6a, 0x4e, 0xff, 0x6c, 0x6d, 0x53, 0xff, 0x6e, 0x74, 0x5c, 0xff, 0x72, 0x7e, 0x64, 0xff, 0x73, 0x84, 0x6b, 0xff, 0x74, 0x85, 0x6d, 0xff, 0x77, 0x82, 0x6b, 0xff, 0x76, 0x7d, 0x64, 0xff, 0x73, 0x74, 0x61, 0xff, 0x6b, 0x66, 0x5b, 0xff, 0x64, 0x5b, 0x5a, 0xff, 0x61, 0x5c, 0x5e, 0xff, 0x62, 0x62, 0x65, 0xff, 0x68, 0x6e, 0x6d, 0xff, 0x6d, 0x76, 0x75, 0xff, 0x68, 0x75, 0x70, 0xff, 0x9c, 0xae, 0xb0, 0xff, 0xd0, 0xe2, 0xf4, 0xff, 0xc4, 0xd6, 0xec, 0xff, 0xa6, 0xbf, 0xe0, 0xff, 0xd8, 0xe8, 0xf6, 0xff, 0xfb, 0xfd, 0xfe, 0xff, 0xdb, 0xe3, 0xf5, 0xff, 0xc9, 0xd2, 0xf6, 0xff, 0xc1, 0xca, 0xf0, 0xff, 0xa7, 0xb0, 0xed, 0xff, 0x95, 0x9b, 0xe6, 0xff, 0x86, 0x92, 0xcf, 0xff, 0x71, 0x7e, 0xb9, 0xff, 0x63, 0x6e, 0x9e, 0xff, 0x51, 0x5a, 0x8a, 0xff, 0x43, 0x4b, 0x79, 0xff, 0x31, 0x3c, 0x6a, 0xff, 0x2b, 0x37, 0x67, 0xff, 0x2c, 0x3a, 0x65, 0xff, 0x26, 0x36, 0x5e, 0xff, 0x22, 0x33, 0x54, 0xff, 0x25, 0x33, 0x4a, 0xff, 0x24, 0x2f, 0x47, 0xff, 0x3a, 0x40, 0x69, 0xff, 0x19, 0x23, 0x41, 0xff, 0x18, 0x21, 0x37, 0xff, 0x1b, 0x25, 0x3c, 0xff, 0x21, 0x2c, 0x4c, 0xff, 0x29, 0x38, 0x5c, 0xff, 0x2d, 0x3a, 0x5e, 0xff, 0x3f, 0x4a, 0x6e, 0xff, 0x2c, 0x38, 0x5d, 0xff, 0x2b, 0x38, 0x5f, 0xff, 0x2b, 0x37, 0x5b, 0xff, 0x26, 0x32, 0x52, 0xff, 0x1e, 0x2e, 0x4d, 0xff, 0x27, 0x39, 0x63, 0xff, 0x35, 0x48, 0x7d, 0xff, 0x1f, 0x34, 0x68, 0xff, 0x1d, 0x2f, 0x68, 0xff, 0x2c, 0x42, 0x74, 0xff, 0x2e, 0x41, 0x77, 0xff, 0x06, 0x0a, 0x17, 0xff, 0x03, 0x04, 0x06, 0xff, 0x17, 0x1f, 0x26, 0xff, 0x95, 0x90, 0x8d, 0xff, 0xbb, 0xa0, 0x87, 0xff, 0xb6, 0x9e, 0x86, 0xff, 0xb5, 0x9b, 0x81, 0xff, 0xba, 0xa0, 0x83, 0xff, 0xbc, 0xa3, 0x88, 0xff, 0xba, 0xa2, 0x89, 0xff, 0xb9, 0xa3, 0x8b, 0xff, 0xb8, 0xa4, 0x8d, 0xff, 0xae, 0x9b, 0x87, 0xff, 0xa0, 0x96, 0x84, 0xff, 0x95, 0x90, 0x81, 0xfb, 0x8f, 0x8b, 0x7e, 0x51, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x73, 0x78, 0x57, 0x07, 0x79, 0x71, 0x4f, 0xe4, 0x78, 0x6e, 0x4a, 0xff, 0x74, 0x71, 0x4e, 0xff, 0x79, 0x7b, 0x58, 0xff, 0x7c, 0x84, 0x62, 0xff, 0x79, 0x89, 0x68, 0xff, 0x78, 0x88, 0x6a, 0xff, 0x7b, 0x86, 0x67, 0xff, 0x79, 0x80, 0x62, 0xff, 0x77, 0x79, 0x60, 0xff, 0x72, 0x6d, 0x5c, 0xff, 0x69, 0x5e, 0x57, 0xff, 0x64, 0x5c, 0x5c, 0xff, 0x63, 0x62, 0x64, 0xff, 0x6a, 0x71, 0x6d, 0xff, 0x70, 0x7a, 0x77, 0xff, 0x72, 0x7f, 0x77, 0xff, 0x73, 0x82, 0x76, 0xff, 0xcc, 0xdb, 0xe8, 0xff, 0xc9, 0xd9, 0xec, 0xff, 0xb4, 0xca, 0xe5, 0xff, 0xd6, 0xe9, 0xf8, 0xff, 0xf7, 0xfe, 0xfb, 0xff, 0xe7, 0xf1, 0xfd, 0xff, 0xc4, 0xd4, 0xf1, 0xff, 0x9c, 0xac, 0xd1, 0xff, 0x92, 0xa4, 0xbf, 0xff, 0x77, 0x81, 0xab, 0xff, 0x66, 0x6f, 0x95, 0xff, 0x56, 0x5c, 0x87, 0xff, 0x43, 0x4d, 0x77, 0xff, 0x3c, 0x45, 0x72, 0xff, 0x2d, 0x34, 0x65, 0xff, 0x2c, 0x36, 0x67, 0xff, 0x2d, 0x3d, 0x6a, 0xff, 0x28, 0x3a, 0x61, 0xff, 0x2e, 0x3d, 0x5f, 0xff, 0x2f, 0x3b, 0x59, 0xff, 0x23, 0x2d, 0x44, 0xff, 0x19, 0x20, 0x36, 0xff, 0x18, 0x1e, 0x3d, 0xff, 0x1d, 0x22, 0x40, 0xff, 0x17, 0x1d, 0x32, 0xff, 0x19, 0x24, 0x39, 0xff, 0x20, 0x2f, 0x4d, 0xff, 0x2e, 0x3e, 0x63, 0xff, 0x28, 0x36, 0x5a, 0xff, 0x2b, 0x38, 0x5b, 0xff, 0x33, 0x40, 0x65, 0xff, 0x28, 0x34, 0x59, 0xff, 0x27, 0x33, 0x55, 0xff, 0x19, 0x23, 0x42, 0xff, 0x23, 0x33, 0x56, 0xff, 0x37, 0x4a, 0x79, 0xff, 0x2c, 0x40, 0x77, 0xff, 0x1e, 0x30, 0x66, 0xff, 0x1c, 0x31, 0x66, 0xff, 0x2d, 0x45, 0x76, 0xff, 0x2d, 0x43, 0x76, 0xff, 0x05, 0x0b, 0x16, 0xff, 0x02, 0x03, 0x09, 0xff, 0x47, 0x4e, 0x58, 0xff, 0xaa, 0xa5, 0xa3, 0xff, 0xb6, 0xa6, 0xa0, 0xff, 0xb2, 0xa0, 0x96, 0xff, 0xab, 0x9a, 0x88, 0xff, 0xaf, 0x9b, 0x88, 0xff, 0xb7, 0xa2, 0x91, 0xff, 0xb5, 0xa0, 0x90, 0xff, 0xb5, 0xa1, 0x91, 0xff, 0xaf, 0x9e, 0x8c, 0xff, 0xa6, 0x96, 0x84, 0xff, 0x9b, 0x93, 0x82, 0xff, 0x90, 0x8f, 0x80, 0xe4, 0x8a, 0x85, 0x7c, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7d, 0x71, 0x49, 0x64, 0x7b, 0x6e, 0x46, 0xff, 0x7a, 0x71, 0x4c, 0xff, 0x7d, 0x7a, 0x53, 0xff, 0x7f, 0x84, 0x5f, 0xff, 0x7b, 0x87, 0x65, 0xff, 0x7b, 0x86, 0x66, 0xff, 0x7d, 0x84, 0x65, 0xff, 0x7a, 0x7f, 0x61, 0xff, 0x78, 0x78, 0x5f, 0xff, 0x72, 0x6e, 0x5b, 0xff, 0x6c, 0x61, 0x58, 0xff, 0x67, 0x5f, 0x5d, 0xff, 0x66, 0x65, 0x65, 0xff, 0x6d, 0x73, 0x6f, 0xff, 0x73, 0x7b, 0x76, 0xff, 0x76, 0x82, 0x77, 0xff, 0x77, 0x82, 0x71, 0xff, 0x96, 0xa4, 0xa3, 0xff, 0xc6, 0xd6, 0xe3, 0xff, 0xb1, 0xc6, 0xe2, 0xff, 0xd6, 0xe3, 0xf1, 0xff, 0xf1, 0xfc, 0xfa, 0xff, 0xdf, 0xec, 0xf7, 0xff, 0xc0, 0xcf, 0xe8, 0xff, 0x90, 0xa5, 0xc6, 0xff, 0x71, 0x84, 0xaf, 0xff, 0x5e, 0x6e, 0x94, 0xff, 0x4d, 0x5b, 0x7d, 0xff, 0x3b, 0x47, 0x71, 0xff, 0x35, 0x43, 0x6f, 0xff, 0x3e, 0x4c, 0x79, 0xff, 0x2f, 0x3c, 0x6d, 0xff, 0x2d, 0x39, 0x6a, 0xff, 0x22, 0x30, 0x5a, 0xff, 0x35, 0x45, 0x66, 0xff, 0x49, 0x59, 0x76, 0xff, 0x38, 0x43, 0x5d, 0xff, 0x1f, 0x28, 0x3f, 0xff, 0x1d, 0x22, 0x39, 0xff, 0x15, 0x18, 0x32, 0xff, 0x17, 0x1b, 0x34, 0xff, 0x1b, 0x23, 0x37, 0xff, 0x1e, 0x29, 0x40, 0xff, 0x21, 0x2f, 0x4f, 0xff, 0x2a, 0x3a, 0x60, 0xff, 0x31, 0x41, 0x65, 0xff, 0x2a, 0x39, 0x5d, 0xff, 0x22, 0x30, 0x55, 0xff, 0x31, 0x3c, 0x60, 0xff, 0x25, 0x2f, 0x51, 0xff, 0x21, 0x2c, 0x4b, 0xff, 0x28, 0x39, 0x60, 0xff, 0x35, 0x49, 0x79, 0xff, 0x26, 0x39, 0x71, 0xff, 0x1c, 0x2d, 0x66, 0xff, 0x1c, 0x33, 0x68, 0xff, 0x2f, 0x46, 0x78, 0xff, 0x36, 0x4c, 0x7d, 0xff, 0x0a, 0x10, 0x20, 0xff, 0x07, 0x0e, 0x14, 0xff, 0x76, 0x7e, 0x87, 0xff, 0xa5, 0x9e, 0xa1, 0xff, 0x9f, 0x9b, 0x98, 0xff, 0x9b, 0x94, 0x90, 0xff, 0x9f, 0x9a, 0x94, 0xff, 0xa2, 0x98, 0x8e, 0xff, 0xa7, 0x98, 0x8b, 0xff, 0xa4, 0x96, 0x89, 0xff, 0xa4, 0x97, 0x8b, 0xff, 0xa4, 0x96, 0x8c, 0xff, 0x99, 0x91, 0x7f, 0xff, 0x92, 0x8f, 0x80, 0xff, 0x8d, 0x8e, 0x81, 0x64, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x6d, 0x42, 0x13, 0x7d, 0x6b, 0x42, 0xe0, 0x7f, 0x70, 0x4c, 0xff, 0x7f, 0x79, 0x56, 0xff, 0x7f, 0x82, 0x64, 0xff, 0x7b, 0x85, 0x69, 0xff, 0x7d, 0x85, 0x68, 0xff, 0x7e, 0x84, 0x65, 0xff, 0x7d, 0x7f, 0x60, 0xff, 0x7a, 0x76, 0x5d, 0xff, 0x76, 0x6c, 0x5b, 0xff, 0x6e, 0x62, 0x5a, 0xff, 0x68, 0x5f, 0x5d, 0xff, 0x67, 0x66, 0x65, 0xff, 0x6f, 0x71, 0x6f, 0xff, 0x76, 0x7e, 0x77, 0xff, 0x7a, 0x86, 0x7a, 0xff, 0x80, 0x89, 0x77, 0xff, 0x7c, 0x85, 0x76, 0xff, 0xc3, 0xcd, 0xcc, 0xff, 0xba, 0xca, 0xe4, 0xff, 0xd2, 0xde, 0xee, 0xff, 0xe9, 0xf8, 0xfc, 0xff, 0xe0, 0xeb, 0xfb, 0xff, 0xc0, 0xce, 0xea, 0xff, 0x9d, 0xaf, 0xd6, 0xff, 0x6c, 0x7c, 0xae, 0xff, 0x61, 0x6c, 0x9c, 0xff, 0x6b, 0x76, 0xa3, 0xff, 0x64, 0x6f, 0xa0, 0xff, 0x64, 0x70, 0xa4, 0xff, 0x49, 0x56, 0x8a, 0xff, 0x3b, 0x49, 0x7e, 0xff, 0x5e, 0x6c, 0x90, 0xff, 0x9b, 0xa8, 0xc5, 0xff, 0xa2, 0xae, 0xcf, 0xff, 0x7a, 0x87, 0xa7, 0xff, 0x51, 0x5b, 0x75, 0xff, 0x1c, 0x22, 0x3b, 0xff, 0x19, 0x1d, 0x36, 0xff, 0x13, 0x15, 0x2c, 0xff, 0x16, 0x1a, 0x2e, 0xff, 0x15, 0x1e, 0x31, 0xff, 0x1a, 0x26, 0x41, 0xff, 0x1f, 0x2c, 0x4e, 0xff, 0x2c, 0x39, 0x5e, 0xff, 0x29, 0x35, 0x5a, 0xff, 0x2c, 0x39, 0x5e, 0xff, 0x30, 0x3d, 0x63, 0xff, 0x2f, 0x3b, 0x5e, 0xff, 0x21, 0x2b, 0x4b, 0xff, 0x22, 0x2e, 0x51, 0xff, 0x2f, 0x41, 0x6b, 0xff, 0x2b, 0x3e, 0x70, 0xff, 0x1d, 0x31, 0x67, 0xff, 0x1b, 0x32, 0x69, 0xff, 0x1d, 0x36, 0x6b, 0xff, 0x32, 0x47, 0x7c, 0xff, 0x40, 0x55, 0x86, 0xff, 0x0f, 0x16, 0x2a, 0xff, 0x2a, 0x32, 0x35, 0xff, 0x92, 0x95, 0x99, 0xff, 0xaf, 0xa2, 0xa2, 0xff, 0xad, 0xa5, 0x9c, 0xff, 0xa7, 0xa0, 0x9c, 0xff, 0xa9, 0xa4, 0xa3, 0xff, 0xad, 0xa2, 0x99, 0xff, 0xb1, 0xa1, 0x92, 0xff, 0xae, 0x9f, 0x92, 0xff, 0xaa, 0x9c, 0x90, 0xff, 0xa8, 0x9c, 0x91, 0xff, 0x9e, 0x94, 0x83, 0xff, 0x98, 0x92, 0x81, 0xe0, 0x94, 0x90, 0x80, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x83, 0x6a, 0x42, 0x84, 0x82, 0x6c, 0x4a, 0xff, 0x7d, 0x70, 0x53, 0xff, 0x77, 0x76, 0x5d, 0xff, 0x74, 0x79, 0x63, 0xff, 0x77, 0x7b, 0x64, 0xff, 0x7c, 0x7c, 0x60, 0xff, 0x7d, 0x7a, 0x5d, 0xff, 0x7f, 0x73, 0x5d, 0xff, 0x7b, 0x69, 0x5a, 0xff, 0x70, 0x61, 0x5b, 0xff, 0x6b, 0x61, 0x60, 0xff, 0x6c, 0x67, 0x68, 0xff, 0x71, 0x71, 0x70, 0xff, 0x77, 0x7c, 0x78, 0xff, 0x7f, 0x87, 0x7c, 0xff, 0x87, 0x8d, 0x7f, 0xff, 0x8b, 0x8d, 0x7a, 0xff, 0x95, 0x94, 0x82, 0xff, 0xc3, 0xcd, 0xd4, 0xff, 0xbc, 0xcf, 0xe2, 0xff, 0xdd, 0xea, 0xf8, 0xff, 0xd4, 0xe0, 0xf8, 0xff, 0xd2, 0xdd, 0xf9, 0xff, 0xbd, 0xc9, 0xe7, 0xff, 0xba, 0xc4, 0xde, 0xff, 0xcf, 0xd8, 0xef, 0xff, 0xd3, 0xdf, 0xfa, 0xff, 0xc0, 0xd0, 0xf0, 0xff, 0xb2, 0xc2, 0xeb, 0xff, 0xb6, 0xc4, 0xe9, 0xff, 0xb9, 0xc6, 0xe9, 0xff, 0xd4, 0xe4, 0xf8, 0xff, 0xbc, 0xcb, 0xea, 0xff, 0x81, 0x8a, 0xae, 0xff, 0x44, 0x4c, 0x6f, 0xff, 0x3c, 0x42, 0x5f, 0xff, 0x1d, 0x21, 0x3b, 0xff, 0x12, 0x17, 0x2c, 0xff, 0x11, 0x17, 0x27, 0xff, 0x15, 0x1a, 0x2d, 0xff, 0x1e, 0x25, 0x3a, 0xff, 0x1a, 0x25, 0x42, 0xff, 0x25, 0x30, 0x56, 0xff, 0x2f, 0x3a, 0x62, 0xff, 0x29, 0x35, 0x5c, 0xff, 0x31, 0x3c, 0x64, 0xff, 0x2a, 0x36, 0x5d, 0xff, 0x2d, 0x39, 0x5b, 0xff, 0x1e, 0x29, 0x4c, 0xff, 0x25, 0x33, 0x59, 0xff, 0x2d, 0x40, 0x6d, 0xff, 0x25, 0x3a, 0x6d, 0xff, 0x1e, 0x34, 0x6b, 0xff, 0x20, 0x38, 0x6c, 0xff, 0x22, 0x3a, 0x6f, 0xff, 0x32, 0x47, 0x7b, 0xff, 0x46, 0x5b, 0x8c, 0xff, 0x14, 0x1a, 0x2d, 0xff, 0x52, 0x57, 0x5b, 0xff, 0xaf, 0xa9, 0xa9, 0xff, 0xb6, 0xaa, 0xa8, 0xff, 0xb8, 0xa9, 0xa5, 0xff, 0xb8, 0xab, 0xa6, 0xff, 0xb9, 0xaf, 0xad, 0xff, 0xbd, 0xad, 0xa2, 0xff, 0xbf, 0xac, 0x9e, 0xff, 0xbe, 0xac, 0x9f, 0xff, 0xba, 0xa8, 0x9b, 0xff, 0xb8, 0xa8, 0x9a, 0xff, 0xb3, 0xa4, 0x90, 0xff, 0xae, 0xa0, 0x8d, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x89, 0x6d, 0x45, 0x0c, 0x83, 0x6e, 0x4c, 0xdd, 0x82, 0x70, 0x53, 0xff, 0x7c, 0x71, 0x58, 0xff, 0x74, 0x70, 0x5a, 0xff, 0x75, 0x71, 0x59, 0xff, 0x81, 0x74, 0x5c, 0xff, 0x7f, 0x73, 0x59, 0xff, 0x7d, 0x70, 0x5b, 0xff, 0x79, 0x69, 0x5b, 0xff, 0x73, 0x62, 0x5e, 0xff, 0x6f, 0x62, 0x64, 0xff, 0x6e, 0x67, 0x6a, 0xff, 0x73, 0x72, 0x72, 0xff, 0x7c, 0x7f, 0x7c, 0xff, 0x84, 0x89, 0x82, 0xff, 0x8d, 0x8f, 0x83, 0xff, 0x94, 0x94, 0x80, 0xff, 0x95, 0x8e, 0x6f, 0xff, 0xaf, 0xb2, 0xac, 0xff, 0xc9, 0xd7, 0xe8, 0xff, 0xd5, 0xe5, 0xf4, 0xff, 0xcf, 0xdf, 0xf5, 0xff, 0xca, 0xd9, 0xf1, 0xff, 0xde, 0xea, 0xf6, 0xff, 0xf2, 0xf7, 0xfa, 0xff, 0xe4, 0xeb, 0xf6, 0xff, 0xdb, 0xe5, 0xf9, 0xff, 0xcc, 0xdc, 0xf8, 0xff, 0xb7, 0xc8, 0xe9, 0xff, 0xc0, 0xcc, 0xee, 0xff, 0xc1, 0xc9, 0xed, 0xff, 0xb5, 0xbf, 0xe8, 0xff, 0x70, 0x78, 0xa5, 0xff, 0x4b, 0x4f, 0x7c, 0xff, 0x31, 0x36, 0x53, 0xff, 0x19, 0x1d, 0x31, 0xff, 0x15, 0x17, 0x2d, 0xff, 0x14, 0x18, 0x2e, 0xff, 0x22, 0x27, 0x3b, 0xff, 0x17, 0x1c, 0x2e, 0xff, 0x1b, 0x23, 0x37, 0xff, 0x23, 0x2d, 0x4c, 0xff, 0x2c, 0x38, 0x62, 0xff, 0x33, 0x45, 0x6f, 0xff, 0x32, 0x42, 0x6d, 0xff, 0x2e, 0x3f, 0x6a, 0xff, 0x22, 0x31, 0x58, 0xff, 0x30, 0x3b, 0x5e, 0xff, 0x1e, 0x2b, 0x4e, 0xff, 0x25, 0x35, 0x5d, 0xff, 0x27, 0x3a, 0x6c, 0xff, 0x27, 0x3c, 0x72, 0xff, 0x23, 0x3a, 0x71, 0xff, 0x21, 0x3c, 0x6f, 0xff, 0x25, 0x3d, 0x72, 0xff, 0x31, 0x46, 0x7a, 0xff, 0x4e, 0x63, 0x93, 0xff, 0x1a, 0x23, 0x36, 0xff, 0x84, 0x85, 0x8a, 0xff, 0xc1, 0xac, 0xab, 0xff, 0xb3, 0xad, 0xa6, 0xff, 0xbb, 0xad, 0xa7, 0xff, 0xba, 0xaf, 0xa8, 0xff, 0xbc, 0xb2, 0xb0, 0xff, 0xbe, 0xae, 0xa2, 0xff, 0xc0, 0xad, 0x9f, 0xff, 0xbb, 0xaa, 0x9c, 0xff, 0xb5, 0xa7, 0x96, 0xff, 0xb3, 0xa6, 0x95, 0xff, 0xb2, 0xa3, 0x8c, 0xdd, 0xad, 0xa0, 0x8c, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x86, 0x71, 0x54, 0x54, 0x88, 0x73, 0x57, 0xfa, 0x82, 0x72, 0x5d, 0xff, 0x75, 0x6f, 0x5f, 0xff, 0x75, 0x6e, 0x5a, 0xff, 0x7d, 0x71, 0x56, 0xff, 0x7e, 0x70, 0x59, 0xff, 0x7c, 0x6d, 0x5b, 0xff, 0x73, 0x6a, 0x59, 0xff, 0x74, 0x64, 0x5d, 0xff, 0x6f, 0x63, 0x63, 0xff, 0x6d, 0x69, 0x6a, 0xff, 0x75, 0x74, 0x73, 0xff, 0x82, 0x82, 0x7d, 0xff, 0x8f, 0x8e, 0x87, 0xff, 0x95, 0x96, 0x88, 0xff, 0x96, 0x98, 0x86, 0xff, 0xa7, 0x95, 0x74, 0xff, 0x9a, 0x93, 0x7f, 0xff, 0xc7, 0xcf, 0xdf, 0xff, 0xdb, 0xe3, 0xf5, 0xff, 0xd5, 0xdf, 0xf0, 0xff, 0xe1, 0xe9, 0xf6, 0xff, 0xf3, 0xf4, 0xfc, 0xff, 0xe0, 0xe6, 0xf4, 0xff, 0xd9, 0xe0, 0xf2, 0xff, 0xce, 0xd8, 0xf1, 0xff, 0xc1, 0xcd, 0xed, 0xff, 0xac, 0xba, 0xe4, 0xff, 0xad, 0xb8, 0xe5, 0xff, 0x93, 0x9c, 0xce, 0xff, 0x5b, 0x62, 0x90, 0xff, 0x27, 0x2d, 0x56, 0xff, 0x2a, 0x2e, 0x4e, 0xff, 0x1f, 0x22, 0x3d, 0xff, 0x1d, 0x21, 0x3a, 0xff, 0x25, 0x28, 0x3f, 0xff, 0x1b, 0x1e, 0x36, 0xff, 0x1c, 0x21, 0x3d, 0xff, 0x22, 0x2a, 0x41, 0xff, 0x26, 0x31, 0x4e, 0xff, 0x21, 0x2c, 0x4d, 0xff, 0x28, 0x32, 0x53, 0xff, 0x2c, 0x3e, 0x65, 0xff, 0x2f, 0x3f, 0x65, 0xff, 0x2d, 0x3b, 0x62, 0xff, 0x2c, 0x37, 0x5b, 0xff, 0x28, 0x33, 0x54, 0xff, 0x28, 0x36, 0x5d, 0xff, 0x2a, 0x3b, 0x66, 0xff, 0x2b, 0x40, 0x71, 0xff, 0x27, 0x3c, 0x71, 0xff, 0x25, 0x3b, 0x72, 0xff, 0x27, 0x3f, 0x73, 0xff, 0x29, 0x3f, 0x74, 0xff, 0x2c, 0x45, 0x7b, 0xff, 0x48, 0x60, 0x93, 0xff, 0x4e, 0x50, 0x5e, 0xff, 0xb9, 0xac, 0xac, 0xff, 0xba, 0xac, 0xab, 0xff, 0xb7, 0xab, 0xa7, 0xff, 0xba, 0xad, 0xa7, 0xff, 0xba, 0xad, 0xa7, 0xff, 0xc0, 0xb4, 0xac, 0xff, 0xbb, 0xaf, 0xa4, 0xff, 0xbc, 0xad, 0xa2, 0xff, 0xbb, 0xac, 0xa0, 0xff, 0xb7, 0xa8, 0x98, 0xff, 0xb0, 0xa2, 0x8f, 0xfa, 0xab, 0x9e, 0x8a, 0x55, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x87, 0x70, 0x56, 0x05, 0x84, 0x72, 0x58, 0xc0, 0x7e, 0x74, 0x62, 0xff, 0x74, 0x74, 0x68, 0xff, 0x75, 0x73, 0x64, 0xff, 0x7c, 0x71, 0x58, 0xff, 0x7a, 0x6c, 0x56, 0xff, 0x7d, 0x6e, 0x5c, 0xff, 0x78, 0x6c, 0x5d, 0xff, 0x76, 0x66, 0x5d, 0xff, 0x71, 0x65, 0x64, 0xff, 0x6d, 0x68, 0x6a, 0xff, 0x75, 0x76, 0x76, 0xff, 0x85, 0x85, 0x84, 0xff, 0x90, 0x8f, 0x8b, 0xff, 0x96, 0x96, 0x8b, 0xff, 0x97, 0x9b, 0x8a, 0xff, 0xa7, 0x9a, 0x7d, 0xff, 0x9b, 0x94, 0x7a, 0xff, 0xd1, 0xda, 0xe6, 0xff, 0xe7, 0xec, 0xfd, 0xff, 0xe0, 0xe9, 0xf8, 0xff, 0xe2, 0xec, 0xfe, 0xff, 0xd9, 0xdf, 0xf5, 0xff, 0xc5, 0xd0, 0xef, 0xff, 0xbd, 0xca, 0xe9, 0xff, 0xb1, 0xbe, 0xe4, 0xff, 0x8f, 0x9e, 0xc5, 0xff, 0x6b, 0x79, 0xa8, 0xff, 0x49, 0x53, 0x80, 0xff, 0x3d, 0x43, 0x6e, 0xff, 0x30, 0x34, 0x5e, 0xff, 0x29, 0x2e, 0x51, 0xff, 0x21, 0x25, 0x41, 0xff, 0x1b, 0x1e, 0x38, 0xff, 0x1d, 0x20, 0x3f, 0xff, 0x22, 0x26, 0x40, 0xff, 0x24, 0x29, 0x41, 0xff, 0x24, 0x2d, 0x48, 0xff, 0x1f, 0x28, 0x43, 0xff, 0x2d, 0x38, 0x5c, 0xff, 0x24, 0x33, 0x5a, 0xff, 0x2b, 0x38, 0x61, 0xff, 0x31, 0x3e, 0x68, 0xff, 0x2d, 0x39, 0x63, 0xff, 0x2c, 0x35, 0x60, 0xff, 0x30, 0x3a, 0x60, 0xff, 0x1c, 0x26, 0x48, 0xff, 0x25, 0x34, 0x5c, 0xff, 0x24, 0x36, 0x62, 0xff, 0x29, 0x3e, 0x6e, 0xff, 0x24, 0x39, 0x6e, 0xff, 0x28, 0x3e, 0x75, 0xff, 0x2a, 0x42, 0x76, 0xff, 0x2a, 0x41, 0x77, 0xff, 0x2a, 0x45, 0x7a, 0xff, 0x43, 0x57, 0x87, 0xff, 0x8c, 0x87, 0x8c, 0xff, 0xbb, 0xa6, 0x92, 0xff, 0xba, 0xab, 0x9d, 0xff, 0xb3, 0xa9, 0x9d, 0xff, 0xb5, 0xaa, 0x9e, 0xff, 0xb7, 0xaa, 0x9d, 0xff, 0xb6, 0xa9, 0x9c, 0xff, 0xbd, 0xb0, 0xa2, 0xff, 0xba, 0xaf, 0xa1, 0xff, 0xb8, 0xad, 0x9d, 0xff, 0xb1, 0xa7, 0x92, 0xff, 0xac, 0xa2, 0x89, 0xc0, 0xaa, 0x9c, 0x87, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7f, 0x71, 0x5d, 0x20, 0x77, 0x71, 0x65, 0xe8, 0x71, 0x73, 0x6b, 0xff, 0x75, 0x77, 0x6d, 0xff, 0x80, 0x75, 0x64, 0xff, 0x7e, 0x70, 0x59, 0xff, 0x7f, 0x70, 0x5d, 0xff, 0x7d, 0x6d, 0x60, 0xff, 0x7b, 0x69, 0x5e, 0xff, 0x74, 0x67, 0x65, 0xff, 0x6b, 0x68, 0x6c, 0xff, 0x72, 0x75, 0x78, 0xff, 0x81, 0x84, 0x84, 0xff, 0x8b, 0x8d, 0x8c, 0xff, 0x93, 0x96, 0x8d, 0xff, 0x9a, 0x9e, 0x8d, 0xff, 0xa5, 0x9e, 0x89, 0xff, 0x9f, 0x93, 0x73, 0xff, 0xcc, 0xd3, 0xd8, 0xff, 0xd6, 0xdc, 0xed, 0xff, 0xd7, 0xe2, 0xf5, 0xff, 0xc3, 0xd1, 0xf0, 0xff, 0xaa, 0xb7, 0xdd, 0xff, 0x98, 0xa7, 0xd2, 0xff, 0x8a, 0x98, 0xc4, 0xff, 0x7f, 0x8e, 0xbb, 0xff, 0x5f, 0x6e, 0x9c, 0xff, 0x4f, 0x5d, 0x88, 0xff, 0x46, 0x4e, 0x77, 0xff, 0x34, 0x39, 0x5e, 0xff, 0x39, 0x3b, 0x5e, 0xff, 0x26, 0x29, 0x48, 0xff, 0x23, 0x27, 0x41, 0xff, 0x25, 0x29, 0x41, 0xff, 0x30, 0x34, 0x50, 0xff, 0x23, 0x26, 0x40, 0xff, 0x20, 0x25, 0x40, 0xff, 0x2f, 0x3a, 0x5a, 0xff, 0x2b, 0x37, 0x5a, 0xff, 0x2c, 0x39, 0x5c, 0xff, 0x35, 0x43, 0x6c, 0xff, 0x33, 0x42, 0x6f, 0xff, 0x37, 0x43, 0x6b, 0xff, 0x2f, 0x38, 0x62, 0xff, 0x35, 0x3d, 0x68, 0xff, 0x34, 0x3d, 0x64, 0xff, 0x26, 0x32, 0x57, 0xff, 0x29, 0x38, 0x64, 0xff, 0x2a, 0x3c, 0x6b, 0xff, 0x26, 0x3c, 0x6d, 0xff, 0x28, 0x40, 0x74, 0xff, 0x2a, 0x42, 0x78, 0xff, 0x28, 0x43, 0x76, 0xff, 0x29, 0x43, 0x78, 0xff, 0x29, 0x46, 0x7b, 0xff, 0x3b, 0x50, 0x80, 0xff, 0x98, 0x8e, 0x93, 0xff, 0xca, 0xa4, 0x88, 0xff, 0xb6, 0x9a, 0x86, 0xff, 0xaf, 0x98, 0x8a, 0xff, 0xaf, 0x9b, 0x8f, 0xff, 0xb3, 0x9d, 0x93, 0xff, 0xb7, 0xa2, 0x97, 0xff, 0xbb, 0xa5, 0x9b, 0xff, 0xb8, 0xa5, 0x9b, 0xff, 0xb3, 0xa0, 0x94, 0xff, 0xb1, 0x9e, 0x8f, 0xe8, 0xb0, 0x9d, 0x87, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x72, 0x6c, 0x67, 0x62, 0x6e, 0x6d, 0x6b, 0xfb, 0x72, 0x73, 0x6e, 0xff, 0x81, 0x77, 0x69, 0xff, 0x81, 0x73, 0x5e, 0xff, 0x80, 0x71, 0x5c, 0xff, 0x7f, 0x6e, 0x63, 0xff, 0x7d, 0x6a, 0x5e, 0xff, 0x72, 0x67, 0x64, 0xff, 0x6a, 0x67, 0x6c, 0xff, 0x6e, 0x72, 0x76, 0xff, 0x7f, 0x83, 0x84, 0xff, 0x8a, 0x8d, 0x8e, 0xff, 0x93, 0x98, 0x90, 0xff, 0x9e, 0x9e, 0x8d, 0xff, 0xa3, 0x9b, 0x8f, 0xff, 0xac, 0x9b, 0x75, 0xff, 0xc2, 0xc3, 0xc2, 0xff, 0xc1, 0xcb, 0xe5, 0xff, 0xb6, 0xc3, 0xde, 0xff, 0xa0, 0xb1, 0xd5, 0xff, 0x7e, 0x8c, 0xba, 0xff, 0x77, 0x83, 0xa9, 0xff, 0x73, 0x7d, 0xa5, 0xff, 0x66, 0x6f, 0x96, 0xff, 0x5f, 0x67, 0x90, 0xff, 0x5a, 0x61, 0x88, 0xff, 0x40, 0x48, 0x6e, 0xff, 0x40, 0x48, 0x6e, 0xff, 0x3e, 0x42, 0x63, 0xff, 0x32, 0x34, 0x4f, 0xff, 0x28, 0x2c, 0x43, 0xff, 0x32, 0x36, 0x4f, 0xff, 0x25, 0x2a, 0x3e, 0xff, 0x2e, 0x32, 0x48, 0xff, 0x30, 0x36, 0x54, 0xff, 0x29, 0x32, 0x5c, 0xff, 0x2e, 0x3e, 0x68, 0xff, 0x35, 0x3e, 0x61, 0xff, 0x3b, 0x42, 0x65, 0xff, 0x31, 0x3c, 0x62, 0xff, 0x2f, 0x3d, 0x60, 0xff, 0x32, 0x3d, 0x62, 0xff, 0x34, 0x3d, 0x62, 0xff, 0x2f, 0x39, 0x5e, 0xff, 0x27, 0x32, 0x5b, 0xff, 0x2b, 0x3a, 0x68, 0xff, 0x2b, 0x3e, 0x70, 0xff, 0x2b, 0x43, 0x74, 0xff, 0x2a, 0x42, 0x77, 0xff, 0x28, 0x41, 0x78, 0xff, 0x27, 0x43, 0x76, 0xff, 0x27, 0x41, 0x75, 0xff, 0x25, 0x44, 0x7b, 0xff, 0x31, 0x4a, 0x83, 0xff, 0x86, 0x85, 0x94, 0xff, 0xad, 0x94, 0x81, 0xff, 0x9a, 0x88, 0x7b, 0xff, 0x94, 0x85, 0x7a, 0xff, 0x96, 0x8c, 0x84, 0xff, 0x98, 0x91, 0x8b, 0xff, 0x9d, 0x95, 0x92, 0xff, 0xa0, 0x98, 0x96, 0xff, 0xa1, 0x98, 0x97, 0xff, 0xa0, 0x98, 0x94, 0xfb, 0xa0, 0x98, 0x8f, 0x63, 0xa6, 0x99, 0x8d, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x71, 0x6b, 0x5e, 0x03, 0x66, 0x6a, 0x63, 0x92, 0x6a, 0x6e, 0x65, 0xff, 0x7b, 0x75, 0x64, 0xff, 0x7e, 0x73, 0x5f, 0xff, 0x7e, 0x70, 0x5d, 0xff, 0x7c, 0x6f, 0x5f, 0xff, 0x7b, 0x6a, 0x5e, 0xff, 0x72, 0x63, 0x5f, 0xff, 0x66, 0x61, 0x65, 0xff, 0x69, 0x6a, 0x72, 0xff, 0x75, 0x77, 0x7c, 0xff, 0x7d, 0x82, 0x82, 0xff, 0x89, 0x8b, 0x88, 0xff, 0x94, 0x92, 0x89, 0xff, 0xa2, 0x97, 0x81, 0xff, 0xa7, 0x95, 0x77, 0xff, 0xa4, 0x98, 0x7c, 0xff, 0xce, 0xd4, 0xda, 0xff, 0x9e, 0xac, 0xc1, 0xff, 0x8d, 0x9d, 0xbb, 0xff, 0x6b, 0x75, 0x99, 0xff, 0x64, 0x73, 0x97, 0xff, 0x57, 0x5e, 0x7d, 0xff, 0x5a, 0x60, 0x80, 0xff, 0x62, 0x6a, 0x8c, 0xff, 0x4e, 0x57, 0x75, 0xff, 0x52, 0x5a, 0x79, 0xff, 0x4c, 0x55, 0x74, 0xff, 0x44, 0x4a, 0x69, 0xff, 0x3b, 0x3e, 0x58, 0xff, 0x34, 0x3b, 0x50, 0xff, 0x2b, 0x31, 0x48, 0xff, 0x45, 0x4b, 0x69, 0xff, 0x35, 0x3d, 0x5e, 0xff, 0x35, 0x40, 0x63, 0xff, 0x2e, 0x3b, 0x5f, 0xff, 0x33, 0x3f, 0x61, 0xff, 0x3c, 0x47, 0x6b, 0xff, 0x38, 0x43, 0x68, 0xff, 0x3d, 0x48, 0x6f, 0xff, 0x41, 0x49, 0x6f, 0xff, 0x38, 0x40, 0x61, 0xff, 0x2f, 0x39, 0x5d, 0xff, 0x2d, 0x3b, 0x63, 0xff, 0x22, 0x34, 0x5e, 0xff, 0x2b, 0x3f, 0x6a, 0xff, 0x2b, 0x40, 0x6d, 0xff, 0x2f, 0x48, 0x79, 0xff, 0x2c, 0x44, 0x78, 0xff, 0x2b, 0x43, 0x78, 0xff, 0x27, 0x40, 0x76, 0xff, 0x22, 0x40, 0x72, 0xff, 0x28, 0x43, 0x7c, 0xff, 0x31, 0x49, 0x7c, 0xff, 0x68, 0x76, 0x96, 0xff, 0x98, 0x84, 0x75, 0xff, 0x7e, 0x71, 0x5a, 0xff, 0x6d, 0x68, 0x64, 0xff, 0x69, 0x68, 0x6a, 0xff, 0x60, 0x6b, 0x6e, 0xff, 0x65, 0x6f, 0x74, 0xff, 0x6f, 0x73, 0x75, 0xff, 0x75, 0x79, 0x7a, 0xff, 0x79, 0x7d, 0x7e, 0x93, 0x72, 0x78, 0x78, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x66, 0x60, 0x0d, 0x64, 0x69, 0x65, 0xc1, 0x76, 0x6f, 0x62, 0xff, 0x7d, 0x71, 0x60, 0xff, 0x7f, 0x6f, 0x5e, 0xff, 0x7d, 0x6d, 0x60, 0xff, 0x79, 0x67, 0x5b, 0xff, 0x71, 0x60, 0x5d, 0xff, 0x60, 0x5a, 0x5e, 0xff, 0x61, 0x60, 0x69, 0xff, 0x6a, 0x6b, 0x71, 0xff, 0x71, 0x74, 0x76, 0xff, 0x7b, 0x7d, 0x7c, 0xff, 0x86, 0x84, 0x7d, 0xff, 0x96, 0x8a, 0x79, 0xff, 0xa4, 0x8c, 0x6d, 0xff, 0x93, 0x7e, 0x59, 0xff, 0xa4, 0x96, 0x83, 0xff, 0x91, 0x95, 0x99, 0xff, 0x71, 0x82, 0x9d, 0xff, 0x5b, 0x63, 0x7f, 0xff, 0x56, 0x62, 0x73, 0xff, 0x49, 0x52, 0x63, 0xff, 0x39, 0x41, 0x55, 0xff, 0x3a, 0x43, 0x57, 0xff, 0x33, 0x3a, 0x4b, 0xff, 0x40, 0x46, 0x58, 0xff, 0x3c, 0x43, 0x54, 0xff, 0x30, 0x37, 0x4f, 0xff, 0x3a, 0x42, 0x5d, 0xff, 0x3a, 0x41, 0x5d, 0xff, 0x43, 0x48, 0x6a, 0xff, 0x38, 0x42, 0x67, 0xff, 0x3c, 0x47, 0x6c, 0xff, 0x39, 0x43, 0x64, 0xff, 0x40, 0x48, 0x69, 0xff, 0x39, 0x46, 0x6a, 0xff, 0x37, 0x42, 0x67, 0xff, 0x38, 0x44, 0x69, 0xff, 0x3c, 0x47, 0x6e, 0xff, 0x3d, 0x47, 0x6d, 0xff, 0x40, 0x48, 0x6e, 0xff, 0x33, 0x3e, 0x64, 0xff, 0x22, 0x32, 0x59, 0xff, 0x26, 0x37, 0x61, 0xff, 0x2e, 0x42, 0x6d, 0xff, 0x2d, 0x42, 0x70, 0xff, 0x33, 0x4b, 0x7c, 0xff, 0x2f, 0x48, 0x7a, 0xff, 0x29, 0x41, 0x75, 0xff, 0x26, 0x3e, 0x73, 0xff, 0x26, 0x43, 0x75, 0xff, 0x27, 0x42, 0x79, 0xff, 0x2f, 0x48, 0x7a, 0xff, 0x55, 0x65, 0x8b, 0xff, 0x97, 0x89, 0x84, 0xff, 0x7f, 0x70, 0x59, 0xff, 0x6a, 0x67, 0x5d, 0xff, 0x63, 0x66, 0x64, 0xff, 0x61, 0x66, 0x68, 0xff, 0x6b, 0x6f, 0x72, 0xff, 0x7a, 0x77, 0x77, 0xff, 0x81, 0x7f, 0x7b, 0xc1, 0x7b, 0x80, 0x79, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x65, 0x63, 0x16, 0x71, 0x6b, 0x5b, 0xda, 0x7c, 0x71, 0x5e, 0xff, 0x7f, 0x6d, 0x5b, 0xff, 0x7b, 0x69, 0x5a, 0xff, 0x77, 0x64, 0x5a, 0xff, 0x70, 0x5e, 0x5b, 0xff, 0x5d, 0x54, 0x59, 0xff, 0x5b, 0x58, 0x62, 0xff, 0x63, 0x61, 0x69, 0xff, 0x67, 0x68, 0x6b, 0xff, 0x6f, 0x6f, 0x6f, 0xff, 0x77, 0x72, 0x6c, 0xff, 0x84, 0x76, 0x69, 0xff, 0x8f, 0x77, 0x5e, 0xff, 0x90, 0x76, 0x53, 0xff, 0x8b, 0x6e, 0x48, 0xff, 0x79, 0x69, 0x51, 0xff, 0x60, 0x5c, 0x57, 0xff, 0x5a, 0x59, 0x63, 0xff, 0x61, 0x64, 0x78, 0xff, 0x58, 0x5f, 0x7a, 0xff, 0x55, 0x5d, 0x7c, 0xff, 0x4e, 0x55, 0x75, 0xff, 0x4e, 0x55, 0x74, 0xff, 0x40, 0x47, 0x67, 0xff, 0x3d, 0x44, 0x62, 0xff, 0x4f, 0x59, 0x7a, 0xff, 0x3e, 0x4c, 0x6b, 0xff, 0x49, 0x55, 0x74, 0xff, 0x42, 0x4d, 0x72, 0xff, 0x40, 0x4c, 0x73, 0xff, 0x42, 0x4f, 0x74, 0xff, 0x3e, 0x47, 0x6b, 0xff, 0x44, 0x4d, 0x6f, 0xff, 0x37, 0x43, 0x67, 0xff, 0x40, 0x4a, 0x71, 0xff, 0x3f, 0x49, 0x71, 0xff, 0x3a, 0x44, 0x6d, 0xff, 0x39, 0x46, 0x6e, 0xff, 0x3e, 0x47, 0x73, 0xff, 0x2f, 0x3c, 0x65, 0xff, 0x26, 0x38, 0x5e, 0xff, 0x30, 0x41, 0x6a, 0xff, 0x2b, 0x3e, 0x69, 0xff, 0x35, 0x4a, 0x78, 0xff, 0x31, 0x49, 0x78, 0xff, 0x31, 0x48, 0x79, 0xff, 0x2d, 0x44, 0x76, 0xff, 0x2b, 0x40, 0x74, 0xff, 0x23, 0x3d, 0x70, 0xff, 0x24, 0x41, 0x73, 0xff, 0x2a, 0x44, 0x74, 0xff, 0x3a, 0x52, 0x81, 0xff, 0x8e, 0x92, 0xa1, 0xff, 0x88, 0x78, 0x69, 0xff, 0x71, 0x6b, 0x60, 0xff, 0x69, 0x67, 0x65, 0xff, 0x6c, 0x6a, 0x6a, 0xff, 0x7b, 0x77, 0x77, 0xff, 0x8e, 0x85, 0x7e, 0xda, 0x95, 0x89, 0x82, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x6f, 0x6d, 0x54, 0x28, 0x79, 0x6e, 0x56, 0xe1, 0x7d, 0x6b, 0x54, 0xff, 0x79, 0x66, 0x52, 0xff, 0x75, 0x61, 0x56, 0xff, 0x6f, 0x5c, 0x59, 0xff, 0x5d, 0x54, 0x59, 0xff, 0x58, 0x53, 0x5c, 0xff, 0x59, 0x56, 0x5f, 0xff, 0x59, 0x59, 0x5e, 0xff, 0x63, 0x61, 0x62, 0xff, 0x68, 0x61, 0x5d, 0xff, 0x75, 0x65, 0x5a, 0xff, 0x7e, 0x68, 0x55, 0xff, 0x85, 0x6d, 0x50, 0xff, 0x85, 0x6b, 0x48, 0xff, 0x82, 0x62, 0x34, 0xff, 0x78, 0x5e, 0x3c, 0xff, 0x7a, 0x72, 0x6c, 0xff, 0x89, 0x8c, 0x9a, 0xff, 0x7b, 0x86, 0x9e, 0xff, 0x66, 0x73, 0x90, 0xff, 0x69, 0x75, 0x93, 0xff, 0x5c, 0x6b, 0x8d, 0xff, 0x5d, 0x6c, 0x8f, 0xff, 0x44, 0x54, 0x76, 0xff, 0x55, 0x61, 0x89, 0xff, 0x4a, 0x57, 0x7e, 0xff, 0x4c, 0x5a, 0x7d, 0xff, 0x4b, 0x59, 0x7d, 0xff, 0x4d, 0x59, 0x7b, 0xff, 0x46, 0x51, 0x75, 0xff, 0x45, 0x50, 0x77, 0xff, 0x3c, 0x47, 0x6e, 0xff, 0x40, 0x4c, 0x72, 0xff, 0x3a, 0x44, 0x6c, 0xff, 0x37, 0x42, 0x6a, 0xff, 0x3b, 0x46, 0x6f, 0xff, 0x3c, 0x4b, 0x74, 0xff, 0x31, 0x3b, 0x6a, 0xff, 0x24, 0x32, 0x5d, 0xff, 0x2e, 0x40, 0x67, 0xff, 0x30, 0x42, 0x6b, 0xff, 0x32, 0x45, 0x71, 0xff, 0x31, 0x47, 0x74, 0xff, 0x32, 0x49, 0x78, 0xff, 0x32, 0x49, 0x79, 0xff, 0x30, 0x47, 0x79, 0xff, 0x29, 0x40, 0x72, 0xff, 0x28, 0x40, 0x73, 0xff, 0x27, 0x41, 0x72, 0xff, 0x27, 0x42, 0x72, 0xff, 0x29, 0x43, 0x77, 0xff, 0x74, 0x83, 0x9f, 0xff, 0xa6, 0x9b, 0x98, 0xff, 0x73, 0x66, 0x60, 0xff, 0x6c, 0x65, 0x64, 0xff, 0x6d, 0x6a, 0x68, 0xff, 0x7b, 0x73, 0x70, 0xe2, 0x8a, 0x7d, 0x75, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7d, 0x6d, 0x51, 0x25, 0x80, 0x6c, 0x4f, 0xe2, 0x78, 0x64, 0x4e, 0xff, 0x73, 0x5f, 0x52, 0xff, 0x6b, 0x5a, 0x57, 0xff, 0x5e, 0x52, 0x54, 0xff, 0x58, 0x50, 0x52, 0xff, 0x52, 0x4f, 0x53, 0xff, 0x4f, 0x4c, 0x51, 0xff, 0x55, 0x4f, 0x50, 0xff, 0x60, 0x57, 0x54, 0xff, 0x80, 0x67, 0x5c, 0xff, 0x8e, 0x75, 0x5a, 0xff, 0x95, 0x74, 0x53, 0xff, 0x94, 0x73, 0x4e, 0xff, 0x88, 0x69, 0x45, 0xff, 0x7a, 0x5d, 0x3b, 0xff, 0x70, 0x55, 0x37, 0xff, 0x74, 0x66, 0x59, 0xff, 0xa1, 0xa3, 0xad, 0xff, 0x7e, 0x87, 0x9a, 0xff, 0x80, 0x8d, 0xaa, 0xff, 0x6c, 0x7a, 0x98, 0xff, 0x61, 0x71, 0x93, 0xff, 0x66, 0x78, 0x9e, 0xff, 0x63, 0x73, 0x99, 0xff, 0x58, 0x67, 0x8b, 0xff, 0x5b, 0x6a, 0x8e, 0xff, 0x51, 0x62, 0x84, 0xff, 0x46, 0x56, 0x75, 0xff, 0x48, 0x55, 0x7b, 0xff, 0x42, 0x4e, 0x75, 0xff, 0x38, 0x46, 0x69, 0xff, 0x41, 0x4d, 0x75, 0xff, 0x3a, 0x45, 0x6e, 0xff, 0x38, 0x44, 0x6c, 0xff, 0x37, 0x43, 0x6b, 0xff, 0x26, 0x36, 0x61, 0xff, 0x2b, 0x39, 0x62, 0xff, 0x28, 0x37, 0x60, 0xff, 0x2d, 0x3b, 0x67, 0xff, 0x34, 0x46, 0x71, 0xff, 0x34, 0x48, 0x75, 0xff, 0x32, 0x49, 0x75, 0xff, 0x38, 0x4e, 0x7d, 0xff, 0x32, 0x48, 0x78, 0xff, 0x2b, 0x42, 0x74, 0xff, 0x28, 0x41, 0x73, 0xff, 0x25, 0x3f, 0x70, 0xff, 0x24, 0x3d, 0x6f, 0xff, 0x25, 0x3d, 0x70, 0xff, 0x2a, 0x44, 0x75, 0xff, 0x4d, 0x5f, 0x86, 0xff, 0xa5, 0xa9, 0xbb, 0xff, 0xa3, 0x9c, 0x9f, 0xff, 0x67, 0x62, 0x63, 0xff, 0x6b, 0x64, 0x67, 0xe2, 0x7c, 0x6e, 0x6d, 0x25, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x81, 0x6b, 0x4e, 0x28, 0x7b, 0x65, 0x4f, 0xdb, 0x71, 0x5e, 0x50, 0xff, 0x67, 0x57, 0x52, 0xff, 0x5c, 0x50, 0x52, 0xff, 0x58, 0x51, 0x52, 0xff, 0x59, 0x52, 0x55, 0xff, 0x58, 0x51, 0x52, 0xff, 0x5c, 0x50, 0x4d, 0xff, 0x70, 0x5f, 0x53, 0xff, 0x91, 0x74, 0x5f, 0xff, 0xa1, 0x84, 0x65, 0xff, 0xa8, 0x88, 0x67, 0xff, 0x9f, 0x82, 0x5b, 0xff, 0x98, 0x7a, 0x4f, 0xff, 0x8a, 0x6b, 0x42, 0xff, 0x7a, 0x5c, 0x39, 0xff, 0x7e, 0x5d, 0x3d, 0xff, 0x86, 0x72, 0x61, 0xff, 0xb3, 0xb9, 0xc2, 0xff, 0x75, 0x86, 0x9e, 0xff, 0x75, 0x85, 0xa0, 0xff, 0x78, 0x88, 0xa7, 0xff, 0x69, 0x7c, 0xa0, 0xff, 0x65, 0x77, 0x9b, 0xff, 0x5e, 0x70, 0x93, 0xff, 0x52, 0x64, 0x85, 0xff, 0x46, 0x5a, 0x79, 0xff, 0x44, 0x58, 0x79, 0xff, 0x48, 0x5b, 0x7f, 0xff, 0x40, 0x52, 0x77, 0xff, 0x3b, 0x4d, 0x72, 0xff, 0x3c, 0x4b, 0x72, 0xff, 0x38, 0x47, 0x6d, 0xff, 0x37, 0x45, 0x6c, 0xff, 0x33, 0x42, 0x69, 0xff, 0x22, 0x30, 0x58, 0xff, 0x26, 0x35, 0x5f, 0xff, 0x31, 0x41, 0x6b, 0xff, 0x31, 0x42, 0x6d, 0xff, 0x37, 0x4a, 0x77, 0xff, 0x38, 0x4e, 0x7b, 0xff, 0x36, 0x4e, 0x7b, 0xff, 0x34, 0x4b, 0x79, 0xff, 0x33, 0x4a, 0x79, 0xff, 0x2e, 0x45, 0x75, 0xff, 0x25, 0x3e, 0x6f, 0xff, 0x25, 0x3f, 0x6e, 0xff, 0x24, 0x3e, 0x6e, 0xff, 0x24, 0x3d, 0x6d, 0xff, 0x25, 0x3f, 0x6f, 0xff, 0x35, 0x4d, 0x7c, 0xff, 0x71, 0x79, 0x98, 0xff, 0xa5, 0xa2, 0xb5, 0xff, 0xa3, 0x9c, 0xaa, 0xdb, 0x59, 0x4a, 0x51, 0x28, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x75, 0x63, 0x4d, 0x16, 0x70, 0x5e, 0x4f, 0xc0, 0x68, 0x57, 0x52, 0xff, 0x5e, 0x54, 0x54, 0xff, 0x5d, 0x55, 0x56, 0xff, 0x62, 0x58, 0x59, 0xff, 0x5f, 0x53, 0x52, 0xff, 0x6a, 0x59, 0x51, 0xff, 0x85, 0x70, 0x5d, 0xff, 0xac, 0x89, 0x71, 0xff, 0xb5, 0x99, 0x7b, 0xff, 0xb8, 0x9b, 0x82, 0xff, 0xb2, 0x9a, 0x72, 0xff, 0xa6, 0x8e, 0x62, 0xff, 0x92, 0x7a, 0x54, 0xff, 0x7a, 0x65, 0x4a, 0xff, 0x7c, 0x67, 0x4a, 0xff, 0x8d, 0x70, 0x4b, 0xff, 0xd4, 0xe5, 0xea, 0xff, 0xcc, 0xe3, 0xed, 0xff, 0x7b, 0x8b, 0x9f, 0xff, 0x75, 0x86, 0xa2, 0xff, 0x69, 0x7c, 0xa0, 0xff, 0x5e, 0x71, 0x96, 0xff, 0x69, 0x7c, 0xa1, 0xff, 0x4e, 0x61, 0x86, 0xff, 0x56, 0x6a, 0x8d, 0xff, 0x46, 0x58, 0x7c, 0xff, 0x3a, 0x4c, 0x6d, 0xff, 0x39, 0x4b, 0x6d, 0xff, 0x39, 0x49, 0x71, 0xff, 0x38, 0x46, 0x6d, 0xff, 0x2f, 0x3d, 0x63, 0xff, 0x23, 0x32, 0x58, 0xff, 0x26, 0x36, 0x5c, 0xff, 0x28, 0x39, 0x5d, 0xff, 0x26, 0x36, 0x5f, 0xff, 0x2c, 0x3f, 0x6a, 0xff, 0x2f, 0x43, 0x70, 0xff, 0x37, 0x4a, 0x78, 0xff, 0x37, 0x4d, 0x7c, 0xff, 0x31, 0x49, 0x79, 0xff, 0x34, 0x4c, 0x7a, 0xff, 0x32, 0x4b, 0x78, 0xff, 0x2c, 0x44, 0x72, 0xff, 0x24, 0x3f, 0x6d, 0xff, 0x23, 0x3d, 0x6c, 0xff, 0x22, 0x3c, 0x6b, 0xff, 0x22, 0x3c, 0x6b, 0xff, 0x23, 0x3d, 0x6d, 0xff, 0x28, 0x41, 0x71, 0xff, 0x48, 0x5a, 0x84, 0xff, 0x6a, 0x75, 0x98, 0xc1, 0x73, 0x7a, 0x99, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x73, 0x5e, 0x50, 0x0d, 0x68, 0x58, 0x53, 0x93, 0x5e, 0x53, 0x53, 0xfb, 0x5d, 0x54, 0x55, 0xff, 0x67, 0x58, 0x5a, 0xff, 0x65, 0x58, 0x55, 0xff, 0x76, 0x62, 0x56, 0xff, 0x9d, 0x82, 0x6c, 0xff, 0xb9, 0x9a, 0x7e, 0xff, 0xc0, 0xa5, 0x8d, 0xff, 0xc2, 0xaa, 0x90, 0xff, 0xbd, 0xa7, 0x89, 0xff, 0xaa, 0x97, 0x8a, 0xff, 0xb9, 0xa7, 0xb0, 0xff, 0xbe, 0xae, 0xbf, 0xff, 0xc2, 0xb5, 0xbb, 0xff, 0xcb, 0xbe, 0xa6, 0xff, 0xcd, 0xdd, 0xe1, 0xff, 0xd2, 0xf0, 0xfe, 0xff, 0xc7, 0xdd, 0xf1, 0xff, 0x83, 0x94, 0xaa, 0xff, 0x73, 0x84, 0xa5, 0xff, 0x6a, 0x7d, 0xa4, 0xff, 0x61, 0x75, 0x9d, 0xff, 0x56, 0x6a, 0x90, 0xff, 0x50, 0x64, 0x8a, 0xff, 0x48, 0x59, 0x7e, 0xff, 0x3c, 0x4c, 0x6f, 0xff, 0x3e, 0x4d, 0x70, 0xff, 0x3a, 0x48, 0x6e, 0xff, 0x2a, 0x3a, 0x64, 0xff, 0x29, 0x39, 0x62, 0xff, 0x1f, 0x30, 0x58, 0xff, 0x21, 0x31, 0x58, 0xff, 0x25, 0x36, 0x5b, 0xff, 0x2b, 0x3c, 0x63, 0xff, 0x37, 0x4a, 0x75, 0xff, 0x3b, 0x4e, 0x7a, 0xff, 0x37, 0x4b, 0x79, 0xff, 0x31, 0x47, 0x74, 0xff, 0x32, 0x4a, 0x7a, 0xff, 0x33, 0x4a, 0x78, 0xff, 0x31, 0x49, 0x76, 0xff, 0x2c, 0x43, 0x71, 0xff, 0x26, 0x3e, 0x6c, 0xff, 0x22, 0x3c, 0x69, 0xff, 0x20, 0x39, 0x67, 0xff, 0x21, 0x3a, 0x68, 0xff, 0x1f, 0x38, 0x66, 0xff, 0x22, 0x3a, 0x6b, 0xfb, 0x2f, 0x46, 0x73, 0x93, 0x5a, 0x6b, 0x92, 0x0d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x69, 0x51, 0x56, 0x03, 0x5d, 0x52, 0x53, 0x63, 0x5c, 0x52, 0x53, 0xe8, 0x63, 0x52, 0x53, 0xff, 0x66, 0x56, 0x51, 0xff, 0x82, 0x65, 0x54, 0xff, 0xa8, 0x86, 0x6e, 0xff, 0xbd, 0xa0, 0x83, 0xff, 0xc1, 0xa6, 0x8d, 0xff, 0xc1, 0xaa, 0x8b, 0xff, 0xbc, 0xa8, 0x9f, 0xff, 0xca, 0xbc, 0xd1, 0xff, 0xc5, 0xba, 0xe4, 0xff, 0xc0, 0xb1, 0xdd, 0xff, 0xcf, 0xc0, 0xe3, 0xff, 0xe9, 0xe9, 0xe7, 0xff, 0xef, 0xf1, 0xf6, 0xff, 0xc5, 0xe5, 0xf5, 0xff, 0xc5, 0xe3, 0xf9, 0xff, 0xb7, 0xca, 0xdf, 0xff, 0x7c, 0x8e, 0xaa, 0xff, 0x70, 0x85, 0xa9, 0xff, 0x62, 0x78, 0x9c, 0xff, 0x57, 0x6d, 0x91, 0xff, 0x58, 0x6d, 0x91, 0xff, 0x55, 0x69, 0x8d, 0xff, 0x53, 0x66, 0x8a, 0xff, 0x3a, 0x4d, 0x71, 0xff, 0x3d, 0x4f, 0x72, 0xff, 0x34, 0x45, 0x6e, 0xff, 0x2c, 0x3d, 0x66, 0xff, 0x25, 0x36, 0x5e, 0xff, 0x27, 0x38, 0x5f, 0xff, 0x24, 0x37, 0x5c, 0xff, 0x36, 0x48, 0x70, 0xff, 0x38, 0x4b, 0x75, 0xff, 0x35, 0x48, 0x74, 0xff, 0x34, 0x48, 0x75, 0xff, 0x35, 0x4a, 0x77, 0xff, 0x32, 0x48, 0x76, 0xff, 0x31, 0x48, 0x75, 0xff, 0x30, 0x46, 0x73, 0xff, 0x2d, 0x44, 0x71, 0xff, 0x28, 0x3e, 0x6d, 0xff, 0x22, 0x3a, 0x68, 0xff, 0x1f, 0x36, 0x64, 0xff, 0x1d, 0x35, 0x63, 0xff, 0x1c, 0x34, 0x62, 0xe8, 0x1e, 0x3a, 0x67, 0x63, 0x21, 0x3b, 0x69, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x4d, 0x4c, 0x20, 0x5d, 0x4d, 0x49, 0xc0, 0x66, 0x51, 0x49, 0xfa, 0x83, 0x5f, 0x4c, 0xff, 0xa3, 0x7c, 0x64, 0xff, 0xbf, 0x9e, 0x88, 0xff, 0xc4, 0xad, 0x8e, 0xff, 0xc3, 0xad, 0x99, 0xff, 0xbc, 0xaa, 0xcc, 0xff, 0xc7, 0xb9, 0xea, 0xff, 0xbe, 0xb4, 0xe8, 0xff, 0xbf, 0xb6, 0xe9, 0xff, 0xc8, 0xc3, 0xeb, 0xff, 0xee, 0xf0, 0xf4, 0xff, 0xea, 0xec, 0xf3, 0xff, 0xca, 0xe6, 0xf2, 0xff, 0xbf, 0xe1, 0xf3, 0xff, 0xc5, 0xe2, 0xf8, 0xff, 0xa5, 0xbc, 0xd6, 0xff, 0x6b, 0x83, 0xa2, 0xff, 0x6b, 0x85, 0xa7, 0xff, 0x66, 0x7f, 0xa1, 0xff, 0x56, 0x6f, 0x94, 0xff, 0x4e, 0x65, 0x89, 0xff, 0x47, 0x5d, 0x80, 0xff, 0x48, 0x5c, 0x80, 0xff, 0x42, 0x55, 0x79, 0xff, 0x3d, 0x50, 0x73, 0xff, 0x2f, 0x41, 0x65, 0xff, 0x2e, 0x3f, 0x65, 0xff, 0x2a, 0x3b, 0x62, 0xff, 0x2a, 0x3d, 0x63, 0xff, 0x32, 0x43, 0x6c, 0xff, 0x30, 0x42, 0x6e, 0xff, 0x36, 0x48, 0x77, 0xff, 0x37, 0x4a, 0x79, 0xff, 0x35, 0x4b, 0x78, 0xff, 0x34, 0x4b, 0x77, 0xff, 0x33, 0x4b, 0x75, 0xff, 0x2e, 0x46, 0x70, 0xff, 0x2c, 0x43, 0x70, 0xff, 0x29, 0x40, 0x6d, 0xff, 0x22, 0x3a, 0x65, 0xff, 0x1d, 0x36, 0x61, 0xfa, 0x1b, 0x33, 0x5e, 0xc0, 0x1c, 0x34, 0x60, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x58, 0x46, 0x41, 0x05, 0x5e, 0x49, 0x40, 0x53, 0x75, 0x55, 0x43, 0xdf, 0x97, 0x77, 0x59, 0xff, 0xbc, 0x9d, 0x8c, 0xff, 0xbb, 0xac, 0x8f, 0xff, 0xb9, 0xa6, 0xab, 0xff, 0xb4, 0xa5, 0xdb, 0xff, 0xc0, 0xb3, 0xec, 0xff, 0xba, 0xaf, 0xea, 0xff, 0xb7, 0xaf, 0xeb, 0xff, 0xce, 0xcb, 0xe7, 0xff, 0xf5, 0xf6, 0xf6, 0xff, 0xf4, 0xf7, 0xfb, 0xff, 0xcf, 0xec, 0xf2, 0xff, 0xb9, 0xde, 0xee, 0xff, 0xba, 0xe1, 0xf8, 0xff, 0xbc, 0xd8, 0xf0, 0xff, 0x89, 0xa1, 0xbe, 0xff, 0x67, 0x82, 0xa8, 0xff, 0x6c, 0x86, 0xad, 0xff, 0x63, 0x7c, 0xa8, 0xff, 0x5c, 0x74, 0x9d, 0xff, 0x5b, 0x71, 0x9a, 0xff, 0x54, 0x68, 0x92, 0xff, 0x47, 0x5a, 0x85, 0xff, 0x38, 0x4b, 0x6e, 0xff, 0x3b, 0x4c, 0x72, 0xff, 0x31, 0x41, 0x6c, 0xff, 0x31, 0x40, 0x6c, 0xff, 0x31, 0x43, 0x6b, 0xff, 0x2c, 0x3d, 0x68, 0xff, 0x32, 0x45, 0x73, 0xff, 0x33, 0x44, 0x74, 0xff, 0x34, 0x46, 0x77, 0xff, 0x31, 0x45, 0x73, 0xff, 0x2d, 0x46, 0x6f, 0xff, 0x2d, 0x46, 0x6e, 0xff, 0x2a, 0x42, 0x6c, 0xff, 0x27, 0x3f, 0x6a, 0xff, 0x24, 0x3b, 0x68, 0xff, 0x21, 0x3a, 0x62, 0xdf, 0x1d, 0x35, 0x5e, 0x53, 0x1b, 0x33, 0x5b, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0x44, 0x2f, 0x0c, 0x90, 0x6f, 0x5b, 0x81, 0xb4, 0x97, 0x86, 0xdf, 0xb3, 0xa8, 0x8c, 0xff, 0xad, 0xa1, 0xab, 0xff, 0xac, 0xa4, 0xd4, 0xff, 0xc0, 0xb8, 0xe8, 0xff, 0xc9, 0xc1, 0xe8, 0xff, 0xe0, 0xda, 0xf2, 0xff, 0xf4, 0xf5, 0xf9, 0xff, 0xf9, 0xf7, 0xf6, 0xff, 0xf9, 0xf6, 0xf9, 0xff, 0xd7, 0xec, 0xf2, 0xff, 0xc4, 0xe4, 0xf2, 0xff, 0xb5, 0xdd, 0xef, 0xff, 0xb6, 0xd9, 0xf5, 0xff, 0xa2, 0xbe, 0xe0, 0xff, 0x74, 0x8e, 0xb2, 0xff, 0x6e, 0x89, 0xb0, 0xff, 0x67, 0x81, 0xad, 0xff, 0x51, 0x67, 0x95, 0xff, 0x5a, 0x72, 0x9e, 0xff, 0x5c, 0x73, 0x9e, 0xff, 0x47, 0x5a, 0x84, 0xff, 0x44, 0x54, 0x7e, 0xff, 0x3d, 0x4e, 0x79, 0xff, 0x3d, 0x4c, 0x78, 0xff, 0x2e, 0x3e, 0x6a, 0xff, 0x30, 0x40, 0x6a, 0xff, 0x34, 0x44, 0x6f, 0xff, 0x36, 0x47, 0x74, 0xff, 0x34, 0x48, 0x77, 0xff, 0x31, 0x45, 0x74, 0xff, 0x2f, 0x45, 0x70, 0xff, 0x2c, 0x43, 0x6c, 0xff, 0x2d, 0x44, 0x6d, 0xff, 0x2a, 0x41, 0x6b, 0xff, 0x27, 0x3e, 0x68, 0xdf, 0x23, 0x3a, 0x65, 0x82, 0x1f, 0x36, 0x60, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xb1, 0x99, 0x82, 0x13, 0xaa, 0x9d, 0x90, 0x64, 0xaa, 0xa0, 0xab, 0xe4, 0xae, 0xa6, 0xc9, 0xfb, 0xbc, 0xb4, 0xd4, 0xff, 0xef, 0xeb, 0xf5, 0xff, 0xfb, 0xfb, 0xf2, 0xff, 0xf3, 0xf4, 0xf5, 0xff, 0xf6, 0xf4, 0xf5, 0xff, 0xf6, 0xf4, 0xf7, 0xff, 0xd8, 0xec, 0xf2, 0xff, 0xc6, 0xe3, 0xef, 0xff, 0xba, 0xe3, 0xf3, 0xff, 0xae, 0xd6, 0xf2, 0xff, 0xaa, 0xc8, 0xec, 0xff, 0x8d, 0xa7, 0xcc, 0xff, 0x66, 0x83, 0xaa, 0xff, 0x6a, 0x88, 0xb2, 0xff, 0x62, 0x7a, 0xa7, 0xff, 0x5b, 0x74, 0xa0, 0xff, 0x55, 0x6e, 0x98, 0xff, 0x4f, 0x64, 0x8d, 0xff, 0x4a, 0x5d, 0x88, 0xff, 0x40, 0x52, 0x7d, 0xff, 0x3b, 0x4b, 0x76, 0xff, 0x34, 0x45, 0x6f, 0xff, 0x36, 0x48, 0x71, 0xff, 0x34, 0x46, 0x71, 0xff, 0x35, 0x48, 0x75, 0xff, 0x34, 0x48, 0x74, 0xff, 0x32, 0x45, 0x71, 0xff, 0x2f, 0x42, 0x6d, 0xff, 0x2b, 0x3e, 0x68, 0xfb, 0x29, 0x3f, 0x68, 0xe4, 0x2a, 0x40, 0x6a, 0x64, 0x27, 0x3d, 0x67, 0x13, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x8c, 0x91, 0x74, 0x07, 0xb6, 0xb6, 0xb6, 0x51, 0xed, 0xed, 0xed, 0xa1, 0xf9, 0xf6, 0xf8, 0xe8, 0xf5, 0xf2, 0xf6, 0xff, 0xf2, 0xf3, 0xf3, 0xff, 0xf3, 0xf3, 0xf3, 0xff, 0xf3, 0xf4, 0xf7, 0xff, 0xd1, 0xe9, 0xf0, 0xff, 0xc0, 0xe0, 0xef, 0xff, 0xb2, 0xdd, 0xef, 0xff, 0xab, 0xd4, 0xf2, 0xff, 0xa6, 0xc9, 0xef, 0xff, 0xa4, 0xc3, 0xeb, 0xff, 0x6d, 0x8b, 0xb4, 0xff, 0x68, 0x87, 0xaf, 0xff, 0x69, 0x84, 0xaf, 0xff, 0x59, 0x71, 0x9d, 0xff, 0x59, 0x71, 0x9b, 0xff, 0x57, 0x70, 0x98, 0xff, 0x51, 0x65, 0x8f, 0xff, 0x48, 0x5a, 0x85, 0xff, 0x40, 0x51, 0x7c, 0xff, 0x3c, 0x4d, 0x78, 0xff, 0x35, 0x47, 0x73, 0xff, 0x34, 0x48, 0x73, 0xff, 0x34, 0x49, 0x73, 0xff, 0x33, 0x48, 0x72, 0xff, 0x32, 0x43, 0x6f, 0xe8, 0x30, 0x43, 0x6e, 0xa1, 0x2d, 0x41, 0x6a, 0x51, 0x26, 0x3a, 0x63, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xfb, 0xfe, 0xf6, 0x03, 0xf2, 0xf2, 0xf3, 0x0a, 0xf0, 0xf2, 0xf3, 0x4a, 0xf2, 0xf2, 0xf4, 0xa8, 0xf3, 0xf3, 0xf2, 0xe1, 0xf0, 0xf2, 0xf4, 0xee, 0xcc, 0xe5, 0xeb, 0xfa, 0xbc, 0xdc, 0xf0, 0xff, 0xaa, 0xd5, 0xeb, 0xff, 0x9f, 0xc9, 0xec, 0xff, 0xa1, 0xc5, 0xf0, 0xff, 0xa1, 0xc4, 0xee, 0xff, 0x81, 0xa1, 0xca, 0xff, 0x67, 0x85, 0xae, 0xff, 0x63, 0x82, 0xad, 0xff, 0x56, 0x6f, 0x9a, 0xff, 0x5f, 0x75, 0x9f, 0xff, 0x56, 0x72, 0x99, 0xff, 0x53, 0x69, 0x93, 0xff, 0x44, 0x58, 0x83, 0xff, 0x3b, 0x4c, 0x77, 0xff, 0x39, 0x49, 0x75, 0xfa, 0x3b, 0x4d, 0x7c, 0xee, 0x39, 0x4e, 0x79, 0xe1, 0x37, 0x4e, 0x78, 0xa8, 0x36, 0x4a, 0x74, 0x4a, 0x34, 0x43, 0x6f, 0x0a, 0x31, 0x44, 0x6d, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xf9, 0xf4, 0xf5, 0x0b, 0xd9, 0xe7, 0xec, 0x28, 0xc2, 0xe1, 0xec, 0x41, 0xb5, 0xd9, 0xec, 0x5d, 0xa4, 0xcf, 0xed, 0x88, 0x9c, 0xc1, 0xeb, 0xb2, 0x9d, 0xbd, 0xe9, 0xd1, 0xa3, 0xc7, 0xf1, 0xe7, 0x98, 0xbc, 0xe7, 0xf4, 0x72, 0x8f, 0xba, 0xfd, 0x6c, 0x88, 0xb1, 0xfd, 0x61, 0x7d, 0xa6, 0xf4, 0x58, 0x71, 0x9c, 0xe7, 0x5b, 0x74, 0xa1, 0xd1, 0x55, 0x6b, 0x95, 0xb2, 0x4f, 0x65, 0x8f, 0x88, 0x3e, 0x52, 0x7c, 0x5d, 0x3d, 0x4f, 0x7a, 0x41, 0x3b, 0x50, 0x7a, 0x28, 0x3c, 0x51, 0x78, 0x0b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,

};

const lv_img_dsc_t img_benchmark_avatar = {
    .header.magic = LV_IMAGE_HEADER_MAGIC,
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .header.flags = 0,
    .header.w = 80,
    .header.h = 80,
    .header.stride = 320,
    .data_size = 25600,
    .data = img_benchmark_avatar_map,
};

#endif /*LV_USE_DEMO_BENCHMARK*/
