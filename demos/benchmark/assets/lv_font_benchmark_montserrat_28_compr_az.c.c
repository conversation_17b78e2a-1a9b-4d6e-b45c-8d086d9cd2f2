#include "../../../lvgl.h"

#if LV_USE_DEMO_BENCHMARK


/*******************************************************************************
 * Size: 28 px
 * Bpp: 4
 * Opts:
 ******************************************************************************/

#ifndef LV_FONT_BENCHMARK_MONTSERRAT_28_COMPR_AZ
#define LV_FONT_BENCHMARK_MONTSERRAT_28_COMPR_AZ 1
#endif

#if LV_FONT_BENCHMARK_MONTSERRAT_28_COMPR_AZ

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+20 " " */

    /* U+61 "a" */
    0x0, 0xb, 0xdf, 0x7f, 0xb6, 0x90, 0x3, 0x47,
    0x42, 0x8, 0x0, 0x96, 0xe4, 0x2, 0x50, 0x15,
    0x9a, 0x95, 0x0, 0x33, 0x0, 0x1d, 0x1d, 0x4c,
    0xad, 0x5a, 0x0, 0x80, 0x1, 0xb8, 0x7, 0x89,
    0xc0, 0x48, 0x3, 0xfd, 0xc0, 0x6, 0x0, 0x14,
    0x67, 0x73, 0xfe, 0x90, 0xc, 0xda, 0xe6, 0x22,
    0x0, 0xf0, 0x94, 0x80, 0xd7, 0x7f, 0xe9, 0x0,
    0x94, 0x1, 0x4a, 0x20, 0x1f, 0xb8, 0x0, 0xa0,
    0x1e, 0xe0, 0xb, 0x80, 0x8, 0x1, 0xc4, 0xe0,
    0x12, 0x80, 0x2d, 0x40, 0x2, 0xd8, 0x1, 0x8a,
    0x80, 0x6b, 0xfd, 0xd2, 0x2c, 0x1, 0x97, 0x58,
    0x40, 0x4, 0xdb, 0x40, 0x10,

    /* U+62 "b" */
    0x7f, 0xf1, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xf9,
    0x4b, 0x7d, 0xfb, 0x6a, 0x1, 0xf3, 0x65, 0x20,
    0x81, 0x25, 0x69, 0x0, 0x75, 0x98, 0x2c, 0x4b,
    0x88, 0x16, 0x90, 0x6, 0x12, 0xda, 0x76, 0x8e,
    0x60, 0x7, 0x80, 0x77, 0x90, 0x7, 0x4a, 0x81,
    0x28, 0x4, 0xc4, 0x1, 0xf4, 0x0, 0x3c, 0x2,
    0xc0, 0xf, 0xc4, 0x20, 0xc0, 0x13, 0x0, 0x7f,
    0x18, 0x10, 0x4, 0xc0, 0x1f, 0xc6, 0x4, 0x1,
    0x60, 0x7, 0xe3, 0x10, 0x60, 0x9, 0x88, 0x3,
    0xe8, 0x0, 0x78, 0x6, 0xf2, 0x0, 0xe9, 0x40,
    0x25, 0x0, 0x88, 0x9b, 0x4e, 0xd1, 0xcc, 0x0,
    0xf0, 0xd, 0xc6, 0xb, 0x12, 0xe2, 0x5, 0xa4,
    0x1, 0x87, 0x29, 0x4, 0x5, 0x2b, 0x48, 0x0,

    /* U+63 "c" */
    0x0, 0xc7, 0x3b, 0xfe, 0xd9, 0x30, 0xe, 0x1b,
    0xc6, 0x20, 0x1, 0x36, 0x48, 0x4, 0x38, 0x80,
    0x2d, 0x32, 0x60, 0x3, 0x40, 0x2, 0x88, 0x17,
    0xa5, 0x99, 0x3c, 0x60, 0x11, 0xa0, 0x1d, 0x0,
    0x70, 0xee, 0xa0, 0x2c, 0x1, 0x0, 0x1f, 0x9,
    0x0, 0xc, 0x0, 0x80, 0x1f, 0xf3, 0x80, 0x80,
    0x7f, 0xf0, 0x1c, 0x4, 0x3, 0xff, 0x80, 0x60,
    0x4, 0x0, 0xff, 0xa8, 0x1, 0x0, 0x1f, 0x9,
    0x0, 0x9, 0x0, 0xe8, 0x3, 0x87, 0x75, 0x0,
    0xa, 0x20, 0x5e, 0x96, 0x64, 0xf1, 0x80, 0x61,
    0xc4, 0x1, 0x69, 0x93, 0x0, 0x1e, 0x0, 0x21,
    0xbc, 0x62, 0x0, 0x13, 0x64, 0x0, 0x0,

    /* U+64 "d" */
    0x0, 0xff, 0xe0, 0x17, 0xfa, 0x0, 0x3f, 0xff,
    0xe0, 0x1f, 0xcb, 0x5b, 0xfd, 0x8c, 0x1, 0xf1,
    0x65, 0x29, 0x0, 0x9c, 0xeb, 0x0, 0x61, 0xc3,
    0x1, 0x69, 0x92, 0x81, 0x50, 0x6, 0xd1, 0x5,
    0xe9, 0x66, 0x57, 0x18, 0x80, 0x46, 0x60, 0x3a,
    0x0, 0xe1, 0xd1, 0x0, 0xa8, 0x1, 0x0, 0x1f,
    0xb, 0x0, 0x44, 0x0, 0x40, 0xf, 0xd6, 0x1,
    0x38, 0x8, 0x7, 0xf0, 0x80, 0x4e, 0x2, 0x1,
    0xfc, 0x20, 0x11, 0x0, 0xc, 0x3, 0xf2, 0x0,
    0x54, 0x0, 0x80, 0xf, 0xd4, 0x1, 0x19, 0x81,
    0x20, 0x3, 0xd6, 0x40, 0x1b, 0x44, 0x1f, 0x18,
    0xd1, 0xf9, 0x4, 0x3, 0xe, 0x18, 0x1c, 0xe5,
    0xc0, 0x8b, 0x40, 0x38, 0xb2, 0x90, 0x80, 0x4e,
    0x38, 0xc0, 0x20,

    /* U+65 "e" */
    0x0, 0xc9, 0x7b, 0xfd, 0x8c, 0x20, 0x1c, 0x39,
    0x68, 0x40, 0x27, 0x3e, 0x80, 0x10, 0xf9, 0x82,
    0x4d, 0xd3, 0x0, 0xda, 0x0, 0x28, 0x42, 0xec,
    0xc8, 0xb3, 0xa4, 0x14, 0x26, 0x81, 0x28, 0x1,
    0xc5, 0xe0, 0x2f, 0x40, 0x6, 0x0, 0xf8, 0x90,
    0x34, 0x80, 0x1f, 0xff, 0xc8, 0xc, 0xe0, 0x1f,
    0xfc, 0x13, 0x70, 0x7, 0xff, 0xfd, 0xc6, 0x0,
    0x40, 0xf, 0xfa, 0xc0, 0x10, 0x1, 0xf8, 0x40,
    0x6, 0x80, 0x76, 0x20, 0x1c, 0xbe, 0x60, 0xa,
    0x20, 0x4e, 0x96, 0x64, 0x65, 0xf, 0x80, 0x7,
    0x10, 0x0, 0xd3, 0x27, 0x30, 0x2e, 0x0, 0x86,
    0xf1, 0x88, 0x40, 0x56, 0xb4, 0x80,

    /* U+66 "f" */
    0x0, 0xc3, 0x1b, 0xfd, 0x68, 0x1, 0xe, 0x39,
    0x0, 0xa6, 0x80, 0x50, 0x40, 0xb7, 0x66, 0xa0,
    0x9, 0xc0, 0xa9, 0x11, 0x26, 0x1, 0x8, 0x20,
    0x7, 0xc2, 0x1, 0xfa, 0x7f, 0x80, 0xd, 0xff,
    0x70, 0x7, 0xff, 0x2, 0xf2, 0xc0, 0x9, 0x99,
    0x68, 0x11, 0xa8, 0x0, 0x4c, 0xf0, 0x7, 0xff,
    0xfc, 0x3, 0xff, 0xc6,

    /* U+67 "g" */
    0x0, 0xcb, 0x7b, 0xfd, 0x8e, 0x21, 0x9f, 0x80,
    0x3, 0xda, 0x42, 0x1, 0x38, 0xf4, 0x0, 0xc7,
    0x84, 0x4, 0xf3, 0x27, 0x11, 0x59, 0x0, 0x5c,
    0x0, 0x7d, 0x86, 0x64, 0x73, 0x80, 0x80, 0x14,
    0x81, 0xa0, 0x3, 0xd0, 0xc0, 0x17, 0x80, 0x2c,
    0x3, 0xf5, 0x80, 0x4c, 0x2, 0x40, 0x1f, 0x88,
    0x40, 0x30, 0x80, 0x7f, 0x84, 0x0, 0xc0, 0x2,
    0x0, 0xfc, 0x40, 0x17, 0x80, 0x20, 0x3, 0xf4,
    0x0, 0x4a, 0x40, 0xb2, 0x1, 0xe9, 0x50, 0xd,
    0xe0, 0x6, 0xe9, 0x66, 0x4f, 0x30, 0x7, 0x16,
    0x10, 0xb, 0x4c, 0x98, 0x45, 0x60, 0x7, 0x0,
    0x1e, 0xd2, 0x10, 0x9, 0xc7, 0xa0, 0x0, 0x40,
    0x32, 0xde, 0xff, 0x63, 0x89, 0x0, 0xc, 0x3,
    0xff, 0x81, 0x40, 0xc, 0x3, 0xe6, 0x0, 0xf9,
    0x5c, 0x5, 0x43, 0xc6, 0x7a, 0x9d, 0x99, 0x19,
    0x40, 0xb, 0x0, 0x71, 0x80, 0xac, 0x4c, 0x9c,
    0xc0, 0x12, 0xa0, 0x2, 0xcc, 0x3a, 0x8, 0x0,
    0x52, 0x39, 0x80, 0x0,

    /* U+68 "h" */
    0x7f, 0xf1, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xf8,
    0x8d, 0x7d, 0xfd, 0x6a, 0x1, 0xe6, 0xd9, 0x41,
    0x1, 0x4a, 0xb0, 0xe, 0xa2, 0x16, 0x88, 0x28,
    0x1, 0x24, 0x3, 0x93, 0xa5, 0xdd, 0x58, 0x0,
    0x62, 0x0, 0x86, 0xc0, 0x38, 0xe0, 0x0, 0x80,
    0x14, 0x80, 0x7c, 0x80, 0xe, 0x0, 0x8c, 0x3,
    0xe2, 0x0, 0x8, 0x4, 0xe0, 0x1f, 0xe3, 0x0,
    0xff, 0xff, 0x80, 0x7f, 0xf4, 0x80,

    /* U+69 "i" */
    0x5e, 0xd3, 0x81, 0x29, 0x60, 0x6, 0xdc, 0xd3,
    0x83, 0x28, 0x7, 0x3f, 0xf8, 0xc0, 0x3f, 0xfd,
    0x80,

    /* U+6A "j" */
    0x0, 0xe3, 0xee, 0x20, 0x7, 0x40, 0x8a, 0x0,
    0x38, 0x80, 0xa, 0x1, 0xd3, 0x38, 0x3, 0xcc,
    0xc0, 0xf, 0xfe, 0x1a, 0xff, 0x98, 0x3, 0xff,
    0xfe, 0x1, 0xff, 0xef, 0x30, 0xe, 0xc0, 0x3,
    0x5, 0xb2, 0xc3, 0x0, 0x9a, 0xa4, 0xd3, 0x80,
    0x28, 0x1d, 0x48, 0x5, 0x31, 0x0,

    /* U+6B "k" */
    0x7f, 0xf1, 0x80, 0x7f, 0xff, 0xc0, 0x3f, 0xf9,
    0xe9, 0xfe, 0xe2, 0x0, 0xff, 0x2d, 0x80, 0xe1,
    0x0, 0x7f, 0x35, 0x0, 0xe1, 0x0, 0x7f, 0x3c,
    0x80, 0xe1, 0x0, 0x7f, 0x44, 0x0, 0x70, 0x80,
    0x3f, 0xa5, 0xc0, 0x70, 0x80, 0x3f, 0x3d, 0x30,
    0x1, 0xc8, 0x3, 0xfa, 0x94, 0x2, 0x65, 0x0,
    0xfe, 0x10, 0x3a, 0x0, 0x51, 0x0, 0x7f, 0x26,
    0x2b, 0x80, 0x3c, 0x3, 0xf2, 0xd8, 0x2, 0x10,
    0xa, 0x80, 0x3e, 0x90, 0xd, 0x64, 0xa, 0xe0,
    0x1f, 0xfc, 0xf, 0x0, 0x42, 0x0, 0x7f, 0xc5,
    0x40, 0xa, 0x10, 0xf, 0xf9, 0x5c, 0x7, 0x40,

    /* U+6C "l" */
    0x7f, 0xf1, 0x80, 0x7f, 0xff, 0xc0, 0x38,

    /* U+6D "m" */
    0x7f, 0xf1, 0xc, 0x67, 0x7e, 0xc9, 0x80, 0x45,
    0x1b, 0xfe, 0xd9, 0x20, 0xf, 0x3f, 0xb9, 0x88,
    0x13, 0x63, 0x83, 0xeb, 0x90, 0x0, 0x9b, 0x58,
    0x3, 0xa8, 0x4e, 0x6e, 0x94, 0x1, 0x15, 0x0,
    0x73, 0x74, 0xa0, 0x9, 0x30, 0xe, 0x7c, 0x64,
    0x5a, 0x90, 0x2, 0x83, 0xe3, 0x22, 0xd4, 0x0,
    0x20, 0x3, 0x14, 0x0, 0x73, 0x10, 0x0, 0xe0,
    0x3, 0x9c, 0x81, 0x0, 0x35, 0x0, 0x7c, 0x80,
    0xb, 0x0, 0xf9, 0x0, 0x2, 0x1, 0x18, 0x7,
    0xdc, 0x0, 0x30, 0xf, 0x84, 0x0, 0x60, 0x13,
    0x80, 0x7f, 0x9c, 0x3, 0xfc, 0x20, 0x1f, 0xff,
    0xf0, 0xf, 0xff, 0xf8, 0x7, 0xff, 0x50,

    /* U+6E "n" */
    0x7f, 0xf1, 0x3, 0xe7, 0x7f, 0x5a, 0x80, 0x79,
    0xfa, 0xc, 0x40, 0x52, 0xac, 0x3, 0xa8, 0x4a,
    0x2a, 0x8e, 0x20, 0x92, 0x1, 0xcd, 0xae, 0xaa,
    0x8c, 0x10, 0x62, 0x0, 0x8a, 0x40, 0x38, 0xa4,
    0x0, 0x80, 0x15, 0x0, 0x7c, 0xa0, 0xe, 0x0,
    0x8c, 0x3, 0xe3, 0x0, 0x8, 0x4, 0xe0, 0x1f,
    0xe3, 0x0, 0xff, 0xff, 0x80, 0x7f, 0xf4, 0x80,

    /* U+6F "o" */
    0x0, 0xc9, 0x5b, 0xfd, 0xb0, 0x40, 0x1e, 0x1b,
    0xb2, 0x90, 0x9, 0x3e, 0xc0, 0x6, 0x1f, 0x40,
    0x16, 0x99, 0x28, 0x1, 0xec, 0x2, 0xa1, 0x5,
    0xe9, 0x66, 0x57, 0x18, 0x23, 0x81, 0xa0, 0x1d,
    0x0, 0x70, 0xe8, 0x84, 0x85, 0x80, 0x20, 0x3,
    0xe1, 0x90, 0x15, 0x30, 0x2, 0x0, 0x7e, 0x40,
    0x1, 0x38, 0x8, 0x7, 0xf0, 0x80, 0x39, 0xc0,
    0x40, 0x3f, 0x84, 0x1, 0xc6, 0x0, 0x40, 0xf,
    0xd6, 0x0, 0x2b, 0x0, 0x40, 0x7, 0xc2, 0xc0,
    0x2a, 0x68, 0x7, 0x40, 0x1c, 0x3a, 0x21, 0x20,
    0xa, 0x20, 0x5e, 0x96, 0x65, 0x71, 0x82, 0x38,
    0x0, 0x71, 0x0, 0x5a, 0x64, 0xa0, 0x7, 0xb0,
    0xc, 0x37, 0x65, 0x20, 0x12, 0x6d, 0x80, 0x8,

    /* U+70 "p" */
    0x7f, 0xf1, 0x3, 0x5f, 0x7e, 0xda, 0x80, 0x7c,
    0xdb, 0x28, 0x20, 0x49, 0x5a, 0x40, 0x1d, 0x44,
    0x2f, 0x76, 0x93, 0x2, 0xd2, 0x0, 0xc2, 0x7f,
    0x8, 0x86, 0xc8, 0x0, 0x78, 0x6, 0x1d, 0x10,
    0xe, 0x76, 0x2, 0x50, 0x9, 0x84, 0x3, 0xeb,
    0x0, 0x78, 0x5, 0x80, 0x1f, 0x88, 0x41, 0x80,
    0x27, 0x0, 0xfe, 0x30, 0x20, 0x9, 0x80, 0x3f,
    0x8c, 0x8, 0x2, 0xc0, 0xf, 0xc6, 0x20, 0xc0,
    0x13, 0x10, 0x7, 0xd0, 0x0, 0xf0, 0xd, 0xe4,
    0x1, 0xd2, 0x80, 0x4a, 0x1, 0x9, 0x6d, 0x3b,
    0x47, 0x30, 0x3, 0xc0, 0x35, 0x18, 0x2c, 0x4b,
    0x88, 0x16, 0x90, 0x6, 0x7c, 0xa4, 0x10, 0x14,
    0xad, 0x20, 0xf, 0x96, 0xfb, 0xfa, 0xd4, 0x3,
    0xff, 0xf0,

    /* U+71 "q" */
    0x0, 0xcb, 0x5b, 0xfd, 0x8c, 0x0, 0xff, 0x40,
    0x0, 0xb2, 0x94, 0x80, 0x4e, 0x74, 0x80, 0x30,
    0xe1, 0x80, 0xb4, 0xc9, 0x40, 0xb0, 0x3, 0x68,
    0x82, 0xf4, 0xb3, 0x2b, 0x4c, 0x40, 0x23, 0x30,
    0x1d, 0x0, 0x71, 0x68, 0x80, 0x54, 0x0, 0x80,
    0xf, 0x86, 0x40, 0x22, 0x0, 0x20, 0x7, 0xe4,
    0x0, 0x9c, 0x4, 0x3, 0xf8, 0x40, 0x27, 0x1,
    0x0, 0xfe, 0x10, 0x8, 0x80, 0x8, 0x1, 0xfa,
    0xc0, 0x2a, 0x0, 0x40, 0x7, 0xc2, 0xc0, 0x11,
    0x98, 0xe, 0x80, 0x38, 0x74, 0x40, 0x36, 0x88,
    0x2f, 0x4b, 0x32, 0xb8, 0xc4, 0x3, 0xe, 0x18,
    0xb, 0x4c, 0x94, 0xf, 0x40, 0x38, 0xb2, 0x90,
    0x80, 0x4e, 0x70, 0x40, 0x3e, 0x5b, 0xdf, 0xec,
    0x60, 0xf, 0xff, 0xc8,

    /* U+72 "r" */
    0x7f, 0xf1, 0x3, 0x67, 0x40, 0x4, 0x3b, 0x26,
    0x20, 0x1b, 0x88, 0xd, 0xb4, 0x2, 0x22, 0x6e,
    0x4a, 0x80, 0x6c, 0x20, 0xf, 0x38, 0x80, 0x7d,
    0xa0, 0x1f, 0x98, 0x3, 0xff, 0xf0,

    /* U+73 "s" */
    0x0, 0x8a, 0x77, 0xfd, 0xd9, 0x6, 0x1, 0xa3,
    0x58, 0x80, 0x2, 0x6f, 0x90, 0x0, 0x57, 0x1,
    0x79, 0xa9, 0x61, 0xa, 0x0, 0x70, 0x3, 0x21,
    0x95, 0xa7, 0xb0, 0x80, 0xc, 0x4, 0x40, 0xf,
    0x18, 0x4, 0xc0, 0x4e, 0x1, 0xfe, 0x80, 0x4,
    0x75, 0xc2, 0x88, 0x7, 0x1d, 0x90, 0xa, 0x3d,
    0x76, 0xa8, 0x6, 0x4d, 0xc9, 0x63, 0x0, 0x15,
    0x38, 0x7, 0x1b, 0x4e, 0x7d, 0x8, 0x40, 0x7,
    0xf9, 0x58, 0x0, 0x40, 0xae, 0x1, 0xf1, 0x80,
    0xc, 0x2e, 0x3e, 0x9d, 0x54, 0xf8, 0x80, 0x82,
    0x22, 0x0, 0x2c, 0x55, 0x20, 0xc0, 0xe8, 0x7,
    0x30, 0xe6, 0x20, 0x2, 0x5a, 0xc1, 0x0,

    /* U+74 "t" */
    0x0, 0xa2, 0xa, 0x1, 0xf0, 0xbb, 0xbc, 0x3,
    0xff, 0x9f, 0x3f, 0xc0, 0x5, 0xff, 0xb8, 0x3,
    0xff, 0x81, 0x79, 0x60, 0x4, 0xcc, 0xb4, 0x8,
    0xd4, 0x0, 0x26, 0x78, 0x3, 0xff, 0xf2, 0x20,
    0x1f, 0xfc, 0x14, 0x0, 0xfc, 0xa0, 0x36, 0xaa,
    0xa4, 0x0, 0xa0, 0x41, 0x2a, 0x8b, 0xe0, 0x11,
    0x7b, 0x8, 0xa, 0xc0,

    /* U+75 "u" */
    0x9f, 0xf0, 0x80, 0x7c, 0xbf, 0xe6, 0x0, 0xff,
    0xff, 0x80, 0x7f, 0xf8, 0x44, 0x0, 0x40, 0x1f,
    0x68, 0x5, 0xa0, 0x4, 0x0, 0xf9, 0x80, 0x24,
    0x0, 0x49, 0x0, 0x75, 0x8, 0x4, 0x2e, 0x3,
    0x90, 0x88, 0x6d, 0x50, 0xe, 0x85, 0x1, 0x7b,
    0xb4, 0x90, 0xd0, 0x7, 0x55, 0xa8, 0x80, 0x9c,
    0x73, 0x80, 0x40,

    /* U+76 "v" */
    0xd, 0xfe, 0x0, 0xfe, 0x2f, 0xf3, 0x85, 0x80,
    0xb0, 0x7, 0xeb, 0x0, 0x30, 0x30, 0x85, 0x0,
    0x7c, 0x2c, 0xc, 0x20, 0x6, 0x3, 0x30, 0x7,
    0x98, 0x1, 0x60, 0x15, 0x0, 0x24, 0x3, 0xd2,
    0x8, 0x40, 0x11, 0x28, 0x28, 0x80, 0x65, 0x10,
    0xf0, 0xe, 0x90, 0x4, 0x80, 0x69, 0x2, 0x40,
    0xe, 0x32, 0x6, 0x0, 0x8c, 0xc1, 0x60, 0x1f,
    0x58, 0xa, 0x80, 0x24, 0x0, 0xc0, 0x1f, 0x30,
    0x84, 0x80, 0xa8, 0x30, 0x7, 0xf3, 0x1, 0x99,
    0x80, 0x16, 0x1, 0xfd, 0x20, 0x9, 0x90, 0x21,
    0x0, 0x7f, 0xa, 0x82, 0x88, 0x78, 0x7, 0xfd,
    0x20, 0x11, 0x20, 0x7, 0xfc, 0x66, 0x0, 0x58,
    0x7, 0x80,

    /* U+77 "w" */
    0xaf, 0xe0, 0xf, 0xd5, 0xfc, 0x1, 0xfa, 0x7f,
    0x78, 0x5, 0x0, 0x3c, 0x2a, 0x2, 0xa0, 0x1f,
    0x38, 0x52, 0x8, 0x68, 0x7, 0x9c, 0x2, 0xe0,
    0xf, 0x28, 0x83, 0x3, 0x3, 0x0, 0x7a, 0x80,
    0x24, 0x10, 0xe, 0xe0, 0x41, 0xa, 0x0, 0x28,
    0x6, 0x23, 0xa, 0x0, 0x38, 0x6, 0x14, 0xe,
    0x0, 0x11, 0x87, 0x0, 0x6a, 0x2, 0x54, 0xa,
    0x0, 0xce, 0x2, 0xa0, 0x15, 0x82, 0x8, 0x4,
    0xc1, 0x41, 0xc0, 0x64, 0x1, 0x50, 0x38, 0x6,
    0x60, 0x3, 0x80, 0x10, 0x41, 0x81, 0x44, 0x28,
    0x0, 0x46, 0x14, 0x1, 0x85, 0x2, 0x80, 0x1c,
    0x8, 0x20, 0x7, 0x6, 0x0, 0x50, 0x11, 0x80,
    0x77, 0x1, 0x90, 0xa8, 0x70, 0x5, 0x40, 0x28,
    0xc, 0x14, 0x1, 0xe5, 0x10, 0xa7, 0x0, 0x28,
    0x4, 0x66, 0xd, 0x41, 0x6, 0x0, 0xf9, 0xc1,
    0x68, 0x18, 0x3, 0xa8, 0x1b, 0x81, 0x4, 0x3,
    0xea, 0x2, 0x30, 0xa0, 0xe, 0x70, 0x2, 0x86,
    0x80, 0x7e, 0x32, 0x0, 0x11, 0x80, 0x70, 0xa8,
    0x4, 0xc0, 0x1f, 0xd4, 0x0, 0xa0, 0xf, 0xb8,
    0x0, 0xa0, 0x1c,

    /* U+78 "x" */
    0x1e, 0xfe, 0x10, 0xf, 0x4f, 0xf9, 0x40, 0x74,
    0x7, 0x40, 0x39, 0x58, 0x19, 0x40, 0x7, 0x20,
    0x70, 0x1, 0x15, 0x81, 0xc8, 0x6, 0x65, 0x7,
    0x50, 0x7, 0x8, 0x70, 0x7, 0xa8, 0x82, 0xca,
    0x4c, 0x28, 0x80, 0x3e, 0xf0, 0x1c, 0x60, 0x75,
    0x0, 0xfc, 0x52, 0x2, 0x7, 0x0, 0x1f, 0xe6,
    0x10, 0x4, 0x80, 0x7f, 0xd4, 0x20, 0xb, 0x20,
    0xf, 0xe6, 0x50, 0x50, 0x1e, 0x0, 0xfc, 0x72,
    0x7, 0x54, 0x3, 0x90, 0xf, 0xf, 0x0, 0xe8,
    0x2b, 0x3, 0x28, 0x7, 0x51, 0x5, 0x8, 0x2,
    0x48, 0x28, 0x80, 0x27, 0x40, 0x74, 0x0, 0xde,
    0x0, 0xf0, 0x1, 0xc0, 0x1c, 0x0, 0x71, 0x50,
    0x15, 0x0,

    /* U+79 "y" */
    0xd, 0xfe, 0x0, 0xfe, 0x2f, 0xf3, 0x85, 0x80,
    0xb0, 0x7, 0xeb, 0x0, 0x38, 0x30, 0x85, 0x80,
    0x7c, 0x2c, 0xe, 0x1, 0x48, 0x12, 0x80, 0x7a,
    0x40, 0x12, 0x1, 0x30, 0x2, 0x40, 0x3c, 0xc0,
    0xa2, 0x1, 0xb, 0x1, 0x98, 0x3, 0x30, 0x84,
    0x80, 0x75, 0x80, 0x24, 0x3, 0x58, 0x19, 0x80,
    0x38, 0x94, 0x14, 0x40, 0x8, 0x41, 0x0, 0x1f,
    0x48, 0x2, 0x40, 0x1e, 0x4, 0x80, 0x1f, 0x19,
    0x81, 0xc0, 0x90, 0x2c, 0x3, 0xfa, 0x0, 0xf,
    0x60, 0x2c, 0x1, 0xfc, 0x84, 0x12, 0xc1, 0x20,
    0x1f, 0xf5, 0x80, 0x80, 0x18, 0x3, 0xfe, 0x61,
    0x0, 0x30, 0x80, 0x7f, 0xf0, 0x1c, 0x1, 0x60,
    0x1f, 0xfc, 0x1, 0x60, 0x42, 0x0, 0xf8, 0x40,
    0x34, 0x0, 0x20, 0x3, 0xf7, 0xdb, 0x2d, 0x30,
    0x29, 0x80, 0x7c, 0xc2, 0x93, 0x4a, 0x5, 0x60,
    0x1f, 0x9a, 0x4c, 0x0, 0x51, 0xa2, 0x1, 0xf8,

    /* U+7A "z" */
    0xbf, 0xff, 0xf9, 0x40, 0x3f, 0xf8, 0x3, 0x39,
    0x9f, 0x38, 0x1, 0x10, 0x46, 0x7e, 0x17, 0x2,
    0xa0, 0xf, 0x87, 0xc0, 0x1c, 0x20, 0x1f, 0x51,
    0x5, 0x18, 0x7, 0xd0, 0x80, 0xea, 0x1, 0xf2,
    0x38, 0x24, 0x0, 0x7c, 0x56, 0x3, 0x40, 0x1f,
    0xbc, 0x1, 0xa2, 0x1, 0xf5, 0x10, 0x49, 0x80,
    0x7c, 0xea, 0xc, 0xc0, 0xf, 0x92, 0x0, 0x68,
    0xcf, 0xe1, 0x90, 0x0, 0xe6, 0x7e, 0x93, 0x0,
    0xff, 0xe0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 121, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 268, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 93, .adv_w = 306, .box_w = 16, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 197, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 292, .adv_w = 306, .box_w = 16, .box_h = 21, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 399, .adv_w = 274, .box_w = 15, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 493, .adv_w = 158, .box_w = 11, .box_h = 21, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 545, .adv_w = 309, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 685, .adv_w = 305, .box_w = 15, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 747, .adv_w = 125, .box_w = 4, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 764, .adv_w = 127, .box_w = 9, .box_h = 26, .ofs_x = -3, .ofs_y = -5},
    {.bitmap_index = 810, .adv_w = 276, .box_w = 16, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 898, .adv_w = 125, .box_w = 4, .box_h = 21, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 905, .adv_w = 474, .box_w = 26, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1000, .adv_w = 305, .box_w = 15, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1056, .adv_w = 284, .box_w = 16, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1160, .adv_w = 306, .box_w = 16, .box_h = 20, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 1266, .adv_w = 306, .box_w = 16, .box_h = 20, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 1374, .adv_w = 184, .box_w = 9, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1404, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1499, .adv_w = 185, .box_w = 11, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1551, .adv_w = 303, .box_w = 15, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1602, .adv_w = 250, .box_w = 17, .box_h = 15, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1700, .adv_w = 403, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1855, .adv_w = 247, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1953, .adv_w = 250, .box_w = 17, .box_h = 20, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 2081, .adv_w = 233, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/



/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 1, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 97, .range_length = 26, .glyph_id_start = 2,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 3, 4, 5, 6,
    0, 1, 0, 0, 7, 4, 1, 1,
    2, 2, 8, 9, 10, 11, 0, 12,
    12, 13, 12, 14
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 3, 3, 3, 0,
    3, 2, 4, 5, 2, 2, 4, 4,
    3, 4, 3, 4, 6, 7, 8, 9,
    9, 10, 9, 11
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 1, 0, 0, 0, 0, 0,
    -4, 0, 0, -2, 0, 0, 0, 0,
    0, 0, 0, -7, -8, -4, 0, -3,
    -4, 0, 0, 0, 3, 0, -3, -8,
    -3, 0, 0, 0, 0, 0, 0, 6,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, -4, -7, -2, -4,
    0, -4, 36, 19, 0, 0, 0, 4,
    0, 0, -4, 0, -10, -3, 0, -8,
    0, -4, -13, -9, -5, 0, 0, 0,
    0, 22, 0, 0, 0, 0, 0, 0,
    -4, -3, -5, -3, 0, -1, 7, 0,
    7, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, -4, 0, 0, 0,
    -8, 0, 0, 0, -6, 0, -4, 0,
    0, -8, 0, -7, 0, -7, -3, 7,
    0, -4, -13, -4, -4, 0, -8, 0,
    3, -4, 0, -4, -13, 0, -4, 0,
    0, -4, 0, 0, 0, 1, 0, -4,
    -4, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 14,
    .right_class_cnt     = 11,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

/*Store all the custom data of the font*/
static lv_font_fmt_txt_dsc_t font_dsc = {
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 1
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
lv_font_t lv_font_benchmark_montserrat_28_compr_az = {
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 26,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if LV_FONT_BENCHMARK_MONTSERRAT_28_COMPR_AZ*/

#endif

