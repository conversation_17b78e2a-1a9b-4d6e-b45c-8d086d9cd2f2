# Kconfig file for LVGL v9.2.2

menu "LVGL configuration"

	# Define CONFIG_LV_CONF_SKIP so we can use LVGL
	# without lv_conf.h file, the lv_conf_internal.h and
	# lv_conf_kconfig.h files are used instead.
	config LV_CONF_SKIP
		bool "Check this to not use custom lv_conf.h"
		default y

	config LV_CONF_MINIMAL
		bool "LVGL minimal configuration"

	menu "Color Settings"
		choice LV_COLOR_DEPTH
			prompt "Color depth"
			default LV_COLOR_DEPTH_16
			help
				Color depth to be used.

			config LV_COLOR_DEPTH_32
				bool "32: XRGB8888"
			config LV_COLOR_DEPTH_24
				bool "24: RGB888"
			config LV_COLOR_DEPTH_16
				bool "16: RGB565"
			config LV_COLOR_DEPTH_8
				bool "8: RGB232"
			config LV_COLOR_DEPTH_1
				bool "1: 1 byte per pixel"
		endchoice

		config LV_COLOR_DEPTH
			int
			default 1 if LV_COLOR_DEPTH_1
			default 8 if LV_COLOR_DEPTH_8
			default 16 if LV_COLOR_DEPTH_16
			default 24 if LV_COLOR_DEPTH_24
			default 32 if LV_COLOR_DEPTH_32
	endmenu

	menu "Memory Settings"
		choice
			prompt "Malloc functions source"
			default LV_USE_BUILTIN_MALLOC

		config LV_USE_BUILTIN_MALLOC
			bool "LVGL's built in implementation"

		config LV_USE_CLIB_MALLOC
			bool "Standard C functions malloc/realloc/free"

		config LV_USE_MICROPYTHON_MALLOC
			bool "MicroPython functions malloc/realloc/free"

		config LV_USE_RTTHREAD_MALLOC
			bool "RTThread functions malloc/realloc/free"

		config LV_USE_CUSTOM_MALLOC
			bool "Implement the functions externally"

		endchoice # "Malloc functions"

		choice
			prompt "String functions source"
			default LV_USE_BUILTIN_STRING

		config LV_USE_BUILTIN_STRING
			bool "LVGL's built in implementation"

		config LV_USE_CLIB_STRING
			bool "Standard C functions memcpy/memset/strlen/strcpy"

		config LV_USE_CUSTOM_STRING
			bool "Implement the functions externally"

		endchoice # "String functions"

		choice
			prompt "Sprintf functions source"
			default LV_USE_BUILTIN_SPRINTF

		config LV_USE_BUILTIN_SPRINTF
			bool "LVGL's built in implementation"

		config LV_USE_CLIB_SPRINTF
			bool "Standard C functions vsnprintf"

		config LV_USE_CUSTOM_SPRINTF
			bool "Implement the functions externally"

		endchoice # "Sprintf functions"

		config LV_MEM_SIZE_KILOBYTES
			int "Size of the memory used by `lv_malloc()` in kilobytes (>= 2kB)"
			default 64
			depends on LV_USE_BUILTIN_MALLOC

		config LV_MEM_POOL_EXPAND_SIZE_KILOBYTES
			int "Size of the memory expand for `lv_malloc()` in kilobytes"
			default 0
			depends on LV_USE_BUILTIN_MALLOC

		config LV_MEM_ADR
			hex "Address for the memory pool instead of allocating it as a normal array"
			default 0x0
			depends on LV_USE_BUILTIN_MALLOC

	endmenu

	menu "HAL Settings"
		config LV_DEF_REFR_PERIOD
			int "Default refresh period (ms)"
			default 33
			help
				Default display refresh, input device read and animation step period.

		config LV_DPI_DEF
			int "Default Dots Per Inch (in px/inch)"
			default 130
			help
				Used to initialize default sizes such as widgets sized, style paddings.
				(Not so important, you can adjust it to modify default sizes and spaces)
	endmenu

	menu "Operating System (OS)"
		choice LV_USE_OS
			prompt "Default operating system to use"
			default LV_OS_NONE

			config LV_OS_NONE
				bool "0: NONE"
			config LV_OS_PTHREAD
				bool "1: PTHREAD"
			config LV_OS_FREERTOS
				bool "2: FREERTOS"
			config LV_OS_CMSIS_RTOS2
				bool "3: CMSIS_RTOS2"
			config LV_OS_RTTHREAD
				bool "4: RTTHREAD"
			config LV_OS_WINDOWS
				bool "5: WINDOWS"
			config LV_OS_MQX
				bool "6: MQX"
			config LV_OS_CUSTOM
				bool "255: CUSTOM"
		endchoice

		config LV_USE_OS
			int
			default 0 if LV_OS_NONE
			default 1 if LV_OS_PTHREAD
			default 2 if LV_OS_FREERTOS
			default 3 if LV_OS_CMSIS_RTOS2
			default 4 if LV_OS_RTTHREAD
			default 5 if LV_OS_WINDOWS
			default 6 if LV_OS_MQX
			default 255 if LV_OS_CUSTOM

		config LV_OS_CUSTOM_INCLUDE
			string "Custom OS include header"
			default "stdint.h"
			depends on LV_OS_CUSTOM

        config LV_USE_FREERTOS_TASK_NOTIFY
            bool "Use RTOS task with a direct notification for synchronization"
            default y
            depends on LV_OS_FREERTOS
        help
            Unblocking an RTOS task with a direct notification is 45% faster and uses less RAM
            than unblocking a task using an intermediary object such as a binary semaphore.
            RTOS task notifications can only be used when there is only one task that can be the recipient of the event.
	endmenu

	menu "Rendering Configuration"
		config LV_DRAW_BUF_STRIDE_ALIGN
			int "Buffer stride alignment"
			default 1
		help
			Align the stride of all layers and images to this bytes.

		config LV_DRAW_BUF_ALIGN
			int "Buffer address alignment"
			default 4
		help
			Align the start address of draw_buf addresses to this bytes.

		config LV_DRAW_TRANSFORM_USE_MATRIX
			bool "Using matrix for transformations"
			default n
			depends on LV_USE_MATRIX
			help
				Requirements: The rendering engine needs to support 3x3 matrix transformations.

		config LV_DRAW_LAYER_SIMPLE_BUF_SIZE
			int "Optimal size to buffer the widget with opacity"
			default 24576
			depends on LV_USE_DRAW_SW
			help
				If a widget has `style_opa < 255` (not `bg_opa`, `text_opa` etc) or not NORMAL blend mode
				it is buffered into a "simple" layer before rendering. The widget can be buffered in smaller chunks.
				"Transformed layers" (if `transform_angle/zoom` are set) use larger buffers and can't be drawn in chunks.

		config LV_DRAW_THREAD_STACK_SIZE
			int "Stack size of draw thread in bytes"
			default 8192
			depends on LV_USE_OS > 0
			help
				If FreeType or ThorVG is enabled, it is recommended to set it to 32KB or more.

		config LV_USE_DRAW_SW
			bool "Enable software rendering"
			default y
		help
			Required to draw anything on the screen.

		config LV_DRAW_SW_SUPPORT_RGB565
			bool "Enable support for RGB565 color format"
			default y
			depends on LV_USE_DRAW_SW

		config LV_DRAW_SW_SUPPORT_RGB565A8
			bool "Enable support for RGB565A8 color format"
			default y
			depends on LV_USE_DRAW_SW

		config LV_DRAW_SW_SUPPORT_RGB888
			bool "Enable support for RGB888 color format"
			default y
			depends on LV_USE_DRAW_SW

		config LV_DRAW_SW_SUPPORT_XRGB8888
			bool "Enable support for XRGB8888 color format"
			default y
			depends on LV_USE_DRAW_SW

		config LV_DRAW_SW_SUPPORT_ARGB8888
			bool "Enable support for ARGB8888 color format"
			default y
			depends on LV_USE_DRAW_SW

		config LV_DRAW_SW_SUPPORT_L8
			bool "Enable support for L8 color format"
			default y
			depends on LV_USE_DRAW_SW

		config LV_DRAW_SW_SUPPORT_AL88
			bool "Enable support for AL88 color format"
			default y
			depends on LV_USE_DRAW_SW

		config LV_DRAW_SW_SUPPORT_A8
			bool "Enable support for A8 color format"
			default y
			depends on LV_USE_DRAW_SW

		config LV_DRAW_SW_SUPPORT_I1
			bool "Enable support for I1 color format"
			default y
			depends on LV_USE_DRAW_SW

		config LV_DRAW_SW_DRAW_UNIT_CNT
			int "Number of draw units"
			default 1
			depends on LV_USE_DRAW_SW
			help
				> 1 requires an operating system enabled in `LV_USE_OS`
				> 1 means multiply threads will render the screen in parallel

		config LV_USE_DRAW_ARM2D_SYNC
			bool "Enable Arm's 2D image processing library (Arm-2D) for all Cortex-M processors"
			default n
			depends on LV_USE_DRAW_SW
			help
				Must deploy arm-2d library to your project and add include PATH for "arm_2d.h".

		config LV_USE_NATIVE_HELIUM_ASM
			bool "Enable native helium assembly"
			default n
			depends on LV_USE_DRAW_SW
			help
				Disabling this allows arm2d to work on its own (for testing only)

		config LV_DRAW_SW_COMPLEX
			bool "Enable complex draw engine"
			default y
			depends on LV_USE_DRAW_SW
			help
				0: use a simple renderer capable of drawing only simple rectangles with gradient, images, texts, and straight lines only,
				1: use a complex renderer capable of drawing rounded corners, shadow, skew lines, and arcs too.

		config LV_USE_DRAW_SW_COMPLEX_GRADIENTS
			bool "Enable drawing complex gradients in software"
			default n
			depends on LV_USE_DRAW_SW
			help
				0: do not enable complex gradients
				1: enable complex gradients (linear at an angle, radial or conical)

		config LV_DRAW_SW_SHADOW_CACHE_SIZE
			int "Allow buffering some shadow calculation"
			depends on LV_DRAW_SW_COMPLEX
			default 0
			help
				LV_DRAW_SW_SHADOW_CACHE_SIZE is the max shadow size to buffer, where
				shadow size is `shadow_width + radius`.
				Caching has LV_DRAW_SW_SHADOW_CACHE_SIZE^2 RAM cost.

		config LV_DRAW_SW_CIRCLE_CACHE_SIZE
			int "Set number of maximally cached circle data"
			depends on LV_DRAW_SW_COMPLEX
			default 4
			help
				The circumference of 1/4 circle are saved for anti-aliasing
				radius * 4 bytes are used per circle (the most often used
				radiuses are saved).
				Set to 0 to disable caching.

		choice LV_USE_DRAW_SW_ASM
			prompt "Asm mode in sw draw"
			default LV_DRAW_SW_ASM_NONE
			depends on LV_USE_DRAW_SW
			help
				ASM mode to be used

			config LV_DRAW_SW_ASM_NONE
				bool "0: NONE"
			config LV_DRAW_SW_ASM_NEON
				bool "1: NEON"
			config LV_DRAW_SW_ASM_HELIUM
				bool "2: HELIUM"
			config LV_DRAW_SW_ASM_CUSTOM
				bool "255: CUSTOM"
		endchoice

		config LV_USE_DRAW_SW_ASM
			int
			default 0 if LV_DRAW_SW_ASM_NONE
			default 1 if LV_DRAW_SW_ASM_NEON
			default 2 if LV_DRAW_SW_ASM_HELIUM
			default 255 if LV_DRAW_SW_ASM_CUSTOM

		config LV_DRAW_SW_ASM_CUSTOM_INCLUDE
			string "Set the custom asm include file"
			default ""
			depends on LV_DRAW_SW_ASM_CUSTOM

		config LV_USE_DRAW_VGLITE
			bool "Use NXP's VG-Lite GPU on iMX RTxxx platforms"
			default n

		config LV_USE_VGLITE_BLIT_SPLIT
			bool "Enable blit quality degradation workaround recommended for screen's dimension > 352 pixels"
			depends on LV_USE_DRAW_VGLITE
			default n

		config LV_USE_VGLITE_DRAW_THREAD
			bool "Use additional draw thread for VG-Lite processing"
			depends on LV_USE_DRAW_VGLITE && LV_USE_OS > 0
			default y

		config LV_USE_VGLITE_DRAW_ASYNC
			bool "Enable VGLite draw async"
			depends on LV_USE_VGLITE_DRAW_THREAD
			default y
			help
				Queue multiple tasks and flash them once to the GPU. The task ready state will be send asynchronous to dispatcher.

		config LV_USE_VGLITE_ASSERT
			bool "Enable VGLite asserts"
			depends on LV_USE_DRAW_VGLITE
			default n

		config LV_USE_PXP
		bool "Use NXP's PXP on iMX RTxxx platforms"
		default n

		config LV_USE_DRAW_PXP
		bool "Use PXP for drawing"
			depends on LV_USE_PXP
			default y

		config LV_USE_ROTATE_PXP
			bool "Use PXP to rotate display"
			depends on LV_USE_PXP
			default n

		config LV_USE_PXP_DRAW_THREAD
			bool "Use additional draw thread for PXP processing"
			depends on LV_USE_DRAW_PXP && LV_USE_OS > 0
			default y

		config LV_USE_PXP_ASSERT
			bool "Enable PXP asserts"
			depends on LV_USE_DRAW_PXP
			default n

		config LV_USE_DRAW_DAVE2D
			bool "Use Renesas Dave2D on RA platforms"
			default n

		config LV_USE_DRAW_SDL
			bool "Draw using cached SDL textures"
			default n
			help
				Uses SDL renderer API

		config LV_USE_DRAW_VG_LITE
			bool "Use VG-Lite GPU"
			default n
			select LV_USE_MATRIX

		config LV_VG_LITE_USE_GPU_INIT
			bool "Enable VG-Lite custom external 'gpu_init()' function"
			default n
			depends on LV_USE_DRAW_VG_LITE

		config LV_VG_LITE_USE_ASSERT
			bool "Enable VG-Lite assert"
			default n
			depends on LV_USE_DRAW_VG_LITE

		config LV_VG_LITE_FLUSH_MAX_COUNT
			int "VG-Lite flush commit trigger threshold"
			default 8
			depends on LV_USE_DRAW_VG_LITE
			help
				GPU will try to batch these many draw tasks

		config LV_VG_LITE_USE_BOX_SHADOW
			bool "Enable border to simulate shadow"
			default n
			depends on LV_USE_DRAW_VG_LITE
			help
				which usually improves performance,
				but does not guarantee the same rendering quality as the software.

		config LV_VG_LITE_GRAD_CACHE_CNT
			int "VG-Lite gradient maximum cache number."
			default 32
			depends on LV_USE_DRAW_VG_LITE
			help
				The memory usage of a single gradient:
					linear: 4K bytes.
					radial: radius * 4K bytes.

		config LV_VG_LITE_STROKE_CACHE_CNT
			int "VG-Lite stroke maximum cache number."
			default 32
			depends on LV_USE_DRAW_VG_LITE

		config LV_USE_VECTOR_GRAPHIC
			bool "Use Vector Graphic APIs"
			default n
			select LV_USE_MATRIX
			help
				Enable drawing support vector graphic APIs.
	endmenu

	menu "Feature Configuration"
		menu "Logging"
			config LV_USE_LOG
				bool "Enable the log module"

			choice
				bool "Default log verbosity" if LV_USE_LOG
				default LV_LOG_LEVEL_WARN
				help
					Specify how important log should be added.

				config LV_LOG_LEVEL_TRACE
					bool "A lot of logs to give detailed information"
				config LV_LOG_LEVEL_INFO
					bool "Log important events"
				config LV_LOG_LEVEL_WARN
					bool "Log if something unwanted happened but didn't cause a problem"
				config LV_LOG_LEVEL_ERROR
					bool "Only critical issues, when the system may fail"
				config LV_LOG_LEVEL_USER
					bool "Only logs added by the user"
				config LV_LOG_LEVEL_NONE
					bool "Do not log anything"
			endchoice

			config LV_LOG_LEVEL
				int
				default 0 if LV_LOG_LEVEL_TRACE
				default 1 if LV_LOG_LEVEL_INFO
				default 2 if LV_LOG_LEVEL_WARN
				default 3 if LV_LOG_LEVEL_ERROR
				default 4 if LV_LOG_LEVEL_USER
				default 5 if LV_LOG_LEVEL_NONE

			config LV_LOG_PRINTF
				bool "Print the log with 'printf'" if LV_USE_LOG
				help
					Use printf for log output.
					If not set the user needs to register a callback with `lv_log_register_print_cb`.

			config LV_LOG_USE_TIMESTAMP
				bool "Enable print timestamp"
				default y
				depends on LV_USE_LOG

			config LV_LOG_USE_FILE_LINE
				bool "Enable print file and line number"
				default y
				depends on LV_USE_LOG

			config LV_LOG_TRACE_MEM
				bool "Enable/Disable LV_LOG_TRACE in mem module"
				default y
				depends on LV_USE_LOG

			config LV_LOG_TRACE_TIMER
				bool "Enable/Disable LV_LOG_TRACE in timer module"
				default y
				depends on LV_USE_LOG

			config LV_LOG_TRACE_INDEV
				bool "Enable/Disable LV_LOG_TRACE in indev module"
				default y
				depends on LV_USE_LOG

			config LV_LOG_TRACE_DISP_REFR
				bool "Enable/Disable LV_LOG_TRACE in disp refr module"
				default y
				depends on LV_USE_LOG

			config LV_LOG_TRACE_EVENT
				bool "Enable/Disable LV_LOG_TRACE in event module"
				default y
				depends on LV_USE_LOG

			config LV_LOG_TRACE_OBJ_CREATE
				bool "Enable/Disable LV_LOG_TRACE in obj create module"
				default y
				depends on LV_USE_LOG

			config LV_LOG_TRACE_LAYOUT
				bool "Enable/Disable LV_LOG_TRACE in layout module"
				default y
				depends on LV_USE_LOG

			config LV_LOG_TRACE_ANIM
				bool "Enable/Disable LV_LOG_TRACE in anim module"
				default y
				depends on LV_USE_LOG

			config LV_LOG_TRACE_CACHE
				bool "Enable/Disable LV_LOG_TRACE in cache module"
				default y
				depends on LV_USE_LOG
		endmenu

		menu "Asserts"
			config LV_USE_ASSERT_NULL
				bool "Check if the parameter is NULL. (Very fast, recommended)"
				default y if !LV_CONF_MINIMAL

			config LV_USE_ASSERT_MALLOC
				bool "Checks if the memory is successfully allocated or no. (Very fast, recommended)"
				default y if !LV_CONF_MINIMAL

			config LV_USE_ASSERT_STYLE
				bool "Check if the styles are properly initialized. (Very fast, recommended)"

			config LV_USE_ASSERT_MEM_INTEGRITY
				bool "Check the integrity of `lv_mem` after critical operations. (Slow)"

			config LV_USE_ASSERT_OBJ
				bool "Check NULL, the object's type and existence (e.g. not deleted). (Slow)"

			config LV_ASSERT_HANDLER_INCLUDE
				string "Header to include for the custom assert function"
				default "assert.h"
				help
					Add a custom handler when assert happens e.g. to restart the MCU
		endmenu

		menu "Debug"
			config LV_USE_REFR_DEBUG
				bool "Draw random colored rectangles over the redrawn areas"

			config LV_USE_LAYER_DEBUG
				bool "Draw a red overlay for ARGB layers and a green overlay for RGB layers"

			config LV_USE_PARALLEL_DRAW_DEBUG
				bool "Draw overlays with different colors for each draw_unit's tasks"
				help
					Also add the index number of the draw unit on white background.
					For layers add the index number of the draw unit on black background.
		endmenu

		menu "Others"
			config LV_ENABLE_GLOBAL_CUSTOM
				bool "Enable 'lv_global' customization"

			config LV_GLOBAL_CUSTOM_INCLUDE
				string "Header to include for the custom 'lv_global' function"
				depends on LV_ENABLE_GLOBAL_CUSTOM
				default "lv_global.h"

			config LV_CACHE_DEF_SIZE
				int "Default image cache size. 0 to disable caching"
				default 0
				depends on LV_USE_DRAW_SW
				help
					If only the built-in image formats are used there is no real advantage of caching.
					(I.e. no new image decoder is added).

					With complex image decoders (e.g. PNG or JPG) caching can
					save the continuous open/decode of images.
					However the opened images might consume additional RAM.

			config LV_IMAGE_HEADER_CACHE_DEF_CNT
				int "Default image header cache count. 0 to disable caching"
				default 0
				depends on LV_USE_DRAW_SW
				help
					If only the built-in image formats are used there is no real advantage of caching.
					(I.e. no new image decoder is added).

					With complex image decoders (e.g. PNG or JPG) caching can
					save the continuous getting header information of images.
					However the records of opened images headers might consume additional RAM.

			config LV_GRADIENT_MAX_STOPS
				int "Number of stops allowed per gradient"
				default 2
				depends on LV_USE_DRAW_SW
				help
					Increase this to allow more stops.
					This adds (sizeof(lv_color_t) + 1) bytes per additional stop

			config LV_COLOR_MIX_ROUND_OFS
				int "Adjust color mix functions rounding"
				default 128 if !LV_COLOR_DEPTH_32
				default 0 if LV_COLOR_DEPTH_32
				range 0 254
				help
					0: no adjustment, get the integer part of the result (round down)
					64: round up from x.75
					128: round up from half
					192: round up from x.25
					254: round up

			config LV_OBJ_STYLE_CACHE
				bool "Use cache to speed up getting object style properties"
				default n
				help
					Add 2 x 32 bit variables to each lv_obj_t to speed up getting style properties

			config LV_USE_OBJ_ID
				bool "Add id field to obj"
				default n

			config LV_OBJ_ID_AUTO_ASSIGN
				bool "Automatically assign an ID when obj is created"
				default y
				depends on LV_USE_OBJ_ID

			config LV_USE_OBJ_ID_BUILTIN
				bool "Use builtin method to deal with obj ID"
				default n
				depends on LV_USE_OBJ_ID

			config LV_USE_OBJ_PROPERTY
				bool "Use obj property set/get API"
				default n

			config LV_USE_OBJ_PROPERTY_NAME
				bool "Use name to access property"
				default n
				depends on LV_USE_OBJ_PROPERTY
				help
					Add a name table to every widget class, so the property can be accessed by name.
					Note, the const table will increase flash usage.

			config LV_USE_VG_LITE_THORVG
				bool "VG-Lite Simulator"
				default n
				depends on LV_USE_THORVG
				help
					Use thorvg to simulate VG-Lite hardware behavior, it's useful
					for debugging and testing on PC simulator. Enable LV_USE_THORVG,
					Either internal ThorVG or external ThorVG library is required.

			config LV_VG_LITE_THORVG_LVGL_BLEND_SUPPORT
				bool "Enable LVGL blend mode support"
				default n
				depends on LV_USE_VG_LITE_THORVG

			config LV_VG_LITE_THORVG_YUV_SUPPORT
				bool "Enable YUV color format support"
				default n
				depends on LV_USE_VG_LITE_THORVG

			config LV_VG_LITE_THORVG_LINEAR_GRADIENT_EXT_SUPPORT
				bool "Enable linear gradient extension support"
				default n
				depends on LV_USE_VG_LITE_THORVG

			config LV_VG_LITE_THORVG_16PIXELS_ALIGN
				bool "Enable 16 pixels alignment"
				default y
				depends on LV_USE_VG_LITE_THORVG

			config LV_VG_LITE_THORVG_BUF_ADDR_ALIGN
				int "Buffer address alignment"
				default 64
				depends on LV_USE_VG_LITE_THORVG

			config LV_VG_LITE_THORVG_THREAD_RENDER
				bool "Enable multi-thread render"
				default n
				depends on LV_USE_VG_LITE_THORVG
		endmenu
	endmenu

	menu "Compiler Settings"
		config LV_BIG_ENDIAN_SYSTEM
			bool "For big endian systems set to 1"

		config LV_ATTRIBUTE_MEM_ALIGN_SIZE
			int "Required alignment size for buffers"
			default 1

		config LV_ATTRIBUTE_FAST_MEM_USE_IRAM
			bool "Set IRAM as LV_ATTRIBUTE_FAST_MEM"
			help
				Set this option to configure IRAM as LV_ATTRIBUTE_FAST_MEM

		config LV_USE_FLOAT
			bool "Use float as lv_value_precise_t"
			default n

		config LV_USE_MATRIX
			bool "Enable matrix support"
			default n
			select LV_USE_FLOAT

		config LV_USE_PRIVATE_API
			bool "Include `lvgl_private.h` in `lvgl.h` to access internal data and functions by default"
			default n
	endmenu

	menu "Font Usage"
		menu "Enable built-in fonts"
			config LV_FONT_MONTSERRAT_8
				bool "Enable Montserrat 8"
			config LV_FONT_MONTSERRAT_10
				bool "Enable Montserrat 10"
			config LV_FONT_MONTSERRAT_12
				bool "Enable Montserrat 12"
			config LV_FONT_MONTSERRAT_14
				bool "Enable Montserrat 14"
				default y if !LV_CONF_MINIMAL
			config LV_FONT_MONTSERRAT_16
				bool "Enable Montserrat 16"
			config LV_FONT_MONTSERRAT_18
				bool "Enable Montserrat 18"
			config LV_FONT_MONTSERRAT_20
				bool "Enable Montserrat 20"
			config LV_FONT_MONTSERRAT_22
				bool "Enable Montserrat 22"
			config LV_FONT_MONTSERRAT_24
				bool "Enable Montserrat 24"
			config LV_FONT_MONTSERRAT_26
				bool "Enable Montserrat 26"
			config LV_FONT_MONTSERRAT_28
				bool "Enable Montserrat 28"
			config LV_FONT_MONTSERRAT_30
				bool "Enable Montserrat 30"
			config LV_FONT_MONTSERRAT_32
				bool "Enable Montserrat 32"
			config LV_FONT_MONTSERRAT_34
				bool "Enable Montserrat 34"
			config LV_FONT_MONTSERRAT_36
				bool "Enable Montserrat 36"
			config LV_FONT_MONTSERRAT_38
				bool "Enable Montserrat 38"
			config LV_FONT_MONTSERRAT_40
				bool "Enable Montserrat 40"
			config LV_FONT_MONTSERRAT_42
				bool "Enable Montserrat 42"
			config LV_FONT_MONTSERRAT_44
				bool "Enable Montserrat 44"
			config LV_FONT_MONTSERRAT_46
				bool "Enable Montserrat 46"
			config LV_FONT_MONTSERRAT_48
				bool "Enable Montserrat 48"

			config LV_FONT_MONTSERRAT_28_COMPRESSED
				bool "Enable Montserrat 28 compressed"
			config LV_FONT_DEJAVU_16_PERSIAN_HEBREW
				bool "Enable Dejavu 16 Persian, Hebrew, Arabic letters"
			config LV_FONT_SIMSUN_14_CJK
				bool "Enable Simsun 14 CJK"
			config LV_FONT_SIMSUN_16_CJK
				bool "Enable Simsun 16 CJK"

			config LV_FONT_UNSCII_8
				bool "Enable UNSCII 8 (Perfect monospace font)"
				default y if LV_CONF_MINIMAL
			config LV_FONT_UNSCII_16
				bool "Enable UNSCII 16 (Perfect monospace font)"
		endmenu

		choice LV_FONT_DEFAULT
			prompt "Select theme default title font"
			default LV_FONT_DEFAULT_MONTSERRAT_14 if !LV_CONF_MINIMAL
			default LV_FONT_DEFAULT_UNSCII_8 if LV_CONF_MINIMAL
			help
				Select theme default title font

			config LV_FONT_DEFAULT_MONTSERRAT_8
				bool "Montserrat 8"
				select LV_FONT_MONTSERRAT_8
			config LV_FONT_DEFAULT_MONTSERRAT_10
				bool "Montserrat 10"
				select LV_FONT_MONTSERRAT_10
			config LV_FONT_DEFAULT_MONTSERRAT_12
				bool "Montserrat 12"
				select LV_FONT_MONTSERRAT_12
			config LV_FONT_DEFAULT_MONTSERRAT_14
				bool "Montserrat 14"
				select LV_FONT_MONTSERRAT_14
			config LV_FONT_DEFAULT_MONTSERRAT_16
				bool "Montserrat 16"
				select LV_FONT_MONTSERRAT_16
			config LV_FONT_DEFAULT_MONTSERRAT_18
				bool "Montserrat 18"
				select LV_FONT_MONTSERRAT_18
			config LV_FONT_DEFAULT_MONTSERRAT_20
				bool "Montserrat 20"
				select LV_FONT_MONTSERRAT_20
			config LV_FONT_DEFAULT_MONTSERRAT_22
				bool "Montserrat 22"
				select LV_FONT_MONTSERRAT_22
			config LV_FONT_DEFAULT_MONTSERRAT_24
				bool "Montserrat 24"
				select LV_FONT_MONTSERRAT_24
			config LV_FONT_DEFAULT_MONTSERRAT_26
				bool "Montserrat 26"
				select LV_FONT_MONTSERRAT_26
			config LV_FONT_DEFAULT_MONTSERRAT_28
				bool "Montserrat 28"
				select LV_FONT_MONTSERRAT_28
			config LV_FONT_DEFAULT_MONTSERRAT_30
				bool "Montserrat 30"
				select LV_FONT_MONTSERRAT_30
			config LV_FONT_DEFAULT_MONTSERRAT_32
				bool "Montserrat 32"
				select LV_FONT_MONTSERRAT_32
			config LV_FONT_DEFAULT_MONTSERRAT_34
				bool "Montserrat 34"
				select LV_FONT_MONTSERRAT_34
			config LV_FONT_DEFAULT_MONTSERRAT_36
				bool "Montserrat 36"
				select LV_FONT_MONTSERRAT_36
			config LV_FONT_DEFAULT_MONTSERRAT_38
				bool "Montserrat 38"
				select LV_FONT_MONTSERRAT_38
			config LV_FONT_DEFAULT_MONTSERRAT_40
				bool "Montserrat 40"
				select LV_FONT_MONTSERRAT_40
			config LV_FONT_DEFAULT_MONTSERRAT_42
				bool "Montserrat 42"
				select LV_FONT_MONTSERRAT_42
			config LV_FONT_DEFAULT_MONTSERRAT_44
				bool "Montserrat 44"
				select LV_FONT_MONTSERRAT_44
			config LV_FONT_DEFAULT_MONTSERRAT_46
				bool "Montserrat 46"
				select LV_FONT_MONTSERRAT_46
			config LV_FONT_DEFAULT_MONTSERRAT_48
				bool "Montserrat 48"
				select LV_FONT_MONTSERRAT_48
			config LV_FONT_DEFAULT_MONTSERRAT_28_COMPRESSED
				bool "Montserrat 28 compressed"
				select LV_FONT_MONTSERRAT_28_COMPRESSED
			config LV_FONT_DEFAULT_DEJAVU_16_PERSIAN_HEBREW
				bool "Dejavu 16 Persian, Hebrew, Arabic letters"
				select LV_FONT_DEJAVU_16_PERSIAN_HEBREW
			config LV_FONT_DEFAULT_SIMSUN_14_CJK
				bool "Simsun 14 CJK"
				select LV_FONT_SIMSUN_14_CJK
			config LV_FONT_DEFAULT_SIMSUN_16_CJK
				bool "Simsun 16 CJK"
				select LV_FONT_SIMSUN_16_CJK
			config LV_FONT_DEFAULT_UNSCII_8
				bool "UNSCII 8 (Perfect monospace font)"
				select LV_FONT_UNSCII_8
			config LV_FONT_DEFAULT_UNSCII_16
				bool "UNSCII 16 (Perfect monospace font)"
				select LV_FONT_UNSCII_16
		endchoice

		config LV_FONT_FMT_TXT_LARGE
			bool "Enable it if you have fonts with a lot of characters"
			help
				The limit depends on the font size, font face and format
				but with > 10,000 characters if you see issues probably you
				need to enable it.

		config LV_USE_FONT_COMPRESSED
			bool "Sets support for compressed fonts"

		config LV_USE_FONT_PLACEHOLDER
			bool "Enable drawing placeholders when glyph dsc is not found"
			default y
	endmenu

	menu "Text Settings"
		choice LV_TXT_ENC
			prompt "Select a character encoding for strings"
			help
				Select a character encoding for strings. Your IDE or editor should have the same character encoding.
			default LV_TXT_ENC_UTF8 if !LV_CONF_MINIMAL
			default LV_TXT_ENC_ASCII if LV_CONF_MINIMAL

			config LV_TXT_ENC_UTF8
				bool "UTF8"
			config LV_TXT_ENC_ASCII
				bool "ASCII"
		endchoice

		config LV_TXT_BREAK_CHARS
			string "Can break (wrap) texts on these chars"
			default " ,.;:-_)}"

		config LV_TXT_LINE_BREAK_LONG_LEN
			int "Line break long length"
			default 0
			help
				If a word is at least this long, will break wherever 'prettiest'.
				To disable, set to a value <= 0.

		config LV_TXT_LINE_BREAK_LONG_PRE_MIN_LEN
			int "Min num chars before break"
			default 3
			depends on LV_TXT_LINE_BREAK_LONG_LEN > 0
			help
				Minimum number of characters in a long word to put on a line before a break.

		config LV_TXT_LINE_BREAK_LONG_POST_MIN_LEN
			int "Min num chars after break"
			default 3
			depends on LV_TXT_LINE_BREAK_LONG_LEN > 0
			help
				Minimum number of characters in a long word to put on a line after a break

		config LV_USE_BIDI
			bool "Support bidirectional texts"
			help
				Allows mixing Left-to-Right and Right-to-Left texts.
				The direction will be processed according to the Unicode Bidirectional Algorithm:
				https://www.w3.org/International/articles/inline-bidi-markup/uba-basics

		choice
			prompt "Set the default BIDI direction"
			default LV_BIDI_DIR_AUTO
			depends on LV_USE_BIDI

			config LV_BIDI_DIR_LTR
				bool "Left-to-Right"
			config LV_BIDI_DIR_RTL
				bool "Right-to-Left"
			config LV_BIDI_DIR_AUTO
				bool "Detect texts base direction"
		endchoice

		config LV_USE_ARABIC_PERSIAN_CHARS
			bool "Enable Arabic/Persian processing"
			help
				In these languages characters should be replaced with
				another form based on their position in the text.
	endmenu

	menu "Widget Usage"
		config LV_WIDGETS_HAS_DEFAULT_VALUE
			bool "Widgets has default value"
			default y if !LV_CONF_MINIMAL
		config LV_USE_ANIMIMG
			bool "Anim image"
			default y if !LV_CONF_MINIMAL
		config LV_USE_ARC
			bool "Arc"
			default y if !LV_CONF_MINIMAL
		config LV_USE_BAR
			bool "Bar"
			default y if !LV_CONF_MINIMAL
		config LV_USE_BUTTON
			bool "Button"
			default y if !LV_CONF_MINIMAL
		config LV_USE_BUTTONMATRIX
			bool "Button matrix"
			default y if !LV_CONF_MINIMAL
		config LV_USE_CALENDAR
			bool "Calendar"
			default y if !LV_CONF_MINIMAL
		config LV_CALENDAR_WEEK_STARTS_MONDAY
			bool "Calendar week starts monday"
			depends on LV_USE_CALENDAR
		config LV_USE_CALENDAR_HEADER_ARROW
			bool "Use calendar header arrow"
			depends on LV_USE_CALENDAR
			default y
		config LV_USE_CALENDAR_HEADER_DROPDOWN
			bool "Use calendar header dropdown"
			depends on LV_USE_CALENDAR
			default y
		config LV_USE_CALENDAR_CHINESE
			bool "Use chinese calendar"
			depends on LV_USE_CALENDAR
		config LV_USE_CANVAS
			bool "Canvas. Requires: lv_image"
			imply LV_USE_IMAGE
			default y if !LV_CONF_MINIMAL
		config LV_USE_CHART
			bool "Chart"
			default y if !LV_CONF_MINIMAL
		config LV_USE_CHECKBOX
			bool "Check Box"
			default y if !LV_CONF_MINIMAL
		config LV_USE_DROPDOWN
			bool "Drop down list. Requires: lv_label"
			imply LV_USE_LABEL
			default y if !LV_CONF_MINIMAL
		config LV_USE_IMAGE
			bool "Image. Requires: lv_label"
			imply LV_USE_LABEL
			default y if !LV_CONF_MINIMAL
		config LV_USE_IMAGEBUTTON
			bool "ImageButton"
			default y if !LV_CONF_MINIMAL
		config LV_USE_KEYBOARD
			bool "Keyboard"
			default y if !LV_CONF_MINIMAL
		config LV_USE_LABEL
			bool "Label"
			default y if !LV_CONF_MINIMAL
		config LV_LABEL_TEXT_SELECTION
			bool "Enable selecting text of the label"
			depends on LV_USE_LABEL
			default y
		config LV_LABEL_LONG_TXT_HINT
			bool "Store extra some info in labels (12 bytes) to speed up drawing of very long texts"
			depends on LV_USE_LABEL
			default y
		config LV_LABEL_WAIT_CHAR_COUNT
			int "The count of wait chart"
			depends on LV_USE_LABEL
			default 3
		config LV_USE_LED
			bool "LED"
			default y if !LV_CONF_MINIMAL
		config LV_USE_LINE
			bool "Line"
			default y if !LV_CONF_MINIMAL
		config LV_USE_LIST
			bool "List"
			default y if !LV_CONF_MINIMAL
		config LV_USE_LOTTIE
			bool "Lottie"
			default n
			depends on LV_USE_VECTOR_GRAPHIC && (LV_USE_THORVG_INTERNAL || LV_USE_THORVG_EXTERNAL)
			help
				Enable Lottie animations. Requires LV_USE_VECTOR_GRAPHIC and LV_USE_THORVG_INTERNAL or LV_USE_THORVG_EXTERNAL.
		config LV_USE_MENU
			bool "Menu"
			default y if !LV_CONF_MINIMAL
		config LV_USE_MSGBOX
			bool "Msgbox"
			default y if !LV_CONF_MINIMAL
		config LV_USE_ROLLER
			bool "Roller. Requires: lv_label"
			imply LV_USE_LABEL
			default y if !LV_CONF_MINIMAL
		config LV_USE_SCALE
			bool "Scale"
			default y if !LV_CONF_MINIMAL
		config LV_USE_SLIDER
			bool "Slider. Requires: lv_bar"
			imply LV_USE_BAR
			default y if !LV_CONF_MINIMAL
		config LV_USE_SPAN
			bool "Span"
			default y if !LV_CONF_MINIMAL
		config LV_SPAN_SNIPPET_STACK_SIZE
			int "Maximum number of span descriptor"
			default 64
			depends on LV_USE_SPAN
		config LV_USE_SPINBOX
			bool "Spinbox"
			default y if !LV_CONF_MINIMAL
		config LV_USE_SPINNER
			bool "Spinner"
			default y if !LV_CONF_MINIMAL
		config LV_USE_SWITCH
			bool "Switch"
			default y if !LV_CONF_MINIMAL
		config LV_USE_TEXTAREA
			bool "Text area. Requires: lv_label"
			select LV_USE_LABEL
			default y if !LV_CONF_MINIMAL
		config LV_TEXTAREA_DEF_PWD_SHOW_TIME
			int "Text area def. pwd show time [ms]"
			default 1500
			depends on LV_USE_TEXTAREA
		config LV_USE_TABLE
			bool "Table"
			default y if !LV_CONF_MINIMAL
		config LV_USE_TABVIEW
			bool "Tabview"
			default y if !LV_CONF_MINIMAL
		config LV_USE_TILEVIEW
			bool "Tileview"
			default y if !LV_CONF_MINIMAL
		config LV_USE_WIN
			bool "Win"
			default y if !LV_CONF_MINIMAL
	endmenu

	menu "Themes"
		config LV_USE_THEME_DEFAULT
			bool "A simple, impressive and very complete theme"
			default y if !LV_COLOR_DEPTH_1 && !LV_CONF_MINIMAL
		config LV_THEME_DEFAULT_DARK
			bool "Yes to set dark mode, No to set light mode"
			depends on LV_USE_THEME_DEFAULT
		config LV_THEME_DEFAULT_GROW
			bool "Enable grow on press"
			default y
			depends on LV_USE_THEME_DEFAULT
		config LV_THEME_DEFAULT_TRANSITION_TIME
			int "Default transition time in [ms]"
			default 80
			depends on LV_USE_THEME_DEFAULT
		config LV_USE_THEME_SIMPLE
			bool "A very simple theme that is a good starting point for a custom theme"
			default y if !LV_COLOR_DEPTH_1 && !LV_CONF_MINIMAL
		config LV_USE_THEME_MONO
			bool "Monochrome theme, suitable for some E-paper & dot matrix displays"
			default y if LV_COLOR_DEPTH_1 && !LV_CONF_MINIMAL
	endmenu

	menu "Layouts"
		config LV_USE_FLEX
			bool "A layout similar to Flexbox in CSS"
			default y if !LV_CONF_MINIMAL
		config LV_USE_GRID
			bool "A layout similar to Grid in CSS"
			default y if !LV_CONF_MINIMAL
	endmenu

	menu "3rd Party Libraries"
		config LV_FS_DEFAULT_DRIVE_LETTER
			int "Default drive letter (e.g. 65 for 'A')"
			default 0
			help
				Setting a default drive letter allows skipping the driver prefix in filepaths

		config LV_USE_FS_STDIO
			bool "File system on top of stdio API"
		config LV_FS_STDIO_LETTER
			int "Set an upper cased letter on which the drive will accessible (e.g. 65 for 'A')"
			default 0
			depends on LV_USE_FS_STDIO
		config LV_FS_STDIO_PATH
			string "Set the working directory"
			depends on LV_USE_FS_STDIO
		config LV_FS_STDIO_CACHE_SIZE
			int ">0 to cache this number of bytes in lv_fs_read()"
			default 0
			depends on LV_USE_FS_STDIO

		config LV_USE_FS_POSIX
			bool "File system on top of posix API"
		config LV_FS_POSIX_LETTER
			int "Set an upper cased letter on which the drive will accessible (e.g. 65 for 'A')"
			default 0
			depends on LV_USE_FS_POSIX
		config LV_FS_POSIX_PATH
			string "Set the working directory"
			depends on LV_USE_FS_POSIX
		config LV_FS_POSIX_CACHE_SIZE
			int ">0 to cache this number of bytes in lv_fs_read()"
			default 0
			depends on LV_USE_FS_POSIX

		config LV_USE_FS_WIN32
			bool "File system on top of Win32 API"
		config LV_FS_WIN32_LETTER
			int "Set an upper cased letter on which the drive will accessible (e.g. 65 for 'A')"
			default 0
			depends on LV_USE_FS_WIN32
		config LV_FS_WIN32_PATH
			string "Set the working directory"
			depends on LV_USE_FS_WIN32
		config LV_FS_WIN32_CACHE_SIZE
			int ">0 to cache this number of bytes in lv_fs_read()"
			default 0
			depends on LV_USE_FS_WIN32

		config LV_USE_FS_FATFS
			bool "File system on top of FatFS"
		config LV_FS_FATFS_LETTER
			int "Set an upper cased letter on which the drive will accessible (e.g. 65 for 'A')"
			default 0
			depends on LV_USE_FS_FATFS
		config LV_FS_FATFS_CACHE_SIZE
			int ">0 to cache this number of bytes in lv_fs_read()"
			default 0
			depends on LV_USE_FS_FATFS

		config LV_USE_FS_MEMFS
			bool "File system on top of memory-mapped API"
		config LV_FS_MEMFS_LETTER
			int "Set an upper cased letter on which the drive will accessible (e.g. 65 for 'A')"
			default 0
			depends on LV_USE_FS_MEMFS

		config LV_USE_FS_LITTLEFS
			bool "File system on top of littlefs API"
		config LV_FS_LITTLEFS_LETTER
			int "Set an upper cased letter on which the drive will accessible (e.g. 65 for 'A')"
			default 0
			depends on LV_USE_FS_LITTLEFS

		config LV_USE_FS_ARDUINO_ESP_LITTLEFS
			bool "File system on top of Arduino ESP littlefs API"
		config LV_FS_ARDUINO_ESP_LITTLEFS_LETTER
			int "Set an upper cased letter on which the drive will accessible (e.g. 65 for 'A')"
			default 0
			depends on LV_USE_FS_ARDUINO_ESP_LITTLEFS

		config LV_USE_FS_ARDUINO_SD
			bool "File system on top of Arduino SD API"
		config LV_FS_ARDUINO_SD_LETTER
			int "Set an upper cased letter on which the drive will accessible (e.g. 65 for 'A')"
			default 0
			depends on LV_USE_FS_ARDUINO_SD

		config LV_USE_LODEPNG
			bool "PNG decoder library"

		config LV_USE_LIBPNG
			bool "PNG decoder(libpng) library"

		config LV_USE_BMP
			bool "BMP decoder library"

		config LV_USE_TJPGD
			bool "TJPGD decoder library"

		config LV_USE_LIBJPEG_TURBO
			bool "libjpeg-turbo decoder library"

		config LV_USE_GIF
			bool "GIF decoder library"

		config LV_GIF_CACHE_DECODE_DATA
			bool "Use extra 16KB RAM to cache decoded data to accelerate"
			depends on LV_USE_GIF

		config LV_BIN_DECODER_RAM_LOAD
			bool "Decode whole image to RAM for bin decoder"
			default n

		config LV_USE_RLE
			bool "LVGL's version of RLE compression method"

		config LV_USE_QRCODE
			bool "QR code library"

		config LV_USE_BARCODE
			bool "Barcode library"

		config LV_USE_FREETYPE
			bool "FreeType library"
		config LV_FREETYPE_USE_LVGL_PORT
			bool "Let FreeType to use LVGL memory and file porting"
			default n
			depends on LV_USE_FREETYPE
		config LV_FREETYPE_CACHE_FT_GLYPH_CNT
			int "The maximum number of Glyph in count"
			default 256
			depends on LV_USE_FREETYPE

		config LV_USE_TINY_TTF
			bool "Enable Tiny TTF decoder"
			default n
		config LV_TINY_TTF_FILE_SUPPORT
			bool "Enable loading Tiny TTF data from files"
			default n
			depends on LV_USE_TINY_TTF
		config LV_TINY_TTF_CACHE_GLYPH_CNT
			bool "Tiny ttf cache entries count"
			default 256
			depends on LV_USE_TINY_TTF

		config LV_USE_RLOTTIE
			bool "Lottie library"

		config LV_USE_THORVG
			bool "ThorVG library"
			choice
				prompt "Use ThorVG config"
				depends on LV_USE_THORVG
				default LV_USE_THORVG_INTERNAL
				config LV_USE_THORVG_INTERNAL
					bool "Use ThorVG internal"
				config LV_USE_THORVG_EXTERNAL
					bool "Use ThorVG external"
			endchoice

		config LV_USE_LZ4
			bool "Enable LZ4 compress/decompress lib"
			choice
				prompt "Choose lvgl built-in LZ4 lib or external lib"
				depends on LV_USE_LZ4
				default LV_USE_LZ4_INTERNAL
				config LV_USE_LZ4_INTERNAL
					bool "Use lvgl built-in LZ4 lib"
				config LV_USE_LZ4_EXTERNAL
					bool "Use external LZ4 library"
			endchoice

		config LV_USE_FFMPEG
			bool "FFmpeg library"
		config LV_FFMPEG_DUMP_FORMAT
			bool "Dump format"
			depends on LV_USE_FFMPEG
			default n
	endmenu

	menu "Others"
		config LV_USE_SNAPSHOT
			bool "Enable API to take snapshot"
			default n if !LV_CONF_MINIMAL

		config LV_USE_SYSMON
			bool "Enable system monitor component"
			default n

		config LV_USE_PERF_MONITOR
			bool "Show CPU usage and FPS count"
			depends on LV_USE_SYSMON

		choice
			prompt "Performance monitor position"
			depends on LV_USE_PERF_MONITOR
			default LV_PERF_MONITOR_ALIGN_BOTTOM_RIGHT

			config LV_PERF_MONITOR_ALIGN_TOP_LEFT
				bool "Top left"
			config LV_PERF_MONITOR_ALIGN_TOP_MID
				bool "Top middle"
			config LV_PERF_MONITOR_ALIGN_TOP_RIGHT
				bool "Top right"
			config LV_PERF_MONITOR_ALIGN_BOTTOM_LEFT
				bool "Bottom left"
			config LV_PERF_MONITOR_ALIGN_BOTTOM_MID
				bool "Bottom middle"
			config LV_PERF_MONITOR_ALIGN_BOTTOM_RIGHT
				bool "Bottom right"
			config LV_PERF_MONITOR_ALIGN_LEFT_MID
				bool "Left middle"
			config LV_PERF_MONITOR_ALIGN_RIGHT_MID
				bool "Right middle"
			config LV_PERF_MONITOR_ALIGN_CENTER
				bool "Center"
		endchoice

		config LV_USE_PERF_MONITOR_LOG_MODE
			bool "Prints performance data using log"
			depends on LV_USE_PERF_MONITOR
			default n

		config LV_USE_MEM_MONITOR
			bool "Show the used memory and the memory fragmentation"
			default n
			depends on LV_USE_BUILTIN_MALLOC && LV_USE_SYSMON

		choice
			prompt "Memory monitor position"
			depends on LV_USE_MEM_MONITOR
			default LV_MEM_MONITOR_ALIGN_BOTTOM_LEFT

			config LV_MEM_MONITOR_ALIGN_TOP_LEFT
				bool "Top left"
			config LV_MEM_MONITOR_ALIGN_TOP_MID
				bool "Top middle"
			config LV_MEM_MONITOR_ALIGN_TOP_RIGHT
				bool "Top right"
			config LV_MEM_MONITOR_ALIGN_BOTTOM_LEFT
				bool "Bottom left"
			config LV_MEM_MONITOR_ALIGN_BOTTOM_MID
				bool "Bottom middle"
			config LV_MEM_MONITOR_ALIGN_BOTTOM_RIGHT
				bool "Bottom right"
			config LV_MEM_MONITOR_ALIGN_LEFT_MID
				bool "Left middle"
			config LV_MEM_MONITOR_ALIGN_RIGHT_MID
				bool "Right middle"
			config LV_MEM_MONITOR_ALIGN_CENTER
				bool "Center"
		endchoice

		config LV_USE_PROFILER
			bool "Runtime performance profiler"
		config LV_USE_PROFILER_BUILTIN
			bool "Enable the built-in profiler"
			depends on LV_USE_PROFILER
			default y
		config LV_PROFILER_BUILTIN_BUF_SIZE
			int "Default profiler trace buffer size in bytes"
			depends on LV_USE_PROFILER_BUILTIN
			default 16384
		config LV_PROFILER_INCLUDE
			string "Header to include for the profiler"
			depends on LV_USE_PROFILER
			default "lvgl/src/misc/lv_profiler_builtin.h"

		config LV_USE_MONKEY
			bool "Enable Monkey test"
			default n

		config LV_USE_GRIDNAV
			bool "Enable grid navigation"
			default n

		config LV_USE_FRAGMENT
			bool "Enable lv_obj fragment"
			default n

		config LV_USE_IMGFONT
			bool "Support using images as font in label or span widgets"
			default n

		config LV_USE_OBSERVER
			bool "Observer"
			default y

		config LV_USE_IME_PINYIN
			bool "Enable Pinyin input method"
			default n
		config LV_IME_PINYIN_USE_K9_MODE
			bool "Enable Pinyin input method 9 key input mode"
			depends on LV_USE_IME_PINYIN
			default n
		config LV_IME_PINYIN_K9_CAND_TEXT_NUM
			int "Maximum number of candidate panels for 9-key input mode"
			depends on LV_IME_PINYIN_USE_K9_MODE
			default 3
		config LV_IME_PINYIN_USE_DEFAULT_DICT
			bool "Use built-in Thesaurus"
			depends on LV_USE_IME_PINYIN
			default y
			help
				If you do not use the default thesaurus, be sure to use lv_ime_pinyin after setting the thesaurus
		config LV_IME_PINYIN_CAND_TEXT_NUM
			int "Maximum number of candidate panels"
			depends on LV_USE_IME_PINYIN
			default 6
			help
				Set the maximum number of candidate panels that can be displayed.
				This needs to be adjusted according to the size of the screen.

		config LV_USE_FILE_EXPLORER
			bool "Enable file explorer"
			default n
		config LV_FILE_EXPLORER_PATH_MAX_LEN
			int "Maximum length of path"
			depends on LV_USE_FILE_EXPLORER
			default 128
		config LV_FILE_EXPLORER_QUICK_ACCESS
			bool "Enable quick access bar"
			depends on LV_USE_FILE_EXPLORER
			default y
			help
				This can save some memory, but not much. After the quick access bar is created, it can be hidden by clicking the button at the top left corner of the browsing area, which is very useful for small screen devices.

		config LVGL_VERSION_MAJOR
			int
			default 9 # LVGL_VERSION_MAJOR
		config LVGL_VERSION_MINOR
			int
			default 2 # LVGL_VERSION_MINOR
		config LVGL_VERSION_PATCH
			int
			default 2 # LVGL_VERSION_PATCH
	endmenu

	menu "Devices"
		config LV_USE_SDL
			bool "Use SDL to open window on PC and handle mouse and keyboard"
			default n
		config LV_SDL_INCLUDE_PATH
			string "SDL include path"
			depends on LV_USE_SDL
			default "SDL2/SDL.h"

		choice
			prompt "SDL rendering mode"
			depends on LV_USE_SDL
			default LV_SDL_RENDER_MODE_DIRECT
			help
				LV_DISPLAY_RENDER_MODE_DIRECT is recommended for best performance

			config LV_SDL_RENDER_MODE_PARTIAL
				bool "Use the buffer(s) to render the screen is smaller parts"

			config LV_SDL_RENDER_MODE_DIRECT
				bool "Only the changed areas will be updated with 2 screen sized buffers"

			config LV_SDL_RENDER_MODE_FULL
				bool "Always redraw the whole screen even if only one pixel has been changed with 2 screen sized buffers"
		endchoice

		choice
			prompt "SDL buffer size"
			depends on LV_USE_SDL
			default LV_SDL_SINGLE_BUFFER

			config LV_SDL_SINGLE_BUFFER
				bool "One screen-sized buffer"

			config LV_SDL_DOUBLE_BUFFER
				bool "Two screen-sized buffer"
				depends on !LV_SDL_RENDER_MODE_PARTIAL

			config LV_SDL_CUSTOM_BUFFER
				bool "Custom-sized buffer"
				depends on LV_SDL_RENDER_MODE_PARTIAL
		endchoice

		config LV_SDL_BUFFER_COUNT
			int
			depends on LV_USE_SDL
			default 0 if LV_SDL_CUSTOM_BUFFER
			default 1 if LV_SDL_SINGLE_BUFFER
			default 2 if LV_SDL_DOUBLE_BUFFER

		config LV_SDL_ACCELERATED
			bool "Use hardware acceleration"
			depends on LV_USE_SDL
			default y

		config LV_SDL_FULLSCREEN
			bool "SDL fullscreen"
			depends on LV_USE_SDL
			default n

		config LV_SDL_DIRECT_EXIT
			bool "Exit the application when all SDL windows are closed"
			depends on LV_USE_SDL
			default y

		choice
			prompt "SDL mousewheel mode"
			depends on LV_USE_SDL
			default LV_SDL_MOUSEWHEEL_MODE_ENCODER

			config LV_SDL_MOUSEWHEEL_MODE_ENCODER
				bool "The mousewheel emulates an encoder input device"

			config LV_SDL_MOUSEWHEEL_MODE_CROWN
				bool "The mousewheel emulates a smart watch crown"
		endchoice

		config LV_SDL_MOUSEWHEEL_MODE
			int
			depends on LV_USE_SDL
			default 0 if LV_SDL_MOUSEWHEEL_MODE_ENCODER
			default 1 if LV_SDL_MOUSEWHEEL_MODE_CROWN

		config LV_USE_X11
			bool "Use X11 window manager to open window on Linux PC and handle mouse and keyboard"
			default n
		config LV_X11_DOUBLE_BUFFER
			bool "Use double buffers for lvgl rendering"
			depends on LV_USE_X11
			default y
		config LV_X11_DIRECT_EXIT
			bool "Exit the application when all X11 windows have been closed"
			depends on LV_USE_X11
			default y
		choice
			prompt "X11 device render mode"
			depends on LV_USE_X11
			default LV_X11_RENDER_MODE_PARTIAL

			config LV_X11_RENDER_MODE_PARTIAL
				bool "Partial render mode (preferred)"
				help
					Use the buffer(s) to render the screen is smaller parts. This way the buffers can be smaller then the display to save RAM.
					Appr. 1/10 screen size buffer(s) are used.
			config LV_X11_RENDER_MODE_DIRECT
				bool "Direct render mode"
				help
					The buffer(s) has to be screen sized and LVGL will render into the correct location of the buffer. This way the buffer always contain the whole image. Only the changed ares will be updated.
					With 2 buffers the buffers' content are kept in sync automatically and in flush_cb only address change is required.
			config LV_X11_RENDER_MODE_FULL
				bool "Full render mode"
				help
					Always redraw the whole screen even if only one pixel has been changed.
					With 2 buffers in flush_cb only and address change is required.
		endchoice

		config LV_USE_WAYLAND
			bool "Use the wayland client to open a window and handle inputs on Linux or BSD"
			default n
		config LV_WAYLAND_WINDOW_DECORATIONS
			bool "Draw client side window decorations, only necessary on Mutter (GNOME)"
			depends on LV_USE_WAYLAND
			default n
		config LV_WAYLAND_WL_SHELL
			bool "Support the legacy wl_shell instead of the default XDG Shell protocol"
			depends on LV_USE_WAYLAND
			default n

		config LV_USE_LINUX_FBDEV
			bool "Use Linux framebuffer device"
			default n
		config LV_LINUX_FBDEV_BSD
			bool "Use BSD flavored framebuffer device"
			depends on LV_USE_LINUX_FBDEV
			default n

		choice
			prompt "Framebuffer device render mode"
			depends on LV_USE_LINUX_FBDEV
			default LV_LINUX_FBDEV_RENDER_MODE_PARTIAL

			config LV_LINUX_FBDEV_RENDER_MODE_PARTIAL
				bool "Partial mode"
				help
					Use the buffer(s) to render the screen is smaller parts. This way the buffers can be smaller then the display to save RAM. At least 1/10 screen size buffer(s) are recommended.

			config LV_LINUX_FBDEV_RENDER_MODE_DIRECT
				bool "Direct mode"
				help
					The buffer(s) has to be screen sized and LVGL will render into the correct location of the buffer. This way the buffer always contain the whole image. Only the changed ares will be updated. With 2 buffers the buffers' content are kept in sync automatically and in flush_cb only address change is required.

			config LV_LINUX_FBDEV_RENDER_MODE_FULL
				bool "Full mode"
				help
					Always redraw the whole screen even if only one pixel has been changed. With 2 buffers in flush_cb only and address change is required.

		endchoice

		choice
			prompt "Framebuffer size"
			depends on LV_USE_LINUX_FBDEV
			default LV_LINUX_FBDEV_SINGLE_BUFFER

			config LV_LINUX_FBDEV_SINGLE_BUFFER
				bool "One screen-sized buffer"

			config LV_LINUX_FBDEV_DOUBLE_BUFFER
				bool "Two screen-sized buffer"
				depends on !LV_LINUX_FBDEV_RENDER_MODE_PARTIAL

			config LV_LINUX_FBDEV_CUSTOM_BUFFER
				bool "Custom-sized buffer"
				depends on LV_LINUX_FBDEV_RENDER_MODE_PARTIAL

		endchoice

		config LV_LINUX_FBDEV_BUFFER_COUNT
			int
			depends on LV_USE_LINUX_FBDEV
			default 0 if LV_LINUX_FBDEV_CUSTOM_BUFFER
			default 1 if LV_LINUX_FBDEV_SINGLE_BUFFER
			default 2 if LV_LINUX_FBDEV_DOUBLE_BUFFER

		config LV_LINUX_FBDEV_BUFFER_SIZE
			int "Custom partial buffer size (in number of rows)"
			depends on LV_USE_LINUX_FBDEV && LV_LINUX_FBDEV_CUSTOM_BUFFER
			default 60

		config LV_USE_NUTTX
			bool "Use Nuttx to open window and handle touchscreen"
			default n

		config LV_USE_NUTTX_LIBUV
			bool "Use uv loop to replace default timer loop and other fb/indev timers"
			depends on LV_USE_NUTTX
			default n

		config LV_USE_NUTTX_CUSTOM_INIT
			bool "Use Custom Nuttx init API to open window and handle touchscreen"
			depends on LV_USE_NUTTX
			default n

		config LV_USE_NUTTX_LCD
			bool "Use NuttX LCD device"
			depends on LV_USE_NUTTX
			default n

		choice
			prompt "NuttX LCD buffer size"
			depends on LV_USE_NUTTX_LCD
			default LV_NUTTX_LCD_SINGLE_BUFFER

			config LV_NUTTX_LCD_SINGLE_BUFFER
				bool "One screen-sized buffer"

			config LV_NUTTX_LCD_DOUBLE_BUFFER
				bool "Two screen-sized buffer"

			config LV_NUTTX_LCD_CUSTOM_BUFFER
				bool "Custom-sized buffer"

		endchoice

		config LV_NUTTX_LCD_BUFFER_COUNT
			int
			depends on LV_USE_NUTTX_LCD
			default 0 if LV_NUTTX_LCD_CUSTOM_BUFFER
			default 1 if LV_NUTTX_LCD_SINGLE_BUFFER
			default 2 if LV_NUTTX_LCD_DOUBLE_BUFFER

		config LV_NUTTX_LCD_BUFFER_SIZE
			int "Custom partial buffer size (in number of rows)"
			depends on LV_USE_NUTTX_LCD && LV_NUTTX_LCD_CUSTOM_BUFFER
			default 60

		config LV_USE_NUTTX_TOUCHSCREEN
			bool "Use NuttX touchscreen driver"
			depends on LV_USE_NUTTX
			default n

		config LV_USE_LINUX_DRM
			bool "Use Linux DRM device"
			default n

		config LV_USE_TFT_ESPI
			bool "Use TFT_eSPI driver"
			default n

		config LV_USE_EVDEV
			bool "Use evdev input driver"
			default n

		config LV_USE_LIBINPUT
			bool "Use libinput input driver"
			default n

		config LV_LIBINPUT_BSD
			bool "Use the BSD variant of the libinput input driver"
			depends on LV_USE_LIBINPUT
			default n

		config LV_LIBINPUT_XKB
			bool "Enable full keyboard support via XKB"
			depends on LV_USE_LIBINPUT
			default n

		config LV_USE_ST7735
			bool "Use ST7735 LCD driver"
			default n

		config LV_USE_ST7789
			bool "Use ST7789 LCD driver"
			default n

		config LV_USE_ST7796
			bool "Use ST7796 LCD driver"
			default n

		config LV_USE_ILI9341
			bool "Use ILI9341 LCD driver"
			default n

		config LV_USE_GENERIC_MIPI
			bool "Generic MIPI driver"
			default y if LV_USE_ST7735 || LV_USE_ST7789 || LV_USE_ST7796 || LV_USE_ILI9341

		config LV_USE_RENESAS_GLCDC
			bool "Use Renesas GLCDC driver"
			default n

		config LV_USE_WINDOWS
			bool "Use LVGL Windows backend"
			depends on LV_OS_WINDOWS
			default n

		config LV_USE_OPENGLES
			bool "Use GLFW and OpenGL to open window on PC and handle mouse and keyboard"
			default n

		config LV_USE_OPENGLES_DEBUG
			bool "Enable debug mode for OpenGL"
			depends on LV_USE_OPENGLES
			default n

		config LV_USE_QNX
			bool "Use a QNX Screen window as a display"
			default n

		config LV_QNX_BUF_COUNT
			int
			depends on LV_USE_QNX
			default 1
	endmenu

	menu "Examples"
		config LV_BUILD_EXAMPLES
			bool "Enable the examples to be built"
			default y if !LV_CONF_MINIMAL
	endmenu

	menu "Demos"
		config LV_USE_DEMO_WIDGETS
			bool "Show some widget"
			default n

		config LV_USE_DEMO_KEYPAD_AND_ENCODER
			bool "Demonstrate the usage of encoder and keyboard"
			default n

		config LV_USE_DEMO_BENCHMARK
			bool "Benchmark your system"
			default n
			depends on LV_FONT_MONTSERRAT_14 && LV_FONT_MONTSERRAT_24 && LV_USE_DEMO_WIDGETS
		config LV_USE_DEMO_RENDER
			bool "Render test for each primitives. Requires at least 480x272 display"
			default n

		config LV_USE_DEMO_SCROLL
			bool "Scroll settings test for LVGL"
			default n

		config LV_USE_DEMO_STRESS
			bool "Stress test for LVGL"
			default n

		config LV_USE_DEMO_TRANSFORM
			bool "Transform test for LVGL"
			default n
			depends on LV_FONT_MONTSERRAT_18

		config LV_USE_DEMO_MUSIC
			bool "Music player demo"
			default n
		config LV_DEMO_MUSIC_SQUARE
			bool "Enable Square"
			depends on LV_USE_DEMO_MUSIC
			default n
		config LV_DEMO_MUSIC_LANDSCAPE
			bool "Enable Landscape"
			depends on LV_USE_DEMO_MUSIC
			default n
		config LV_DEMO_MUSIC_ROUND
			bool "Enable Round"
			depends on LV_USE_DEMO_MUSIC
			default n
		config LV_DEMO_MUSIC_LARGE
			bool "Enable Large"
			depends on LV_USE_DEMO_MUSIC
			default n
		config LV_DEMO_MUSIC_AUTO_PLAY
			bool "Enable Auto play"
			depends on LV_USE_DEMO_MUSIC
			default n

		config LV_USE_DEMO_FLEX_LAYOUT
			bool "Flex layout previewer"
			default n
		config LV_USE_DEMO_MULTILANG
			bool "multi-language demo"
			default n
		config LV_USE_DEMO_VECTOR_GRAPHIC
			bool "vector graphic demo"
			default n
			depends on LV_USE_VECTOR_GRAPHIC
	endmenu

endmenu
