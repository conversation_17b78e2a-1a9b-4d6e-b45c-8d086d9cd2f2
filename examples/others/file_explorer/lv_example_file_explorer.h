/**
 * @file lv_example_file_explorer.h
 *
 */

#ifndef LV_EX_FILE_EXPLORER_H
#define LV_EX_FILE_EXPLORER_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_file_explorer_1(void);
void lv_example_file_explorer_2(void);
void lv_example_file_explorer_3(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EX_FILE_EXPLORER_H*/
