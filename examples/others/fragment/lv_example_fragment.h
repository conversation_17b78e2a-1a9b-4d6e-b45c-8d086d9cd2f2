/**
 * @file lv_example_fragment.h
 */
#ifndef LV_EXAMPLE_FRAGMENT_H
#define LV_EXAMPLE_FRAGMENT_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_fragment_1(void);
void lv_example_fragment_2(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_fragment_H*/
