/**
 * @file lv_example_others.h
 *
 */

#ifndef LV_EXAMPLE_OTHERS_H
#define LV_EXAMPLE_OTHERS_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "file_explorer/lv_example_file_explorer.h"
#include "fragment/lv_example_fragment.h"
#include "gridnav/lv_example_gridnav.h"
#include "ime/lv_example_ime_pinyin.h"
#include "imgfont/lv_example_imgfont.h"
#include "monkey/lv_example_monkey.h"
#include "observer/lv_example_observer.h"
#include "snapshot/lv_example_snapshot.h"

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_OTHERS_H*/
