A simple horizontal scale
"""""""""""""""""""""""""

.. lv_example:: widgets/scale/lv_example_scale_1
  :language: c

An vertical scale with section and custom styling
"""""""""""""""""""""""""""""""""""""""""""""""""

.. lv_example:: widgets/scale/lv_example_scale_2
  :language: c

A simple round scale
""""""""""""""""""""

.. lv_example:: widgets/scale/lv_example_scale_3
  :language: c

A round scale with section and custom styling
"""""""""""""""""""""""""""""""""""""""""""""

.. lv_example:: widgets/scale/lv_example_scale_4
  :language: c

An scale with section and custom styling
""""""""""""""""""""""""""""""""""""""""

.. lv_example:: widgets/scale/lv_example_scale_5
  :language: c

A round scale with multiple needles, resembling a clock
"""""""""""""""""""""""""""""""""""""""""""""""""""""""

.. lv_example:: widgets/scale/lv_example_scale_6
  :language: c

Customizing scale major tick label color with `LV_EVENT_DRAW_TASK_ADDED` event
""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""""

.. lv_example:: widgets/scale/lv_example_scale_7
  :language: c

