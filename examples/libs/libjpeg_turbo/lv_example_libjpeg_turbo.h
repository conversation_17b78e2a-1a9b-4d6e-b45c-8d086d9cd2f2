/**
 * @file lv_example_libjpeg_turbo.h
 *
 */

#ifndef LV_EXAMPLE_LIBJPEG_TURBO_H
#define LV_EXAMPLE_LIBJPEG_TURBO_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_libjpeg_turbo_1(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_LIBJPEG_TURBO_H*/
