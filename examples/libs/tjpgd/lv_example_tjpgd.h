/**
 * @file lv_example_tjpgd.h
 *
 */

#ifndef LV_EXAMPLE_TJPGD_H
#define LV_EXAMPLE_TJPGD_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_tjpgd_1(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_TJPGD_H*/
