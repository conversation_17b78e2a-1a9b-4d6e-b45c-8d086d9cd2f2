/**
 * @file lv_example_grid.h
 *
 */

#ifndef LV_EXAMPLE_GRID_H
#define LV_EXAMPLE_GRID_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
void lv_example_grid_1(void);
void lv_example_grid_2(void);
void lv_example_grid_3(void);
void lv_example_grid_4(void);
void lv_example_grid_5(void);
void lv_example_grid_6(void);

/**********************
 *      MACROS
 **********************/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*LV_EXAMPLE_GRID_H*/
