#include "../../../lvgl.h"
#if LV_BUILD_TEST

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_TEST_IMG_LVGL_LOGO_JPG
    #define LV_ATTRIBUTE_IMG_TEST_IMG_LVGL_LOGO_JPG
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMG_TEST_IMG_LVGL_LOGO_JPG uint8_t
test_img_lvgl_logo_jpg_map[] = {
    0xff,0xd8,0xff,0xe0,0x00,0x10,0x4a,0x46,0x49,0x46,0x00,0x01,0x01,0x01,0x00,0x78,
    0x00,0x78,0x00,0x00,0xff,0xe1,0x00,0x22,0x45,0x78,0x69,0x66,0x00,0x00,0x4d,0x4d,
    0x00,0x2a,0x00,0x00,0x00,0x08,0x00,0x01,0x01,0x12,0x00,0x03,0x00,0x00,0x00,0x01,
    0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0xec,0x00,0x11,0x44,0x75,0x63,0x6b,
    0x79,0x00,0x01,0x00,0x04,0x00,0x00,0x00,0x64,0x00,0x00,0xff,0xdb,0x00,0x43,0x00,
    0x02,0x01,0x01,0x02,0x01,0x01,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x03,0x05,
    0x03,0x03,0x03,0x03,0x03,0x06,0x04,0x04,0x03,0x05,0x07,0x06,0x07,0x07,0x07,0x06,
    0x07,0x07,0x08,0x09,0x0b,0x09,0x08,0x08,0x0a,0x08,0x07,0x07,0x0a,0x0d,0x0a,0x0a,
    0x0b,0x0c,0x0c,0x0c,0x0c,0x07,0x09,0x0e,0x0f,0x0d,0x0c,0x0e,0x0b,0x0c,0x0c,0x0c,
    0xff,0xdb,0x00,0x43,0x01,0x02,0x02,0x02,0x03,0x03,0x03,0x06,0x03,0x03,0x06,0x0c,
    0x08,0x07,0x08,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,
    0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,
    0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,0x0c,
    0x0c,0x0c,0x0c,0x0c,0x0c,0xff,0xc0,0x00,0x11,0x08,0x00,0x28,0x00,0x69,0x03,0x01,
    0x22,0x00,0x02,0x11,0x01,0x03,0x11,0x01,0xff,0xc4,0x00,0x1f,0x00,0x00,0x01,0x05,
    0x01,0x01,0x01,0x01,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x02,
    0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0xff,0xc4,0x00,0xb5,0x10,0x00,0x02,
    0x01,0x03,0x03,0x02,0x04,0x03,0x05,0x05,0x04,0x04,0x00,0x00,0x01,0x7d,0x01,0x02,
    0x03,0x00,0x04,0x11,0x05,0x12,0x21,0x31,0x41,0x06,0x13,0x51,0x61,0x07,0x22,0x71,
    0x14,0x32,0x81,0x91,0xa1,0x08,0x23,0x42,0xb1,0xc1,0x15,0x52,0xd1,0xf0,0x24,0x33,
    0x62,0x72,0x82,0x09,0x0a,0x16,0x17,0x18,0x19,0x1a,0x25,0x26,0x27,0x28,0x29,0x2a,
    0x34,0x35,0x36,0x37,0x38,0x39,0x3a,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4a,0x53,
    0x54,0x55,0x56,0x57,0x58,0x59,0x5a,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6a,0x73,
    0x74,0x75,0x76,0x77,0x78,0x79,0x7a,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8a,0x92,
    0x93,0x94,0x95,0x96,0x97,0x98,0x99,0x9a,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,
    0xaa,0xb2,0xb3,0xb4,0xb5,0xb6,0xb7,0xb8,0xb9,0xba,0xc2,0xc3,0xc4,0xc5,0xc6,0xc7,
    0xc8,0xc9,0xca,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,0xd9,0xda,0xe1,0xe2,0xe3,0xe4,
    0xe5,0xe6,0xe7,0xe8,0xe9,0xea,0xf1,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf9,0xfa,
    0xff,0xc4,0x00,0x1f,0x01,0x00,0x03,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,
    0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,
    0x0b,0xff,0xc4,0x00,0xb5,0x11,0x00,0x02,0x01,0x02,0x04,0x04,0x03,0x04,0x07,0x05,
    0x04,0x04,0x00,0x01,0x02,0x77,0x00,0x01,0x02,0x03,0x11,0x04,0x05,0x21,0x31,0x06,
    0x12,0x41,0x51,0x07,0x61,0x71,0x13,0x22,0x32,0x81,0x08,0x14,0x42,0x91,0xa1,0xb1,
    0xc1,0x09,0x23,0x33,0x52,0xf0,0x15,0x62,0x72,0xd1,0x0a,0x16,0x24,0x34,0xe1,0x25,
    0xf1,0x17,0x18,0x19,0x1a,0x26,0x27,0x28,0x29,0x2a,0x35,0x36,0x37,0x38,0x39,0x3a,
    0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4a,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5a,
    0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6a,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7a,
    0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8a,0x92,0x93,0x94,0x95,0x96,0x97,0x98,
    0x99,0x9a,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xaa,0xb2,0xb3,0xb4,0xb5,0xb6,
    0xb7,0xb8,0xb9,0xba,0xc2,0xc3,0xc4,0xc5,0xc6,0xc7,0xc8,0xc9,0xca,0xd2,0xd3,0xd4,
    0xd5,0xd6,0xd7,0xd8,0xd9,0xda,0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0xe8,0xe9,0xea,0xf2,
    0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf9,0xfa,0xff,0xda,0x00,0x0c,0x03,0x01,0x00,0x02,
    0x11,0x03,0x11,0x00,0x3f,0x00,0xf7,0xdf,0x8d,0x9f,0xf0,0x75,0x95,0x9f,0x84,0xfe,
    0x28,0xeb,0x5a,0x6f,0x82,0x7e,0x11,0xa7,0x8a,0x3c,0x33,0x61,0x72,0xf6,0xf6,0x7a,
    0xad,0xef,0x88,0x4d,0x84,0x97,0xea,0x8c,0x57,0xcd,0x10,0xad,0xbc,0x9b,0x51,0xb1,
    0x95,0xcb,0x6e,0xda,0x46,0x40,0x3c,0x0e,0x5b,0xfe,0x22,0xd2,0xd7,0xbf,0xe8,0x82,
    0xe9,0x7f,0xf8,0x58,0xbf,0xff,0x00,0x21,0xd7,0xc7,0xba,0xc7,0xfc,0x11,0x6f,0xe3,
    0x65,0x9e,0xaf,0x77,0x17,0x9b,0xf0,0xc6,0x4f,0x2a,0x67,0x4d,0xe9,0xe3,0xcd,0x29,
    0x55,0xb0,0xc4,0x64,0x06,0x99,0x58,0x03,0xe8,0xc0,0x1f,0x50,0x0f,0x15,0xe1,0x5f,
    0xb4,0x7f,0xec,0xb9,0xe3,0x5f,0xd9,0x3f,0xc6,0xd6,0xba,0x0f,0x8d,0xb4,0xb8,0x2c,
    0x6e,0xaf,0xed,0x16,0xfe,0xc6,0x7b,0x5b,0xd8,0x6f,0xac,0xef,0xed,0xd9,0x99,0x44,
    0xb0,0xcf,0x0b,0x34,0x72,0x2e,0xe5,0x65,0x38,0x39,0x0c,0xa4,0x10,0x0d,0x67,0x4b,
    0x11,0x42,0xa3,0xe5,0xa7,0x35,0x26,0xbb,0x34,0xff,0x00,0x22,0xeb,0x61,0x31,0x54,
    0x62,0xa7,0x5a,0x9c,0xa2,0x9e,0xcd,0xc5,0xa4,0xfe,0xf4,0x7e,0x9b,0xff,0x00,0xc4,
    0x5a,0x5a,0xf7,0xfd,0x10,0x5d,0x2f,0xff,0x00,0x0b,0x17,0xff,0x00,0xe4,0x3a,0xf5,
    0x2f,0x87,0x5f,0xf0,0x71,0x3f,0xc4,0x6d,0x77,0xe1,0x6c,0x7f,0x14,0x35,0xcf,0xd9,
    0x87,0x5a,0xb3,0xf8,0x3b,0x67,0xa9,0x2e,0x9f,0xab,0x78,0x9f,0x4d,0xf1,0x07,0xda,
    0xc5,0x99,0x2c,0x10,0x94,0x8d,0xed,0xe3,0x0f,0x87,0x65,0x5c,0x96,0x54,0xdc,0x42,
    0x6f,0x0c,0x40,0xaf,0xc3,0x5a,0xf4,0xeb,0x7f,0xdb,0x4b,0xe2,0x86,0x95,0xfb,0x2a,
    0x6a,0x1f,0x05,0xed,0xfc,0x5d,0x7d,0x1f,0xc3,0x3d,0x42,0xeb,0xed,0xd3,0xe8,0xbe,
    0x54,0x45,0x1a,0x40,0xe2,0x5d,0xa2,0x4d,0xbe,0x60,0x8c,0xc8,0x16,0x43,0x18,0x60,
    0x85,0xc6,0xec,0x64,0x92,0x7a,0x39,0x51,0xcc,0xa4,0xcf,0xeb,0x13,0xc3,0x7e,0x21,
    0xb3,0xf1,0x77,0x87,0xac,0x35,0x6d,0x3e,0x65,0xb8,0xd3,0xf5,0x4b,0x78,0xee,0xed,
    0xa5,0x00,0x81,0x2c,0x52,0x28,0x74,0x60,0x0f,0x3c,0xa9,0x07,0x9f,0x5a,0xbd,0x5c,
    0x3f,0xec,0xcb,0xff,0x00,0x26,0xdd,0xf0,0xf7,0xfe,0xc5,0xad,0x3b,0xff,0x00,0x49,
    0x63,0xaf,0x84,0xff,0x00,0x6b,0x2f,0xf8,0x28,0xf7,0xc5,0x8f,0x84,0xff,0x00,0xf0,
    0x5a,0xcf,0x86,0xff,0x00,0x04,0xf4,0x3d,0x5f,0x4a,0xb7,0xf8,0x7f,0xe2,0x49,0x34,
    0x91,0x7d,0x69,0x2e,0x99,0x1c,0x93,0xb8,0x9d,0xe4,0x12,0x81,0x29,0xf9,0xd4,0x90,
    0xa3,0x04,0x1e,0x3f,0x9e,0x68,0xd3,0x9b,0x43,0xf4,0x92,0x8a,0xf0,0x5f,0xf8,0x29,
    0xa7,0x8f,0x7e,0x23,0x7c,0x26,0xfd,0x89,0x7c,0x75,0xe3,0x0f,0x85,0x7a,0x85,0xb5,
    0x87,0x8b,0xbc,0x23,0x67,0xfd,0xb2,0x9f,0x68,0xb2,0x4b,0xc8,0xee,0x2d,0x60,0x60,
    0xf7,0x29,0xb1,0xc1,0x19,0xf2,0x04,0x8c,0x08,0x19,0xca,0x01,0xdc,0xd7,0x9a,0xff,
    0x00,0xc1,0x11,0xff,0x00,0x6f,0xad,0x73,0xfe,0x0a,0x01,0xfb,0x20,0xcd,0xae,0xf8,
    0xbe,0xea,0xca,0xe7,0xc6,0xbe,0x1c,0xd6,0x6e,0x34,0xad,0x59,0xed,0xa0,0x5b,0x75,
    0x99,0x4e,0xd9,0xa0,0x94,0x46,0xbc,0x28,0x31,0x48,0x13,0x8e,0xad,0x13,0x1a,0x3c,
    0xc7,0x7d,0x6c,0x7d,0x89,0x45,0x7e,0x60,0xfe,0xdb,0xdf,0xf0,0x53,0x5f,0x8c,0x52,
    0xff,0x00,0xc1,0x5e,0x3c,0x1f,0xfb,0x3a,0xfc,0x1b,0xd6,0xb4,0x8d,0x37,0x4f,0x9d,
    0xf4,0xeb,0x1d,0x6e,0x69,0xb4,0x98,0xaf,0xa4,0x8e,0x79,0xb7,0x5d,0x5c,0xca,0x0b,
    0xf4,0x58,0xac,0x8a,0x36,0xd1,0x8f,0x99,0x5f,0x27,0xb0,0x7f,0xfc,0x14,0x3b,0xfe,
    0x0b,0x2f,0xf1,0x2b,0xfe,0x1a,0xd0,0xfe,0xcf,0x7f,0xb3,0x0f,0x86,0x2d,0x7c,0x51,
    0xe3,0xeb,0x39,0x9a,0xcf,0x53,0xd4,0xe6,0x81,0x6e,0x84,0x57,0x2a,0x9b,0xe4,0x82,
    0x04,0x66,0x58,0x94,0x42,0x33,0xe6,0xcd,0x31,0xd8,0xac,0x19,0x76,0x8d,0xa5,0x8b,
    0xe5,0x17,0x32,0x3f,0x4e,0xe8,0xaf,0xc6,0xdf,0x1c,0x7f,0xc1,0x4a,0xff,0x00,0x6e,
    0x3f,0xf8,0x26,0x57,0x89,0xbc,0x3f,0xac,0xfe,0xd1,0x1e,0x15,0xd0,0xfc,0x5d,0xe0,
    0x2d,0x72,0xed,0x6d,0x64,0x9a,0xda,0x1b,0x38,0xd9,0x58,0x82,0xe6,0x18,0xae,0x2c,
    0xc8,0x48,0xe7,0xd8,0xae,0xca,0xb3,0x21,0x57,0x08,0xd8,0x3c,0x16,0x5f,0xa0,0xbf,
    0xe0,0xb0,0x7f,0xf0,0x54,0x5f,0x14,0x7c,0x07,0xfd,0x88,0x3e,0x15,0xfc,0x58,0xf8,
    0x1f,0xaf,0x69,0xe9,0x67,0xf1,0x07,0x54,0x87,0xca,0xba,0xba,0xd3,0xe3,0xba,0xf3,
    0x6d,0x25,0xb2,0x9a,0x60,0xa5,0x24,0xce,0xc7,0x0c,0x80,0x30,0xea,0xa5,0x4a,0x9c,
    0x60,0x8a,0x2c,0x1c,0xc7,0xe8,0x85,0x15,0xc3,0xfe,0xcc,0xfe,0x35,0xd4,0xbe,0x24,
    0xfe,0xce,0x1f,0x0f,0xfc,0x45,0xac,0x4b,0x14,0xda,0xbf,0x88,0x3c,0x37,0xa7,0x6a,
    0x57,0xd2,0x45,0x1f,0x94,0x92,0x4f,0x35,0xac,0x72,0x48,0xca,0xa3,0x85,0x05,0x98,
    0x90,0x07,0x41,0x5d,0xc5,0x22,0x8f,0xc1,0x79,0xd7,0xf7,0xef,0xb4,0x7c,0xbb,0x8e,
    0x31,0x5e,0x45,0xff,0x00,0x05,0x82,0x95,0x47,0xc3,0xef,0xd9,0xd5,0x37,0x0f,0x97,
    0xc2,0xba,0xa1,0xdb,0x9e,0x99,0xd6,0x6e,0xbb,0x7e,0x1f,0xa5,0x7e,0x88,0x5f,0xfc,
    0x5b,0x86,0xee,0xfa,0x69,0xa4,0xf0,0x67,0xc3,0x93,0x24,0xb2,0x33,0xb1,0xff,0x00,
    0x84,0x53,0x4f,0x6c,0x92,0x72,0x79,0x68,0x49,0x3f,0x89,0x26,0xb8,0x6f,0xf8,0x28,
    0x37,0xc7,0xff,0x00,0xf8,0x46,0x7c,0x23,0xf0,0x8e,0x13,0xe0,0x3f,0x85,0x1a,0xb4,
    0x33,0xe8,0x97,0x77,0x08,0x9a,0xb7,0x83,0x34,0xfb,0xf5,0xb4,0x3f,0x6d,0x96,0x32,
    0xb0,0xac,0x91,0x95,0x89,0x08,0x45,0x24,0x20,0x50,0x5b,0x24,0xe7,0x8c,0x7e,0x63,
    0xe0,0x7f,0x00,0xe2,0x33,0x1e,0x25,0x78,0x1c,0xbe,0xaa,0x95,0x49,0xd2,0x9d,0x94,
    0x93,0x8a,0xb2,0x70,0x93,0x77,0xf7,0xbb,0x6d,0x6e,0xa7,0xdb,0x78,0x81,0xf4,0x94,
    0xc8,0xb8,0xc7,0x2c,0x8e,0x5d,0x84,0xc3,0x55,0xa7,0x2a,0x73,0x55,0x1b,0x97,0x2b,
    0x4d,0x25,0x28,0x59,0x59,0xef,0x7a,0x89,0xfa,0x26,0x7e,0x26,0x86,0x0d,0xde,0x99,
    0x71,0xff,0x00,0x1e,0xf2,0x7f,0xba,0x6b,0xf4,0xc6,0x5b,0xbf,0x0d,0xfe,0xd4,0x7f,
    0x01,0xbe,0x32,0x69,0xbe,0x20,0xf8,0x6f,0xf0,0xb7,0x4b,0x93,0xc2,0xfe,0x03,0xd4,
    0x7c,0x4d,0xa5,0x5f,0xf8,0x77,0xc2,0x76,0x5a,0x2d,0xfd,0x9d,0xed,0xa1,0x89,0xa3,
    0x61,0x35,0xbc,0x6a,0xcd,0x19,0xdc,0x55,0xe3,0x6c,0xab,0x29,0xe8,0x08,0x04,0x7e,
    0x67,0x5c,0x7f,0xc7,0xbc,0x9f,0xee,0x9a,0xfd,0xd3,0x8a,0x38,0x63,0x1b,0x90,0x63,
    0xde,0x5d,0x8f,0xe5,0xe7,0x49,0x4b,0xdd,0x77,0x56,0x7b,0x6a,0xd2,0xfc,0x8f,0xcd,
    0x72,0xfc,0x7d,0x2c,0x65,0x2f,0x6f,0x46,0xf6,0xbd,0xb5,0xd3,0x63,0xfa,0xeb,0xfd,
    0x99,0x7f,0xe4,0xdb,0xbe,0x1e,0xff,0x00,0xd8,0xb5,0xa7,0x7f,0xe9,0x2c,0x75,0xf9,
    0x41,0xfb,0x7d,0x7f,0xca,0xcb,0xff,0x00,0x06,0x7f,0xdf,0xd0,0x7f,0xf4,0x39,0xab,
    0xf5,0x7f,0xf6,0x65,0xff,0x00,0x93,0x6e,0xf8,0x7b,0xff,0x00,0x62,0xd6,0x9d,0xff,
    0x00,0xa4,0xb1,0xd7,0xe6,0xdf,0xfc,0x15,0x13,0xfe,0x09,0xd7,0xfb,0x48,0x7c,0x51,
    0xff,0x00,0x82,0x9c,0xe8,0x9f,0x1b,0x3e,0x0d,0x68,0x3a,0x1d,0xe2,0xf8,0x67,0x4f,
    0xd3,0xdb,0x4e,0xbb,0xbe,0xd4,0xed,0x63,0x51,0x75,0x01,0x90,0x9d,0xd0,0xca,0xc0,
    0x90,0x37,0x0e,0xd8,0x3f,0xcb,0xe6,0x62,0x7a,0x2f,0x64,0x7e,0xa9,0xeb,0x1a,0x4d,
    0xaf,0x88,0x34,0x8b,0xab,0x0b,0xe8,0x63,0xba,0xb3,0xbe,0x85,0xed,0xee,0x21,0x90,
    0x65,0x25,0x8d,0xc1,0x56,0x52,0x3d,0x08,0x24,0x7e,0x35,0xf8,0xad,0xff,0x00,0x04,
    0x6b,0xf1,0x62,0xff,0x00,0xc1,0x36,0x3f,0xe0,0xa8,0xff,0x00,0x1d,0x3e,0x06,0xf8,
    0x96,0xee,0x4b,0x5d,0x02,0x4b,0x4b,0xc9,0xed,0xa5,0x94,0xf2,0xe3,0x4d,0x0f,0x77,
    0x6f,0x36,0x3f,0xdb,0xd3,0xe4,0x9a,0x43,0xdf,0xe5,0x1f,0x87,0xd1,0xdf,0x02,0xb5,
    0x0f,0xf8,0x29,0x34,0x9f,0x1b,0xbc,0x1e,0xbe,0x3a,0xb1,0xf8,0x7e,0x9e,0x09,0x6d,
    0x6a,0xcc,0x78,0x81,0xa0,0x3a,0x77,0x9a,0xba,0x7f,0x9c,0xbf,0x69,0xd9,0xb1,0xcb,
    0x6e,0xf2,0xb7,0xe3,0x6f,0x39,0xc5,0x70,0xbf,0xf0,0x5b,0xbf,0xf8,0x24,0x07,0xc5,
    0x5f,0xda,0x9f,0xf6,0xab,0xd0,0xfe,0x26,0x7c,0x19,0xd3,0xed,0x27,0xbc,0xd4,0xb4,
    0x33,0xa6,0x6b,0xe7,0xfb,0x56,0x2d,0x3a,0x55,0x96,0x2d,0xd1,0xa4,0x9b,0x9d,0x94,
    0xb8,0x92,0xde,0x5f,0x29,0x80,0xcf,0xcb,0x0e,0x0f,0x0d,0xcd,0x79,0x03,0xbe,0xe8,
    0xe0,0xbf,0xe0,0xdf,0x2f,0x05,0x5f,0x7e,0xd8,0x1f,0xf0,0x50,0x1f,0x8d,0x1f,0xb4,
    0xa7,0x88,0x2d,0x9d,0xbc,0xab,0x8b,0x84,0xd3,0xfc,0xce,0x44,0x57,0x7a,0x84,0xad,
    0x23,0x2a,0x1f,0xfa,0x63,0x6a,0x82,0x2c,0x76,0x59,0xc5,0x56,0xff,0x00,0x83,0x7a,
    0x7e,0xcf,0x2f,0xfc,0x15,0x27,0xf6,0x88,0x7d,0x7b,0x6f,0xfc,0x26,0x3e,0x4e,0xa2,
    0x57,0xce,0xff,0x00,0x5d,0x83,0xab,0x7f,0xa5,0xe3,0x3c,0xff,0x00,0xac,0xf2,0x73,
    0xdf,0xa5,0x7e,0x80,0x7f,0xc1,0x1e,0xbf,0x62,0x6d,0x43,0xf6,0x0c,0xfd,0x87,0x74,
    0x1f,0x07,0xeb,0xf6,0xd6,0xb6,0xde,0x2f,0xd4,0x2e,0xae,0x35,0x8f,0x10,0x2d,0xbc,
    0xab,0x32,0x0b,0xa9,0x9f,0x0a,0x81,0xd7,0x86,0xd9,0x04,0x70,0x46,0x48,0x24,0x12,
    0x84,0x8e,0x0d,0x7c,0xbf,0xff,0x00,0x05,0x05,0xff,0x00,0x82,0x37,0x7c,0x52,0xd1,
    0x7f,0x6b,0xd9,0x3f,0x68,0x6f,0xd9,0x6f,0xc4,0x56,0xba,0x0f,0x8d,0xb5,0x09,0xda,
    0xf7,0x53,0xd1,0xe5,0x9d,0x2d,0x4b,0x5d,0x3a,0xed,0x9a,0x68,0x1e,0x40,0x61,0x75,
    0x9f,0xac,0x90,0xcc,0x02,0x96,0x2e,0xdb,0x8e,0xed,0xaa,0x6e,0x2e,0x5d,0x0d,0x4f,
    0xf8,0x29,0xf7,0xfc,0x14,0x57,0xe3,0xdf,0xec,0xd9,0xe2,0x4f,0x1b,0xae,0xa1,0xfb,
    0x38,0xf8,0x4f,0xc5,0x9f,0x06,0x3c,0x37,0x79,0x6c,0x2d,0xfc,0x41,0xae,0x23,0x4f,
    0x6b,0x72,0x1f,0xca,0x58,0xe4,0x64,0x2c,0x57,0x3e,0x7c,0xbb,0x01,0x0b,0xc1,0xc7,
    0xd6,0xbe,0x70,0xff,0x00,0x82,0xc0,0x7c,0x76,0x9b,0xf6,0x99,0xff,0x00,0x82,0x2b,
    0x7e,0xcf,0x1e,0x39,0x9f,0xc3,0x7a,0x1f,0x84,0x5f,0x5e,0xf1,0x44,0xf2,0x2e,0x91,
    0xa3,0xc6,0x63,0xb1,0xb3,0x44,0x8f,0x50,0x89,0x04,0x4a,0x40,0xc0,0x2a,0x81,0xb1,
    0x8c,0x65,0x8f,0x5e,0xb5,0xd7,0xf8,0xf7,0xf6,0x08,0xfd,0xbc,0xff,0x00,0xe0,0xa7,
    0x5a,0x86,0x8b,0xe1,0x6f,0x8f,0x3a,0xd6,0x85,0xe0,0x3f,0x87,0x9a,0x7d,0xe2,0x5c,
    0xdd,0xa4,0x12,0x59,0x37,0x9a,0xcb,0x91,0xe6,0xac,0x16,0x8c,0xe6,0x69,0x40,0x27,
    0x68,0x95,0xd2,0x30,0x4e,0x46,0x3b,0xfb,0xdf,0xfc,0x16,0x0f,0xfe,0x09,0x79,0xe2,
    0xaf,0x8e,0x1f,0xb0,0xdf,0xc2,0x8f,0x84,0xbf,0x04,0x74,0x0b,0x5b,0xcb,0x4f,0x87,
    0xba,0x9c,0x21,0x2d,0xee,0xf5,0x18,0xad,0x8c,0x76,0x91,0x59,0x4d,0x00,0x76,0x79,
    0x0a,0x87,0x72,0xcc,0xa5,0x8f,0x52,0x58,0x9c,0x75,0xa3,0x40,0xd4,0xfb,0x27,0xf6,
    0x2e,0xff,0x00,0x93,0x3a,0xf8,0x4d,0xff,0x00,0x62,0x6e,0x8f,0xff,0x00,0xa4,0x30,
    0xd7,0xa6,0x57,0xe4,0xd7,0x81,0x3c,0x3b,0xff,0x00,0x05,0x42,0xf8,0x6d,0xe0,0x5d,
    0x17,0xc3,0xba,0x4e,0x97,0xf0,0xea,0x1d,0x2f,0xc3,0xf6,0x10,0x69,0xb6,0x68,0xf2,
    0xe9,0x6e,0xc9,0x0c,0x31,0xac,0x68,0x0b,0x17,0xe4,0xed,0x51,0xcf,0x7a,0xde,0xfe,
    0xd2,0xff,0x00,0x82,0xa6,0xff,0x00,0xd0,0x3f,0xe1,0x7f,0xfd,0xf5,0xa7,0x7f,0xf1,
    0x74,0xac,0x57,0x31,0xf4,0xaf,0x8c,0xbf,0xe0,0x99,0xcd,0xa8,0x78,0x92,0xea,0x7d,
    0x1b,0xc4,0x96,0xf6,0x7a,0x6c,0xae,0x5e,0x18,0x27,0xb4,0x69,0x24,0x88,0x1f,0xe1,
    0x2c,0x18,0x67,0x1e,0xb8,0x19,0xae,0x23,0xf6,0xa8,0xff,0x00,0x82,0x42,0x6a,0xdf,
    0xb4,0x2e,0x89,0xe0,0x6b,0x7b,0x4f,0x1c,0x69,0xfa,0x64,0xbe,0x13,0xd3,0x67,0xd3,
    0xe6,0x33,0x69,0xaf,0x22,0xdc,0x6f,0xb8,0x79,0xc3,0xae,0x1c,0x15,0xc7,0x98,0x54,
    0x83,0x9e,0x80,0xf1,0xd2,0x8a,0x29,0xf0,0x9c,0xdf,0x0d,0x66,0x6b,0x38,0xc9,0xbf,
    0x77,0x59,0x29,0x46,0xff,0x00,0x12,0xb4,0xb7,0x5c,0xb2,0xba,0xfc,0x34,0xe8,0x7c,
    0xcd,0x1e,0x0d,0xca,0x29,0x4e,0x73,0xa7,0x4a,0xce,0x6a,0xcf,0xde,0x96,0xd7,0x4f,
    0x45,0x7d,0x35,0x4b,0x6f,0x43,0x92,0xf8,0x57,0xff,0x00,0x04,0x37,0xd6,0x7c,0x09,
    0xe1,0x1f,0x89,0x5a,0x65,0xd7,0xc4,0x4d,0x36,0xe1,0xbc,0x75,0xe0,0xcd,0x47,0xc2,
    0xd0,0x49,0x0e,0x94,0xeb,0xf6,0x49,0x2e,0x42,0x6d,0x99,0x81,0x93,0xe6,0x55,0xd9,
    0xca,0x8c,0x13,0x9e,0xa2,0xbe,0x73,0xf0,0x47,0xfc,0x1a,0x7b,0xaa,0xaf,0x8b,0x2c,
    0x1b,0xc4,0xdf,0x18,0xb4,0xf9,0xb4,0x15,0x94,0x1b,0xe8,0xb4,0xcd,0x0d,0xe3,0xbb,
    0x96,0x2f,0xe2,0x58,0xde,0x49,0x59,0x11,0x88,0xe3,0x73,0x2b,0x01,0xd7,0x69,0xe9,
    0x45,0x15,0xed,0x71,0x07,0x12,0x66,0x19,0xde,0x2f,0xeb,0xf9,0x94,0xf9,0xaa,0x34,
    0x95,0xec,0x96,0x8b,0x6d,0x12,0x48,0xf7,0x30,0x79,0x7d,0x0c,0x2d,0x3f,0x65,0x41,
    0x59,0x6f,0xbb,0x7f,0x99,0xfb,0x29,0xe1,0x9f,0x0d,0xd9,0xf8,0x3f,0xc3,0x7a,0x7e,
    0x93,0xa7,0xc3,0xf6,0x7d,0x3f,0x4b,0xb6,0x8e,0xd2,0xda,0x20,0x49,0xf2,0xe2,0x8d,
    0x42,0x22,0xe4,0xf3,0xc2,0x80,0x39,0xab,0xd4,0x51,0x5e,0x19,0xd8,0x14,0x51,0x45,
    0x00,0x14,0x51,0x45,0x00,0x14,0x51,0x45,0x00,0x14,0x51,0x45,0x00,0x7f,0xff,0xd9,
};

const lv_image_dsc_t test_img_lvgl_logo_jpg = {
  .header.magic = LV_IMAGE_HEADER_MAGIC,
  .header.cf = LV_COLOR_FORMAT_RAW,
  .header.flags = 0,
  .header.w = 105,
  .header.h = 40,
  .header.stride = 0,
  .data_size = sizeof(test_img_lvgl_logo_jpg_map),
  .data = test_img_lvgl_logo_jpg_map,
};

#endif /*LV_BUILD_TEST*/
