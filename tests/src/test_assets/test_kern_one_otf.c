#include <stdint.h>
#include <stddef.h>
#if LV_BUILD_TEST

/*TestKERNOne.otf from https://github.com/unicode-org/text-rendering-tests*/
const uint8_t test_kern_one_otf[] = {
    0x4f, 0x54, 0x54, 0x4f, 0x00, 0x0a, 0x00, 0x80, 0x00, 0x03, 0x00, 0x20, 0x43, 0x46, 0x46, 0x20,
    0x20, 0xda, 0x90, 0x5b, 0x00, 0x00, 0x03, 0xb8, 0x00, 0x00, 0x01, 0x66, 0x4f, 0x53, 0x2f, 0x32,
    0x5a, 0x11, 0x87, 0xea, 0x00, 0x00, 0x01, 0x10, 0x00, 0x00, 0x00, 0x60, 0x63, 0x6d, 0x61, 0x70,
    0x02, 0x92, 0x00, 0x18, 0x00, 0x00, 0x03, 0x4c, 0x00, 0x00, 0x00, 0x4c, 0x68, 0x65, 0x61, 0x64,
    0x0d, 0xf9, 0x3b, 0x43, 0x00, 0x00, 0x00, 0xac, 0x00, 0x00, 0x00, 0x36, 0x68, 0x68, 0x65, 0x61,
    0x06, 0x42, 0x01, 0xc8, 0x00, 0x00, 0x00, 0xe4, 0x00, 0x00, 0x00, 0x24, 0x68, 0x6d, 0x74, 0x78,
    0x08, 0xfc, 0x00, 0xf3, 0x00, 0x00, 0x05, 0x20, 0x00, 0x00, 0x00, 0x14, 0x6b, 0x65, 0x72, 0x6e,
    0x01, 0x54, 0xfd, 0xe7, 0x00, 0x00, 0x05, 0x34, 0x00, 0x00, 0x00, 0x30, 0x6d, 0x61, 0x78, 0x70,
    0x00, 0x05, 0x50, 0x00, 0x00, 0x00, 0x01, 0x08, 0x00, 0x00, 0x00, 0x06, 0x6e, 0x61, 0x6d, 0x65,
    0xe2, 0x60, 0xf2, 0x44, 0x00, 0x00, 0x01, 0x70, 0x00, 0x00, 0x01, 0xda, 0x70, 0x6f, 0x73, 0x74,
    0xff, 0xb8, 0x00, 0x32, 0x00, 0x00, 0x03, 0x98, 0x00, 0x00, 0x00, 0x20, 0x00, 0x01, 0x00, 0x00,
    0x00, 0x01, 0x00, 0x00, 0x7f, 0x24, 0x30, 0xed, 0x5f, 0x0f, 0x3c, 0xf5, 0x00, 0x03, 0x03, 0xe8,
    0x00, 0x00, 0x00, 0x00, 0xd3, 0xef, 0x1e, 0xed, 0x00, 0x00, 0x00, 0x00, 0xd8, 0x9a, 0xd9, 0x1e,
    0x00, 0x32, 0xff, 0x38, 0x02, 0x26, 0x03, 0x20, 0x00, 0x00, 0x00, 0x03, 0x00, 0x02, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x03, 0xe8, 0xff, 0x38, 0x00, 0x00, 0x02, 0x58,
    0x00, 0x32, 0x00, 0x32, 0x02, 0x26, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x50, 0x00, 0x00, 0x05, 0x00, 0x00,
    0x00, 0x03, 0x01, 0xcc, 0x01, 0x90, 0x00, 0x05, 0x00, 0x08, 0x02, 0x8a, 0x02, 0x58, 0x00, 0x00,
    0x00, 0x4b, 0x02, 0x8a, 0x02, 0x58, 0x00, 0x00, 0x01, 0x5e, 0x00, 0x32, 0x01, 0x2c, 0x00, 0x00,
    0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x4e, 0x49, 0x43, 0x00, 0x40,
    0x00, 0x20, 0x01, 0x31, 0x03, 0x20, 0xff, 0x38, 0x00, 0xc8, 0x03, 0xe8, 0x00, 0xc8, 0x20, 0x00,
    0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0xf4, 0x02, 0xbc, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x0c, 0x00, 0x96, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1e,
    0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x15, 0x00, 0x1e, 0x00, 0x01,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x07, 0x00, 0x33, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x06, 0x00, 0x1b, 0x00, 0x3a, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x09, 0x00, 0x0d,
    0x00, 0x55, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x13, 0x00, 0x0a, 0x00, 0x62, 0x00, 0x03,
    0x00, 0x01, 0x04, 0x09, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x6c, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09,
    0x00, 0x01, 0x00, 0x2a, 0x00, 0xa8, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09, 0x00, 0x02, 0x00, 0x0e,
    0x00, 0xd2, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09, 0x00, 0x06, 0x00, 0x36, 0x00, 0xe0, 0x00, 0x03,
    0x00, 0x01, 0x04, 0x09, 0x00, 0x09, 0x00, 0x1a, 0x01, 0x16, 0x00, 0x03, 0x00, 0x01, 0x04, 0x09,
    0x00, 0x13, 0x00, 0x14, 0x01, 0x30, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x20,
    0xa9, 0x20, 0x32, 0x30, 0x31, 0x36, 0x20, 0x55, 0x6e, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2c, 0x20,
    0x49, 0x6e, 0x63, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x73, 0x74,
    0x20, 0x4b, 0x45, 0x52, 0x4e, 0x20, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72,
    0x4f, 0x70, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x73, 0x74, 0x4b, 0x45, 0x52, 0x4e,
    0x4f, 0x6e, 0x65, 0x2d, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x53, 0x61, 0x73, 0x63, 0x68,
    0x61, 0x20, 0x42, 0x72, 0x61, 0x77, 0x65, 0x72, 0xf5, 0x54, 0x75, 0x54, 0xf5, 0xf5, 0x54, 0x75,
    0x54, 0xf5, 0x00, 0x43, 0x00, 0x6f, 0x00, 0x70, 0x00, 0x79, 0x00, 0x72, 0x00, 0x69, 0x00, 0x67,
    0x00, 0x68, 0x00, 0x74, 0x00, 0x20, 0x00, 0xa9, 0x00, 0x20, 0x00, 0x32, 0x00, 0x30, 0x00, 0x31,
    0x00, 0x36, 0x00, 0x20, 0x00, 0x55, 0x00, 0x6e, 0x00, 0x69, 0x00, 0x63, 0x00, 0x6f, 0x00, 0x64,
    0x00, 0x65, 0x00, 0x2c, 0x00, 0x20, 0x00, 0x49, 0x00, 0x6e, 0x00, 0x63, 0x00, 0x2e, 0x00, 0x4f,
    0x00, 0x70, 0x00, 0x65, 0x00, 0x6e, 0x00, 0x54, 0x00, 0x79, 0x00, 0x70, 0x00, 0x65, 0x00, 0x54,
    0x00, 0x65, 0x00, 0x73, 0x00, 0x74, 0x00, 0x20, 0x00, 0x4b, 0x00, 0x45, 0x00, 0x52, 0x00, 0x4e,
    0x00, 0x20, 0x00, 0x4f, 0x00, 0x6e, 0x00, 0x65, 0x00, 0x52, 0x00, 0x65, 0x00, 0x67, 0x00, 0x75,
    0x00, 0x6c, 0x00, 0x61, 0x00, 0x72, 0x00, 0x4f, 0x00, 0x70, 0x00, 0x65, 0x00, 0x6e, 0x00, 0x54,
    0x00, 0x79, 0x00, 0x70, 0x00, 0x65, 0x00, 0x54, 0x00, 0x65, 0x00, 0x73, 0x00, 0x74, 0x00, 0x4b,
    0x00, 0x45, 0x00, 0x52, 0x00, 0x4e, 0x00, 0x4f, 0x00, 0x6e, 0x00, 0x65, 0x00, 0x2d, 0x00, 0x52,
    0x00, 0x65, 0x00, 0x67, 0x00, 0x75, 0x00, 0x6c, 0x00, 0x61, 0x00, 0x72, 0x00, 0x53, 0x00, 0x61,
    0x00, 0x73, 0x00, 0x63, 0x00, 0x68, 0x00, 0x61, 0x00, 0x20, 0x00, 0x42, 0x00, 0x72, 0x00, 0x61,
    0x00, 0x77, 0x00, 0x65, 0x00, 0x72, 0x01, 0x31, 0x00, 0x54, 0x00, 0x75, 0x00, 0x54, 0x01, 0x31,
    0x01, 0x31, 0x00, 0x54, 0x00, 0x75, 0x00, 0x54, 0x01, 0x31, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02,
    0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x14, 0x00, 0x03, 0x00, 0x01, 0x00, 0x00, 0x00, 0x14,
    0x00, 0x04, 0x00, 0x38, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x08, 0x00, 0x02, 0x00, 0x02, 0x00, 0x20,
    0x00, 0x54, 0x00, 0x75, 0x01, 0x31, 0xff, 0xff, 0x00, 0x00, 0x00, 0x20, 0x00, 0x54, 0x00, 0x75,
    0x01, 0x31, 0xff, 0xff, 0xff, 0xe4, 0xff, 0xad, 0xff, 0x8e, 0xfe, 0xd1, 0x00, 0x01, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xff, 0xb5, 0x00, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x04, 0x04, 0x00, 0x01, 0x01, 0x01,
    0x1c, 0x4f, 0x70, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x73, 0x74, 0x4b, 0x45, 0x52,
    0x4e, 0x4f, 0x6e, 0x65, 0x2d, 0x52, 0x65, 0x67, 0x75, 0x6c, 0x61, 0x72, 0x00, 0x01, 0x01, 0x01,
    0x1f, 0xf8, 0x0f, 0x00, 0xf8, 0x1b, 0x01, 0xf8, 0x1c, 0x02, 0xf8, 0x18, 0x04, 0xbd, 0xfb, 0x5c,
    0xf8, 0xba, 0xf9, 0xb4, 0x05, 0xf7, 0x22, 0x0f, 0x94, 0xf7, 0xf1, 0x12, 0xf7, 0x2b, 0x11, 0x00,
    0x02, 0x01, 0x01, 0x21, 0x3e, 0x43, 0x6f, 0x70, 0x79, 0x72, 0x69, 0x67, 0x68, 0x74, 0x20, 0x28,
    0x43, 0x29, 0x20, 0x32, 0x30, 0x31, 0x36, 0x20, 0x55, 0x6e, 0x69, 0x63, 0x6f, 0x64, 0x65, 0x2c,
    0x20, 0x49, 0x6e, 0x63, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x54, 0x65, 0x73,
    0x74, 0x20, 0x4b, 0x45, 0x52, 0x4e, 0x20, 0x4f, 0x6e, 0x65, 0x20, 0x52, 0x65, 0x67, 0x75, 0x6c,
    0x61, 0x72, 0x00, 0x00, 0x01, 0x01, 0x00, 0x00, 0x35, 0x00, 0x91, 0x00, 0x56, 0x00, 0x01, 0x00,
    0x05, 0x01, 0x01, 0x89, 0xa0, 0xaa, 0xbb, 0xbe, 0xf8, 0x88, 0xf8, 0x2e, 0xf9, 0xb4, 0x15, 0xfb,
    0xd1, 0xfe, 0x7c, 0xf7, 0xd1, 0x06, 0x3e, 0xf7, 0xd2, 0x15, 0xfb, 0x05, 0xfb, 0x3a, 0xf7, 0x05,
    0x07, 0xf7, 0x19, 0x6a, 0x15, 0x27, 0x5c, 0xef, 0x06, 0xac, 0xf7, 0x91, 0x15, 0xfb, 0x05, 0xfb,
    0x3a, 0xf7, 0x05, 0xac, 0x3b, 0xef, 0xba, 0x6a, 0x74, 0x69, 0xc3, 0x07, 0xef, 0xf7, 0x0e, 0x15,
    0x27, 0xfb, 0x3a, 0xad, 0xf7, 0x19, 0xcd, 0x07, 0xac, 0xc7, 0x15, 0x6a, 0x49, 0x45, 0x27, 0xac,
    0xcd, 0xb0, 0x49, 0xac, 0x07, 0xcd, 0xec, 0x15, 0x6a, 0x67, 0xac, 0x06, 0xad, 0xd1, 0x15, 0x45,
    0xcd, 0x69, 0xfb, 0x3a, 0xf3, 0x07, 0xf7, 0x3a, 0xf7, 0x15, 0x15, 0x6a, 0x49, 0x66, 0xcd, 0x6a,
    0xfb, 0x3a, 0xac, 0xcd, 0xb0, 0x4a, 0xac, 0x07, 0xf7, 0x39, 0xfd, 0x83, 0x15, 0x6a, 0x6b, 0x07,
    0x45, 0x5c, 0x05, 0xf1, 0x6a, 0xfb, 0x3a, 0xac, 0x06, 0xd1, 0xba, 0x05, 0x45, 0xac, 0x06, 0x0e,
    0xf8, 0xec, 0xbd, 0xf8, 0x88, 0x15, 0xef, 0xef, 0xef, 0xfc, 0xec, 0xef, 0xf8, 0xec, 0xef, 0x27,
    0xef, 0xf7, 0x5c, 0xfc, 0x88, 0x06, 0x0e, 0xf7, 0x5c, 0xbd, 0x16, 0xef, 0xf8, 0x88, 0x27, 0x06,
    0x0e, 0xf8, 0x24, 0xbd, 0x16, 0xf7, 0xc0, 0xf8, 0x88, 0x27, 0xfc, 0x24, 0x27, 0xf8, 0x24, 0x27,
    0x06, 0x0e, 0xf8, 0xec, 0x0e, 0x1e, 0xa0, 0x37, 0xff, 0x0c, 0x09, 0x8b, 0x0c, 0x0b, 0x00, 0x00,
    0x01, 0xf4, 0x00, 0x5d, 0x02, 0x58, 0x00, 0x32, 0x00, 0xc8, 0x00, 0x32, 0x01, 0x90, 0x00, 0x32,
    0x02, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x2c, 0x00, 0x01, 0x00, 0x05,
    0x00, 0x18, 0x00, 0x02, 0x00, 0x06, 0x00, 0x01, 0x00, 0x02, 0xff, 0x38, 0x00, 0x01, 0x00, 0x03,
    0xff, 0x38, 0x00, 0x02, 0x00, 0x01, 0xff, 0x38, 0x00, 0x02, 0x00, 0x02, 0x01, 0xf4, 0x00, 0x03,
    0x00, 0x01, 0xff, 0x38
};

const size_t test_kern_one_otf_size = sizeof(test_kern_one_otf);

#endif /*LV_BUILD_TEST*/
