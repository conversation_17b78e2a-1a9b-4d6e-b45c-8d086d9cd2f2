#include "../../../lvgl.h"
#if LV_BUILD_TEST

#ifndef LV_ATTRIBUTE_MEM_ALIGN
    #define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMAGE_IMGBTN_RIGHT
    #define LV_ATTRIBUTE_IMAGE_IMGBTN_RIGHT
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMAGE_IMGBTN_RIGHT uint8_t imagebutton_right_map[] =
{
    /*Pixel format:  Blue: 8 bit, Green: 8 bit, Red: 8 bit, Alpha: 8 bit*/
    0x00, 0x38, 0x5f, 0x23, 0x00, 0x38, 0x5f, 0x14, 0x00, 0x38, 0x5f, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x5e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x2c, 0x55, 0xff, 0x00, 0x2f, 0x58, 0xdf, 0x00, 0x37, 0x5e, 0xb0, 0x00, 0x38, 0x5f, 0x34, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x5f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xdd, 0xe7, 0xed, 0xff, 0x81, 0x9f, 0xb4, 0xff, 0x0a, 0x41, 0x68, 0xff, 0x00, 0x37, 0x5e, 0xff, 0x00, 0x38, 0x5f, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0xba, 0xe2, 0xf6, 0xff, 0xe2, 0xf5, 0xff, 0xff, 0xf9, 0xfb, 0xfb, 0xff, 0x60, 0x84, 0x9d, 0xff, 0x00, 0x32, 0x5a, 0xff, 0x01, 0x39, 0x5f, 0x77, 0x00, 0x00, 0x00, 0x00, 0x00, 0x37, 0x5f, 0x00,
    0x2a, 0xa2, 0xe1, 0xff, 0x37, 0xa8, 0xe3, 0xff, 0x63, 0xbb, 0xea, 0xff, 0xf5, 0xfc, 0xff, 0xff, 0x71, 0x93, 0xaa, 0xff, 0x00, 0x2f, 0x58, 0xfc, 0x00, 0x37, 0x5f, 0x23, 0x00, 0x00, 0x00, 0x00,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x26, 0xa0, 0xe0, 0xff, 0x3b, 0xa9, 0xe3, 0xff, 0xe9, 0xf3, 0xfa, 0xff, 0x26, 0x56, 0x78, 0xff, 0x00, 0x37, 0x5f, 0xb4, 0x00, 0x00, 0x00, 0x00,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x26, 0xa0, 0xe0, 0xff, 0x4a, 0xb0, 0xe6, 0xff, 0x78, 0x9a, 0xb1, 0xff, 0x00, 0x38, 0x5f, 0xff, 0x00, 0x38, 0x5f, 0x07,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2c, 0xa3, 0xe2, 0xff, 0x5e, 0xa0, 0xc6, 0xff, 0x06, 0x3c, 0x62, 0xff, 0x00, 0x38, 0x5f, 0x47,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x3a, 0x96, 0xc8, 0xff, 0x04, 0x3c, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x23, 0x8c, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x57,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8a, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x21, 0x8a, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x22, 0x8b, 0xc4, 0xff, 0x01, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x26, 0x8d, 0xc5, 0xff, 0x02, 0x3b, 0x63, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2b, 0xa3, 0xe1, 0xff, 0x2b, 0x8d, 0xc2, 0xff, 0x02, 0x3b, 0x62, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x29, 0xa2, 0xe1, 0xff, 0x31, 0xa5, 0xe2, 0xff, 0x2c, 0x81, 0xb2, 0xff, 0x01, 0x39, 0x61, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2a, 0xa2, 0xe1, 0xff, 0x29, 0xa2, 0xe1, 0xff, 0x2e, 0xa4, 0xe2, 0xff, 0x4b, 0xb0, 0xe4, 0xff, 0x15, 0x63, 0x93, 0xff, 0x00, 0x38, 0x5f, 0xff, 0x00, 0x38, 0x5f, 0x54,
    0x2a, 0xa2, 0xe1, 0xff, 0x2d, 0xa3, 0xe1, 0xff, 0x36, 0xa7, 0xe3, 0xff, 0x4e, 0xb2, 0xe7, 0xff, 0x2c, 0x86, 0xba, 0xff, 0x08, 0x51, 0x80, 0xff, 0x00, 0x37, 0x5d, 0xff, 0x00, 0x38, 0x60, 0x54,
    0x44, 0xae, 0xe5, 0xff, 0x4b, 0xb2, 0xe8, 0xff, 0x4e, 0xb1, 0xe4, 0xff, 0x27, 0x7e, 0xb2, 0xff, 0x0b, 0x5b, 0x8d, 0xff, 0x0e, 0x5b, 0x88, 0xff, 0x01, 0x37, 0x5d, 0xff, 0x00, 0x39, 0x5f, 0x44,
    0x48, 0xa9, 0xde, 0xff, 0x30, 0x8b, 0xbf, 0xff, 0x10, 0x62, 0x95, 0xff, 0x0c, 0x5c, 0x8e, 0xff, 0x14, 0x66, 0x96, 0xff, 0x10, 0x57, 0x80, 0xff, 0x00, 0x36, 0x5b, 0xff, 0x00, 0x3b, 0x65, 0x0c,
    0x09, 0x58, 0x89, 0xff, 0x0c, 0x5c, 0x8d, 0xff, 0x11, 0x61, 0x92, 0xff, 0x16, 0x68, 0x96, 0xff, 0x18, 0x6a, 0x98, 0xff, 0x07, 0x3e, 0x61, 0xff, 0x00, 0x34, 0x58, 0xcb, 0x00, 0x00, 0x00, 0x00,
    0x17, 0x68, 0x97, 0xff, 0x17, 0x69, 0x97, 0xff, 0x18, 0x6a, 0x98, 0xff, 0x19, 0x6d, 0x9c, 0xff, 0x10, 0x52, 0x79, 0xff, 0x00, 0x29, 0x46, 0xff, 0x00, 0x2c, 0x4a, 0x40, 0x00, 0x00, 0x00, 0x00,
    0x18, 0x6a, 0x98, 0xff, 0x18, 0x6b, 0x99, 0xff, 0x18, 0x6a, 0x98, 0xff, 0x11, 0x55, 0x7c, 0xff, 0x00, 0x22, 0x3b, 0xff, 0x00, 0x25, 0x3e, 0xb0, 0x00, 0x06, 0x0a, 0x10, 0x00, 0x00, 0x00, 0x00,
    0x18, 0x68, 0x96, 0xff, 0x12, 0x57, 0x80, 0xff, 0x08, 0x3c, 0x5c, 0xff, 0x00, 0x25, 0x40, 0xff, 0x00, 0x1f, 0x35, 0xc7, 0x00, 0x07, 0x0b, 0x44, 0x00, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x24, 0x3e, 0xff, 0x00, 0x24, 0x3e, 0xff, 0x00, 0x24, 0x3d, 0xf8, 0x00, 0x16, 0x26, 0x93, 0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x34, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x1b, 0x2f, 0xb0, 0x00, 0x15, 0x23, 0x8c, 0x00, 0x02, 0x04, 0x5c, 0x00, 0x00, 0x00, 0x54, 0x00, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00, 0x24, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x58, 0x00, 0x00, 0x00, 0x58, 0x00, 0x00, 0x00, 0x57, 0x00, 0x00, 0x00, 0x3c, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x58, 0x00, 0x00, 0x00, 0x58, 0x00, 0x00, 0x00, 0x54, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x1b, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00, 0x48, 0x00, 0x00, 0x00, 0x34, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x27, 0x00, 0x00, 0x00, 0x18, 0x00, 0x00, 0x00, 0x0b, 0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
};

const lv_image_dsc_t imagebutton_right = {
    .header.w = 8,
    .header.h = 50,
    .header.stride = 32,
    .header.cf = LV_COLOR_FORMAT_ARGB8888,
    .data = imagebutton_right_map,
    .data_size = sizeof(imagebutton_right_map),
};

#endif /* LV_BUILD_TEST */
