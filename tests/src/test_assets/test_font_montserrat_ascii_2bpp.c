/*******************************************************************************
 * Size: 20 px
 * Bpp: 2
 * Opts:
 ******************************************************************************/

#include "../../../lvgl.h"
#if LV_BUILD_TEST

#ifndef TEST_FONT_MONTSERRAT_ASCII_2BPP
    #define TEST_FONT_MONTSERRAT_ASCII_2BPP 1
#endif

#if TEST_FONT_MONTSERRAT_ASCII_2BPP

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x7d, 0xf7, 0x9e, 0x78, 0xe3, 0x8e, 0x34, 0xd0,
    0x9, 0xbd, 0xe0,

    /* U+0022 "\"" */
    0xb0, 0xeb, 0xe, 0xb0, 0xdb, 0xd, 0xb0, 0xd5,
    0x8,

    /* U+0023 "#" */
    0x0, 0x70, 0x1c, 0x0, 0xb, 0x1, 0xc0, 0x0,
    0xe0, 0x2c, 0x3, 0xff, 0xff, 0xfe, 0x1a, 0xea,
    0xbe, 0x90, 0xd, 0x3, 0x40, 0x0, 0xc0, 0x34,
    0x0, 0x1c, 0x3, 0x40, 0x1, 0xc0, 0x70, 0x7,
    0xff, 0xff, 0xfc, 0x2b, 0xea, 0xfa, 0x40, 0x38,
    0xb, 0x0, 0x3, 0x40, 0xe0, 0x0, 0x34, 0xe,
    0x0,

    /* U+0024 "$" */
    0x0, 0x1c, 0x0, 0x0, 0x1c, 0x0, 0x0, 0x1c,
    0x0, 0x1, 0xff, 0xe0, 0xf, 0xff, 0xfc, 0x2f,
    0x1c, 0x14, 0x3c, 0x1c, 0x0, 0x3d, 0x1c, 0x0,
    0x1f, 0x5c, 0x0, 0xb, 0xfe, 0x40, 0x0, 0x7f,
    0xf4, 0x0, 0x1d, 0xbc, 0x0, 0x1c, 0x2d, 0x0,
    0x1c, 0x1e, 0x38, 0x1c, 0x3d, 0x3f, 0xff, 0xf8,
    0x6, 0xff, 0xd0, 0x0, 0x1c, 0x0, 0x0, 0x1c,
    0x0, 0x0, 0x4, 0x0,

    /* U+0025 "%" */
    0xb, 0xe0, 0x0, 0xe0, 0xb, 0x5e, 0x0, 0xb0,
    0x3, 0x41, 0xc0, 0x74, 0x0, 0xc0, 0x30, 0x38,
    0x0, 0x30, 0xc, 0x2c, 0x0, 0xe, 0xa, 0x1c,
    0x0, 0x0, 0xff, 0xe, 0x2f, 0x40, 0x0, 0xb,
    0x2d, 0x74, 0x0, 0x7, 0xc, 0x7, 0x0, 0x3,
    0x47, 0x1, 0xc0, 0x2, 0xc1, 0xc0, 0x30, 0x1,
    0xc0, 0x30, 0x1c, 0x0, 0xd0, 0xa, 0xd, 0x0,
    0xf0, 0x0, 0xbe, 0x0,

    /* U+0026 "&" */
    0x0, 0xbf, 0x40, 0x0, 0x3e, 0x6d, 0x0, 0x7,
    0x80, 0xf0, 0x0, 0x74, 0xf, 0x0, 0x3, 0xc2,
    0xd0, 0x0, 0x1f, 0xf4, 0x0, 0x1, 0xfc, 0x0,
    0x0, 0xbd, 0xf0, 0x20, 0x1e, 0x7, 0xc3, 0x83,
    0xc0, 0x1f, 0xb0, 0x3c, 0x0, 0x7f, 0x3, 0xd0,
    0x7, 0xf0, 0x1f, 0xeb, 0xf7, 0xc0, 0x2f, 0xf4,
    0x8, 0x0, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0xbb, 0xbb, 0xb5,

    /* U+0028 "(" */
    0x3, 0xc0, 0x74, 0xf, 0x0, 0xe0, 0x2d, 0x2,
    0xc0, 0x3c, 0x3, 0xc0, 0x3c, 0x3, 0xc0, 0x3c,
    0x3, 0xc0, 0x3c, 0x2, 0xc0, 0x2d, 0x0, 0xe0,
    0xf, 0x0, 0x74, 0x3, 0xc0,

    /* U+0029 ")" */
    0x38, 0xb, 0x0, 0xe0, 0x3c, 0xb, 0x41, 0xd0,
    0x38, 0xe, 0x3, 0xc0, 0xf0, 0x3c, 0xe, 0x3,
    0x81, 0xd0, 0xb4, 0x3c, 0xe, 0xb, 0x3, 0x80,

    /* U+002A "*" */
    0x2, 0x80, 0x12, 0x84, 0x7e, 0xbd, 0xb, 0xe0,
    0x1f, 0xf4, 0x76, 0x9d, 0x2, 0x80, 0x1, 0x40,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0xe, 0x0, 0x0, 0xe0, 0x0,
    0xe, 0x0, 0x0, 0xe0, 0xb, 0xff, 0xfd, 0x6a,
    0xfa, 0x80, 0xe, 0x0, 0x0, 0xe0, 0x0, 0xe,
    0x0,

    /* U+002C "," */
    0x18, 0x3e, 0x2d, 0x1c, 0x2c, 0x34,

    /* U+002D "-" */
    0xaa, 0x9f, 0xfe,

    /* U+002E "." */
    0x18, 0x3e, 0x2d,

    /* U+002F "/" */
    0x0, 0x1, 0x40, 0x1, 0xd0, 0x0, 0xb0, 0x0,
    0x3c, 0x0, 0x1d, 0x0, 0xb, 0x0, 0x3, 0xc0,
    0x1, 0xd0, 0x0, 0xb0, 0x0, 0x38, 0x0, 0x1d,
    0x0, 0xb, 0x0, 0x3, 0x80, 0x1, 0xd0, 0x0,
    0xb0, 0x0, 0x38, 0x0, 0x1d, 0x0, 0xb, 0x0,
    0x3, 0x80, 0x1, 0xd0, 0x0,

    /* U+0030 "0" */
    0x0, 0xbf, 0x80, 0x0, 0xff, 0xfd, 0x0, 0xf8,
    0x7, 0xc0, 0x7c, 0x0, 0x78, 0x2d, 0x0, 0xf,
    0xf, 0x0, 0x3, 0xc3, 0xc0, 0x0, 0xb4, 0xf0,
    0x0, 0x2d, 0x3c, 0x0, 0xf, 0xb, 0x40, 0x3,
    0xc1, 0xf0, 0x1, 0xe0, 0x3e, 0x1, 0xf0, 0x3,
    0xff, 0xf4, 0x0, 0x2f, 0xe0, 0x0,

    /* U+0031 "1" */
    0xff, 0xdb, 0xfd, 0x2, 0xd0, 0x2d, 0x2, 0xd0,
    0x2d, 0x2, 0xd0, 0x2d, 0x2, 0xd0, 0x2d, 0x2,
    0xd0, 0x2d, 0x2, 0xd0, 0x2d,

    /* U+0032 "2" */
    0x7, 0xfe, 0x0, 0xff, 0xff, 0x47, 0x80, 0x2f,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0xf0, 0x0, 0x3,
    0xc0, 0x0, 0x3d, 0x0, 0x3, 0xe0, 0x0, 0x3e,
    0x0, 0x3, 0xe0, 0x0, 0x3d, 0x0, 0x3, 0xd0,
    0x0, 0x3f, 0xff, 0xf9, 0xff, 0xff, 0xf0,

    /* U+0033 "3" */
    0x7f, 0xff, 0xf0, 0xff, 0xff, 0xc0, 0x0, 0x3c,
    0x0, 0x3, 0xd0, 0x0, 0x3d, 0x0, 0x2, 0xe0,
    0x0, 0xf, 0xf8, 0x0, 0x16, 0xfc, 0x0, 0x0,
    0xf4, 0x0, 0x2, 0xe0, 0x0, 0xb, 0x5d, 0x0,
    0x7c, 0x7f, 0xff, 0xd0, 0x2f, 0xf8, 0x0,

    /* U+0034 "4" */
    0x0, 0x1, 0xe0, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0xf, 0x0, 0x0, 0x3, 0xd0, 0x0, 0x0, 0xb8,
    0x0, 0x0, 0x1f, 0x0, 0x0, 0x3, 0xc0, 0x74,
    0x0, 0xf4, 0x7, 0x40, 0x2e, 0x0, 0x74, 0x3,
    0xff, 0xff, 0xfc, 0x3f, 0xff, 0xff, 0xc0, 0x0,
    0x7, 0x40, 0x0, 0x0, 0x74, 0x0, 0x0, 0x7,
    0x40,

    /* U+0035 "5" */
    0xf, 0xff, 0xf0, 0x3f, 0xff, 0xc0, 0xe0, 0x0,
    0x7, 0x80, 0x0, 0x1d, 0x0, 0x0, 0x7f, 0xe4,
    0x2, 0xff, 0xfd, 0x0, 0x0, 0xbd, 0x0, 0x0,
    0xb8, 0x0, 0x1, 0xf0, 0x0, 0x7, 0x8e, 0x0,
    0x7d, 0x7f, 0xff, 0xe0, 0x1f, 0xf9, 0x0,

    /* U+0036 "6" */
    0x0, 0x6f, 0xe4, 0x2, 0xff, 0xf8, 0xb, 0x80,
    0x0, 0x1f, 0x0, 0x0, 0x2d, 0x0, 0x0, 0x3c,
    0x7f, 0xd0, 0x3e, 0xfb, 0xf8, 0x3f, 0x40, 0x3d,
    0x3e, 0x0, 0x1e, 0x3d, 0x0, 0xf, 0x1e, 0x0,
    0x1e, 0xf, 0x40, 0x3d, 0x7, 0xfb, 0xf8, 0x0,
    0xbf, 0x90,

    /* U+0037 "7" */
    0x7f, 0xff, 0xfc, 0x7f, 0xff, 0xfc, 0x78, 0x0,
    0x78, 0x78, 0x0, 0xf0, 0x10, 0x1, 0xf0, 0x0,
    0x3, 0xd0, 0x0, 0x3, 0xc0, 0x0, 0xb, 0x40,
    0x0, 0xf, 0x0, 0x0, 0x2e, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0x78, 0x0, 0x0, 0xf0, 0x0, 0x1,
    0xe0, 0x0,

    /* U+0038 "8" */
    0x1, 0xff, 0x90, 0xf, 0xfb, 0xf8, 0x1f, 0x0,
    0x3d, 0x2d, 0x0, 0x2d, 0x1e, 0x0, 0x3d, 0xb,
    0xea, 0xf8, 0x7, 0xff, 0xf4, 0x1f, 0x40, 0x7d,
    0x3c, 0x0, 0x1f, 0x3c, 0x0, 0xf, 0x3c, 0x0,
    0xf, 0x2e, 0x0, 0x3e, 0xf, 0xfb, 0xfc, 0x1,
    0xff, 0x90,

    /* U+0039 "9" */
    0x2, 0xfe, 0x40, 0x1f, 0xef, 0xe0, 0x3d, 0x0,
    0xf4, 0x78, 0x0, 0x3c, 0x78, 0x0, 0x3c, 0x3d,
    0x0, 0xfd, 0x2f, 0xeb, 0xfd, 0x6, 0xfe, 0x2d,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0xb8, 0x4, 0x2, 0xf0, 0x1f, 0xff, 0xc0, 0xb,
    0xfd, 0x0,

    /* U+003A ":" */
    0x2d, 0x3e, 0x18, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x18, 0x3e, 0x2d,

    /* U+003B ";" */
    0x2d, 0x3e, 0x18, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x18, 0x3e, 0x2d, 0x1c, 0x2c, 0x34,

    /* U+003C "<" */
    0x0, 0x0, 0x40, 0x0, 0xbd, 0x1, 0xfe, 0x42,
    0xfe, 0x0, 0xbd, 0x0, 0xb, 0xe4, 0x0, 0xb,
    0xf4, 0x0, 0x7, 0xf8, 0x0, 0x2, 0xd0, 0x0,
    0x0,

    /* U+003D "=" */
    0xbf, 0xff, 0xd6, 0xaa, 0xa8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfd, 0x6a,
    0xaa, 0x80,

    /* U+003E ">" */
    0x40, 0x0, 0xb, 0xd0, 0x0, 0x1f, 0xe0, 0x0,
    0xb, 0xf4, 0x0, 0x7, 0xd0, 0x1, 0xfc, 0x2,
    0xfd, 0x7, 0xf8, 0x0, 0xb4, 0x0, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x7, 0xfe, 0x0, 0xff, 0xff, 0x47, 0x80, 0x2f,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0xf0, 0x0, 0xb,
    0x40, 0x0, 0xf8, 0x0, 0xf, 0x80, 0x0, 0x7c,
    0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x0, 0x0, 0xbc, 0x0, 0x1, 0xe0, 0x0,

    /* U+0040 "@" */
    0x0, 0x6, 0xff, 0xe0, 0x0, 0x0, 0x3f, 0x95,
    0xbe, 0x0, 0x1, 0xf0, 0x0, 0x7, 0xc0, 0x3,
    0x80, 0x69, 0x9, 0xb0, 0xf, 0x3, 0xff, 0xdd,
    0x38, 0x1c, 0xf, 0x41, 0xfd, 0x1c, 0x2c, 0x2d,
    0x0, 0x3d, 0xd, 0x38, 0x3c, 0x0, 0x2d, 0xe,
    0x34, 0x3c, 0x0, 0x1d, 0xa, 0x34, 0x3c, 0x0,
    0x1d, 0xa, 0x38, 0x2c, 0x0, 0x2d, 0xe, 0x2c,
    0xf, 0x0, 0x7d, 0xd, 0x1c, 0x7, 0xeb, 0xdf,
    0xbc, 0xf, 0x1, 0xfe, 0x47, 0xe0, 0x3, 0x80,
    0x0, 0x0, 0x0, 0x1, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x96, 0xb4, 0x0, 0x0, 0x6, 0xff,
    0x90, 0x0,

    /* U+0041 "A" */
    0x0, 0x3, 0xe0, 0x0, 0x0, 0x7, 0xf0, 0x0,
    0x0, 0xf, 0xb4, 0x0, 0x0, 0x1e, 0x3c, 0x0,
    0x0, 0x2c, 0x2d, 0x0, 0x0, 0x3c, 0xf, 0x0,
    0x0, 0xb4, 0xf, 0x0, 0x0, 0xf0, 0x7, 0x80,
    0x2, 0xd0, 0x3, 0xc0, 0x3, 0xff, 0xff, 0xe0,
    0x7, 0xea, 0xaa, 0xf0, 0xf, 0x0, 0x0, 0xb8,
    0x1e, 0x0, 0x0, 0x3c, 0x3d, 0x0, 0x0, 0x2d,

    /* U+0042 "B" */
    0xff, 0xff, 0x90, 0x3e, 0xaa, 0xfe, 0xf, 0x0,
    0x7, 0xc3, 0xc0, 0x0, 0xb4, 0xf0, 0x0, 0x3d,
    0x3c, 0x0, 0x2f, 0xf, 0xff, 0xff, 0x3, 0xea,
    0xaf, 0xf0, 0xf0, 0x0, 0x2e, 0x3c, 0x0, 0x3,
    0xcf, 0x0, 0x0, 0xf3, 0xc0, 0x0, 0xbc, 0xfa,
    0xaa, 0xfd, 0x3f, 0xff, 0xf8, 0x0,

    /* U+0043 "C" */
    0x0, 0x1f, 0xf9, 0x0, 0x1f, 0xff, 0xfc, 0x7,
    0xe0, 0x2, 0xd0, 0xf4, 0x0, 0x0, 0x2e, 0x0,
    0x0, 0x3, 0xc0, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x2,
    0xe0, 0x0, 0x0, 0xf, 0x40, 0x0, 0x0, 0x7e,
    0x0, 0x2d, 0x1, 0xff, 0xff, 0xc0, 0x1, 0xff,
    0x90,

    /* U+0044 "D" */
    0xff, 0xff, 0x90, 0xf, 0xff, 0xff, 0x80, 0xf0,
    0x0, 0x7f, 0xf, 0x0, 0x0, 0xf8, 0xf0, 0x0,
    0x3, 0xcf, 0x0, 0x0, 0x2d, 0xf0, 0x0, 0x2,
    0xef, 0x0, 0x0, 0x1e, 0xf0, 0x0, 0x2, 0xdf,
    0x0, 0x0, 0x3c, 0xf0, 0x0, 0xb, 0x8f, 0x0,
    0x7, 0xf0, 0xff, 0xff, 0xf8, 0xf, 0xff, 0xf9,
    0x0,

    /* U+0045 "E" */
    0xff, 0xff, 0xf3, 0xff, 0xff, 0xcf, 0x0, 0x0,
    0x3c, 0x0, 0x0, 0xf0, 0x0, 0x3, 0xc0, 0x0,
    0xf, 0xff, 0xfc, 0x3f, 0xff, 0xf0, 0xf0, 0x0,
    0x3, 0xc0, 0x0, 0xf, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0xff, 0xff, 0xf3, 0xff, 0xff, 0xd0,

    /* U+0046 "F" */
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xf,
    0x0, 0x0, 0xf0, 0x0, 0xf, 0x0, 0x0, 0xff,
    0xff, 0xcf, 0xff, 0xfc, 0xf0, 0x0, 0xf, 0x0,
    0x0, 0xf0, 0x0, 0xf, 0x0, 0x0, 0xf0, 0x0,
    0xf, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x1f, 0xf9, 0x0, 0x1f, 0xff, 0xfc, 0x7,
    0xe0, 0x2, 0xd0, 0xf4, 0x0, 0x0, 0x2e, 0x0,
    0x0, 0x3, 0xc0, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0x3, 0xc0, 0x0, 0xe, 0x3c, 0x0, 0x0, 0xe2,
    0xe0, 0x0, 0xe, 0xf, 0x40, 0x0, 0xe0, 0x7e,
    0x0, 0x2e, 0x1, 0xff, 0xff, 0xd0, 0x1, 0xff,
    0x90,

    /* U+0048 "H" */
    0xf0, 0x0, 0xf, 0x3c, 0x0, 0x3, 0xcf, 0x0,
    0x0, 0xf3, 0xc0, 0x0, 0x3c, 0xf0, 0x0, 0xf,
    0x3c, 0x0, 0x3, 0xcf, 0xff, 0xff, 0xf3, 0xff,
    0xff, 0xfc, 0xf0, 0x0, 0xf, 0x3c, 0x0, 0x3,
    0xcf, 0x0, 0x0, 0xf3, 0xc0, 0x0, 0x3c, 0xf0,
    0x0, 0xf, 0x3c, 0x0, 0x3, 0xc0,

    /* U+0049 "I" */
    0xf3, 0xcf, 0x3c, 0xf3, 0xcf, 0x3c, 0xf3, 0xcf,
    0x3c, 0xf3, 0xc0,

    /* U+004A "J" */
    0xf, 0xff, 0xd0, 0xbf, 0xfd, 0x0, 0x2, 0xd0,
    0x0, 0x2d, 0x0, 0x2, 0xd0, 0x0, 0x2d, 0x0,
    0x2, 0xd0, 0x0, 0x2d, 0x0, 0x2, 0xd0, 0x0,
    0x2d, 0x0, 0x3, 0xc3, 0xc0, 0x7c, 0x2f, 0xff,
    0x40, 0x7f, 0xd0,

    /* U+004B "K" */
    0xf0, 0x0, 0x2e, 0x3c, 0x0, 0x2e, 0xf, 0x0,
    0x2e, 0x3, 0xc0, 0x2e, 0x0, 0xf0, 0x2e, 0x0,
    0x3c, 0x2e, 0x0, 0xf, 0x2f, 0x0, 0x3, 0xef,
    0xf0, 0x0, 0xff, 0x2f, 0x0, 0x3f, 0x3, 0xe0,
    0xf, 0x0, 0x3d, 0x3, 0xc0, 0x3, 0xd0, 0xf0,
    0x0, 0x7c, 0x3c, 0x0, 0x7, 0xc0,

    /* U+004C "L" */
    0xf0, 0x0, 0xf, 0x0, 0x0, 0xf0, 0x0, 0xf,
    0x0, 0x0, 0xf0, 0x0, 0xf, 0x0, 0x0, 0xf0,
    0x0, 0xf, 0x0, 0x0, 0xf0, 0x0, 0xf, 0x0,
    0x0, 0xf0, 0x0, 0xf, 0x0, 0x0, 0xff, 0xff,
    0xef, 0xff, 0xfe,

    /* U+004D "M" */
    0xf0, 0x0, 0x0, 0x3f, 0xe0, 0x0, 0x2, 0xff,
    0xc0, 0x0, 0xf, 0xff, 0xc0, 0x0, 0xbf, 0xfb,
    0x40, 0x7, 0xbf, 0xcf, 0x0, 0x3c, 0xff, 0x1e,
    0x1, 0xd3, 0xfc, 0x3c, 0xf, 0xf, 0xf0, 0x3c,
    0xb4, 0x3f, 0xc0, 0xbb, 0x80, 0xff, 0x0, 0xfc,
    0x3, 0xfc, 0x1, 0xd0, 0xf, 0xf0, 0x1, 0x0,
    0x3f, 0xc0, 0x0, 0x0, 0xf0,

    /* U+004E "N" */
    0xf0, 0x0, 0xf, 0x3f, 0x0, 0x3, 0xcf, 0xe0,
    0x0, 0xf3, 0xfe, 0x0, 0x3c, 0xf7, 0xd0, 0xf,
    0x3c, 0x7c, 0x3, 0xcf, 0xb, 0xc0, 0xf3, 0xc0,
    0xbc, 0x3c, 0xf0, 0xf, 0x8f, 0x3c, 0x0, 0xf7,
    0xcf, 0x0, 0x1f, 0xf3, 0xc0, 0x2, 0xfc, 0xf0,
    0x0, 0x2f, 0x3c, 0x0, 0x3, 0xc0,

    /* U+004F "O" */
    0x0, 0x1f, 0xf9, 0x0, 0x1, 0xff, 0xff, 0xc0,
    0x7, 0xe0, 0x2, 0xf0, 0xf, 0x40, 0x0, 0xbc,
    0x2e, 0x0, 0x0, 0x3d, 0x3c, 0x0, 0x0, 0x1e,
    0x3c, 0x0, 0x0, 0xf, 0x3c, 0x0, 0x0, 0xf,
    0x3c, 0x0, 0x0, 0x1e, 0x2e, 0x0, 0x0, 0x3d,
    0xf, 0x40, 0x0, 0xbc, 0x7, 0xe0, 0x2, 0xf0,
    0x1, 0xff, 0xff, 0xc0, 0x0, 0x1f, 0xf9, 0x0,

    /* U+0050 "P" */
    0xff, 0xff, 0x40, 0xff, 0xff, 0xf0, 0xf0, 0x0,
    0xbc, 0xf0, 0x0, 0x3c, 0xf0, 0x0, 0x2d, 0xf0,
    0x0, 0x2d, 0xf0, 0x0, 0x3c, 0xf0, 0x1, 0xf8,
    0xff, 0xff, 0xe0, 0xff, 0xfa, 0x40, 0xf0, 0x0,
    0x0, 0xf0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0xf0,
    0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x1f, 0xf9, 0x0, 0x0, 0x7f, 0xff, 0xf0,
    0x0, 0x7e, 0x0, 0x6f, 0x0, 0x3d, 0x0, 0x2,
    0xf0, 0x2e, 0x0, 0x0, 0x3d, 0xf, 0x0, 0x0,
    0x7, 0x83, 0xc0, 0x0, 0x0, 0xf0, 0xf0, 0x0,
    0x0, 0x3c, 0x3c, 0x0, 0x0, 0x1e, 0xb, 0x80,
    0x0, 0xf, 0x40, 0xf4, 0x0, 0xb, 0xc0, 0x1f,
    0x80, 0xb, 0xc0, 0x1, 0xff, 0xff, 0xc0, 0x0,
    0xb, 0xff, 0x40, 0x0, 0x0, 0xb, 0xc0, 0x20,
    0x0, 0x0, 0xbe, 0xbc, 0x0, 0x0, 0x6, 0xf8,
    0x0,

    /* U+0052 "R" */
    0xff, 0xff, 0x40, 0xff, 0xff, 0xf0, 0xf0, 0x0,
    0xbc, 0xf0, 0x0, 0x3c, 0xf0, 0x0, 0x2d, 0xf0,
    0x0, 0x2d, 0xf0, 0x0, 0x3c, 0xf0, 0x1, 0xf8,
    0xff, 0xff, 0xe0, 0xff, 0xef, 0x80, 0xf0, 0x3,
    0xc0, 0xf0, 0x1, 0xf0, 0xf0, 0x0, 0xb8, 0xf0,
    0x0, 0x3d,

    /* U+0053 "S" */
    0x1, 0xff, 0xd0, 0xf, 0xff, 0xfc, 0x2f, 0x0,
    0x14, 0x3c, 0x0, 0x0, 0x3d, 0x0, 0x0, 0x1f,
    0x40, 0x0, 0x7, 0xfe, 0x40, 0x0, 0x6f, 0xf4,
    0x0, 0x0, 0xbc, 0x0, 0x0, 0x2d, 0x0, 0x0,
    0x1e, 0x38, 0x0, 0x3d, 0x2f, 0xff, 0xf8, 0x2,
    0xff, 0x90,

    /* U+0054 "T" */
    0xff, 0xff, 0xfe, 0xbf, 0xff, 0xfe, 0x0, 0x3c,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0x3c, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0x3c, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x3c,
    0x0, 0x0, 0x3c, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0x3c, 0x0,

    /* U+0055 "U" */
    0x3c, 0x0, 0x3, 0xcf, 0x0, 0x0, 0xf3, 0xc0,
    0x0, 0x3c, 0xf0, 0x0, 0xf, 0x3c, 0x0, 0x3,
    0xcf, 0x0, 0x0, 0xf3, 0xc0, 0x0, 0x3c, 0xf0,
    0x0, 0xf, 0x3c, 0x0, 0x3, 0xcf, 0x0, 0x1,
    0xe2, 0xe0, 0x0, 0xf4, 0x3e, 0x0, 0xbc, 0x7,
    0xff, 0xfc, 0x0, 0x2f, 0xf8, 0x0,

    /* U+0056 "V" */
    0x3d, 0x0, 0x0, 0x3c, 0x1f, 0x0, 0x0, 0x78,
    0xf, 0x0, 0x0, 0xf0, 0x7, 0x80, 0x1, 0xe0,
    0x3, 0xc0, 0x2, 0xd0, 0x2, 0xe0, 0x3, 0xc0,
    0x0, 0xf0, 0xb, 0x40, 0x0, 0xb4, 0xf, 0x0,
    0x0, 0x7c, 0x1e, 0x0, 0x0, 0x3d, 0x3c, 0x0,
    0x0, 0x1e, 0x78, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0xb, 0xf0, 0x0, 0x0, 0x3, 0xd0, 0x0,

    /* U+0057 "W" */
    0x3c, 0x0, 0xf, 0x40, 0x0, 0xe3, 0xc0, 0x0,
    0xfc, 0x0, 0x2d, 0x2e, 0x0, 0x1f, 0xc0, 0x3,
    0xc0, 0xf0, 0x3, 0xed, 0x0, 0x38, 0xf, 0x0,
    0x38, 0xf0, 0xb, 0x40, 0xb8, 0x7, 0x4f, 0x0,
    0xf0, 0x3, 0xc0, 0xf0, 0x74, 0xe, 0x0, 0x3c,
    0xe, 0x3, 0xc2, 0xd0, 0x2, 0xe2, 0xd0, 0x3c,
    0x3c, 0x0, 0xf, 0x3c, 0x1, 0xd3, 0x80, 0x0,
    0xf7, 0x80, 0xf, 0xb4, 0x0, 0xb, 0xf4, 0x0,
    0xff, 0x0, 0x0, 0x3f, 0x0, 0x7, 0xe0, 0x0,
    0x3, 0xe0, 0x0, 0x3d, 0x0,

    /* U+0058 "X" */
    0x3c, 0x0, 0xb, 0x41, 0xf0, 0x1, 0xe0, 0xb,
    0x80, 0x3c, 0x0, 0x3d, 0xf, 0x40, 0x0, 0xf2,
    0xe0, 0x0, 0x7, 0xfc, 0x0, 0x0, 0x2f, 0x0,
    0x0, 0x3, 0xf4, 0x0, 0x0, 0xbb, 0xc0, 0x0,
    0x1f, 0x1f, 0x0, 0x3, 0xc0, 0xb8, 0x0, 0xf4,
    0x3, 0xd0, 0x2e, 0x0, 0xf, 0x7, 0xc0, 0x0,
    0x7c,

    /* U+0059 "Y" */
    0x3d, 0x0, 0x1, 0xe0, 0x3c, 0x0, 0xf, 0x0,
    0xb8, 0x0, 0x74, 0x0, 0xf0, 0x3, 0xc0, 0x1,
    0xf0, 0x2d, 0x0, 0x2, 0xd1, 0xe0, 0x0, 0x3,
    0xcf, 0x0, 0x0, 0xb, 0xf4, 0x0, 0x0, 0xf,
    0xc0, 0x0, 0x0, 0x2d, 0x0, 0x0, 0x0, 0xb4,
    0x0, 0x0, 0x2, 0xd0, 0x0, 0x0, 0xb, 0x40,
    0x0, 0x0, 0x2d, 0x0, 0x0,

    /* U+005A "Z" */
    0x3f, 0xff, 0xff, 0x4f, 0xff, 0xff, 0xc0, 0x0,
    0x3, 0xd0, 0x0, 0x2, 0xe0, 0x0, 0x2, 0xf0,
    0x0, 0x1, 0xf0, 0x0, 0x0, 0xf0, 0x0, 0x0,
    0xf4, 0x0, 0x0, 0xf8, 0x0, 0x0, 0xb8, 0x0,
    0x0, 0x7c, 0x0, 0x0, 0x7c, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x4f, 0xff, 0xff, 0xe0,

    /* U+005B "[" */
    0xff, 0x7e, 0x8f, 0x3, 0xc0, 0xf0, 0x3c, 0xf,
    0x3, 0xc0, 0xf0, 0x3c, 0xf, 0x3, 0xc0, 0xf0,
    0x3c, 0xf, 0x3, 0xc0, 0xf0, 0x3e, 0x8f, 0xf4,

    /* U+005C "\\" */
    0x50, 0x0, 0x1d, 0x0, 0x3, 0x80, 0x0, 0xf0,
    0x0, 0x1d, 0x0, 0x3, 0x80, 0x0, 0xb0, 0x0,
    0x1d, 0x0, 0x3, 0x80, 0x0, 0xb0, 0x0, 0x1d,
    0x0, 0x3, 0x80, 0x0, 0xb0, 0x0, 0x1d, 0x0,
    0x3, 0x80, 0x0, 0xb0, 0x0, 0x1d, 0x0, 0x3,
    0x80, 0x0, 0xb0, 0x0, 0x1d,

    /* U+005D "]" */
    0xbf, 0x9b, 0xe0, 0x78, 0x1e, 0x7, 0x81, 0xe0,
    0x78, 0x1e, 0x7, 0x81, 0xe0, 0x78, 0x1e, 0x7,
    0x81, 0xe0, 0x78, 0x1e, 0x7, 0x9b, 0xeb, 0xf8,

    /* U+005E "^" */
    0x0, 0x50, 0x0, 0x1f, 0x0, 0x3, 0xb4, 0x0,
    0x36, 0xc0, 0xb, 0xd, 0x0, 0xd0, 0xe0, 0x1c,
    0x7, 0x3, 0x80, 0x38, 0x70, 0x2, 0xc0,

    /* U+005F "_" */
    0x0, 0x0, 0xf, 0xff, 0xff, 0x0, 0x0, 0x0,

    /* U+0060 "`" */
    0x14, 0x1, 0xf0, 0x3, 0xc0,

    /* U+0061 "a" */
    0x1b, 0xf9, 0x7, 0xfb, 0xf4, 0x20, 0x7, 0xc0,
    0x0, 0x3c, 0x0, 0x3, 0xc2, 0xff, 0xfc, 0xbd,
    0x57, 0xcf, 0x0, 0x2c, 0xf0, 0x3, 0xcb, 0xd6,
    0xfc, 0x1f, 0xf6, 0xc0,

    /* U+0062 "b" */
    0x38, 0x0, 0x0, 0x38, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x38, 0x0, 0x0, 0x38, 0xbf, 0x80, 0x3f,
    0xff, 0xf4, 0x3f, 0x0, 0xbc, 0x3c, 0x0, 0x2d,
    0x3c, 0x0, 0x1e, 0x38, 0x0, 0xf, 0x3c, 0x0,
    0x1e, 0x3c, 0x0, 0x2d, 0x3f, 0x0, 0xbc, 0x3f,
    0xff, 0xf4, 0x38, 0xbf, 0x80,

    /* U+0063 "c" */
    0x0, 0xbf, 0x80, 0x1f, 0xff, 0xc1, 0xf4, 0x7,
    0x4f, 0x40, 0x0, 0x3c, 0x0, 0x0, 0xf0, 0x0,
    0x3, 0xc0, 0x0, 0xf, 0x40, 0x0, 0x1f, 0x40,
    0x74, 0x1f, 0xff, 0xc0, 0xb, 0xf8, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0xf, 0x0, 0x0, 0xf, 0x0, 0x0,
    0xf, 0x0, 0x0, 0xf, 0x1, 0xbf, 0x4f, 0xb,
    0xff, 0xef, 0x1f, 0x40, 0x7f, 0x3d, 0x0, 0x2f,
    0x3c, 0x0, 0xf, 0x3c, 0x0, 0xf, 0x3c, 0x0,
    0xf, 0x3d, 0x0, 0x2f, 0x1f, 0x0, 0x7f, 0xb,
    0xeb, 0xef, 0x1, 0xbf, 0x4f,

    /* U+0065 "e" */
    0x1, 0xbf, 0x40, 0xb, 0xfb, 0xf0, 0x1f, 0x0,
    0xb8, 0x3c, 0x0, 0x3c, 0x3c, 0x0, 0x2d, 0x3f,
    0xff, 0xfd, 0x3d, 0x55, 0x54, 0x3c, 0x0, 0x0,
    0x1f, 0x40, 0x20, 0x7, 0xff, 0xf4, 0x0, 0xbf,
    0x80,

    /* U+0066 "f" */
    0x1, 0xfd, 0x7, 0xed, 0xf, 0x0, 0xf, 0x0,
    0xbf, 0xfc, 0x6f, 0xa8, 0xf, 0x0, 0xf, 0x0,
    0xf, 0x0, 0xf, 0x0, 0xf, 0x0, 0xf, 0x0,
    0xf, 0x0, 0xf, 0x0, 0xf, 0x0,

    /* U+0067 "g" */
    0x1, 0xbf, 0x4f, 0xb, 0xff, 0xff, 0x1f, 0x40,
    0x7f, 0x3d, 0x0, 0x1f, 0x3c, 0x0, 0xf, 0x3c,
    0x0, 0xf, 0x3c, 0x0, 0xf, 0x3d, 0x0, 0x1f,
    0x1f, 0x40, 0x7f, 0xb, 0xfb, 0xff, 0x1, 0xbf,
    0x4f, 0x0, 0x0, 0xe, 0x9, 0x0, 0x3d, 0x1f,
    0xfb, 0xf8, 0x2, 0xff, 0x90,

    /* U+0068 "h" */
    0x38, 0x0, 0x0, 0xe0, 0x0, 0x3, 0x80, 0x0,
    0xe, 0x0, 0x0, 0x38, 0xbf, 0x80, 0xff, 0xff,
    0xc3, 0xf0, 0xf, 0x4f, 0x0, 0x1f, 0x3c, 0x0,
    0x3c, 0xe0, 0x0, 0xf3, 0x80, 0x3, 0xce, 0x0,
    0xf, 0x38, 0x0, 0x3c, 0xe0, 0x0, 0xf3, 0x80,
    0x3, 0xc0,

    /* U+0069 "i" */
    0x38, 0x7c, 0x24, 0x0, 0x38, 0x38, 0x38, 0x38,
    0x38, 0x38, 0x38, 0x38, 0x38, 0x38, 0x38,

    /* U+006A "j" */
    0x0, 0xf0, 0x7, 0xc0, 0x5, 0x0, 0x0, 0x0,
    0xf0, 0x3, 0xc0, 0xf, 0x0, 0x3c, 0x0, 0xf0,
    0x3, 0xc0, 0xf, 0x0, 0x3c, 0x0, 0xf0, 0x3,
    0xc0, 0xf, 0x0, 0x3c, 0x1, 0xe1, 0xef, 0x47,
    0xf4, 0x0,

    /* U+006B "k" */
    0x38, 0x0, 0x0, 0x38, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x38, 0x0, 0x0, 0x38, 0x0, 0xf4, 0x38,
    0x3, 0xd0, 0x38, 0xf, 0x40, 0x38, 0x3d, 0x0,
    0x39, 0xf8, 0x0, 0x3f, 0xfc, 0x0, 0x3f, 0x6f,
    0x0, 0x3d, 0xf, 0x80, 0x38, 0x3, 0xd0, 0x38,
    0x1, 0xf0, 0x38, 0x0, 0x7c,

    /* U+006C "l" */
    0x38, 0xe3, 0x8e, 0x38, 0xe3, 0x8e, 0x38, 0xe3,
    0x8e, 0x38, 0xe3, 0x80,

    /* U+006D "m" */
    0x38, 0xbf, 0x41, 0xff, 0x40, 0xff, 0xbf, 0xaf,
    0xbf, 0x43, 0xe0, 0xf, 0xe0, 0x1f, 0xf, 0x0,
    0x2f, 0x0, 0x2d, 0x3c, 0x0, 0x78, 0x0, 0xb4,
    0xe0, 0x1, 0xe0, 0x2, 0xd3, 0x80, 0x7, 0x80,
    0xb, 0x4e, 0x0, 0x1e, 0x0, 0x2d, 0x38, 0x0,
    0x78, 0x0, 0xb4, 0xe0, 0x1, 0xe0, 0x2, 0xd3,
    0x80, 0x7, 0x80, 0xb, 0x40,

    /* U+006E "n" */
    0x38, 0xbf, 0x80, 0xff, 0xef, 0xc3, 0xf0, 0xf,
    0x4f, 0x0, 0x1f, 0x3c, 0x0, 0x3c, 0xe0, 0x0,
    0xf3, 0x80, 0x3, 0xce, 0x0, 0xf, 0x38, 0x0,
    0x3c, 0xe0, 0x0, 0xf3, 0x80, 0x3, 0xc0,

    /* U+006F "o" */
    0x0, 0xbf, 0x80, 0x7, 0xff, 0xf0, 0x1f, 0x40,
    0x7c, 0x3d, 0x0, 0x2d, 0x3c, 0x0, 0xe, 0x3c,
    0x0, 0xf, 0x3c, 0x0, 0xe, 0x3d, 0x0, 0x2d,
    0x1f, 0x40, 0x7c, 0x7, 0xff, 0xf0, 0x0, 0xbf,
    0x80,

    /* U+0070 "p" */
    0x38, 0xbf, 0x80, 0x3f, 0xeb, 0xf4, 0x3f, 0x0,
    0x7c, 0x3c, 0x0, 0x2d, 0x3c, 0x0, 0x1e, 0x38,
    0x0, 0xf, 0x3c, 0x0, 0x1e, 0x3c, 0x0, 0x2d,
    0x3f, 0x0, 0xbc, 0x3f, 0xff, 0xf4, 0x38, 0xbf,
    0x80, 0x38, 0x0, 0x0, 0x38, 0x0, 0x0, 0x38,
    0x0, 0x0, 0x38, 0x0, 0x0,

    /* U+0071 "q" */
    0x1, 0xbf, 0x4f, 0xb, 0xff, 0xef, 0x1f, 0x40,
    0x7f, 0x3d, 0x0, 0x2f, 0x3c, 0x0, 0xf, 0x3c,
    0x0, 0xf, 0x3c, 0x0, 0xf, 0x3d, 0x0, 0x2f,
    0x1f, 0x40, 0x7f, 0xb, 0xff, 0xef, 0x1, 0xbf,
    0x4f, 0x0, 0x0, 0xf, 0x0, 0x0, 0xf, 0x0,
    0x0, 0xf, 0x0, 0x0, 0xf,

    /* U+0072 "r" */
    0x38, 0xb8, 0xff, 0xd3, 0xf4, 0xf, 0x40, 0x3c,
    0x0, 0xf0, 0x3, 0x80, 0xe, 0x0, 0x38, 0x0,
    0xe0, 0x3, 0x80, 0x0,

    /* U+0073 "s" */
    0x7, 0xfe, 0x42, 0xfe, 0xfc, 0x3c, 0x0, 0x3,
    0xc0, 0x0, 0x3f, 0x40, 0x0, 0xff, 0xe0, 0x0,
    0x5b, 0xc0, 0x0, 0x2d, 0x20, 0x2, 0xd7, 0xfa,
    0xfc, 0x1b, 0xfd, 0x0,

    /* U+0074 "t" */
    0x6, 0x0, 0xf, 0x0, 0xf, 0x0, 0xbf, 0xfc,
    0x6f, 0xa8, 0xf, 0x0, 0xf, 0x0, 0xf, 0x0,
    0xf, 0x0, 0xf, 0x0, 0xf, 0x0, 0xf, 0x40,
    0x7, 0xed, 0x1, 0xfd,

    /* U+0075 "u" */
    0x78, 0x0, 0x39, 0xe0, 0x0, 0xe7, 0x80, 0x3,
    0x9e, 0x0, 0xe, 0x78, 0x0, 0x39, 0xe0, 0x0,
    0xe7, 0x80, 0x7, 0x8f, 0x0, 0x2e, 0x3d, 0x1,
    0xf8, 0x7f, 0xbf, 0xe0, 0x2f, 0xd3, 0x80,

    /* U+0076 "v" */
    0x3c, 0x0, 0xf, 0x7, 0x80, 0x7, 0x80, 0xf0,
    0x2, 0xc0, 0x2d, 0x0, 0xe0, 0x3, 0xc0, 0xb4,
    0x0, 0xb0, 0x3c, 0x0, 0x1e, 0x1d, 0x0, 0x3,
    0xcf, 0x0, 0x0, 0x7b, 0x80, 0x0, 0xf, 0xc0,
    0x0, 0x2, 0xf0, 0x0,

    /* U+0077 "w" */
    0xb0, 0x0, 0xf0, 0x0, 0xe7, 0x40, 0x1f, 0x40,
    0x1d, 0x3c, 0x2, 0xf8, 0x3, 0xc2, 0xc0, 0x3a,
    0xc0, 0x38, 0x1e, 0x7, 0x5d, 0x7, 0x0, 0xf0,
    0xf0, 0xf0, 0xf0, 0xb, 0xe, 0xb, 0xe, 0x0,
    0x3a, 0xc0, 0x3a, 0xc0, 0x3, 0xfc, 0x3, 0xfc,
    0x0, 0x1f, 0x40, 0x1f, 0x40, 0x0, 0xf0, 0x0,
    0xf0, 0x0,

    /* U+0078 "x" */
    0x3c, 0x0, 0xf0, 0x78, 0xb, 0x40, 0xb4, 0x78,
    0x0, 0xf7, 0xc0, 0x0, 0xfc, 0x0, 0x2, 0xe0,
    0x0, 0x1f, 0xd0, 0x0, 0xf3, 0xc0, 0xf, 0x43,
    0xc0, 0xb8, 0xb, 0x87, 0xc0, 0xf, 0x40,

    /* U+0079 "y" */
    0x3c, 0x0, 0xf, 0x7, 0x80, 0x7, 0x80, 0xf0,
    0x2, 0xc0, 0x2d, 0x0, 0xe0, 0x3, 0xc0, 0xb4,
    0x0, 0xb4, 0x3c, 0x0, 0xe, 0x1d, 0x0, 0x3,
    0xcf, 0x0, 0x0, 0x7f, 0x80, 0x0, 0xf, 0xc0,
    0x0, 0x2, 0xe0, 0x0, 0x0, 0xb4, 0x0, 0x0,
    0x3c, 0x0, 0xf, 0xbc, 0x0, 0x2, 0xfd, 0x0,
    0x0,

    /* U+007A "z" */
    0x3f, 0xff, 0xe2, 0xaa, 0xbd, 0x0, 0xb, 0x80,
    0x1, 0xf0, 0x0, 0x3c, 0x0, 0xf, 0x40, 0x2,
    0xd0, 0x0, 0x78, 0x0, 0x1f, 0x0, 0x3, 0xfa,
    0xa9, 0x3f, 0xff, 0xe0,

    /* U+007B "{" */
    0x3, 0xe0, 0xfd, 0xf, 0x1, 0xe0, 0x1e, 0x1,
    0xe0, 0x1e, 0x1, 0xe0, 0xbd, 0xf, 0xc0, 0x1e,
    0x1, 0xe0, 0x1e, 0x1, 0xe0, 0x1e, 0x1, 0xe0,
    0xf, 0x0, 0xfd, 0x3, 0xe0,

    /* U+007C "|" */
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0,

    /* U+007D "}" */
    0xbc, 0x7, 0xf0, 0xf, 0x0, 0xb4, 0xb, 0x40,
    0xb4, 0xb, 0x40, 0xb4, 0x7, 0xe0, 0x3f, 0xb,
    0x40, 0xb4, 0xb, 0x40, 0xb4, 0xb, 0x40, 0xb4,
    0xf, 0x7, 0xf0, 0xbc, 0x0,

    /* U+007E "~" */
    0x2f, 0x40, 0xd7, 0xae, 0x1c, 0xa0, 0x7f, 0x80,
    0x0, 0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 86, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 86, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11, .adv_w = 125, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 20, .adv_w = 225, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 69, .adv_w = 199, .box_w = 12, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 129, .adv_w = 270, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 189, .adv_w = 220, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 242, .adv_w = 67, .box_w = 2, .box_h = 6, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 245, .adv_w = 108, .box_w = 6, .box_h = 19, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 274, .adv_w = 108, .box_w = 5, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 298, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 314, .adv_w = 186, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 339, .adv_w = 73, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 345, .adv_w = 123, .box_w = 6, .box_h = 2, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 348, .adv_w = 73, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 351, .adv_w = 113, .box_w = 9, .box_h = 20, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 396, .adv_w = 213, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 442, .adv_w = 118, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 463, .adv_w = 184, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 502, .adv_w = 183, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 541, .adv_w = 214, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 590, .adv_w = 184, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 629, .adv_w = 197, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 671, .adv_w = 191, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 713, .adv_w = 206, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 755, .adv_w = 197, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 797, .adv_w = 73, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 808, .adv_w = 73, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 822, .adv_w = 186, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 847, .adv_w = 186, .box_w = 10, .box_h = 7, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 865, .adv_w = 186, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 890, .adv_w = 183, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 929, .adv_w = 331, .box_w = 20, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1019, .adv_w = 234, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1075, .adv_w = 242, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1121, .adv_w = 231, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1170, .adv_w = 264, .box_w = 14, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1219, .adv_w = 214, .box_w = 11, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1258, .adv_w = 203, .box_w = 10, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1293, .adv_w = 247, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1342, .adv_w = 260, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1388, .adv_w = 99, .box_w = 3, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1399, .adv_w = 164, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1434, .adv_w = 230, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1480, .adv_w = 190, .box_w = 10, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1515, .adv_w = 306, .box_w = 15, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1568, .adv_w = 260, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1614, .adv_w = 269, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1670, .adv_w = 231, .box_w = 12, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1712, .adv_w = 269, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1785, .adv_w = 233, .box_w = 12, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1827, .adv_w = 199, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1869, .adv_w = 188, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1911, .adv_w = 253, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1957, .adv_w = 228, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2013, .adv_w = 360, .box_w = 22, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2090, .adv_w = 215, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2139, .adv_w = 207, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2192, .adv_w = 210, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2238, .adv_w = 107, .box_w = 5, .box_h = 19, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 2262, .adv_w = 113, .box_w = 9, .box_h = 20, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 2307, .adv_w = 107, .box_w = 5, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2331, .adv_w = 187, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2354, .adv_w = 160, .box_w = 10, .box_h = 3, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2362, .adv_w = 192, .box_w = 6, .box_h = 3, .ofs_x = 2, .ofs_y = 12},
    {.bitmap_index = 2367, .adv_w = 191, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2395, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2440, .adv_w = 183, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2471, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2516, .adv_w = 196, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2549, .adv_w = 113, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2579, .adv_w = 221, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2624, .adv_w = 218, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2666, .adv_w = 89, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2681, .adv_w = 91, .box_w = 7, .box_h = 19, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 2715, .adv_w = 197, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2760, .adv_w = 89, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2772, .adv_w = 338, .box_w = 19, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2825, .adv_w = 218, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2856, .adv_w = 203, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2889, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2934, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2979, .adv_w = 131, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2999, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3027, .adv_w = 132, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3055, .adv_w = 217, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3086, .adv_w = 179, .box_w = 13, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3122, .adv_w = 288, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3172, .adv_w = 177, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3203, .adv_w = 179, .box_w = 13, .box_h = 15, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 3252, .adv_w = 167, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3280, .adv_w = 112, .box_w = 6, .box_h = 19, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3309, .adv_w = 96, .box_w = 2, .box_h = 19, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 3319, .adv_w = 112, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3348, .adv_w = 186, .box_w = 10, .box_h = 4, .ofs_x = 1, .ofs_y = 5}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 3, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 14, 0, 9, -7, 0, 0, 0,
    0, -18, -19, 2, 15, 7, 5, -13,
    2, 16, 1, 13, 3, 10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 19, 3, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, 0, 0, 0, 0, -6,
    5, 6, 0, 0, -3, 0, -2, 3,
    0, -3, 0, -3, -2, -6, 0, 0,
    0, 0, -3, 0, 0, -4, -5, 0,
    0, -3, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, -9, 0, -39, 0, 0, -6, 0,
    6, 10, 0, 0, -6, 3, 3, 11,
    6, -5, 6, 0, 0, -18, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, -16, 0, -13, -2, 0, 0, 0,
    0, 1, 12, 0, -10, -3, -1, 1,
    0, -5, 0, 0, -2, -24, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -26, -3, 12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 11, 0, 3, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 3, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 6, 3, 10, -3, 0, 0, 6,
    -3, -11, -44, 2, 9, 6, 1, -4,
    0, 12, 0, 10, 0, 10, 0, -30,
    0, -4, 10, 0, 11, -3, 6, 3,
    0, 0, 1, -3, 0, 0, -5, 26,
    0, 26, 0, 10, 0, 13, 4, 5,
    0, 0, 0, -12, 0, 0, 0, 0,
    1, -2, 0, 2, -6, -4, -6, 2,
    0, -3, 0, 0, 0, -13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -21, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, -18, 0, -20, 0, 0, 0, 0,
    -2, 0, 32, -4, -4, 3, 3, -3,
    0, -4, 3, 0, 0, -17, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -31, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 19, 0, 0, -12, 0, 11, 0,
    -22, -31, -22, -6, 10, 0, 0, -21,
    0, 4, -7, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 8, 10, -39, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 2,
    2, -4, -6, 0, -1, -1, -3, 0,
    0, -2, 0, 0, 0, -6, 0, -3,
    0, -7, -6, 0, -8, -11, -11, -6,
    0, -6, 0, -6, 0, 0, 0, 0,
    -3, 0, 0, 3, 0, 2, -3, 0,
    0, 0, 0, 3, -2, 0, 0, 0,
    -2, 3, 3, -1, 0, 0, 0, -6,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 4, -2, 0, -4, 0, -5, 0,
    0, -2, 0, 10, 0, 0, -3, 0,
    0, 0, 0, 0, -1, 1, -2, -2,
    0, -3, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    -3, -4, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -3, -3, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, -2, -4, 0,
    0, -10, -2, -10, 6, 0, 0, -6,
    3, 6, 9, 0, -8, -1, -4, 0,
    -1, -15, 3, -2, 2, -17, 3, 0,
    0, 1, -17, 0, -17, -3, -28, -2,
    0, -16, 0, 6, 9, 0, 4, 0,
    0, 0, 0, 1, 0, -6, -4, 0,
    0, 0, 0, -3, 0, 0, 0, -3,
    0, 0, 0, 0, 0, -2, -2, 0,
    -2, -4, 0, 0, 0, 0, 0, 0,
    0, -3, -3, 0, -2, -4, -3, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, -2, 0, -6, 3, 0, 0, -4,
    2, 3, 3, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 2,
    0, 0, -3, 0, -3, -2, -4, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    -3, 0, 0, 0, 0, -4, -5, 0,
    0, 10, -2, 1, -10, 0, 0, 9,
    -16, -17, -13, -6, 3, 0, -3, -21,
    -6, 0, -6, 0, -6, 5, -6, -20,
    0, -9, 0, 0, 2, -1, 3, -2,
    0, 3, 0, -10, -12, 0, -16, -8,
    -7, -8, -10, -4, -9, -1, -6, -9,
    0, 1, 0, -3, 0, 0, 0, 2,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, -2,
    0, -1, -3, 0, -5, -7, -7, -1,
    0, -10, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 1, -2, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 15, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, -6, 0, 0, 0,
    0, -16, -10, 0, 0, 0, -5, -16,
    0, 0, -3, 3, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, 0, 0, 0, 4, 0,
    2, -6, -6, 0, -3, -3, -4, 0,
    0, 0, 0, 0, 0, -10, 0, -3,
    0, -5, -3, 0, -7, -8, -10, -3,
    0, -6, 0, -10, 0, 0, 0, 0,
    26, 0, 0, 2, 0, 0, -4, 0,
    0, -14, 0, 0, 0, 0, 0, -30,
    -6, 11, 10, -3, -13, 0, 3, -5,
    0, -16, -2, -4, 3, -22, -3, 4,
    0, 5, -11, -5, -12, -11, -13, 0,
    0, -19, 0, 18, 0, 0, -2, 0,
    0, 0, -2, -2, -3, -9, -11, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -2, -3, -5, 0,
    0, -6, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -6, 0, 0, 6,
    -1, 4, 0, -7, 3, -2, -1, -8,
    -3, 0, -4, -3, -2, 0, -5, -5,
    0, 0, -3, -1, -2, -5, -4, 0,
    0, -3, 0, 3, -2, 0, -7, 0,
    0, 0, -6, 0, -5, 0, -5, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 3, 0, -4, 0, -2, -4, -10,
    -2, -2, -2, -1, -2, -4, -1, 0,
    0, 0, 0, 0, -3, -3, -3, 0,
    0, 0, 0, 4, -2, 0, -2, 0,
    0, 0, -2, -4, -2, -3, -4, -3,
    3, 13, -1, 0, -9, 0, -2, 6,
    0, -3, -13, -4, 5, 0, 0, -15,
    -5, 3, -5, 2, 0, -2, -3, -10,
    0, -5, 2, 0, 0, -5, 0, 0,
    0, 3, 3, -6, -6, 0, -5, -3,
    -5, -3, -3, 0, -5, 2, -6, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, -4, 0, 0, -3, -3, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, -2, 0,
    0, 0, -5, 0, -6, 0, 0, 0,
    -11, 0, 2, -7, 6, 1, -2, -15,
    0, 0, -7, -3, 0, -13, -8, -9,
    0, 0, -14, -3, -13, -12, -15, 0,
    -8, 0, 3, 21, -4, 0, -7, -3,
    -1, -3, -5, -9, -6, -12, -13, -7,
    0, 0, -2, 0, 1, 0, 0, -22,
    -3, 10, 7, -7, -12, 0, 1, -10,
    0, -16, -2, -3, 6, -29, -4, 1,
    0, 0, -21, -4, -17, -3, -23, 0,
    0, -22, 0, 19, 1, 0, -2, 0,
    0, 0, 0, -2, -2, -12, -2, 0,
    0, 0, 0, 0, -10, 0, -3, 0,
    -1, -9, -15, 0, 0, -2, -5, -10,
    -3, 0, -2, 0, 0, 0, 0, -14,
    -3, -11, -10, -3, -5, -8, -3, -5,
    0, -6, -3, -11, -5, 0, -4, -6,
    -3, -6, 0, 2, 0, -2, -11, 0,
    0, -6, 0, 0, 0, 0, 4, 0,
    2, -6, 13, 0, -3, -3, -4, 0,
    0, 0, 0, 0, 0, -10, 0, -3,
    0, -5, -3, 0, -7, -8, -10, -3,
    0, -6, 3, 13, 0, 0, 0, 0,
    26, 0, 0, 2, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -2, -6,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, -3, -3, 0, 0, -6, -3, 0,
    0, -6, 0, 5, -2, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    6, 3, -3, 0, -10, -5, 0, 10,
    -11, -10, -6, -6, 13, 6, 3, -28,
    -2, 6, -3, 0, -3, 4, -3, -11,
    0, -3, 3, -4, -3, -10, -3, 0,
    0, 10, 6, 0, -9, 0, -18, -4,
    9, -4, -12, 1, -4, -11, -11, -3,
    3, 0, -5, 0, -9, 0, 3, 11,
    -7, -12, -13, -8, 10, 0, 1, -23,
    -3, 3, -5, -2, -7, 0, -7, -12,
    -5, -5, -3, 0, 0, -7, -7, -3,
    0, 10, 7, -3, -18, 0, -18, -4,
    0, -11, -19, -1, -10, -5, -11, -9,
    0, 0, -4, 0, -6, -3, 0, -3,
    -6, 0, 5, -11, 3, 0, 0, -17,
    0, -3, -7, -5, -2, -10, -8, -11,
    -7, 0, -10, -3, -7, -6, -10, -3,
    0, 0, 1, 15, -5, 0, -10, -3,
    0, -3, -6, -7, -9, -9, -12, -4,
    6, 0, -5, 0, -16, -4, 2, 6,
    -10, -12, -6, -11, 11, -3, 2, -30,
    -6, 6, -7, -5, -12, 0, -10, -13,
    -4, -3, -3, -3, -7, -10, -1, 0,
    0, 10, 9, -2, -21, 0, -19, -7,
    8, -12, -22, -6, -11, -13, -16, -11,
    0, 0, 0, 0, -4, 0, 0, 3,
    -4, 6, 2, -6, 6, 0, 0, -10,
    -1, 0, -1, 0, 1, 1, -3, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 3, 10, 1, 0, -4, 0,
    0, 0, 0, -2, -2, -4, 0, 0,
    1, 3, 0, 0, 0, 0, 3, 0,
    -3, 0, 12, 0, 6, 1, 1, -4,
    0, 6, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 10, 0, 9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 0, -3, 5, 0, 10, 0,
    0, 32, 4, -6, -6, 3, 3, -2,
    1, -16, 0, 0, 15, -19, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -22, 12, 45, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, 0, -6, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -9, 0, 0, 1, 0,
    0, 3, 41, -6, -3, 10, 9, -9,
    3, 0, 0, 3, 3, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -42, 9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, -9,
    0, 0, 0, 0, -7, -2, 0, 0,
    0, -7, 0, -4, 0, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -21, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, -5, 0, -9, 0, 0, 0, -5,
    3, -4, 0, 0, -9, -3, -7, 0,
    0, -9, 0, -3, 0, -15, 0, -4,
    0, 0, -26, -6, -13, -4, -12, 0,
    0, -21, 0, -9, -2, 0, 0, 0,
    0, 0, 0, 0, 0, -5, -6, -3,
    0, 0, 0, 0, -7, 0, -7, 4,
    -4, 6, 0, -2, -7, -2, -5, -6,
    0, -4, -2, -2, 2, -9, -1, 0,
    0, 0, -28, -3, -4, 0, -7, 0,
    -2, -15, -3, 0, 0, -2, -3, 0,
    0, 0, 0, 2, 0, -2, -5, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 4, 0, 0, 0, 0,
    0, -7, 0, -2, 0, 0, 0, -6,
    3, 0, 0, 0, -9, -3, -6, 0,
    0, -9, 0, -3, 0, -15, 0, 0,
    0, 0, -31, 0, -6, -12, -16, 0,
    0, -21, 0, -2, -5, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -5, -2,
    1, 0, 0, 5, -4, 0, 10, 16,
    -3, -3, -10, 4, 16, 5, 7, -9,
    4, 13, 4, 9, 7, 9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 20, 15, -6, -3, 0, -3, 26,
    14, 26, 0, 0, 0, 3, 0, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -27, -4, -3, -13, -16, 0,
    0, -21, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -27, -4, -3, -13, -16, 0,
    0, -13, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -7, 3, 0, -3, 3, 6, 3, -10,
    0, -1, -3, 3, 0, 3, 0, 0,
    0, 0, -8, 0, -3, -2, -6, 0,
    -3, -13, 0, 20, -3, 0, -7, -2,
    0, -2, -5, 0, -3, -9, -6, -4,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -27, -4, -3, -13, -16, 0,
    0, -21, 0, 0, 0, 0, 0, 0,
    16, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, -10, -4, -3, 10,
    -3, -3, -13, 1, -2, 1, -2, -9,
    1, 7, 1, 3, 1, 3, -8, -13,
    -4, 0, -12, -6, -9, -13, -12, 0,
    -5, -6, -4, -4, -3, -2, -4, -2,
    0, -2, -1, 5, 0, 5, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -3, -3, 0,
    0, -9, 0, -2, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, 0, 0, 0, -3, 0, 0, -5,
    -3, 3, 0, -5, -6, -2, 0, -9,
    -2, -7, -2, -4, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -21, 0, 10, 0, 0, -6, 0,
    0, 0, 0, -4, 0, -3, 0, 0,
    0, 0, -2, 0, -7, 0, 0, 13,
    -4, -11, -10, 2, 4, 4, -1, -9,
    2, 5, 2, 10, 2, 11, -2, -9,
    0, 0, -13, 0, 0, -10, -9, 0,
    0, -6, 0, -4, -5, 0, -5, 0,
    -5, 0, -2, 5, 0, -3, -10, -3,
    0, 0, -3, 0, -6, 0, 0, 4,
    -7, 0, 3, -3, 3, 0, 0, -11,
    0, -2, -1, 0, -3, 4, -3, 0,
    0, 0, -13, -4, -7, 0, -10, 0,
    0, -15, 0, 12, -3, 0, -6, 0,
    2, 0, -3, 0, -3, -10, 0, -3,
    0, 0, 0, 0, -2, 0, 0, 3,
    -4, 1, 0, 0, -4, -2, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -20, 0, 7, 0, 0, -3, 0,
    0, 0, 0, 1, 0, -3, -3, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 48,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/

static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 2,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t test_font_montserrat_ascii_2bpp = {
#else
lv_font_t test_font_montserrat_ascii_2bpp = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 22,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if TEST_FONT_MONTSERRAT_ASCII_2BPP*/

#endif /*LV_BUILD_TEST*/
