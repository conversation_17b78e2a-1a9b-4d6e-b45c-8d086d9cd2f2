/*******************************************************************************
 * Size: 20 px
 * Bpp: 4
 * Opts:
 ******************************************************************************/

#include "../../../lvgl.h"
#if LV_BUILD_TEST

#ifndef TEST_FONT_MONTSERRAT_ASCII_4BPP_COMPRESSED
    #define TEST_FONT_MONTSERRAT_ASCII_4BPP_COMPRESSED 1
#endif

#if TEST_FONT_MONTSERRAT_ASCII_4BPP_COMPRESSED

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x6f, 0xc0, 0x8, 0xc1, 0xc4, 0x4, 0x2, 0x70,
    0x30, 0x8, 0x40, 0x40, 0x1e, 0x64, 0x22, 0xd6,
    0x19, 0x48, 0x6a, 0xd0, 0x70,

    /* U+0022 "\"" */
    0xbe, 0x1, 0xf8, 0x0, 0xf0, 0x98, 0x5, 0xe0,
    0x10, 0x80, 0x7e, 0xfa, 0x0, 0x3a, 0x0,

    /* U+0023 "#" */
    0x0, 0xcf, 0xe0, 0x12, 0x79, 0x0, 0x7b, 0x4c,
    0x2, 0x30, 0x20, 0xf, 0x33, 0x0, 0x2e, 0x20,
    0xd, 0xbf, 0xe3, 0x5f, 0xf9, 0x8b, 0xfd, 0x1,
    0x6e, 0xe0, 0x47, 0x78, 0x4d, 0xdd, 0x80, 0xd1,
    0x41, 0xf1, 0x10, 0xdc, 0x41, 0x0, 0x21, 0x6,
    0x0, 0xc4, 0x1, 0xe6, 0x3, 0x0, 0x10, 0x8,
    0x7, 0x88, 0x4, 0x0, 0xc0, 0xc0, 0x13, 0xff,
    0xa0, 0x3f, 0xeb, 0xd, 0xfe, 0x4, 0x77, 0x11,
    0x1d, 0xe3, 0x7, 0x74, 0x81, 0xc4, 0x1, 0x22,
    0x26, 0x38, 0x83, 0x80, 0x62, 0xe0, 0x9, 0x98,
    0x1, 0xe1, 0x2, 0x0, 0x88, 0x80, 0x18,

    /* U+0024 "$" */
    0x0, 0xe5, 0xf0, 0xf, 0xfe, 0xa3, 0xe5, 0x7,
    0xe4, 0x8, 0x0, 0x76, 0xc, 0x40, 0x8d, 0xf0,
    0x1, 0x24, 0x7d, 0x61, 0xbf, 0x36, 0x0, 0x40,
    0xf1, 0x0, 0xcc, 0xc0, 0xc, 0xc0, 0x1f, 0xd4,
    0x16, 0xe6, 0x1, 0xf3, 0x38, 0xc4, 0x84, 0xa0,
    0x7, 0x46, 0xc1, 0x83, 0x5f, 0x20, 0x6, 0x27,
    0x90, 0xb5, 0x1b, 0x20, 0xf, 0x92, 0x98, 0x14,
    0x4, 0x3, 0xef, 0xf, 0xd, 0x92, 0x0, 0xc5,
    0x61, 0x80, 0x6, 0xde, 0x80, 0xdd, 0x12, 0xa0,
    0x65, 0xa8, 0x10, 0x11, 0xcd, 0x0, 0x49, 0x5d,
    0x41, 0xf8, 0xc0, 0x1f, 0xfc, 0x77, 0x80, 0xe,

    /* U+0025 "%" */
    0x0, 0x4f, 0x72, 0x40, 0x3d, 0xb4, 0x1, 0xa9,
    0x2a, 0x89, 0x40, 0x1a, 0x4e, 0xc0, 0x21, 0x59,
    0x44, 0x4a, 0x88, 0x1, 0x1a, 0x84, 0x2, 0x20,
    0x50, 0x2, 0x81, 0x0, 0xd3, 0x20, 0x6, 0x10,
    0x20, 0x1, 0x1, 0x5, 0x9c, 0x80, 0x71, 0x1c,
    0x8, 0xa1, 0x5, 0x55, 0xc0, 0x1f, 0x6b, 0xf7,
    0x1e, 0x86, 0xdc, 0xab, 0xf9, 0xc0, 0x21, 0xce,
    0xe6, 0xd, 0x9c, 0x5b, 0xdd, 0xa1, 0x80, 0x30,
    0x88, 0x15, 0x5c, 0x4b, 0xc8, 0xb1, 0x0, 0xf,
    0xd, 0x41, 0xb0, 0x18, 0x0, 0xc4, 0x3, 0xd4,
    0x6e, 0x1, 0xe7, 0x0, 0xf3, 0x27, 0x0, 0x18,
    0x8, 0x0, 0xa2, 0x1, 0x8a, 0x60, 0x80, 0x4,
    0x92, 0x47, 0x10, 0x0, 0xdc, 0x6e, 0x1, 0xa8,
    0x77, 0x4c, 0xc0,

    /* U+0026 "&" */
    0x0, 0x86, 0x77, 0xf5, 0xc0, 0x3f, 0x63, 0x24,
    0x34, 0x38, 0x7, 0x90, 0x9a, 0x5e, 0xca, 0xc0,
    0x3c, 0x21, 0xc0, 0x11, 0x80, 0x7c, 0x81, 0x22,
    0x2b, 0x18, 0x0, 0xf0, 0xd0, 0xe7, 0xa5, 0x20,
    0x7, 0xc2, 0x22, 0x1c, 0x50, 0xf, 0xa2, 0xca,
    0x3, 0x88, 0x1, 0x6, 0x0, 0x77, 0x36, 0x3d,
    0x16, 0x10, 0xbd, 0x80, 0x24, 0x64, 0x0, 0xb4,
    0x39, 0x61, 0x60, 0x1, 0x30, 0xc, 0xb6, 0x2a,
    0x66, 0x0, 0x11, 0x1c, 0x3, 0x8, 0x80, 0xcc,
    0x0, 0x90, 0x8e, 0xbb, 0x75, 0x4, 0x86, 0x10,
    0x26, 0xb1, 0xa2, 0xa, 0x39, 0xb1, 0x8, 0x0,
    0x53, 0xbf, 0xec, 0x70, 0x1, 0xd8, 0x0,

    /* U+0027 "'" */
    0xbe, 0x0, 0x9, 0x80, 0x6f, 0xa0,

    /* U+0028 "(" */
    0x0, 0xbb, 0x40, 0x27, 0x1b, 0x0, 0xa4, 0x18,
    0x0, 0x62, 0xa0, 0x15, 0x87, 0x80, 0x46, 0xe,
    0x1, 0x28, 0x10, 0x4, 0x22, 0x0, 0xe3, 0x0,
    0x84, 0x4, 0x2, 0x10, 0x10, 0xe, 0x30, 0xc,
    0x22, 0x0, 0xca, 0x4, 0x1, 0x18, 0x38, 0x5,
    0x61, 0xe0, 0x11, 0x8a, 0x0, 0x69, 0x16, 0x0,
    0x9c, 0x6c, 0x0,

    /* U+0029 ")" */
    0x2f, 0xb0, 0x1, 0x2a, 0x18, 0x2, 0x42, 0x40,
    0x6, 0x2a, 0x1, 0x38, 0x20, 0x3, 0xc0, 0xc0,
    0xa, 0x1a, 0x0, 0x10, 0x10, 0x1, 0x83, 0x80,
    0x4, 0x4, 0x0, 0x20, 0x20, 0x3, 0x7, 0x0,
    0x8, 0x8, 0x1, 0x43, 0x40, 0x1e, 0x6, 0x0,
    0x70, 0x40, 0x31, 0x50, 0x4, 0x84, 0x81, 0x2a,
    0x18, 0x0,

    /* U+002A "*" */
    0x0, 0xa6, 0x40, 0x11, 0x30, 0x6, 0x62, 0x79,
    0xc2, 0x26, 0x4b, 0xae, 0xba, 0x21, 0xf5, 0x41,
    0x54, 0x0, 0x55, 0x3, 0x44, 0x19, 0x91, 0x6,
    0x67, 0x30, 0x1, 0xb9, 0x80, 0x43, 0xfc, 0x2,
    0x0,

    /* U+002B "+" */
    0x0, 0xff, 0xe1, 0x7d, 0x0, 0x7f, 0xf5, 0xab,
    0xfe, 0x5, 0xff, 0x93, 0x15, 0x60, 0x25, 0x59,
    0x99, 0x55, 0x3, 0xd5, 0x44, 0x1, 0xff, 0xc8,

    /* U+002C "," */
    0x6, 0xa1, 0x9, 0x59, 0x5, 0xf, 0xf, 0x5,
    0xc, 0x32, 0x4, 0xb0,

    /* U+002D "-" */
    0x9b, 0xbc, 0xa8, 0x9d, 0xa0,

    /* U+002E "." */
    0x7, 0xb2, 0x8, 0x4a, 0x5, 0x1c,

    /* U+002F "/" */
    0x0, 0xf9, 0xd4, 0x3, 0xc9, 0x4, 0x1, 0xee,
    0x6, 0x0, 0xf2, 0x98, 0x80, 0x72, 0x5, 0x0,
    0x7b, 0x81, 0xc0, 0x3c, 0xa6, 0x20, 0x1c, 0xa1,
    0x40, 0x1e, 0xf0, 0x70, 0xf, 0x2a, 0x8, 0x7,
    0x28, 0x68, 0x7, 0xbc, 0x1c, 0x3, 0xca, 0x82,
    0x1, 0xca, 0x1a, 0x1, 0xee, 0x7, 0x0, 0xe1,
    0x44, 0x8, 0x7, 0x38, 0x70, 0x7, 0xb4, 0x14,
    0x3, 0x85, 0x10, 0x1, 0xe7, 0xe, 0x0, 0xf0,

    /* U+0030 "0" */
    0x0, 0x86, 0x37, 0xfa, 0x8c, 0x3, 0x8b, 0xdc,
    0x88, 0xb, 0x8c, 0x1, 0xbc, 0x57, 0xb7, 0xa4,
    0x24, 0x80, 0xc, 0x47, 0x42, 0x0, 0x68, 0x9,
    0x0, 0x68, 0x40, 0x7, 0x38, 0x20, 0x1, 0x41,
    0x40, 0x3c, 0x60, 0x60, 0x20, 0x20, 0x1e, 0x60,
    0x60, 0xf, 0xfe, 0x10, 0x80, 0x80, 0x79, 0x81,
    0x81, 0x41, 0x40, 0x3c, 0x60, 0x61, 0xa1, 0x0,
    0x1c, 0xe0, 0x80, 0x6, 0x23, 0xa1, 0x0, 0x34,
    0x4, 0x80, 0x5e, 0x2b, 0xdb, 0xf2, 0x12, 0x40,
    0x11, 0x7b, 0x91, 0x5, 0x71, 0x80, 0x0,

    /* U+0031 "1" */
    0xdf, 0xfc, 0x8e, 0x66, 0x10, 0x5, 0x66, 0x14,
    0x3, 0xff, 0xf0,

    /* U+0032 "2" */
    0x0, 0x36, 0x77, 0xf5, 0x18, 0x0, 0xb6, 0x4c,
    0x8, 0x57, 0x14, 0x1c, 0x9b, 0xb9, 0xbe, 0xc1,
    0x60, 0xbd, 0x22, 0x1, 0x48, 0x88, 0x80, 0x40,
    0x3c, 0x60, 0x1f, 0xc4, 0x58, 0x3, 0xc3, 0xe1,
    0x0, 0x1e, 0x1d, 0x27, 0x50, 0xe, 0x1c, 0x37,
    0x80, 0xe, 0x1c, 0x27, 0x80, 0xe, 0x2f, 0x28,
    0x80, 0x7, 0x16, 0xc, 0xb8, 0x7, 0x16, 0x8,
    0x56, 0x67, 0x4b, 0x8, 0x0, 0xcf, 0xca,

    /* U+0033 "3" */
    0x4f, 0xff, 0xe0, 0x73, 0x3f, 0x8, 0x10, 0x1e,
    0x67, 0x70, 0x8b, 0xc0, 0x3c, 0x3e, 0x14, 0x40,
    0x1e, 0xd2, 0x94, 0x0, 0xf4, 0x9a, 0xb0, 0x7,
    0xcc, 0xb, 0xd0, 0x1, 0xe9, 0x93, 0x83, 0xe0,
    0x7, 0x33, 0x23, 0x88, 0xd4, 0x3, 0xf2, 0x86,
    0x80, 0x80, 0x78, 0x83, 0xdf, 0xd8, 0x40, 0x26,
    0x50, 0x50, 0x19, 0xee, 0xbe, 0x42, 0x49, 0xf5,
    0xcc, 0x46, 0x5c, 0x60,

    /* U+0034 "4" */
    0x0, 0xf9, 0xfe, 0xc0, 0x3f, 0xc7, 0x5, 0x40,
    0x1f, 0xc3, 0xa1, 0xc2, 0x1, 0xfd, 0xa3, 0x26,
    0x1, 0xfd, 0x6, 0xac, 0x1, 0xfc, 0x8e, 0x54,
    0x0, 0x10, 0xf, 0x15, 0x7, 0x80, 0x1b, 0x9c,
    0x3, 0xb8, 0x68, 0x80, 0x3f, 0xa4, 0xd9, 0x40,
    0x3f, 0x8d, 0x81, 0xbf, 0xf4, 0x84, 0x7f, 0x88,
    0x4c, 0xfe, 0x20, 0x13, 0x38, 0xb3, 0x3e, 0xa0,
    0x9c, 0xc0, 0x80, 0x7f, 0xf4, 0x0,

    /* U+0035 "5" */
    0x0, 0x7f, 0xfe, 0x0, 0x8, 0x0, 0xcf, 0xc0,
    0x3, 0x4, 0xcc, 0xf0, 0x1, 0x80, 0x80, 0x3f,
    0x10, 0x70, 0x7, 0xe1, 0x9, 0xcc, 0x53, 0x90,
    0x5, 0xc0, 0x26, 0x65, 0x8d, 0x70, 0x4, 0xff,
    0xed, 0x70, 0x84, 0x0, 0xf1, 0x43, 0x87, 0x0,
    0x7e, 0xc0, 0x60, 0x20, 0xf, 0x10, 0x31, 0xec,
    0x10, 0x4, 0xb2, 0x1c, 0xe0, 0xfb, 0xdb, 0xf4,
    0xc, 0x88, 0xe8, 0x30, 0x21, 0x4a, 0x90,

    /* U+0036 "6" */
    0x0, 0xcb, 0x7d, 0xfd, 0x6c, 0x1, 0xae, 0x90,
    0xc, 0xc9, 0xa0, 0x15, 0x22, 0x37, 0xb3, 0x7a,
    0x80, 0x8, 0xa7, 0x64, 0x1, 0x8, 0x80, 0x1c,
    0x10, 0x1, 0xfc, 0x80, 0x8d, 0x9f, 0xec, 0x60,
    0x8, 0x42, 0xa4, 0x50, 0xcd, 0x34, 0x1, 0x96,
    0x76, 0xf3, 0x45, 0x54, 0x1, 0x2b, 0x0, 0x45,
    0x61, 0xe0, 0x61, 0xa0, 0x1c, 0xc0, 0xc1, 0x61,
    0xa0, 0x1c, 0xc0, 0xc0, 0xc2, 0xac, 0x1, 0x15,
    0x87, 0x0, 0xd0, 0x4f, 0x5e, 0x68, 0xba, 0x0,
    0x13, 0x98, 0xd0, 0x92, 0xe0, 0x0,

    /* U+0037 "7" */
    0x6f, 0xff, 0xf1, 0x0, 0x4, 0xcf, 0x88, 0x0,
    0x60, 0x6, 0xcc, 0xeb, 0x5, 0x10, 0xf, 0xd6,
    0x12, 0xf, 0x74, 0x1, 0x90, 0x8c, 0xc0, 0x28,
    0x40, 0x1a, 0x2, 0x40, 0x3f, 0x19, 0x89, 0x40,
    0x3f, 0x48, 0x58, 0x7, 0xe2, 0x51, 0x60, 0xf,
    0xd6, 0xc, 0x1, 0xf8, 0x58, 0x24, 0x3, 0xf3,
    0x3, 0x8, 0x7, 0xe9, 0xb, 0x0, 0xfc, 0xc2,
    0x84, 0x1, 0xc0,

    /* U+0038 "8" */
    0x0, 0x9b, 0x3b, 0xfa, 0xd4, 0x3, 0x64, 0x99,
    0x29, 0x25, 0x58, 0x1, 0x8c, 0xdd, 0x95, 0x9a,
    0x48, 0x80, 0xf0, 0xa1, 0x0, 0x89, 0x40, 0xc3,
    0xc3, 0x40, 0x30, 0xa8, 0x18, 0x32, 0xa7, 0x54,
    0xd7, 0x13, 0xa0, 0x3, 0xc4, 0x4a, 0xca, 0x3,
    0x80, 0x6, 0xa0, 0x9e, 0xfd, 0x81, 0xb4, 0x8,
    0xd, 0x61, 0x2, 0x7a, 0x8, 0x11, 0x8c, 0x3,
    0x94, 0xc, 0x40, 0x44, 0x1, 0xc6, 0x2, 0xa,
    0x14, 0x20, 0x11, 0x70, 0x30, 0x59, 0x27, 0x65,
    0xe6, 0x91, 0xc0, 0xe, 0xc1, 0x92, 0x12, 0x4e,
    0x0,

    /* U+0039 "9" */
    0x0, 0x1d, 0x77, 0xf5, 0xa0, 0x6, 0x6c, 0x53,
    0x52, 0x4b, 0x80, 0x0, 0xc8, 0x5e, 0xd6, 0x71,
    0xba, 0x82, 0x3, 0x20, 0x4, 0x3c, 0x10, 0x6,
    0x1, 0xf0, 0x81, 0xa, 0x3, 0x20, 0x4, 0x3e,
    0x0, 0x52, 0x70, 0xbd, 0xab, 0xe2, 0x30, 0x10,
    0x8b, 0x43, 0x56, 0x67, 0xb0, 0x8, 0x1, 0x2f,
    0xbf, 0x64, 0x5c, 0x1c, 0x3, 0xf0, 0x90, 0x18,
    0x7, 0xeb, 0x6, 0x0, 0x90, 0x2, 0x1a, 0x51,
    0x80, 0x3, 0x5f, 0x6e, 0xb9, 0x47, 0x44, 0x0,
    0xaa, 0x32, 0x33, 0x47, 0x98, 0x0,

    /* U+003A ":" */
    0xa, 0xe4, 0x5, 0x1c, 0x8, 0x4a, 0x7, 0xb2,
    0x0, 0xff, 0xe2, 0xbd, 0x90, 0x42, 0x50, 0x28,
    0xe0,

    /* U+003B ";" */
    0xa, 0xe4, 0x5, 0x1c, 0x8, 0x4a, 0x7, 0xb2,
    0x0, 0xff, 0xe2, 0xb5, 0x8, 0x4a, 0xc8, 0x28,
    0x78, 0x78, 0x28, 0x61, 0x90, 0x25, 0x80,

    /* U+003C "<" */
    0x0, 0xfc, 0xc6, 0x1, 0xc5, 0x3f, 0x2e, 0x1,
    0x2e, 0x6b, 0x25, 0xa1, 0x47, 0x51, 0x3f, 0x5a,
    0x4, 0x39, 0x5f, 0x40, 0x80, 0x44, 0x7, 0xc8,
    0x1, 0xd1, 0xac, 0x97, 0xae, 0x20, 0x11, 0x4f,
    0x48, 0xc7, 0x59, 0x0, 0x66, 0xcc, 0x32, 0x30,
    0x7, 0x8e, 0x7d, 0x40,

    /* U+003D "=" */
    0xaf, 0xff, 0xc9, 0x8a, 0xbf, 0x99, 0x95, 0x5f,
    0x88, 0x3, 0xff, 0x91, 0x5f, 0xff, 0x93, 0x15,
    0x7f, 0x30,

    /* U+003E ">" */
    0x63, 0x0, 0xfd, 0x98, 0xd6, 0x10, 0xe, 0xb8,
    0x9, 0xe9, 0x30, 0x8, 0x5f, 0x71, 0x5b, 0x30,
    0xc0, 0x18, 0xeb, 0xa4, 0xa5, 0x0, 0x39, 0xe8,
    0x41, 0xc0, 0x7, 0x3f, 0x23, 0x3e, 0x6b, 0xb8,
    0xcb, 0x9a, 0xc0, 0xf, 0x8, 0xea, 0x30, 0xd,
    0x1a, 0xe2, 0x1, 0xe0,

    /* U+003F "?" */
    0x0, 0x3e, 0x77, 0xed, 0x18, 0x0, 0xba, 0xc,
    0x8c, 0xcb, 0x8c, 0x8, 0x2f, 0xf9, 0x8e, 0x70,
    0x80, 0x6e, 0x80, 0xd, 0x0, 0x22, 0x1, 0x0,
    0xe1, 0x1, 0x10, 0x7, 0xd4, 0x10, 0x1, 0xe1,
    0xc4, 0x76, 0x0, 0xf6, 0x9b, 0xc0, 0x7, 0x90,
    0xcd, 0x0, 0x1f, 0x3c, 0x40, 0x3, 0xf1, 0xba,
    0x0, 0x7e, 0x29, 0x30, 0xf, 0xd0, 0xde, 0x1,
    0xfb, 0x1, 0x0, 0x30,

    /* U+0040 "@" */
    0x0, 0xf2, 0x4e, 0xff, 0xbb, 0x24, 0xc0, 0x3f,
    0x8b, 0x6d, 0x11, 0x13, 0x25, 0x7c, 0xb1, 0x0,
    0xf2, 0xe8, 0x66, 0xcb, 0xb3, 0xcf, 0x5b, 0x79,
    0x80, 0x63, 0xa5, 0xd3, 0x5, 0x88, 0x28, 0x1e,
    0xca, 0xe8, 0x80, 0x5a, 0x74, 0x5, 0xb4, 0xee,
    0xad, 0x46, 0x1a, 0x3a, 0x0, 0x30, 0xf8, 0xe,
    0x14, 0x77, 0xd8, 0x40, 0x0, 0x61, 0x8, 0x34,
    0x8c, 0x24, 0x61, 0xc4, 0x13, 0x4, 0x2, 0x51,
    0x50, 0x54, 0x0, 0x20, 0x38, 0x6, 0x16, 0x0,
    0xc5, 0xa0, 0x3c, 0x0, 0x21, 0x0, 0xf7, 0x0,
    0x66, 0x10, 0xe, 0x31, 0x0, 0xff, 0x84, 0x40,
    0x3c, 0x0, 0x70, 0x30, 0xe, 0xd0, 0xc, 0xc4,
    0xa, 0x80, 0x9, 0xe, 0x10, 0x9, 0xd0, 0x8,
    0xc, 0xb0, 0x34, 0x8c, 0xe, 0xb, 0xaa, 0x76,
    0x20, 0x13, 0x5a, 0x68, 0xc, 0x3e, 0x0, 0x7b,
    0x35, 0x66, 0x5b, 0xd0, 0x2a, 0x68, 0x5, 0xa7,
    0x40, 0x4, 0xcf, 0xf5, 0xa0, 0x2f, 0x7d, 0x8,
    0x4, 0x74, 0xba, 0x60, 0x1f, 0xfc, 0x45, 0xc0,
    0xcd, 0x97, 0x68, 0xaf, 0x70, 0xf, 0xe3, 0xda,
    0x74, 0x89, 0x51, 0x86, 0x0, 0xe0,

    /* U+0041 "A" */
    0x0, 0xfb, 0xbe, 0x0, 0x3f, 0xf8, 0xa, 0x20,
    0xc0, 0x1f, 0xfc, 0x8, 0x5, 0x16, 0x0, 0xff,
    0x21, 0x2d, 0x85, 0x80, 0x7f, 0xbc, 0x24, 0x54,
    0x94, 0x3, 0xf1, 0x21, 0x98, 0x24, 0x24, 0x3,
    0xf4, 0x4, 0x80, 0xc, 0xf0, 0x7, 0x85, 0x45,
    0x40, 0x29, 0x8, 0x0, 0xf4, 0x84, 0x80, 0x65,
    0x14, 0x20, 0xe, 0x60, 0x8f, 0xfe, 0x10, 0xb0,
    0xc, 0xc2, 0x4a, 0xbf, 0x3, 0x8, 0x5, 0x61,
    0xd5, 0x5f, 0x30, 0x48, 0x1, 0x49, 0xc, 0x3,
    0xeb, 0x6, 0x0, 0x48, 0x78, 0x7, 0xe2, 0x51,
    0x60,

    /* U+0042 "B" */
    0xef, 0xff, 0x75, 0xa8, 0x7, 0x22, 0xac, 0x86,
    0x95, 0x40, 0xd, 0x55, 0xaf, 0x6c, 0x14, 0xc0,
    0x3f, 0x91, 0x41, 0x40, 0x3f, 0xcc, 0x4, 0x1,
    0xf8, 0xa4, 0xcc, 0x80, 0x17, 0x7f, 0xed, 0x62,
    0xd0, 0xc, 0x8a, 0xb8, 0xc4, 0xb0, 0x40, 0x2a,
    0xae, 0xce, 0x72, 0xa0, 0xf, 0xf4, 0x2, 0x0,
    0x7f, 0xf0, 0x44, 0x3, 0xfa, 0x0, 0x44, 0x0,
    0xaa, 0xeb, 0xe7, 0xa, 0x0, 0x91, 0x57, 0x21,
    0xb6, 0xa0, 0x0,

    /* U+0043 "C" */
    0x0, 0xc2, 0xf9, 0xdf, 0xd6, 0xa0, 0x1c, 0xbf,
    0x6, 0x24, 0x9, 0x58, 0x20, 0x5, 0xa1, 0x6d,
    0xfd, 0xec, 0x43, 0x40, 0x2a, 0x9, 0x91, 0x0,
    0x47, 0x78, 0xa1, 0x40, 0xcc, 0x0, 0xf8, 0xc0,
    0xa, 0x14, 0x1, 0xff, 0x10, 0x18, 0x7, 0xff,
    0x34, 0x80, 0xc0, 0x3f, 0xe5, 0xa, 0x0, 0xff,
    0xa8, 0x19, 0x80, 0x1f, 0x18, 0x0, 0xa8, 0x26,
    0x8c, 0x2, 0x3b, 0xc5, 0x0, 0x2d, 0xa, 0xe7,
    0xef, 0xe2, 0x1a, 0x0, 0x4b, 0xf0, 0x62, 0x42,
    0x95, 0x82,

    /* U+0044 "D" */
    0xef, 0xff, 0x6d, 0x20, 0x7, 0x88, 0xce, 0x22,
    0x2d, 0xd8, 0x40, 0x36, 0x66, 0xdf, 0xa2, 0x4c,
    0x0, 0xff, 0x2e, 0x99, 0x50, 0x7, 0xfd, 0xa0,
    0xa2, 0x1, 0xfe, 0x15, 0x7, 0x0, 0xff, 0x88,
    0x38, 0x3, 0xfe, 0xf0, 0xf, 0xfe, 0xe, 0x87,
    0x0, 0x7f, 0x85, 0x41, 0xc0, 0x3f, 0xd4, 0xa,
    0x20, 0x1f, 0x97, 0x50, 0xac, 0x2, 0xdc, 0xcb,
    0x7e, 0x89, 0x30, 0x40, 0x23, 0x3c, 0x44, 0x5b,
    0xb0, 0x80, 0x0,

    /* U+0045 "E" */
    0xef, 0xff, 0xe0, 0x8, 0x8c, 0xfe, 0x0, 0xb3,
    0x3f, 0x0, 0x7f, 0xf6, 0x3b, 0xff, 0xc4, 0x1,
    0x19, 0xfe, 0x0, 0xb7, 0x33, 0xc2, 0x1, 0xff,
    0xd8, 0xdc, 0xcf, 0x8c, 0x0, 0x67, 0xf9, 0xc0,

    /* U+0046 "F" */
    0xef, 0xff, 0xe0, 0x1, 0x19, 0xfc, 0x0, 0xcc,
    0xfc, 0x1, 0xff, 0xd4, 0xdc, 0xcf, 0x8, 0x0,
    0xcf, 0xf0, 0x3, 0xbf, 0xfc, 0x40, 0x1f, 0xfe,
    0x60,

    /* U+0047 "G" */
    0x0, 0xc2, 0xd9, 0xdf, 0xd6, 0xc0, 0x1c, 0xbf,
    0x26, 0x24, 0x9, 0x3a, 0x40, 0x5, 0xa1, 0x6d,
    0xfd, 0xed, 0x62, 0x50, 0x2a, 0x9, 0x91, 0x0,
    0x45, 0x3a, 0xe1, 0x40, 0xcc, 0x0, 0xf8, 0x80,
    0xa, 0x14, 0x1, 0xff, 0x10, 0x18, 0x7, 0xe1,
    0x10, 0x7, 0xfc, 0x7d, 0x40, 0x40, 0x60, 0x1f,
    0xf2, 0x84, 0x80, 0x7f, 0xd2, 0xa, 0xc0, 0x1f,
    0xe1, 0xa0, 0x9a, 0x30, 0x8, 0xa4, 0x3, 0x2d,
    0x8a, 0xe7, 0xef, 0xea, 0x8f, 0x80, 0x49, 0xf0,
    0x62, 0x42, 0x93, 0xc8,

    /* U+0048 "H" */
    0xef, 0x10, 0xf, 0xb7, 0xc8, 0x3, 0xff, 0xef,
    0xdf, 0xff, 0x10, 0x6, 0x23, 0x3f, 0x80, 0x3b,
    0x33, 0xf1, 0x0, 0x7f, 0xfd, 0xc0,

    /* U+0049 "I" */
    0xef, 0x10, 0xf, 0xfe, 0xd8,

    /* U+004A "J" */
    0x0, 0x67, 0xff, 0x90, 0x0, 0xa6, 0x78, 0x3,
    0xa7, 0x33, 0x20, 0x7, 0xff, 0xf4, 0x40, 0x39,
    0x81, 0x83, 0x70, 0x80, 0xc, 0x42, 0x40, 0xc7,
    0xba, 0xe9, 0x8, 0x0, 0x54, 0x99, 0x9, 0xd3,
    0x0,

    /* U+004B "K" */
    0xef, 0x10, 0xf, 0x5f, 0xc0, 0x7, 0xf5, 0xa3,
    0xc0, 0x7, 0xeb, 0x46, 0x80, 0xf, 0xd6, 0x8b,
    0x20, 0x1f, 0xa9, 0x11, 0x40, 0x1f, 0xa9, 0x52,
    0xc0, 0x3f, 0x4a, 0x84, 0x0, 0x7e, 0xb6, 0x10,
    0xd1, 0x0, 0xf9, 0x4b, 0xd4, 0x74, 0x3, 0xe2,
    0xc1, 0xa3, 0x35, 0x0, 0x7b, 0xc4, 0x1, 0xa2,
    0xae, 0x1, 0xc6, 0x1, 0xe, 0x4, 0x20, 0x7,
    0xf1, 0x58, 0x59, 0x0, 0x7f, 0x24, 0x87, 0x88,

    /* U+004C "L" */
    0xef, 0x10, 0xf, 0xff, 0xf8, 0x7, 0xff, 0x83,
    0x73, 0x3d, 0x0, 0x3, 0x3f, 0xc0,

    /* U+004D "M" */
    0xef, 0x10, 0xf, 0xf7, 0x78, 0x2, 0x0, 0x3f,
    0xa0, 0x40, 0x26, 0x30, 0xf, 0x89, 0xc0, 0x3b,
    0xc0, 0x3e, 0x90, 0xe, 0x63, 0x60, 0xe, 0x44,
    0x28, 0x6, 0x81, 0x81, 0x0, 0xd2, 0x16, 0x1,
    0x86, 0x6, 0x0, 0x27, 0x28, 0x11, 0x0, 0x66,
    0x36, 0x30, 0x18, 0x27, 0x0, 0xfb, 0xc3, 0xc2,
    0xc2, 0x40, 0x3f, 0x1b, 0x1c, 0xaa, 0x90, 0x3,
    0xfa, 0x5, 0x82, 0xc0, 0x3f, 0xc3, 0x1, 0x2,
    0x1, 0xff, 0x35, 0xb8, 0x7, 0xff, 0x5, 0x0,
    0x3e,

    /* U+004E "N" */
    0xef, 0x20, 0xf, 0xb7, 0xc8, 0x1, 0xe2, 0x1,
    0xff, 0x15, 0x0, 0x7f, 0xc2, 0x90, 0x1, 0xfe,
    0xa0, 0x75, 0x0, 0xfe, 0x5a, 0xa, 0x30, 0xf,
    0xe5, 0x70, 0xd1, 0x0, 0xfe, 0x84, 0x1d, 0x0,
    0xff, 0x51, 0x1c, 0x80, 0x7f, 0xf, 0x83, 0x30,
    0x3, 0xf8, 0xa8, 0x24, 0x80, 0x3f, 0x95, 0xc0,
    0x3f, 0xf8, 0x10, 0x80, 0x1f, 0xfc, 0xb, 0x20,
    0x0,

    /* U+004F "O" */
    0x0, 0xc2, 0xd9, 0xdf, 0xd6, 0xa0, 0x1f, 0x27,
    0xc9, 0x81, 0xa, 0x56, 0x98, 0x6, 0x5b, 0x16,
    0xde, 0xdf, 0xc4, 0x2c, 0x20, 0x0, 0xd0, 0x4c,
    0x88, 0x2, 0x3b, 0x60, 0xf0, 0x4, 0x83, 0x30,
    0x3, 0xe9, 0x22, 0x28, 0x28, 0x50, 0x7, 0xf5,
    0x87, 0x81, 0x1, 0x80, 0x7f, 0x28, 0x30, 0x7,
    0xff, 0x14, 0x80, 0xc0, 0x3f, 0x94, 0x18, 0x14,
    0x28, 0x3, 0xfa, 0xc3, 0xc2, 0x41, 0x98, 0x1,
    0xf4, 0x91, 0x14, 0x6, 0x82, 0x68, 0x80, 0x23,
    0xb6, 0xf, 0x0, 0x96, 0xc5, 0x77, 0xf7, 0xf1,
    0xb, 0x8, 0x3, 0x27, 0xc1, 0x89, 0xa, 0x56,
    0x98, 0x0,

    /* U+0050 "P" */
    0xef, 0xfe, 0xec, 0x70, 0xe, 0x23, 0x31, 0x1,
    0xc6, 0x90, 0x5, 0x99, 0x6f, 0x6a, 0x17, 0x0,
    0x7e, 0x2b, 0x13, 0x30, 0x7, 0xf2, 0x3, 0x0,
    0x7f, 0x8, 0x7, 0xf8, 0x90, 0x1c, 0x3, 0xc2,
    0xbe, 0xa, 0x40, 0xe, 0xff, 0xba, 0x89, 0x28,
    0x2, 0x33, 0xc8, 0xd7, 0x60, 0xd, 0xb9, 0x95,
    0xca, 0x0, 0x7f, 0xf6, 0xc0,

    /* U+0051 "Q" */
    0x0, 0xc2, 0xd9, 0xdf, 0xd6, 0xa0, 0x1f, 0x93,
    0xe4, 0xc4, 0x85, 0x2b, 0x4c, 0x3, 0x96, 0xc5,
    0x77, 0xf7, 0xed, 0xb, 0x8, 0x2, 0x1a, 0x9,
    0xa2, 0x0, 0x92, 0xd8, 0x3c, 0x2, 0x90, 0x56,
    0x0, 0xfa, 0x48, 0x8a, 0x0, 0x50, 0x90, 0xf,
    0xeb, 0xf, 0x0, 0x10, 0x18, 0x7, 0xf2, 0x83,
    0x0, 0x7f, 0xf1, 0xc8, 0x8, 0x3, 0xf9, 0x41,
    0x80, 0x8, 0x16, 0x1, 0xfd, 0x41, 0xe0, 0xb,
    0x6, 0x50, 0xf, 0xa0, 0xc9, 0x40, 0x5, 0x21,
    0x52, 0x40, 0x11, 0xdb, 0x87, 0x0, 0x66, 0x90,
    0x6d, 0xec, 0xec, 0x41, 0xc3, 0x0, 0xe6, 0xd7,
    0x21, 0x31, 0x29, 0xe3, 0x0, 0xf8, 0xa3, 0x7d,
    0x80, 0xd0, 0x0, 0x34, 0x20, 0x1f, 0xa5, 0xc7,
    0x6e, 0xdc, 0xa4, 0x1, 0xfd, 0x16, 0x8a, 0x88,
    0xc3,

    /* U+0052 "R" */
    0xef, 0xfe, 0xec, 0x70, 0xe, 0x23, 0x31, 0x1,
    0xc6, 0x90, 0x5, 0x99, 0x6f, 0x6a, 0x17, 0x0,
    0x7e, 0x2b, 0x13, 0x30, 0x7, 0xf2, 0x3, 0x0,
    0x7f, 0x8, 0x7, 0xf8, 0x90, 0x1c, 0x3, 0xc2,
    0xbe, 0xa, 0x40, 0xe, 0xff, 0xba, 0x89, 0x68,
    0x2, 0x33, 0x90, 0x81, 0xa8, 0x3, 0x6e, 0x62,
    0xf0, 0x58, 0x80, 0x3f, 0xd, 0x7, 0x80, 0x7f,
    0x23, 0x14, 0x80, 0x7f, 0x49, 0x32, 0x0,

    /* U+0053 "S" */
    0x0, 0x9b, 0x3b, 0xfb, 0x1c, 0x40, 0x2c, 0x93,
    0x3e, 0x8c, 0x0, 0x41, 0x9b, 0x75, 0x9b, 0xd1,
    0x60, 0x4, 0xf, 0x20, 0x8, 0x5d, 0x80, 0x33,
    0x80, 0x7f, 0x50, 0x5b, 0x88, 0x7, 0xcd, 0x1,
    0x1d, 0xb2, 0x80, 0x1c, 0xfd, 0x2a, 0x6d, 0x7c,
    0x80, 0x18, 0x5a, 0xbb, 0x14, 0x6c, 0x80, 0x3e,
    0x3a, 0x70, 0x50, 0x10, 0xf, 0xbc, 0x3c, 0x32,
    0xcc, 0x3, 0x1c, 0x86, 0x3, 0xa6, 0x7e, 0x63,
    0xb0, 0x59, 0x2, 0xb1, 0x88, 0xcc, 0xb, 0x72,
    0x0,

    /* U+0054 "T" */
    0xef, 0xff, 0xf5, 0x29, 0x9c, 0x40, 0x3, 0x3c,
    0x57, 0x99, 0x78, 0x16, 0x66, 0x80, 0xf, 0xff,
    0xf8, 0x7, 0xff, 0xb4,

    /* U+0055 "U" */
    0xf, 0xf0, 0x7, 0xc5, 0xfa, 0x1, 0xff, 0xff,
    0x0, 0xff, 0xe1, 0x88, 0x7, 0xff, 0x8, 0x80,
    0xc0, 0x3c, 0xe0, 0xc0, 0x81, 0x40, 0x1e, 0x90,
    0xc0, 0xb0, 0x68, 0x0, 0x86, 0x8c, 0x58, 0xa,
    0x81, 0xff, 0x7b, 0x8a, 0x3a, 0x1, 0x2e, 0xb1,
    0x10, 0x49, 0xfc, 0xc0,

    /* U+0056 "V" */
    0xc, 0xf5, 0x0, 0xfe, 0xef, 0x10, 0x90, 0x90,
    0xf, 0xcc, 0x2c, 0x20, 0xa2, 0x66, 0x0, 0xfa,
    0xc2, 0xc0, 0x29, 0x9, 0x0, 0xf2, 0x12, 0x10,
    0x4, 0xc0, 0xa4, 0x1, 0xde, 0x1e, 0x1, 0x85,
    0x82, 0x80, 0x31, 0x21, 0x20, 0x7, 0x58, 0x38,
    0x6, 0xb0, 0xb0, 0xf, 0x12, 0x3, 0x0, 0x5,
    0x85, 0x80, 0x3e, 0xf0, 0xb0, 0x3, 0x3, 0x0,
    0x7e, 0x42, 0x22, 0x4, 0x84, 0x80, 0x7f, 0x58,
    0x7a, 0x8a, 0x88, 0x7, 0xf3, 0xa, 0x58, 0x48,
    0x7, 0xfc, 0xc0, 0x26, 0x60, 0xf, 0xfa, 0x40,
    0x12, 0x1, 0xe0,

    /* U+0057 "W" */
    0x3f, 0xd0, 0xf, 0xb3, 0xd8, 0x3, 0xc5, 0xf6,
    0x64, 0x43, 0x0, 0xe1, 0x30, 0xa0, 0xf, 0x50,
    0x70, 0x28, 0x58, 0x7, 0x30, 0x0, 0xc4, 0x3,
    0x94, 0x10, 0x2c, 0x14, 0x3, 0xac, 0x1c, 0x18,
    0x3, 0x19, 0x20, 0x81, 0x91, 0xc, 0x2, 0x23,
    0x4a, 0xb, 0x0, 0xd6, 0x1c, 0x1, 0x28, 0x58,
    0x4, 0xa1, 0xc4, 0x66, 0x10, 0x9, 0x41, 0x40,
    0x2b, 0x5, 0x0, 0xa8, 0x14, 0x2c, 0x18, 0x0,
    0x64, 0x80, 0x18, 0xc8, 0x86, 0x4, 0x45, 0x0,
    0x28, 0x58, 0x2, 0xc3, 0x80, 0x39, 0x42, 0xc2,
    0x83, 0xc0, 0x4, 0x66, 0x20, 0x60, 0x50, 0xe,
    0xb0, 0x50, 0x50, 0x50, 0xa, 0xc1, 0x4c, 0x50,
    0x3, 0xc6, 0x44, 0x62, 0x50, 0xc, 0xc1, 0x76,
    0xe, 0x0, 0xf9, 0x42, 0x43, 0x80, 0x30, 0x99,
    0x98, 0x14, 0x3, 0xeb, 0x0, 0x90, 0x3, 0xa8,
    0x5, 0x0, 0x3f, 0x19, 0x3, 0x0, 0x79, 0xc0,
    0x1c, 0x1, 0x80,

    /* U+0058 "X" */
    0x1f, 0xf1, 0x80, 0x7a, 0xfd, 0xc0, 0x68, 0x34,
    0x40, 0x33, 0x22, 0x1c, 0x0, 0xac, 0x36, 0x1,
    0x14, 0x8d, 0x0, 0x69, 0x25, 0x60, 0x7, 0x85,
    0x8, 0x7, 0x70, 0x49, 0x49, 0x3a, 0x0, 0x78,
    0xe0, 0x31, 0x8e, 0x0, 0x3f, 0x3a, 0x8, 0x70,
    0x7, 0xf0, 0xa8, 0x1, 0x40, 0x3f, 0xa8, 0x58,
    0x20, 0xc0, 0x3e, 0x74, 0x39, 0xa0, 0xe0, 0xf,
    0x1c, 0x7, 0x2, 0xa8, 0xa8, 0x3, 0xb8, 0x28,
    0x80, 0x14, 0x4a, 0xa0, 0xa, 0x89, 0x54, 0x1,
    0xb8, 0x28, 0x81, 0x54, 0x54, 0x1, 0xc7, 0x1,
    0xc0,

    /* U+0059 "Y" */
    0xc, 0xf5, 0x0, 0xf9, 0x3e, 0xc0, 0x1c, 0x16,
    0x20, 0x1e, 0x90, 0x90, 0x1, 0x38, 0xc0, 0x7,
    0x39, 0x41, 0x0, 0x50, 0x2c, 0x60, 0x11, 0x41,
    0x38, 0x7, 0x58, 0x78, 0x5, 0x21, 0x20, 0x1e,
    0x54, 0x36, 0x5, 0x45, 0x40, 0xf, 0xa4, 0x20,
    0x6c, 0x2c, 0x3, 0xf1, 0x38, 0xf8, 0xc0, 0x80,
    0x7f, 0x41, 0x9, 0x38, 0x7, 0xfc, 0xa1, 0x40,
    0x1f, 0xff, 0x60,

    /* U+005A "Z" */
    0xf, 0xff, 0xf9, 0x0, 0xcf, 0xf0, 0x80, 0x18,
    0x33, 0x3e, 0xd3, 0x9, 0x20, 0xf, 0xd4, 0x4c,
    0xc0, 0xf, 0xd0, 0x87, 0x20, 0x1f, 0x95, 0xc7,
    0x40, 0x3f, 0x1d, 0x6, 0x88, 0x7, 0xc3, 0xa1,
    0x46, 0x1, 0xfb, 0x45, 0xd4, 0x3, 0xf4, 0x9a,
    0x40, 0x7, 0xe6, 0x61, 0x50, 0x7, 0xe4, 0x90,
    0xf1, 0x0, 0xf8, 0x68, 0x7, 0xb3, 0x3e, 0x73,
    0x10, 0x13, 0x3f, 0xdc,

    /* U+005B "[" */
    0xef, 0xf9, 0x0, 0xa, 0xa6, 0x0, 0x55, 0x8,
    0x3, 0xff, 0xf9, 0x54, 0x20, 0x2, 0xa9, 0x80,

    /* U+005C "\\" */
    0x57, 0x0, 0xf8, 0xe1, 0x40, 0x3c, 0xe1, 0xe0,
    0x1e, 0x13, 0x50, 0xf, 0xa8, 0x14, 0x3, 0xce,
    0x1e, 0x1, 0xe1, 0x45, 0x0, 0xfb, 0x41, 0x40,
    0x3c, 0xe1, 0xc0, 0x1e, 0x14, 0x40, 0x7, 0xda,
    0xa, 0x1, 0xe7, 0xe, 0x0, 0xf0, 0xa2, 0x4,
    0x3, 0xdc, 0xe, 0x1, 0xe5, 0xd, 0x0, 0xf9,
    0x50, 0x40, 0x3d, 0xe0, 0xe0, 0x1e, 0x50, 0xd0,
    0xf, 0x95, 0x4, 0x3, 0xde, 0xe,

    /* U+005D "]" */
    0xaf, 0xfa, 0x71, 0x4c, 0x0, 0xd5, 0x20, 0x1f,
    0xff, 0xc6, 0xa9, 0x0, 0x62, 0x98, 0x0,

    /* U+005E "^" */
    0x0, 0xce, 0xa0, 0x1f, 0x2c, 0x50, 0x80, 0x7a,
    0x50, 0x18, 0x3, 0x8c, 0xde, 0xd4, 0x1, 0xd4,
    0x49, 0x44, 0x80, 0x10, 0xb5, 0x1, 0x9b, 0xc0,
    0x26, 0x6, 0x0, 0x52, 0x10, 0x2, 0x94, 0x40,
    0xc, 0x14, 0x8, 0x52, 0x1, 0x99, 0x80,

    /* U+005F "_" */
    0x0, 0xff, 0x7f, 0xff, 0xb3, 0x3f, 0xc0,

    /* U+0060 "`" */
    0x27, 0x70, 0x4, 0x44, 0x8c, 0x10, 0x2, 0xe8,
    0xe0, 0x80,

    /* U+0061 "a" */
    0x5, 0xbe, 0xfe, 0xb4, 0x0, 0x3d, 0x21, 0xa1,
    0xa5, 0xa8, 0x2a, 0xbb, 0x6f, 0x60, 0x2c, 0xa,
    0x84, 0x2, 0x72, 0x12, 0x0, 0x84, 0x78, 0x4,
    0x23, 0xbb, 0x8c, 0x1, 0x2e, 0x53, 0x77, 0x18,
    0x1, 0x83, 0x19, 0x12, 0x70, 0x0, 0x80, 0x80,
    0x46, 0x80, 0x6, 0xc, 0x76, 0x9c, 0x10, 0x4,
    0x48, 0xc4, 0xad, 0x38, 0x0,

    /* U+0062 "b" */
    0x3f, 0xb0, 0xf, 0xff, 0x20, 0xcf, 0x7e, 0xc9,
    0x0, 0x66, 0xf6, 0x33, 0x9b, 0x10, 0x2, 0x20,
    0xcd, 0xce, 0x71, 0xa1, 0x0, 0xb4, 0xc0, 0x28,
    0x51, 0x60, 0x1, 0x18, 0x7, 0x70, 0x68, 0x1,
    0x80, 0x3c, 0xc0, 0xc0, 0x6, 0x0, 0xf3, 0x3,
    0x0, 0x8, 0xc0, 0x3b, 0x83, 0x40, 0x2d, 0x30,
    0xa, 0x14, 0x58, 0x0, 0x61, 0x9b, 0x9c, 0xe1,
    0x42, 0x0, 0x6f, 0x63, 0x30, 0x36, 0xa0, 0x0,

    /* U+0063 "c" */
    0x0, 0x8e, 0xbb, 0xfa, 0x44, 0x2, 0x7c, 0x53,
    0x30, 0x37, 0x90, 0x24, 0xd, 0xee, 0x74, 0xa,
    0x4, 0x5, 0x20, 0x4, 0xfc, 0xc0, 0x62, 0x80,
    0x1c, 0x20, 0x40, 0x40, 0x1f, 0x88, 0x8, 0x3,
    0xf8, 0xc5, 0x0, 0x38, 0x40, 0x10, 0x14, 0x80,
    0x13, 0xf3, 0x2, 0x40, 0xde, 0xe7, 0x40, 0xa0,
    0x1, 0xf1, 0x4c, 0xc0, 0xde, 0x40,

    /* U+0064 "d" */
    0x0, 0xfe, 0x1f, 0xd0, 0xf, 0xfe, 0xda, 0x5f,
    0x7e, 0x30, 0x7, 0x45, 0xa1, 0x98, 0xa6, 0x80,
    0x25, 0x71, 0xbd, 0xce, 0x84, 0x0, 0xa4, 0x2d,
    0x0, 0x27, 0x60, 0x8, 0xc5, 0x40, 0x3a, 0x80,
    0x4, 0x4, 0x1, 0xe2, 0x0, 0x10, 0x10, 0x7,
    0x88, 0x2, 0x31, 0x40, 0xe, 0xb0, 0xa, 0x42,
    0x88, 0x2, 0x57, 0x0, 0x95, 0xc7, 0x6e, 0xb6,
    0x90, 0x3, 0x45, 0xa2, 0xa8, 0x22, 0xc0, 0x0,

    /* U+0065 "e" */
    0x0, 0x92, 0xfb, 0xf1, 0x80, 0x3a, 0x2d, 0x9,
    0xa, 0x70, 0x2, 0x57, 0x2d, 0xcb, 0xe5, 0x38,
    0x0, 0x48, 0x71, 0x0, 0x54, 0x4e, 0x0, 0x31,
    0x21, 0x1e, 0x40, 0x42, 0x1, 0xee, 0xf3, 0x1,
    0x10, 0x6, 0xef, 0xf3, 0x81, 0x8b, 0xa2, 0x7c,
    0x21, 0x1, 0xa8, 0x1, 0x15, 0x90, 0x1, 0x20,
    0x6f, 0xb3, 0x74, 0x88, 0x0, 0x9f, 0x14, 0xc,
    0xcb, 0x8c, 0x0,

    /* U+0066 "f" */
    0x0, 0x9b, 0x7f, 0x58, 0x0, 0xd2, 0x6a, 0x20,
    0x15, 0x6, 0x56, 0x30, 0x0, 0x80, 0x80, 0x2b,
    0xf1, 0xf, 0xf8, 0x71, 0x40, 0xa, 0xb0, 0xbd,
    0x8, 0x55, 0x40, 0x1f, 0xff, 0x50,

    /* U+0067 "g" */
    0x0, 0x92, 0xfb, 0xf5, 0xc3, 0x7c, 0x1, 0x16,
    0x86, 0x74, 0x61, 0x80, 0x15, 0xc6, 0xf7, 0x3a,
    0x8c, 0x40, 0x12, 0x14, 0x80, 0x12, 0xc8, 0x4,
    0x62, 0x80, 0x1c, 0xc0, 0x2, 0x2, 0x0, 0xfe,
    0x20, 0x20, 0xf, 0x8, 0x4, 0x62, 0xa0, 0x1c,
    0xc0, 0x14, 0x85, 0xa0, 0x4, 0xb0, 0x1, 0x2b,
    0x8d, 0xed, 0xf5, 0x18, 0x6, 0x8b, 0x43, 0x43,
    0x8d, 0x1, 0x0, 0x92, 0xfb, 0xf5, 0xc8, 0x14,
    0x6, 0x90, 0x3, 0x17, 0x7, 0x2, 0xaa, 0xff,
    0x2f, 0x74, 0x4a, 0xa0, 0x4d, 0x73, 0x24, 0x34,
    0xaa, 0x0,

    /* U+0068 "h" */
    0x3f, 0xb0, 0xf, 0xff, 0x0, 0xcf, 0x7f, 0x48,
    0x80, 0x4d, 0xec, 0x64, 0x2d, 0x80, 0x11, 0x1e,
    0xee, 0x22, 0x38, 0x5, 0xe4, 0x0, 0x29, 0xb,
    0x0, 0x11, 0x0, 0x32, 0x80, 0x80, 0x18, 0x3,
    0x84, 0xc, 0x3, 0xff, 0xd6,

    /* U+0069 "i" */
    0x3e, 0xb0, 0x41, 0x40, 0x77, 0x58, 0x2, 0x10,
    0xf, 0xec, 0x3, 0xff, 0xba,

    /* U+006A "j" */
    0x0, 0x8b, 0xb0, 0x3, 0x38, 0x98, 0x80, 0x4b,
    0x14, 0x20, 0x19, 0xd4, 0x3, 0xf, 0xe8, 0x7,
    0xff, 0xfc, 0x3, 0xc8, 0xe, 0xf, 0xb7, 0x41,
    0xc0, 0x3, 0x42, 0xa4, 0x0,

    /* U+006B "k" */
    0x3f, 0xb0, 0xf, 0xff, 0x41, 0x6f, 0xa8, 0x7,
    0xc5, 0x85, 0x2a, 0x1, 0xe3, 0xc1, 0x96, 0x0,
    0xf1, 0xe0, 0xcb, 0x0, 0x79, 0x30, 0x19, 0x80,
    0x1e, 0x5b, 0x0, 0x31, 0x0, 0x78, 0x42, 0x98,
    0x3c, 0x3, 0xeb, 0x59, 0x32, 0x90, 0xe, 0x44,
    0x0, 0x34, 0x59, 0x80, 0x1f, 0x86, 0x82, 0x48,
    0x3, 0xf2, 0x40, 0x78, 0x0,

    /* U+006C "l" */
    0x3f, 0xb0, 0xf, 0xfe, 0xf0,

    /* U+006D "m" */
    0x3f, 0xa3, 0xae, 0xfd, 0x70, 0x2, 0xe7, 0xfb,
    0x14, 0x3, 0x3e, 0x22, 0x99, 0x45, 0x54, 0x48,
    0x66, 0xa7, 0x0, 0x89, 0x3e, 0xf3, 0x0, 0xaa,
    0x6e, 0xbc, 0xb0, 0x81, 0x0, 0xa8, 0x40, 0x6,
    0xc0, 0x32, 0x1, 0x22, 0x1, 0x40, 0x4, 0x20,
    0x1b, 0xc1, 0x40, 0x38, 0xc0, 0x80, 0xc, 0x1,
    0xc6, 0x4, 0x1, 0xff, 0xff, 0x0, 0xff, 0xe8,
    0x0,

    /* U+006E "n" */
    0x3f, 0xa2, 0xae, 0xfe, 0x91, 0x0, 0x9f, 0x54,
    0x90, 0x1b, 0x0, 0x22, 0x3e, 0xcb, 0xf3, 0x27,
    0x0, 0xb8, 0x40, 0x3, 0x1, 0x60, 0x2, 0x20,
    0x6, 0x50, 0x10, 0x3, 0x0, 0x70, 0x81, 0x80,
    0x7f, 0xfa, 0xc0,

    /* U+006F "o" */
    0x0, 0x8e, 0xbb, 0xf6, 0x44, 0x3, 0x3e, 0x29,
    0x9c, 0xde, 0x60, 0x5, 0x81, 0xbd, 0xce, 0x81,
    0xd1, 0x9, 0xa, 0x40, 0x9, 0xd8, 0x58, 0xc,
    0x50, 0x3, 0xa8, 0x30, 0x80, 0x80, 0x3c, 0x40,
    0xc4, 0x4, 0x1, 0xe2, 0x6, 0x3, 0x14, 0x0,
    0xea, 0xc, 0x8, 0xa, 0x40, 0x9, 0xd8, 0x58,
    0x12, 0x6, 0xf7, 0x3a, 0x7, 0x44, 0x0, 0xf8,
    0xa6, 0x73, 0x79, 0x80,

    /* U+0070 "p" */
    0x3f, 0xa2, 0x9e, 0xfd, 0x92, 0x0, 0xcd, 0x8c,
    0xaa, 0x6, 0xc4, 0x0, 0x8c, 0xdb, 0x75, 0xb2,
    0x34, 0x20, 0x17, 0x10, 0x4, 0xcc, 0x16, 0x0,
    0x11, 0x80, 0x76, 0x86, 0x80, 0x18, 0x3, 0xcc,
    0xc, 0x0, 0x60, 0xf, 0x30, 0x30, 0x0, 0x8c,
    0x3, 0xb8, 0x34, 0x2, 0xd3, 0x0, 0xa1, 0x45,
    0x80, 0x4, 0x19, 0xb9, 0xce, 0x14, 0x20, 0x6,
    0xf6, 0x33, 0x3, 0x6a, 0x0, 0x61, 0x9e, 0xfe,
    0x92, 0x0, 0xff, 0xed, 0x0,

    /* U+0071 "q" */
    0x0, 0x92, 0xfb, 0xf1, 0x83, 0xf4, 0x1, 0x16,
    0x86, 0x62, 0x9a, 0x0, 0x95, 0xc6, 0xf7, 0x3a,
    0x14, 0x2, 0x90, 0xa4, 0x0, 0x9d, 0x80, 0x23,
    0x14, 0x0, 0xea, 0x0, 0x10, 0x10, 0x7, 0x88,
    0x0, 0x40, 0x40, 0x1e, 0x20, 0x8, 0xc5, 0x0,
    0x3a, 0x80, 0x29, 0xa, 0x40, 0x9, 0xd8, 0x2,
    0x57, 0x1b, 0xdc, 0xe8, 0x40, 0xd, 0x16, 0x86,
    0x62, 0x9a, 0x0, 0xe4, 0xbe, 0xfc, 0x60, 0xf,
    0xfe, 0xd8,

    /* U+0072 "r" */
    0x3f, 0xa1, 0x9e, 0x80, 0x3, 0x7b, 0xf, 0x80,
    0xc, 0x2f, 0xdc, 0x2, 0xa4, 0x0, 0xc2, 0x80,
    0x1c, 0x40, 0x1e, 0x70, 0xf, 0xfe, 0xa0,

    /* U+0073 "s" */
    0x0, 0x2e, 0x77, 0xf5, 0x28, 0x2, 0x68, 0xc9,
    0xd, 0x60, 0x5, 0x87, 0xb2, 0xf7, 0x5c, 0x6,
    0x6, 0x20, 0x11, 0x18, 0x11, 0x58, 0xc6, 0x1,
    0xde, 0x66, 0x9c, 0xfb, 0x30, 0x1, 0x67, 0x5c,
    0x22, 0x30, 0x80, 0x21, 0x47, 0xb6, 0x4, 0x9,
    0x30, 0xc, 0x40, 0x6a, 0xd9, 0xd7, 0x6e, 0x43,
    0x55, 0x52, 0x82, 0x29, 0x46, 0x0,

    /* U+0074 "t" */
    0x0, 0x3c, 0x0, 0x7a, 0x5c, 0x3, 0xff, 0x81,
    0x7e, 0x21, 0xff, 0xe, 0x28, 0x1, 0x56, 0x17,
    0xa1, 0xa, 0xa8, 0x3, 0xff, 0xbe, 0x40, 0x80,
    0x1d, 0x41, 0x76, 0xd7, 0x0, 0x34, 0x12, 0x1,
    0x0,

    /* U+0075 "u" */
    0x4f, 0xa0, 0xe, 0x3f, 0xb0, 0xf, 0xff, 0x68,
    0x80, 0x73, 0x0, 0x18, 0x18, 0x3, 0xb0, 0x0,
    0x45, 0x38, 0x4, 0xac, 0x1, 0x48, 0x46, 0xd6,
    0xd1, 0x80, 0x49, 0x88, 0x6a, 0x11, 0xc0, 0x0,

    /* U+0076 "v" */
    0xd, 0xf2, 0x0, 0xf7, 0x78, 0x2, 0xc2, 0xc0,
    0x39, 0x45, 0x80, 0xc, 0xc, 0x1, 0xdc, 0x16,
    0x1, 0x30, 0x30, 0x4, 0x48, 0x82, 0x0, 0xac,
    0x28, 0x2, 0xb0, 0xf0, 0xc, 0x48, 0x66, 0x0,
    0x31, 0x20, 0x7, 0x78, 0x48, 0x30, 0x50, 0x7,
    0x90, 0x94, 0x6c, 0x18, 0x3, 0xea, 0xa, 0x26,
    0x10, 0xf, 0x98, 0x10, 0x28, 0x3, 0xf0, 0xb0,
    0x19, 0x80, 0x30,

    /* U+0077 "w" */
    0xbf, 0x10, 0xe, 0xef, 0x0, 0xe1, 0xfa, 0xe0,
    0x60, 0xc, 0xa2, 0xc, 0x1, 0x9c, 0x3d, 0x42,
    0x80, 0x37, 0x0, 0x34, 0x3, 0x50, 0xa8, 0x29,
    0x10, 0x0, 0x28, 0xa8, 0x81, 0x0, 0x9, 0xb8,
    0x3, 0x82, 0x80, 0xe, 0x1f, 0xc0, 0xc0, 0x6,
    0xa, 0x0, 0x20, 0xa8, 0x2, 0x85, 0x54, 0x36,
    0x0, 0xa1, 0x30, 0x9, 0x88, 0xc8, 0xdc, 0x0,
    0xe6, 0x46, 0x4c, 0x1, 0xac, 0x2a, 0x81, 0x40,
    0xa, 0xa, 0xb0, 0xa0, 0xc, 0x64, 0xec, 0x46,
    0x0, 0x32, 0x56, 0x32, 0x0, 0xea, 0x11, 0x50,
    0x6, 0xa2, 0x1b, 0x0, 0xf3, 0x0, 0x18, 0x3,
    0x28, 0x1, 0x80, 0x20,

    /* U+0078 "x" */
    0x2f, 0xe1, 0x0, 0xdb, 0xe6, 0x52, 0x34, 0x1,
    0x51, 0x49, 0x83, 0x2a, 0x38, 0x32, 0xa9, 0x80,
    0x2a, 0x28, 0x59, 0x2b, 0x0, 0xee, 0xa, 0xe,
    0x10, 0xe, 0x34, 0x4, 0x30, 0xf, 0x32, 0x2,
    0x30, 0x7, 0x14, 0x8e, 0x8c, 0x90, 0x6, 0xf0,
    0xa2, 0xd0, 0xf0, 0xa, 0x49, 0xd0, 0xd, 0xca,
    0x81, 0x58, 0xe0, 0x2, 0x83, 0x56,

    /* U+0079 "y" */
    0xd, 0xf2, 0x0, 0xf7, 0x78, 0x2, 0xc2, 0xc0,
    0x39, 0x45, 0xc0, 0xc, 0x2c, 0x1, 0xdc, 0x14,
    0x1, 0x30, 0x38, 0x4, 0x48, 0x82, 0x0, 0xa4,
    0x28, 0x2, 0xb0, 0xf0, 0xc, 0x2a, 0x48, 0x0,
    0x62, 0x40, 0xe, 0x90, 0xf0, 0x60, 0xb0, 0xf,
    0x19, 0x21, 0x58, 0x30, 0x7, 0xd6, 0x1c, 0x4e,
    0x1, 0xf9, 0x84, 0xc2, 0x80, 0x3f, 0x98, 0x10,
    0x80, 0x3f, 0x88, 0x3c, 0x3, 0xc6, 0x0, 0x35,
    0x34, 0x0, 0xe2, 0xcd, 0xbc, 0xf, 0x0, 0xf1,
    0x39, 0xa1, 0xd9, 0x80, 0x78,

    /* U+007A "z" */
    0x1f, 0xff, 0xd0, 0x2a, 0xbe, 0x0, 0x68, 0x55,
    0x71, 0x83, 0x28, 0x7, 0x33, 0xe, 0x40, 0x38,
    0xe4, 0x74, 0x3, 0x87, 0x82, 0x84, 0x3, 0xa8,
    0xa1, 0x0, 0x39, 0xd1, 0xe, 0x1, 0xc9, 0x5,
    0x40, 0x1c, 0x34, 0x3, 0x75, 0x59, 0x88, 0x40,
    0xd5, 0x7b, 0x40,

    /* U+007B "{" */
    0x0, 0x1e, 0x7d, 0x0, 0x34, 0xcd, 0x80, 0x62,
    0x78, 0xc0, 0xe0, 0xc0, 0x1f, 0xfc, 0x91, 0x0,
    0xd1, 0x61, 0x80, 0x5, 0x11, 0x20, 0x3, 0x60,
    0x6c, 0x2, 0x30, 0x30, 0xf, 0xfe, 0x7b, 0x3,
    0x80, 0x44, 0x25, 0x8c, 0x0, 0xd3, 0x36, 0x0,

    /* U+007C "|" */
    0xee, 0x0, 0x7f, 0xf6, 0x0,

    /* U+007D "}" */
    0xaf, 0xc3, 0x0, 0x61, 0x9b, 0x40, 0xd, 0x86,
    0x26, 0x1, 0x30, 0x38, 0x7, 0xff, 0x24, 0xc0,
    0x40, 0x2c, 0xb, 0x80, 0x3, 0x88, 0x94, 0x1,
    0x63, 0x1a, 0x1, 0x8c, 0x2, 0x30, 0xf, 0xfe,
    0x5b, 0x3, 0x83, 0x61, 0x89, 0x86, 0x19, 0xb4,
    0x0,

    /* U+007E "~" */
    0x9, 0xee, 0x30, 0x5, 0xac, 0xec, 0x8b, 0x36,
    0x4c, 0x4b, 0x8d, 0x57, 0x4b, 0xb2, 0xc7, 0x10,
    0x0, 0x2f, 0x66, 0xc8, 0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 86, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 86, .box_w = 3, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 21, .adv_w = 125, .box_w = 6, .box_h = 6, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 36, .adv_w = 225, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 123, .adv_w = 199, .box_w = 12, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 211, .adv_w = 270, .box_w = 17, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 318, .adv_w = 220, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 413, .adv_w = 67, .box_w = 2, .box_h = 6, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 419, .adv_w = 108, .box_w = 6, .box_h = 19, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 470, .adv_w = 108, .box_w = 5, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 520, .adv_w = 128, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 553, .adv_w = 186, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 577, .adv_w = 73, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 589, .adv_w = 123, .box_w = 6, .box_h = 2, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 594, .adv_w = 73, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 600, .adv_w = 113, .box_w = 9, .box_h = 20, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 664, .adv_w = 213, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 743, .adv_w = 118, .box_w = 6, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 754, .adv_w = 184, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 817, .adv_w = 183, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 877, .adv_w = 214, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 939, .adv_w = 184, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1002, .adv_w = 197, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1080, .adv_w = 191, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1139, .adv_w = 206, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1220, .adv_w = 197, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1298, .adv_w = 73, .box_w = 4, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1315, .adv_w = 73, .box_w = 4, .box_h = 14, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1338, .adv_w = 186, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1382, .adv_w = 186, .box_w = 10, .box_h = 7, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 1400, .adv_w = 186, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 2},
    {.bitmap_index = 1444, .adv_w = 183, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1504, .adv_w = 331, .box_w = 20, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 1662, .adv_w = 234, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1743, .adv_w = 242, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1810, .adv_w = 231, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1884, .adv_w = 264, .box_w = 14, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1951, .adv_w = 214, .box_w = 11, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1983, .adv_w = 203, .box_w = 10, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2008, .adv_w = 247, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2084, .adv_w = 260, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2106, .adv_w = 99, .box_w = 3, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2111, .adv_w = 164, .box_w = 10, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2144, .adv_w = 230, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2208, .adv_w = 190, .box_w = 10, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2222, .adv_w = 306, .box_w = 15, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2295, .adv_w = 260, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2352, .adv_w = 269, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2442, .adv_w = 231, .box_w = 12, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2495, .adv_w = 269, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2608, .adv_w = 233, .box_w = 12, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2671, .adv_w = 199, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2744, .adv_w = 188, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2764, .adv_w = 253, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2808, .adv_w = 228, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2891, .adv_w = 360, .box_w = 22, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3022, .adv_w = 215, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3103, .adv_w = 207, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 3162, .adv_w = 210, .box_w = 13, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3222, .adv_w = 107, .box_w = 5, .box_h = 19, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 3238, .adv_w = 113, .box_w = 9, .box_h = 20, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3300, .adv_w = 107, .box_w = 5, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3315, .adv_w = 187, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 3354, .adv_w = 160, .box_w = 10, .box_h = 3, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3361, .adv_w = 192, .box_w = 6, .box_h = 3, .ofs_x = 2, .ofs_y = 12},
    {.bitmap_index = 3371, .adv_w = 191, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3424, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3488, .adv_w = 183, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3542, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3606, .adv_w = 196, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3665, .adv_w = 113, .box_w = 8, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3695, .adv_w = 221, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3777, .adv_w = 218, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3814, .adv_w = 89, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3827, .adv_w = 91, .box_w = 7, .box_h = 19, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 3856, .adv_w = 197, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3909, .adv_w = 89, .box_w = 3, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3914, .adv_w = 338, .box_w = 19, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3971, .adv_w = 218, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4006, .adv_w = 203, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4066, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 4135, .adv_w = 218, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4201, .adv_w = 131, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4224, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4278, .adv_w = 132, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4311, .adv_w = 217, .box_w = 11, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4343, .adv_w = 179, .box_w = 13, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4402, .adv_w = 288, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4494, .adv_w = 177, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4548, .adv_w = 179, .box_w = 13, .box_h = 15, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 4625, .adv_w = 167, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4668, .adv_w = 112, .box_w = 6, .box_h = 19, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 4708, .adv_w = 96, .box_w = 2, .box_h = 19, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 4713, .adv_w = 112, .box_w = 6, .box_h = 19, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4754, .adv_w = 186, .box_w = 10, .box_h = 4, .ofs_x = 1, .ofs_y = 5}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 3, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 14, 0, 9, -7, 0, 0, 0,
    0, -18, -19, 2, 15, 7, 5, -13,
    2, 16, 1, 13, 3, 10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 19, 3, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, 0, 0, 0, 0, -6,
    5, 6, 0, 0, -3, 0, -2, 3,
    0, -3, 0, -3, -2, -6, 0, 0,
    0, 0, -3, 0, 0, -4, -5, 0,
    0, -3, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, -9, 0, -39, 0, 0, -6, 0,
    6, 10, 0, 0, -6, 3, 3, 11,
    6, -5, 6, 0, 0, -18, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -4, -16, 0, -13, -2, 0, 0, 0,
    0, 1, 12, 0, -10, -3, -1, 1,
    0, -5, 0, 0, -2, -24, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -26, -3, 12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 11, 0, 3, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 3, 1, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 6, 3, 10, -3, 0, 0, 6,
    -3, -11, -44, 2, 9, 6, 1, -4,
    0, 12, 0, 10, 0, 10, 0, -30,
    0, -4, 10, 0, 11, -3, 6, 3,
    0, 0, 1, -3, 0, 0, -5, 26,
    0, 26, 0, 10, 0, 13, 4, 5,
    0, 0, 0, -12, 0, 0, 0, 0,
    1, -2, 0, 2, -6, -4, -6, 2,
    0, -3, 0, 0, 0, -13, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -21, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, -18, 0, -20, 0, 0, 0, 0,
    -2, 0, 32, -4, -4, 3, 3, -3,
    0, -4, 3, 0, 0, -17, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -31, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 19, 0, 0, -12, 0, 11, 0,
    -22, -31, -22, -6, 10, 0, 0, -21,
    0, 4, -7, 0, -5, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 8, 10, -39, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, 0, 0, 2,
    2, -4, -6, 0, -1, -1, -3, 0,
    0, -2, 0, 0, 0, -6, 0, -3,
    0, -7, -6, 0, -8, -11, -11, -6,
    0, -6, 0, -6, 0, 0, 0, 0,
    -3, 0, 0, 3, 0, 2, -3, 0,
    0, 0, 0, 3, -2, 0, 0, 0,
    -2, 3, 3, -1, 0, 0, 0, -6,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 4, -2, 0, -4, 0, -5, 0,
    0, -2, 0, 10, 0, 0, -3, 0,
    0, 0, 0, 0, -1, 1, -2, -2,
    0, -3, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, 0,
    -3, -4, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -3, -3, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, -2, -4, 0,
    0, -10, -2, -10, 6, 0, 0, -6,
    3, 6, 9, 0, -8, -1, -4, 0,
    -1, -15, 3, -2, 2, -17, 3, 0,
    0, 1, -17, 0, -17, -3, -28, -2,
    0, -16, 0, 6, 9, 0, 4, 0,
    0, 0, 0, 1, 0, -6, -4, 0,
    0, 0, 0, -3, 0, 0, 0, -3,
    0, 0, 0, 0, 0, -2, -2, 0,
    -2, -4, 0, 0, 0, 0, 0, 0,
    0, -3, -3, 0, -2, -4, -3, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, -2, 0, -6, 3, 0, 0, -4,
    2, 3, 3, 0, 0, 0, 0, 0,
    0, -2, 0, 0, 0, 0, 0, 2,
    0, 0, -3, 0, -3, -2, -4, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    -3, 0, 0, 0, 0, -4, -5, 0,
    0, 10, -2, 1, -10, 0, 0, 9,
    -16, -17, -13, -6, 3, 0, -3, -21,
    -6, 0, -6, 0, -6, 5, -6, -20,
    0, -9, 0, 0, 2, -1, 3, -2,
    0, 3, 0, -10, -12, 0, -16, -8,
    -7, -8, -10, -4, -9, -1, -6, -9,
    0, 1, 0, -3, 0, 0, 0, 2,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, -2,
    0, -1, -3, 0, -5, -7, -7, -1,
    0, -10, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 1, -2, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 15, 0, 0, 0, 0, 0,
    0, 2, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, 3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, -6, 0, 0, 0,
    0, -16, -10, 0, 0, 0, -5, -16,
    0, 0, -3, 3, 0, -9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, 0, 0, 0, 4, 0,
    2, -6, -6, 0, -3, -3, -4, 0,
    0, 0, 0, 0, 0, -10, 0, -3,
    0, -5, -3, 0, -7, -8, -10, -3,
    0, -6, 0, -10, 0, 0, 0, 0,
    26, 0, 0, 2, 0, 0, -4, 0,
    0, -14, 0, 0, 0, 0, 0, -30,
    -6, 11, 10, -3, -13, 0, 3, -5,
    0, -16, -2, -4, 3, -22, -3, 4,
    0, 5, -11, -5, -12, -11, -13, 0,
    0, -19, 0, 18, 0, 0, -2, 0,
    0, 0, -2, -2, -3, -9, -11, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, -2, -3, -5, 0,
    0, -6, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -6, 0, 0, 6,
    -1, 4, 0, -7, 3, -2, -1, -8,
    -3, 0, -4, -3, -2, 0, -5, -5,
    0, 0, -3, -1, -2, -5, -4, 0,
    0, -3, 0, 3, -2, 0, -7, 0,
    0, 0, -6, 0, -5, 0, -5, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 3, 0, -4, 0, -2, -4, -10,
    -2, -2, -2, -1, -2, -4, -1, 0,
    0, 0, 0, 0, -3, -3, -3, 0,
    0, 0, 0, 4, -2, 0, -2, 0,
    0, 0, -2, -4, -2, -3, -4, -3,
    3, 13, -1, 0, -9, 0, -2, 6,
    0, -3, -13, -4, 5, 0, 0, -15,
    -5, 3, -5, 2, 0, -2, -3, -10,
    0, -5, 2, 0, 0, -5, 0, 0,
    0, 3, 3, -6, -6, 0, -5, -3,
    -5, -3, -3, 0, -5, 2, -6, -5,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    0, 0, -4, 0, 0, -3, -3, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, -2, 0,
    0, 0, -5, 0, -6, 0, 0, 0,
    -11, 0, 2, -7, 6, 1, -2, -15,
    0, 0, -7, -3, 0, -13, -8, -9,
    0, 0, -14, -3, -13, -12, -15, 0,
    -8, 0, 3, 21, -4, 0, -7, -3,
    -1, -3, -5, -9, -6, -12, -13, -7,
    0, 0, -2, 0, 1, 0, 0, -22,
    -3, 10, 7, -7, -12, 0, 1, -10,
    0, -16, -2, -3, 6, -29, -4, 1,
    0, 0, -21, -4, -17, -3, -23, 0,
    0, -22, 0, 19, 1, 0, -2, 0,
    0, 0, 0, -2, -2, -12, -2, 0,
    0, 0, 0, 0, -10, 0, -3, 0,
    -1, -9, -15, 0, 0, -2, -5, -10,
    -3, 0, -2, 0, 0, 0, 0, -14,
    -3, -11, -10, -3, -5, -8, -3, -5,
    0, -6, -3, -11, -5, 0, -4, -6,
    -3, -6, 0, 2, 0, -2, -11, 0,
    0, -6, 0, 0, 0, 0, 4, 0,
    2, -6, 13, 0, -3, -3, -4, 0,
    0, 0, 0, 0, 0, -10, 0, -3,
    0, -5, -3, 0, -7, -8, -10, -3,
    0, -6, 3, 13, 0, 0, 0, 0,
    26, 0, 0, 2, 0, 0, -4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, -2, -6,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, -3, -3, 0, 0, -6, -3, 0,
    0, -6, 0, 5, -2, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    6, 3, -3, 0, -10, -5, 0, 10,
    -11, -10, -6, -6, 13, 6, 3, -28,
    -2, 6, -3, 0, -3, 4, -3, -11,
    0, -3, 3, -4, -3, -10, -3, 0,
    0, 10, 6, 0, -9, 0, -18, -4,
    9, -4, -12, 1, -4, -11, -11, -3,
    3, 0, -5, 0, -9, 0, 3, 11,
    -7, -12, -13, -8, 10, 0, 1, -23,
    -3, 3, -5, -2, -7, 0, -7, -12,
    -5, -5, -3, 0, 0, -7, -7, -3,
    0, 10, 7, -3, -18, 0, -18, -4,
    0, -11, -19, -1, -10, -5, -11, -9,
    0, 0, -4, 0, -6, -3, 0, -3,
    -6, 0, 5, -11, 3, 0, 0, -17,
    0, -3, -7, -5, -2, -10, -8, -11,
    -7, 0, -10, -3, -7, -6, -10, -3,
    0, 0, 1, 15, -5, 0, -10, -3,
    0, -3, -6, -7, -9, -9, -12, -4,
    6, 0, -5, 0, -16, -4, 2, 6,
    -10, -12, -6, -11, 11, -3, 2, -30,
    -6, 6, -7, -5, -12, 0, -10, -13,
    -4, -3, -3, -3, -7, -10, -1, 0,
    0, 10, 9, -2, -21, 0, -19, -7,
    8, -12, -22, -6, -11, -13, -16, -11,
    0, 0, 0, 0, -4, 0, 0, 3,
    -4, 6, 2, -6, 6, 0, 0, -10,
    -1, 0, -1, 0, 1, 1, -3, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 3, 10, 1, 0, -4, 0,
    0, 0, 0, -2, -2, -4, 0, 0,
    1, 3, 0, 0, 0, 0, 3, 0,
    -3, 0, 12, 0, 6, 1, 1, -4,
    0, 6, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 10, 0, 9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 0, -3, 5, 0, 10, 0,
    0, 32, 4, -6, -6, 3, 3, -2,
    1, -16, 0, 0, 15, -19, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -22, 12, 45, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, 0, -6, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -9, 0, 0, 1, 0,
    0, 3, 41, -6, -3, 10, 9, -9,
    3, 0, 0, 3, 3, -4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -42, 9, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, -9,
    0, 0, 0, 0, -7, -2, 0, 0,
    0, -7, 0, -4, 0, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -21, 0, 0, 0, 0, 1, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, -5, 0, -9, 0, 0, 0, -5,
    3, -4, 0, 0, -9, -3, -7, 0,
    0, -9, 0, -3, 0, -15, 0, -4,
    0, 0, -26, -6, -13, -4, -12, 0,
    0, -21, 0, -9, -2, 0, 0, 0,
    0, 0, 0, 0, 0, -5, -6, -3,
    0, 0, 0, 0, -7, 0, -7, 4,
    -4, 6, 0, -2, -7, -2, -5, -6,
    0, -4, -2, -2, 2, -9, -1, 0,
    0, 0, -28, -3, -4, 0, -7, 0,
    -2, -15, -3, 0, 0, -2, -3, 0,
    0, 0, 0, 2, 0, -2, -5, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 4, 0, 0, 0, 0,
    0, -7, 0, -2, 0, 0, 0, -6,
    3, 0, 0, 0, -9, -3, -6, 0,
    0, -9, 0, -3, 0, -15, 0, 0,
    0, 0, -31, 0, -6, -12, -16, 0,
    0, -21, 0, -2, -5, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -5, -2,
    1, 0, 0, 5, -4, 0, 10, 16,
    -3, -3, -10, 4, 16, 5, 7, -9,
    4, 13, 4, 9, 7, 9, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 20, 15, -6, -3, 0, -3, 26,
    14, 26, 0, 0, 0, 3, 0, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -27, -4, -3, -13, -16, 0,
    0, -21, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -27, -4, -3, -13, -16, 0,
    0, -13, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -7, 3, 0, -3, 3, 6, 3, -10,
    0, -1, -3, 3, 0, 3, 0, 0,
    0, 0, -8, 0, -3, -2, -6, 0,
    -3, -13, 0, 20, -3, 0, -7, -2,
    0, -2, -5, 0, -3, -9, -6, -4,
    0, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, 4, 0, 0,
    0, 0, -27, -4, -3, -13, -16, 0,
    0, -21, 0, 0, 0, 0, 0, 0,
    16, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -5, 0, -10, -4, -3, 10,
    -3, -3, -13, 1, -2, 1, -2, -9,
    1, 7, 1, 3, 1, 3, -8, -13,
    -4, 0, -12, -6, -9, -13, -12, 0,
    -5, -6, -4, -4, -3, -2, -4, -2,
    0, -2, -1, 5, 0, 5, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -3, -3, 0,
    0, -9, 0, -2, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, -3, 0,
    0, 0, 0, 0, -3, 0, 0, -5,
    -3, 3, 0, -5, -6, -2, 0, -9,
    -2, -7, -2, -4, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -21, 0, 10, 0, 0, -6, 0,
    0, 0, 0, -4, 0, -3, 0, 0,
    0, 0, -2, 0, -7, 0, 0, 13,
    -4, -11, -10, 2, 4, 4, -1, -9,
    2, 5, 2, 10, 2, 11, -2, -9,
    0, 0, -13, 0, 0, -10, -9, 0,
    0, -6, 0, -4, -5, 0, -5, 0,
    -5, 0, -2, 5, 0, -3, -10, -3,
    0, 0, -3, 0, -6, 0, 0, 4,
    -7, 0, 3, -3, 3, 0, 0, -11,
    0, -2, -1, 0, -3, 4, -3, 0,
    0, 0, -13, -4, -7, 0, -10, 0,
    0, -15, 0, 12, -3, 0, -6, 0,
    2, 0, -3, 0, -3, -10, 0, -3,
    0, 0, 0, 0, -2, 0, 0, 3,
    -4, 1, 0, 0, -4, -2, 0, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -20, 0, 7, 0, 0, -3, 0,
    0, 0, 0, 1, 0, -3, -3, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 60,
    .right_class_cnt     = 48,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/

static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 1,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 1,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t test_font_montserrat_ascii_4bpp_compressed = {
#else
lv_font_t test_font_montserrat_ascii_4bpp_compressed = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 22,          /*The maximum line height required by the font*/
    .base_line = 4,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if TEST_FONT_MONTSERRAT_ASCII_4BPP_COMPRESSED*/

#endif /*LV_BUILD_TEST*/
