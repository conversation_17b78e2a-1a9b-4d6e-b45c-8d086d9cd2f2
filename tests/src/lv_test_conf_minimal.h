#ifndef LV_TEST_CONF_MINIMAL_H
#define LV_TEST_CONF_MINIMAL_H

#define  LV_MEM_SIZE        65535
#define  LV_USE_LOG         1
#define  LV_USE_ASSERT_NULL             0
#define  LV_USE_ASSERT_MALLOC           0
#define  LV_USE_ASSERT_MEM_INTEGRITY    0
#define  LV_USE_ASSERT_OBJ              0
#define  LV_USE_ASSERT_STYLE            0

#define  LV_USE_BIDI                    0
#define  LV_USE_ARABIC_PERSIAN_CHARS    0

#define  LV_BUILD_EXAMPLES              1

#define  LV_USE_THEME_SIMPLE            1
#define  LV_USE_THEME_DEFAULT           0

#define  LV_USE_LODEPNG                 1
#define  LV_USE_BMP                     1
#define  LV_USE_RLE                     1
#define  LV_USE_GIF                     1
#define  LV_USE_QRCODE                  1

#endif /* LV_TEST_CONF_MINIMAL_H */
