#if LV_BUILD_TEST
#include "../lvgl.h"
#include "../../lvgl_private.h"

#include "unity/unity.h"
#include <string.h>

const char * read_exp =
    "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam sed maximus orci. Morbi massa nisi, varius eu convallis ac, venenatis at metus. In in nibh id urna pretium feugiat vitae eu libero. Ut eget fringilla eros. Nunc ullamcorper lectus mauris, vel rhoncus velit volutpat et. Phasellus sed molestie massa. Maecenas quis dui sollicitudin, vulputate nunc ut, dictum quam. Nam a congue lorem. Nulla non facilisis sapien. Ut luctus nulla nibh, sed finibus urna porta non. Duis aliquet augue id urna euismod auctor. Integer pellentesque vulputate enim non mattis. Donec finibus mattis dolor, et feugiat nisi pharetra porta. Mauris ullamcorper cursus magna. Orci varius natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.";

static void read_range(lv_fs_file_t * f, uint32_t from, uint32_t to);

static void read_random_drv(char drv_letter, uint32_t cache_size);

void setUp(void)
{
    /* Function run before every test */
}

void tearDown(void)
{
    /* Function run after every test */
}
#include <stdio.h>
#include <errno.h>
#include <unistd.h>

void test_read(void)
{
    lv_fs_res_t res;

    char cur[512];
    getcwd(cur, 512);
    errno = 0;
    void * a = fopen("src/test_files/readtest.txt", "r");
    printf("%s, %d, %p\n", cur, errno, a);
    fclose(a);

    /*'A' has cache*/
    lv_fs_file_t fa;
    res = lv_fs_open(&fa, "A:src/test_files/readtest.txt", LV_FS_MODE_RD);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);

    /*'B' has no cache*/
    lv_fs_file_t fb;
    res = lv_fs_open(&fb, "B:src/test_files/readtest.txt", LV_FS_MODE_RD);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);

    /*Use an odd size to make sure it's not aligned with the driver's cache size*/
    uint8_t buf[79];
    uint32_t cnt = 0;
    uint32_t br = 1;
    while(br) {
        res = lv_fs_read(&fa, buf, sizeof(buf), &br);
        TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);
        TEST_ASSERT_TRUE(memcmp(buf, read_exp + cnt, br) == 0);

        res = lv_fs_read(&fb, buf, sizeof(buf), &br);
        TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);
        TEST_ASSERT_TRUE(memcmp(buf, read_exp + cnt, br) == 0);
        cnt += br;
    }

    lv_fs_close(&fa);
    lv_fs_close(&fb);
}

void test_read_random(void)
{
    read_random_drv('A', 8);
    read_random_drv('B', 8);

    read_random_drv('A', 32);
    read_random_drv('B', 32);

    read_random_drv('A', 128);
    read_random_drv('B', 128);

    read_random_drv('A', 1024);
    read_random_drv('B', 1024);
}

/**
 * Read bytes from the `from` index to the `to index`
 * Assume that file `f` has 256 byte of content 0..255
 */
static void read_range(lv_fs_file_t * f, uint32_t from, uint32_t to)
{
    lv_fs_seek(f, from, LV_FS_SEEK_SET);

    uint32_t len = to - from + 1;
    uint8_t buf_rd[256];
    uint32_t br;
    lv_fs_read(f, buf_rd, len, &br);

    TEST_ASSERT_EQUAL(br, len);

    uint32_t i;
    for(i = 0; i < len; i++) {
        TEST_ASSERT_EQUAL(buf_rd[i], from + i);
    }
}

static void read_next(lv_fs_file_t * f, uint32_t from, uint32_t len)
{
    uint8_t buf_rd[256];
    uint32_t br;
    lv_fs_read(f, buf_rd, len, &br);

    TEST_ASSERT_EQUAL(br, len);

    uint32_t i;
    for(i = 0; i < br; i++) {
        TEST_ASSERT_EQUAL(buf_rd[i], from + i);
    }
}

static void read_random_drv(char drv_letter, uint32_t cache_size)
{
    /*Hack to force a small cache size*/
    lv_fs_drv_t * drv = lv_fs_get_drv(drv_letter);
    uint32_t original_cache_size = drv->cache_size;
    drv->cache_size = cache_size;

    char fn[64];
    lv_snprintf(fn, sizeof(fn), "%c:fs_read_random.bin", drv_letter);

    lv_fs_res_t res;
    lv_fs_file_t f;
    res = lv_fs_open(&f, fn, LV_FS_MODE_WR);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);

    uint8_t buf256[256];
    uint32_t i;
    for(i = 0; i < 256; i++) buf256[i] = i;

    res = lv_fs_write(&f, buf256, 256, NULL);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);

    res = lv_fs_close(&f);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);

    res = lv_fs_open(&f, fn, LV_FS_MODE_RD);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);

    // *INDENT-OFF*
    static const uint8_t ranges[1000][4] = {
            /*{from, to, next_read_cnt, next_read_cnt}*/
            {34, 36, 31, 13}, {40, 55, 40, 19}, {75, 92, 31, 1}, {80, 91, 19, 31}, {121, 141, 5, 22}, {8, 23, 21, 39}, {33, 37, 5, 34}, {71, 90, 22, 37}, {111, 124, 14, 38}, {2, 15, 6, 16}, {109, 129, 14, 13}, {85, 97, 10, 24}, {79, 90, 31, 32}, {8, 23, 4, 1}, {13, 21, 9, 15}, {90, 95, 2, 21}, {77, 95, 17, 28}, {60, 75, 26, 13}, {115, 117, 19, 24}, {31, 46, 9, 17}, {81, 98, 8, 32}, {115, 121, 33, 2}, {25, 31, 34, 23}, {26, 42, 16, 26}, {105, 121, 30, 16}, {82, 99, 26, 3}, {83, 87, 37, 6}, {33, 43, 34, 36}, {35, 51, 18, 30}, {29, 34, 20, 31}, {25, 36, 21, 23}, {123, 125, 40, 16}, {12, 30, 35, 14}, {80, 94, 19, 19}, {94, 112, 19, 13}, {113, 117, 15, 20}, {39, 53, 19, 14}, {38, 49, 29, 16}, {56, 63, 3, 7}, {128, 140, 8, 7}, {112, 115, 5, 16}, {68, 87, 24, 34}, {88, 95, 18, 12}, {4, 20, 18, 29}, {4, 8, 10, 32}, {33, 43, 28, 3}, {67, 80, 10, 13}, {74, 86, 14, 8}, {78, 98, 12, 26}, {86, 92, 24, 34}, {63, 66, 12, 30}, {117, 119, 40, 37}, {90, 97, 23, 11}, {95, 107, 39, 40}, {67, 74, 6, 34}, {128, 133, 11, 22}, {98, 99, 12, 25}, {91, 110, 5, 15}, {33, 37, 13, 32}, {12, 32, 3, 34}, {100, 102, 5, 25}, {52, 63, 37, 31}, {108, 125, 39, 7}, {77, 81, 28, 27}, {36, 54, 9, 29}, {109, 126, 12, 29}, {61, 70, 28, 19}, {47, 66, 9, 25}, {66, 69, 3, 17}, {75, 89, 26, 5}, {89, 103, 22, 15}, {113, 133, 34, 28}, {113, 114, 32, 33}, {124, 141, 9, 33}, {79, 85, 30, 28}, {60, 61, 30, 9}, {39, 54, 9, 18}, {93, 104, 35, 17}, {112, 122, 15, 6}, {104, 116, 1, 3}, {60, 62, 29, 9}, {119, 132, 40, 22}, {54, 70, 15, 14}, {8, 20, 24, 30}, {18, 29, 24, 1}, {104, 120, 15, 23}, {15, 25, 16, 32}, {45, 54, 14, 16}, {75, 79, 1, 38}, {52, 59, 15, 19}, {109, 128, 35, 38}, {85, 89, 7, 33}, {5, 6, 26, 32}, {12, 16, 32, 39}, {85, 105, 30, 4}, {88, 103, 37, 31}, {88, 104, 21, 23}, {18, 31, 5, 32}, {35, 50, 7, 5}, {3, 4, 3, 3}, {105, 120, 7, 3},
            {21, 37, 21, 23}, {75, 93, 4, 39}, {3, 10, 6, 40}, {90, 107, 1, 23}, {100, 115, 37, 11}, {99, 107, 18, 14}, {127, 135, 11, 33}, {63, 74, 27, 14}, {96, 108, 3, 14}, {64, 76, 19, 3}, {67, 84, 15, 34}, {82, 91, 15, 22}, {34, 44, 14, 36}, {107, 114, 15, 31}, {120, 130, 31, 33}, {126, 134, 25, 31}, {58, 78, 11, 14}, {124, 127, 19, 13}, {114, 129, 13, 12}, {111, 128, 9, 40}, {127, 134, 9, 23}, {128, 131, 39, 7}, {62, 65, 28, 29}, {87, 104, 38, 17}, {13, 29, 24, 37}, {124, 133, 20, 24}, {63, 64, 28, 32}, {59, 69, 17, 8}, {12, 13, 1, 25}, {64, 78, 14, 25}, {49, 69, 20, 36}, {96, 105, 10, 8}, {29, 31, 27, 11}, {67, 83, 37, 2}, {89, 96, 2, 6}, {23, 27, 7, 1}, {87, 93, 13, 9}, {25, 43, 1, 26}, {81, 93, 12, 39}, {62, 68, 13, 32}, {114, 133, 6, 19}, {10, 21, 2, 25}, {94, 102, 24, 12}, {13, 29, 2, 26}, {110, 111, 14, 10}, {120, 123, 17, 1}, {121, 135, 31, 16}, {120, 127, 3, 34}, {74, 79, 32, 24}, {120, 138, 16, 13}, {121, 133, 25, 20}, {87, 101, 33, 31}, {79, 89, 27, 39}, {48, 60, 20, 35}, {43, 63, 9, 30}, {8, 27, 39, 13}, {77, 83, 30, 29}, {109, 116, 24, 5}, {122, 139, 4, 34}, {74, 82, 1, 22}, {98, 118, 20, 4}, {30, 34, 9, 25}, {98, 99, 37, 35}, {105, 108, 17, 4}, {84, 103, 19, 8}, {116, 133, 28, 40}, {53, 70, 27, 23}, {91, 93, 40, 8}, {116, 124, 21, 30}, {66, 68, 11, 32}, {91, 109, 27, 27}, {74, 83, 3, 17}, {57, 60, 22, 12}, {7, 15, 37, 16}, {93, 106, 6, 22}, {104, 106, 30, 20}, {56, 73, 9, 38}, {105, 110, 5, 19}, {122, 134, 30, 37}, {71, 83, 25, 26}, {68, 85, 21, 28}, {126, 140, 10, 29}, {87, 93, 38, 14}, {68, 82, 31, 20}, {26, 41, 8, 32}, {52, 55, 17, 13}, {51, 60, 31, 14}, {122, 126, 30, 26}, {32, 43, 3, 22}, {76, 93, 28, 24}, {97, 117, 12, 21}, {9, 17, 36, 21}, {52, 71, 24, 5}, {123, 138, 14, 21}, {82, 84, 9, 17}, {32, 34, 40, 24}, {89, 99, 21, 15}, {16, 32, 19, 9}, {39, 48, 38, 6}, {125, 129, 18, 31},
            {28, 37, 21, 15}, {70, 77, 35, 31}, {107, 127, 18, 31}, {118, 125, 3, 23}, {89, 97, 28, 25}, {78, 96, 21, 40}, {19, 20, 9, 21}, {25, 38, 19, 35}, {63, 83, 2, 34}, {39, 58, 37, 1}, {52, 63, 1, 33}, {110, 120, 10, 16}, {84, 87, 31, 22}, {10, 22, 32, 22}, {84, 87, 39, 21}, {61, 70, 12, 22}, {118, 132, 4, 30}, {41, 50, 19, 36}, {3, 5, 40, 2}, {122, 123, 4, 28}, {121, 139, 25, 38}, {49, 69, 32, 13}, {60, 78, 32, 24}, {26, 32, 36, 19}, {54, 71, 15, 7}, {70, 76, 17, 37}, {15, 32, 15, 40}, {37, 52, 22, 22}, {46, 47, 20, 27}, {90, 91, 37, 23}, {104, 123, 4, 31}, {111, 129, 1, 33}, {25, 45, 21, 36}, {1, 21, 29, 25}, {104, 123, 33, 26}, {1, 3, 17, 20}, {63, 77, 14, 5}, {48, 55, 29, 19}, {126, 137, 29, 32}, {83, 91, 1, 36}, {120, 123, 22, 18}, {42, 57, 27, 9}, {106, 114, 8, 20}, {101, 107, 14, 36}, {52, 58, 11, 26}, {16, 22, 39, 27}, {107, 120, 35, 31}, {21, 37, 9, 11}, {110, 128, 35, 5}, {65, 83, 39, 39}, {13, 18, 10, 9}, {95, 96, 37, 8}, {94, 96, 14, 2}, {85, 87, 25, 7}, {27, 38, 1, 34}, {15, 25, 21, 27}, {87, 92, 18, 24}, {34, 44, 10, 19}, {123, 140, 31, 35}, {40, 48, 14, 2}, {63, 80, 14, 22}, {104, 109, 9, 39}, {109, 121, 36, 22}, {43, 53, 26, 36}, {117, 127, 15, 15}, {27, 31, 34, 32}, {30, 41, 2, 11}, {89, 102, 40, 16}, {107, 124, 22, 19}, {104, 117, 33, 22}, {10, 26, 21, 32}, {51, 58, 11, 34}, {87, 98, 27, 25}, {99, 116, 36, 35}, {103, 114, 4, 34}, {109, 121, 25, 8}, {114, 126, 6, 6}, {47, 66, 31, 14}, {115, 125, 39, 34}, {27, 29, 15, 7}, {103, 109, 12, 5}, {109, 111, 21, 39}, {83, 84, 7, 2}, {63, 67, 2, 37}, {14, 15, 35, 10}, {43, 56, 25, 27}, {74, 93, 2, 22}, {59, 79, 2, 18}, {56, 70, 39, 16}, {116, 128, 40, 38}, {14, 24, 6, 26}, {103, 106, 18, 29}, {128, 146, 7, 21}, {63, 83, 32, 33}, {109, 116, 26, 16}, {85, 89, 10, 5}, {116, 129, 17, 37}, {97, 116, 26, 7}, {98, 100, 40, 13}, {94, 109, 24, 32},
            {105, 118, 7, 40}, {37, 45, 18, 18}, {65, 77, 34, 18}, {47, 59, 30, 21}, {4, 6, 35, 16}, {34, 47, 17, 38}, {81, 97, 17, 9}, {80, 88, 38, 8}, {118, 123, 34, 19}, {82, 93, 24, 18}, {82, 101, 21, 17}, {67, 72, 18, 27}, {117, 121, 20, 21}, {107, 119, 16, 11}, {19, 36, 13, 29}, {39, 56, 40, 20}, {14, 22, 24, 6}, {31, 32, 24, 39}, {46, 52, 36, 3}, {70, 80, 1, 27}, {108, 112, 2, 35}, {81, 89, 26, 35}, {66, 72, 15, 23}, {46, 63, 23, 22}, {96, 104, 4, 4}, {23, 27, 17, 12}, {84, 88, 11, 21}, {79, 98, 26, 8}, {38, 52, 1, 5}, {125, 141, 39, 27}, {112, 121, 33, 3}, {103, 109, 8, 16}, {96, 106, 31, 16}, {74, 87, 14, 31}, {96, 113, 2, 28}, {107, 113, 23, 30}, {64, 83, 33, 13}, {79, 92, 6, 29}, {80, 82, 35, 10}, {28, 34, 11, 4}, {123, 137, 40, 30}, {107, 119, 36, 34}, {114, 132, 9, 28}, {100, 114, 35, 22}, {123, 131, 33, 10}, {83, 92, 22, 19}, {29, 34, 15, 14}, {104, 110, 40, 26}, {7, 27, 34, 38}, {29, 39, 35, 1}, {87, 102, 10, 25}, {117, 126, 31, 40}, {62, 71, 4, 25}, {4, 18, 11, 28}, {41, 54, 25, 13}, {41, 60, 40, 31}, {122, 138, 19, 26}, {117, 123, 35, 34}, {10, 21, 33, 1}, {60, 64, 27, 38}, {55, 58, 34, 39}, {51, 56, 36, 12}, {101, 119, 13, 14}, {90, 91, 18, 12}, {74, 87, 3, 24}, {45, 59, 8, 19}, {60, 74, 24, 33}, {84, 87, 11, 38}, {107, 116, 16, 20}, {58, 70, 12, 34}, {99, 103, 26, 26}, {62, 73, 32, 33}, {68, 81, 7, 12}, {106, 122, 37, 27}, {109, 110, 11, 32}, {89, 94, 21, 17}, {98, 108, 3, 40}, {77, 94, 24, 34}, {17, 36, 34, 30}, {65, 70, 8, 7}, {31, 38, 1, 36}, {28, 43, 15, 5}, {107, 111, 13, 8}, {31, 49, 18, 35}, {50, 55, 3, 24}, {4, 16, 24, 19}, {35, 40, 40, 30}, {117, 130, 35, 20}, {33, 38, 9, 37}, {3, 5, 40, 38}, {18, 35, 30, 15}, {65, 70, 40, 16}, {121, 136, 13, 6}, {3, 21, 3, 20}, {41, 54, 14, 37}, {51, 67, 4, 1}, {80, 82, 8, 28}, {12, 21, 35, 30}, {75, 91, 19, 14}, {96, 99, 31, 13},
            {45, 46, 35, 30}, {39, 57, 11, 17}, {104, 109, 7, 39}, {126, 135, 18, 25}, {55, 74, 16, 39}, {21, 37, 29, 24}, {22, 29, 36, 3}, {38, 41, 14, 34}, {52, 59, 35, 27}, {110, 125, 20, 28}, {33, 48, 38, 1}, {23, 34, 13, 38}, {55, 60, 33, 23}, {71, 76, 16, 1}, {22, 28, 16, 22}, {43, 50, 35, 16}, {91, 96, 40, 21}, {100, 119, 39, 22}, {64, 66, 29, 32}, {95, 98, 21, 30}, {61, 77, 12, 7}, {46, 52, 3, 13}, {108, 121, 35, 14}, {9, 15, 25, 9}, {41, 60, 24, 20}, {4, 23, 5, 29}, {29, 31, 2, 10}, {58, 62, 38, 6}, {40, 60, 8, 29}, {41, 54, 5, 25}, {99, 108, 5, 33}, {56, 57, 11, 18}, {94, 95, 9, 28}, {34, 37, 13, 40}, {128, 140, 39, 35}, {101, 110, 5, 4}, {41, 45, 31, 11}, {58, 72, 9, 24}, {48, 54, 24, 35}, {10, 21, 6, 12}, {38, 58, 11, 4}, {2, 22, 18, 9}, {6, 15, 22, 7}, {74, 94, 18, 14}, {75, 84, 29, 29}, {20, 40, 32, 39}, {8, 22, 20, 12}, {116, 136, 32, 40}, {20, 26, 8, 24}, {96, 99, 15, 33}, {26, 34, 35, 27}, {25, 42, 36, 8}, {89, 109, 38, 20}, {43, 54, 30, 21}, {48, 57, 36, 31}, {83, 86, 1, 35}, {65, 74, 4, 21}, {17, 27, 4, 38}, {1, 13, 25, 18}, {75, 93, 28, 32}, {40, 41, 40, 40}, {6, 11, 6, 34}, {2, 8, 4, 23}, {82, 98, 34, 18}, {81, 94, 30, 8}, {33, 38, 39, 31}, {89, 101, 5, 16}, {22, 32, 22, 27}, {124, 139, 20, 7}, {75, 84, 8, 18}, {26, 37, 8, 4}, {90, 92, 32, 25}, {69, 71, 31, 34}, {90, 105, 30, 38}, {8, 28, 35, 26}, {112, 115, 29, 21}, {103, 121, 29, 28}, {52, 60, 15, 31}, {86, 92, 8, 2}, {79, 88, 39, 3}, {124, 127, 39, 16}, {5, 20, 25, 13}, {38, 57, 25, 39}, {56, 71, 8, 10}, {49, 60, 6, 1}, {49, 52, 35, 16}, {64, 75, 36, 39}, {4, 16, 34, 13}, {125, 129, 8, 19}, {99, 119, 29, 28}, {91, 92, 13, 3}, {127, 135, 13, 19}, {49, 62, 12, 19}, {10, 26, 25, 2}, {119, 120, 35, 12}, {111, 122, 22, 11}, {51, 67, 23, 15}, {66, 69, 9, 21}, {63, 71, 11, 38}, {28, 33, 31, 2},
            {66, 84, 12, 11}, {29, 44, 12, 13}, {67, 87, 17, 1}, {82, 98, 7, 12}, {15, 16, 5, 31}, {10, 19, 29, 8}, {121, 123, 9, 15}, {42, 58, 4, 30}, {59, 66, 8, 33}, {75, 88, 39, 32}, {39, 51, 40, 27}, {19, 21, 25, 2}, {18, 30, 3, 38}, {0, 4, 27, 29}, {76, 77, 30, 9}, {42, 46, 1, 19}, {116, 133, 1, 18}, {119, 125, 16, 2}, {78, 93, 33, 2}, {111, 114, 9, 14}, {3, 21, 6, 22}, {26, 37, 21, 16}, {10, 23, 34, 6}, {52, 71, 24, 21}, {122, 140, 25, 38}, {107, 127, 1, 32}, {118, 129, 28, 8}, {52, 61, 7, 16}, {61, 65, 30, 5}, {57, 68, 39, 15}, {56, 75, 35, 18}, {25, 35, 1, 12}, {114, 116, 39, 15}, {117, 120, 24, 36}, {106, 121, 24, 16}, {113, 117, 27, 8}, {122, 133, 20, 1}, {53, 70, 18, 19}, {93, 98, 2, 6}, {71, 77, 37, 21}, {17, 35, 38, 14}, {84, 104, 38, 39}, {78, 84, 9, 36}, {36, 44, 7, 12}, {5, 9, 23, 27}, {29, 42, 37, 24}, {70, 84, 1, 22}, {81, 86, 8, 35}, {90, 92, 4, 9}, {62, 81, 12, 33}, {119, 132, 27, 11}, {113, 118, 32, 37}, {33, 37, 10, 29}, {117, 133, 5, 8}, {70, 88, 37, 40}, {48, 59, 32, 27}, {34, 37, 2, 38}, {116, 117, 19, 19}, {58, 62, 13, 21}, {34, 36, 34, 20}, {116, 118, 39, 10}, {38, 58, 17, 17}, {55, 69, 22, 37}, {119, 137, 29, 21}, {8, 22, 39, 16}, {85, 88, 4, 29}, {94, 95, 14, 36}, {119, 136, 16, 27}, {93, 103, 23, 16}, {44, 64, 22, 19}, {110, 112, 21, 29}, {15, 19, 4, 19}, {6, 20, 29, 10}, {3, 6, 9, 20}, {50, 68, 34, 35}, {13, 20, 28, 24}, {125, 132, 18, 20}, {112, 127, 2, 39}, {61, 74, 36, 29}, {123, 133, 39, 26}, {70, 87, 31, 30}, {80, 85, 40, 6}, {22, 35, 5, 23}, {92, 107, 14, 27}, {86, 104, 26, 24}, {79, 90, 4, 20}, {87, 88, 1, 27}, {19, 36, 8, 32}, {19, 26, 11, 1}, {95, 104, 14, 35}, {63, 66, 8, 5}, {122, 131, 7, 21}, {8, 19, 26, 7}, {1, 21, 18, 2}, {21, 38, 23, 26}, {81, 88, 7, 31}, {21, 32, 20, 25}, {114, 123, 11, 27}, {93, 112, 12, 31}, {37, 51, 11, 1},
            {117, 134, 21, 36}, {3, 10, 39, 40}, {104, 122, 9, 40}, {86, 104, 9, 37}, {20, 27, 20, 9}, {47, 62, 25, 22}, {95, 102, 28, 20}, {42, 47, 21, 28}, {12, 18, 24, 27}, {65, 74, 2, 8}, {18, 26, 28, 18}, {65, 85, 21, 14}, {34, 50, 35, 35}, {92, 107, 26, 14}, {19, 27, 34, 24}, {82, 89, 2, 38}, {93, 95, 4, 6}, {124, 138, 6, 1}, {102, 104, 2, 24}, {58, 68, 33, 2}, {86, 94, 24, 17}, {46, 54, 18, 19}, {74, 91, 33, 28}, {105, 112, 1, 33}, {56, 62, 23, 12}, {57, 70, 38, 36}, {120, 132, 4, 36}, {30, 47, 10, 24}, {32, 51, 28, 29}, {117, 137, 25, 27}, {21, 41, 18, 13}, {45, 54, 22, 39}, {91, 106, 37, 10}, {44, 55, 5, 35}, {77, 94, 20, 6}, {104, 114, 8, 5}, {55, 75, 27, 20}, {4, 9, 11, 23}, {12, 16, 8, 35}, {124, 137, 30, 27}, {74, 94, 16, 32}, {70, 72, 32, 33}, {4, 5, 22, 31}, {8, 23, 12, 33}, {56, 71, 37, 25}, {69, 88, 37, 1}, {82, 101, 37, 14}, {36, 47, 2, 4}, {101, 104, 3, 18}, {4, 8, 13, 35}, {37, 51, 20, 2}, {69, 84, 29, 28}, {9, 17, 9, 26}, {127, 143, 20, 16}, {81, 95, 22, 40}, {77, 84, 4, 15}, {32, 37, 9, 20}, {19, 35, 28, 39}, {89, 94, 15, 40}, {111, 114, 3, 16}, {112, 116, 12, 29}, {77, 95, 16, 17}, {122, 135, 31, 8}, {37, 48, 34, 19}, {35, 40, 18, 4}, {86, 99, 23, 24}, {63, 69, 25, 22}, {41, 61, 31, 4}, {114, 120, 11, 37}, {33, 47, 11, 35}, {51, 52, 30, 5}, {124, 125, 18, 27}, {32, 51, 3, 4}, {120, 129, 25, 29}, {107, 121, 2, 32}, {58, 76, 1, 33}, {128, 134, 8, 24}, {85, 105, 21, 36}, {91, 101, 36, 15}, {34, 50, 1, 15}, {128, 145, 28, 35}, {14, 24, 3, 39}, {102, 115, 37, 19}, {37, 55, 5, 9}, {126, 134, 12, 12}, {13, 22, 37, 39}, {65, 68, 30, 30}, {115, 125, 31, 29}, {58, 61, 7, 11}, {123, 129, 33, 30}, {15, 16, 12, 37}, {57, 68, 21, 34}, {62, 74, 7, 12}, {19, 25, 22, 23}, {60, 61, 32, 38}, {24, 37, 5, 28}, {128, 142, 11, 11}, {3, 18, 40, 13}, {118, 133, 12, 31}, {51, 69, 6, 27},
            {19, 30, 7, 27}, {49, 57, 17, 22}, {39, 53, 3, 39}, {36, 40, 1, 34}, {87, 88, 29, 40}, {97, 110, 8, 36}, {108, 120, 21, 33}, {82, 84, 1, 22}, {9, 23, 12, 25}, {25, 38, 35, 27}, {81, 99, 19, 6}, {98, 109, 38, 26}, {90, 94, 27, 4}, {74, 81, 28, 19}, {128, 130, 27, 30}, {52, 57, 15, 36}, {8, 21, 11, 28}, {115, 120, 18, 10}, {108, 126, 7, 11}, {70, 75, 16, 3}, {20, 22, 7, 1}, {110, 121, 5, 13}, {16, 20, 10, 28}, {14, 15, 27, 26}, {46, 57, 30, 3}, {2, 5, 14, 16}, {18, 21, 33, 3}, {117, 136, 8, 11}, {94, 111, 21, 30}, {41, 61, 30, 2}, {113, 121, 11, 37}, {58, 78, 21, 13}, {25, 32, 31, 20}, {10, 17, 21, 3}, {27, 30, 2, 26}, {1, 17, 12, 40}, {8, 20, 12, 20}, {82, 90, 35, 25}, {15, 27, 40, 9}, {11, 18, 40, 20}, {100, 107, 9, 6}, {6, 10, 2, 1}, {39, 40, 16, 8}, {16, 35, 7, 11}, {76, 80, 31, 14}, {16, 34, 12, 21}, {121, 127, 20, 20}, {66, 73, 36, 5}, {101, 120, 32, 17}, {25, 31, 14, 17}, {91, 99, 21, 5}, {6, 12, 11, 7}, {91, 102, 27, 12}, {82, 102, 24, 8}, {65, 67, 40, 35}, {7, 9, 19, 7}, {17, 31, 7, 20}, {16, 31, 17, 36}, {26, 36, 16, 16}, {104, 112, 13, 27}, {48, 67, 24, 3}, {46, 56, 32, 27}, {69, 89, 21, 20}, {114, 132, 33, 32}, {90, 106, 7, 27}, {78, 85, 5, 39}, {118, 136, 24, 25}, {110, 116, 28, 14}, {53, 70, 30, 34}, {106, 108, 36, 40}, {76, 96, 17, 23}, {21, 30, 10, 6}, {35, 45, 1, 5}, {92, 105, 30, 36}, {99, 110, 21, 32}, {123, 140, 5, 28}, {101, 109, 8, 18}, {91, 110, 29, 9}, {60, 62, 14, 25}, {98, 106, 35, 2}, {9, 26, 3, 9}, {74, 92, 5, 34}, {37, 45, 17, 2}, {37, 51, 2, 33}, {113, 131, 2, 9}, {106, 117, 26, 2}, {114, 131, 27, 32}, {123, 133, 19, 22}, {75, 80, 10, 20}, {84, 90, 33, 33}, {102, 116, 24, 29}, {39, 44, 3, 2}, {11, 13, 7, 2}, {90, 91, 23, 19}, {67, 86, 39, 40}, {122, 135, 37, 5}, {105, 116, 2, 8}, {71, 82, 19, 19}, {58, 65, 13, 22}, {76, 91, 37, 16},
            {27, 28, 13, 16}, {24, 38, 21, 2}, {7, 13, 20, 21}, {2, 12, 15, 29}, {26, 32, 13, 34}, {81, 93, 16, 13}, {42, 56, 15, 14}, {66, 72, 6, 10}, {49, 59, 29, 17}, {51, 70, 23, 29}, {119, 128, 34, 6}, {36, 56, 38, 15}, {10, 23, 17, 11}, {3, 7, 24, 12}, {15, 24, 26, 8}, {74, 76, 17, 10}, {70, 86, 19, 25}, {27, 45, 38, 16}, {107, 119, 31, 18}, {101, 114, 23, 37}, {108, 125, 39, 19}, {1, 14, 27, 26}, {110, 121, 35, 8}, {37, 46, 28, 25}, {89, 94, 17, 8}, {119, 123, 19, 6}, {13, 27, 22, 18}, {32, 44, 40, 19}, {52, 56, 17, 39}, {96, 98, 32, 32}, {46, 60, 14, 24}, {120, 131, 9, 24}, {54, 71, 29, 10}, {19, 31, 37, 22}, {39, 55, 23, 30}, {47, 59, 15, 8}, {40, 49, 20, 23}, {69, 82, 22, 32}, {2, 8, 10, 21}, {79, 81, 33, 28}, {6, 20, 7, 9}, {46, 48, 12, 15}, {54, 57, 11, 1}, {93, 95, 24, 40}, {101, 110, 37, 35}, {14, 29, 8, 14}, {4, 16, 36, 38}, {95, 98, 4, 8}, {104, 107, 6, 5}, {21, 29, 25, 40}, {1, 10, 25, 2}, {57, 59, 24, 2}, {118, 119, 30, 1}, {107, 127, 28, 37}, {127, 141, 5, 38}, {87, 99, 33, 15}, {62, 73, 22, 29}, {41, 50, 16, 18}, {100, 113, 15, 20}, {37, 52, 40, 15}, {17, 35, 13, 16}, {33, 48, 39, 18}, {98, 111, 29, 2}, {59, 78, 33, 12}, {7, 16, 40, 19}, {47, 55, 30, 29}, {35, 46, 40, 8}, {101, 112, 15, 39}, {73, 88, 21, 2}, {56, 76, 4, 10}, {71, 75, 20, 4}, {98, 105, 35, 29}, {5, 24, 30, 20}, {32, 36, 19, 33}, {61, 70, 28, 35}, {101, 109, 11, 29}, {47, 50, 31, 24}, {106, 125, 31, 4}, {36, 55, 24, 34}, {7, 8, 17, 18}, {47, 67, 34, 32}, {11, 12, 29, 2}, {32, 34, 9, 37}, {48, 64, 14, 17}, {108, 114, 26, 31}, {124, 142, 32, 3}, {17, 37, 36, 17}, {22, 40, 18, 6}, {103, 104, 39, 29}, {47, 57, 18, 18}, {85, 92, 6, 21}, {100, 118, 26, 27}, {105, 114, 22, 27}, {104, 107, 14, 30}, {99, 117, 27, 20}, {91, 95, 15, 8}, {21, 26, 39, 25}, {51, 71, 30, 37}, {63, 73, 21, 32}, {128, 145, 5, 18},
            {50, 51, 39, 31}, {35, 53, 27, 23}, {107, 114, 2, 24}, {12, 28, 24, 4}, {5, 20, 34, 39}, {98, 106, 28, 23}, {23, 28, 30, 34}, {69, 87, 10, 38}, {23, 42, 16, 27}, {27, 43, 16, 13}, {59, 77, 27, 33}, {28, 45, 28, 30}, {88, 89, 15, 9}, {103, 106, 14, 23}, {89, 105, 3, 28}, {29, 32, 36, 11}, {29, 43, 32, 20}, {104, 106, 8, 18}, {19, 22, 14, 11}, {99, 114, 25, 40}, {18, 27, 17, 28}, {69, 81, 36, 15}, {15, 30, 6, 12}, {46, 64, 12, 21}, {38, 44, 35, 33}, {36, 44, 31, 5}, {20, 29, 35, 19}, {83, 96, 14, 32}, {127, 135, 18, 2}, {114, 134, 23, 35}, {34, 45, 18, 40}, {122, 131, 19, 21}, {86, 103, 37, 36}, {94, 110, 15, 26}, {35, 54, 33, 24}, {91, 102, 12, 31}, {26, 42, 27, 1}, {9, 29, 35, 30}, {115, 130, 36, 4}, {44, 55, 32, 38}, {62, 67, 27, 38}, {65, 83, 18, 2}, {6, 13, 33, 32}, {46, 55, 33, 11}, {30, 46, 1, 19}, {76, 87, 30, 27}, {41, 59, 14, 25}, {49, 51, 39, 19}, {122, 140, 3, 3}, {105, 108, 33, 40}, {106, 121, 32, 19}, {76, 96, 16, 11}, {41, 51, 27, 6}, {81, 101, 10, 30}, {121, 126, 19, 24}, {113, 125, 28, 25}, {47, 50, 38, 13}, {105, 123, 10, 27}, {85, 100, 1, 36}, {56, 72, 37, 25}, {69, 83, 24, 4}, {106, 124, 33, 27}, {99, 117, 30, 30}, {41, 57, 27, 31}, {80, 87, 7, 13}, {43, 44, 13, 37}, {33, 37, 6, 25}, {16, 24, 27, 20}, {126, 140, 4, 34}, {19, 38, 8, 19}, {96, 100, 14, 15}, {48, 54, 13, 26}, {56, 73, 21, 14}, {92, 108, 25, 3}, {14, 17, 22, 4}, {76, 77, 30, 16}, {30, 49, 32, 8}, {37, 54, 4, 24}, {6, 21, 37, 10}, {22, 26, 2, 23}, {81, 91, 10, 9}, {92, 95, 34, 17}, {25, 37, 12, 26}, {2, 4, 34, 23}, {7, 25, 8, 6}, {69, 86, 10, 10}, {112, 122, 4, 28}, {103, 110, 21, 23}, {120, 123, 2, 37}, {51, 54, 7, 14}, {29, 41, 37, 20}, {125, 143, 31, 28}, {31, 36, 28, 38}, {90, 105, 12, 2}, {14, 16, 30, 37}, {124, 128, 36, 22}, {45, 60, 12, 21}, {67, 83, 27, 17}, {100, 112, 37, 30},
    };
    // *INDENT-ON*

    for(i = 0; i < 1000; i++) {
        uint8_t from = ranges[i][0];
        uint8_t to = ranges[i][1];
        read_range(&f, from, to);

        from = to + 1;
        uint8_t next = ranges[i][2];
        read_next(&f, from, next);

        from += next;
        next = ranges[i][3];
        read_next(&f, from, next);
    }

    res = lv_fs_close(&f);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);

    drv->cache_size = original_cache_size;
}

void test_write_read_random(void)
{
    lv_fs_drv_t * drv = lv_fs_get_drv('A');
    uint32_t original_cache_size = drv->cache_size;
    drv->cache_size = 7;

    /* create the file and reopen for read+write. stdio "rb+" mode requires the file to exist */
    lv_fs_res_t res;
    lv_fs_file_t f;
    res = lv_fs_open(&f, "A:fs_write_read_random.bin", LV_FS_MODE_WR);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);
    res = lv_fs_close(&f);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);
    res = lv_fs_open(&f, "A:fs_write_read_random.bin", LV_FS_MODE_WR | LV_FS_MODE_RD);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);

    uint8_t buf1000[1000];
    for(uint32_t i = 0; i < 1000; i++) buf1000[i] = i;
    res = lv_fs_write(&f, buf1000, 1000, NULL);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);

    /**
     * {{{action, amount}, ... x20 actions per combo}, ... x100 combos}
     * actions are read (0), write (1), seek (2)
     * read and write amount ranges from 1 to 20. seek amount ranges from -20 to 20.
     * seeks occur as frequently as reads+writes combined.
     */
    // *INDENT-OFF*
    static const int8_t actions[100][20][2] = {
        {{2, -20}, {2, 17}, {0, 20}, {2, 19}, {1, 18}, {1, 4}, {1, 13}, {2, -7}, {1, 6}, {1, 19}, {2, 3}, {1, 12}, {2, 12}, {2, -2}, {2, -20}, {2, -17}, {1, 15}, {2, -17}, {0, 18}, {2, 14}}, {{2, 19}, {2, 17}, {2, -12}, {2, -5}, {2, -1}, {2, -17}, {1, 4}, {2, 19}, {2, 3}, {2, 1}, {2, 10}, {2, 18}, {0, 10}, {2, 8}, {2, 17}, {2, 20}, {0, 5}, {0, 14}, {0, 6}, {1, 8}}, {{0, 1}, {2, -20}, {2, -12}, {1, 9}, {0, 9}, {2, 19}, {1, 8}, {0, 13}, {0, 5}, {2, -7}, {2, -14}, {0, 8}, {2, 13}, {1, 5}, {0, 3}, {0, 16}, {2, 19}, {0, 4}, {1, 6}, {2, -4}}, {{0, 1}, {2, 12}, {1, 7}, {1, 17}, {1, 19}, {0, 8}, {1, 1}, {2, 9}, {2, -14}, {2, 14}, {2, -17}, {2, 11}, {1, 4}, {2, 19}, {2, -18}, {1, 12}, {2, -14}, {0, 9}, {1, 7}, {2, 17}}, {{2, -6}, {0, 13}, {1, 15}, {1, 8}, {2, -1}, {2, 15}, {2, 3}, {2, -16}, {2, -11}, {2, 14}, {2, -19}, {2, 6}, {1, 10}, {1, 6}, {1, 12}, {2, 12}, {0, 10}, {2, 10}, {2, -4}, {2, 5}}, {{2, -6}, {1, 2}, {1, 11}, {1, 19}, {2, 2}, {2, 20}, {2, -19}, {2, -7}, {0, 13}, {2, 4}, {1, 6}, {0, 9}, {0, 6}, {2, -1}, {2, -5}, {2, 14}, {2, 15}, {0, 4}, {0, 6}, {0, 10}}, {{1, 17}, {1, 17}, {0, 15}, {0, 3}, {1, 8}, {2, -9}, {1, 13}, {0, 20}, {0, 3}, {1, 17}, {2, -8}, {2, -1}, {1, 13}, {0, 13}, {0, 8}, {1, 1}, {2, -4}, {2, 13}, {2, 17}, {2, -14}}, {{1, 17}, {1, 13}, {1, 17}, {1, 5}, {1, 3}, {0, 20}, {2, 12}, {0, 13}, {2, 5}, {2, -10}, {2, 10}, {0, 6}, {2, 18}, {0, 17}, {2, 2}, {1, 5}, {2, 16}, {2, -10}, {0, 2}, {2, -9}}, {{2, 7}, {2, -2}, {1, 14}, {0, 15}, {1, 2}, {2, 6}, {2, 11}, {1, 2}, {0, 12}, {2, 9}, {0, 17}, {0, 15}, {2, -19}, {0, 11}, {2, 3}, {2, 15}, {2, 19}, {2, -16}, {1, 6}, {1, 15}}, {{2, 12}, {1, 2}, {2, -4}, {2, -20}, {0, 18}, {2, -16}, {1, 15}, {2, -9}, {2, -7}, {1, 1}, {0, 7}, {0, 18}, {1, 18}, {0, 2}, {2, 17}, {1, 2}, {1, 8}, {1, 18}, {2, -7}, {0, 2}},
        {{2, 19}, {2, 17}, {2, -7}, {0, 17}, {0, 18}, {1, 4}, {1, 1}, {0, 3}, {2, 4}, {2, -4}, {1, 20}, {0, 1}, {2, 20}, {0, 6}, {2, 10}, {0, 19}, {2, -1}, {0, 7}, {0, 16}, {1, 13}}, {{2, 10}, {2, 13}, {2, 15}, {2, 3}, {2, 12}, {0, 14}, {2, 6}, {2, 14}, {2, -13}, {2, 18}, {0, 19}, {2, -8}, {2, -9}, {0, 12}, {2, -20}, {2, -6}, {2, 15}, {2, 18}, {1, 18}, {1, 10}}, {{0, 15}, {2, -6}, {2, 17}, {1, 7}, {0, 9}, {0, 11}, {0, 12}, {0, 20}, {2, -1}, {2, -8}, {0, 13}, {2, 19}, {1, 3}, {0, 18}, {1, 13}, {2, 12}, {2, 13}, {1, 5}, {2, -14}, {2, 18}}, {{0, 6}, {1, 4}, {1, 6}, {0, 2}, {2, 8}, {1, 9}, {2, 20}, {0, 9}, {2, -1}, {2, 5}, {0, 18}, {2, 8}, {2, 12}, {2, -14}, {0, 18}, {1, 1}, {2, -15}, {2, 14}, {2, -11}, {2, -2}}, {{1, 4}, {2, 6}, {2, 18}, {1, 19}, {0, 18}, {0, 19}, {0, 14}, {0, 12}, {2, 3}, {0, 19}, {2, -20}, {0, 8}, {2, -15}, {2, -15}, {0, 8}, {1, 17}, {2, 2}, {1, 14}, {0, 11}, {0, 13}}, {{2, -10}, {2, -8}, {1, 18}, {1, 11}, {1, 5}, {0, 2}, {2, -19}, {2, -9}, {1, 5}, {2, -1}, {2, 2}, {1, 5}, {0, 9}, {0, 17}, {2, 2}, {0, 10}, {1, 12}, {1, 10}, {0, 8}, {2, -13}}, {{0, 13}, {1, 5}, {2, -1}, {2, -16}, {2, 2}, {0, 6}, {2, -11}, {0, 5}, {2, 5}, {2, -6}, {2, -6}, {1, 19}, {0, 8}, {2, -16}, {2, 14}, {0, 11}, {0, 14}, {0, 18}, {0, 14}, {2, 1}}, {{2, 3}, {2, -2}, {0, 10}, {1, 1}, {0, 10}, {2, -15}, {2, 19}, {2, 2}, {1, 15}, {1, 10}, {2, -18}, {0, 20}, {0, 10}, {2, 3}, {1, 13}, {0, 8}, {0, 16}, {0, 14}, {2, 13}, {2, -7}}, {{0, 4}, {1, 7}, {0, 2}, {0, 15}, {0, 20}, {1, 9}, {0, 14}, {2, 18}, {0, 1}, {0, 4}, {2, -17}, {1, 10}, {1, 15}, {0, 2}, {2, -6}, {2, 4}, {2, 4}, {2, 17}, {2, -13}, {1, 20}}, {{1, 8}, {0, 20}, {2, 4}, {1, 6}, {2, -19}, {1, 4}, {0, 5}, {1, 8}, {1, 17}, {2, -4}, {2, 14}, {1, 10}, {2, 7}, {1, 18}, {2, 17}, {1, 6}, {0, 20}, {2, 1}, {2, -12}, {2, 18}},
        {{2, -7}, {1, 12}, {2, 4}, {2, 14}, {0, 13}, {1, 17}, {2, -2}, {2, -9}, {2, -13}, {1, 4}, {2, 4}, {1, 5}, {0, 15}, {2, -11}, {2, 3}, {2, 1}, {2, -11}, {2, 11}, {2, -11}, {0, 4}}, {{1, 7}, {2, 20}, {0, 6}, {0, 9}, {1, 2}, {0, 11}, {2, 4}, {2, -19}, {0, 17}, {2, -7}, {0, 20}, {0, 10}, {1, 11}, {1, 20}, {1, 6}, {2, 14}, {0, 11}, {0, 19}, {2, 4}, {1, 8}}, {{0, 9}, {2, -3}, {1, 17}, {2, 14}, {2, 3}, {1, 19}, {2, 17}, {0, 18}, {2, 16}, {0, 9}, {2, -15}, {1, 14}, {2, 11}, {2, -7}, {0, 17}, {2, 12}, {2, 6}, {2, 9}, {0, 7}, {1, 3}}, {{0, 1}, {2, 13}, {1, 19}, {0, 19}, {2, 6}, {2, -14}, {2, 8}, {2, -18}, {2, 4}, {2, -7}, {0, 12}, {2, 15}, {1, 3}, {2, -19}, {2, 15}, {2, -2}, {1, 19}, {1, 18}, {1, 19}, {0, 16}}, {{2, 7}, {2, 14}, {2, 14}, {2, -19}, {0, 1}, {2, -5}, {0, 17}, {2, 2}, {2, 15}, {1, 13}, {0, 20}, {0, 12}, {2, 20}, {0, 9}, {2, -8}, {0, 14}, {1, 19}, {2, -15}, {2, 17}, {2, -12}}, {{0, 16}, {2, -7}, {2, -4}, {1, 11}, {1, 17}, {2, -14}, {0, 16}, {2, 6}, {1, 19}, {2, 17}, {2, 10}, {2, -12}, {2, -17}, {1, 4}, {2, -14}, {2, -7}, {2, -17}, {2, -1}, {2, -16}, {1, 8}}, {{0, 5}, {0, 15}, {2, 4}, {2, 20}, {2, 7}, {2, 3}, {2, 17}, {0, 8}, {0, 18}, {2, -18}, {1, 1}, {0, 15}, {2, -4}, {0, 13}, {1, 11}, {0, 4}, {2, 11}, {1, 11}, {1, 10}, {2, -9}}, {{2, -5}, {2, -16}, {1, 2}, {1, 17}, {2, 9}, {2, -2}, {0, 7}, {0, 14}, {2, -10}, {0, 6}, {1, 14}, {1, 15}, {2, 12}, {2, 11}, {2, -9}, {2, -10}, {2, -14}, {2, 9}, {0, 13}, {2, 5}}, {{2, 8}, {2, -16}, {2, 20}, {1, 8}, {1, 2}, {2, -5}, {2, -20}, {1, 2}, {1, 4}, {1, 9}, {0, 19}, {2, -18}, {0, 6}, {0, 13}, {1, 5}, {2, 2}, {0, 4}, {1, 19}, {2, 15}, {1, 12}}, {{2, -8}, {0, 16}, {1, 10}, {2, 5}, {2, -16}, {0, 3}, {2, 5}, {0, 6}, {1, 17}, {2, 1}, {2, 13}, {0, 3}, {0, 6}, {0, 6}, {0, 19}, {1, 13}, {2, 19}, {2, 5}, {1, 16}, {2, 5}},
        {{2, -19}, {2, -1}, {0, 19}, {2, -3}, {2, 13}, {2, -12}, {0, 2}, {2, -20}, {2, 15}, {2, -9}, {2, -2}, {2, 13}, {2, -6}, {0, 2}, {1, 6}, {2, -1}, {0, 12}, {0, 20}, {2, -14}, {1, 2}}, {{1, 9}, {2, -14}, {2, -2}, {2, -13}, {0, 3}, {1, 6}, {2, 20}, {2, 11}, {2, 17}, {2, 5}, {0, 5}, {1, 1}, {2, -9}, {1, 8}, {2, -2}, {0, 18}, {2, -8}, {1, 11}, {2, 6}, {1, 7}}, {{2, 17}, {2, 14}, {2, 16}, {1, 20}, {2, -1}, {2, -7}, {1, 3}, {1, 14}, {0, 1}, {0, 18}, {2, -14}, {0, 10}, {0, 18}, {2, 19}, {1, 13}, {2, -16}, {1, 17}, {1, 1}, {2, -13}, {0, 13}}, {{2, -2}, {0, 14}, {0, 12}, {2, -8}, {1, 16}, {0, 18}, {2, 5}, {2, 13}, {1, 14}, {2, -4}, {2, 16}, {2, -16}, {2, 16}, {1, 18}, {2, 15}, {2, 4}, {2, -5}, {1, 7}, {2, -20}, {2, -9}}, {{2, 18}, {2, 2}, {2, 5}, {0, 4}, {2, 17}, {2, -15}, {2, 8}, {2, -11}, {1, 8}, {2, -6}, {2, -13}, {0, 5}, {0, 18}, {0, 15}, {2, -8}, {2, -15}, {1, 1}, {2, 4}, {2, -9}, {2, 14}}, {{2, -1}, {2, 1}, {1, 12}, {0, 9}, {2, -11}, {1, 4}, {1, 11}, {1, 5}, {0, 12}, {2, -7}, {1, 1}, {0, 6}, {0, 15}, {2, -10}, {2, -18}, {2, -12}, {0, 3}, {2, 2}, {2, -17}, {2, 1}}, {{2, -5}, {2, 18}, {1, 2}, {2, 14}, {2, 20}, {0, 13}, {0, 11}, {0, 6}, {2, -7}, {2, 13}, {1, 13}, {2, -1}, {1, 12}, {2, -9}, {2, 3}, {1, 6}, {2, -16}, {0, 6}, {0, 9}, {2, -14}}, {{1, 6}, {2, 7}, {2, -13}, {0, 5}, {1, 4}, {0, 15}, {2, 18}, {2, -4}, {2, 16}, {1, 1}, {2, 17}, {0, 2}, {1, 12}, {0, 17}, {1, 18}, {1, 5}, {1, 8}, {1, 4}, {2, -9}, {1, 9}}, {{0, 20}, {1, 10}, {2, 20}, {2, 13}, {0, 18}, {0, 10}, {1, 3}, {0, 8}, {2, -16}, {2, -16}, {2, -2}, {0, 20}, {2, -19}, {2, -11}, {1, 17}, {1, 18}, {1, 5}, {0, 6}, {2, -9}, {1, 8}}, {{0, 18}, {0, 19}, {2, 16}, {2, -14}, {1, 7}, {1, 9}, {0, 16}, {0, 16}, {1, 17}, {0, 14}, {2, -16}, {1, 6}, {1, 11}, {2, 7}, {1, 19}, {1, 11}, {2, -14}, {0, 7}, {1, 12}, {0, 2}},
        {{2, 3}, {2, -15}, {0, 13}, {2, -3}, {0, 16}, {1, 18}, {0, 11}, {0, 16}, {1, 13}, {2, -18}, {1, 14}, {2, 11}, {0, 11}, {2, -14}, {2, -19}, {0, 2}, {2, -7}, {0, 5}, {0, 20}, {2, -7}}, {{2, 9}, {2, 5}, {2, 7}, {0, 15}, {2, 4}, {0, 5}, {2, -17}, {2, 16}, {2, 8}, {0, 11}, {0, 1}, {1, 8}, {2, -5}, {1, 1}, {2, 4}, {2, -4}, {2, -17}, {0, 5}, {1, 10}, {0, 5}}, {{0, 11}, {1, 16}, {1, 18}, {0, 5}, {2, -13}, {0, 5}, {1, 13}, {1, 10}, {1, 11}, {0, 5}, {1, 9}, {1, 4}, {1, 19}, {2, 1}, {1, 19}, {1, 18}, {1, 8}, {2, 2}, {2, -5}, {1, 4}}, {{0, 12}, {1, 16}, {2, -1}, {1, 10}, {1, 10}, {2, -19}, {2, -20}, {1, 17}, {1, 19}, {1, 4}, {2, 12}, {0, 1}, {2, -15}, {2, -1}, {2, -10}, {2, -19}, {0, 18}, {0, 6}, {2, 18}, {0, 18}}, {{1, 10}, {0, 13}, {2, 3}, {0, 20}, {0, 19}, {0, 5}, {0, 9}, {0, 5}, {0, 20}, {0, 9}, {2, 16}, {1, 11}, {0, 12}, {2, -17}, {0, 20}, {0, 14}, {1, 17}, {2, 15}, {1, 7}, {2, -1}}, {{2, -10}, {2, 1}, {0, 2}, {2, -11}, {2, -17}, {2, -10}, {1, 9}, {2, -6}, {2, -19}, {1, 14}, {0, 4}, {1, 6}, {2, -9}, {2, 3}, {1, 2}, {2, 19}, {1, 15}, {0, 7}, {0, 4}, {2, 2}}, {{1, 12}, {2, -8}, {2, 13}, {2, -19}, {0, 17}, {0, 20}, {1, 3}, {1, 15}, {1, 20}, {2, 17}, {0, 12}, {2, -2}, {1, 8}, {2, -12}, {0, 11}, {2, 17}, {1, 14}, {1, 13}, {2, 2}, {2, 3}}, {{0, 13}, {1, 13}, {2, -4}, {1, 17}, {1, 1}, {1, 5}, {0, 10}, {1, 12}, {0, 9}, {2, -16}, {0, 13}, {2, 19}, {2, 9}, {1, 18}, {2, -1}, {2, -14}, {0, 5}, {1, 2}, {0, 20}, {2, -19}}, {{1, 7}, {2, 19}, {2, 11}, {2, -16}, {1, 19}, {2, -14}, {0, 7}, {1, 10}, {2, 8}, {0, 12}, {1, 12}, {2, -11}, {2, 6}, {2, -16}, {2, 15}, {1, 20}, {2, -12}, {1, 7}, {2, -16}, {2, 7}}, {{0, 8}, {2, 19}, {1, 12}, {2, -12}, {2, 4}, {1, 10}, {1, 13}, {2, 2}, {2, -14}, {2, -10}, {2, 18}, {1, 16}, {0, 10}, {2, 19}, {2, -1}, {1, 17}, {2, -1}, {0, 14}, {1, 11}, {0, 18}},
        {{2, 2}, {2, -2}, {2, 12}, {0, 7}, {0, 14}, {2, -15}, {2, 13}, {2, -3}, {0, 1}, {2, -2}, {1, 2}, {2, 4}, {2, 4}, {2, 16}, {1, 4}, {2, 1}, {1, 9}, {1, 6}, {1, 12}, {1, 11}}, {{2, -10}, {1, 16}, {1, 12}, {0, 18}, {2, -13}, {0, 19}, {2, 15}, {0, 11}, {0, 2}, {0, 4}, {0, 7}, {0, 10}, {2, -10}, {2, 11}, {0, 13}, {0, 10}, {2, -14}, {2, -18}, {0, 7}, {2, 16}}, {{2, -19}, {2, -5}, {1, 9}, {0, 15}, {2, 4}, {2, -14}, {1, 13}, {2, 10}, {2, -7}, {2, 14}, {2, 8}, {2, 9}, {0, 14}, {2, -4}, {1, 2}, {1, 19}, {0, 18}, {2, 18}, {1, 6}, {2, -6}}, {{2, -15}, {2, 1}, {0, 8}, {1, 5}, {2, -11}, {2, -3}, {0, 19}, {0, 4}, {1, 19}, {1, 2}, {1, 2}, {0, 3}, {0, 16}, {2, -3}, {2, 18}, {1, 20}, {2, -16}, {2, -2}, {2, -17}, {1, 15}}, {{1, 19}, {2, -11}, {1, 13}, {0, 14}, {1, 18}, {2, 11}, {2, 13}, {2, -14}, {1, 5}, {2, -6}, {0, 4}, {2, 19}, {0, 12}, {1, 4}, {2, -6}, {2, 8}, {2, 9}, {2, -10}, {2, -3}, {2, -19}}, {{2, 7}, {2, -9}, {2, 5}, {2, 12}, {2, -20}, {2, -3}, {2, -12}, {2, 5}, {0, 6}, {2, 15}, {1, 8}, {1, 9}, {2, 18}, {2, -14}, {2, 10}, {1, 5}, {1, 2}, {0, 14}, {2, 14}, {0, 19}}, {{2, 12}, {0, 9}, {0, 14}, {1, 4}, {2, 4}, {2, -8}, {2, 4}, {0, 13}, {1, 10}, {2, 18}, {2, 13}, {2, 15}, {1, 18}, {2, 7}, {2, -18}, {2, 18}, {1, 9}, {2, -9}, {0, 16}, {2, -14}}, {{0, 16}, {2, 9}, {2, 7}, {1, 6}, {0, 14}, {2, -18}, {1, 14}, {1, 2}, {0, 10}, {1, 6}, {0, 10}, {2, 2}, {1, 1}, {0, 19}, {1, 6}, {2, -3}, {2, 3}, {2, 12}, {2, 5}, {1, 18}}, {{2, -17}, {0, 17}, {0, 5}, {2, 18}, {2, 8}, {0, 14}, {0, 10}, {1, 20}, {2, 7}, {1, 10}, {1, 14}, {1, 10}, {0, 11}, {1, 15}, {0, 16}, {1, 14}, {0, 3}, {2, 16}, {2, -5}, {2, -4}}, {{1, 15}, {2, -9}, {0, 11}, {0, 16}, {2, 1}, {0, 12}, {0, 7}, {0, 16}, {2, 14}, {0, 3}, {1, 11}, {2, 4}, {1, 6}, {2, -1}, {1, 20}, {1, 13}, {0, 2}, {2, -12}, {0, 9}, {2, 8}},
        {{0, 1}, {2, -4}, {0, 19}, {2, -15}, {2, -16}, {2, -10}, {2, 7}, {0, 10}, {2, -6}, {2, -8}, {2, -3}, {2, 18}, {1, 20}, {2, 10}, {1, 18}, {2, -3}, {0, 7}, {2, 3}, {2, 14}, {2, 15}}, {{1, 17}, {0, 7}, {1, 17}, {2, -8}, {2, -12}, {2, -9}, {2, 15}, {0, 10}, {1, 10}, {2, -6}, {2, -20}, {1, 17}, {2, -9}, {1, 15}, {1, 3}, {1, 8}, {0, 1}, {1, 13}, {2, -5}, {0, 14}}, {{2, 8}, {0, 11}, {0, 12}, {2, 5}, {2, -4}, {2, -5}, {2, -2}, {0, 20}, {2, -4}, {1, 1}, {1, 2}, {0, 4}, {2, 14}, {1, 17}, {2, -13}, {1, 13}, {2, 12}, {2, 20}, {2, 14}, {2, -9}}, {{2, -20}, {2, 13}, {1, 20}, {2, 20}, {2, 9}, {0, 16}, {2, 1}, {0, 13}, {2, 17}, {2, 8}, {2, 13}, {2, -3}, {2, -1}, {2, -19}, {2, 1}, {1, 16}, {2, -16}, {0, 6}, {2, 2}, {1, 8}}, {{1, 4}, {1, 9}, {1, 8}, {2, -12}, {0, 4}, {1, 8}, {0, 9}, {2, -2}, {2, -19}, {2, 2}, {2, -1}, {0, 14}, {2, 4}, {2, -17}, {0, 1}, {2, 4}, {2, -20}, {2, 3}, {0, 7}, {1, 4}}, {{0, 6}, {2, -5}, {1, 13}, {2, 15}, {2, 11}, {0, 7}, {1, 17}, {2, 9}, {1, 14}, {2, -7}, {2, 12}, {2, -18}, {0, 19}, {2, -1}, {2, 14}, {2, 5}, {2, -16}, {1, 2}, {2, -2}, {0, 17}}, {{1, 11}, {2, 4}, {2, 7}, {1, 16}, {2, 14}, {0, 14}, {2, 16}, {1, 11}, {1, 10}, {2, -3}, {2, -10}, {1, 7}, {2, 9}, {0, 19}, {2, 1}, {0, 7}, {1, 11}, {0, 9}, {2, -16}, {1, 8}}, {{0, 10}, {0, 4}, {2, -8}, {2, 20}, {2, 16}, {2, 5}, {0, 16}, {1, 6}, {2, 2}, {2, 14}, {2, 11}, {1, 15}, {2, -10}, {2, -5}, {0, 4}, {0, 4}, {2, -13}, {2, 11}, {0, 13}, {0, 11}}, {{0, 6}, {1, 4}, {2, 15}, {2, 17}, {1, 1}, {2, -20}, {1, 18}, {0, 19}, {2, -12}, {2, 1}, {2, -15}, {2, -11}, {1, 19}, {0, 13}, {1, 2}, {0, 17}, {2, 14}, {0, 14}, {2, 12}, {0, 19}}, {{2, -16}, {1, 20}, {2, -14}, {1, 7}, {2, -13}, {2, 19}, {2, 20}, {2, -14}, {1, 17}, {2, 20}, {1, 2}, {2, -19}, {2, -17}, {2, 18}, {2, 6}, {1, 17}, {2, 7}, {0, 8}, {1, 20}, {2, 9}},
        {{2, 20}, {2, 17}, {2, 15}, {1, 13}, {0, 7}, {2, -6}, {0, 8}, {2, -17}, {1, 15}, {2, -20}, {0, 17}, {2, -17}, {0, 12}, {1, 14}, {2, -8}, {2, -17}, {0, 12}, {2, -3}, {2, -13}, {0, 7}}, {{0, 3}, {2, 12}, {2, 4}, {0, 13}, {0, 15}, {1, 4}, {2, 7}, {2, -20}, {0, 4}, {0, 6}, {0, 12}, {2, 10}, {2, -13}, {0, 20}, {2, -11}, {0, 16}, {2, -13}, {2, -5}, {1, 14}, {0, 7}}, {{2, 16}, {1, 11}, {2, -1}, {0, 9}, {0, 19}, {2, 9}, {2, 6}, {1, 15}, {2, 2}, {2, 8}, {1, 3}, {2, 20}, {2, -18}, {2, -15}, {2, -20}, {0, 10}, {1, 9}, {2, 5}, {2, -17}, {2, 14}}, {{2, -15}, {2, -10}, {2, -18}, {1, 20}, {1, 4}, {0, 18}, {1, 10}, {2, 19}, {2, -9}, {2, 20}, {0, 1}, {0, 11}, {2, -18}, {2, 5}, {1, 15}, {0, 10}, {2, -14}, {2, 6}, {0, 17}, {0, 3}}, {{1, 18}, {1, 5}, {0, 11}, {2, 11}, {0, 13}, {1, 5}, {0, 19}, {2, 20}, {1, 10}, {2, 19}, {0, 13}, {0, 7}, {2, 1}, {2, 14}, {1, 19}, {0, 9}, {1, 17}, {2, 18}, {0, 11}, {1, 9}}, {{2, -17}, {2, 10}, {2, -19}, {2, -18}, {2, 6}, {0, 9}, {0, 20}, {1, 7}, {2, -14}, {2, -11}, {0, 14}, {1, 1}, {0, 13}, {0, 2}, {2, 3}, {0, 2}, {2, 11}, {2, 10}, {2, 12}, {2, -4}}, {{2, 13}, {1, 14}, {2, 7}, {1, 12}, {2, -9}, {2, -4}, {2, -8}, {2, 13}, {0, 3}, {1, 20}, {2, 2}, {2, 5}, {0, 19}, {0, 10}, {2, 14}, {2, -15}, {1, 5}, {2, 18}, {2, -8}, {2, 6}}, {{1, 5}, {1, 9}, {1, 19}, {2, 19}, {0, 13}, {2, 2}, {2, 12}, {2, 17}, {2, -16}, {0, 12}, {0, 9}, {1, 17}, {0, 13}, {2, 7}, {1, 7}, {2, 14}, {0, 12}, {0, 7}, {2, -16}, {1, 7}}, {{2, 15}, {0, 9}, {1, 3}, {0, 4}, {2, -3}, {0, 12}, {2, 9}, {0, 6}, {1, 16}, {0, 10}, {2, -6}, {1, 20}, {2, 11}, {2, -3}, {2, -6}, {0, 15}, {0, 14}, {0, 11}, {2, -19}, {2, -14}}, {{0, 12}, {2, 8}, {1, 20}, {2, 18}, {2, 3}, {2, 2}, {2, 4}, {2, 7}, {2, -19}, {2, -9}, {2, 2}, {0, 19}, {0, 11}, {2, -9}, {0, 9}, {2, -14}, {1, 13}, {0, 2}, {0, 1}, {2, -19}},
        {{2, -4}, {1, 6}, {0, 18}, {2, 4}, {1, 12}, {0, 17}, {2, 10}, {2, 13}, {2, 10}, {1, 10}, {2, -1}, {2, -11}, {2, -15}, {2, -5}, {2, -16}, {2, -12}, {0, 2}, {2, -10}, {2, 15}, {1, 2}}, {{2, -9}, {2, -14}, {1, 4}, {0, 5}, {2, -9}, {0, 7}, {0, 9}, {1, 16}, {1, 10}, {2, -14}, {0, 7}, {2, -18}, {0, 19}, {1, 3}, {1, 7}, {2, -19}, {2, 14}, {0, 9}, {1, 7}, {2, -5}}, {{2, 16}, {0, 13}, {2, 20}, {2, -9}, {0, 20}, {2, 20}, {0, 5}, {0, 6}, {1, 4}, {2, -11}, {0, 14}, {0, 9}, {2, 18}, {0, 14}, {0, 12}, {2, 16}, {2, 8}, {2, -13}, {1, 9}, {1, 3}}, {{2, 1}, {2, 13}, {2, -8}, {0, 9}, {2, -16}, {0, 12}, {0, 12}, {2, 19}, {2, -10}, {2, 1}, {2, -20}, {1, 10}, {0, 18}, {2, -15}, {2, -15}, {1, 15}, {0, 15}, {0, 1}, {2, -5}, {0, 19}}, {{2, 6}, {2, 15}, {2, -20}, {1, 20}, {2, 3}, {2, 7}, {1, 1}, {0, 5}, {0, 4}, {2, 8}, {1, 19}, {2, -3}, {2, 18}, {1, 10}, {0, 11}, {2, -19}, {0, 3}, {2, 5}, {1, 4}, {0, 6}}, {{0, 5}, {2, -17}, {1, 1}, {0, 1}, {2, -9}, {0, 8}, {2, -10}, {2, -4}, {0, 13}, {2, 19}, {0, 6}, {2, 9}, {2, 14}, {2, 11}, {0, 16}, {2, 13}, {1, 1}, {2, 9}, {2, 14}, {0, 18}}, {{0, 10}, {1, 12}, {1, 4}, {2, -12}, {2, 7}, {0, 5}, {1, 15}, {2, 7}, {2, 17}, {1, 18}, {2, 3}, {0, 12}, {1, 8}, {1, 13}, {2, -17}, {1, 20}, {0, 18}, {1, 12}, {1, 16}, {0, 16}}, {{2, -10}, {2, -1}, {2, 10}, {1, 4}, {2, -12}, {1, 19}, {2, 18}, {2, -15}, {2, -5}, {1, 11}, {2, -7}, {0, 20}, {2, -12}, {2, -12}, {1, 11}, {2, 8}, {2, 2}, {2, -2}, {2, -4}, {2, 19}}, {{2, 5}, {1, 15}, {1, 18}, {2, 20}, {1, 4}, {0, 7}, {2, -8}, {2, 1}, {2, -6}, {2, 8}, {1, 1}, {2, 12}, {1, 15}, {0, 16}, {1, 13}, {0, 1}, {0, 19}, {0, 20}, {2, -3}, {0, 12}}, {{1, 18}, {2, -4}, {1, 8}, {2, 19}, {1, 11}, {0, 12}, {0, 20}, {2, 4}, {1, 12}, {0, 17}, {2, 8}, {1, 5}, {2, -13}, {1, 3}, {1, 14}, {1, 4}, {0, 20}, {1, 18}, {0, 7}, {0, 17}},
        {{2, 14}, {2, 10}, {2, -12}, {2, -15}, {1, 3}, {2, -17}, {1, 12}, {2, 9}, {2, -9}, {0, 11}, {2, 12}, {0, 16}, {0, 13}, {2, -17}, {2, 13}, {2, 11}, {1, 13}, {2, 16}, {0, 14}, {2, -8}}, {{0, 16}, {2, -10}, {1, 12}, {1, 12}, {2, 19}, {1, 5}, {2, 1}, {2, 19}, {0, 17}, {2, -19}, {2, 20}, {0, 7}, {1, 11}, {2, -3}, {2, -19}, {2, -20}, {2, 15}, {2, -18}, {2, 11}, {2, -19}}, {{2, -11}, {2, 10}, {0, 10}, {1, 15}, {1, 1}, {2, 7}, {2, -9}, {2, -3}, {0, 2}, {0, 20}, {0, 1}, {2, 14}, {2, -16}, {1, 14}, {0, 19}, {0, 11}, {2, 4}, {2, -9}, {2, -4}, {2, -10}}, {{2, 2}, {2, 17}, {1, 7}, {0, 18}, {0, 12}, {2, -15}, {2, 18}, {1, 7}, {1, 3}, {2, 14}, {1, 19}, {1, 6}, {2, -19}, {2, 9}, {0, 11}, {1, 13}, {2, -13}, {0, 19}, {1, 15}, {0, 14}}, {{2, 14}, {1, 3}, {2, 4}, {0, 14}, {1, 8}, {2, 7}, {0, 16}, {1, 11}, {2, 11}, {1, 13}, {1, 6}, {2, -10}, {0, 17}, {1, 18}, {1, 1}, {2, -11}, {1, 9}, {0, 16}, {2, 5}, {2, -6}}, {{0, 11}, {2, -2}, {1, 7}, {2, 8}, {2, 20}, {0, 7}, {0, 14}, {2, -17}, {2, 14}, {2, -14}, {2, -16}, {2, -12}, {2, -19}, {2, 10}, {2, -8}, {1, 1}, {1, 14}, {0, 5}, {0, 14}, {2, 14}}, {{2, 10}, {2, 18}, {1, 15}, {1, 15}, {2, 10}, {2, 14}, {1, 15}, {2, -16}, {2, -4}, {2, -1}, {2, -20}, {2, 2}, {1, 6}, {0, 2}, {2, -17}, {0, 13}, {2, -8}, {2, 18}, {2, 9}, {2, -12}}, {{2, -7}, {1, 16}, {2, 15}, {1, 14}, {1, 15}, {0, 8}, {1, 19}, {2, -13}, {2, 17}, {2, 4}, {2, -17}, {2, -2}, {0, 8}, {1, 11}, {2, 14}, {0, 13}, {2, -12}, {2, -1}, {2, -5}, {0, 6}}, {{0, 2}, {2, -1}, {2, -15}, {1, 9}, {0, 17}, {0, 8}, {2, 19}, {0, 5}, {1, 16}, {2, 3}, {1, 17}, {2, -8}, {2, -1}, {2, -4}, {2, -16}, {0, 4}, {2, -8}, {2, 14}, {2, -20}, {2, -15}}, {{0, 5}, {0, 20}, {1, 13}, {0, 13}, {2, 18}, {1, 19}, {0, 7}, {0, 13}, {2, -7}, {2, 11}, {0, 16}, {1, 6}, {2, -19}, {2, 6}, {1, 18}, {2, 4}, {2, -7}, {2, -8}, {2, 20}, {0, 19}}
    };
    // *INDENT-ON*

    uint8_t buf[20];
    uint8_t n = 0;
    uint32_t bres = 0;
    for(uint32_t i = 0; i < 100; i++) {
        /* bring the pos back to the middle of the file */
        int32_t pos = 500;
        res = lv_fs_seek(&f, pos, LV_FS_SEEK_SET);
        TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);
        for(uint32_t j = 0; j < 20; j++) {
            int8_t action = actions[i][j][0];
            int8_t amount = actions[i][j][1];
            switch(action) {
                case 0: /* read */
                    res = lv_fs_read(&f, buf, amount, &bres);
                    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);
                    TEST_ASSERT_EQUAL(amount, bres);
                    TEST_ASSERT(0 == memcmp(buf, buf1000 + pos, amount));
                    pos += amount;
                    break;
                case 1: /* write */
                    for(int32_t k = 0; k < amount; k++) {
                        buf[k] = n;
                        buf1000[pos + k] = n;
                        n++;
                    }
                    res = lv_fs_write(&f, buf, amount, &bres);
                    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);
                    TEST_ASSERT_EQUAL(amount, bres);
                    pos += amount;
                    break;
                case 2: /* seek */
                    pos += amount; /* amount may be negative */
                    res = lv_fs_seek(&f, pos, LV_FS_SEEK_SET);
                    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);
                    break;
            }
        }
    }

    /* test SEEK_END */
    uint32_t tell_pos;
    res = lv_fs_seek(&f, 0, LV_FS_SEEK_END);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);
    res = lv_fs_tell(&f, &tell_pos);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);
    TEST_ASSERT_EQUAL(tell_pos, 1000);

    res = lv_fs_close(&f);
    TEST_ASSERT_EQUAL(LV_FS_RES_OK, res);

    drv->cache_size = original_cache_size;
}

#endif
