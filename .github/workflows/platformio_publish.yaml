on:
  push:
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+" # Push events to matching v*, i.e. v1.0, v20.15.10

name: PlatformIO Publish
jobs:
  build:
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Install Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.9'
      - name: Install PlatformIO Core
        run: pip install --upgrade platformio
      - name: Switch to the development version
        run: pio upgrade --dev
      - name: Publish
        run: pio pkg publish --no-interactive --owner lvgl .
