name: <PERSON><PERSON><PERSON><PERSON>
on:
  push:
    branches: [ master, release/v8.* ]
  pull_request:
    branches: [ master, release/v8.* ]
jobs:
  lint:
    if: ${{ github.event_name != 'pull_request' || github.repository != github.event.pull_request.head.repo.full_name }}
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/checkout@v4
      - uses: arduino/arduino-lint-action@v1
        with:
          library-manager: update
