name: C/C++ CI

on:
  push:
    branches: [ master, release/v8.* ]
  pull_request:
    branches: [ master, release/v8.* ]

jobs:
  build:
    if: ${{ github.event_name != 'pull_request' || github.repository != github.event.pull_request.head.repo.full_name }}
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        # A valid option parameter to the cmake file.
        # See BUILD_OPTIONS in tests/CMakeLists.txt.
        build_option: ['OPTIONS_16BIT',
                       'OPTIONS_24BIT',
                       'OPTIONS_FULL_32BIT',
                       'OPTIONS_VG_LITE',
                       'OPTIONS_SDL']
    name: Build ${{ matrix.build_option }}
    steps:
    - uses: actions/checkout@v4
    - uses: ammaraskar/gcc-problem-matcher@master
    - name: Install prerequisites
      run: scripts/install-prerequisites.sh
    - name: Building ${{ matrix.build_option }}
      run: python tests/main.py --build-option=${{ matrix.build_option }} build

  build-windows-GCC:
    runs-on: windows-2022
    name: Build Windows GCC
    steps:
    - uses: actions/checkout@v4
    - uses: ammaraskar/gcc-problem-matcher@master
    - name: Install prerequisites
      run: scripts\install-prerequisites.bat
    - name: Build
      run: python tests/main.py build
      env:
        CC: gcc

  build-windows-MSVC:
    runs-on: windows-2022
    name: Build Windows MSVC
    steps:
    - uses: actions/checkout@v4
    - name: Install prerequisites
      run: scripts\install-prerequisites.bat
    - uses: ilammy/msvc-dev-cmd@v1
      with:
        arch: x64
    - uses: ruby/setup-ruby@v1
      with:
        ruby-version: 'mswin'
    - name: Build
      run: python tests/main.py build
      env:
        CC: cl

  test-native:
    runs-on: ubuntu-22.04
    strategy:
     matrix:
       # A valid option parameter to the cmake file.
       # See BUILD_OPTIONS in tests/CMakeLists.txt.
       build_config: ['64bit build',
                      '32bit build']
    name: Run tests with ${{ matrix.build_config }}
    steps:
    - uses: actions/checkout@v4
    - uses: ammaraskar/gcc-problem-matcher@master
    - name: Install prerequisites
      run: scripts/install-prerequisites.sh
    - name: Fix kernel mmap rnd bits
      # Asan in llvm 14 provided in ubuntu 22.04 is incompatible with
      # high-entropy ASLR in much newer kernels that GitHub runners are
      # using leading to random crashes: https://reviews.llvm.org/D148280
      run: sudo sysctl vm.mmap_rnd_bits=28
    - name: Set environment variables for 32-bit build
      if: matrix.build_config == '32bit build'
      run: echo "NON_AMD64_BUILD=1" >> $GITHUB_ENV
    - name: Run tests
      run: python tests/main.py --report --update-image test
    - name: Archive screenshot errors
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: screenshot-errors-amd64
        path: |
             tests/ref_imgs*/**/*_err.png
             test_screenshot_error.h
#    - name: Upload coverage to Codecov
#      uses: codecov/codecov-action@v4
#      if: github.event_name == 'push' && github.repository == 'lvgl/lvgl'
#      with:
#        fail_ci_if_error: true
#        verbose: true
