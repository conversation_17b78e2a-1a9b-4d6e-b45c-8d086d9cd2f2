A clear and concise description of what the bug or new feature is.

### Notes
- Update the [Documentation](https://github.com/lvgl/lvgl/tree/master/docs) if needed.
- Add [Examples](https://github.com/lvgl/lvgl/tree/master/examples) if relevant.
- Add [Tests](https://github.com/lvgl/lvgl/blob/master/tests/README.md) if applicable.
- If you added new options to `lv_conf_template.h` run [lv_conf_internal_gen.py](https://github.com/lvgl/lvgl/blob/master/scripts/lv_conf_internal_gen.py) and update [Kconfig](https://github.com/lvgl/lvgl/blob/master/Kconfig).
- Run `scripts/code-format.py` (`astyle v3.4.12` needs to installed by running `cd scripts; ./install_astyle.sh`) and follow the [Code Conventions](https://docs.lvgl.io/master/CODING_STYLE.html).
- Mark the Pull request as [Draft](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/proposing-changes-to-your-work-with-pull-requests/changing-the-stage-of-a-pull-request) while you are working on the first version, and mark is as _Ready_ when it's ready for review.
- When changes were requested, [re-request review](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/proposing-changes-to-your-work-with-pull-requests/requesting-a-pull-request-review) to notify the maintainers.
- Help us to review this Pull Request! Anyone can [approve or request changes](https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/reviewing-changes-in-pull-requests/approving-a-pull-request-with-required-reviews).
